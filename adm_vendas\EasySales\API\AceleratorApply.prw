#INCLUDE "TOTVS.CH"
#INCLUDE "RESTFUL.CH"
#INCLUDE "AceleratorApply.CH"

//-----------------------------------
/*/{Protheus.doc} AceleratorApply
Dummy Function

@return		Nenhum
<AUTHOR>
@since		08/04/2019
/*/
//-----------------------------------
User Function AceleratorApply()

Return Nil

//-------------------------------------------------------------------
/*/{Protheus.doc} Login
<AUTHOR>
@since 08/04/2019
@wsmethod Login
@verbo GET
@return true ou false
/*/
//-------------------------------------------------------------------
WSRESTFUL Login DESCRIPTION "Valida tokenId" FORMAT "application/json"
	WSData TokenId as String

	WSMETHOD GET DESCRIPTION	"Valida tokenId da aplica��o do Easy Sales"	PRODUCES APPLICATION_JSON

END WSRESTFUL

//-------------------------------------------------------------------
/*/{Protheus.doc} Login
<AUTHOR>
@since 08/04/2019
@wsmethod Login
@verbo GET
@return true ou false
/*/
//-------------------------------------------------------------------

WSMETHOD GET WSRECEIVE TokenId WSSERVICE Login
	Local lRet	:= .F.
	Local oRet	:= JsonObject():new()

	If Empty(::TokenId)
		SetRestFault(400, STR0003)		//"Token Não informado"
	Else
		lRet := U_ESPX010A(::TokenId)

		If !lRet
			SetRestFault(400, STR0002)		//"Token inválido."
		Else
			lRet := .T.

			oRet["message"] := STR0001 	//"Login realizado com sucesso."
			oRet["dadosemp"] := U_ESPX010E(::TokenId)
			
			::SetResponse( oRet:ToJson() )
		EndIf
	EndIf

	iif( ValType( oRet ) == 'O', ( FreeObj( oRet ), oRet := Nil ), Nil )

Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} GetFileSdf
<AUTHOR>
@since 08/04/2019
@wsmethod GetFileSdf
@verbo GET
@return true ou false
/*/
//-------------------------------------------------------------------
WSRESTFUL GetFileSdf DESCRIPTION "Retorna SDF" FORMAT "application/json"
	WSData TokenId		as String

	WSMETHOD GET DESCRIPTION	"Retorna arquivo SDF conforme token informado"	PRODUCES APPLICATION_JSON

END WSRESTFUL

WSMETHOD GET WSRECEIVE TokenId WSSERVICE GetFileSdf

	Local lRet		:= .F.
	Local cBuffer	:= ""
	Local oRet		:= JsonObject():new()

	If Empty(::TokenId)
		SetRestFault(400, STR0003)				//"Token Não informado"
	Else
		If !U_ESPX010A(::TokenId)
			SetRestFault(400, STR0002)			//"Token inválido."
		Else
			cBuffer := Encode64(U_ESPX010D(::TokenId, 'SDFBRA.TXT'))

			If !Empty(cBuffer)
				lRet := .T.

				oRet["message"] := STR0004		//"Arquivo recuperado com sucesso."
				oRet["SDF"]		:= cBuffer
				
				::SetResponse( oRet:ToJson() )
			EndIf
		EndIf
	EndIf


	cBuffer := Nil
	iif( ValType( oRet ) == 'O', ( FreeObj( oRet ), oRet := Nil ), Nil )

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} GetFileBackup
<AUTHOR>
@since 08/04/2019
@wsmethod GetFileBackup
@verbo GET
@return true ou false
/*/
//-------------------------------------------------------------------
WSRESTFUL GetFileBackup DESCRIPTION "Retorna arquivos para backup" FORMAT "application/json"
	WSData TokenId		as String

	WSMETHOD GET DESCRIPTION	"Retorna arquivo para backup conforme os aceleradores do token informado"	PRODUCES APPLICATION_JSON

END WSRESTFUL

WSMETHOD GET WSRECEIVE TokenId WSSERVICE GetFileBackup

	Local lRet		:= .F.
	Local cBuffer	:= ""
	Local oRet		:= JsonObject():new()

	If Empty(::TokenId)
		SetRestFault(400, STR0003)		//"Token Não informado"
	Else
		If !U_ESPX010A(::TokenId)
			SetRestFault(400, STR0002)		//"Token inválido."
		Else
			cBuffer := Encode64(U_ESPX010D(::TokenId, 'ACEBAK.TXT'))

			If !Empty(cBuffer)
				lRet := .T.

				oRet["message"] := STR0004 	//"Arquivo recuperado com sucesso."
				oRet["SDF"]		:= cBuffer
				
				::SetResponse( oRet:ToJson() )
			EndIf
		EndIf
	EndIf

	cBuffer := Nil
	iif( ValType( oRet ) == 'O', ( FreeObj( oRet ), oRet := Nil ), Nil )

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} GetFileList
<AUTHOR>
@since 08/04/2019
@wsmethod GetFileList
@verbo GET
@return true ou false
/*/ 
//-------------------------------------------------------------------
WSRESTFUL GetFileList DESCRIPTION "Retorna lista de aceleradores" FORMAT "application/json"
	WSData TokenId		as String

	WSMETHOD GET DESCRIPTION	"Retorna lista de aceleradores conforme o token informado"	PRODUCES APPLICATION_JSON

END WSRESTFUL

WSMETHOD GET WSRECEIVE TokenId WSSERVICE GetFileList

	Local lRet		:= .F.
	Local cBuffer	:= ""
	Local oRet		:= JsonObject():new()

	If Empty(::TokenId)
		SetRestFault(400, STR0003)		//"Token Não informado"
	Else
		If !U_ESPX010A(::TokenId)
			SetRestFault(400, STR0002)		//"Token inválido."
		Else
			cBuffer := U_ESPX010C(Self:TokenId)

			If !Empty(cBuffer)

				oRet["message"] := STR0005 	//"Arquivo recuperado com sucesso."
				oRet["SDF"]		:= cBuffer
				
				::SetResponse( oRet:ToJson() )
			EndIf

			lRet := .T.
		EndIf
	EndIf

	cBuffer := Nil
	iif( ValType( oRet ) == 'O', ( FreeObj( oRet ), oRet := Nil ), Nil )

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} GetFile
<AUTHOR>
@since 08/04/2019
@wsmethod GetFile
@verbo GET
@return true ou false
/*/ 
//-------------------------------------------------------------------
WSRESTFUL GetFile DESCRIPTION "Retorna lista de aceleradores" FORMAT "application/json"
	WSData TokenId		as String
	WSData FilePath		as String

	WSMETHOD GET DESCRIPTION	"Retorna lista de aceleradores conforme o token informado"	PRODUCES APPLICATION_JSON

END WSRESTFUL

WSMETHOD GET WSRECEIVE TokenId WSSERVICE GetFile

	Local lRet		:= .F.
	Local cBuffer	:= ""
	Local oRet		:= JsonObject():new()

	If Empty(::TokenId)
		SetRestFault(400, STR0003)		//"Token Não informado"
	Else
		If !U_ESPX010A(::TokenId)
			SetRestFault(400, STR0002)		//"Token inválido."
		Else
			cBuffer := Encode64(U_ESPX010B(::TokenId, ::FilePath))

			If !Empty(cBuffer)
				oRet["message"] := STR0004 	//"Arquivo recuperado com sucesso."
			Else
				oRet["message"] := STR0006 	//"Não existem dados para serem recuperados."
			EndIf
			oRet["SDF"]		:= cBuffer

			lRet := .T.
			::SetResponse( oRet:ToJson() )
		EndIf
	EndIf

	cBuffer := Nil
	iif( ValType( oRet ) == 'O', ( FreeObj( oRet ), oRet := Nil ), Nil )

Return lRet