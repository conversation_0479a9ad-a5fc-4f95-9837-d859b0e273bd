#include 'totvs.ch'
/*/{Protheus.doc} MT030Jin
Ponto de entrada no fonte MATI030 - Integracao mensagem unica do Protheus para adicionar campos especificos ao objeto ofwEAIObj.
<AUTHOR>
@since 25/02/2021
@version 1.0
@type function
/*/
User Function MT030Jin()

    Local oJson := JsonObject():New()

    oJson["TcOthers"]                         := JsonObject():New()
    oJson["TcOthers"]["PorteDoCliente"]       := SA1->A1_XPORTE  
    oJson["TcOthers"]["NumeroDeFuncionarios"] := GetAdvFVal("AI0","AI0_XNFUNC",xFilial("AI0")+SA1->A1_COD,1)
    oJson["TcOthers"]["GrupoEconomico"]       := SA1->A1_XGRPEC 
    oJson["TcOthers"]["Faturamento"]          := GetAdvFVal("AI0","AI0_XFATAN",xFilial("AI0")+SA1->A1_COD,1)  
    oJson["TcOthers"]["SitCliente"]           := SA1->A1_XSITCLI 
    oJson["TcOthers"]["SubSegmento"]          := SA1->A1_XCODSUB
    oJson["TcOthers"]["TipoCliente"]          := SA1->A1_XCLIVIP 
    oJson["TcOthers"]["Segmento"]             := SA1->A1_CODSEG
    oJson["TcOthers"]["UnidadeDeVenda"]       := alltrim(SA1->A1_CODMEMB)
    oJson["TcOthers"]["CodigoProspect"]       := GetAdvFVal("SUS","US_COD",xFilial("SUS")+SA1->(A1_COD+A1_LOJA),5,"")
    oJson["TcOthers"]["LojaProspect"]         := GetAdvFVal("SUS","US_LOJA",xFilial("SUS")+SA1->(A1_COD+A1_LOJA),5,"")
    oJson["TcOthers"]["CategoriaCliente"]     := GetAdvFVal("AI0","AI0_XCATCL",xFilial("AI0")+SA1->A1_COD,1)  
Return oJson:ToJson()
