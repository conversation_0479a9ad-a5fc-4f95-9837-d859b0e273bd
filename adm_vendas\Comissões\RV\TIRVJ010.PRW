#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVJ010     
Job Responsavel por criar a P37 para consultar a API do PSA View  Escopo fechado PSA
@type  Function
<AUTHOR> Menabue Lima
@since 09/11/2023
@version 1.0
	/*/
User Function TIRVJ010(nCount,nPage,cCodReq,aComp)
	Local oReceptor:=TIINTERCEPTOR():New()
	Local cFecthXml :=""
	Local cApiPSA:=GetMv("TI_RVJ010A" , .F., "RVF010")
	Local nMonth :=GetMv("TI_RVJ10NM" , .F., 12)
	Default nCount := GetMv("TI_RVJ10CT" , .F., 5000)
	Default nPage := 1
	Default cCodReq:=""
	cFecthXml +='<fetch no-lock="true" returntotalrecordcount="true" latematerialize="true" count="'+cValToChar(nCount)+'" page="'+cValToChar(nPage)+'">'
	cFecthXml +='<entity name="ctm_parceladoescopofechado">
	cFecthXml +='<attribute name="ctm_name" />
	cFecthXml +='<attribute name="ctm_progressofisico" />
	cFecthXml +='<attribute name="ctm_valorparcela" />
	cFecthXml +='<attribute name="statuscode" />
	cFecthXml +='<attribute name="ctm_descricao" />
	cFecthXml +='<attribute name="ctm_parceladoescopofechadoid" alias="PARCELAESCOPOFECHADO_ID" />
	cFecthXml +='<attribute name="ctm_composicao_de_apontamento" alias="ID_COMPOSICAO" />
	cFecthXml +='<attribute name="createdon" alias="DT_INCLUSAO" />
	cFecthXml +='<attribute name="modifiedon" alias="DT_ALTERACAO" />
	cFecthXml +='<link-entity name="ctm_composicaodeapontamento" from="ctm_composicaodeapontamentoid" to="ctm_composicao_de_apontamento" link-type="inner" alias="Composicao">
	cFecthXml +='<attribute name="ctm_competencia" alias="COMPETENCIA" />
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="msdyn_project" from="msdyn_projectid" to="ctm_projeto" link-type="outer" alias="Projeto">
	cFecthXml +='<attribute name="ctm_codigo" alias="CODIGO_PROJETO" />
	cFecthXml +='<attribute name="ctm_codigo_principalcp" />
	cFecthXml +='<attribute name="ctm_codigo_principalgpp" />
	cFecthXml +='<attribute name="ctm_metodocob" alias="METODO_COBRANCA" />
	cFecthXml +='<link-entity name="bookableresource" from="bookableresourceid" to="ctm_principalcp" link-type="outer" alias="CPRecurso">
	cFecthXml +='<link-entity name="systemuser" from="systemuserid" to="userid" link-type="outer" alias="CPUser">
	cFecthXml +='<attribute name="ctm_st_cpf" alias="CP_CPF" />
	cFecthXml +='<attribute name="fullname" alias="CP_NOME" />
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="bookableresource" from="bookableresourceid" to="ctm_principalgpp" link-type="outer" alias="GPPRecurso">
	cFecthXml +='<link-entity name="systemuser" from="systemuserid" to="userid" link-type="outer" alias="GPPUser">
	cFecthXml +='<attribute name="ctm_st_cpf" alias="GPP_CPF" />
	cFecthXml +='<attribute name="fullname" alias="GPP_NOME" />
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="ctm_solicitacaoescopofechado" from="ctm_solicitacaoescopofechadoid" to="ctm_solicitacaoescopofechado" link-type="outer" alias="SolicitacaoEscopoFechado">
	cFecthXml +='<attribute name="ctm_name" alias="ctm_solicitacaoescopofechado" />
	cFecthXml +='<link-entity name="ctm_recursosdoescopofechado" from="ctm_solicitacaoescopofechado" to="ctm_solicitacaoescopofechadoid" link-type="outer" alias="RecursoEscopoFechado">
	cFecthXml +='<attribute name="ctm_recursosdoescopofechadoid" alias="ESCOPOFECHADO_RECURSO_ID" />
	cFecthXml +='<link-entity name="bookableresource" from="bookableresourceid" to="ctm_bookableresourceid" link-type="outer" alias="Recurso">
	cFecthXml +='<link-entity name="systemuser" from="systemuserid" to="userid" link-type="outer" alias="RecursoUsuario">
	cFecthXml +='<attribute name="ctm_st_cpf" />
	cFecthXml +='<attribute name="fullname" />
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="contact" from="contactid" to="contactid" link-type="outer" alias="RecursoContato">
	cFecthXml +='<attribute name="ctm_st_cpf" />
	cFecthXml +='<attribute name="fullname" />
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='<filter>
	cFecthXml +='<filter type="or">
    cFecthXml +='<condition entityname="Composicao" attribute="ctm_competencia" operator="eq" value="'+aComp[1]+'" />
    //cFecthXml +='<condition entityname="Composicao" attribute="ctm_competencia" operator="eq" value="'+aComp[2]+'" />
    //cFecthXml +='<condition entityname="Composicao" attribute="ctm_competencia" operator="eq" value="'+aComp[3]+'" />
	
    cFecthXml +='</filter>
	cFecthXml +='</filter>
	cFecthXml +='</entity>
	cFecthXml +='</fetch>
	cCodP37:= oReceptor:GetP37COD()
	BeginTran()
	Reclock("P37",.T.)
	P37->P37_FILIAL:= Xfilial("P37")
	P37->P37_COD   := cCodP37
	P37->P37_DATA  := ddatabase
	P37->P37_HORA  := TIME()
	P37->P37_PATH2 :=EncodeUtf8("/api/data/v9.2/ctm_parceladoescopofechados?$count=true&fetchXml="+ESCAPE(cFecthXml))
	P37->P37_CODAPI:=cApiPSA
	If P36->(DbSeek(xFilial("P36")+P37->P37_CODAPI))
		P37->P37_URL   := P36->P36_URL
	EndIf
	P37->P37_METHOD:= "GET"
	P37->P37_ASYNC1:= "N"
	P37->P37_STATUS:= "0"
	P37->P37_TIPORE:= "1"//Envio
	P37->P37_CALLBA:= "U_TIRVF010"
	P37->P37_HEADER:= "Accept: application/json|OData-MaxVersion: 4.0|OData-Version: 4.0|"+'Prefer: odata.include-annotations="OData.Community.Display.V1.FormattedValue"'
	P37->P37_CODREQ:= cCodReq
	P37->P37_BODY  := '{"nPage":'+cValToChar(nPage)+', "nCount":'+cValToChar(nCount)+'}'
	P37->(MsUnlock())
	EndTran()
	FreeObj(oReceptor)
Return
