#include "tlpp-core.th"

namespace ti

Function U_WritableNumberValue(cField, nValue)
    Local nTam      := FwGetSx3Cache(cField, "X3_TAMANHO")
    Local nDec      := FwGetSx3Cache(cField, "X3_DECIMAL")
    Local cValue    := ""
	Local nRetVal   := 0

    While Len(cValue) < nDec   
        cValue += "9"
    End

    If nDec > 0
        cValue := "." + cValue
    EndIf

    While Len(cValue) < nTam
        cValue := "9" + cValue
    End

	nRetVal := Val(cValue)

	If nValue < nRetVal  
		nRetVal := nValue
	EndIf

Return nRetVal 


