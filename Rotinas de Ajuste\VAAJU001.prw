#INCLUDE "PROTHEUS.CH"

Static __cCianorte := "00001003100"

User Function VAAJU001()

Local _aAux	:= {}
Local _nX	:= 0

RpcClearEnv()
RpcSetType(3)
RpcSetEnv("00","00001003100")

//Copia dos arquivos CE1 e SF4
If MsgYesNo("Copiar Arquivos para o Servidor (CE1/AOX/SX6) ?")
	__CopyFile("C:\Users\<USER>\Desktop\planilhas\ce1000_cianorte.dbf","\AUTOCOM\ce1000_cianorte.dbf")
	__CopyFile("C:\Users\<USER>\Desktop\planilhas\aox000_cianorte.dbf","\AUTOCOM\aox000_cianorte.dbf")
	__CopyFile("C:\Users\<USER>\Desktop\planilhas\sx6000_cianorte.dbf","\AUTOCOM\sx6000_cianorte.dbf")
	
	//Realiza append caso o arquivo esteja no diretorio
	If File("\AUTOCOM\SX6000_CIANORTE.DBF")
		dbSelectArea("SX6")
		SX6->(dbSetOrder(1))
		APPEND FROM \AUTOCOM\SX6000_CIANORTE.DBF VIA "DBFCDXADS"
	EndIf
	
	//Realiza append caso o arquivo esteja no diretorio
	If File("\AUTOCOM\CE1000_CIANORTE.DBF")
		dbSelectArea("CE1")
		CE1->(dbSetOrder(1))
		APPEND FROM \AUTOCOM\CE1000_CIANORTE.DBF VIA "DBFCDXADS"
	EndIf
	
	//Realiza append caso o arquivo esteja no diretorio
	If File("\AUTOCOM\AOX000_CIANORTE.DBF")
		dbSelectArea("AOX")
		AOX->(dbOrderNickName("AOXAGNCOD"))
		APPEND FROM \AUTOCOM\AOX000_CIANORTE.DBF VIA "DBFCDXADS"
	EndIf
EndIf

//Insercao de dados na tabela XN do SX5
If MsgYesNo("Incluir dados da tabela XN - SX5?")
	_aAux := {}
	
	AADD( _aAux, {__cCianorte, "XN", "00", "19533000"} )
	
	dbSelectArea("SX5")
	For _nX := 1 To Len( _aAux )
		RecLock("SX5",.T.)
		SX5->(FieldPut(FieldPos("X5_FILIAL"), _aAux[_nX,1]))
		SX5->(FieldPut(FieldPos("X5_TABELA"), _aAux[_nX,2]))
		SX5->(FieldPut(FieldPos("X5_CHAVE"), _aAux[_nX,3]))
		SX5->(FieldPut(FieldPos("X5_DESCRI"), _aAux[_nX,4]))
		SX5->(FieldPut(FieldPos("X5_DESCSPA"), _aAux[_nX,4]))
		SX5->(FieldPut(FieldPos("X5_DESCENG"), _aAux[_nX,4]))
		SX5->(MsUnLock())
	Next _nX
EndIf

//Insercao de dados na tabela 01 do SX5
If MsgYesNo("Incluir dados da tabela 01 - SX5?")
	_aAux := {}
	
	AADD( _aAux, {__cCianorte	,"01","UNE","000000001"} )
	AADD( _aAux, {__cCianorte	,"01","CAN","000000001"} )
	AADD( _aAux, {__cCianorte	,"01","DEB","000000001"} )
	AADD( _aAux, {__cCianorte	,"01","DES","000000001"} )
	AADD( _aAux, {__cCianorte	,"01","IMP","000000001"} )
	AADD( _aAux, {__cCianorte	,"01","TST","000000001"} )
	
	dbSelectArea("SX5")
	For _nX := 1 To Len( _aAux )
		RecLock("SX5",.T.)
		SX5->(FieldPut(FieldPos("X5_FILIAL"), _aAux[_nX,1]))
		SX5->(FieldPut(FieldPos("X5_TABELA"), _aAux[_nX,2]))
		SX5->(FieldPut(FieldPos("X5_CHAVE"), _aAux[_nX,3]))
		SX5->(FieldPut(FieldPos("X5_DESCRI"), _aAux[_nX,4]))
		SX5->(FieldPut(FieldPos("X5_DESCSPA"), _aAux[_nX,4]))
		SX5->(FieldPut(FieldPos("X5_DESCENG"), _aAux[_nX,4]))
		SX5->(MsUnLock())
	Next _nX
EndIf

Return()