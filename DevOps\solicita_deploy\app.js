import { formatISO } from "date-fns";
import fs from "fs";

let today = new Date();
let yyyyMmDd = formatISO(today, { representation: "date" }).replaceAll("-", "");
let hhMmSs = formatISO(today, { representation: "time" })
  .replaceAll(":", "")
  .replace("Z", "")
  .substring(0, 6);
let body = {};
let name = "";

function solicitaDeploy(requester, port) {
  body = {
    requester: requester,
    environment: "TOTVS12",
    port: port,
  };
  name = `${port}_deploy-rpo-jump_${body.environment}_${yyyyMmDd}_${hhMmSs}`;

  fs.writeFile(
    `${process.env.BUILD_SOURCESDIRECTORY}\\devops\\solicita_deploy\\data\\${name}.json`,
    JSON.stringify(body),
    (err) => {
      if (err) {
        console.error(err);
      }
    }
  );
}

if (process.env.JUMP_PORT.length > 0) {
  solicitaDeploy(process.env.BUILD_REQUESTEDFOREMAIL.substring(0,process.env.BUILD_REQUESTEDFOREMAIL.indexOf("@")), process.env.JUMP_PORT);
} else {console.log(process.env.JUMP_PORT);
  throw new Error("JUMP_PORT is not defined");
}
