#INCLUDE "PROTHEUS.CH"

/*/{Protheus.doc} GCVA103T
(Ajusta PH6 e PH7)
@type function
<AUTHOR>
@since 03/10/2018
@version 1.0
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
TIADMVIN-3412X
/*/
//TIADMIN-3138
User Function GCVA103T(cContra, cRevisa, cAMProc, ctargetDir)
    Local cChavePH6	:= ""
    Local cChavePH7	:= ""

    Local oAtuPH6		:= nil

    Local cCondic	:= ""
    Local cMoeda	:= ""
    Local cGU		:= ""
    Local cNumero   := ""

    Local aArea	:= GetArea()
    Local aAreaPH5 := PH5->(GetArea())
    Local aAreaPH6 := PH6->(GetArea())
    Local aAreaPH7 := PH7->(GetArea())
    Local aAreaCND := CND->(GetArea())
    Local aAreaSF2 := SF2->(GetArea())
    Local aAreaSD2 := SD2->(GetArea())
    Local aAreaSC5 := SC5->(GetArea())
    


    Local cCompet  := AMtoCmp(cAMProc)
    Local aMedAnt  := {}
    Local aPedAnt  := {}
    Local np

    Default ctargetDir := ""
    cContra := PADR(cContra,Len(PH5->PH5_CONTRA))

    PH5->(DbSetOrder(12)) //PH5_FILIAL+PH5_CONTRA+PH5_REVISA+PH5_ANOMES+PH5_NUMERO+PH5_CONDIC+PH5_CLIENT+PH5_LOJA+PH5_CONDPG+PH5_NOTASE+PH5_MOEDA+PH5_MASCCC+PH5_GU+PH5_SEQ
    
    If ! PH5->(DbSeek(xFilial("PH5") + cContra + cRevisa + cAMProc))
        RestArea(aAreaPH5) 
        RestArea(aArea)
    EndIf

    DelTot(cContra, cRevisa, cAMProc, aMedAnt, aPedAnt) //deleta PH6 E PH7

    
    PH6->(DbSetOrder(1))  //PH6_FILIAL+PH6_CONTRA+PH6_REVISA+PH6_NUMERO+PH6_COMPET+PH6_CONDIC+PH6_CLIENT+PH6_LOJA+PH6_CONDPG+PH6_NOTASE+PH6_MOEDA+PH6_MASCCC+PH6_GU+PH6_SEQ
    PH7->(DbSetOrder(1))  //PH7_FILIAL+PH7_CONTRA+PH7_REVISA+PH7_NUMERO+PH7_COMPET+PH7_CONDIC+PH7_MOEDA+PH7_GU
    SF2->(DbSetOrder(1))  //F2_FILIAL+F2_DOC+F2_SERIE+F2_CLIENTE+F2_LOJA+F2_FORMUL+F2_TIPO   
    SD2->(DbSetOrder(3))  //D2_FILIAL+D2_DOC+D2_SERIE+D2_CLIENTE+D2_LOJA+D2_COD+D2_ITEM                                                                                                     
    SC5->(DbSetOrder(1))
    CND->(DBSetOrder(4))
    

    oAtuPH6	:= TGCVXC08():New()

    //Chave PH6
    If !Empty(ctargetDir)
        oAtuPH6:ctargetDir	:= ctargetDir
    EndIf
    oAtuPH6:cPH6Filial	:= PH5->PH5_FILIAL
    oAtuPH6:cPH6Contra	:= PH5->PH5_CONTRA
    oAtuPH6:cPH6Revisa	:= PH5->PH5_REVISA
    oAtuPH6:cPH6Numero	:= PH5->PH5_NUMERO
    oAtuPH6:cPH6Compet	:= PH5->PH5_COMPET 
    
    Do While PH5->(!EOF()) .And. PH5->(PH5_CONTRA + PH5_REVISA + PH5_ANOMES) == cContra + cRevisa + cAMProc
        If Empty(cChavePH7)
            cChavePH7	:= PH5->(PH5_FILIAL + PH5_CONTRA + PH5_REVISA + PH5_NUMERO + PH5_COMPET + PH5_CONDIC + PH5_MOEDA + PH5_GU) 
        EndIf

        If Empty(cChavePH6)
            cChavePH6	:= PH5->(PH5_FILIAL + PH5_CONTRA + PH5_REVISA + PH5_NUMERO + PH5_COMPET + PH5_CONDIC + PH5_CLIENT + PH5_LOJA + PH5_CONDPG + PH5_NOTASE + PH5_MOEDA + PH5_MASCCC + PH5_GU + PH5_SEQ) 
        EndIf
        If Empty(oAtuPH6:cPH6Numero)
        	oAtuPH6:cPH6Numero	:= PH5->PH5_NUMERO
        EndIf
        
        If Empty(oAtuPH6:cPH6Condic)
            oAtuPH6:cPH6Condic	:= PH5->PH5_CONDIC
        EndIf
        
        If Empty(oAtuPH6:cPH6Client)
            oAtuPH6:cPH6Client	:= PH5->PH5_CLIENT
        EndIf
        If Empty(oAtuPH6:cPH6Loja)
            oAtuPH6:cPH6Loja	:= PH5->PH5_LOJA
        EndIf
        If Empty(oAtuPH6:cPH6Condpg)
            oAtuPH6:cPH6Condpg	:= PH5->PH5_CONDPG
        EndIf
        If Empty(oAtuPH6:cPH6Codiss)
            oAtuPH6:cPH6Codiss	:= PH5->PH5_CODISS
        EndIf
        If Empty(oAtuPH6:cPH6Notase)
            oAtuPH6:cPH6Notase	:= PH5->PH5_NOTASE
        EndIf
        If Empty(oAtuPH6:cPH6Moeda)
            oAtuPH6:cPH6Moeda	:= PH5->PH5_MOEDA
        EndIf
        If Empty(oAtuPH6:cPH6Masccc)
            oAtuPH6:cPH6Masccc	:= PH5->PH5_MASCCC
        EndIf
        If Empty(oAtuPH6:cPH6Pedven)
            oAtuPH6:cPH6Pedven	:= PH5->PH5_PEDVEN
        EndIf
        If Empty(oAtuPH6:cPH6Nota) 
            oAtuPH6:cPH6Nota	:= PH5->PH5_NOTA
        EndIf
        If Empty(oAtuPH6:cPH6Serie)
            oAtuPH6:cPH6Serie	:= PH5->PH5_SERIE
        EndIf
        
        If Empty(oAtuPH6:cPH6Grupo)
            oAtuPH6:cPH6Grupo	:= PH5->PH5_GRUPO
        EndIf
        If Empty(oAtuPH6:cPH6Unineg )
            oAtuPH6:cPH6Unineg	:= PH5->PH5_UNINEG
        EndIf
        If Empty(oAtuPH6:cPH6Nummed)
            oAtuPH6:cPH6Nummed	:= PH5->PH5_NUMMED
        EndIf
        If Empty(oAtuPH6:cPH6Seq)
            oAtuPH6:cPH6Seq	:= PH5->PH5_SEQ
        EndIf
        If Empty(oAtuPH6:cPH6Gu )
            oAtuPH6:cPH6Gu	:= PH5->PH5_GU
        EndIf
        If Empty(oAtuPH6:cPH6Anomes)
            oAtuPH6:cPH6Anomes	:= PH5->PH5_ANOMES
        EndIf
        
        If Empty(oAtuPH6:dPH6Dtgped)
            If !Empty(PH5->PH5_PEDVEN)
                If SC5->(DbSeek(PH5->PH5_UNINEG + PH5->PH5_PEDVEN)) 
                    oAtuPH6:dPH6Dtgped	:= SC5->C5_EMISSAO
                    np:= Ascan(aPedAnt, { |x| x[1] == PH5->PH5_PEDVEN })
                    If np > 0
                        oAtuPH6:cPH6HrgPed := aPedAnt[np, 3]
                    Else
                        oAtuPH6:cPH6HrgPed := Time()
                    EndIf
                EndIf
            EndIf
        EndIf
        
        If Empty(oAtuPH6:cPH6Hrgnfs) .Or. Empty(oAtuPH6:dPH6Dtgnfs)
            If !Empty(PH5->PH5_NOTA )
                If SF2->(DbSeek(PH5->PH5_UNINEG + PH5->PH5_NOTA + PH5->PH5_SERIE))
                    oAtuPH6:cPH6Hrgnfs	:= SF2->F2_HORA
                    oAtuPH6:dPH6Dtgnfs	:= SF2->F2_EMISSAO
                    oAtuPH6:cPH6Cmpfat	:= SubStr(DtoS(SF2->F2_EMISSAO),5,2) + "/" + SubStr(DtoS(SF2->F2_EMISSAO),1,4) 
                EndIf
            EndIf
        EndIf
        
        If Empty(oAtuPH6:cPH6MsBlql) 
            oAtuPH6:cPH6MsBlql := PH5->PH5_MSBLQL
        EndIf
        
        If Empty(oAtuPH6:dPH6Dtoper)
            oAtuPH6:dPH6Dtoper	:= PH5->PH5_DTOPER
        Else
            If oAtuPH6:dPH6Dtoper	< PH5->PH5_DTOPER
                oAtuPH6:dPH6Dtoper	:= PH5->PH5_DTOPER
            EndIf
        EndIf

        If Empty(oAtuPH6:cPH6Hroper)
            oAtuPH6:cPH6Hroper	:= PH5->PH5_HROPER
        Else    
            If DtoS(oAtuPH6:dPH6Dtoper) +  oAtuPH6:cPH6Hroper < Dtos(PH5->PH5_DTOPER) + PH5->PH5_HROPER
                oAtuPH6:cPH6Hroper := PH5->PH5_HROPER
            EndIf
        EndIf

        If Empty(oAtuPH6:dPH6Dtmoed)
            oAtuPH6:dPH6Dtmoed	:= PH5->PH5_DTMOED
        EndIf
        
        If Empty(oAtuPH6:dPH6Dtgmed)
            If !Empty(PH5->PH5_NUMMED )
                If CND->(DbSeek(PH5->PH5_UNINEG + PH5->PH5_NUMMED)) 
                    oAtuPH6:dPH6Dtgmed	:= CND->CND_DTINIC
                    np:= Ascan(aMedAnt, { |x| x[1] == PH5->PH5_NUMMED })
                    If np > 0
                        oAtuPH6:cPH6HrgMed := aMedAnt[np, 3]
                    Else
                        oAtuPH6:cPH6HrgMed := Time()
                    EndIf
                EndIf
            EndIf
        EndIf
        
        If !Empty(PH5->PH5_NOTA)
            If SD2->(DbSeek(PH5->(PH5_UNINEG + PH5_NOTA + PH5_SERIE + PH5_CLIENT + PH5_LOJA + PH5_PRODUT + PH5_ITEMNF)))
                oAtuPH6:nPH6ValDev += SD2->D2_VALDEV
            EndIf
        EndIf 
            
        oAtuPH6:nPH6Vltot	+= PH5->PH5_VLTOT
        oAtuPH6:nPH6Vlcanc	+= PH5->PH5_VLCANC
        oAtuPH6:nPH6Vlboni	+= PH5->PH5_VLBONI
        oAtuPH6:nPH6Vlcare	+= PH5->PH5_VLCARE
        oAtuPH6:nPH6Vlnova	+= PH5->PH5_VLNOVA
        oAtuPH6:nPH6Vlreaj	+= PH5->PH5_VLREAJ
        oAtuPH6:nPH6Vltran	+= PH5->PH5_VLTRAN
        oAtuPH6:nPH6Vlrfat	+= PH5->PH5_VLRFAT
        oAtuPH6:nPH6Bup		+= PH5->PH5_BUP
        oAtuPH6:nPH6Bdown	+= PH5->PH5_BDOWN
        oAtuPH6:nPH6Vltrib	+= PH5->PH5_VLTRIB
        oAtuPH6:nPH6Vlfats	+= PH5->PH5_VLFATS
        oAtuPH6:nPH6Txmoed	:= PH5->PH5_TXMOED
        oAtuPH6:nPH6Vlmoed	+= PH5->PH5_VLMOED
        oAtuPH6:nPH6Vlfat2	+= PH5->PH5_VLFAT2
        oAtuPH6:nPH6Vlraud	+= PH5->PH5_VLRAUD
        oAtuPH6:nPH6Vlrtrc	+= PH5->PH5_VLRTRC
        oAtuPH6:nPH6Vlincr	+= PH5->PH5_VLINCR
        oAtuPH6:nPH6VlInte	+= PH5->PH5_VLINTE
        oAtuPH6:nPH6IntCan	+= PH5->PH5_INTCAN
        oAtuPH6:nPH6Vltrae	+= PH5->PH5_VLTRAE
        oAtuPH6:nPH6Sldcan	+= PH5->PH5_SLDCAN
        oAtuPH6:nPH6Varroy	+= PH5->PH5_VARROY
        oAtuPH6:nPH6Qtdrea	+= PH5->PH5_QTDREA
        oAtuPH6:nPH6Vlmult	+= PH5->PH5_VLMULT
        oAtuPH6:nPH6Vlrea	+= PH5->PH5_VLREA
        oAtuPH6:nPH6Vlrbil	+= PH5->PH5_VLRBIL
        oAtuPH6:nPH6Varbil	+= PH5->PH5_VARBIL
        oAtuPH6:nPH6DltTrc	+= PH5->PH5_DLTTRC
        oAtuPH6:nPH6xVlrRe	+= PH5->PH5_XVLRRE
        oAtuPH6:nDiaFat	    := PH5->PH5_DIAFAT

        
        cCondic	:= PH5->PH5_CONDIC			
        cMoeda	:= PH5->PH5_MOEDA
        cGU		:= PH5->PH5_GU
        cNumero := PH5->PH5_NUMERO
        
        PH5->(DBSKIP())
            
        If cChavePH6 <> PH5->(PH5_FILIAL + PH5_CONTRA + PH5_REVISA + PH5_NUMERO + PH5_COMPET + PH5_CONDIC + PH5_CLIENT + PH5_LOJA + PH5_CONDPG + PH5_NOTASE + PH5_MOEDA + PH5_MASCCC + PH5_GU + PH5_SEQ) 
            oAtuPH6:ChkStatus()
            oAtuPH6:AtuPH6()
            oAtuPH6:ZERAVAR()
            cChavePH6 := "" 	
        EndIf

        If cChavePH7 <> PH5->PH5_FILIAL + PH5->PH5_CONTRA + PH5->PH5_REVISA + PH5->PH5_NUMERO + PH5->PH5_COMPET + PH5->PH5_CONDIC + PH5->PH5_MOEDA + PH5->PH5_GU
            AtuPH7(cContra, cRevisa, cNumero, cCompet, cCondic, cMoeda, cGU, ctargetDir)
            cChavePH7	:= ""
            cCondic  	:= ""
            cMoeda		:= ""
            cGU			:= ""
            cNumero     := ""
        EndIf		

    EndDo

    RestArea(aAreaSF2)
    RestArea(aAreaSD2)
    RestArea(aAreaSC5)
    RestArea(aAreaCND)
    RestArea(aAreaPH5) 
    RestArea(aAreaPH6) 
    RestArea(aAreaPH7) 
    RestArea(aArea)
Return

User Function GCVA1037(cContra, cRevisa, cNumero, cCompet, cCondic, cMoeda, cGU, ctargetDir )

Return AtuPH7(cContra, cRevisa, cNumero, cCompet, cCondic, cMoeda, cGU, ctargetDir )

Static Function AtuPH7(cContra, cRevisa, cNumero, cCompet, cCondic, cMoeda, cGU, ctargetDir )

    Local cChavePH7	:= ""
    Local oAtuPH7		:= nil
    Local aArea		:= GetArea()
    Local aAPH7		:= {}
    Local aAPH6		:= {}

    Default ctargetDir := ""
    If Select("PH6") == 0
        ChkFile("PH6")
    EndIf

    If Select("PH7") > 0
        ChkFile("PH7")
    EndIf

    aAPH6 := PH6->(GetArea())
    aAPH7 := PH7->(GetArea())

    PH6->(DbsetOrder(3)) //PH6_FILIAL+PH6_GU+PH6_CONTRA+PH6_REVISA+PH6_NUMERO+PH6_COMPET+PH6_CONDIC+PH6_CLIENT+PH6_LOJA+PH6_CONDPG+PH6_NOTASE+PH6_MOEDA+PH6_MASCCC+PH6_SEQ                 
    PH7->(DbSetOrder(1)) //PH7_FILIAL+PH7_CONTRA+PH7_REVISA+PH7_NUMERO+PH7_COMPET+PH7_CONDIC+PH7_MOEDA+PH7_GU

    If PH6->(DbSeek(xFilial("PH6") + cGU + cContra + cRevisa + cNumero + cCompet +  cCondic  ))
        cChavePH7	:= PH6->(PH6_FILIAL+PH6_GU+PH6_CONTRA+PH6_REVISA+PH6_NUMERO+PH6_COMPET+PH6_CONDIC) 
        oAtuPH7	:= TGCVXC09():New()
        If !Empty(ctargetDir)
            oAtuPH7:ctargetDir	:= ctargetDir
        EndIf
        oAtuPH7:cPH7Filial	:= PH6->PH6_FILIAL
        oAtuPH7:cPH7Contra	:= PH6->PH6_CONTRA
        oAtuPH7:cPH7Revisa	:= PH6->PH6_REVISA
        oAtuPH7:cPH7Numero	:= PH6->PH6_NUMERO
        oAtuPH7:cPH7Compet	:= PH6->PH6_COMPET 
        oAtuPH7:cPH7Condic	:= PH6->PH6_CONDIC
        oAtuPH7:cPH7Unineg	:= PH6->PH6_UNINEG 
            
        Do While !PH6->(Eof()) .And. cChavePH7 == PH6->(PH6_FILIAL+PH6_GU+PH6_CONTRA+PH6_REVISA+PH6_NUMERO+PH6_COMPET+PH6_CONDIC) 
            If cMoeda == PH6->PH6_MOEDA 
                oAtuPH7:cPH7Moeda		:= PH6->PH6_MOEDA 
                //::cPH7Status		:= ""
                oAtuPH7:cPH7Grupo	:= PH6->PH6_GRUPO 
                oAtuPH7:cPH7Gu		:= PH6->PH6_GU 
                oAtuPH7:cPH7Anomes	:= PH6->PH6_ANOMES 
                oAtuPH7:cPH7MsBlql	:= PH6->PH6_MSBLQL
                oAtuPH7:cPH7Hroper	:= Time()
                oAtuPH7:dPH7Dtoper	:= MsDate()
                oAtuPH7:nPH7Vltot	+= PH6->PH6_VLTOT
                oAtuPH7:nPH7Vlboni	+= PH6->PH6_VLBONI 
                oAtuPH7:nPH7Vlcare	+= PH6->PH6_VLCARE
                
                oAtuPH7:nPH7Vlcanc	+= PH6->PH6_VLCANC
                oAtuPH7:nPH7Vltran	+= PH6->PH6_VLTRAN
                oAtuPH7:nPH7Vlreaj	+= PH6->PH6_VLREAJ
                oAtuPH7:nPH7Vlnova	+= PH6->PH6_VLNOVA
                oAtuPH7:nPH7Vlrefa	+= 0 //????
                oAtuPH7:nPH7Vlrfat	+= PH6->PH6_VLRFAT
                oAtuPH7:nPH7Bup		+= PH6->PH6_BUP
                oAtuPH7:nPH7Bdown	+= PH6->PH6_BDOWN 
                oAtuPH7:nPH7Vltrib	+= PH6->PH6_VLTRIB 
                oAtuPH7:nPH7Vlfats	+= PH6->PH6_VLFATS
                oAtuPH7:nPH7Vlfat2	+= PH6->PH6_VLFAT2
                oAtuPH7:nPH7Vlraud	+= PH6->PH6_VLRAUD
                oAtuPH7:nPH7Vltrae	+= PH6->PH6_VLTRAE
                oAtuPH7:nPH7Vlincr	+= PH6->PH6_VLINCR
                oAtuPH7:nPH7VlInte	+= PH6->PH6_VLINTE
                oAtuPH7:nPH7IntCan	+= PH6->PH6_INTCAN
                oAtuPH7:nPH7Vlrtrc	+= PH6->PH6_VLRTRC
                oAtuPH7:nPH7Varroy	+= PH6->PH6_VARROY
                oAtuPH7:nPH7Vlmult	+= PH6->PH6_VLMULT
                oAtuPH7:nPH7Qtdrea	+= PH6->PH6_QTDREA
                oAtuPH7:nPH7Vlrea	+= PH6->PH6_VLREA
                oAtuPH7:nPH7Vlrbil	+= PH6->PH6_VLRBIL 
                oAtuPH7:nPH7Varbil	+= PH6->PH6_VARBIL
                oAtuPH7:nPH7DltTrc	+= PH6->PH6_DLTTRC
                oAtuPH7:nPH7xVlrRe	+= PH6->PH6_XVLRRE
                
                oAtuPH7:nTotal		+= 1
                If PH6->PH6_STATUS $ '00/01/02/03/04/05'
                    oAtuPH7:nFat			+= 1
                EndIf
                
                If PH6->PH6_STATUS $ '05'
                    oAtuPH7:nFatur		+= 1
                EndIf
                
                If PH6->PH6_STATUS $ '04'
                    oAtuPH7:nRefat		+= 1
                EndIf
                
                If PH6->PH6_STATUS $ '06'
                    oAtuPH7:nNotFat		+= 1
                EndIf
                
                If PH6->PH6_STATUS $ '07/08'
                    oAtuPH7:nNfCanc		+= 1
                EndIf
                
                If PH6->PH6_STATUS $ '00/01/02/03'
                    oAtuPH7:nAberto		+= 1
                EndIf
                
                If PH6->PH6_STATUS == "00"
                    oAtuPH7:nStat00		+= 1	
                EndIf
                
                If PH6->PH6_STATUS == "01"
                    oAtuPH7:nStat01		+= 1	
                EndIf
                
                If PH6->PH6_STATUS == "02"
                    oAtuPH7:nStat02		+= 1	
                EndIf
                
                If PH6->PH6_STATUS == "03"
                    oAtuPH7:nStat03		+= 1	
                EndIf
                
                If PH6->PH6_STATUS == "04"
                    oAtuPH7:nStat04		+= 1	
                EndIf
                
                If PH6->PH6_STATUS == "05"
                    oAtuPH7:nStat05		+= 1	
                EndIf
                
                If PH6->PH6_STATUS == "06"
                    oAtuPH7:nStat06		+= 1	
                EndIf
                
                If PH6->PH6_STATUS == "07"
                    oAtuPH7:nStat07		+= 1	
                EndIf
                
                If PH6->PH6_STATUS == "08"
                    oAtuPH7:nStat08		+= 1	
                EndIf
            EndIf
            
            PH6->(DbSkip())
        EndDo
        oAtuPH7:ChkStatus()
        oAtuPH7:AtuPH7()
        FreeObj(oAtuPH7)
    EndIf
    
    PH6->(RestArea(aAph6))
    PH7->(RestArea(aAph7))
    RestArea(aArea)

Return


Static Function DelTot(cContra, cRevisa, cAMProc, aMedAnt, aPedAnt)
    Local cCompet  := AMtoCmp(cAMProc)

    PH6->(DbSetOrder(2))
    While PH6->(DbSeek(xFilial("PH6") + cContra + cRevisa + cCompet))
        If ! Empty(PH6->PH6_NUMMED)  
            If Ascan(aMedAnt, { |x| x[1] == PH6->PH6_NUMMED}) == 0
                aadd(aMedAnt, {PH6->PH6_NUMMED, PH6->PH6_DTGMED, PH6->PH6_HRGMED })
            EndIf 
        EndIf
        If ! Empty(PH6->PH6_PEDVEN)  
            If Ascan(aPedAnt, { |x| x[1] == PH6->PH6_PEDVEN}) == 0
                aadd(aPedAnt, {PH6->PH6_PEDVEN, PH6->PH6_DTGPED, PH6->PH6_HRGPED })
            EndIf 
        EndIf
        PH6->(RecLock("PH6", .F.))
        PH6->(DbDelete())
        PH6->(MsUnLock())
    End

    PH7->(DbSetOrder(2))
    While PH7->(DbSeek(xFilial("PH7") + cContra + cRevisa + cCompet))
        PH7->(RecLock("PH7", .F.))
        PH7->(DbDelete())
        PH7->(MsUnLock())
    End
Return

/*
===========================================================================================================
==   funcoes auxiliares
===========================================================================================================
*/

Static Function AMtoCmp(cAM)
    Local cCmp := Right(cAM, 2) + "/" + Left(cAM, 4)

Return cCmp





