#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Service.ProtheusUserRegisterService

using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.MessageErrorMvcUtils

/*/{Protheus.doc} ProtheusUserRegister
This class aims to create an User on Protheus using the model MPUSERACCOUNTDATA
@type class
@version  1.0
<AUTHOR>
@since 10/31/2024
/*/
Class   ProtheusUserRegister
    Private Data    oUser       As  Object
    Private Data    oGroup      As  Object
    Private Data    cError      As  Character
    Private Data    aError      As  Array
    Private Data    aRetFun     As  Array

    Public  Method  new() Constructor
    Public  Method  activeModel()
    Public  Method  chooseOperation(nOperation As Numeric)
    Public  Method  procesJson(cJson As Character)
    Public  Method  difuseVoid()
    Public  Method  validadeData() 	As Logical
    Public  Method  setValuePersonal(cPar1 As character,cPar2 As character,cPar3 As variant)
    Public  Method  setValuePersonalGroup(cPar1 As character,cPar2 As character)
    Public  Method  commitDataPersonal()
    Public  Method  getArrayError() As  Array
    Public  Method  deActiveModelPersonal() As  Object
EndClass

/*/{Protheus.doc} ProtheusUserRegister
This method creates a new instance of the class
@type class
@version  1.0
<AUTHOR>
@since 10/31/2024
@return object, Return the instantiated object itself
/*/
Method		new()  Class  ProtheusUserRegister
	::oUser  	:=	FWLoadModel( "MPUSERACCOUNTDATA" )
	::oGroup 	:=	::oUser:GetModel( "DATAGROUP" ) 
	::cError 	:=	''
	::aRetFun	:=	{ .T., "" }
Return Self

/*/{Protheus.doc} ProtheusUserRegister::activeModel
This method create active the model of data
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return object, Return the instantiated object itself
/*/
Method  activeModel()   Class   ProtheusUserRegister
	::oUser:Activate()
Return  Self

/*/{Protheus.doc} ProtheusUserRegister::chooseOperation
This method aims to choose the operation of the model
List of Operation
3 - Inclusion
4 - Alteration
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@param nOperation, numeric, the operation of the model
@return Object, Return the instantiated object itself
/*/
Method  chooseOperation(nOperation  as  Numeric)    Class   ProtheusUserRegister
	::oUser:SetOperation( nOperation )
Return Self

/*/{Protheus.doc} ProtheusUserRegister::difuseVoid
This method apply a comand of difuse on the Model
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  difuseVoid()  Class  ProtheusUserRegister
	::oUser:GetModel("PROTHEUSMENU"):DiffuseVoid()
Return Self

/*/{Protheus.doc} ProtheusUserRegister::validadeData
This method apply an validation on Model
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return logical, Return a logical value to inform if the validation was successful 
/*/
Method  validadeData()  As  Logical Class   ProtheusUserRegister 
	Local lValidade as Logical
	lValidade:= ::oUser:VldData()
Return lValidade


/*/{Protheus.doc} ProtheusUserRegister::setValuePersonal
This method set a value to the model
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@param cPar1, character, the object Data User
@param cPar2, character, The name of the Table Field
@param cPar3, variant, The Value that will be set on table Field
@return Object, Return the object instantiated itself
/*/
Method  setValuePersonal(cPar1 as character,cPar2 as character,cPar3 as variant)    Class   ProtheusUserRegister
	If ! ::oUser:SetValue( cPar1, cPar2, cPar3)
		::cError := MessageErrorMvc():getError( ::oUser, ::cError )
	Endif
Return Self

/*/{Protheus.doc} ProtheusUserRegister::setValuePersonalGroup
This method set a value to the model
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@param cPar1, character, The name of the Table Field
@param cPar2, variant, The value that will be set on table field
@return Object, Return the instantiated object itself
/*/
Method  setValuePersonalGroup(cPar1 as character,cPar2 as variant)  Class   ProtheusUserRegister
	If ! ::oGroup:SetValue( cPar1, cPar2)
		::cError := MessageErrorMvc():getError( ::oUser, ::cError )
	Endif
Return Self

/*/{Protheus.doc} ProtheusUserRegister::commitDataPersonal
This method commit the data on the model to Create an user on table sys_usr
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, The instantiated object itself
/*/
Method  commitDataPersonal()    Class   ProtheusUserRegister
	::oUser:CommitData()
Return Self

/*/{Protheus.doc} ProtheusUserRegister::getArrayError
This method aims to get the error in the process
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Array, Return the array with the errors
/*/
Method  getArrayError()     Class   ProtheusUserRegister
	Local aError 								:= 	{} 				As Array
	Local CRLF      						:=  Chr(13)+Chr(10) As Character
	Local MODEL_MSGERR_MESSAGE	:=	6				As Numeric	
	
	aError := ::oUser:GetErrorMessage()
	If(Empty( ::cError ))
		::cError += ''
		::aRetFun[1] := .T.
    ::aRetFun[2] := ::cError
	Else
		::cError += CRLF + AllTrim( aError[MODEL_MSGERR_MESSAGE] )
		::aRetFun[1] := .F.
    ::aRetFun[2] := ::cError
	EndIf	
Return ::aRetFun

/*/{Protheus.doc} ProtheusUserRegister::deActiveModelPersonal
This method aims to execute the method deactive on object from model
@type method
@version  1.0
<AUTHOR>
@since 12/11/2024
@return object, The object itself
/*/
Method  deActiveModelPersonal() As  Object  Class   ProtheusUserRegister
    ::oUser:DeActivate()
Return  Self
