#INCLUDE "Totvs.CH"


//-------------------------------------------------------------------
/*/{Protheus.doc} TSRVR008 
Relatorio de cancelamentos de servicos (antigo CANCEL por projetos)
<AUTHOR> Mobile
@since 17/09/2015
/*/
//-------------------------------------------------------------------

User Function TSRVR008()

	Local oReport
 	Local cAlisrv 		:= GetNextAlias()
	
	Local nOpca := 0
	
	Private cTabPad	:= ""
	Private aGrupos	:= {}
	Private cPerg 	:= "TSRVR008"
	Private cNSaidaDe	:= ""
	Private cNSaidaAte:= ""
	Private cDSaidaDe	:= CTOD("")
	Private cDSaidaAte:= CTOD("")
	Private cNEntrDe	:= ""
	Private cNEntrAte	:= ""
	Private cDEntrDe	:= CTOD("")
	Private cDEntrAte	:= CTOD("")
	Private cNPrjDe	:= ""
	Private cNPrjAte	:= ""
	Private cUnidade	:= "" 
	
	AjustaSX1()
	Pergunte(cPERG,.F. )

	oReport := ReportDef(cAlisrv)
	oReport:PrintDialog()
	
Return


//-------------------------------------------------------------------
/*/{Protheus.doc} ReportDef 
Definicao do relatorio
<AUTHOR> Mobile
@since 17/09/2015
/*/
//-------------------------------------------------------------------

Static Function ReportDef(cAlisrv)
	
	Local oReport
	Local oSection1
	Local oSection2
	Local cNomeRel := "Relatorio Cancel"
	
	oReport:= TReport():New( "TSRVR008" , cNomeRel , cPerg , {|oReport| ReportPrint(oReport,cAlisrv)} , cNomeRel )
	oReport:SetLandScape(.T.)
	
	oSection1:= TRSection():New(oReport,"Notas x Projetos")
	
//	TRCell():New(/*oSection*/,/*X3_CAMPO*/,/*Tabela*/,/*Titulo*/,/*Picture*/,/*Tamanho*/,/*lPixel*/,/*{|| code-block de impressao }*/)	
	TRCell():New(oSection1,"PE5_UNSRV"	, "PE5" ,"U.Serv."		,"@!"				, 3		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"PE1_DESCRI", "PE1" ,"Descr.U.Serv."	,"@!"				,30		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"CODEMPFIL"	,		 ,"Emp/Fil"		,"@!"				,13		,.F.,,,,,,,.F.)		// 1
	TRCell():New(oSection1,"EMPRESA"	,		 ,"Empresa"		,"@!"				,50		,.F.,,,,,,,.F.)		// 2
	TRCell():New(oSection1,"CLIENTE"	,		 ,"Cliente"		,"@!"				, 6		,.F.,,,,,,,.F.)		// 3
	TRCell():New(oSection1,"LOJA"		,		 ,"Loja"			,"@!"				, 2		,.F.,,,,,,,.F.)		// 4
	TRCell():New(oSection1,"RAZAOSOCIA",		 ,"Razao Social"	,"@!"				,50		,.F.,,,,,,,.F.)		// 5
	TRCell():New(oSection1,"SEGMENTO"	,		 ,"Segmento"		,"@!"				, 6		,.F.,,,,,,,.F.)		// 6
	TRCell():New(oSection1,"DESCSEGPOR",		 ,"Descricao"	   	,"@!"				,30		,.F.,,,,,,,.F.)		// 7
	TRCell():New(oSection1,"PEA_PROJET", "PEA" ,"Projeto"		,"@!"				,10		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"PE5_CLIENT", "PE5" ,"ClientePRJ"	,"@!"				, 6		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"PE5_LOJA"	, "PE5" ,"LojaPRJ"	 	,"@!"				, 2		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"NOMECLIPRJ", 		 ,"NomeCliPRJ"	,"@!"				,50		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"PE5_DESPRO", "PE5" ,"Descr.Projeto"	 ,"@!"				,50		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"PE5_STATUS", "PE5" ,"Status"		    ,"@!"				, 1		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"PE5_EMISSA", "PE5" ,"Emis.Proj."	,"@!"				,10		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"CODGPP"		,		,"Cod Gpp"		    ,"@!"				, 6		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"NOMEGPP"	,		,"Nome Gpp"		,"@!"				,30		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"PARCCFP"	,		,"Parcela"	     	,"@!"				,06		,.F.,,,,,,,.F.)		// 8
	TRCell():New(oSection1,"PRODUTO"	,		,"Produto"		    ,"@!"				,10		,.F.,,,,,,,.F.)		//12
	TRCell():New(oSection1,"DESCPROD"  ,      ,"Descr.Produto"	,"@!"				,30		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"GRUPO"		,		,"Grupo"		    ,"@!"				,40		,.F.,,,,,,,.F.)		//13
	TRCell():New(oSection1,"PEA_VLHORA",		 ,"Valor Hora"	,"@E 999,999.99"	,10		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"PEA_FILFAT", "PEA" ,"Filial Fatura" ,"@!"				,13		,.F.,,,,,,,.F.)		//
	TRCell():New(oSection1,"NOTAORI"	,		,"NF Origem"		,"@!"				,20		,.F.,,,,,,,.F.)		// 8
	TRCell():New(oSection1,"SERIEORI"	,		,"Ser.Orig."		,"@!"				, 3		,.F.,,,,,,,.F.)		// 9
	TRCell():New(oSection1,"EMISSORI"	,		,"Dt.Orig."		,"@!"				,10		,.F.,,,,,,,.F.)		//10
	TRCell():New(oSection1,"ITEMNF"		,		,"It.NF.Orig."    ,"@!"				, 2		,.F.,,,,,,,.F.)		//11
	TRCell():New(oSection1,"NOTA"		,		,"NF Canc."		,"@!"				,20		,.F.,,,,,,,.F.)		// 8
	TRCell():New(oSection1,"SERIE"		,		,"Ser.Canc."      ,"@!"				, 3		,.F.,,,,,,,.F.)		// 9
	TRCell():New(oSection1,"EMISSNF"	,		,"Dt.Canc."		,"@!"				,10		,.F.,,,,,,,.F.)		//10
	TRCell():New(oSection1,"QTDENF"		,		,"Qtde.Canc."	    ,"@E 999,999.99"	,10		,.F.,,,,,,,.F.)		//14
	TRCell():New(oSection1,"UNITNF"		,		,"Vlr.Unit."   	,"@E 999,999,999.99",14		,.F.,,,,,,,.F.)		//15
	TRCell():New(oSection1,"TOTALNF"	,		,"Tot.Moeda 1"	,"@E 999,999,999.99",14		,.F.,,,,,,,.F.)		//16
	TRCell():New(oSection1,"TOTALN2"	,		,"Tot.Moeda 2"	,"@E 999,999,999.99",14		,.F.,,,,,,,.F.)		//17
	TRCell():New(oSection1,"TOTALN3"	,		,"Tot.Moeda 3"	,"@E 999,999,999.99",14		,.F.,,,,,,,.F.)		//18
	TRCell():New(oSection1,"HRSCANC"	,		,"Hrs. Canc."	,"@E 999,999.99"	,10		,.F.,,,,,,,.F.)		//19
	TRCell():New(oSection1,"MOEDAORI"   ,		,"Moeda"	    ,"@!"	            ,1		,.F.,,,,,,,.F.)		//20
	
Return(oReport)


//-------------------------------------------------------------------
/*/{Protheus.doc} ReportPrint 
Impressao do relatorio
<AUTHOR> Mobile
@since 17/09/2015
@obs Incluso os campos com os dados do cliente do projeto. 
	  Egidio Jr - 22/09/2016.
/*/
//-------------------------------------------------------------------

Static Function ReportPrint(oReport,cAlisrv)
	
	Local oSection1b 	:= oReport:Section(1)
	Local aQuery		:= {}
	Local cTab			:= ""
	Local cUndOK     	:= ""
	Local cTabTemp 		:= GetNextAlias()
	Local oTempTable
	Local cArqDestino 	:= ""

	Local oExcel		:= MntExcel():New()
	Local cNameRel		:= ''
	
	//-- chama funcao para gerar tabela temporaria lendo todas as empresas	
	RunExec( cTabTemp, @oTempTable )
	
	cTab := "%" + oTempTable:GetRealName() + "%"
	
	cUndOK := FormatIn(AllTrim(cUnidade),";") 
	cUndOK :="% PE5_UNSRV IN "+cUndOK+" %"
	oSection1b:BeginQuery()
	
	BeginSQL Alias cAlisrv
	
	Column PE5_EMISSA as Date
	COLUMN QTDENF    AS NUMERIC(18,2)
	COLUMN UNITNF    AS NUMERIC(18,2)
	COLUMN TOTALNF   AS NUMERIC(18,2)
	COLUMN TOTALN2   AS NUMERIC(18,2)
	COLUMN TOTALN3   AS NUMERIC(18,2)
	COLUMN HRSCANC   AS NUMERIC(18,2)	
	COLUMN PFZQTDCAN AS NUMERIC(18,2)	
	
	SELECT 
	CODEMPFIL,
	EMPRESA,
	CLIENTE,
	LOJA,
	RAZAOSOCIA,
	SEGMENTO,
	DESCSEGPOR,
	PEA_FILFAT,
	PEA_PROJET ,
	PE5_CLIENT 	AS CLIENTEPRJ,
	PE5_LOJA	 	AS LOJAPRJ,
	SA11.A1_NOME  AS NOMECLIPRJ,
	PEA_QTDFAT,
	PEA_VLHORA,
	PE5_STATUS,
	PE5_DESPRO,
	PE5_UNSRV,
	PE1_DESCRI,
	PE5_EMISSA,
	CASE WHEN PE7GPP.PE7_CODREC IS NULL THEN 'PAPEL ' || CASE WHEN PE7GPP.PE7_PAPEL IS NULL THEN 'EM BRANCO' ELSE PE7GPP.PE7_PAPEL END ELSE PE7GPP.PE7_CODREC END AS CODGPP,
	RD0GPP.RD0_NOME   AS NOMEGPP,
	PE7DES.PE7_CODREC AS LDESENV,
	RD0DES.RD0_NOME   AS NOMLDESENV,
	PEA.PEA_PARCEL AS PARCCFP,
	NOTA,
	SERIE,
	EMISSNF,
	NOTAORI,
	SERIEORI,
	EMISSORI,
	ITEMNF,
	PRODUTO,
	DESCPROD,
	GRUPO,
	QTDENF,
	UNITNF,
	TOTALNF,
	TOTALN2,
	TOTALN3,
	PFZQTDCAN HRSCANC,
	//CASE WHEN SERIE = 'IMP' THEN 0 ELSE CASE WHEN QTDENF BETWEEN 0 AND 1 THEN CASE WHEN PEA.PEA_MOEFAT = '1' THEN Round(TOTALNF / Round(PEA.PEA_VFMOE1/PEA.PEA_QTDPAR, 2), 2) ELSE Round(TOTALN2 / Round(PEA.PEA_VFMOE2/PEA.PEA_QTDPAR, 2), 2) END ELSE QTDENF END END HRSCANC,
	MOEDAORI
	
	FROM %exp:cTab% TABPAD
	
	INNER JOIN %Table:PEA% PEA
	ON  TABPAD.NOTAORI   = PEA.PEA_NUMFAT
	AND TABPAD.SERIEORI  = PEA.PEA_SERFAT
	AND TABPAD.ITEMNF    = PEA.PEA_ITEMOR
	AND TABPAD.CODEMPFIL = PEA.PEA_FILFAT
	AND PEA.%NotDel%
	
	INNER JOIN %Table:PE5% PE5
	ON  PE5.PE5_PROJET = PEA.PEA_PROJET
	AND %Exp:cUndOK% //PE5_UNSRV IN %Exp:cUndOK% // erro do fonte antigo
	AND PE5.%NotDel%
	
	INNER JOIN %Table:PE1% PE1
	ON  PE1.PE1_CODIGO = PE5.PE5_UNSRV
	AND PE1.%NotDel%
	
	LEFT JOIN %Table:SA1% SA11
	ON SA11.A1_FILIAL = %xFilial:SA1%
	AND SA11.%NotDel%
	AND SA11.A1_COD = PE5.PE5_CLIENT
	AND SA11.A1_LOJA = PE5.PE5_LOJA
	
	LEFT JOIN %Table:PE7% PE7GPP
	ON  PE5.PE5_PROJET   = PE7GPP.PE7_PROJET
	AND PE7GPP.PE7_PAPEL = 'GPP'
	AND PE7GPP.%NotDel%
	
	LEFT JOIN %Table:RD0% RD0GPP
	ON RD0GPP.RD0_CODIGO = PE7GPP.PE7_CODREC
	AND RD0GPP.%NotDel%
	
	LEFT JOIN %Table:PE7% PE7DES
	ON  PE5.PE5_PROJET = PE7DES.PE7_PROJET
	AND PE7DES.PE7_PAPEL = 'LD'
	AND PE7DES.%NotDel%
	
	LEFT JOIN %Table:RD0% RD0DES
	ON RD0DES.RD0_CODIGO = PE7DES.PE7_CODREC
	AND RD0DES.%NotDel%

	ORDER BY NOTA, SERIE
	
	EndSQL

	aQuery := GetLastQuery()
	
	MemoWrite("C:\Query\TSRVA204.sql",aQuery[2])
	
	oSection1b:EndQuery()
	oSection1b:SetParentQuery()
	oReport:SetMeter((cAlisrv)->(RecCount()))
	
	oSection1b:Print()
	
	DbSelectArea(cAlisrv)
	//COPIA ARQUIVO DE CANCELAMENTOS
	(cAlisrv)->(DbGoTop())

	cNameRel		:= "REL_CANC_" + DtoS(Date()) + "_" + StrTran(Time(), ":", "_") + ".xls"
	//Alterado por Hermiro Junior - 24/01/20

	//Vers�o de Build n�o contempla mais gera��o de arquivo DBF para abertura no Excel.
	//Alterado para gera��o de Excel.
	
	/*
	oExcel:SetNameArq(cNameRel)
	oExcel:SetTitle('Relat�rio de Cancelamentos')
	oExcel:SetCab((cAlisrv)->(dbStruct()))
	oExcel:SetTabTemp(cAlisrv)
	oExcel:Create(oExcel)
	*/


	If !ExistDIR("C:\APEXCEL")
		If	MAKEDIR("C:\APEXCEL")!= 0
			Aviso("Erro" ,"Nao foi possivel criar diretorio: C:\APEXCEL\",{"OK"}) //"Inconsistencia"###"Nao foi possivel criar diretorio "###".Finalizando ..."
			return nil
		EndIf
	EndIf

	//cFile := "TSRVR009"+DtoS(Date())+STRTRAN(Time(), ":", "")+".XLS"
	cArqDestino := "C:\APEXCEL\"+cNameRel

	If U_TRGXLX01((cAlisrv), @cArqDestino, , /*cTitle*/, .f./*lAskOpen*/)
	//	MsgAlert('Arquivo de Conferencia salvo em '+cArqDestino,'AVISO')
	EndIf





	If Select(cAlisrv) > 0 
		(cAlisrv)->( dbCloseArea() )
	EndIf
	
Return


//-------------------------------------------------------------------
/*/{Protheus.doc} RunExec 
Chamada do processamento das empresas e geracao do arquivo temporario
<AUTHOR> Mobile
@since 17/09/2015
/*/
//-------------------------------------------------------------------

Static Function RunExec( cTabTemp, oTempTable )
	
	Local cQuery := ""
	
	cNSaidaDe	:= MV_PAR01
	cNSaidaAte	:= MV_PAR02
	cDSaidaDe	:= MV_PAR03
	cDSaidaAte	:= MV_PAR04
	cNEntrDe	:= MV_PAR05
	cNEntrAte	:= MV_PAR06
	cDEntrDe	:= MV_PAR07
	cDEntrAte	:= MV_PAR08
	cNPrjDe	:= MV_PAR09
	cNPrjAte	:= MV_PAR10
	cUnidade	:= MV_PAR11
	
	U_GetCanc( cTabTemp, @oTempTable )
	
Return


//-------------------------------------------------------------------
/*/{Protheus.doc} GetCanc 
Processamento das empresas e geracao do arquivo temporario
<AUTHOR> Mobile
@since 17/09/2015
/*/
//-------------------------------------------------------------------

User Function GetCanc(cTabTemp, oTempTable)
	
	Local cAliasEmp	:= GetNextAlias()
	Local aFieldsX	:= {}
	Local cQueryEmp	:= ""
	Local nX			:= 0
	Local nConta		:= 0
	Local cUndOK  	:= FormatIn(AllTrim(cUnidade),";")
	Local nPFZQtd	:= 0
	Local cWhere    := ''
	Local aDados		:= {} //Ticket 299777 - Devido ao TdiPrepEnv, o Arquivo Temporario era fechado, utilizamos um array para conter os dados para depois gravar no Temporario
	Local cQuery 	:= ""

	//-- query para buscar as empresas/filiais onde tem cancelamento para 
	//-- as parcelas das unidades seleciondas
	//-- para o intervalo de notas
	//-- e para os itens que tiveram cancelamento

	If !Empty(cNPrjDe) .AND. !Empty(cNPrjAte)
		cWhere += "AND PEA_PROJET BETWEEN '" + cNPrjDe + "' AND '" + cNPrjAte + "' "
	Else	
		cWhere := " AND 1 = 1" //erro fonte original
	EndIf

	//BeginSql Alias cAliasEmp

	cQuery := "SELECT DISTINCT PEA_FILFAT AS FILIAL "
	cQuery += " FROM "+RetSqlName("PEA")+" PEA " 
	cQuery += " INNER JOIN "+RetSqlName("PE5")+" PE5 "
	cQuery += " ON PE5_FILIAL = '"+xFilial("PE5")+"'"
	cQuery += " AND PE5_PROJET = PEA_PROJET 
	cQuery += " AND PE5_UNSRV IN "+cUndOK 
	cQuery += " AND PE5.D_E_L_E_T_=' ' "
	cQuery += " WHERE PEA_FILIAL ='" + xFilial("PEA") +"'"
	cQuery += " AND PEA.D_E_L_E_T_=' ' " 
	//  %Exp:cWhere %  //erro fonte original
	
	//EndSql
	

	
	cQuery := ChangeQuery(cQuery)
	
	cAliasEmp := MPSysOpenQuery(cQuery)


	
	(cAliasEmp)->(dbGoTop())
	
	While !(cAliasEmp)->(EOF())
		
		cFilVer := substr((cAliasEmp)->FILIAL,3)
		nPos	:= aScan(aGrupos, {|X| X[2] == cFilVer })
		
		If nPos == 0
			aAdd(aGrupos,{substr((cAliasEmp)->FILIAL,1,2),substr((cAliasEmp)->FILIAL,3)})
		EndIf
		
		(cAliasEmp)->(dbSkip())
	End
	
	aGrupos := aSort(aGrupos,,,{|x,y| (x[1]+x[2])<(y[1]+y[2]) })
	
	(cAliasEmp)->(dbCloseArea())
	
	cEmpBkp := cEmpAnt
	cFilBkp := cFilAnt
	
	AAdd( aFieldsX, {"CODEMPFIL"  , "C" , 13					,0 } )	// 1
	AAdd( aFieldsX, {"EMPRESA"    , "C" , 50					,0 } )	// 2
	AAdd( aFieldsX, {"CLIENTE"    , "C" , TAMSX3("A1_COD")[1]	,0 } )	// 3
	AAdd( aFieldsX, {"LOJA"       , "C" , TAMSX3("A1_LOJA")[1]	,0 } )	// 4
	AAdd( aFieldsX, {"RAZAOSOCIA" , "C" , TAMSX3("A1_NOME")[1]	,0 } )	// 5
	AAdd( aFieldsX, {"SEGMENTO"   , "C" ,  6					,0 } )	// 6
	AAdd( aFieldsX, {"DESCSEGPOR" , "C" , 50					,0 } )	// 7
	AAdd( aFieldsX, {"NOTA"       , "C" , 20					,0 } )	// 8
	AAdd( aFieldsX, {"SERIE"      , "C" ,  3					,0 } )	// 9
	AAdd( aFieldsX, {"EMISSNF"    , "C" , 10					,0 } )	//10
	AAdd( aFieldsX, {"NOTAORI"    , "C" , 20					,0 } )	//11
	AAdd( aFieldsX, {"SERIEORI"   , "C" , 3						,0 } )	//12	
	AAdd( aFieldsX, {"EMISSORI"   , "C" , 10					,0 } ) //13	
	AAdd( aFieldsX, {"ITEMNF"     , "C" ,  2					,0 } )	//14
	AAdd( aFieldsX, {"PRODUTO"    , "C" , 10					,0 } )	//15
	AAdd( aFieldsX, {"DESCPROD"   , "C" , 30					,0 } )	//16
	AAdd( aFieldsX, {"GRUPO"      , "C" ,  4					,0 } )	//17
	AAdd( aFieldsX, {"QTDENF"     , "N" , 10					,2 } )	//18
	AAdd( aFieldsX, {"UNITNF"     , "N" , 14					,2 } )	//19
	AAdd( aFieldsX, {"TOTALNF"    , "N" , 14					,2 } )	//20
	AAdd( aFieldsX, {"TOTALN2"    , "N" , 14					,2 } )	//21
	AAdd( aFieldsX, {"TOTALN3"    , "N" , 14					,2 } )	//22
	AAdd( aFieldsX, {"MOEDAORI"   , "C" ,  1					,0 } )	//23
	AAdd( aFieldsX, {"PFZQTDCAN"  , "N" , 14					,2 } )	//24
	
	
	For nX := 1 to Len(aGrupos)
		
		U_TdiPrepEnv( 2, aGrupos[nX,1], aGrupos[nX,2] )

		cTabPad	:= U_FATR080QRY( cNSaidaDe, cNSaidaAte, cDSaidaDe, cDSaidaAte, cNEntrDe, cNEntrAte, cDEntrDe, cDEntrAte )
		
		/*/------------------ LAY OUT DA TABELA TEMPORARIA RETORNADA DA FUNCAO FATR080QRY ---------------------------
		D1_FORNECE
		D1_TOTAL
		D1_VALDESC
		A1_COD
		A1_LOJA
		A1_NOME
		A1_PESSOA
		A1_CGC
		A1_CODSEG
		AOV_DESSEG
		A1_CODTER
		AOY_NMTER
		A1_TPMEMB
		A1_CODMEMB
		F1_SERIE
		F1_DOC
		F1_DTDIGIT
		F1_MOEDA
		F1_MOTRET
		F1_TIPO
		F2_SERIE
		F2_DOC
		F2_EMISSAO
		F2_VALBRUT
		D2_SERIE
		D2_ITEM
		E1_VENCREA
		BM_GRUPO
		BM_DESC
		B1_COD
		B1_DESC
		F4_CODIGO
		RECNOSD1
		D2_CONTA
		CT1_DESC01
		D2_CLVL
		D2_CCCUSTO
		D2_ITEMCC
		RECNOREF
		CTT_DEC01
		INSS
		PIS
		COFINS
		ISS
		-------------------------------- LAY OUT DA TABELA TEMPORARIA ---------------------------------- /*/
		
		dbSelectArea(cTabPad)
		(cTabPad)->(dbGoTop())
		
		While !(cTabPad)->(EOF())
			DbSelectArea("SD1")
			SD1->(DbGoTo((cTabPad)->RECNOSD1))
			
			DbSelectArea("PFZ")
			PFZ->( DbSetOrder(2) )	//PFZ_FILIAL+PFZ_SERNCC+PFZ_NUMNCC+PFZ_ITNCC+PFZ_SERORI+PFZ_NFORI+PFZ_ITEMOR
			
			If PFZ->( MsSeek(xFilial("PFZ")+SD1->(D1_SERIE+D1_DOC+D1_ITEM+D1_SERIORI+D1_NFORI+D1_ITEMORI)) ) .AND. PFZ->PFZ_CLASSI == "1"
				nPFZQtd	:= PFZ->PFZ_QTDCAN
			Else
				nPFZQtd	:= 0
			EndIf
			
			AAdd(aDados, { ;
	/*1	*/			cEmpAnt+cFilAnt	,;	
	/*2	*/			AllTrim( SM0->M0_NOME ) + " " + AllTrim( SM0->M0_FILIAL )	,;	
	/*3	*/			(cTabPad)->A1_COD	,;	
	/*4	*/			(cTabPad)->A1_LOJA	,;	
	/*5	*/			(cTabPad)->A1_NOME	,;	
	/*6	*/			(cTabPad)->A1_CODSEG	,;	
	/*7	*/			(cTabPad)->AOV_DESSEG	,;	 
	/*8	*/			(cTabPad)->F1_DOC	,;	
	/*9	*/			(cTabPad)->F1_SERIE	,;	
	/*10*/			DTOC(STOD((cTabPad)->F1_DTDIGIT))	,;	
	/*11*/			(cTabPad)->F2_DOC	,;	
	/*12*/			(cTabPad)->F2_SERIE	,;		
	/*13*/			(cTabPad)->D2_ITEM	,;	
	/*14*/			DTOC(STOD((cTabPad)->F2_EMISSAO))	,;	
	/*15*/			(cTabPad)->B1_COD	,;	
	/*16*/			(cTabPad)->B1_DESC	,;	
	/*17*/			(cTabPad)->BM_GRUPO	,;	
	/*18*/			SD1->D1_QUANT	,;	
	/*19*/			SD1->D1_VUNIT	,;	
	/*20*/		    IIf((cTabPad)->F1_MOEDA==1, SD1->D1_TOTAL - SD1->D1_VALDESC, xMoeda((SD1->D1_TOTAL - SD1->D1_VALDESC), (cTabPad)->F1_MOEDA, 1, STOD((cTabPad)->F1_DTDIGIT) ))	,;	 //Ticket #54086
	/*21*/			IIf((cTabPad)->F1_MOEDA==2, SD1->D1_TOTAL - SD1->D1_VALDESC, xMoeda((SD1->D1_TOTAL - SD1->D1_VALDESC), (cTabPad)->F1_MOEDA, 2, STOD((cTabPad)->F2_EMISSAO) ))	,;	 //Ticket #54086
	/*22*/			IIf((cTabPad)->F1_MOEDA==3, SD1->D1_TOTAL - SD1->D1_VALDESC, xMoeda((SD1->D1_TOTAL - SD1->D1_VALDESC), (cTabPad)->F1_MOEDA, 3, STOD((cTabPad)->F2_EMISSAO) ))	,;	  //Ticket #54086
	/*23*/			cValToChar( (cTabPad)->F1_MOEDA )	,;	
	/*24*/			nPFZQtd	,;	
			})
						
			(cTabPad)->(dbSkip())
		End
	
	Next nX
	
	U_TdiPrepEnv( 2, cEmpBkp, cFilBkp )

	//Ticket 299777 - Devido ao TdiPrepEnv, o Arquivo Temporario era fechado, utilizamos um array para conter os dados para depois gravar no Temporario
	oTempTable := FWTemporaryTable():New( cTabTemp )
	oTemptable:SetFields( aFieldsX )
	aFieldsX := {}
	oTempTable:AddIndex("SRVR8", {"NOTA","SERIE"} )
	//���������������������Ŀ
	//� Cria��o da tabela	�
	//�����������������������
	oTempTable:Create()
	cNTabTemp 	:= oTempTable:GetRealName()
	For nX:=1 to Len(aDados)
			If !Empty(cTabTemp) .and. Select(cTabTemp) > 0
				RecLock(cTabTemp,.T.)
	/*1	*/			(cTabTemp)->CODEMPFIL  	:= aDados[nX,1]
	/*2	*/			(cTabTemp)->EMPRESA    	:= aDados[nX,2]
	/*3	*/			(cTabTemp)->CLIENTE    	:= aDados[nX,3]
	/*4	*/			(cTabTemp)->LOJA       	:= aDados[nX,4]
	/*5	*/			(cTabTemp)->RAZAOSOCIA 	:= aDados[nX,5]
	/*6	*/			(cTabTemp)->SEGMENTO   	:= aDados[nX,6]
	/*7	*/			(cTabTemp)->DESCSEGPOR 	:= aDados[nX,7] 
	/*8	*/			(cTabTemp)->NOTA       	:= aDados[nX,8]
	/*9	*/			(cTabTemp)->SERIE      	:= aDados[nX,9]
	/*10*/			(cTabTemp)->EMISSNF    	:= aDados[nX,10]
	/*11*/			(cTabTemp)->NOTAORI    	:= aDados[nX,11]
	/*12*/			(cTabTemp)->SERIEORI   	:= aDados[nX,12]	
	/*13*/			(cTabTemp)->ITEMNF     	:= aDados[nX,13]
	/*14*/			(cTabTemp)->EMISSORI   	:= aDados[nX,14]
	/*15*/			(cTabTemp)->PRODUTO    	:= aDados[nX,15]
	/*16*/			(cTabTemp)->DESCPROD   	:= aDados[nX,16]
	/*17*/			(cTabTemp)->GRUPO      	:= aDados[nX,17]
	/*18*/			(cTabTemp)->QTDENF	   		:= aDados[nX,18]
	/*19*/			(cTabTemp)->UNITNF     	:= aDados[nX,19]
	/*20*/		    (cTabTemp)->TOTALNF    	:= aDados[nX,20]
	/*21*/			(cTabTemp)->TOTALN2    	:= aDados[nX,21]
	/*22*/			(cTabTemp)->TOTALN3    	:= aDados[nX,22]
	/*23*/			(cTabTemp)->MOEDAORI   	:= aDados[nX,23]
	/*24*/			(cTabTemp)->PFZQTDCAN  	:= aDados[nX,24]
				(cTabTemp)->(MsUnlock())
			EndIf
	Next nX
	
Return


//-------------------------------------------------------------------
/*/{Protheus.doc} AjustaSx1 
Ajusta perguntas 
<AUTHOR> Marcondes
@since 17/09/2015
/*/
//-------------------------------------------------------------------

Static Function AjustaSx1()

	Local _sAlias := Alias()
	Local aRegs :={}
	Local i := 0, j := 0
	Local cGrupo     := ''
	Local cOrdem	 := ''
	Local cPergunt	 := ''
	Local cPerSpa	 := ''
	Local cPerEng	 := ''
	Local cVar		 := ''
	Local cTipo		 := ''
	Local nTamanho   := 0
	Local nDecimal   := 0 
	Local nPresel	 := 0
	Local cGSC		 := ''
	Local cValid	 := ''
	Local cF3		 := ''
	Local cGrpSxg	 := ''
	Local cPyme	     := ''
	Local cVar01	 := ''
	Local cDef01	 := ''
	Local cDefSpa1	 := ''
	Local cDefEng1	 := ''
	Local cCnt01     := ''
	Local cDef02     := ''
	Local cDefSpa2	 := ''
	Local cDefEng2	 := ''
	Local cDef03	 := ''
	Local cDefSpa3	 := ''
	Local cDefEng3	 := ''
	Local cDef04	 := ''
	Local cDefSpa4	 := ''
	Local cDefEng4	 := ''
	Local cDef05	 := ''
	Local cDefSpa5	 := ''
	Local cDefEng5	 := ''
	Local aHelpPor	 := {}
	Local aHelpEng	 := {}
	Local aHelpSpa	 := {}
	Local cHelp		 := ''
	
	aRegs := {}
	AADD(aRegs,{cPerg,"01","Nota de Saida de?"	,"Nota de Saida de?"	,"Nota de Saida de?"	,"mv_ch1","C",tamSx3("F2_DOC")[1]		,0,0,"G",""                    ,"mv_par01",""       ,""       ,""       ,"","",""         ,""         ,""         ,"","",""       ,""       ,""       ,"","","","","","","","","","","","SF2"  	})
	AADD(aRegs,{cPerg,"02","Nota de Saida ate?"	,"Nota de Saida ate?"	,"Nota de Saida ate?"	,"mv_ch2","C",tamSx3("F2_DOC")[1]		,0,0,"G",""                    ,"mv_par02",""       ,""       ,""       ,"","",""         ,""         ,""         ,"","",""       ,""       ,""       ,"","","","","","","","","","","","SF2"  	})
	AADD(aRegs,{cPerg,"03","Emissao de?"			,"Emissao de?"			,"Emissao de?"			,"mv_ch3","D",tamSx3("F2_EMISSAO")[1]	,0,0,"G",""                    ,"mv_par03",""       ,""       ,""       ,"","",""         ,""         ,""         ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""		})
	AADD(aRegs,{cPerg,"04","Emissao ate?"			,"Emissao ate?"			,"Emissao ate?"			,"mv_ch4","D",tamSx3("F2_EMISSAO")[1]	,0,0,"G",""                    ,"mv_par04",""       ,""       ,""       ,"","",""         ,""         ,""         ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""		})
	AADD(aRegs,{cPerg,"05","Nota de Entrada de?"	,"Nota de Entrada de?"	,"Nota de Entrada de?"	,"mv_ch5","C",tamSx3("F1_DOC")[1]		,0,0,"G",""                    ,"mv_par05",""       ,""       ,""       ,"","",""         ,""         ,""         ,"","",""       ,""       ,""       ,"","","","","","","","","","","","SF1"	})
	AADD(aRegs,{cPerg,"06","Nota de Entrada ate?"	,"Nota de Entrada de?"	,"Nota de Entrada de?"	,"mv_ch6","C",tamSx3("F1_DOC")[1]		,0,0,"G",""                    ,"mv_par06",""       ,""       ,""       ,"","",""         ,""         ,""         ,"","",""       ,""       ,""       ,"","","","","","","","","","","","SF1"	})
	AADD(aRegs,{cPerg,"07","Entrada de?"			,"Entrada de?"			,"Entrada de?"			,"mv_ch7","D",tamSx3("F1_DTDIGIT")[1]	,0,0,"G",""                    ,"mv_par07",""       ,""       ,""       ,"","",""         ,""         ,""         ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""		})
	AADD(aRegs,{cPerg,"08","Entrada ate?"			,"Entrada ate?"			,"Entrada ate?"			,"mv_ch8","D",tamSx3("F1_DTDIGIT")[1]	,0,0,"G",""                    ,"mv_par08",""       ,""       ,""       ,"","",""         ,""         ,""         ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""		})
	AADD(aRegs,{cPerg,"09","Projeto de?"			,"Projeto de?"			,"Projeto de?"			,"mv_ch9","C",tamSx3("PE5_PROJET")[1]	,0,0,"G",""                    ,"mv_par09",""       ,""       ,""       ,"","",""         ,""         ,""         ,"","",""       ,""       ,""       ,"","","","","","","","","","","","PE5"	})
	AADD(aRegs,{cPerg,"10","Projeto ate?"			,"Projeto ate?"			,"Projeto ate?"			,"mv_cha","C",tamSx3("PE5_PROJET")[1]	,0,0,"G",""                    ,"mv_par10",""       ,""       ,""       ,"","",""         ,""         ,""         ,"","",""       ,""       ,""       ,"","","","","","","","","","","","PE5"	})
	AADD(aRegs,{cPerg,"11","Unidade de Servico ?"	,"Unidade de Servico ?" ,"Unidade de Servico ?" ,"mv_chb","C",99						,0,0,"G","U_SV011C01('MV_PAR11')","mv_par11","","","","","","","","","","","","","","","","","","","","","","","","",""   })
	
	dbSelectArea("SX1")
	dbSetOrder(1)
	
	
aHelpPor	:= {}
aHelpEng	:= {}
aHelpSpa	:= {}

Aadd( aHelpPor,	"Help Port	"	)
Aadd( aHelpPor,	"Help Port								"	)

Aadd( aHelpSpa,	"Help Spa		"	)
Aadd( aHelpSpa,	"Help Spa		"	)

Aadd( aHelpEng,	"Help Eng		"	)
Aadd( aHelpEng,	"Help Eng"	)

    For i:= 1 to Len(aRegs)
			cGrupo       := aRegs[i][1]
			cOrdem		 := aRegs[i][2]
			cPergunt	 := aRegs[i][3]
			cPerSpa		 := aRegs[i][4]
			cPerEng		 := aRegs[i][5]
			cVar		 := aRegs[i][6]
			cTipo		 := aRegs[i][7]
			nTamanho     := aRegs[i][8]
			nDecimal	 := aRegs[i][9]
            nPresel		 := aRegs[i][10]
			cGSC		 := aRegs[i][11]
			cValid		 := aRegs[i][12]
			cF3			 := aRegs[i][38]
			cGrpSxg      := ''
		
			If Len(aRegs[i]) > 38 
				cPyme		 := aRegs[i][39]
			else
				cPyme		 := ""
			Endif 
				
			cHelp        := ''
			cVar01		 := aRegs[i][13]
			cDef01		 := aRegs[i][14]
			cDefSpa1	 := aRegs[i][15]
			cDefEng1	 := aRegs[i][16]
	        cCnt01       := aRegs[i][17]
			cDef02		 := aRegs[i][19]
			cDefSpa2	 := aRegs[i][20]
			cDefEng2	 := aRegs[i][21]
			cDef03		 := aRegs[i][24]
			cDefSpa3	 := aRegs[i][25]
			cDefEng3	 := aRegs[i][26]
			cDef04		 := aRegs[i][29]
			cDefSpa4	 := aRegs[i][30]
	        cDefEng4	 := aRegs[i][31]
			cDef05		 := aRegs[i][34]
			cDefSpa5	 := aRegs[i][35]
			cDefEng5	 := aRegs[i][36]
			

		U_FTTVSX001(cGrupo,cOrdem,cPergunt,cPerSpa,cPerEng,cVar,cTipo,nTamanho,nDecimal,;
                        nPresel,cGSC,cValid,cF3, cGrpSxg,cPyme,cVar01,cDef01,cDefSpa1,cDefEng1,;
	                    cCnt01,cDef02,cDefSpa2,cDefEng2,cDef03,cDefSpa3,cDefEng3,cDef04,cDefSpa4,;
	                    cDefEng4,cDef05,cDefSpa5,cDefEng5,aHelpPor,aHelpEng,aHelpSpa,cHelp)		
	Next
	
	dbSelectArea(_sAlias)

Return
