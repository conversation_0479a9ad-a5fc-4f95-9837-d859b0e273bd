#Include "Protheus.ch"
#Include "Topconn.ch"

/*/{Protheus.doc} F200FIM

   
	<AUTHOR>

	@since 28/10/2021

/*/

 
User function F200FIM()

Local aAreaSE5 := SE5->(GetArea())
Local cChaveSE5:= SE5->(E5_FILIAL+E5_PREFIXO+E5_NUMERO+E5_PARCELA+E5_TIPO+E5_CLIFOR+E5_LOJA+E5_SEQ)
Local lReceiv  := GetMV("TI_RCVBX01",,.F.)
Local aRetReceiv := {'',''} 

If lReceiv

    aRetReceiv := U_AcordPror(SE5->E5_FILORIG, SE5->E5_NUMERO, SE5->E5_PREFIXO, SE5->E5_PARCELA, SE5->E5_TIPO, iif(!empty(SE5->E5_CLIFOR),SE5->E5_CLIFOR,SE5->E5_CLIENTE )   )

    If Len(aRetReceiv) > 0 .And. !empty(aRetReceiv[1])

        SE5->(DbSetOrder(7))//E5_FILIAL+E5_PREFIXO+E5_NUMERO+E5_PARCELA+E5_TIPO+E5_CLIFOR+E5_LOJA+E5_SEQ                                                                                      
        If SE5->(DbSeek(cChaveSE5))
        
            While SE5->(!Eof()) .And. SE5->(E5_FILIAL+E5_PREFIXO+E5_NUMERO+E5_PARCELA+E5_TIPO+E5_CLIFOR+E5_LOJA+E5_SEQ) == cChaveSE5
                If Empty(SE5->E5_XIDACOR)
                    RecLock('SE5', .F.)
                    SE5->E5_XIDACOR := aRetReceiv[1]
                    SE5->E5_XPARCAC := aRetReceiv[2]
                    SE5->(MsUnLock())
                EndIF
                SE5->(DbSkip())

            EndDo

        EndIf

    EndIf
    
EndIF    

Return

