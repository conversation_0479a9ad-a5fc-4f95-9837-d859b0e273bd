#INCLUDE 'TOTVS.CH'
#INCLUDE "FWBROWSE.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "TOPCONN.CH"

#DEFINE CRLF		Chr(13)+Chr(10)

#DEFINE THREAD_IDLE		'TIINTEXEC - ##IDLE'
#DEFINE POS_USER 		01
#DEFINE POS_PC	 		02
#DEFINE POS_IDTHREAD	03
#DEFINE POS_INSTRUC		09
#DEFINE POS_OBS 		11

/*/{Protheus.doc} TIITC002
    Funcoes Responsavel por retornar os dados da P37  geral agrupado, dash de monitoramento
    @type  Function
    <AUTHOR> Menabue Lima
    @since 22/11/2022
    @version 1.0
    /*/
User Function TIITC003()
	Local aRequest  := PARAMIXB //Parametros recebidos do Interceptor  [1]= Codigo da Requisicao P37_COD [2]= Body Recebido da requisicao  P37_BODY
	Local cAlsPro := GetNextAlias()
	Local oJson     := JsonObject():new()
	Local oBody     := oJson:FromJson( aRequest[2] )

	Local cQry		:= ""


	cQry := " SELECT P37_CALLBA,COUNT(*) QTD,P37_METHOD,P37_STATUS,P37_CODAPI,MIN(P37_DATA) MINDT,MIN(P37_HORA) MINHR,MAX(P37_DATA) MAXDT,MAX(P37_HORA) MAXHR , P37_TIPORE"
	cQry += "  ,	P37_CODAPI||'-'|| P36_DESCRI  P37_DESCRI"
	cQry += " FROM "+ RetSqlName("P37") +" P37"
	cQry += " INNER JOIN "+ RetSqlName("P36") +" P36 ON P36_CODIGO = P37_CODAPI AND P36.D_E_L_E_T_ = ' '"
	cQry += " WHERE P37_STATUS IN ('0','1' )  "
	cQry += " AND P37.D_E_L_E_T_ = ' '"
	cQry += " GROUP BY P37_METHOD, P37_STATUS, P37_CALLBA , P37_CODAPI, P36_DESCRI, P37_TIPORE "
	cQry += " UNION ALL"
	cQry += " SELECT 'TOTAL' P37_CALLBA,COUNT(*) QTD,'*****' P37_METHOD,'*****' P37_STATUS,'********' P37_CODAPI,'******' MINDT,'******'MINHR"
	cQry += "  ,'******' MAXDT,'******' MAXHR, P37_TIPORE ,'TOTAL' P37_DESCRI"
	cQry += " FROM "+ RetSqlName("P37") +" P37"
	cQry += " INNER JOIN "+ RetSqlName("P36") +" P36 ON P36_CODIGO = P37_CODAPI AND P36.D_E_L_E_T_ = ' '"
	cQry += " WHERE P37_STATUS IN ('0','1' )  "
	cQry += " AND P37.D_E_L_E_T_ = ' ' GROUP BY P37_TIPORE"
	cQry += " ORDER BY QTD DESC"
	 
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAlsPro,.T.,.T.)


	nLinha := 0
	aJson:={}
	cJson:= '{ "requisicoes": [ '
	While (cAlsPro)->(!Eof())

		cJson +='{ 	"callback"     : "'+Alltrim((cAlsPro)->P37_CALLBA)+'",'
		cJson +='	"description"  : "'+Alltrim((cAlsPro)->P37_DESCRI)+'",'
		cJson +='	"quantity"     :  '+cValToChar((cAlsPro)->QTD)    +' ,'
		cJson +='	"method"       : "'+AllTrim((cAlsPro)->P37_METHOD)+'",' 
		cJson +='   "status"       : "'+AllTrim((cAlsPro)->P37_STATUS)+'",'
		cJson +='   "codeApi"      : "'+AllTrim((cAlsPro)->P37_CODAPI)+'",'
		cJson +='   "dateMin"      : "'+((cAlsPro)->MINDT)            +'",'
		cJson +='   "hourMin"      : "'+(cAlsPro)->MINHR			  +'",'
		cJson +='   "dateMax"      : "'+((cAlsPro)->MAXDT)            +'",'
		cJson +='   "hourMax"      : "'+(cAlsPro)->MAXHR			  +'",'
		cJson +='   "typeOfRequest": "'+AllTrim((cAlsPro)->P37_TIPORE)+'"'
		cJson +=  ' },'
		(cAlsPro)->(DbSkip())
	EndDO

	cJson:= SubStr(cJson,1,len(cJson)-1)+'] }'

	(cAlsPro)->(DbCloseArea())


Return cJson

