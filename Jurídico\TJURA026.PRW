#include "TOTVS.CH"
#include "fileio.ch"
#Include "Restful.Ch"
#include "FWMVCDEF.CH"
#Include "topconn.ch"
#Include "tbiconn.ch"
#include "fileio.ch"


 /*/{Protheus.doc} TJURA026
    (long_description)
    @type  Function
    <AUTHOR>
    @since date
    @version version
    @param param, param_type, param_descr
    @return return, return_type, return_description
    @example
    (examples)
    @see (links_or_references)
    /*/
User Function TJURA026(nRecesp)

Local aAuxA1    :=  {} 
Local cJAux     :=  ''
Local nCont     :=  1
Local nCont2    :=  1
Local nOrder    :=  1
Local cVirg     :=  ''
Local aCpoSa1   :=  {'A1_COD','A1_LOJA','A1_NOME','A1_PESSOA','A1_NREDUZ','A1_END','A1_BAIRRO','A1_TIPO','A1_EST','A1_COD_MUN','A1_MUN','A1_C<PERSON>','A1_INSCR','A1_PAIS','A1_BCO1','A1_BCO2','A1_BCO3','A1_COND','A1_MOEDALC','A1_MSALDO','A1_MCOMPRA','A1_PRICOM','A1_ULTCOM','A1_TMPVIS','A1_TMPSTD','A1_SALDUP','A1_VACUM','A1_SALPED','A1_MATR','A1_MAIDUPL','A1_INCISS','A1_SALDUPM','A1_TPISSRS','A1_CODPAIS','A1_SATIV6','A1_TIPCLI','A1_RECINSS','A1_RECCOFI','A1_RECCSLL','A1_RECPIS','A1_CODSEG','A1_CODSIAF','A1_CODMEMB','A1_TPMEMB','A1_RECIRRF'} //,'A1_MSBLQL'
Local aCposCn   :=  {{'A1_VEND','A1__EAR','SA3'},{'A1_XDAR','A1__DAR','SA3'},{'A1_XGAR','A1__GAR','SA3'},{'A1_UNIDVEN','A1__VEND','ADK'}}
Local aResult   :=  {}

Default nRecesp :=  0

If Empty(FunName())
    RpcSetType(3)
    RPCSetEnv("00","00001000100")
EndIf

aAuxX3 := FWSX3Util():GetAllFields( "SA1" , .F. )

If nRecesp == 0
    aAuxA1 := BuscaA1()
Else
    Aadd(aAuxA1,nRecesp)
EndIf

For nCont := 1 to len(aAuxA1)
    DbSelectArea("SA1")
    DbGoto(aAuxA1[nCont])
    
    cChave := space(2)+SA1->A1_COD+SA1->A1_LOJA

    cJAux := '{'+CRLF
    cJAux += '"id": "JURSA1",'+CRLF
    cJAux += '"operation": 1,'+CRLF
    cJAux += '"pk": "'+encode64(cChave)+'",'+CRLF
    cJAux += '"models": ['+CRLF
    cJAux += '    {'+CRLF
    cJAux += '        "id": "SA1MASTER",'+CRLF
    cJAux += '        "modeltype": "FIELDS",'+CRLF
    cJAux += '        "fields": ['+CRLF

    For nCont2 := 1 to len(aCpoSa1)
        aAux2 := FWSX3Util():GetFieldStruct( aCpoSa1[nCont2] )
        
        If aAux2[2] <> "M"
            cJAux += cVirg +iF(nCont2==1,CRLF,'')+ '        {'+CRLF
            cJAux += '              "id" : "'+aCpoSa1[nCont2]+'",'+CRLF
            cJAux += '              "order" : '+cvaltochar(nOrder)+','+CRLF

            cJAux += '              "value" : "'+If(aAux2[2]=="N",cvaltochar(&(aCpoSa1[nCont2])),Alltrim(&(aCpoSa1[nCont2])))+'"'+CRLF
            
            cJAux += '          }'+CRLF
            cVirg := ','
            nOrder++
        EndIf
        
    Next nCont2

    For nCont2 := 1 to len(aCposCn)
            cConteudo := &('SA1->'+aCposCn[nCont2,1])+'-'+Posicione(aCposCn[nCont2,3],1,xFilial(aCposCn[nCont2,3])+&('SA1->'+aCposCn[nCont2,1]),If(aCposCn[nCont2,3]=="SA3",SUBSTR(aCposCn[nCont2,3],2),aCposCn[nCont2,3])+"_NOME")
            cJAux += cVirg +iF(nCont2==1,CRLF,'')+ '        {'+CRLF
            cJAux += '              "id" : "'+aCposCn[nCont2,2]+'",'+CRLF
            cJAux += '              "order" : '+cvaltochar(nOrder)+','+CRLF

            cJAux += '              "value" : "'+cConteudo+'"'+CRLF
            
            cJAux += '          }'+CRLF
            cVirg := ','
            nOrder++

    Next nCont2

    cVirg := ''
   
    cJAux += ']'+CRLF
    cJAux += '}'+CRLF
    cJAux += ']'+CRLF
    cJAux += '}'+CRLF

    Aadd(aResult,APICustJur(cChave,cJAux,aAuxA1[nCont]))

Next nCont

If len(aResult) > 0
        
    For nCont := 1 to len(aResult)
        DbSelectArea('SA1')
        DbGoto(aResult[nCont,3])

        ProcLogIni( {},"TJURA026",cvaltochar(aResult[nCont,3]) )
    
        ProcLogAtu("INICIO","SA1 - Codigo: "+aResult[nCont,2] + If(aResult[nCont,1]," # Sucesso!!!"," # Falha!!!"))
        
        ProcLogAtu("MENSAGEM", aResult[nCont,4])

        ProcLogAtu("FIM")
        Reclock("PW8",.T.)
        PW8->PW8_FILIAL := xFilial("PW8")
        PW8->PW8_CODIGO := GetSXENum("PW8","PW8_CODIGO")
        PW8->PW8_CLIENT := SA1->A1_COD
        PW8->PW8_LOJA   := SA1->A1_LOJA
        PW8->PW8_DATA   := dDataBase
        PW8->PW8_HORA   := cvaltochar(time())
        PW8->PW8_JSON   := aResult[nCont,5]
        PW8->PW8_RETORN := aResult[nCont,4]
        PW8->PW8_STATUS := aResult[nCont,1]
        PW8->(Msunlock())
        
    Next nCont
    
    PUTMV("TI_TJUR026", dtos(DDATABASE)+strtran(time(),":"))
EndIf

Return


/*/{Protheus.doc} BuscaA1
description
    @type  Static    <AUTHOR>
    @since 17/01/2022
    @version version
    @param param_name, param_type, param_descr
    @return return_var, return_type, return_description
    @example
    (examples)
    @see (links_or_references)
/*/
Static Function BuscaA1()

Local aArea     :=  GetArea()
Local aRet      :=  {}
Local cQuery 
Local dDtUlEx   :=  Alltrim(SuperGetmv('TI_TJUR026',.F.,"20220501000000"))


cQuery := "SELECT R_E_C_N_O_ AS REG "
cQuery += " FROM "+RetSQLName("SA1")
cQuery += " WHERE D_E_L_E_T_=' '"
cQuery += " AND COALESCE(to_char(S_T_A_M_P_,'YYYYMMDDHH24MISS'),'19810101000000' )  >= '"+dDtUlEx+"'"
  
If Select('TRB') > 0
    dbSelectArea('TRB')
    dbCloseArea()
EndIf

DBUseArea( .T., "TOPCONN", TCGenQry( ,, cQuery ), "TRB", .F., .T. )

DbSelectArea("TRB")

While !EOF()
    Aadd(aRet,TRB->REG)
    Dbskip()
EndDo

RestArea(aArea)

Return(aRet)

/*/{Protheus.doc} APICustJur
    (long_description)
    @type  Static Function
    <AUTHOR>
    @since 17/01/2022
    @version version
    @param param_name, param_type, param_descr
    @return return_var, return_type, return_description
    @example
    (examples)
    @see (links_or_references)
/*/
Static Function APICustJur(cChave,cJAux,nRecA1)

Local oRest 
Local oJson     :=  ""
Local aHeader   :=  {}
Local cRetorno  :=  ""
Local lRet      :=  .T.
Local aRet      :=  {}

//http://app.totvsjuridico.totvs.com.br:10022/fwmodel/jura148/
//username titotvs
//pass T1T0tv2@202w
Local cUrlInt	:=	Alltrim(SuperGetmv('TI_APIJ026',.F.,"http://app.totvsjuridico.totvs.com.br:10022")) //http://app.totvsjuridico.totvs.com.br:10022

Local cPath     :=  Alltrim(SuperGetmv('TI_ENDJ026',.F.,"/fwmodel/JURSA1/")) ///fwmodel/jura148/

Local cUsrJ     :=  Alltrim(SuperGetmv('TI_USRJ026',.F.,"titotvs"))

Local cPasJ     :=  Alltrim(SuperGetmv('TI_PASJ026',.F.,"T1T0tv2@202w"))



//Get
oRest := FWRest():New(cUrlInt)

oRest:SetPath(cPath+Encode64(cChave))

//aadd(aHeader,'Content-Type : application/json')
aadd(aHeader,'Authorization: Basic ' + Encode64(cUsrJ+":"+cPasJ))

oRest:SetPostParams(cJAux)

If oRest:Get(aHeader)
    oJson := JsonObject():New()
    ret := oJson:FromJson(oRest:GetResult())
    /*names := oJson:GetNames()*/
    cRetorno := decode64(oJson:GetJsonText("pk"))
    lRet := .T.
else
    cRetorno := Alltrim(oRest:GetLastError()) 
    cRet := Alltrim(oRest:cresult)
    lRet := .F.
EndIF


//put
oRest := FWRest():New(cUrlInt)

oRest:SetPath(cPath+Encode64(cChave))

cjaux := strtran(cjaux,'"operation": 1','"operation": 4')

If oRest:Put(aHeader, cJAux) //oRest:Get(aHeader) //
    oJson := JsonObject():New()
    ret := oJson:FromJson(oRest:GetResult())
    /*names := oJson:GetNames()*/
    cRetorno := decode64(oJson:GetJsonText("pk"))
    oBody := Nil
    lRet  := .T.
else
    cRetorno := Alltrim(oRest:GetLastError()) 
    cRet := Alltrim(oRest:cresult)
    oBody  := JsonObject():New()
	oBody:fromJson(cRet)
    lRet := .F.
EndIF

If oBody <> NIL
    oRest := FWRest():New(cUrlInt)

    oRest:SetPath(cPath)
    cjaux := strtran(cjaux,'"operation": 4','"operation": 3')
    
    cjaux := strtran(cjaux,'"pk": "'+Encode64(cChave)+'"','"pk": ""')

    oRest:SetPostParams(cJAux)

    If oRest:Post(aHeader)
        oJson := JsonObject():New()
        ret := oJson:FromJson(oRest:GetResult())
        /*names := oJson:GetNames()*/
        cRetorno := decode64(oJson:GetJsonText("pk"))
        oBody := Nil
        lRet := .T.
    else
        cRetorno := Alltrim(oRest:GetLastError()) 
        cRet := Alltrim(oRest:cresult)
        oBody  := JsonObject():New()
        oBody:fromJson(cRet)
        lRet := .F.
    Endif
EndIf

Aadd(aRet,lRet)
Aadd(aRet,cChave)
Aadd(aRet,nRecA1)
Aadd(aRet,cRetorno)
Aadd(aRet,cJAux)

Return(aRet)
