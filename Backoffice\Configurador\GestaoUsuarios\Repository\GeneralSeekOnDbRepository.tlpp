#include    "tlpp-core.th"

namespace   Backoffice.Configurador.Gestao_Usuarios.Repository.GeneralSeekOnDbRepository

using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.ConstructEnvironmentUtils

/*/{Protheus.doc} GeneralSeekOnDb
This Class aims to realize a dbseek on table
and get the values of field.
@type class
@version  1.0
<AUTHOR>
@since 11/29/2024
/*/
Class   GeneralSeekOnDb
    Private Data    cTable  As  Character
    Private Data    nOrder  As  Numeric
    Private Data    aArea   As  Array

    Public  Method  new()   As  Object
    Public  Method  setTable(cTableChoose   As  Character)  As  Object
    Public  Method  setNumberOrder(nOrderChoose   As  Numeric)    As  Object
    Public  Method  setOrderOnDb()  As  Object
    Public  Method  seek(cKey as  Character)  As  Logical
    Public  Method  getField(cField As  Character)  As  Variant
    Public  Method  getArea()   As  Object
    Public  Method  restArea()  As  Object
EndClass

/*/{Protheus.doc} GeneralSeekOnDb::new
This method aims to create the object
@type method
@version  1.0
<AUTHOR>
@since 11/29/2024
@return object, Return the object itself
/*/
Method  new()   As  Object  Class   GeneralSeekOnDb
    ::cTable  :=  ''
    ::nOrder  :=  1
Return  Self

/*/{Protheus.doc} GeneralSeekOnDb::setOrderOnDb
This aims to execute the comando dbsetorder
@type method
@version  1.0
<AUTHOR>
@since 11/29/2024
@return object, Return the object itself
/*/
Method  setOrderOnDb()  As  Object  Class   GeneralSeekOnDb
    Local   cNumOrder   :=  ''  As  Character

    cNumOrder   :=  AllTrim(Str(::nOrder))
    &(::cTable+'->(dbSetOrder('+cNumOrder+'))')

Return  Self


/*/{Protheus.doc} GeneralSeekOnDb::setTable
This method aims to set the table
@type method
@version  1.0
<AUTHOR>
@since 11/29/2024
@param cTableChoose, character, The table that will be manipulated
@return object, The object itself
/*/
Method  setTable(cTableChoose   As  Character)  As  Object  Class   GeneralSeekOnDb
    ::cTable    :=  cTableChoose
Return  Self


/*/{Protheus.doc} GeneralSeekOnDb::setNumberOrder
This method aims to set the order number from table on the object
@type method
@version  1.0
<AUTHOR>
@since 11/29/2024
@param nOrderChoose, character, The number of the Order on Table
@return object, The object itsef
/*/
Method  setNumberOrder(nOrderChoose    As  Numeric)  As  Object  Class   GeneralSeekOnDb
    ::nOrder  :=  nOrderChoose
Return  Self


/*/{Protheus.doc} GeneralSeekOnDb::seek
This method aims to execute a seek on the table
@type method
@version  1.0
<AUTHOR>
@since 11/29/2024
@param cKey, character, The key values to execute the seek, ex: A1_COD+A1_NOME
@return logical, return a logical value if the seek adquires sucess
/*/
Method  seek(cKey as  Character)  As  Logical   Class   GeneralSeekOnDb
    Local   lSeek   :=  .F. As  Logical
    If &(::cTable+'->(dbSeek("'+cKey+'"))')
        lSeek   :=  .T.
    Else
        lSeek   :=  .F.
    EndIf
Return  lSeek

/*/{Protheus.doc} GeneralSeekOnDb::getField
This method aims to get the value of a specified field
@type method
@version  1.0
<AUTHOR>
@since 11/29/2024
@param cField, character, the name of the field, ex: A1_NOME
@return variant, The value of the specified field.
/*/
Method  getField(cField As  Character)  As  Variant Class   GeneralSeekOnDb
    Local   cSearchField    :=  ''  As  Variant

    cSearchField    :=  &(::cTable+'->'+cField)
Return  cSearchField


/*/{Protheus.doc} GeneralSeekOnDb::getArea
This method aims to get a new area on Database
@type method
@version  1.0
<AUTHOR>
@since 12/11/2024
@return object, The object itself
/*/
Method  getArea()   As  Object  Class   GeneralSeekOnDb
    ::aArea  :=  &(::cTable + '->(GetArea())')
Return  Self

/*/{Protheus.doc} GeneralSeekOnDb::restArea
This method aims to execute the process of rest area on Database
@type method
@version  1.0
<AUTHOR>
@since 12/11/2024
@return object, The object itself
/*/
Method  restArea()  As  Object  Class   GeneralSeekOnDb
    RestArea(::aArea)
Return  Self


/*/{Protheus.doc} GeneralSeekOnDbRepositoryExample
This function aims to explain how use the class
@type function
@version  1.0
<AUTHOR>
@since 12/11/2024
@return variant, Return a Nil Value
/*/
User Function GeneralSeekOnDbRepositoryExample()
    Local oTable
    Local cFieldValue   :=  ''  As  Character

    ConstructEnvironment():initEnvironment()
    
    oTable := GeneralSeekOnDb():new()
    
    oTable:setTable('SA1')
    oTable:getArea()
    oTable:setNumberOrder(1)
    oTable:setOrderOnDb()
    If(oTable:seek(xFilial("SA1")+'000001'+'01'))
        cFieldValue   :=  oTable:getField('A1_NOME')
    Else
        cFieldValue :=  'Erro ao realizar o seek'
    EndIf
    oTable:restArea()
Return Nil

