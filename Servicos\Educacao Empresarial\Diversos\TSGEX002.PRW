#include "totvs.ch"

/*/{Protheus.doc} TSGEX002
@description Busca a URL por ambiente logado
<AUTHOR>
@since 28/09/2022
@version 1.0
@param nTpURI, numerico, tipo de URL
@return cUrl
/*/

User Function TSGEX002()
    Local cUrl			:= ""
    Local cTbPath		:= ""
    Local cEnvOficiais	:= GetMV("TI_AMBOFIC",,"#TOTVS12|#TOTVS12_ES|#TOTVS12_EN|CRM|CRM_MI")			//Ambientes Oficiais
    Local cKEY          := ""
    Local aRet          := {}
    cTbPath		:= "APPGEW"

    DbSelectArea("ZX5")
    ZX5->( DbSetOrder(1) )

    If Alltrim( Upper(GetEnvServer()) ) $ cEnvOficiais
        cTbPath += "PRD001"
    Elseif "_PRE" $ Upper(GetEnvServer()) .OR. "PRE_" $ Upper(GetEnvServer())
        cTbPath += "PRE001"
    Else
        cTbPath += "DEV001"
    EndIf

    If ZX5->( DbSeek(xFilial("ZX5")+cTbPath) )
        cUrl := Alltrim( ZX5->ZX5_DESCRI )
        cKEY := Alltrim(ZX5->ZX5_COMPL)
    EndIf

    aadd(aRet,{cUrl,cKEY})

Return aRet
