/*/
{PROJETO} - TFIN020.PRW
@desc:		<PERSON> - <PERSON>.   
@author:	<PERSON><PERSON>
@version: 	1.00
@since: 	22/12/2015
/*/

#include "protheus.ch"

User Function TFINX002()

Local cQry			:= ""
Local nSeq			:= 1
Local cLote			:= ""
Local cChave		:= ""
Local cSequencia	:= ""
Local cTitulo		:= ""
Local nCount		:= 1
Local nAbatim		:= 0
Local cTitulo2		:= ""
Local cPrefixo		:= ""
Local cPrefixo2		:= ""
Local aAbatim		:= {}
Local nX
Local nPos			:= 0
Local aDePara		:= {}
Local cStatus		:= ""
Local cTipo			:= ""
Local cDescFWB		:= ""
Local dDataSE		:= dDataBase
Local cCodErro		:= ""
Local cEmpresa		:= ""
Local cFilGuard		:= ""
Local cFilialFW8	:= ""


	aADD(aDePara, {"3", "6"})
	aADD(aDePara, {"4", "0"})
	aADD(aDePara, {"5", "3"})
	


	cQry := " SELECT E1_CLIENTE, E1_LOJA, E1_NOMCLI, E1_PREFIXO, E1_NUM, E1_PARCELA, E1_TIPO, E1_SALDO, E1_FILORIG, E1_FILIAL, E1_VALOR, E1_MOEDA, E1_XSTASER, E1_XERRSER, E1_XDTMVSE "
	cQry += " FROM " + RetSqlName("SE1") + " SE1 "
	cQry += " WHERE SE1.D_E_L_E_T_ <> '*' AND E1_XSTASER IN ('3','4','5') "
	cQry += " ORDER BY E1_FILIAL, E1_XSTASER "

	If Select("SERASA") > 0; ("SERASA")->(dbCloseArea()); Endif  
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),"SERASA",.T.,.T.)

	DbSelectArea("SERASA")
	
	If !Empty(SERASA->E1_CLIENTE)
	
		cFilGuard	:= xFilial("FW8")
		cFilAnt		:= SERASA->E1_FILIAL
		cFilialFW8	:= cFilAnt
	
		cLote	:= GetSx8Num("FW8","FW8_LOTE")
	
		FW8->(RecLock("FW8",.T.))
		
			FW8->FW8_FILIAL		:= Substr(cFilialFW8,1,3)
			FW8->FW8_LOTE		:= cLote
			FW8->FW8_DTLOTE		:= dDataBase
			FW8->FW8_DTARQ		:= dDataBase
			FW8->FW8_DTPROC		:= dDataBase
			FW8->FW8_TIPO		:= IIf(Alltrim(SERASA->E1_XSTASER) == "5" .Or. Alltrim(SERASA->E1_XSTASER) == "3", "1", "2") 
			
		FW8->(MsUnlock())
			
		ConfirmSx8()
			
	EndIf
	
	SERASA->(DbGoTop())
	
	While SERASA->(!EOF())
	
		nAbatim := 0
		
		
		DbSelectArea("SE1")
		
		nAbatim := SomaAbat(SERASA->E1_PREFIXO, SERASA->E1_NUM, SERASA->E1_PARCELA, "R", SERASA->E1_MOEDA, dDataBase, SERASA->E1_CLIENTE, SERASA->E1_LOJA, SERASA->E1_FILIAL)
		
		SE1->(DbCloseArea())	
		
		cSequencia := StrZero(nSeq,3,0)
		
		cChave	:= SERASA->E1_FILIAL+"|"+SERASA->E1_PREFIXO+"|"+SERASA->E1_NUM+"|"+SERASA->E1_PARCELA+"|"+SERASA->E1_TIPO+"|"+SERASA->E1_CLIENTE+"|"+SERASA->E1_LOJA
		
		DbSelectArea("FK7")
		DbSetOrder(2)
		
		FK7->(DbSeek(xFilial("FK7")+ "SE1" + AvKey(cChave,"FK7_CHAVE")))
		
		If Alltrim(FK7->FK7_CHAVE) == Alltrim(cChave)
			cIdDoc	:= FK7->FK7_IDDOC
		Else
			cIdDoc	:= FINGRVFK7("SE1",cChave, SERASA->E1_FILORIG)
		EndIf
		
		FK7->(DbCloseArea())
		
		If nCount > 2000 .Or. (!Empty(cTipo) .And. Alltrim(cTipo) <> Alltrim(SERASA->E1_XSTASER)) .Or. (!Empty(cEmpresa) .And. Substr(cFilialFW8,1,3) <> Substr(SERASA->E1_FILIAL,1,3))
			
			cFilGuard	:= xFilial("FW8")
			cFilAnt		:= SERASA->E1_FILIAL
			cFilialFW8	:= cFilAnt
			
			cLote	:= GetSx8Num("FW8","FW8_LOTE")	
			
			FW8->(RecLock("FW8",.T.))
		
				FW8->FW8_FILIAL		:= Substr(cFilialFW8,1,3)
				FW8->FW8_LOTE		:= cLote	
				FW8->FW8_DTLOTE		:= dDataBase
				FW8->FW8_DTARQ		:= dDataBase
				FW8->FW8_DTPROC		:= dDataBase
				FW8->FW8_TIPO		:= IIf(Alltrim(SERASA->E1_XSTASER) == "5" .Or. Alltrim(SERASA->E1_XSTASER) == "3", "1", "2")
			
			FW8->(MsUnlock())
			
			ConfirmSx8()
		
			nCount := 1
		
		EndIf
	
		FW9->(RecLock("FW9",.T.))
		
		FW9->FW9_FILIAL		:= Substr(cFilialFW8,1,3)
		FW9->FW9_PREFIX		:= SERASA->E1_PREFIXO
		FW9->FW9_NUM		:= SERASA->E1_NUM
		FW9->FW9_PARCEL		:= SERASA->E1_PARCELA
		FW9->FW9_TIPO		:= SERASA->E1_TIPO
		FW9->FW9_CLIENT		:= SERASA->E1_CLIENTE
		FW9->FW9_LOJA		:= SERASA->E1_LOJA
		FW9->FW9_VALOR		:= IIf(SERASA->E1_TIPO $ MVABATIM, SERASA->E1_VALOR, SERASA->E1_VALOR - nAbatim)
		FW9->FW9_FILORIG	:= SERASA->E1_FILORIG
		FW9->FW9_LOTE		:= cLote
		FW9->FW9_IDDOC		:= cIdDoc	
		FW9->FW9_OBS		:= "MIGRACAO SERASA"	
		
		FW9->(MsUnlock())
		
		nPos	:= aScan(aDePara,{|x| Alltrim(x[1]) == Alltrim(SERASA->E1_XSTASER)})	
		cStatus	:= aDepara[nPos][2]
		
		cMsg		:= "Migracao Serasa lote: " + cLote + " iddoc: " + cIdDoc
		
		cChaveCV8	:= cFilialFW8 + cLote
		
		ProcLogIni({},cIdDoc,,@cChaveCV8)
		ProcLogAtu("Mensagem: ","Mensagem: Processamento do Arquivo", cMsg,,.T.)
		
		FWA->(RecLock("FWA",.T.))
		
			FWA->FWA_FILIAL		:= Substr(cFilialFW8,1,3)
			FWA->FWA_IDDOC		:= cIdDoc
			FWA->FWA_PREFIX		:= SERASA->E1_PREFIXO
			FWA->FWA_NUM		:= SERASA->E1_NUM	
			FWA->FWA_PARCEL		:= SERASA->E1_PARCELA
			FWA->FWA_TIPO		:= SERASA->E1_TIPO
			FWA->FWA_CLIENT		:= SERASA->E1_CLIENTE
			FWA->FWA_LOJA		:= SERASA->E1_LOJA
			FWA->FWA_FILORIG	:= SERASA->E1_FILORIG
			FWA->FWA_SEQ		:= cSequencia
			FWA->FWA_STATUS		:= cStatus
			FWA->FWA_LOTE		:= cLote 
		
		FWA->(MsUnlock())
		
		cCodErro := ""
		
		If cStatus == "3"
			cDescFWB	:= "Reg. Incluido"
			dDataSE		:= IIf(SERASA->E1_XDTMVSE <> "", STOD(SERASA->E1_XDTMVSE), dDataBase)
			ElseIf cStatus == "0"
				cDescFWB	:= "Reg. Excluido"
				dDataSE		:= IIf(SERASA->E1_XDTMVSE <> "", STOD(SERASA->E1_XDTMVSE), dDataBase)
			ElseIf cStatus == "6"
				cDescFWB	:= "Erro de Process."
				dDataSE		:= IIf(SERASA->E1_XDTMVSE <> "", STOD(SERASA->E1_XDTMVSE), dDataBase)
				cCodErro	:= SERASA->E1_XERRSER
		EndIf
		
		FWB->(RecLock("FWB",.T.))
		
			FWB->FWB_FILIAL		:= Substr(cFilialFW8,1,3)
			FWB->FWB_IDDOC		:= cIdDoc
			FWB->FWB_SEQ		:= cSequencia
			FWB->FWB_LOTE		:= cLote
			FWB->FWB_VALOR		:= IIf(SERASA->E1_TIPO $ MVABATIM, SERASA->E1_VALOR, SERASA->E1_VALOR - nAbatim)
			FWB->FWB_OCORR		:= cStatus	
			FWB->FWB_DESCR		:= cDescFWB
			FWB->FWB_DTSERA		:= dDataSE
			FWB->FWB_CODERR		:= cCodErro
			FWB->FWB_DTOCORR	:= IIf(cStatus == "6", IIF(SERASA->E1_XDTMVSE <> "", STOD(SERASA->E1_XDTMVSE), dDataBase), )
						
		FWB->(MsUnlock())
		
		cTitulo 	:= SERASA->E1_NUM
		cPrefixo	:= SERASA->E1_PREFIXO
		cTipo		:= SERASA->E1_XSTASER
		cEmpresa	:= SERASA->E1_FILIAL
		
		nCount ++
		
		SERASA->(DbSkip())
		
	End
	
cFilAnt := cFilGuard

Alert("Acabou: " + Time())		
	
Return	
