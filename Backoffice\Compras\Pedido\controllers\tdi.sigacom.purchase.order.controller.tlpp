#include "tlpp-core.th"
#include "tlpp-rest.th"
#Include "TOTVS.CH"
#Include "Protheus.ch"
#INCLUDE "TRYEXCEPTION.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOPCONN.CH"
namespace tdi.sigacom.purchase.order.controller
using namespace tdi.sigacom.purchase.order.service

/*/{Protheus.doc} TdiCOMpurchaseOrder()
    Classe responsavel por receber as requisicoes de Pedidos de Compras
    @type Class
    <AUTHOR> Menabue Lima
    @since 06/11/2024
    @version 1.0
/*/
Class TdiCOMPurchaseOrderControllers from LongNameClass

	public data oServices
	public data  jBody               as object
	public data  jParams             as object
	public data  jPathParams         as object
	public data  cPage               as Character
	public data  cPageSize           as Character
	public data  cFilter             as Character

	public method New()

	//Apis Relacionadas  pedido de compras
	@Get("/api/tdi/com/v1/purchaseorder/orders")
	public method orders()
	@Get("/api/tdi/com/v1/purchaseorder/order/:order")
	public method order()
	@Get("/api/tdi/com/v1/purchaseorder/last/orders")
	public method lastOrders()
	@Get("/api/tdi/com/v1/purchaseorder/last/:order")
	public method lastOrder()
 
EndClass

/*/{Protheus.doc} New()
    Metodo responsavel por instanciar a classe e iniciar variaveis
    @type Method
    <AUTHOR> Menabue Lima
    @since 06/11/2024
    @version 1.0
/*/
Method New() class TdiCOMpurchaseOrderControllers
	::oServices :=  tdi.sigacom.purchase.order.service.TdiCOMpurchaseOrderServices():new()
	::jParams   := JsonObject():New()
	::jPathParams:= JsonObject():New()
Return Self

/*/{Protheus.doc} orders()
    Recebe os dados da requisicao de GET para Efetua a consulta de dados de todos  pedidos de compra
    @type  Method
    <AUTHOR> Menabue Lima
    @since 06/11/2024
    @version 1.0
    //Exemplo POSTMAN - GET:
    http://172.24.35.104:7038/app-root/api/tdi/gct/v1/cancellationapproval/items?page=1&pageSize=10&zrm_status=1&filter=codigo eq '0001' and (contains(filial, 'D MG 01') or contains(filial, 'M PR 02 '))
/*/
Method orders() Class TdiCOMpurchaseOrderControllers
	Local jBodyResponse as Object
	Local nPage         as Numeric
	Local nPageSize     as Numeric
	oRest:setKeyHeaderResponse("Content-Type", "application/json")
	::jParams        := oRest:getQueryRequest()
	jBodyResponse    := JsonObject():New()

	::cPage             := ::jParams['page']
	::cPageSize         := ::jParams['pageSize']


	if empty(::cPage)
		nPage := 1
	else
		nPage := VAL(::cPage)
	endif
	if empty(::cPageSize)
		nPageSize := 10
	else
		nPageSize := Val(::cPageSize)
	endif

	oRest:setResponse( ::oServices:GetOrders(nPage, nPageSize))
Return
/*/{Protheus.doc} order()
    Recebe os dados da requisicao de GET para Efetua a consulta do  pedido selecionado
    @type  Method
    <AUTHOR> Menabue Lima
    @since 06/11/2024
    @version 1.0
    //Exemplo POSTMAN - GET:
    http://localhost:1243/restapi/tdi/contratos/v1/cronograma?page=1&pageSize=10&filter=codigo eq '0001' and (contains(filial, 'D MG 01') or contains(filial, 'M PR 02 '))
/*/
Method order() Class TdiCOMpurchaseOrderControllers
	Local jBodyResponse as Object
	Local nPage         as Numeric
	Local nPageSize     as Numeric
	Local cOrder  		as character


	oRest:setKeyHeaderResponse("Content-Type", "application/json")
	::jPathParams := oRest:getPathParamsRequest()
	::jParams     := oRest:getQueryRequest()
	jBodyResponse := JsonObject():New()
	cOrder 		  := ::jPathParams['order']
	::cPage       := ::jParams['page']
	::cPageSize   := ::jParams['pageSize']

	if empty(::cPage)
		nPage := 1
	else
		nPage := VAL(::cPage)
	endif
	if empty(::cPageSize)
		nPageSize := 10
	else
		nPageSize := Val(::cPageSize)
	endif
	cOrder:=::oServices:getParamValue(cOrder)
	oRest:setResponse( ::oServices:GetOrders(nPage, nPageSize,,cOrder))
Return
 
/*/{Protheus.doc} LastOrdera()
    Recebe os dados da requisicao de GET para Efetua a consulta dos ultimos pedido  conforme parametros selecionados
    @type  Method
    <AUTHOR> Menabue Lima
    @since 06/11/2024
    @version 1.0
    //Exemplo POSTMAN - GET:
    http://localhost:1243/restapi/tdi/contratos/v1/cronograma?page=1&pageSize=10&filter=codigo eq '0001' and (contains(filial, 'D MG 01') or contains(filial, 'M PR 02 '))
/*/
Method LastOrders() Class TdiCOMpurchaseOrderControllers
	Local jBodyResponse as Object
	Local nPage         as Numeric
	Local nPageSize     as Numeric
	Local cOrder  		as character
	Local cPedNumApv    as character

	oRest:setKeyHeaderResponse("Content-Type", "application/json")
	::jPathParams := oRest:getPathParamsRequest()
	::jParams     := oRest:getQueryRequest()
	jBodyResponse := JsonObject():New()
	cForn 		  := ::jParams['supplier']
	cLoja 		  := ::jParams['shop']
	cFil		  := ::jParams['branch']
	::cPage       := ::jParams['page']
	::cPageSize   := ::jParams['pageSize']
	cPedNumApv    := ::jParams['pednumapv']

	if empty(::cPage)
		nPage := 1
	else
		nPage := VAL(::cPage)
	endif
	if empty(::cPageSize)
		nPageSize := 10
	else
		nPageSize := Val(::cPageSize)
	endif
	cOrder:=::oServices:getParamValue(cOrder)
	oRest:setResponse( ::oServices:GetLastOrders(nPage, nPageSize,cFil,cForn,cLoja,cPedNumApv))
Return
