#include "Protheus.ch"
/*/{Protheus.doc} 
<AUTHOR>
@version 1.0
/*/
CLASS JsonUtil
	
	DATA oMap
	
	METHOD New() CONSTRUCTOR
	
	METHOD PutVal()
	METHOD PutArray()
	
	METHOD GetCharac()
	METHOD GetNumber()
	METHOD GetBool()
	METHOD GetObjJson()
	METHOD GetArray()

	METHOD GetUndef()	
	METHOD ToJson()	
	METHOD ToJsonArray()	
	METHOD Parse()
ENDCLASS

METHOD New() CLASS JsonUtil
	::oMAP := Map():New()
RETURN SELF

METHOD PutVal(cKEY,uVAL) CLASS JsonUtil
	Local oEntry
	
	oEntry := Entry():New(cKEY, iif(valtype(uVAL) == 'U', 'null',uVAL) )
	::oMAP:PutEntry(oEntry)
	
RETURN
METHOD PutArray(uVAL) CLASS JsonUtil
	Local oEntry
	
	oEntry := Entry():New(uVAL)
	::oMAP:PutEntry(oEntry)
	
RETURN

METHOD GetUndef(cKEy) CLASS JsonUtil
	Local oEntry 
	Local uRet := nil
	oEntry := ::oMAP:GetEntry(cKEy)
	
	if ValType(oEntry) == "O"
		uRet := oEntry:GetValue()
	EndIf
	
RETURN uRet

METHOD GetCharac(cKEy) CLASS JsonUtil
	local cRet :=""
	local uVal
	If ::oMap:HasKey(cKEy)
		uVal := ::GetUndef(cKEy)
		if(ValType(uVal)=="C")
			cRet := uVal
		ElseIf (ValType(uVal)=="N")
			cRet := AllTrim(str(uVal))
		ElseIf (ValType(uVal)=="D")
			cRet := dtos(uVal)
		EndIf				
	EndIf
RETURN cRet

METHOD GetNumber(cKEy) CLASS JsonUtil
	Local nRet := 0 
	local uVal
	Local var := 0
	If ::oMap:HasKey(cKEy)
		uVal := ::GetUndef(cKEy)
		if(ValType(uVal)=="C")
			nRet := Val(uVal)
		ElseIf (ValType(uVal)=="N")
			cRet := uVal
		EndIf
	EndIf
RETURN nRet

METHOD GetBool(cKEy) CLASS JsonUtil
	Local lRet := .F. 
	local uVal
	If ::oMap:HasKey(cKEy)
		uVal := ::GetUndef(cKEy)
		if(ValType(uVal)=="C")
			if(upper(uVal)== 'FALSE' .OR. upper(uVal)== '.F.' )
				lRet := .F.
			ElseIf (upper(uVal)== 'TRUE' .OR. upper(uVal)== '.T.' )
				lRet := .F.
			EndIf
		ElseIf (ValType(uVal)=="L")
			lRet := uVal
		EndIF
	EndIf
RETURN lRet  

METHOD GetObjJson(cKEy) CLASS JsonUtil
	Local oRet := JsonUtil():New()
	local uVal
	If ::oMap:HasKey(cKEy)
		uVal := ::GetUndef(cKEy)
		if(ValType(uVal)=="O")
			oRet:oMap := uVal:oMap 
		EndIf
	EndIf
RETURN oRet	

METHOD GetArray(cKEy) CLASS JsonUtil
	Local aRet := {}
	local uVal
	If ::oMap:HasKey(cKEy)
		uVal := ::GetUndef(cKEy)
		if(ValType(uVal)=="A")
			aRet := uVal   
		EndIf
	EndIf
RETURN aRet

METHOD ToJson() CLASS JsonUtil
	Local cJson := ""
	cJson += ::oMap:ToJson()	
RETURN cJson

METHOD ToJsonArray() CLASS JsonUtil
	Local cJson := ""
	cJson += ::oMap:ToJsonArray()	
RETURN cJson

METHOD Parse(cJson) CLASS JsonUtil
	Local teste :=""
	Local oParse := JsonParse():New(cJson)
	Local tmpObjson := oParse:ParseToObj()
	
	::oMap := tmpObjson:oMap
RETURN