#INCLUDE 'TOTVS.CH'


/*/{Protheus.doc} ESPG020

	Description

	<AUTHOR>
	@example Example
	@param   [Parameter_Name],Parameter_type,Parameter_Description
	@return  cRetorno
	@table   Tables
	@since   25-08-2020
/*/
User Function ESPG020()	
	Local cRetorno  := ''
	Local cTipo     := U_ESPG080()
	
	/*
	valores esperados:
	1=Campo;2=Parametro;3=Arquivo;4=Tabela Generica;5=Tabela;6=Indice;7=Gatilho
	*/
	IF cTipo $ '1|2|3'
		cAlias	:= 'PZ' + cTipo		
	ELSE
		IF cTipo == '4'
			cTipo := 'G'
		ELSEIF cTipo == '5'
			cTipo := '4'
		ELSEIF cTipo == '6'
			cTipo 	:= 'I'
		ELSEIF cTipo == '7'
			cTipo	:= 'K'
		EndIF				
		cAlias	:= 'PJ' + cTipo
	EndIF		
	
	cAlias := U_AjustarTab(cAlias)
	cRetorno := (cAlias)->&(cAlias + '_CODACE') 
	
Return(cRetorno)
