#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVF006     
Callback de VIEW APONTAMENTOS  PSA ACTIO PRODUTIVIDADE
@type  Function
<AUTHOR> Menabue Lima
@since 27/10/2023
@version 1.0
	/*/
User Function TIRVF006(cCodReq,cResponse,cAsync,cStatReq)

	Local oReceptor	:=TIINTERCEPTOR():New()
	Local oJson 	:= JsonObject():new()
	Local oActio	:= TIRVACTIO():New()
	Local nCount	:=0
	Local nPage		:=0
	Local nItens	:=0
	Local nMaxPg 	:= GetMv("TI_RVJ06PG" , .F., 50)
	Local lHasNext  := .F.
	Local aArea     := P37->(GetArea())
	Local nI		:= 0
	If (cStatReq == "2")//Processado a REquisicao
		oJson:fromJson(cResponse)
		Reclock("P37",.F.)

		P37->P37_STATUS :="F"
		P37->(MsUnlock())

		if oJson:hasProperty('value')
			aItens:=oJson['value']
			nItens:= Len(aItens)
			oJson:fromJson(P37->P37_BODY)
			//Verifica ser tem dados na proxima pagina
			IF(oJson:hasProperty('nCount'))
				nCount:=oJson['nCount']
				if (oJson:hasProperty('nPage'))
					nPage:=oJson['nPage']
					lHasNext:=(nItens>=nCount  .And. oJson['nPage'] <= nMaxPg)
				EndIF
			EndIF

			if lHasNext .And. nPage == 1 
				For nI:= 1 To nMaxPg
					U_TIRVJ006(nCount,nPage+nI,IIF(Empty(Alltrim(P37->P37_CODREQ)),P37->P37_COD,P37->P37_CODREQ),oJson['inicio'],oJson['fim'])
				Next nI
			EndIF
			P37->(RestArea(aArea))
			//Grava o dado na tabela  de destino
			oActio:setRVPsaData(aItens,"PRODUTIVIDADE")
			if !oActio:isError()
				Reclock("P37",.F.)
				P37->P37_STATUS :="2"
				P37->(MsUnlock())
				if !lHasNext
					//Chama o Metodo que calcula do Realizado do PSA e grava na P66
					//oActio:CalcRVPsa("CUBO")
				EndIF
			else
				Reclock("P37",.F.)
				P37->P37_STATUS :="3"
				P37->P37_BODYRP  := '{"message":"'+oActio:GetError()+'"}'
				P37->(MsUnlock())
			EndIF
		else
			Reclock("P37",.F.)
			P37->P37_STATUS :="3"
			P37->P37_BODYRP  := '{"message":"Tag Values nao encontrada"}'
			P37->(MsUnlock())

		EndIF
	EndIF
	FreeObj(oActio)
	FreeObj(oReceptor)
Return
