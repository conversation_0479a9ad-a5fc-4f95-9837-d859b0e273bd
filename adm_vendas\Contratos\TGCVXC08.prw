#include 'totvs.ch'
//TIADMVIN-3138
User Function Tgcvxc08
Return 

CLASS TGCVXC08
	
	Data ctargetDir
	Data cPH6Filial
	Data cPH6Contra
	Data cPH6Revisa
	Data cPH6Numero
	Data cPH6Compet
	Data cPH6Condic
	Data cPH6Client
	Data cPH6Loja	
	Data cPH6Condpg
	Data cPH6Codiss
	Data cPH6Notase
	Data cPH6Moeda
	Data cPH6Masccc
	Data cPH6Pedven
	Data cPH6Nota	
	Data cPH6Serie
	Data cPH6Status
	Data cPH6Grupo
	Data cPH6Unineg
	Data cPH6Nummed
	Data cPH6Seq	
	Data cPH6Gu	
	Data cPH6Anomes
	Data cPH6Cmpfat
	Data cPH6Hrgmed
	Data cPH6Hrgped
	Data cPH6Hrgnfs
	Data cPH6Hroper
	Data cPH6Chvpai
	Data cPH6MsBlql
	Data dPH6Dtmoed
	Data dPH6Dtgmed
	Data dPH6Dtgped
	Data dPH6Dtgnfs
	Data dPH6Dtoper
	Data nPH6Vltot
	Data nPH6Vlcanc
	Data nPH6Vlboni
	Data nPH6Vlcare
	Data nPH6Vlnova
	Data nPH6Vlreaj
	Data nPH6Vltran
	Data nPH6Vlrfat
	Data nPH6Bup	
	Data nPH6Bdown
	Data nPH6Vltrib
	Data nPH6Vlfats
	Data nPH6Txmoed
	Data nPH6Vlmoed
	Data nPH6Vlfat2
	Data nPH6Vlraud
	Data nPH6Vlrtrc
	Data nPH6Vlincr
	Data nPH6VlInte
	Data nPH6IntCan
	Data nPH6Vltrae
	Data nPH6Sldcan
	Data nPH6Varroy
	Data nPH6Qtdrea
	Data nPH6Vlmult
	Data nPH6Vlrea
	Data nPH6Vlrbil
	Data nPH6Varbil
	Data nPH6ValDev
	Data nPH6DltTrc
	Data nPH6xVlrRe
	Data nDiaFat
	

	METHOD New()
	METHOD ChkStatus()
	METHOD AtuPH6()
	METHOD ZERAVAR() 
	

ENDCLASS

//-----------------------------------------------------------------
METHOD New() CLASS TGCVXC08

	::ctargetDir	:= ""
	::cPH6Filial	:= ""
	::cPH6Contra	:= ""
	::cPH6Revisa	:= ""
	::cPH6Numero	:= ""
	::cPH6Compet	:= ""
	::cPH6Condic	:= ""
	::cPH6Client	:= ""
	::cPH6Loja		:= ""
	::cPH6Condpg	:= ""
	::cPH6Codiss	:= ""
	::cPH6Notase	:= ""
	::cPH6Moeda	:= ""
	::cPH6Masccc	:= ""
	::cPH6Pedven	:= ""
	::cPH6Nota		:= ""
	::cPH6Serie	:= ""
	::cPH6Status	:= ""
	::cPH6Grupo	:= ""
	::cPH6Unineg	:= ""
	::cPH6Nummed	:= ""
	::cPH6Seq		:= ""
	::cPH6Gu		:= ""
	::cPH6Anomes	:= ""
	::cPH6Cmpfat	:= ""
	::cPH6Hrgmed	:= ""
	::cPH6Hrgped	:= ""
	::cPH6Hrgnfs	:= ""
	::cPH6Hroper	:= ""
	::cPH6Chvpai	:= ""
	::cPH6MsBlql	:= ""
	::dPH6Dtmoed	:= CtoD("")
	::dPH6Dtgmed	:= CtoD("")
	::dPH6Dtgped	:= CtoD("")
	::dPH6Dtgnfs	:= CtoD("")
	::dPH6Dtoper	:= CtoD("")
	::nPH6Vltot	:= 0
	::nPH6Vlcanc	:= 0
	::nPH6Vlboni	:= 0
	::nPH6Vlcare	:= 0
	::nPH6Vlnova	:= 0
	::nPH6Vlreaj	:= 0
	::nPH6Vltran	:= 0
	::nPH6Vlrfat	:= 0
	::nPH6Bup		:= 0
	::nPH6Bdown	:= 0
	::nPH6Vltrib	:= 0
	::nPH6Vlfats	:= 0
	::nPH6Txmoed	:= 0
	::nPH6Vlmoed	:= 0
	::nPH6Vlfat2	:= 0
	::nPH6Vlraud	:= 0
	::nPH6Vlrtrc	:= 0
	::nPH6Vlincr	:= 0
	::nPH6VlInte    := 0
	::nPH6IntCan    := 0
	::nPH6Vltrae	:= 0
	::nPH6Sldcan	:= 0
	::nPH6Varroy	:= 0
	::nPH6Qtdrea	:= 0
	::nPH6Vlmult	:= 0
	::nPH6Vlrea	:= 0
	::nPH6Vlrbil	:= 0
	::nPH6Varbil	:= 0	
	::nPH6ValDev	:= 0
	::nPH6DltTrc	:= 0
	::nPH6xVlrRe	:= 0
	::nDiaFat       := 0
	
	
Return

Method ChkStatus() Class Tgcvxc08
	Local cSitPh5	:= ""

	If ::nPH6ValDev > 0 
		If ::nPH6ValDev >= ::nPH6Vlrfat
			cSitPh5	:= "07"
		Else
			cSitPh5	:= "08"
			::cPH6Chvpai := ::cPH6Gu + ::cPH6Nota + ::cPH6Serie
		EndIf
	ElseIf !Empty(::cPH6Nota	)
			cSitPh5	:= "05"
	ElseIf !Empty(::cPH6Pedven)
			cSitPh5	:= "02"
	ElseIf !Empty(::cPH6Nummed)
			cSitPh5	:= "01"
	Else
		If ::nPH6Vlrfat == 0 
			cSitPh5	:= "06"
		Else
			cSitPh5	:= "00"
		EndIf
	EndIf
	
	::cPH6Status := cSitPh5
Return



Method AtuPH6() Class Tgcvxc08
	Local cChvPh6 := ""
	Local lCria	:= .F.
	Local cAcaLog	:= ""
	
	cChvPh6 := ::cPH6Filial
	cChvPh6 += ::cPH6Contra
	cChvPh6 += ::cPH6Revisa
	cChvPh6 += ::cPH6Numero
	cChvPh6 += ::cPH6Compet
	cChvPh6 += ::cPH6Condic
	cChvPh6 += ::cPH6Client
	cChvPh6 += ::cPH6Loja
	cChvPh6 += ::cPH6Condpg
	cChvPh6 += ::cPH6Notase
	cChvPh6 += ::cPH6Moeda
	cChvPh6 += ::cPH6Masccc
	cChvPh6 += ::cPH6Gu
	cChvPh6 += ::cPH6Seq
   
		
	If PH6->(DbSeek(cChvPh6))
		lCria := .F.
	Else
		lCria := .T.
	EndIf
	

	If PH6->(RecLock("PH6",lCria))
		If lCria		
			PH6->PH6_FILIAL	:= ::cPH6Filial
			PH6->PH6_CONTRA	:= ::cPH6Contra
			PH6->PH6_REVISA	:= ::cPH6Revisa
			PH6->PH6_NUMERO	:= ::cPH6Numero
			PH6->PH6_COMPET	:= ::cPH6Compet
			PH6->PH6_CONDIC	:= ::cPH6Condic
			PH6->PH6_CLIENT	:= ::cPH6Client
			PH6->PH6_LOJA		:= ::cPH6Loja
			PH6->PH6_CONDPG	:= ::cPH6Condpg
			PH6->PH6_NOTASE	:= ::cPH6Notase
			PH6->PH6_MOEDA	:= ::cPH6Moeda
			PH6->PH6_MASCCC	:= ::cPH6Masccc
			PH6->PH6_GU		:= ::cPH6Gu
			PH6->PH6_SEQ		:= ::cPH6Seq
		EndIf
		PH6->PH6_CODISS	:= ::cPH6Codiss
		PH6->PH6_PEDVEN	:= ::cPH6Pedven
		PH6->PH6_NOTA		:= ::cPH6Nota	
		PH6->PH6_SERIE	:= ::cPH6Serie
		
		If !Empty(::ctargetDir)
			If PH6->PH6_STATUS	<> ::cPH6Status
				cAcaLog := "PH6" + ";"
				If lCria
					cAcaLog	+= "Novo" + ";"
				Else
					cAcaLog	+= "Alteracao" + ";"
				EndIf
				cAcaLog += ::cPH6Filial + ";"
				cAcaLog += ::cPH6Contra + ";"
				cAcaLog += ::cPH6Revisa + ";"
				cAcaLog += ::cPH6Numero + ";"
				cAcaLog += ::cPH6Compet + ";"
				cAcaLog += ::cPH6Condic + ";"
				cAcaLog += ::cPH6Client + ";"
				cAcaLog += ::cPH6Loja + ";"
				cAcaLog += ::cPH6Condpg + ";"
				cAcaLog += ::cPH6Notase + ";"
				cAcaLog += ::cPH6Moeda + ";"
				cAcaLog += ::cPH6Masccc + ";"
				cAcaLog += ::cPH6Gu + ";"
				cAcaLog += ::cPH6Seq + ";"
				cAcaLog += PH6->PH6_STATUS + ";" 
				cAcaLog += ::cPH6Status
				
   
				u_xAcalog(::ctargetDir, cAcaLog)
			EndIf
		EndIf
		PH6->PH6_STATUS	:= ::cPH6Status
		PH6->PH6_GRUPO	:= ::cPH6Grupo
		PH6->PH6_UNINEG	:= ::cPH6Unineg
		PH6->PH6_NUMMED	:= ::cPH6Nummed
		PH6->PH6_ANOMES	:= ::cPH6Anomes
		PH6->PH6_CMPFAT	:= ::cPH6Cmpfat
		If Empty(PH6->PH6_HRGMED)
			PH6->PH6_HRGMED	:= ::cPH6Hrgmed
		EndIf
		If Empty(PH6->PH6_HRGPED)
			PH6->PH6_HRGPED	:= ::cPH6Hrgped
		EndIf
		If Empty(PH6->PH6_HRGNFS)
			PH6->PH6_HRGNFS	:= ::cPH6Hrgnfs
		EndIf
		
		PH6->PH6_HROPER	:= ::cPH6Hroper
		PH6->PH6_CHVPAI	:= ::cPH6Chvpai
		PH6->PH6_MSBLQL	:= ::cPH6MsBlQl
		PH6->PH6_DTMOED	:= ::dPH6Dtmoed
		PH6->PH6_DTGMED	:= ::dPH6Dtgmed
		PH6->PH6_DTGPED	:= ::dPH6Dtgped
		PH6->PH6_DTGNFS	:= ::dPH6Dtgnfs
		PH6->PH6_DTOPER	:= ::dPH6Dtoper
		PH6->PH6_VLTOT	:= ::nPH6Vltot
		PH6->PH6_VLCANC	:= ::nPH6Vlcanc
		PH6->PH6_VLBONI	:= ::nPH6Vlboni
		PH6->PH6_VLCARE	:= ::nPH6Vlcare
		PH6->PH6_VLNOVA	:= ::nPH6Vlnova
		PH6->PH6_VLREAJ	:= ::nPH6Vlreaj
		PH6->PH6_VLTRAN	:= ::nPH6Vltran
		PH6->PH6_VLRFAT	:= ::nPH6Vlrfat
		PH6->PH6_BUP		:= ::nPH6Bup
		PH6->PH6_BDOWN	:= ::nPH6Bdown
		PH6->PH6_VLTRIB	:= ::nPH6Vltrib
		PH6->PH6_VLFATS	:= ::nPH6Vlfats
		PH6->PH6_TXMOED	:= ::nPH6Txmoed
		PH6->PH6_VLMOED	:= ::nPH6Vlmoed
		PH6->PH6_VLFAT2	:= ::nPH6Vlfat2
		PH6->PH6_VLRAUD	:= ::nPH6Vlraud
		PH6->PH6_VLRTRC	:= ::nPH6Vlrtrc
		PH6->PH6_VLINCR	:= ::nPH6Vlincr
		PH6->PH6_VLINTE := ::nPH6VlInte
		PH6->PH6_INTCAN := ::nPH6IntCan
		PH6->PH6_VLTRAE	:= ::nPH6Vltrae
		PH6->PH6_SLDCAN	:= ::nPH6Sldcan
		PH6->PH6_VARROY	:= ::nPH6Varroy
		PH6->PH6_QTDREA	:= ::nPH6Qtdrea
		PH6->PH6_VLMULT	:= ::nPH6Vlmult
		PH6->PH6_VLREA	:= ::nPH6Vlrea
		PH6->PH6_VLRBIL	:= ::nPH6Vlrbil
		PH6->PH6_VARBIL	:= ::nPH6Varbil
		PH6->PH6_DLTTRC	:= ::nPH6DltTrc
		PH6->PH6_XVLRRE	:= ::nPH6xVlrRe
		PH6->PH6_DIAFAT := ::nDiaFat

		
		PH6->(MsUnlock())		

	EndIf

Return


METHOD ZERAVAR() CLASS TGCVXC08	
	::cPH6Numero	:= ""
	::cPH6Condic	:= ""
	::cPH6Client	:= ""
	::cPH6Loja		:= ""
	::cPH6Condpg	:= ""
	::cPH6Codiss	:= ""
	::cPH6Notase	:= ""
	::cPH6Moeda	:= ""
	::cPH6Masccc	:= ""
	::cPH6Pedven	:= ""
	::cPH6Nota		:= ""
	::cPH6Serie	:= ""
	::cPH6Status	:= ""
	::cPH6Grupo	:= ""
	::cPH6Unineg	:= ""
	::cPH6Nummed	:= ""
	::cPH6Seq		:= ""
	::cPH6Gu		:= ""
	::cPH6Anomes	:= ""
	::cPH6Cmpfat	:= ""
	::cPH6Hrgmed	:= ""
	::cPH6Hrgped	:= ""
	::cPH6Hrgnfs	:= ""
	::cPH6Hroper	:= ""
	::cPH6Chvpai	:= ""
	::cPH6MsBlql	:= ""
	::dPH6Dtmoed	:= CtoD("")
	::dPH6Dtgmed	:= CtoD("")
	::dPH6Dtgped	:= CtoD("")
	::dPH6Dtgnfs	:= CtoD("")
	::dPH6Dtoper	:= CtoD("")
	::nPH6Vltot	:= 0
	::nPH6Vlcanc	:= 0
	::nPH6Vlboni	:= 0
	::nPH6Vlcare	:= 0
	::nPH6Vlnova	:= 0
	::nPH6Vlreaj	:= 0
	::nPH6Vltran	:= 0
	::nPH6Vlrfat	:= 0
	::nPH6Bup		:= 0
	::nPH6Bdown	:= 0
	::nPH6Vltrib	:= 0
	::nPH6Vlfats	:= 0
	::nPH6Txmoed	:= 0
	::nPH6Vlmoed	:= 0
	::nPH6Vlfat2	:= 0
	::nPH6Vlraud	:= 0
	::nPH6Vlrtrc	:= 0
	::nPH6Vlincr	:= 0
	::nPH6VlInte    := 0
	::nPH6IntCan    := 0
	::nPH6Vltrae	:= 0
	::nPH6Sldcan	:= 0
	::nPH6Varroy	:= 0
	::nPH6Qtdrea	:= 0
	::nPH6Vlmult	:= 0
	::nPH6Vlrea	    := 0
	::nPH6Vlrbil	:= 0
	::nPH6Varbil	:= 0
	::nPH6ValDev	:= 0
	::nPH6DltTrc	:= 0
	::nPH6xVlrRe	:= 0
	::nDiaFat       := 0

Return

