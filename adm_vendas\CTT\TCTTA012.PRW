#Include "PROTHEUS.CH"
#Include "TOTVS.CH"
#Include "RESTFUL.CH"

/*/{Protheus.doc}
Envia os segmentos para o LMS
@protected
<AUTHOR>
@since     14/07/2021
@version
/*/

user function  SendToLMSApi()
    
    private cUrl  := getMv("TI_URLLMSS")
    private oWsdl := TWsdlManager():New()
    private lRetWsdl  := InitiWsdl()

    private cResultado := ""
    private nErro   := 0
    

    if lRetWsdl
        RptStatus({|| ListSegmento()}, "Aguarde...", "Integrando segmentos com  o LMS...")
    else
        MsgAlert("Falha ao conectar no webservice : " + CRLF + alltrim(cUrl) + CRLF + oWsdl:cError)
    endIf

return

static function InitiWsdl()
    local _lret := .f.

    oWsdl:lSSLInsecure := .t.
    oWsdl:nTimeout := 30
    _lRet := oWsdl:ParseURL(alltrim(cUrl))

    if _lret
        _lret := oWsdl:SetOperation("Save")
    endIf

return _lRet

static function ListSegmento()
    local cTabela := U_aParamGet(1,'TI_CTTTREI')
    local nTotal := 0
    local nAtual := 0
    
    BeginSql alias 'ZX5CTT'
        SEELCT ZX5_CHAVE, ZX5_DESCRI 
        FROM %table:ZX5% ZX5
        WHERE ZX5.%notDel%
        AND ZX5.ZX5_TABELA = %exp:cTabela%
    EndSql

    Count to nTotal
    SetRegua(nTotal)

    ZX5CTT->(DbGoTop())
    while !EoF()
        nAtual++
        IncRegua()
        sendSegmento(ZX5_CHAVE, RTRIM(ZX5_DESCRI))
        ZX5CTT->(dbskip())
    endDo
    
    if len(cResultado)
        MSGINFO( cResultado,"Falha ao enviar " + cvaltochar(nErro) + " Registros" )
    endIf
    
    ZX5CTT->(DbCloseArea())
    oWsdl := nil
return 

static function sendSegmento(cChave, cDescricao)

    local cEnvelope := ''
    local cPassaport := GetMv("TI_PASSLMS")
    local cErro

    cEnvelope := '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/" xmlns:lms="http://schemas.datacontract.org/2004/07/LMSAPI.Domain.DTO.Model">'
    cEnvelope += '<soapenv:Header/>'
    cEnvelope += '<soapenv:Body>'
    cEnvelope += '<tem:Save>'
    cEnvelope += '<tem:passport>'+ alltrim(cPassaport) +'</tem:passport>'
    cEnvelope += '<tem:segmentListDTO>'
    cEnvelope += '<lms:SegmentDTO>'
    cEnvelope += '<lms:Description>'+ EncodeUTF8(Alltrim(cDescricao),"cp1252") +'</lms:Description>'
    cEnvelope += '<lms:SegmentId>'+ cChave +'</lms:SegmentId>'
    cEnvelope += '</lms:SegmentDTO>'
    cEnvelope += '</tem:segmentListDTO>'
    cEnvelope += '</tem:Save>'
    cEnvelope += '</soapenv:Body>'
    cEnvelope += '</soapenv:Envelope>'

    lRetWsdl := oWsdl:SendSoapMsg(cEnvelope)

    If !lRetWsdl
        cErro := CRLF + "Erro ao enviar: " +  cDescricao + CRLF
        cErro += "Erro SendSoapMsg: " + oWsdl:cError + CRLF
        cResultado += cErro + CRLF
        nErro ++
    EndIf
return 
