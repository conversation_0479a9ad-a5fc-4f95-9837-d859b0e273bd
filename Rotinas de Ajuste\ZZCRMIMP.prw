#include "protheus.ch"
#Include "topconn.ch"
#include "tbiconn.ch"

/*/{Protheus.doc} ZZCRMIMP

Importa DBF com os dados dos Atendimentos TOTVS IP e grava no CRM SP via WS.
	
<AUTHOR>
@since 11/10/2018
/*/

User Function ZZCRMIMP()

Local cCodEmp := "20"
Local cCodFil := "83"

Public __TTSInUse := .T.
Public __TTSPUSH  := Array(0)
Public __cLogSiga := ""

//Inicializa o ambiente de trabalho
//prepare environment ;
//   empresa cCodEmp ;
//   filial cCodFil 

conout("Iniciado o processamento da rotina ZZCRMIMP - Data: "+DtoC(Date())+" Hora: "+Time())                   

GERAATE()
  
conout("Finalizado o processamento da rotina ZZCRMIMP - Data: "+DtoC(Date())+" Hora: "+Time())                   

//RESET ENVIRONMENT

Return

//
Static Function WSCONN(cAssunto, cDescric, cDataIni, cDataFim, cCnpj, cCodVend)

	local oSvc  := NIL
	local aResp := {}
  
	oSvc := WSCRMSPCLIENT():New()
	oSvc:oWSDADOSATIVIDADES:cASSUNTO     := cAssunto 
	oSvc:oWSDADOSATIVIDADES:cDESCRICAO   := cDescric
	oSvc:oWSDADOSATIVIDADES:cDATAINICIO  := cDataIni
	oSvc:oWSDADOSATIVIDADES:cDATAFIM     := cDataFim
	oSvc:oWSDADOSATIVIDADES:cCNPJ        := cCnpj
	oSvc:oWSDADOSATIVIDADES:cCODVEND     := cCodVend
	
	If oSvc:CRIAATIVIDADE()
		aAdd(aResp, oSvc:oWSCRIAATIVIDADERESULT:cCODIGOATIVIDADE)
		aAdd(aResp, oSvc:oWSCRIAATIVIDADERESULT:nSTATUS)
		aAdd(aResp, oSvc:oWSCRIAATIVIDADERESULT:cTEXTO)
	Endif
  
Return aResp

//
Static Function GERAATE()

Local aCampos 	:= {}
Local aRespos   := NIL
Local nStatus   := 0
Local cCodSp    := ""
Local cTexto	:= ""
Local _nX
Local oTable as object
Local oExcel := FWMsExcelEx():New()

// Abre o arquivo DBF com os Atendimentos
//dbUseArea(.T., "DBFCDXADS", "SYSTEM/env_totvsip.dbf", "ENV", .F.)

// Cria DBF com os dados de Retorno
aAdd (aCampos, { "CODIGO"	, "C", 006,0 } )  // Codigo do Atendimento TOTVS IP
aAdd (aCampos, { "CODATE"	, "C", 006,0 } )  // Codigo do Atendimento SP
aAdd (aCampos, { "STATUS"	, "N", 001,0 } )  // Codigo do Status
aAdd (aCampos, { "TEXTO"	, "C", 512,0 } )  // Descricao

oTable := FWTemporaryTable():New( "RET", aCampos)
oTable:Create()

dbselectarea("ENV")
ENV->(dbgotop())

oExcel:AddworkSheet("RET_TOTVSIP")
oExcel:AddTable ("RET_TOTVSIP","RET_TOTVSIP")

For _nX:= 1 To Len(aCampos)
	oExcel:AddColumn("RET_TOTVSIP","RET_TOTVSIP",aCampos[_nX][1],1,1)
Next

// Inicio do loop para requisitar webservice 
Do While ENV->(!EOF())
      
	conout("ZZCRMIMP - Enviando atendimento: " + ENV->CODIGO)
	
	aRespos := WSCONN( ENV->ASSUNTO, ;
					   ENV->DESCRI, ;
					   DTOC(ENV->DATAINI), ;
					   DTOC(ENV->DATAFIM), ;
					   ENV->CNPJ, ;
					   ENV->CODVEND)
	
	// Grava arquivo de Retorno
	if len(aRespos) >= 2			
		nStatus := aRespos[2]
		cTexto	:= aRespos[3]
	else
		nStatus := 7
		cTexto	:= ""
	endif
			
	If nStatus == 0
		cCodSp := aRespos[1]
	Else
		cCodSp := ""
	EndIf
			
	DbSelectArea("RET")                                                                          
	RecLock("RET", .T.)
	
	RET->CODIGO	:= ENV->CODIGO	
	RET->CODATE	:= cCodSp
	RET->STATUS	:= nStatus
	RET->TEXTO	:= cTexto
								
	MsUnlock()

	oExcel:AddRow("RET_TOTVSIP","RET_TOTVSIP", {ENV->CODIGO	, cCodSp, nStatus, cTexto})

	ENV->(dbSkip())
EndDo

oExcel:Activate()
oExcel:GetXMLFile("RET_TOTVSIP.xls")

ENV->(dbCloseArea())
RET->(dbCloseArea())

Return
