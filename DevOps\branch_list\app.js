const fs = require("fs");
let listFile = process.env.SONAR_PROJECT_LIST;
let currentBranch = process.env.BUILD_SOURCEBRANCHNAME;
let listContent;

listContent = JSON.parse(fs.readFileSync(listFile, "utf8"));

if (currentBranch == "merge") {
  currentBranch = process.env.SYSTEM_PULLREQUEST_SOURCEBRANCH.substring(
    process.env.SYSTEM_PULLREQUEST_SOURCEBRANCH.lastIndexOf("/") + 1
  );
}

if (!listContent.projects.includes(currentBranch)) {
  listContent.projects.push(currentBranch);
  console.log(`${currentBranch} added`);
  fs.writeFileSync(listFile, JSON.stringify(listContent));
  console.log("File data:", listContent);
} else {
  console.log("Branch " + currentBranch + " already exists in file.");
}
