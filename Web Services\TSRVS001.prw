#Include 'Protheus.ch'
#include 'fwmvcdef.ch'
#Include 'ApWebSrv.ch'
#Include 'ApWebEx.ch'
#INCLUDE "topconn.ch"   

//-------------------------------------------------------------------
/*/{Protheus.doc} TSRVS001
Fonte onde esta configurado o webservice com os metodos do portal PMS
<AUTHOR>
@since 02/09/2015 
@version 1.0
/*/
//-------------------------------------------------------------------

//------------------------------------------------------------------------------------------------------------------------------------------
//
//  Definicao das Estruturas 
//
//------------------------------------------------------------------------------------------------------------------------------------------

//-- Estrutura de acesso do tecnico 
WsStruct stAgendaAcesso
	WsData PodeIncluir				As String
	WsData PodeAlterar				As String
	WsData PodeExcluir				As String
	WsData PodeOutroTecnico			As String
	WsData IncluirOS				As String
	WsData AlterarOS				As String
	WsData ExcluirOS				As String
	WsData ConfirmarOS				As String
	WsData DespesasOS				As String
	WsData ImprimirOS				As String
	WsData ImprimirExtrato			As String
	WsData ImprimirRV				As String
EndWsStruct 

//-- Estrutura do cliente 
WsStruct 	stCustomerView
	WsData Code 						As String 
	WsData Loja 						As String
	WsData Name 						As String 
	WsData CNPJ           			As String 
	WsData Segmento         			As String 
EndWsStruct

//-- Estrutura de local de atendimento
WSSTRUCT 	stSOPlace
 	WSDATA 	PlaceIdSeq				As String 
 	WSDATA 	PlaceAdress			As String 
 	WSDATA 	PlaceAdress2			As String 
 	WSDATA 	PlaceCity				As String 
 	WSDATA 	PlaceState				As String 
 	WSDATA 	PlaceKm				As Float     
 	WSDATA 	PlaceDefault			As String 
ENDWSSTRUCT

//-- Estrutura de lista de projetos CFP
WSSTRUCT 	stProjectCFPView
 	WsData 	Customer      		As String 
 	WsData 	Code  					As String 
 	WsData 	Description   		As String
 	WsData 	ReasonCode				As String
 	WsData		ReasonValids        As String Optional
 	WsData 	Segmento				As String
EndWsStruct

//--Estrutura de lista de motivos
WSSTRUCT 	stReasonView
 	WSDATA Code 						As String        
 	WSDATA Description 			As String
 	WSDATA ReasonType				As String        
 	WSDATA CodeValids          	As String Optional  
ENDWSSTRUCT

//-- Estrutura da ordem de servico
WSSTRUCT 	stServiceOrderView
 	WSDATA Analyst  	 				As String 
 	WSDATA DateSO 					As Date 
	WSDATA StartTime  				AS String  
	WSDATA FinishTime   			AS String 
	WSDATA LeadTime					AS String 	 
	WSDATA OFFTime					AS String 
 	WSDATA Customer 	 				As String 
	WSDATA CustomerUnit	 			As String 
	WSDATA LocalAtend				AS String  
	WSDATA CFPProject				AS String  
	WSDATA	 SOReason					As String  
	WSDATA EmailAprova				AS String Optional 
	WSDATA Notes						AS String  
 	WSDATA ServiceCode 				As 	String  
	WSDATA CFPFront					AS String  
	WSDATA NumSO 					AS String  
	WSDATA	 Module					As String Optional 
 	WSDATA ApontamentsSO				As Array of stTaskSO 
ENDWSSTRUCT

//-- Estrutura de apontamentos da OS  
WSSTRUCT 	stTaskSO
	WSDATA TaskCode					AS String 
	WSDATA TaskName 					AS String
	WSDATA TaskStartTime	 			AS String
	WSDATA TaskEndTime 				AS String
	WSDATA TaskQtdHour 				AS Float
	WSDATA TaskNotes 				AS String
	WSDATA TaskProgress				AS Float
ENDWSSTRUCT

//-- Estrutura de retorno da confirmacao de OS
WSSTRUCT 	stRetConfOS
	WSDATA	 ConfOSOK					AS Boolean
	WSDATA MensRDA					AS String Optional
	WSDATA IdOS						AS String Optional 
ENDWSSTRUCT

//-- Estrutura de Dados das Despesas da Ordem de Servico (RDA)
WsStruct stDespesaRda
	WsData Sequencia    			As String
	WsData Despesa					As String
	WsData Descricao					As String
	WsData Quantidade				As Float
	WsData ValorUnit					As Float
	WsData ValorTotal				As Float
	WsData ValorReemb				As Float
EndWsStruct 

//-- Estrutura de Projetos
WsStruct stGradeProjeto
	WsData CFP						AS String
	WsData PROJETO					AS String
	WsData CLIENTE					AS String
	WsData LOJA						AS String
	WsData NOMECLI					AS String
	WsData STATUSPRJ					AS String
	WsData MOTIVO						AS String
	WsData ORCADO						AS Float
	WsData GPP						AS String
 	WsData RESPON						AS String
 	WsData PMO						AS String
	WsData INCLUSAO					AS Date
	WsData INICIO						AS Date
	WsData FIM						AS Date
	WsData UNSRV						As String
EndWsStruct 

//-- Estrutura de modulo da OS  
WsStruct stModuloOS
	WsData Codigo						As String
	WsData Descricao					As String
	WsData EmailResp					As String
EndWsStruct 

//-- Estrutura da agenda do tecnico para gravacao
WsStruct stAgendaTecnicoPut
	WsData Tecnico					As String 
	WsData DataAgenda				As Date
	WsData HoraInicio				As String
	WsData HoraFim					As String
	WsData HoraTraslado				As String                 	
	WsData HoraAlmoco				As String
	WsData HoraExtra					As String	
	WsData Cliente					As String
	WsData Loja						As String
	WsData ProjetoCFP				As String
	WsData FrenteCFP					As String
	WsData MotivoCFP					As String
	WsData CodLocal					As String	
	WsData NumeroAgendas				As Integer		OPTIONAL
	WsData DiasSemana				As String		OPTIONAL
	WsData Observacao				As String
	WsData IdOS						As String		OPTIONAL	
	WsData Modulo						As String
	WsData CodProduto				As String		OPTIONAL
	WsData VlHoraAg					As Float		OPTIONAL
	WsData AlteraTraslado			As String
EndWsStruct 

//-- Estrutura para retorno do inclusao/exclusao de agenda
WsStruct stRetornoAgenda
	WsData Ok							As Boolean
	WsData DadosSobreporAgenda 	As Array Of stSobreporAgenda 	OPTIONAL
	WsData DatasIncluidas       	As Array Of stDataIncluida  	OPTIONAL 
EndWsStruct 

//-- Estrutura para sobrepor agendas
WsStruct stSobreporAgenda
	WsData Marcado					As Boolean
	WsData DataAge					As Date
	WsData DiaSemana					As String
	WsData HoraInicio				As String
	WsData HoraFim					As String
	WsData NomeCliente				As String
	WsData IdOS						As String
	WsData Confirmada				As Boolean
EndWsStruct 

//-- Estrutura de datas de agendas incluidas 
WsStruct stDataIncluida 
	WsData IdOS						As String
	WsData DataAgOS	 				As Date
	WsData Status						As String
EndWsStruct 

//-- Estrutura de Dados das Despesas da Ordem de Servico (RDA)
WsStruct stDespesaRdaPut
	WsData Sequencia    			As String
	WsData Despesa					As String
	WsData Quantidade				As Float
	WsData ValorUnit					As Float
	WsData ValorTotal				As Float
EndWsStruct 

//-- Estrutura de lista de despesas para gravacao 
WsStruct stListaDespesaRdaPut
	WsData ListaDepesas    			As Array Of stDespesaRdaPut
EndWsStruct 

//-- Estrutura da nova agenda do tecnico
WsStruct stAgendaTecnico
	WsData IdOS						As String 
	WsData	 Status					As String	
	WsData Tecnico					As String 
	WsData NomeTecnico				As String 
	WsData DataAgenda				As Date
	WsData HoraInicio				As String
	WsData HoraFim					As String
	WsData HoraTraslado				As String                 	
	WsData HoraAlmoco				As String
	WsData HoraTotal					As String
	WsData Cliente					As String
	WsData Loja						As String
	WsData NomeCliente				As String
	WsData ProjetoCFP				As String
	WsData DescrProjetoCFP			As String
	WsData FrenteCFP					As String
	WsData DescrFrenteCFP			As String
	WsData MotivoCFP					As String
	WsData DescrMotivoCFP			As String
	WsData CodLocal					As String	
	WsData DescrLocal				As String
	WsData Observacao				As String
	WsData Modulo						As String
	WsData CodProduto				As String		OPTIONAL
	WsData VlHoraAg					As Float		OPTIONAL
	WsData AlteraTraslado			As String
	WsData Apontamentos				As Array Of stTaskSO		 OPTIONAL
	WsData Despesas					As Array Of stDespesaRda OPTIONAL
EndWsStruct 

//-- Estrutura de Tipo de Despesa
WsStruct stTipoDespesa
	WsData Codigo						As String 
	WsData Descricao					As String	 
EndWsStruct 

//-- Estrutura de produtos da frente
WsStruct stProdutosFrente
	WsData Produto					As String 
	WsData Descricao					As String	 
EndWsStruct 

//-- Estrutura de impressao da ordem de servico
WSSTRUCT 	stSOPrintView
 	WSDATA CorpName					As String 
 	WSDATA CorpAddress	 			As String 
 	WSDATA CorpAddress2				As String 
 	WSDATA CorpZipCode	 			As String 
 	WSDATA	 CorpCity					As String 
	WSDATA	 CorpState					As String 
	WSDATA	 CorpSite					As String 
	WSDATA	 CorpPhone					As String 
	WSDATA	 Analyst					As String 
 	WSDATA AnalystName 				As String 
	WSDATA KeySO 					As String   
	WSDATA ServiceOrder				AS stAgendaTecnico 				
ENDWSSTRUCT

//-- Estrutura para impressao do Extrato da Agenda do Tecnico
WsStruct stImpExtratoAgenda
	WsData Cabecalho					As stCabecExtrAge	
	WsData Agendas					As Array Of stCorpoExtrAge		OPTIONAL
	WsData Total						As stTotalExtrAge       		
	WsData DadosAdic					As Array Of stAdicExtrAge		OPTIONAL       		
EndWsStruct                       

//-- Estrutura do cabecalho da impressao do Extrato da Agenda do Tecnico
WsStruct stCabecExtrAge
	WsData Tecnico					As String
	WsData NomeTecnico				As String
	WsData Hora						As String
	WsData DataAtu					As Date
EndWsStruct 

//-- Estrutura do corpo da impressao do Extrato da Agenda do Tecnico
WsStruct stCorpoExtrAge
	WsData DataAgenda				As Date
	WsData Cliente					As String
	WsData Loja						As String
	WsData NomeCliente				As String	
	WsData Servico					As String
	WsData HoraInicio				As String
	WsData HoraFim					As String
	WsData HoraOutros				As String
	WsData HoraTraslado				As String
	WsData HoraTotal					As String
	WsData StatusAgenda				As String
	WsData Observacao				As String
EndWsStruct 

//-- Estrutura do total da impressao do Extrato da Agenda do Tecnico
WsStruct stTotalExtrAge
	WsData TotalOutros				As String
	WsData TotalTraslado				As String
	WsData TotalHoras				As String
EndWsStruct 

//-- Estrutura dos dados adicionais da impressao do Extrato da Agenda do Tecnico
WsStruct stAdicExtrAge
	WsData Cliente					As String
	WsData Loja						As String
	WsData NomeCliente				As String
	WsData NomeFantasia				As String
	WsData Endereco					As String
	WsData Bairro						As String
	WsData Municipio					As String
	WsData Estado						As String
	WsData Telefone					As String
	WsData Contato					As String
	WsData EMail						As String
EndWsStruct 

//-- Estrutura das tarefas do PMS (AF9/AFC) 
WsStruct stEdtTarPMS
	WsData Projeto					As String
	WsData Revisao					As String		
	WsData Tarefa						As String
	WsData Nivel						As String		
	WsData Descricao					As String
	WsData HorasDuracao				As Float	
	WsData DataInicio					As Date 
	WsData DataFim					As Date
	WsData EDTPai						As String
	WsData EDTTarefa					As String
	WsData EhTraslado					As String
	WsData ProgressoFisico			As Float	
EndWsStruct

//-- Estrutura para impressao do novo RDA
WsStruct stImpressaoNewRDA
	WsData DadosTecnico				As stCabecNewRDA	
	WsData Despesa					As Array Of stDespesaNewRDA		OPTIONAL
	WsData ExclusaoOS				As Array Of stExclOSNewRDA 	OPTIONAL
	WsData DebitoAnt					As Array Of stDebAntNewRDA 	OPTIONAL
EndWsStruct 

//-- Estrutura do cabecalho do novo RDA
WsStruct stCabecNewRDA
	WsData Tecnico					As String
	WsData NomeTecnico				As String
	WsData Banco						As String
	WsData DataImprime				As Date
	WsData PeriodoDe					As Date
	WsData PeriodoAte				As Date
	WsData CentroCusto				As String
	WsData Coordenador				As String
	WsData Conferencia				As Date
	WsData Fechamento				As String
	WsData StatusRDA					As String
	WsData EmpPag						As String
	WsData Titulo						As String
	WsData Aprovador					As String
	WsData TotDespValor				As Float
	WsData TotExcOS					As Float
	WsData TotDebRDA					As Float
	WsData TotalRda         		As Float
	WsData DataFechamento   		As Date
EndWsStruct             

//-- Estrutura de Dados das Despesas da Ordem de Servico (Novo RDA)
WsStruct stDespesaNewRda
   WsData DataRDA					As Date
	WsData Despesa					As String
	WsData Descricao					As String
	WsData Quantidade				As Float
	WsData ValorUnit					As Float
	WsData ValorReemb				As Float
	WsData WF           			As String
	WsData Observacao   			As String
EndWsStruct 

//-- Estrtura de exclusao de OS do novo RDA                
WsStruct stExclOSNewRDA
	WsData OrdemServico     		As String
	WsData DataOS           		As Date
	WsData Cliente          		As String
	WsData Loja	          		As String
	WsData NomeCliente     		As String  
	WsData Projeto          		As String
	WsData Motivo           		As String
	WsData DataExclusao     		As Date
	WsData ValorRda         		As Float
EndWsStruct          

//-- Estrtura de debitos de fechamentos anteriores do novo RDA
WsStruct stDebAntNewRDA
	WsData Fechamento   			As String
	WsData DataInicio       		As Date
	WsData DataFim          		As Date
	WsData ValorDespesa     		As Float
	WsData ValorExclOS      		As Float
	WsData ValorDebAnt      		As Float
	WsData ValorRDA         		As Float
EndWsStruct       

//-- Estrtura da frente de entrega do CFP
WsStruct stFrenteEntrega
	WsData Frente  					As String
	WsData Descricao					As String
	WsData Motivo			 			As String
	WsData Coordenador	 			As String
	WsData ProjetoPMS	 			As String
	WsData EdtPms			 			As String
EndWsStruct             

//-- Estrutura dos dados adicionais da impressao do Extrato da Agenda do Tecnico
WsStruct stItemRV

	WsData IdOS					As String
	WsData Atraso				As String
	WsData DataOS				As Date
	WsData NomeCliente			As String
	WsData Horas				As Float
	WsData VlrHoras				As Float
	WsData HorasExtras			As Float
	WsData VlrHE				As Float
	WsData HorasTras			As Float
	WsData VlrHT				As Float
	WsData TotalDSR				As Float
	WsData CodProduto			As String
	WsData Motivo			 	As String
	WsData TotalPremio			As Float
	WsData TotalRV				As Float
	WsData HrTot				As Float
	WsData FatPon				As Float
	WsData SemTrasl				As Float
	
EndWsStruct 

//-- Estrutura dos dados de saida do metodo Retornatecnico
WsStruct StruRetTecnico
	WsData cCdEmpresa 				As String
	WsData cNmEmpresa 				As String
	WsData cCdFilial 				As String
	WsData cNmFilial 				As String
	WsData cCdTecnico 				As String
	WsData cNmTecnico 				As String
EndWsStruct	

//-- Estrutura dos dados do KM 
WsStruct stDadosKm
	WsData CodLocal 					As String
	WsData QtdeKm		 				As Float
	WsData ValorKm		 			As Float
	WsData TotalKm		 			As Float
EndWsStruct	

//-- Estrutura dos Projeto x Coordenador Busca
WsStruct stListaPrjCoord
	WsData Projeto					As String 
	WsData Frente						As String 
	WsData Cliente					As String 
	WsData NomeCliente				As String 
	WsData DescricaoProjeto			As String
	WsData DescricaoFrente			As String
	WsData MotivoProjeto				As String  
	WsData MotivosValidos			As String  
EndWsStruct 

//--Estrutura de lista de segmentos do cliente 
WSSTRUCT 	stSegmentoCliente 
 	WSDATA Codigo					As String        
 	WSDATA Descricao	 			As String
ENDWSSTRUCT

//--Estrutura para Pedido de Venda do suporte tecnico tarifado
WsStruct DadosRecStru
	WsData cCliente	As String	//01 - Códifo do Cliente
 	WsData dDataPV		As Date		//02 - Data PV
 	WsData cHrTotal	As String	//05 -
 	WsData cProduto	As String	//06 - Produto
 	WsData cEmpMax		As String	//07 - Empresa do Maxime
 	WsData cFilMax		As String	//08 - Filial do Maxime
 	WsData nVlHora		As Integer	//09 - Valor da hora
 	WsData cObsNota	As String	//10 - Numero do chamado
 	WsData cUserMXM	As String	//11 - Usuário para autenticação no Maxime
 	WsData cPswMXM		As String	//12 - Senha para autenticação no Maxime
 	WsData cIdTicket   As String Optional 
EndWsStruct

//--Estrutura para Pedido de Venda do suporte tecnico tarifado Zendesk
/*WsStruct DadosRecStruZen
	WsData cCliente	As String	//01 - Códifo do Cliente
 	WsData cHrTotal	As String	//05 -
 	WsData cProduto	As String	//06 - Produto
 	WsData cObsNota	As String	//10 - Numero do chamado
 	WsData cUserMXM	As String	//11 - Usuário para autenticação no Maxime
 	WsData cPswMXM		As String	//12 - Senha para autenticação no Maxime
EndWsStruct*/

WsStruct DadosRetStru
	WsData lGravou		As Boolean  //Se gravou ou não o PV
 	WsData cMsg			As String	//Mensagem
 	WsData	nValorHora	As Float Optional	//Valor hora com imposto do Pedido
EndWsStruct

//-- Estrutura de Dados conferencia (RDA)
WsStruct structConfRDA
	WsData MesAno				As String
	WsData Fechamento   		As String
	WsData DataRDA				As Date
	WsData OrdemServico     	As String
	WsData Cliente          	As String
	WsData Loja	          		As String
	WsData NomeCliente     		As String
	WsData Despesa				As String
	WsData Descricao			As String  
	WsData ValorDespesa			As Float
	WsData ValorReemb			As Float
	WsData Fat					As String
	WsData Pag					As String
	WsData UniSrv				As String
	WsData TotSesDespe			As Float Optional
	WsData TotSesReemb			As Float Optional
	WsData TotGerDespe			As Float Optional
	WsData TotGerReemb			As Float Optional
	
EndWsStruct 

//-- Estrutura de lista conferencia (RDA)
//WsStruct stListaConfRdaPut
//	WsData ConfRDARet   			As Array Of stConfRdaPut
//EndWsStruct 

//------------------------------------------------------------------------------------------------------------------------------------------
//
//  Definicao do WebService
//
//------------------------------------------------------------------------------------------------------------------------------------------

//-------------------------------------------------------------------
/*/{Protheus.doc} TSRVS001
Fonte onde esta configurado o webservice com os metodos do portal PMS
<AUTHOR>
@since 02/09/2015 
@version 1.0
@Project TOTVS 12
/*/
//-------------------------------------------------------------------

WSSERVICE TSRVS001 DESCRIPTION "TDI - Portal de Servicos (Agenda, Ordem de serviços, Apontamentos, Despesas de Atendimento)"

	//-------------------------------------------------------------------
	// Dados 
	//-------------------------------------------------------------------

	WSDATA Tecnico              		As String
	WSDATA AgendaAcesso					As stAgendaAcesso
	WSDATA InitialKey	  				AS String
	WSDATA IndexKey 					AS Integer 
	WSDATA ListOfCustomer 				AS Array Of stCustomerView
	WSDATA NumReg 						AS Integer 
	WSDATA AnalystCode					AS String 
	WSDATA CustomerCode 				AS String	
	WSDATA UnitCode 					AS String
	WSDATA StartDate					AS Date
	WSDATA ListOfPlace					AS Array Of stSOPlace	
	WSDATA ListOfProjectCFP 			AS Array Of stProjectCFPView
	WSDATA ReasonCode    				AS String 
	WSDATA ListOfReason     			AS Array Of stReasonView	
	WSDATA SOConfirm					AS stServiceOrderView
	WSDATA ConfirmOK					AS stRetConfOS
	WSDATA IdOS							AS String
	WSDATA Ok							AS Boolean
	WSDATA DataDe						AS Date
	WSDATA DataAte						AS Date
	WSDATA FechaRdaRet					As Boolean
	WSDATA Tipo							AS String
 	WSDATA DespesaRdaList				As Array Of stDespesaRda
 	WSDATA CFP							AS String
 	WSDATA RetProjetoCFP     			As stGradeProjeto
 	WSDATA ListOfModule					As Array Of stModuloOS
 	WSDATA Dummy						As String
	WSDATA AgendaTecnicoPut				As stAgendaTecnicoPut
	WSDATA SobreporAgenda				As stRetornoAgenda
	WSDATA TecnicoLogado				As String
	WSDATA RetRetornoAgenda				As stRetornoAgenda
	WSDATA CodigoOsEletronica   		As String
	WSDATA DespesaGrava					As stListaDespesaRdaPut
 	WSDATA Cliente						As String
 	WSDATA Loja							As String
	WSDATA ProjetoCFP					As String
	WSDATA DadosAgendaTecnico			As Array Of stAgendaTecnico
	WSDATA DespesaList					As Array Of stTipoDespesa
	WSDATA FrenteCFP					As String
	WSDATA ProdutosFrenteList			As Array of stProdutosFrente
	WSDATA OSPrintRet					AS stSOPrintView
	WSDATA Detalhe 						As String
	WSDATA ExtratoImprime				As stImpExtratoAgenda
	WSDATA ProjetoPMS					As String
	WSDATA EDTPai						As String
	WSDATA dtListaEdtTarPMS 			As Array Of stEdtTarPMS
	WSDATA NewRDAImprime				As stImpressaoNewRDA
	WSDATA FrentesCFP					As Array Of stFrenteEntrega
	WSDATA RVImprime					As Array Of stItemRV
//	WSDATA cSegmento					As String
//	WSDATA cLocalidade					As String
//	WSDATA cEmail						As String						
 	WSDATA RetTecnico					As Array Of StruRetTecnico
	WSDATA DataApto						As Date
	WSDATA HoraIni						As String
	WSDATA HoraFim						As String
	WSDATA Duracao						As Float						
	WSDATA CodLocal						As String
	WSDATA DataOS						As Date
	WSDATA DadosRetKM					As stDadosKm
	WSDATA CodigoKm						As String
	WSDATA dtCoordenador				As String
	WSDATA dtOrigem 					As String 
	WSDATA dtListaPrjCoord 				As Array Of stListaPrjCoord
	WSDATA EhJira						As String 
	WSDATA Segmento						As String
	WSDATA dtSegmentoCliente			As Array Of stSegmentoCliente
	WSDATA DadosPv 						As DadosRecStru		//Recebe os dados para efetivar o Pedido de Venda
	//WSDATA DadosPvZen 					As DadosRecStruZen		//Recebe os dados para efetivar o Pedido de Venda
 	WSDATA cRetInfo						As DadosRetStru		//Retora o resultado
 	WSDATA ConfRDARet					As Array Of structConfRDA
 	WSDATA Fat							As String
	WSDATA Pag							As String
	WSDATA UniSrv						As String
	WSDATA TotSesDespe					As Float
	WSDATA TotSesReemb					As Float
	WSDATA TotGerDespe					As Float
	WSDATA TotGerReemb					As Float
	WSDATA dtConfDe						As Date
	WSDATA dtConfAte					As Date
	WSDATA TotalRV						As Float
	WSDATA CodProduto					As String
	WSDATA Motivo					 	As String
	WSDATA TotalPremio					As Float
	
 				
	//-------------------------------------------------------------------
	// Metodos 
	//-------------------------------------------------------------------

	WSMETHOD AgAcessos 					DESCRIPTION "Retorna os acessos para a manutencao de agenda e O.S. para o usuario"	
	WSMETHOD BrwCustomer 				DESCRIPTION "Retorna lista de cliente"
	WSMETHOD BrwPlace	 				DESCRIPTION "Retorna lista dos locais de atendimento"
	WSMETHOD BrwProjectCFP 				DESCRIPTION "Retorna lista dos projetos CFP"
	WSMETHOD BrwReason	 				DESCRIPTION "Retorna lista dos motivos"
	WSMETHOD ConfirmSO					DESCRIPTION "Confirmacao de ordem de servico"
	WSMETHOD DelAgendaTecnico			DESCRIPTION "Apaga agenda do tecnico"
	WSMETHOD DelOrdemServico			DESCRIPTION "Apaga ordem de servico"
	WSMETHOD FechaRDA					DESCRIPTION "Realiza o fechamento do RDA"
	WSMETHOD GetDespesaRda				DESCRIPTION "Retorna lista de despesas da Ordem de Servico (RDA)"
	WSMETHOD GetProjetoCFP    			DESCRIPTION "Retorna dados do projeto CFP (detalhes)"
	WSMETHOD ModuleList					DESCRIPTION "Lista de modulos para o campo modulo da OS"
	WSMETHOD PutAgendaTecnico			DESCRIPTION "Grava agenda do tecnico"
	WSMETHOD PutDespesaRda				DESCRIPTION "Grava despesa da Ordem de Servico (RDA)"
	WSMETHOD RetAgendaPeriodo			DESCRIPTION "Retorna dados da agenda do tecnico no periodo"
	WSMETHOD TiposDespesa		  		DESCRIPTION "Retorna lista com os tipos de despesas"
	WSMETHOD RetProdFrente 				DESCRIPTION "Retorna lista de produtos para a frente de entrega"
	WSMETHOD OSPrint					DESCRIPTION "Retorna dados para impressao OS"
	WSMETHOD ImprimeAgenda	  			DESCRIPTION "Retorna dados para impressao do extrato da agenda do tencico"
	WSMETHOD GetListaEDTPMS				DESCRIPTION "Retorna lista de EDT's do projeto PMS"  
	WSMETHOD GetListaTarEdtPMS			DESCRIPTION "Retorna lista de tarefas da EDT do projeto PMS"
	WSMETHOD ImprimeNewRDA		 		DESCRIPTION "Retorna dados para impressao do novo RDA"
	WSMETHOD RetFrenteCFP		 		DESCRIPTION "Retorna lista das frentes de entrega do projeto CFP"
	WSMETHOD ImprimeRV			   		DESCRIPTION "Retorna dados para impressao do relatorio de remuneracao variavel"
///	WSMETHOD RetornaTecnico 			DESCRIPTION "Retorna grupo/filial do tecnico pelo segmento, localidade e e-mail" 
	WSMETHOD RetDuracaoApto		 		DESCRIPTION "Retorna a duracao do apontamento com base no calendario"
	WSMETHOD RetKM 						DESCRIPTION "Retorna a quilometram para um cliente, loja, projeto, frente, data"
	WSMETHOD RetCodKm					DESCRIPTION "Retorna o codigo da despesa usada para quilometragem" 
	WSMETHOD GetListaProjetoCoorde		DESCRIPTION "Retorna lista de projetos de um coordenador (frentes de entrega)" 
	WSMETHOD BrwSegCli 					DESCRIPTION "Retorna lista dos segmentos do cliente"
	WSMETHOD INCPVSUPTRF 				DESCRIPTION "Inclui pedido de venda a partir do chamado"
	//WSMETHOD INCPVTKZEN 				DESCRIPTION "Inclui pedido de venda a partir do ticket."
	WSMETHOD CONFRDA 					DESCRIPTION "Conferência de RDA."

ENDWSSERVICE

//------------------------------------------------------------------------------------------------------------------------------------------
//
//  Definicao dos Metodos 
//
//------------------------------------------------------------------------------------------------------------------------------------------

//-------------------------------------------------------------------
/*/{Protheus.doc} AgAcessos (TSRVS001)
Retorna os acessos para a manutencao de agenda e O.S. para o usuario
<AUTHOR>
@since 02/09/2015
@param Tecnico, caracter, codigo do tecnico 
@return AgendaAcesso, estrutura, dados do acesso do tecnico 
/*/
//-------------------------------------------------------------------

WSMETHOD AgAcessos WSRECEIVE Tecnico WSSEND AgendaAcesso WSSERVICE TSRVS001
             
Local nTamCod    := TamSX3("RD0_CODIGO")[1]             
Local cTecnico   := Substr(PadR(Alltrim(::Tecnico),nTamCod),1,nTamCod)
Local cUsuario   := ""
Local lRetI      := .F.
Local lRetA      := .F.
Local lRetE      := .F.
Local lRetO      := .F.
Local lRetIncOS  := .T.
Local lRetAltOS  := .T.
Local lRetExcOS  := .T.
Local lRetConfOS := .T.
Local lRetDespOS := .T.
Local lRetImpOS  := .T.
Local lRetImpExt := .T.
Local lRetImpRV  := .T.
U_xCONOUT("TSRVS001","AGACESSOS","REQUISICAO","")

If Empty(::Tecnico)
	SetSoapFault( "Acessos", "Tecnico deve ser informado." )
	U_xCONOUT("TSRVS001","AGACESSOS","(TECNICO DEVE SER INFORMADO) - RETORNO","")
	Return(.F.)
EndIf

//-- posiciona no tecnico para buscar o usuario
dbSelectArea("RD0")
dbSetOrder(1)
dbSeek(xFilial("RD0")+cTecnico)

If EOF()
	SetSoapFault( "Acessos", "Tecnico nao cadastrado." )
	U_xCONOUT("TSRVS001","AGACESSOS","(TECNICO NAO CADASTRADO) - RETORNO","")
	Return(.F.)
EndIf

cUsuario := RD0->RD0_USER

::AgendaAcesso := WsClassNew( "stAgendaAcesso" )

//-- chama funcao de acesso
lRetI := U_TSRVLATI(cUsuario,,7)  // Incluir / Manutenção Agenda
lRetA := lRetI 
lRetE := U_TSRVLATI(cUsuario,,11) // Excluir O.S.s
lRetO := U_TSRVLATI(cUsuario,,25)  // Pode agendar outros técnicos

lRetIncOS := lRetI
lRetAltOS := lRetA
lRetExcOS := lRetE

lRetConfOS := lRetIncOS
lRetDespOS := lRetIncOS

//-- preenche variaveis de retorno
::AgendaAcesso:PodeIncluir     := If(lRetI,"S","N")
::AgendaAcesso:PodeAlterar     := If(lRetA,"S","N")
::AgendaAcesso:PodeExcluir     := If(lRetE,"S","N")
::AgendaAcesso:PodeOutroTecnico:= If(lRetO,"S","N")

::AgendaAcesso:IncluirOS       := "S"
::AgendaAcesso:AlterarOS       := "S"
::AgendaAcesso:ExcluirOS       := "S"
::AgendaAcesso:ConfirmarOS     := "S"
::AgendaAcesso:DespesasOS      := "S"

::AgendaAcesso:ImprimirOS      := "S"
::AgendaAcesso:ImprimirExtrato := "S"
::AgendaAcesso:ImprimirRV      := "S"

U_xCONOUT("TSRVS001","AGACESSOS","RETORNO","")
                                                                 
Return(.T.)


//-------------------------------------------------------------------
/*/{Protheus.doc} BrwCustomer (TSRVS001)
Lista de cliente
<AUTHOR>
@since 02/09/2015
@param InitialKey, caracter, chave de busca para posicionar primeiro registro  
@param IndexKey, numerico, numero do indice para busca (1-por codigo, 2-por nome)   
@param NumReg, numerico, numero de registros que deve retornar     
@param Segmento, caracter, codigo do segmento do cliente     
@return ListOfCustomer, estrutura, dados do cliente (codigo, nome, cnpj) 
/*/
//-------------------------------------------------------------------

WSMETHOD BrwCustomer WSRECEIVE InitialKey, IndexKey, NumReg, Segmento WSSEND ListOfCustomer WSSERVICE TSRVS001

Local nX := 0
U_xCONOUT("TSRVS001","BRWCUSTOMER","REQUISICAO","")

Default ::Initialkey := "" // Filtro
Default ::IndexKey   := 1 // Ordenacao (1-Por codigo, 2-Por nome)
Default ::NumReg     := 10 // retorna de 100 em 100, ou quantidade enviada, a partir do registro passado no Initialkey
Default ::Segmento   := ""

If	::Indexkey < 1 .Or. ::Indexkey > 3 
	SetSoapFault( "Clientes", "Indexkey invalido (1-Por codigo, 2-Por nome).") 
	U_xCONOUT("TSRVS001","BRWCUSTOMER","( INDEXKEY INVALIDO [1-Por codigo, 2-Por nome] ) - RETORNO","")
	Return .F.
Endif

dbSelectArea("SA1") 
dbSetOrder( ::Indexkey ) 
dbSeek( xFilial("SA1") + ::Initialkey, .T. )  

::ListOfCustomer := {}

While !Eof() .And. SA1->A1_FILIAL == xFilial("SA1")

	//-- se passar o segmento, so traz os clientes do segmento selecionado
	If ! Empty(::Segmento)
		If SA1->A1_CODSEG <> ::Segmento
			dbSkip()
			Loop
		Endif
	Endif
	
	nX := nX + 1
	
	AAdd( ::ListOfCustomer, WsClassNew( "stCustomerView" ) )

	::ListOfCustomer[nX]:Code := SA1->A1_COD
	::ListOfCustomer[nX]:Loja := SA1->A1_LOJA
	::ListOfCustomer[nX]:Name := SA1->A1_NOME
	::ListOfCustomer[nX]:CNPJ := SA1->A1_CGC
	::ListOfCustomer[nX]:Segmento := SA1->A1_CODSEG
	
	dbSkip() 

	If nX == ::NumReg
		Exit
	Endif

EndDo
U_xCONOUT("TSRVS001","BRWCUSTOMER","RETORNO","")
Return .T.


//-------------------------------------------------------------------
/*/{Protheus.doc} BrwPlace (TSRVS001)
Lista dos locais de atendimento
<AUTHOR>
@since 02/09/2015
@param AnalystCode, caracter, codigo do tecnico  
@param CustomerCode, caracter, codigo do cliente   
@param UnitCode, caracter, codigo da loja    
@param StartDate, data, data da agenda     
@return ListOfCustomer, estrutura, dados do cliente (codigo, nome, cnpj) 
/*/
//-------------------------------------------------------------------

WSMETHOD BrwPlace WSRECEIVE AnalystCode, CustomerCode, UnitCode, StartDate WSSEND ListOfPlace WSSERVICE TSRVS001

Local cLocalAtend   := ""
Local nX           := 0
Local nTamTec      := TamSX3("RD0_CODIGO")[1]  
Local nTamCli      := TamSX3("A1_COD")[1]        
Local nTamLoj      := TamSX3("A1_LOJA")[1] 
U_xCONOUT("TSRVS001","BRWPLACE","REQUISICAO","")

DEFAULT ::AnalystCode  := ""				// Codigo do tecnico
DEFAULT ::CustomerCode := ""				// Codigo do cliente
DEFAULT ::UnitCode    := ""				// Codigo da loja do cliente
DEFAULT ::StartDate		:= CtoD("")		// Data da OS

::AnalystCode  := Substr(PadR(Alltrim(::AnalystCode),nTamTec),1,nTamTec)
::CustomerCode := Substr(PadR(Alltrim(::CustomerCode),nTamCli),1,nTamCli)
::UnitCode    := Substr(PadR(Alltrim(::UnitCode),nTamLoj),1,nTamLoj)

If Empty(::AnalystCode)
	SetSoapFault( "Local Atendimento", "Tecnico deve ser informado." )
	U_xCONOUT("TSRVS001","BRWPLACE","(TECNICO DEVE SER INFORMADO) - RETORNO","")
	Return(.F.)
EndIf

If Empty(::CustomerCode)
	SetSoapFault( "Local Atendimento", "Cliente deve ser informado." )
	U_xCONOUT("TSRVS001","BRWPLACE","(CLIENTE DEVE SER INFORMADO) - RETORNO","")
	Return(.F.)
EndIf

If Empty(::UnitCode)
	SetSoapFault( "Local Atendimento", "Loja do cliente deve ser informada.")
EndIf

dbSelectArea("RD0") 
dbSetOrder(1)
dbSeek(xFilial("RD0") + ::AnalystCode)  

If Eof()
	SetSoapFault( "Local Atendimento", "Tecnico nao cadastrado." )
	U_xCONOUT("TSRVS001","BRWPLACE","(TECNICO NAO CADASTRADO) - RETORNO","")
	Return(.F.)
EndIf

dbSelectArea("SA1") 
dbSetOrder(1)
MsSeek(xFilial("SA1") + ::CustomerCode + ::UnitCode )  

If Eof()
	SetSoapFault( "Local Atendimento", "Cliente nao cadastrado." )
	U_xCONOUT("TSRVS001","BRWPLACE","(TECNICO NAO CADASTRADO) - RETORNO","")
	Return(.F.)
EndIf

//-- busca na agenda o local agendado para sugerir como default
If ! Empty(::StartDate)
	dbSelectArea("PF9") 
	dbSetOrder(2)  // PF9_FILIAL+PF9_CODTEC+PF9_DATA+PF9_HRINI+PF9_HRFIM+PF9_CLIENT+PF9_LOJA+PF9_GRDEST+PF9_FILDES+PF9_PROJET+PF9_CODFRT+PF9_PARCFP+PF9_IDOS                          
	dbSeek(xFilial("PF9") + ::AnalystCode + DTOS(::StartDate))  
	
	While !EOF() .And. PF9->PF9LIAL == xFilial("PF9") .And. PF9->PF9_CODTEC == ::AnalystCode .And. PF9->PF9_DATA == ::StartDate
		If PF9->PF9_STATUS == "2" .And. PF9->PF9_CLIENT + PF9->PF9_LOJA == ::CusttomerCode + ::UnitCode // agenda liberada
		   cLocalAtend := PF9->PF9_LOCATE
		   Exit
		Endif   
		dbSkip()
	Enddo
Endif

::ListOfPlace := {}

dbSelectArea("PFH") 
dbSetOrder(1)
MsSeek(xFilial("PFH") + ::CustomerCode + ::UnitCode )  

While !EOF() .And. PFH->PFH_FILIAL == xFilial("PFH") .And. PFH->PFH_CLIENT + PFH->PFH_LOJA == ::CustomerCode + ::UnitCode

	nX := nX + 1

	AAdd(::ListOfPlace, WsClassNew("stSOPlace"))

	::ListOfPlace[nX]:PlaceIdSeq 	 := PFH->PFH_IDSEQ
	::ListOfPlace[nX]:PlaceAdress	 := PFH->PFH_END
	::ListOfPlace[nX]:PlaceAdress2  := PFH->PFH_BAIRRO
	::ListOfPlace[nX]:PlaceCity     := PFH->PFH_MUN
	::ListOfPlace[nX]:PlaceState	 := PFH->PFH_EST
	::ListOfPlace[nX]:PlaceKm    	 := PFH->PFH_KM
	::ListOfPlace[nX]:PlaceDefault  := IIf(PFH->PFH_IDSEQ==cLocalAtend,"0","1")

	dbSkip() 

EndDo
U_xCONOUT("TSRVS001","BRWPLACE","RETORNO","")
Return(.T.)


//-------------------------------------------------------------------
/*/{Protheus.doc} BrwProjectCFP (TSRVS001)
Lista projetos CFP
<AUTHOR>
@since 02/09/2015
@param CustomerCode, caracter, codigo do cliente   
@param UnitCode, caracter, codigo da loja    
@param Segmento, caracter, cSegmento    
@return ListOfProjectCFP, estrutura, dados do projeto (codigo, descricao, motivo, motivos validos) 
/*/
//-------------------------------------------------------------------

WSMETHOD BrwProjectCFP 	WSRECEIVE CustomerCode, UnitCode, Segmento WSSEND ListOfProjectCFP WSSERVICE TSRVS001

Local nTamCli  := TamSX3("A1_COD")[1]        
Local nTamLoj  := TamSX3("A1_LOJA")[1] 
Local nTamSeg  := TamSX3("A1_CODSEG")[1] 
Local cCliente := ""
Local cLoja    := ""
Local cSegmento:= ""

Local nX      := 0
U_xCONOUT("TSRVS001","BRWPROJECTCFP","REQUISICAO","")

DEFAULT ::CustomerCode  := ""
DEFAULT ::UnitCode 	   := ""
DEFAULT ::Segmento      := ""

cCliente := Substr(PadR(Alltrim(::CustomerCode),nTamCli),1,nTamCli)
cLoja    := Substr(PadR(Alltrim(::UnitCode),nTamLoj),1,nTamLoj)
cSegmento:= Substr(PadR(Alltrim(::Segmento),nTamSeg),1,nTamSeg)

If Empty(::CustomerCode)
	SetSoapFault( "Projetos", "Cliente deve ser informado." )
	U_xCONOUT("TSRVS001","BRWPROJECTCFP","(CLIENTE DEVE SER INFORMADO) - RETORNO","")
	Return(.F.)
EndIf

If Empty(::UnitCode)
	SetSoapFault( "Projetos", "Loja do cliente deve ser informada.")
EndIf

dbSelectArea("SA1") 
dbSetOrder(1)
MsSeek(xFilial("SA1") + cCliente + cLoja )  

If Eof()
	SetSoapFault( "Projetos", "Cliente nao cadastrado." )
	U_xCONOUT("TSRVS001","BRWPROJECTCFP","(CLIENTE NAO CADASTRADO) - RETORNO","")
	Return(.F.)
EndIf

::ListOfProjectCFP := {}
     
dbSelectArea("PE5") 
dbSetOrder(3)   // PE5_FILIAL+PE5_CLIENT+PE5_LOJA+PE5_PROJET+PE5_UNSRV+PE5_CODMOT+PE5_TPPROJ
MsSeek( xFilial("PE5") + cCliente + cLoja )  

While !EOF() .And. PE5->( PE5_FILIAL + PE5_CLIENT + PE5_LOJA ) == xFilial("PE5") + cCliente + cLoja

	// somente projetos ativos
	If PE5->PE5_STATUS <> "1"  
		PE5->(dbSkip())
		Loop
	EndIf

	// se informar o segmento, filtra somente os projetos do segmento
	If ! Empty(cSegmento)
		If PE5->PE5_CODSEG <> cSegmento 
			PE5->(dbSkip())
			Loop
		EndIf
	EndIf
	
	nX := nX + 1

	AAdd( ::ListOfProjectCFP , WsClassNew( "stProjectCFPView" ) )
	
	::ListOfProjectCFP[nX]:Customer			:= PE5->PE5_CLIENT 
	::ListOfProjectCFP[nX]:Code				:= PE5->PE5_PROJET
	::ListOfProjectCFP[nX]:Description  	:= PE5->PE5_DESPRO
	::ListOfProjectCFP[nX]:ReasonCode		:= PE5->PE5_CODMOT
	::ListOfProjectCFP[nX]:Segmento			:= PE5->PE5_CODSEG
		
	dbSelectArea("PE0")
	dbSetOrder(1)
	dbSeek(xFilial("PE0")+PE5->PE5_CODMOT)
		
	::ListOfProjectCFP[nX]:ReasonValids	:= PE0->PE0_TPSOS

	PE5->(dbSkip())

EndDo
U_xCONOUT("TSRVS001","BRWPROJECTCFP","RETORNO","")         
Return .T.


//-------------------------------------------------------------------
/*/{Protheus.doc} BrwReason (TSRVS001)
Lista de motivos
<AUTHOR>
@since 02/09/2015
@param ReasonCode, caracter, codigo do motivo (vazio retorna todos motivos da tabela)    
@return ListOfReason, estrutura, dados do motivo (codigo, descricao, tipo, motivos validos) 
/*/
//-------------------------------------------------------------------

WSMETHOD BrwReason WSRECEIVE ReasonCode WSSEND ListOfReason WSSERVICE TSRVS001

Local nX := 0
Local nTamCod := TamSX3("PE0_COD")[1]
Local lRet := .T. 
U_xCONOUT("TSRVS001","BRWREASON","REQUISICAO","")          

DEFAULT ::ReasonCode := ""

::ReasonCode := Substr(PadR(Alltrim(::ReasonCode),nTamCod),1,nTamCod)

::ListOfReason := {}

dbSelectArea("PE0") 
dbSetOrder(1)

If Empty(::ReasonCode)
	dbSeek(xFilial("PE0"))
Else  
	dbSeek(xFilial("PE0")+::ReasonCode)  
Endif

While !EOF() .And. PE0->PE0_FILIAL == xFilial("PE0")

	If PE0->PE0_ATIVO <> "S"
		If ! Empty(::ReasonCode) 
			Exit
		Endif
		dbSkip()
		Loop
	Endif

	nX := nX + 1 

	AAdd( ::ListOfReason , WsClassNew( "stReasonView" ) )
	
	::ListOfReason[nX]:Code       := PE0->PE0_COD   
	::ListOfReason[nX]:Description := PE0->PE0_DESC  
	::ListOfReason[nX]:ReasonType  := PE0->PE0_TPMOT 
	::ListOfReason[nX]:CodeValids  := PE0->PE0_TPSOS

	If ! Empty(::ReasonCode) 
		Exit
	Endif

	DbSkip() 

EndDo

If ! Empty(::ReasonCode) 
	If Len(::ListOfReason) == 0
		SetSoapFault( "Motivos", "Motivo inativo ou nao cadastrado." )
		lRet := .F.
	Endif
Endif
U_xCONOUT("TSRVS001","BRWREASON","RETORNO","")  
Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} ConfirmSO (TSRVS001)
Confirmacao de ordem de servico
<AUTHOR>
@since 02/09/2015
@param SOConfirm, estrutura, dados da OS (analista, data, horarios, projeto, motivo, etc)
@param TecnicoLogado, caracter, codigo do tecnico logado no portal 
@param EhJira, caracter, se foi chamado pelo Jira (S-chamado pelo Jira, N-chamado pelo Portal PMS)  
@return ConfirmOk, estrutura, dados da confirmacao (ok, mensagem, codigo da OS gerada)  
/*/
//-------------------------------------------------------------------

WSMETHOD ConfirmSO WSRECEIVE SOConfirm, TecnicoLogado, EhJira WSSEND ConfirmOk WSSERVICE TSRVS001

Local aRet      := {}
Local aDadosOS  := {}
Local aPMS      := {}
Local nX        := 0
Local nLenAFU   := 0
Local nTamTec	  := TamSX3("RD0_CODIGO")[1]  
Local nTamCli   := TamSX3("A1_COD")[1]        
Local nTamLoj   := TamSX3("A1_LOJA")[1] 
Local nTamLoc   := TamSX3("PF9_LOCATE")[1]
Local nTamPrj   := TamSX3("PF9_PROJET")[1]
Local nTamFrt   := TamSX3("PF9_CODFRT")[1]
Local nTamPro   := TamSX3("PF9_PRODUT")[1]
Local lJira     := .F.
Local cTarefa   := ""
Local cRecurAE8 := ""
Local cCalend   := ""  	
Local nQtdHrs   := 0
Local cRevisao  := ""
Local cTpTrans  := GetMV( "TI_SRV1061" ,, "TS" )
U_xCONOUT("TSRVS001","CONFIRMSO","REQUISICAO","")  

DEFAULT ::TecnicoLogado := ""
DEFAULT ::EhJira := "N"

If Upper(::EhJira) == "S"
	lJira := .T.
Endif	

//-- ajusta tamanho dos campos
::TecnicoLogado			:= Substr(PadR(Alltrim(::TecnicoLogado),nTamTec),1,nTamTec)
::SOConfirm:Analyst		:= Substr(PadR(Alltrim(::SOConfirm:Analyst),nTamTec),1,nTamTec)
::SOConfirm:Customer    	:= Substr(PadR(Alltrim(::SOConfirm:Customer),nTamCli),1,nTamCli)
::SOConfirm:CustomerUnit	:= Substr(PadR(Alltrim(::SOConfirm:CustomerUnit),nTamLoj),1,nTamLoj)
::SOConfirm:LocalAtend	:= Substr(PadR(Alltrim(::SOConfirm:LocalAtend),nTamLoc),1,nTamLoc)
::SOConfirm:CFPProject	:= Substr(PadR(Alltrim(::SOConfirm:CFPProject),nTamPrj),1,nTamPrj)
::SOConfirm:CFPFront		:= Substr(PadR(Alltrim(::SOConfirm:CFPFront),nTamFrt),1,nTamFrt)
::SOConfirm:ServiceCode	:= Substr(PadR(Alltrim(::SOConfirm:ServiceCode),nTamPro),1,nTamPro)

//-- para preencher as variaveis de sistema com o tecnico logado
dbSelectArea("RD0")
dbSetOrder(1) 
dbSeek(xFilial("RD0")+::TecnicoLogado)

If EOF() 
	SetSoapFault( "Confirmacao OS", "Tecnico nao encontrado." )
	U_xCONOUT("TSRVS001","CONFIRMSO","(TECNICO NAO ENCONTRADO) - RETORNO","")  
	Return(.F.)
EndIf

cUserName := RD0->RD0_LOGIN //Rd0UsrNamBrw()
__cUserId := RD0->RD0_USER

//-- tratamento para quando for Jira
//-- alguns campos sao preenchidos automaticamente e nao sao passados pelo Jira
If lJira

	dbSelectArea("PE8")
	dbSetOrder(1)
	dbSeek(xFilial("PE8")+::SOConfirm:CFPProject+::SOConfirm:CFPFront)

	If ! Empty(PE8->PE8_MOTIVO)
		::SOConfirm:SOReason := PE8->PE8_MOTIVO
	Else
		dbSelectArea("PE5")
		dbSetOrder(1)
		dbSeek(xFilial("PE5")+::SOConfirm:CFPProject)
		::SOConfirm:SOReason := PE5->PE5_CODMOT	 
	Endif 

	dbSelectArea("PG8")
	dbSetOrder(1)
	dbSeek(xFilial("PG8")+::SOConfirm:CFPProject+::SOConfirm:CFPFront)

	::SOConfirm:LocalAtend  := "999" // local de atendimento interno
	::SOConfirm:ServiceCode := PG8->PG8_PRODUT
	
	dbSelectArea("PFH")
	dbSetOrder(1)
	dbSeek(xFilial("PFH")+"999")

	If EOF() 
		SetSoapFault( "Confirmacao OS", "Local de atendimento interno (999) não configurado para o cliente." )
		U_xCONOUT("TSRVS001","CONFIRMSO","(LOCAL DE ATENDIMENTO INTERNO '999' NAO CONFIGURADO PARA O CLIENTE) - RETORNO","")
		Return(.F.)
	EndIf

Endif

//-- dados da ordem de servico
AAdd(aDadosOS, ::SOConfirm:Analyst )		//³ 1 - cCodTecnico                                                            ³
AAdd(aDadosOS, ::SOConfirm:DateSO )		//³ 2 - dData                                                                  ³
AAdd(aDadosOS, ::SOConfirm:StartTime )		//³ 3 - cHrInicio                                                              ³
AAdd(aDadosOS, ::SOConfirm:FinishTime )	//³ 4 - cHrFim                                                                 ³
AAdd(aDadosOS, ::SOConfirm:LeadTime )		//³ 5 - cTmpTraslado                                                           ³
AAdd(aDadosOS, ::SOConfirm:OFFTime )		//³ 6 - cTmpParada                                                             ³
AAdd(aDadosOS, ::SOConfirm:Customer)		//³ 7 - cCliente                                                               ³
AAdd(aDadosOS, ::SOConfirm:LocalAtend )	//³ 8 - cLocal_Atendimento                                                     ³
AAdd(aDadosOS, ::SOConfirm:CFPProject )	//³ 9 - cProjeto                                                               ³
AAdd(aDadosOS, ::SOConfirm:SOReason )		//³10 - cCodMotivo                                                             ³
AAdd(aDadosOS, ::SOConfirm:EmailAprova )	//³11 - cEmailAprovador                                                        ³
AAdd(aDadosOS, ::SOConfirm:Notes )			//³12 - cObservacoes                                                           ³
AAdd(aDadosOS, ::SOConfirm:CustomerUnit )	//³13 - Loja do Cliente                                                        ³
AAdd(aDadosOS, ::SOConfirm:ServiceCode )	//³14 - PRODUTO                                                                ³
AAdd(aDadosOS, ::SOConfirm:CFPFront )		//³15 - FRENTE                                                                 ³
AAdd(aDadosOS, ::SOConfirm:NumSO )			//³16 - Id da Agenda                                                                 ³
AAdd(aDadosOS, ::SOConfirm:Module )		//³17 - Modulo

//-- tratamento para quando for Jira
//-- gera array de apontamentos automaticamente 
If lJira

	cRevisao := PmsRevAtu(::SOConfirm:CFPProject)
	cTarefa  := ""

	//-- busca a primeira tarefa relacionada a EDT da frente do rpojeto
	dbSelectArea("AF9")
	dbSetOrder(2)
	dbSeek(xFilial("AF9")+::SOConfirm:CFPProject+cRevisao+PE8->PE8_EDT)
	
	While ! Eof() .And. AF9->(AF9_FILIAL+AF9_PROJET+AF9_REVISA+AF9_EDTPAI) == xFilial("AF9")+::SOConfirm:CFPProject+cRevisao+PE8->PE8_EDT
		If Alltrim(AF9->AF9_TIPPAR) <> cTpTrans
			cTarefa := AF9->AF9_TAREFA
			Exit
		Endif	
		dbSkip()
	Enddo

	If Empty(cTarefa)  
		SetSoapFault( "Confirmacao OS", "Nao foi localizada nenhuma tarefa vinculada a frente de entrega selecionada." )
		U_xCONOUT("TSRVS001","CONFIRMSO","(NAO FOI LOCALIZADA NENHUMA TAREFA VINCULADA A FRENTE DE ENTREGA SELECIONADA) - RETORNO","")
		Return(.F.)
	EndIf
	
	//-- busca duracao da tarefa
	cRecurAE8	:= U_SrvXTecAE8(::SOConfirm:Analyst)
	cCalend	:= Posicione("AE8",1,FWxFilial("AE8")+cRecurAE8,"AE8_CALEND") 	
	nQtdHrs	:= PmsHrsItvl(::SOConfirm:DateSO, ::SOConfirm:StartTime, ::SOConfirm:DateSO, ::SOConfirm:FinishTime, cCalend, ::SOConfirm:CFPProject, cRecurAE8,,.T.)

	AAdd(aPMS , {;
				::SOConfirm:CFPProject ,;							//³ 1 - cPrjPMS                                                                ³
				cRevisao ,;											//³ 2 - cRevisao                                                               ³
				cTarefa ,;												//³ 3 - cTarefa                                                                ³
				::SOConfirm:ServiceCode ,;							//³ 4 - cCodProduto                                                            ³
				::SOConfirm:DateSO	,;								//³ 5 - dData                                                                  ³
				::SOConfirm:StartTime ,;								//³ 6 - cHrIniAFU                                                              ³
				::SOConfirm:FinishTime ,;							//³ 7 - cHrFimAFU                                                              ³
				nQtdHrs ,;												//³ 8 - nQtdHrs                                                                ³
				::SOConfirm:SOReason ,;								//³ 9 - cCodMotivo                                                             ³
				"" ,;													//³10 - cObsAFU                                                                ³
				0 ;														//³11 - nProgrFisico                                                           ³
				})

Else

	//-- dados dos apotamentos nas tarefas vinculadas a OS	
	nLenAFU := Len(::SOConfirm:ApontamentsSO)
	
	For nX:=1 to nLenAFU
	
		AAdd(aPMS , {;
					::SOConfirm:CFPProject ,;							//³ 1 - cPrjPMS                                                                ³
					PmsRevAtu(::SOConfirm:CFPProject)	,;				//³ 2 - cRevisao                                                               ³
					::SOConfirm:ApontamentsSO[nX]:TaskCode ,;			//³ 3 - cTarefa                                                                ³
					::SOConfirm:ServiceCode ,;							//³ 4 - cCodProduto                                                            ³
					::SOConfirm:DateSO	,;								//³ 5 - dData                                                                  ³
					::SOConfirm:ApontamentsSO[nX]:TaskStartTime ,;	//³ 6 - cHrIniAFU                                                              ³
					::SOConfirm:ApontamentsSO[nX]:TaskEndTime ,;		//³ 7 - cHrFimAFU                                                              ³
					::SOConfirm:ApontamentsSO[nX]:TaskQtdHour ,;		//³ 8 - nQtdHrs                                                                ³
					::SOConfirm:SOReason ,;								//³ 9 - cCodMotivo                                                             ³
					TRATACARAC(::SOConfirm:ApontamentsSO[nX]:TaskNotes) ,;		//³10 - cObsAFU                                                                ³
					::SOConfirm:ApontamentsSO[nX]:TaskProgress ;		//³11 - nProgrFisico                                                           ³
					})
					
	Next nX

Endif

//-- chama funcao de gravacao da OS
aRet := U_TSRVA106(3,aDadosOS,aPMS)

::ConfirmOk:ConfOSOK	:= aRet[1]
::ConfirmOk:MensRDA	:= aRet[2]
::ConfirmOk:IdOS		:= aRet[3]

If !( aRet[1] )
	SetSoapFault( "Confirmacao OS", aRet[2] )     
Endif
U_xCONOUT("TSRVS001","CONFIRMSO","RETORNO","")
Return aRet[1]


//-------------------------------------------------------------------
/*/{Protheus.doc} DelAgendaTecnico (TSRVS001)
Apaga agenda do tecnico
<AUTHOR>
@since 02/09/2015
@param IdOS, caracter, Id da agenda      
@param TecnicoLogado, caracter, codigo do tecnico logado
@return Ok, logico, se apagou (.T.) ou nao (.F.) a agenda  
/*/
//-------------------------------------------------------------------

WSMETHOD DelAgendaTecnico WSRECEIVE IdOS, TecnicoLogado WSSEND Ok WSSERVICE TSRVS001

Local lRet    := .T.
Local cMsg    := ""
Local nTamCod := TamSX3("PF9_IDOS")[1]
Local nTamTec := TamSX3("RD0_CODIGO")[1]  
Local aOSExc  := {}
Local oModelOS
U_xCONOUT("TSRVS001","DELAGENDATECNICO","REQUISICAO","")

DEFAULT ::IdOS := ""
DEFAULT ::TecnicoLogado := ""

::IdOS 			:= Substr(PadR(Alltrim(::IdOS),nTamCod),1,nTamCod)
::TecnicoLogado	:= Substr(PadR(Alltrim(::TecnicoLogado),nTamTec),1,nTamTec)

//-- para preencher as variaveis de sistema com o tecnico logado
dbSelectArea("RD0")
dbSetOrder(1) 
dbSeek(xFilial("RD0")+::TecnicoLogado)

If EOF() 
	SetSoapFault( "Exclusao Agenda Tecnico", "Tecnico nao encontrado." )
	U_xCONOUT("TSRVS001","DELAGENDATECNICO","(TECNICO NAO ENCONTRADO) - RETORNO","")
	Return(.F.)
EndIf

cUserName := RD0->RD0_LOGIN //Rd0UsrNamBrw()
__cUserId := RD0->RD0_USER

If Empty(::IdOS)
	SetSoapFault( "Exclusao Agenda Tecnico", "Id da agenda deve ser informado." )
	U_xCONOUT("TSRVS001","DELAGENDATECNICO","(ID DA AGENDA DEVE SER INFORMADO) - RETORNO","")
	Return(.F.)
EndIf

dbSelectArea("PF9")
dbSetOrder(1)
If PF9->(!DbSeek(xFilial("PF9")+::IdOS))
	PF9->(DbGoTop())
	PF9->(DbSeek(xFilial("PF9")+::IdOS))
EndIf

If EOF()
	SetSoapFault( "Exclusao Agenda Tecnico", "Id da agenda "+::IdOS+" nao encontrado." )
	U_xCONOUT("TSRVS001","DELAGENDATECNICO","(ID DA AGENDA '"+::IdOS+"' NAO ENCONTRADO ) - RETORNO","")
	Return(.F.)
EndIf

//-- instancia o model e chama a exclusao
oModelOS := FwLoadModel("TSRVA104")
oModelOS:SetOperation(5)
oModelOS:Activate()

//-- valida dados
If oModelOS:VldData()
	oModelOS:CommitData()
Else	
	DisarmTransaction() 
	cMsg := U_SRVXMsgErroModel(oModelOS,.T.)
	lRet := .F.
Endif	          

oModelOS:DeActivate()
  
::Ok := lRet

If ! lRet
	SetSoapFault( "Exclusao Agenda Tecnico", cMsg )
Endif	
U_xCONOUT("TSRVS001","DELAGENDATECNICO","RETORNO","")
Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} DelOrdemServico (TSRVS001)
Apaga ordem de servico
<AUTHOR>
@since 02/09/2015
@param IdOS, caracter, Id da OS  
@param TecnicoLogado, caracter, codigo do tecnico logado    
@return Ok, logico, se apagou (.T.) ou nao (.F.) a agenda  
/*/
//-------------------------------------------------------------------

WSMETHOD DelOrdemServico WSRECEIVE IdOS, TecnicoLogado WSSEND Ok WSSERVICE TSRVS001

Local lRet    := .T.
Local cMsg    := ""
Local nTamCod := TamSX3("PF9_IDOS")[1]
Local nTamTec := TamSX3("RD0_CODIGO")[1]
Local oModelOS 
U_xCONOUT("TSRVS001","DELORDEMSERVICO","REQUISICAO","")

DEFAULT ::IdOS := ""
DEFAULT ::TecnicoLogado := ""

::IdOS := Substr(PadR(Alltrim(::IdOS),nTamCod),1,nTamCod)
::TecnicoLogado	:= Substr(PadR(Alltrim(::TecnicoLogado),nTamTec),1,nTamTec)

//-- para preencher as variaveis de sistema com o tecnico logado
dbSelectArea("RD0")
dbSetOrder(1) 
dbSeek(xFilial("RD0")+::TecnicoLogado)

If EOF() 
	SetSoapFault( "Exclusao Ordem Servico", "Tecnico nao encontrado." )
	U_xCONOUT("TSRVS001","DELORDEMSERVICO","(TECNICO NAO ENCONTRADO) - RETORNO","")
	Return(.F.)
EndIf

cUserName := RD0->RD0_LOGIN //Rd0UsrNamBrw()
__cUserId := RD0->RD0_USER

If Empty(::IdOS)
	SetSoapFault( "Exclusao Ordem Servico", "Id da OS deve ser informado." )
	U_xCONOUT("TSRVS001","DELORDEMSERVICO","(ID DA OS DEVE SER INFORMADO) - RETORNO","")
	Return(.F.)
EndIf

dbSelectArea("PF9")
dbSetOrder(1)
If PF9->(!DbSeek(xFilial("PF9")+::IdOS))
	PF9->(DbGoTop())
	PF9->(DbSeek(xFilial("PF9")+::IdOS))
EndIf

If EOF() 
	SetSoapFault( "Exclusao Ordem Servico", "Id da OS nao encontrado." )
	U_xCONOUT("TSRVS001","DELORDEMSERVICO","(ID DA OS NAO ENCONTRADO) - RETORNO","")
	Return(.F.)
EndIf

//-- instancia o model e chama a exclusao
oModelOS := FwLoadModel("TSRVA107")
oModelOS:SetOperation(5)
oModelOS:Activate()

//-- valida dados
If oModelOS:VldData()
	oModelOS:CommitData()
Else	
	DisarmTransaction() 
	cMsg := U_SRVXMsgErroModel(oModelOS,.T.)
	lRet := .F.
Endif	          

oModelOS:DeActivate()

::Ok := lRet

If ! lRet
	SetSoapFault( "Exclusao Ordem Servico", cMsg )
Endif	
U_xCONOUT("TSRVS001","DELORDEMSERVICO","RETORNO","")
Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} FechaRDA (TSRVS001)
Realiza o fechamento do RDA
<AUTHOR>
@since 02/09/2015
@param Tecnico, caracter, codigo do tecnico      
@param DataDe, data, data inicial do periodo
@param DataAte, data, data final do periodo
@param Tipo, caracter, se testa bloqueio no periodo (S) ou (N)
@return FechaRdaRet, logico, se realizou o fechamento ou nao
/*/
//-------------------------------------------------------------------

WSMETHOD FechaRDA WSRECEIVE Tecnico, DataDe, DataAte WSSEND FechaRdaRet WSSERVICE TSRVS001
            
Local lRet	:= .T.
Local cMsg	:= ""
Local aRet 	:= {}
Local nTamTec	:= TamSX3("RD0_CODIGO")[1]
U_xCONOUT("TSRVS001","FECHARDA","REQUISICAO","")

::Tecnico := Substr(PadR(Alltrim(::Tecnico),nTamTec),1,nTamTec)
  
dbSelectArea("RD0")
dbSetOrder(1)
dbSeek(xFilial("RD0")+::Tecnico)

If Eof()
	cMsg := "Tecnico nao encontrado."
	lRet := .F.
ElseIf Empty(::DataDe)
	cMsg := "Data inicial invalida."
	lRet := .F.
ElseIf Empty(::DataAte)
	cMsg := "Data final invalida."
	lRet := .F.
ElseIf ::DataDe > ::DataAte
	cMsg := "Periodo invalido."
	lRet := .F.
Endif

If lRet 

	//-- chamar nova funcao de fechamento
	aRet := U_TS154INC(::Tecnico, ::DataDe, ::DataAte)
	
	lRet := aRet[1]   
	cMsg := aRet[2]

Endif

::FechaRdaRet := lRet

If ! lRet
	SetSoapFault( "Fechamento RDA", cMsg )
Endif	
U_xCONOUT("TSRVS001","FECHARDA","RETORNO","")
Return(lRet)


//-------------------------------------------------------------------
/*/{Protheus.doc} GetDespesaRda (TSRVS001)
Retorna lista de despesas da Ordem de Servico (RDA)
<AUTHOR>
@since 02/09/2015
@param IdOS, caracter, Id da OS
@return DespesaRdaList, estrutura, dados das despesas (data, despesa, descricao, valor reembolso, valor historico, data fechamento, sequencia)  
/*/
//-------------------------------------------------------------------

WSMETHOD GetDespesaRda WSRECEIVE IdOS WSSEND DespesaRdaList WSSERVICE TSRVS001

Local nI      := 0 
Local aReturn := {}
Local nTamCod := TamSX3("PF9_IDOS")[1]
Local oModelOS
Local oModelDesp   
U_xCONOUT("TSRVS001","GETDESPESARDA","REQUISICAO","")

DEFAULT ::IdOS := ""

::IdOS := Substr(PadR(Alltrim(::IdOS),nTamCod),1,nTamCod)

If Empty(::IdOS)
	SetSoapFault( "Despesa RDA", "Id da OS deve ser informado." )
	U_xCONOUT("TSRVS001","GETDESPESARDA","(ID DA OS DEVE SER INFORMADO) - RETORNO","")
	Return(.F.)
EndIf

dbSelectArea("PF9")
dbSetOrder(1)
dbSeek(xFilial("PF9")+::IdOS)

If EOF() 
	SetSoapFault( "Despesa RDA", "Id da OS nao encontrado." )
	U_xCONOUT("TSRVS001","GETDESPESARDA","(ID DA OS NAO ENCONTRADO) - RETORNO","")
	Return(.F.)
EndIf

//-- carrega o model da ordem de servico
oModelOS := FwLoadModel("TSRVA105")
oModelOS:SetOperation(1)
oModelOS:Activate()

//-- carrega o model das despesas
oModelDesp := oModelOS:GetModel("PFMDETAIL")

//-- carrega os dados na estrutura
::DespesaRdaList := {}

For nI:= 1 To oModelDesp:Length()

	oModelDesp:GoLine(nI)
                          
	AAdd( ::DespesaRdaList, WsClassNew( "stDespesaRda" ) )

	::DespesaRdaList[nI]:Sequencia		:= oModelDesp:GetValue("PFM_SEQID")
	::DespesaRdaList[nI]:Despesa		:= oModelDesp:GetValue("PFM_CODDES")
	::DespesaRdaList[nI]:Descricao		:= oModelDesp:GetValue("PFM_DESCR")
	::DespesaRdaList[nI]:Quantidade		:= oModelDesp:GetValue("PFM_QUANT")
	::DespesaRdaList[nI]:ValorUnit		:= oModelDesp:GetValue("PFM_VLUNIT")
	::DespesaRdaList[nI]:ValorTotal		:= oModelDesp:GetValue("PFM_VLDESP")
	::DespesaRdaList[nI]:ValorReemb		:= oModelDesp:GetValue("PFM_VLREEM")
	
Next nI

oModelOS:DeActivate()
U_xCONOUT("TSRVS001","GETDESPESARDA","RETORNO","")
Return .T.


//-------------------------------------------------------------------
/*/{Protheus.doc} GetProjetoCFP (TSRVS001)
Retorna dados do projeto CFP (detalhes)
<AUTHOR>
@since 17/09/2015
@param CFP, caracter, codigo do projeto CFP
@return RetProjetoCFP, estrutura, dados do projeto CFP (detalhes do projeto)   
/*/
//-------------------------------------------------------------------

WSMETHOD GetProjetoCFP WSRECEIVE CFP WSSEND RetProjetoCFP WSSERVICE TSRVS001

Local nTamCod := TamSX3("PE5_PROJET")[1]
Local aStatus := {"ATIVO","ENCERRADO","SUSPENSO","GERADO","BLOQUEADO"}
Local cRespon := ""
Local cGPP    := ""
Local cPMO    := ""
U_xCONOUT("TSRVS001","GETPROJETOCFP","REQUISICAO","")

DEFAULT ::CFP := ""

::CFP := Substr(PadR(Alltrim(::CFP),nTamCod),1,nTamCod)

If Empty(::CFP)
	SetSoapFault( "Projeto CFP", "Projeto nao informado." )
	U_xCONOUT("TSRVS001","GETPROJETOCFP","(PROJETO NAO INFORMADO) - RETORNO","")
	Return(.F.)
Endif

dbSelectArea("PE5")
dbSetOrder(1)
dbSeek(xFilial("PE5")+::CFP)

If Eof()
	SetSoapFault( "Projeto CFP", "Projeto nao encontrado." )
	U_xCONOUT("TSRVS001","GETPROJETOCFP","(PROJETO NAO ENCONTRADO) - RETORNO","")
	Return(.F.)
Endif

//-- busca os participantes do projetos 
cRespon	:= ""  
cGPP		:= ""  
cPMO		:= ""  

dbSelectArea("PE7")
dbSetOrder(1)
dbSeek(xFilial("PE7")+::CFP)

While ! Eof() .And. PE7->PE7_FILIAL == xFilial("PF7") .And. PE7->PE7_PROJET == ::CFP

	If Empty(cRespon) .And. Alltrim(PE7->PE7_PAPEL) == "CP"  
		cRespon := PE7->PE7_CODREC
	Endif	  

	If Empty(cGPP) .And. Alltrim(PE7->PE7_PAPEL) == "GP"  
		cGPP := PE7->PE7_CODREC
	Endif	  
	
	If Empty(cPMO) .And. Alltrim(PE7->PE7_PAPEL) == "AP"  
		cPMO := PE7->PE7_CODREC
	Endif	  

	dbSkip()
	
Enddo

//::RetProjetoCFP := WsClassNew( "stGradeProjeto" )

::RetProjetoCFP:CFP		 	:= PE5->PE5_PROJET 		
::RetProjetoCFP:PROJETO	 	:= PE5->PE5_DESPRO
::RetProjetoCFP:CLIENTE	 	:= PE5->PE5_CLIENT  
::RetProjetoCFP:LOJA 	 	:= PE5->PE5_LOJA
::RetProjetoCFP:NOMECLI		:= Posicione("SA1",1,xFilial("SA1")+PE5->PE5_CLIENT+PE5->PE5_LOJA,"A1_NOME")			
::RetProjetoCFP:STATUSPRJ 	:= aStatus[Val(PE5->PE5_STATUS)]
::RetProjetoCFP:MOTIVO		:= PE5->PE5_CODMOT	 
::RetProjetoCFP:ORCADO 		:= PE5->PE5_QTDORC
::RetProjetoCFP:GPP 			:= cGPP + " - " + Posicione("RD0",1,xFilial("RD0")+cGPP,"RD0_NOME")	
::RetProjetoCFP:RESPON		:= cRespon + " - " + Posicione("RD0",1,xFilial("RD0")+cRespon,"RD0_NOME")	
::RetProjetoCFP:PMO 			:= cPMO + " - " + Posicione("RD0",1,xFilial("RD0")+cPMO,"RD0_NOME")
::RetProjetoCFP:INCLUSAO		:= PE5->PE5_EMISSA 
::RetProjetoCFP:INICIO		:= PE5->PE5_DTINI	 
::RetProjetoCFP:FIM 			:= PE5->PE5_DTFIM
::RetProjetoCFP:UNSRV 		:= PE5->PE5_UNSRV + " - " + Posicione("PE1",1,xFilial("PE1")+PE5->PE5_UNSRV,"PE1_DESCRI")	

U_xCONOUT("TSRVS001","GETPROJETOCFP","RETORNO","")

Return(.T.)


//-------------------------------------------------------------------
/*/{Protheus.doc} ModuleList (TSRVS001)
Retorna dados do projeto CFP (detalhes)
<AUTHOR>
@since 17/09/2015
@param Dummy, caracter, sempre passar vazio 
@return ListOfModule, estrutura, dados do modulo da OS (codigo, descricao)    
/*/
//-------------------------------------------------------------------

WSMETHOD ModuleList WSRECEIVE Dummy WSSEND ListOfModule WSSERVICE TSRVS001

Local nX
U_xCONOUT("TSRVS001","MODULELIST","REQUISICAO","")

Default ::Dummy := ""

::ListOfModule := {}

dbSelectArea("PF7")
dbSetOrder(1)
dbSeek(xFilial("PF7"))

If ! Eof()

	While ! Eof() .And. PF7->PF7_FILIAL == xFilial("PF7") 
	
		AAdd( ::ListOfModule, WsClassNew( "stModuloOS" ) )
		
		nX := Len(::ListOfModule)
	
		::ListOfModule[nX]:Codigo		:= PF7->PF7_COD
		::ListOfModule[nX]:Descricao	:= PF7->PF7_DESC
		::ListOfModule[nX]:EmailResp	:= PF7->PF7_EMAIL
		
		dbSkip()
		
	Enddo
 
Else

	SetSoapFault( "Modulos", "Sem modulos para listar." )

Endif
U_xCONOUT("TSRVS001","MODULELIST","RETORNO","")
Return .T.


//-------------------------------------------------------------------
/*/{Protheus.doc} PutAgendaTecnico (TSRVS001)
Grava agenda do tecnico 
<AUTHOR>
@since 17/09/2015
@param Tipo, caracter, tipo (I=incluir, A=alterar)
@param Tecnico, codigo do tecnico
@param DataDe, data, data de inicio da agenda
@param AgendaTecnicoPut, estrutura, dados de inclusao da agenda
@param SobreporAgenda, estrutura, dados para sobrepor agenda
@param TecnicoLogado, caracter, email do tecnico logado no portal
@return RetRetornoAgenda, estrutura, dados da gravacao da agenda (ok, estrutura sobrepor, estrutura IDOS)
@obs se retornar .F. verificar o array SobreporAgenda para ver se tem itens para sobrepor.
     chamar novamente a funcao com os itens marcados que devem ser sobrepostos.      
/*/
//-------------------------------------------------------------------

WSMETHOD PutAgendaTecnico WSRECEIVE Tipo, Tecnico, DataDe, AgendaTecnicoPut, SobreporAgenda, TecnicoLogado WSSEND RetRetornoAgenda WSSERVICE TSRVS001

Local lDiaSeg   	:= .F.
Local lDiaTer   	:= .F.
Local lDiaQua   	:= .F.
Local lDiaQui   	:= .F.
Local lDiaSex   	:= .F.
Local lDiaSab   	:= .F.
Local lDiaDom		:= .F.
Local lCheckFer 	:= .F.                     
Local nTamTec   	:= TamSX3("RD0_CODIGO")[1]             
Local nTamCli		:= TamSX3("A1_COD")[1]             
Local aDadosOS		:= {}
Local aDiasSem		:= {}
Local nQtdDias  	:= 1                       
Local lHrExtr   	:= .F.
Local aRet			:= {} 
Local aSobrepor		:= {}
Local aAgendadas	:= {}
Local lRet      := .F.
Local cMsg      := ""
Local nOpcX		:= 0
Local cAltTras	:= "" 
Local nX
cONOUT("cHAMOU ROTINA")  
U_xCONOUT("TSRVS001","PUTAGENDATECNICO","REQUISICAO","")         
//-- Verifica operacao
If Alltrim(Upper(::Tipo)) == "I" // inclusao
	nOpcx := 3
ElseIf Alltrim(Upper(::Tipo)) == "A" // altercao
	nOpcx := 4
Else
	SetSoapFault( "Agenda Tecnico", "Tipo de operação inválido. Opções permitidas: 'I'=inclusão e 'A'=alteração." )
	U_xCONOUT("TSRVS001","PUTAGENDATECNICO","(TIPO DE OPERACAO INVALIDO. OPCOES PERMITIDAS: 'I'=inclusão e 'A'=alteração.) - RETORNO","")  
	Return .F.
Endif

//-- ajusta tamanho dos campos
::TecnicoLogado := Substr(PadR(Alltrim(::TecnicoLogado),nTamTec),1,nTamTec)
::Tecnico       := Substr(PadR(Alltrim(::Tecnico),nTamTec),1,nTamTec)
::AgendaTecnicoPut:Cliente := Substr(PadR(Alltrim(::AgendaTecnicoPut:Cliente),nTamCli),1,nTamCli)

//-- para preencher as variaveis de sistema com o tecnico logado
dbSelectArea("RD0")
dbSetOrder(1) 
dbSeek(xFilial("RD0")+::TecnicoLogado)

cUserName := RD0->RD0_LOGIN //Rd0UsrNamBrw()
__cUserId := RD0->RD0_USER

//-- Verifica quais dias da semana foram marcados
lDiaDom	:= If(Substr(::AgendaTecnicoPut:DiasSemana,1,1)=="S",.T.,.F.)
lDiaSeg	:= If(Substr(::AgendaTecnicoPut:DiasSemana,2,1)=="S",.T.,.F.)
lDiaTer	:= If(Substr(::AgendaTecnicoPut:DiasSemana,3,1)=="S",.T.,.F.)
lDiaQua	:= If(Substr(::AgendaTecnicoPut:DiasSemana,4,1)=="S",.T.,.F.)
lDiaQui	:= If(Substr(::AgendaTecnicoPut:DiasSemana,5,1)=="S",.T.,.F.)
lDiaSex	:= If(Substr(::AgendaTecnicoPut:DiasSemana,6,1)=="S",.T.,.F.)
lDiaSab	:= If(Substr(::AgendaTecnicoPut:DiasSemana,7,1)=="S",.T.,.F.)
lCheckFer	:= If(Substr(::AgendaTecnicoPut:DiasSemana,8,1)=="S",.T.,.F.)

aDiasSem := { lDiaDom, lDiaSeg, lDiaTer, lDiaQua, lDiaQui, lDiaSex, lDiaSab, lCheckFer }
lHrExtr  := If(::AgendaTecnicoPut:HoraExtra=="S",.T.,.F.) 
nQtdDias := ::AgendaTecnicoPut:NumeroAgendas

If nOpcx == 4  // alteracao
	nQtdDias := 1
Endif

//³ Estrutura do array aDadosOS                                                ³
//³ 1 - cCodTecnico                                                            ³
//³ 2 - dData                                                                  ³
//³ 3 - cHrInicio                                                              ³
//³ 4 - cHrFim                                                                 ³
//³ 5 - cTmpTraslado                                                           ³
//³ 6 - cTmpParada                                                             ³
//³ 7 - lHrExtra                                                               ³
//³ 8 - cCliente                                                               ³
//³ 9 - cProjeto                                                               ³
//³10 - cFrente_Entrega                                                        ³
//³11 - cCodMotivo                                                             ³
//³12 - cLocal_Atendimento                                                     ³
//³13 - nReplica                                                               ³
//³14 - aDiasSemana                                                            ³
//³ 		1 - lDomingo                                                         ³
//³ 		2 - 	lSegunda                                                      ³
//³ 		3 - 	lTerça                                                        ³
//³ 		4 - 	lQuarta                                                       ³
//³ 		5 - 	lQuinta                                                       ³
//³ 		6 - 	lSexta                                                        ³
//³ 		7 - 	lSabado                                                       ³
//³ 		8 - 	lFeriado                                                      ³
//³15 - cObservacoes                                                           ³
//³16 - IDOS                                                                   ³
//³17 - Loja do Cliente                                                        ³
//³18 - Modulo                                                                 ³
//³19 - Valor/Hora de OS Avulsa                                                ³
//³20 - Permite alterar Translado na confirmacao ? (.T./.F.)                   ³
//³21 - Codigo do Produto                                                      ³
//³22 - Bloqueia Total Trabalhado superior ao agendado                         ³


cAltTras := IIf(::AgendaTecnicoPut:AlteraTraslado=="S",.T.,.F.)

//-- array com os dados da OS
aDadosOS := { ;
				::AgendaTecnicoPut:Tecnico 			,;	//³ 1 - cCodTecnico
				::AgendaTecnicoPut:DataAgenda		,;	//³ 2 - dData 
				::AgendaTecnicoPut:HoraInicio		,;	//³ 3 - cHrInicio
				::AgendaTecnicoPut:HoraFim			,;	//³ 4 - cHrFim  
				::AgendaTecnicoPut:HoraTraslado		,;	//³ 5 - cTmpTraslado                 	
				::AgendaTecnicoPut:HoraAlmoco		,;	//³ 6 - cTmpParada 
				lHrExtr								,;	//³ 7 - lHrExtra 
				::AgendaTecnicoPut:Cliente			,;	//³ 8 - cCliente 
				::AgendaTecnicoPut:ProjetoCFP		,;	//³ 9 - cProjeto
				::AgendaTecnicoPut:FrenteCFP		,;	//³10 - cFrente_Entrega 
				::AgendaTecnicoPut:MotivoCFP		,;	//³11 - cCodMotivo
				::AgendaTecnicoPut:CodLocal			,;	//³12 - cLocal_Atendimento 
				nQtdDias								,;	//³13 - nReplica	
				aDiasSem								,;	//³14 - aDiasSemana							
				TRATACARAC(::AgendaTecnicoPut:Observacao)		,;	//³15 - cObservacoes 
				::AgendaTecnicoPut:IdOS				,;	//³16 - IDOS
				::AgendaTecnicoPut:Loja				,;	//³17 - Loja do Cliente 
				::AgendaTecnicoPut:Modulo			,;	//³18 - Modulo
				::AgendaTecnicoPut:VlHoraAg			,;	//³19 - Valor/Hora de OS Avulsa
				cAltTras							,;	//³20 - Permite alterar Translado na confirmacao ? (.T./.F.)
				::AgendaTecnicoPut:CodProduto		,;	//³21 - CodProduto
				.T.                                  ;	//³22 - Bloqueia Total Trabalhado Superior
				}

//³ Estrutura do array aSobrepor                                               ³
//³ 1 - lSobrepor                                                              ³
//³ 2 - dDataAgd                                                               ³
//³ 3 - cDiaSemana                                                             ³
//³ 4 - cHoraInicio                                                            ³
//³ 5 - cHoraFim                                                               ³
//³ 6 - cCliente (Código e Nome do Cliente)                                    ³
//³ 7 - cIdOS                                                                  ³
//³ 8 - lConfirmada                                                            ³
  				 
//-- preenche array com os dados das agendas que precisam ser sobrepostas
aSobrepor := {}

For nX:=1 To Len(::SobreporAgenda:DadosSobreporAgenda)

	If ::SobreporAgenda:DadosSobreporAgenda[nX]:DiaSemana == "VAZIO" .Or. Empty(::SobreporAgenda:DadosSobreporAgenda[nX]:DiaSemana)
		Exit
	Endif	
        
	AAdd( aSobrepor , { ;
							::SobreporAgenda:DadosSobreporAgenda[nX]:Marcado 	,;	//³ 1 - lSobrepor 
						 	::SobreporAgenda:DadosSobreporAgenda[nX]:DataAge   	,;	//³ 2 - dDataAgd
						 	::SobreporAgenda:DadosSobreporAgenda[nX]:DiaSemana	,;	//³ 3 - cDiaSemana  
						 	::SobreporAgenda:DadosSobreporAgenda[nX]:HoraInicio	,;	//³ 4 - cHoraInicio 
						 	::SobreporAgenda:DadosSobreporAgenda[nX]:HoraFim		,;	//³ 5 - cHoraFim  
						 	::SobreporAgenda:DadosSobreporAgenda[nX]:NomeCliente	,;	//³ 6 - cCliente (Código e Nome do Cliente)
						 	::SobreporAgenda:DadosSobreporAgenda[nX]:IdOS      	,;	//³ 7 - cIdOS
						 	::SobreporAgenda:DadosSobreporAgenda[nX]:Confirmada 	; 	//³ 8 - lConfirmada
						 })

Next nX                                      

//-- chamada da funcao de gravacao de OS
aRet := U_TSRVA104(nOpcX,aDadosOS,aSobrepor)

lRet 		:= aRet[1]
cMsg 		:= aRet[2]
aSobrepor	:= aRet[3]
aAgendadas	:= aRet[4]

::RetRetornoAgenda:Ok := lRet

//-- preenche dados de agenda para sobrepor
::RetRetornoAgenda:DadosSobreporAgenda := {}

If Len(aSobrepor) > 0

	For nX:=1 To Len(aSobrepor)
	                                                                         
		AAdd( ::RetRetornoAgenda:DadosSobreporAgenda, WsClassNew( "stSobreporAgenda" ) )
	
		::RetRetornoAgenda:DadosSobreporAgenda[nX]:Marcado 		:= aSobrepor[nX,1]	//³ 1 - lSobrepor 
		::RetRetornoAgenda:DadosSobreporAgenda[nX]:DataAge		:= aSobrepor[nX,2]	//³ 2 - dDataAgd
		::RetRetornoAgenda:DadosSobreporAgenda[nX]:DiaSemana		:= aSobrepor[nX,3]	//³ 3 - cDiaSemana 
		::RetRetornoAgenda:DadosSobreporAgenda[nX]:HoraInicio	:= aSobrepor[nX,4]	//³ 4 - cHoraInicio
		::RetRetornoAgenda:DadosSobreporAgenda[nX]:HoraFim		:= aSobrepor[nX,5]	//³ 5 - cHoraFim
		::RetRetornoAgenda:DadosSobreporAgenda[nX]:NomeCliente	:= aSobrepor[nX,6]	//³ 6 - cCliente (Código e Nome do Cliente)
		::RetRetornoAgenda:DadosSobreporAgenda[nX]:IdOS			:= aSobrepor[nX,7]	//³ 7 - cIdOS
		::RetRetornoAgenda:DadosSobreporAgenda[nX]:Confirmada	:= aSobrepor[nX,8]	//³ 8 - lConfirmada
	
	Next nX                                      

Else

	AAdd( ::RetRetornoAgenda:DadosSobreporAgenda, WsClassNew( "stSobreporAgenda" ) )

	nX := 1 

	::RetRetornoAgenda:DadosSobreporAgenda[nX]:Marcado 		:= .F. 		//³ 1 - lSobrepor 
	::RetRetornoAgenda:DadosSobreporAgenda[nX]:DataAge		:= CtoD("") 	//³ 2 - dDataAgd
	::RetRetornoAgenda:DadosSobreporAgenda[nX]:DiaSemana		:= ""			//³ 3 - cDiaSemana 
	::RetRetornoAgenda:DadosSobreporAgenda[nX]:HoraInicio	:= ""			//³ 4 - cHoraInicio
	::RetRetornoAgenda:DadosSobreporAgenda[nX]:HoraFim		:= ""			//³ 5 - cHoraFim
	::RetRetornoAgenda:DadosSobreporAgenda[nX]:NomeCliente	:= ""			//³ 6 - cCliente (Código e Nome do Cliente)
	::RetRetornoAgenda:DadosSobreporAgenda[nX]:IdOS			:= ""			//³ 7 - cIdOS
	::RetRetornoAgenda:DadosSobreporAgenda[nX]:Confirmada	:= .F.			//³ 8 - lConfirmada

Endif

//-- preenche os Id das agendas incluidas  
::RetRetornoAgenda:DatasIncluidas := {}

If Len(aAgendadas) > 0

	For nX:=1 To Len(aAgendadas)
	
		AAdd( ::RetRetornoAgenda:DatasIncluidas, WsClassNew( "stDataIncluida") )
		
		dbSelectArea("PF9")
		dbSetOrder(1)
		dbSeek(xFilial("PF9")+aAgendadas[nX])
	
		::RetRetornoAgenda:DatasIncluidas[nX]:IdOS 		:= aAgendadas[nX]
		::RetRetornoAgenda:DatasIncluidas[nX]:DataAgOS	:= PF9->PF9_DATA 
		::RetRetornoAgenda:DatasIncluidas[nX]:Status		:= PF9->PF9_STATUS 
	
	Next nX                                      

Else

	AAdd( ::RetRetornoAgenda:DatasIncluidas, WsClassNew( "stDataIncluida") )
	
	nX := 1 
	
	::RetRetornoAgenda:DatasIncluidas[nX]:IdOS 		:= ""
	::RetRetornoAgenda:DatasIncluidas[nX]:DataAgOS	:= CtoD("")  
	::RetRetornoAgenda:DatasIncluidas[nX]:Status		:= "" 

Endif

If ! lRet
	SetSoapFault( "Agenda Tecnico", cMsg )
Endif
U_xCONOUT("TSRVS001","PUTAGENDATECNICO","RETORNO","")	
Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} PutAgendaTecnico (TSRVS001)
Metodo para gravar a despesa da OS (RDA)
<AUTHOR>
@since 17/09/2015
@param CodigoOsEletronica, caracter, IDOS 
@param Tecnico, codigo do tecnico
@param DespesaGrava, estrutura, array com lista dos dados da despesa para gravar 
@param Tipo, caracter, tipo de operacao (I=Incluir, A=Alterar, E=Excluir) 
@return ok, logico, se efetuou operacao
@obs passar uma despesa de cada vez para gravar
/*/
//-------------------------------------------------------------------

WSMETHOD PutDespesaRda WSRECEIVE CodigoOsEletronica, DespesaGrava, Tipo WSSEND Ok WSSERVICE TSRVS001

Local nTamCod 	:= TamSX3("PF9_IDOS")[1]
Local nTamSeq 	:= TamSX3("PFM_SEQID")[1]
Local aDespesas 	:= {}
Local nX			:= 1
Local cIdOS		:= ""
Local lRet			:= .F.
Local cMsg			:= ""
Local nOpcX		:= 3  // (3=Incluir 4=alterar 5=Excluir)
Local cSeq			:= ""
U_xCONOUT("TSRVS001","PUTDESPESARDA","REQUISICAO","")

cIdOS := Substr(PadR(Alltrim(::CodigoOsEletronica),nTamCod),1,nTamCod)

If ::Tipo == "I"
	nOpcX := 3 // Incluir
ElseIf ::Tipo == "A"
	nOpcX := 4 // Alterar
ElseIf ::Tipo == "E"
	nOpcX := 5 // Excluir
Endif

For nX := 1 To Len(::DespesaGrava:ListaDepesas)

	cSeq := StrZero(Val(::DespesaGrava:ListaDepesas[nX]:Sequencia),nTamSeq)

	AAdd( aDespesas , { ;
						cSeq												,;	//³ 1 - ID Seq 
						::DespesaGrava:ListaDepesas[nX]:Despesa 		,;	//³ 2 - Codigo da Despesa
						::DespesaGrava:ListaDepesas[nX]:Quantidade 	,;	//³ 3 - quantidade
						::DespesaGrava:ListaDepesas[nX]:ValorUnit 	,;	//³ 4 - Vl. unitario
						::DespesaGrava:ListaDepesas[nX]:ValorTotal 	;	//³ 5 - Valor Total 
						 } )
						
Next nX 
						 
aRet := U_TSRVA110(nOpcX,cIDOS,aDespesas)

lRet  := aRet[1]
cMsg  := aRet[2]
::Ok  := lRet

If ! lRet
	SetSoapFault( "Despesa RDA", cMsg )
Endif
U_xCONOUT("TSRVS001","PUTDESPESARDA","RETORNO","")	
Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} RetAgendaPeriodo (TSRVS001)
Metodo para retornar dados da agenda do tecnico no periodo 
<AUTHOR>
@since 17/09/2015
@param CodigoOsEletronica, caracter, IDOS 
@param Tecnico, codigo do tecnico
@param DataDe, data, data inicio do periodo
@param DataAte, data, data fim do periodo
@param Cliente, caracter, codigo do cliente
@param ProjetoCFP, caracter, codigo do projeto
@return DadosAgendaTecnico, estrutura, dados da agenda do tecnico
/*/
//-------------------------------------------------------------------

WSMETHOD RetAgendaPeriodo WSRECEIVE Tecnico, DataDe, DataAte, Cliente, Loja, ProjetoCFP WSSEND DadosAgendaTecnico WSSERVICE TSRVS001

Local nTamTec	:= TamSX3("RD0_CODIGO")[1]             
Local nTamCli	:= TamSX3("A1_COD")[1]             
Local nTamLoj	:= TamSX3("A1_LOJA")[1]             
Local nTamProj	:= TamSX3("PE5_PROJET")[1]             
Local cTecnico	:= "" 
Local cCliente	:= ""
Local cLoja		:= ""
Local dDataDe  	:= CtoD("")
Local dDataAte 	:= CtoD("")
Local cProjeto	:= ""
Local cAliTmp 	:= GetNextAlias()
Local cQryTmp 	:= ""
Local nIt			:= 0
Local nI			:= 0
Local oModel
Local oModelOS
Local oModelDesp
Local oModelApto
Local dDtFeriado	:= CtoD("")
Local cQuery		:= ""
Local cStartPath 	:= GetSrvProfString("Startpath","")
Local cEmpTec 		:= readValue('RD0',1, xfilial('RD0') + ::Tecnico, 'RD0_EMPATU' )
Local cFilTec 		:= readValue('RD0',1, xfilial('RD0') + ::Tecnico, 'RD0_FILATU' )
Local cArqSP3       := ""

//-- para usar dentro da funcao E_MSMM do Model AFUDETAIL
Private INCLUI 	:= .F.

Default ::Tecnico	 := ""
Default ::DataDe		 := MsDate()
Default ::DataAte	 := MsDate()
Default ::Cliente	 := ""
Default ::Loja		 := ""
Default ::ProjetoCFP := ""

cTecnico 	:= Substr(PadR(Alltrim(::Tecnico),nTamTec),1,nTamTec)
cCliente	:= Substr(PadR(Alltrim(::Cliente),nTamCli),1,nTamCli)
cLoja		:= Substr(PadR(Alltrim(::Loja),nTamLoj),1,nTamLoj)
cProjeto	:= Substr(PadR(Alltrim(::ProjetoCFP),nTamProj),1,nTamProj)

dDataDe  	:= ::DataDe
dDataAte 	:= ::DataAte

::DadosAgendaTecnico := {}

U_xCONOUT("TSRVS001","RETAGENDAPERIODO","REQUISICAO","")

// Trocar as tabelas para a nova empresa e filial do tecnico
if ( ! empty(cFilTec) .and. len(cFilTec) == 11 .and. ! empty(cEmpTec) ) .and. alltrim(cEmpTec) <> alltrim(cEmpAnt) .and. alltrim(cFilTec) <> alltrim(cFilAnt)
	cArqSP3 := RetFullName("SP3", cEmpTec)
else
	cFilTec := cFilAnt
	cEmpTec := cEmpAnt
	cArqSP3 := RetFullName("SP3", cEmpAnt)
endif

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ Verifica feriados                                                        ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ

cQuery := " SELECT SP3.R_E_C_N_O_ AS RECSP3 "
cQuery += " FROM "+Alltrim(cArqSP3)+" SP3 "
cQuery += " WHERE SP3.D_E_L_E_T_ = '' "
cQuery += " AND P3_FILIAL = '"+cFilTec+"' "
cQuery += " AND ( "
cQuery += "        P3_DATA BETWEEN '" + DtoS(dDataDe) + "' AND '" + DtoS(dDataAte) + "' "
cQuery += "       OR "
cQuery += "       (P3_FIXO = 'S' AND SUBSTRING(P3_MESDIA,1,2) = '"+SubStr(DtoS(dDataAte),5,2)+"' ) "
cQuery += " ) "
cQuery := ChangeQuery(cQuery)
TCQUERY cQuery NEW ALIAS "TS103FER"
While !TS103FER->(EOF())
	SP3->(DbGoTo(TS103FER->RECSP3))
	
	dDtFeriado := IIf(SP3->P3_FIXO == "N", SP3->P3_DATA, StoD(SubStr(DtoS(dDataAte),1,4)+SP3->P3_MESDIA) )

	AAdd( ::DadosAgendaTecnico, WsClassNew("stAgendaTecnico") )
	
	nIt := Len(::DadosAgendaTecnico)

	::DadosAgendaTecnico[nIt]:IdOS				:= "FERIADO"	
	::DadosAgendaTecnico[nIt]:Status			:= "F"	
	::DadosAgendaTecnico[nIt]:Tecnico			:= cTecnico
	::DadosAgendaTecnico[nIt]:NomeTecnico		:= POSICIONE('RD0',1,XFILIAL('RD0')+cTecnico,'RD0_NOME')
	::DadosAgendaTecnico[nIt]:DataAgenda		:= dDtFeriado
	::DadosAgendaTecnico[nIt]:HoraInicio 		:= ""
	::DadosAgendaTecnico[nIt]:HoraFim 			:= ""
	::DadosAgendaTecnico[nIt]:HoraTraslado		:= ""
	::DadosAgendaTecnico[nIt]:HoraAlmoco 		:= ""
	::DadosAgendaTecnico[nIt]:HoraTotal 		:= ""
	::DadosAgendaTecnico[nIt]:Cliente 			:= ""
	::DadosAgendaTecnico[nIt]:Loja				:= ""
	::DadosAgendaTecnico[nIt]:NomeCliente		:= Alltrim(SP3->P3_DESC)
	::DadosAgendaTecnico[nIt]:ProjetoCFP		:= ""
	::DadosAgendaTecnico[nIt]:DescrProjetoCFP	:= ""
	::DadosAgendaTecnico[nIt]:FrenteCFP 		:= ""
	::DadosAgendaTecnico[nIt]:DescrFrenteCFP	:= ""
	::DadosAgendaTecnico[nIt]:MotivoCFP		:= ""
	::DadosAgendaTecnico[nIt]:DescrMotivoCFP	:= ""
	::DadosAgendaTecnico[nIt]:CodLocal			:= ""
	::DadosAgendaTecnico[nIt]:DescrLocal		:= ""
	::DadosAgendaTecnico[nIt]:Observacao		:= ""
	::DadosAgendaTecnico[nIt]:Modulo			:= ""
	::DadosAgendaTecnico[nIt]:CodProduto		:= ""
	::DadosAgendaTecnico[nIt]:VlHoraAg			:= 0
	::DadosAgendaTecnico[nIt]:AlteraTraslado	:= ""
	::DadosAgendaTecnico[nIt]:Despesas			:= {}
	::DadosAgendaTecnico[nIt]:Apontamentos		:= {} 

	TS103FER->(DbSkip())
EndDo
TS103FER->(DbCloseArea())

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ Verifica agenda / OS                                                     ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
cQryTmp := " SELECT PF9_IDOS, PF9_STATUS "
cQryTmp += "   FROM " + RetSQLName("PF9") + " PF9 "
cQryTmp += "  WHERE PF9_FILIAL = '" + xFilial("PF9") + "' "
cQryTmp += "    AND PF9_DATA >= '" + DtoS(dDataDe) + "' "
cQryTmp += "    AND PF9_DATA <= '" + DtoS(dDataAte) + "' "
If ! Empty(cTecnico)
	cQryTmp += " AND PF9_CODTEC = '" + cTecnico + "' "
Endif
If ! Empty(cCliente)
	cQryTmp += " AND PF9_CLIENT = '" + cCliente + "' "
	cQryTmp += " AND PF9_LOJA = '" + cLoja + "' "
Endif
If ! Empty(cProjeto)
	cQryTmp += " AND PF9_PROJET = '" + cProjeto + "' "
Endif
cQryTmp += "    AND PF9.D_E_L_E_T_ = ' ' "

cQryTmp := ChangeQuery(cQryTmp)

dbUseArea(.T., "TOPCONN", TcGenQry(,,cQryTmp), cAliTmp, .F., .T.)

While ! (cAliTmp)->(EOF())

	dbSelectArea("PF9")
	dbSetOrder(1)
	dbSeek(xFilial("PF9")+(cAliTmp)->PF9_IDOS)
	
	If PF9->PF9_STATUS == "3" // O.S. Confirmada

		//-- carrega o model da OS em modo de visualizacao
		oModel := FwLoadModel("TSRVA105")
		oModel:SetOperation(1)
		oModel:Activate()
		
		//-- carrega o model da OS
		oModelOS := oModel:GetModel("PF9MASTER")
		
		AAdd( ::DadosAgendaTecnico, WsClassNew("stAgendaTecnico") )
		
		nIt := Len(::DadosAgendaTecnico)

		::DadosAgendaTecnico[nIt]:IdOS				:= oModelOS:GetValue("PF9_IDOS")	
		::DadosAgendaTecnico[nIt]:Status			:= oModelOS:GetValue("PF9_STATUS")	
		::DadosAgendaTecnico[nIt]:Tecnico			:= oModelOS:GetValue("PF9_CODTEC")
		::DadosAgendaTecnico[nIt]:NomeTecnico		:= oModelOS:GetValue("PF9_NOMTEC")
		::DadosAgendaTecnico[nIt]:DataAgenda		:= oModelOS:GetValue("PF9_DATA")
		::DadosAgendaTecnico[nIt]:HoraInicio 		:= oModelOS:GetValue("PF9_HRINI")
		::DadosAgendaTecnico[nIt]:HoraFim 			:= oModelOS:GetValue("PF9_HRFIM")
		::DadosAgendaTecnico[nIt]:HoraTraslado 	    := oModelOS:GetValue("PF9_HRTRA")
		::DadosAgendaTecnico[nIt]:HoraAlmoco 		:= oModelOS:GetValue("PF9_HRABN")
		::DadosAgendaTecnico[nIt]:HoraTotal 		:= oModelOS:GetValue("PF9_HRTOT")
		::DadosAgendaTecnico[nIt]:Cliente 			:= oModelOS:GetValue("PF9_CLIENT")
		::DadosAgendaTecnico[nIt]:Loja				:= oModelOS:GetValue("PF9_LOJA")
		::DadosAgendaTecnico[nIt]:NomeCliente		:= oModelOS:GetValue("PF9_NOMCLI")
		::DadosAgendaTecnico[nIt]:ProjetoCFP		:= oModelOS:GetValue("PF9_PROJET")
		::DadosAgendaTecnico[nIt]:DescrProjetoCFP	:= TRATACARAC(oModelOS:GetValue("PF9_DESPRO"))
		::DadosAgendaTecnico[nIt]:FrenteCFP 		:= oModelOS:GetValue("PF9_CODFRT")
		::DadosAgendaTecnico[nIt]:DescrFrenteCFP	:= TRATACARAC(Posicione("PE8",1,xFilial("PE8")+oModelOS:GetValue("PF9_PROJET")+oModelOS:GetValue("PF9_CODFRT"),"PE8_DESCR"))
		::DadosAgendaTecnico[nIt]:MotivoCFP		    := oModelOS:GetValue("PF9_CODMOT")
		::DadosAgendaTecnico[nIt]:DescrMotivoCFP	:= TRATACARAC(Posicione("PE0",1,xFilial("PE0")+oModelOS:GetValue("PF9_CODMOT"),"PE0_DESC"))
		::DadosAgendaTecnico[nIt]:CodLocal			:= oModelOS:GetValue("PF9_LOCATE")
		::DadosAgendaTecnico[nIt]:DescrLocal		:= TRATACARAC(oModelOS:GetValue("PF9_END"))
		::DadosAgendaTecnico[nIt]:Observacao		:= TRATACARAC(oModelOS:GetValue("PF9_ATIVID"))
		::DadosAgendaTecnico[nIt]:Modulo			:= oModelOS:GetValue("PF9_MODULO")
		::DadosAgendaTecnico[nIt]:CodProduto		:= oModelOS:GetValue("PF9_PRODUT")
		::DadosAgendaTecnico[nIt]:VlHoraAg			:= oModelOS:GetValue("PF9_VLHORA")
		::DadosAgendaTecnico[nIt]:AlteraTraslado	:= IIf(oModelOS:GetValue("PF9_PERMHT"),"S","N")
		::DadosAgendaTecnico[nIt]:Despesas			:= {}
		::DadosAgendaTecnico[nIt]:Apontamentos		:= {} 

		//-- carrega o model das despesas
		oModelDesp := oModel:GetModel("PFMDETAIL")

		//-- carrega os dados na estrutura
		For nI:= 1 To oModelDesp:Length()
		
			oModelDesp:GoLine(nI)
		                          
			AAdd( ::DadosAgendaTecnico[nIt]:Despesas, WsClassNew( "stDespesaRda" ) )
			
			::DadosAgendaTecnico[nIt]:Despesas[nI]:Sequencia		:= oModelDesp:GetValue("PFM_SEQID")
			::DadosAgendaTecnico[nIt]:Despesas[nI]:Despesa		:= oModelDesp:GetValue("PFM_CODDES")
			::DadosAgendaTecnico[nIt]:Despesas[nI]:Descricao		:= oModelDesp:GetValue("PFM_DESCR")
			::DadosAgendaTecnico[nIt]:Despesas[nI]:Quantidade	:= oModelDesp:GetValue("PFM_QUANT")
			::DadosAgendaTecnico[nIt]:Despesas[nI]:ValorUnit		:= oModelDesp:GetValue("PFM_VLUNIT")
			::DadosAgendaTecnico[nIt]:Despesas[nI]:ValorTotal	:= oModelDesp:GetValue("PFM_VLDESP")
			::DadosAgendaTecnico[nIt]:Despesas[nI]:ValorReemb	:= oModelDesp:GetValue("PFM_VLREEM")
			
		Next nI

		//-- carrega o model dos apontamentos
		oModelApto := oModel:GetModel("AFUDETAIL")

		//-- carrega os dados na estrutura
		For nI:= 1 To oModelApto:Length()
		
			oModelApto:GoLine(nI)
		                          
			AAdd( ::DadosAgendaTecnico[nIt]:Apontamentos, WsClassNew( "stTaskSO" ) )

			::DadosAgendaTecnico[nIt]:Apontamentos[nI]:TaskCode			:= oModelApto:GetValue("TAREFA")
			::DadosAgendaTecnico[nIt]:Apontamentos[nI]:TaskName			:= oModelApto:GetValue("NMTAR")
			::DadosAgendaTecnico[nIt]:Apontamentos[nI]:TaskStartTime	:= oModelApto:GetValue("HORAI")
			::DadosAgendaTecnico[nIt]:Apontamentos[nI]:TaskEndTime		:= oModelApto:GetValue("HORAF")
			::DadosAgendaTecnico[nIt]:Apontamentos[nI]:TaskQtdHour		:= oModelApto:GetValue("HQUANT")
			::DadosAgendaTecnico[nIt]:Apontamentos[nI]:TaskNotes			:= TRATACARAC(oModelApto:GetValue("OBS"))
			::DadosAgendaTecnico[nIt]:Apontamentos[nI]:TaskProgress		:= oModelApto:GetValue("PFISI") 

		Next nI

		oModel:DeActivate()

	Else // Agenda

		//-- carrega o model da Agenda em modo de visualizacao
		oModel := FwLoadModel("TSRVA104")
		oModel:SetOperation(1)
		oModel:Activate()

		//-- carrega o model da OS
		oModelOS := oModel:GetModel("PF9MASTER")

		AAdd( ::DadosAgendaTecnico, WsClassNew("stAgendaTecnico") )
		
		nIt := Len(::DadosAgendaTecnico)

		::DadosAgendaTecnico[nIt]:IdOS				:= oModelOS:GetValue("PF9_IDOS")	
		::DadosAgendaTecnico[nIt]:Status			:= oModelOS:GetValue("PF9_STATUS")	
		::DadosAgendaTecnico[nIt]:Tecnico			:= oModelOS:GetValue("PF9_CODTEC")
		::DadosAgendaTecnico[nIt]:NomeTecnico		:= oModelOS:GetValue("PF9_NOMTEC")
		::DadosAgendaTecnico[nIt]:DataAgenda		:= oModelOS:GetValue("PF9_DATA")
		::DadosAgendaTecnico[nIt]:HoraInicio 		:= oModelOS:GetValue("PF9_HRINI")
		::DadosAgendaTecnico[nIt]:HoraFim 			:= oModelOS:GetValue("PF9_HRFIM")
		::DadosAgendaTecnico[nIt]:HoraTraslado 	:= oModelOS:GetValue("PF9_HRTRA")
		::DadosAgendaTecnico[nIt]:HoraAlmoco 		:= oModelOS:GetValue("PF9_HRABN")
		::DadosAgendaTecnico[nIt]:HoraTotal 		:= oModelOS:GetValue("PF9_HRTOT")
		::DadosAgendaTecnico[nIt]:Cliente 			:= oModelOS:GetValue("PF9_CLIENT")
		::DadosAgendaTecnico[nIt]:Loja				:= oModelOS:GetValue("PF9_LOJA")
		::DadosAgendaTecnico[nIt]:NomeCliente		:= oModelOS:GetValue("PF9_NOMCLI")
		::DadosAgendaTecnico[nIt]:ProjetoCFP		:= oModelOS:GetValue("PF9_PROJET")
		::DadosAgendaTecnico[nIt]:DescrProjetoCFP	:= oModelOS:GetValue("PF9_DESPRO")
		::DadosAgendaTecnico[nIt]:FrenteCFP 		:= oModelOS:GetValue("PF9_CODFRT")
		::DadosAgendaTecnico[nIt]:DescrFrenteCFP	:= Posicione("PE8",1,xFilial("PE8")+oModelOS:GetValue("PF9_PROJET")+oModelOS:GetValue("PF9_CODFRT"),"PE8_DESCR")
		::DadosAgendaTecnico[nIt]:MotivoCFP		:= oModelOS:GetValue("PF9_CODMOT")
		::DadosAgendaTecnico[nIt]:DescrMotivoCFP	:= Posicione("PE0",1,xFilial("PE0")+oModelOS:GetValue("PF9_CODMOT"),"PE0_DESC")
		::DadosAgendaTecnico[nIt]:CodLocal			:= oModelOS:GetValue("PF9_LOCATE")
		::DadosAgendaTecnico[nIt]:DescrLocal		:= oModelOS:GetValue("PF9_END")
		::DadosAgendaTecnico[nIt]:Observacao		:= TRATACARAC(oModelOS:GetValue("PF9_OBSERV"))
		::DadosAgendaTecnico[nIt]:Modulo			:= oModelOS:GetValue("PF9_MODULO")
		::DadosAgendaTecnico[nIt]:CodProduto		:= oModelOS:GetValue("PF9_PRODUT")
		::DadosAgendaTecnico[nIt]:VlHoraAg			:= oModelOS:GetValue("PF9_VLHORA")
		::DadosAgendaTecnico[nIt]:AlteraTraslado	:= IIf(oModelOS:GetValue("PF9_PERMHT"),"S","N")
		::DadosAgendaTecnico[nIt]:Despesas			:= {}
		::DadosAgendaTecnico[nIt]:Apontamentos		:= {} 

		oModel:DeActivate()

	EndIf
	
	(cAliTmp)->(dbSkip())
	
Enddo

if len(self:DadosAgendaTecnico) == 0
	SetSoapFault( "RetAgendaPeriodo", "Sem informações no período." )
	(cAliTmp)->(dbCloseArea())
	U_xCONOUT("TSRVS001","RETAGENDAPERIODO","(SEM INFORMACOES NO PERIODO) - RETORNO","")
	return .F.
endif
	
(cAliTmp)->(dbCloseArea())
U_xCONOUT("TSRVS001","RETAGENDAPERIODO","RETORNO","")
Return .T.


//-------------------------------------------------------------------
/*/{Protheus.doc} TiposDespesa (TSRVS001)
Metodo para retornar array com os tipos de despesas 
<AUTHOR>
@since 17/09/2015
@param Tipo, caracter, tipo de despesa  
@return TiposDespesa, estrutura, dados da despesa (codigo, descricao)
/*/
//-------------------------------------------------------------------

WSMETHOD TiposDespesa WSRECEIVE Tipo WSSEND DespesaList WSSERVICE TSRVS001

Local nI
Local nTamCod	 := TamSX3("PFG_CODIGO")[1]        

Default ::Tipo := ""

::Tipo := Substr(PadR(Alltrim(::Tipo),nTamCod),1,nTamCod)

::DespesaList := {}
U_xCONOUT("TSRVS001","TIPOSDESPESA","REQUISICAO","")   

dbSelectArea("PFG")
dbSetOrder(1)

If Empty(::Tipo)

	dbSeek(xFilial("PFG"))

	If ! EOF()

		While ! EOF() .And. PFG->PFG_FILIAL == xFilial("PFG")
	
			AAdd( ::DespesaList, WsClassNew( "stTipoDespesa" ) )
			
			nI := Len(::DespesaList)
	
			::DespesaList[nI]:Codigo		:= PFG->PFG_CODIGO
			::DespesaList[nI]:Descricao	:= PFG->PFG_DESCR
			
			dbSkip()
			
		Enddo

	Else
	
		SetSoapFault( "Tipos Despesa", "Sem tipos de despesa para listar." )
		U_xCONOUT("TSRVS001","TIPOSDESPESA","(SEM TIPOS DE DESPESA PARA LISTAR) - RETORNO","")  
		Return .F.
			
	Endif

Else

	dbSeek(xFilial("PFG")+::Tipo)
	
	If ! EOF()
	
		AAdd( ::DespesaList, WsClassNew( "stTipoDespesa" ) )
		
		nI := Len(::DespesaList)

		::DespesaList[nI]:Codigo		:= PFG->PFG_CODIGO
		::DespesaList[nI]:Descricao	:= PFG->PFG_DESCR

	Else
	
		SetSoapFault( "Tipos Despesa", "Tipo de despesa nao encontrado." )
		U_xCONOUT("TSRVS001","TIPOSDESPESA","(TIPO DE DESPESA NAO ENCONTRADO) - RETORNO","")  
		Return .F.
			
	Endif

Endif
U_xCONOUT("TSRVS001","TIPOSDESPESA","RETORNO","")

Return .T.


//-------------------------------------------------------------------
/*/{Protheus.doc} RetProdFrente (TSRVS001)
Retorna lista de produtos para a frente de entrega 
<AUTHOR>
@since 17/09/2015
@param ProjetoCFP, caracter, codigo projeto
@param FrenteCFP, caracter, codigo da frente
@return TiposDespesa, estrutura, dados da despesa (codigo, descricao)
/*/
//-------------------------------------------------------------------

WSMETHOD RetProdFrente WSRECEIVE ProjetoCFP, FrenteCFP WSSEND ProdutosFrenteList WSSERVICE TSRVS001

Local nI
Local nTamPrj := TamSX3("PG8_PROJET")[1] 
Local nTamFrt := TamSX3("PG8_CODFRT")[1] 

//Default ::Tipo := ""

::ProjetoCFP := Substr(PadR(Alltrim(::ProjetoCFP),nTamPrj),1,nTamPrj)
::FrenteCFP  := Substr(PadR(Alltrim(::FrenteCFP),nTamFrt),1,nTamFrt)

::ProdutosFrenteList := {}
U_xCONOUT("TSRVS001","RETPRODFRENTE","REQUISICAO","")

dbSelectArea("PG8")
dbSetOrder(1)
dbSeek(xFilial("PG8")+::ProjetoCFP+::FrenteCFP)

If ! EOF()

	While ! EOF() .And. PG8->PG8_FILIAL == xFilial("PG8") .And. PG8->PG8_PROJET == ::ProjetoCFP .And. PG8->PG8_CODFRT == ::FrenteCFP

		AAdd( ::ProdutosFrenteList, WsClassNew( "stProdutosFrente" ) )
		
		nI := Len(::ProdutosFrenteList)

		::ProdutosFrenteList[nI]:Produto	:= PG8->PG8_PRODUT
		::ProdutosFrenteList[nI]:Descricao	:= Posicione("SB1",1,xFilial("SB1")+PG8->PG8_PRODUT,"B1_DESC")
		
		dbSkip()
		
	Enddo

Else

	SetSoapFault( "Produto Frente", "Nao ha produtos cadastrados para este frente de entrega." )
	U_xCONOUT("TSRVS001","RETPRODFRENTE","(NAO HA PRODUTOS CADASTRADOS PARA ESTE FRENTE DE ENTREGA) - RETORNO","")
	Return .F.
		
Endif
U_xCONOUT("TSRVS001","RETPRODFRENTE","RETORNO","")
Return .T.


//-------------------------------------------------------------------
/*/{Protheus.doc} OSPrint (TSRVS001)
Retorna dados para impressao OS
<AUTHOR>
@since 17/09/2015
@param IdOS, caracter, codigo da OS
@return OSPrintRet, estrutura, dados da OS
/*/
//-------------------------------------------------------------------

WSMETHOD OSPrint WSRECEIVE IdOS WSSEND OSPrintRet WSSERVICE TSRVS001

Local nTamCod	 	:= TamSX3("PF9_IDOS")[1]             
Local aAliasSM0 	:= SM0->(GetArea())
Local nI			:= 0
Local oModel
Local oModelOS
Local oModelDesp
Local oModelApto
Local cDescAFU		:= ''

//-- para usar dentro da funcao E_MSMM do Model AFUDETAIL
Private INCLUI 	:= .F.

::IdOS := Substr(PadR(Alltrim(::IdOS),nTamCod),1,nTamCod)
U_xCONOUT("TSRVS001","OSPRINT","REQUISICAO","")

//-- localiza a OS para ver se esta no status de OS confirmada
dbSelectArea("PF9")
dbSetOrder(1)
dbSeek(xFilial("PF9")+::IdOS)

If PF9->PF9_STATUS <> "3" // O.S. Confirmada
	SetSoapFault( "Impressao OS", "Esta não é uma OS confirmada." )
	U_xCONOUT("TSRVS001","OSPRINT","(ESTA NAO EH UMA OS CONFIRMADA) - RETORNO","")
	Return .F.
Endif

dbSelectArea("RD0")
dbSetOrder(1) 
dbSeek(xFilial("RD0")+PF9->PF9_CODTEC)
			
//-- posiciona no projeto da OS para buscar a que grupo / filial pertence
dbSelectArea("PE5")
dbSetOrder(1)
dbSeek(xFilial("PE5")+PF9->PF9_PROJET)

//-- posiciona na empresa de origem do projeto para buscar os dados
dbSelectArea("SM0")
dbSetOrder(1)
dbSeek(PE5->PE5_EMPPRJ+PE5->PE5_FILPRJ)

OSPrintRet:CorpName 		:= SM0->M0_NOMECOM
OSPrintRet:CorpAddress  	:= SM0->M0_ENDENT
OSPrintRet:CorpAddress2		:= SM0->M0_BAIRENT
OSPrintRet:CorpZipCode		:= SM0->M0_CEPENT
OSPrintRet:CorpCity			:= SM0->M0_CIDENT
OSPrintRet:CorpState		:= SM0->M0_ESTENT
OSPrintRet:CorpPhone		:= SM0->M0_TEL
OSPrintRet:CorpSite			:= "www.totvs.com"
OSPrintRet:Analyst 			:= PF9->PF9_CODTEC
OSPrintRet:AnalystName 		:= RD0->RD0_NOME
OSPrintRet:KeySO 			:= PF9->PF9_IDOS   

//-- carrega o model da OS em modo de visualizacao
oModel := FwLoadModel("TSRVA105")
oModel:SetOperation(1)
oModel:Activate()

//-- carrega o model da OS 
oModelOS := oModel:GetModel("PF9MASTER")
		
::OSPrintRet:ServiceOrder:IdOS				:= oModelOS:GetValue("PF9_IDOS")	
::OSPrintRet:ServiceOrder:Status			:= oModelOS:GetValue("PF9_STATUS")	
::OSPrintRet:ServiceOrder:Tecnico			:= oModelOS:GetValue("PF9_CODTEC")
::OSPrintRet:ServiceOrder:NomeTecnico		:= oModelOS:GetValue("PF9_NOMTEC")
::OSPrintRet:ServiceOrder:DataAgenda		:= oModelOS:GetValue("PF9_DATA")
::OSPrintRet:ServiceOrder:HoraInicio 		:= oModelOS:GetValue("PF9_HRINI")
::OSPrintRet:ServiceOrder:HoraFim 			:= oModelOS:GetValue("PF9_HRFIM")
::OSPrintRet:ServiceOrder:HoraTraslado 	:= oModelOS:GetValue("PF9_HRTRA")
::OSPrintRet:ServiceOrder:HoraAlmoco 		:= oModelOS:GetValue("PF9_HRABN")
::OSPrintRet:ServiceOrder:HoraTotal 		:= oModelOS:GetValue("PF9_HRTOT")
::OSPrintRet:ServiceOrder:Cliente 			:= oModelOS:GetValue("PF9_CLIENT")
::OSPrintRet:ServiceOrder:Loja				:= oModelOS:GetValue("PF9_LOJA")
::OSPrintRet:ServiceOrder:NomeCliente		:= oModelOS:GetValue("PF9_NOMCLI")
::OSPrintRet:ServiceOrder:ProjetoCFP		:= oModelOS:GetValue("PF9_PROJET")
::OSPrintRet:ServiceOrder:DescrProjetoCFP	:= oModelOS:GetValue("PF9_DESPRO")
::OSPrintRet:ServiceOrder:FrenteCFP 		:= oModelOS:GetValue("PF9_CODFRT")
::OSPrintRet:ServiceOrder:DescrFrenteCFP	:= Posicione("PE8",1,xFilial("PE8")+oModelOS:GetValue("PF9_PROJET")+oModelOS:GetValue("PF9_CODFRT"),"PE8_DESCR")
::OSPrintRet:ServiceOrder:MotivoCFP		:= oModelOS:GetValue("PF9_CODMOT")
::OSPrintRet:ServiceOrder:DescrMotivoCFP	:= Posicione("PE0",1,xFilial("PE0")+oModelOS:GetValue("PF9_CODMOT"),"PE0_DESC")
::OSPrintRet:ServiceOrder:CodLocal			:= oModelOS:GetValue("PF9_LOCATE")
::OSPrintRet:ServiceOrder:DescrLocal		:= oModelOS:GetValue("PF9_END")
::OSPrintRet:ServiceOrder:Observacao		:= TRATACARAC(oModelOS:GetValue("PF9_ATIVID"))
::OSPrintRet:ServiceOrder:Modulo			:= oModelOS:GetValue("PF9_MODULO")
::OSPrintRet:ServiceOrder:CodProduto		:= oModelOS:GetValue("PF9_PRODUT")
::OSPrintRet:ServiceOrder:VlHoraAg			:= oModelOS:GetValue("PF9_VLHORA")
::OSPrintRet:ServiceOrder:AlteraTraslado	:= IIf(oModelOS:GetValue("PF9_PERMHT"),"S","N")
::OSPrintRet:ServiceOrder:Despesas			:= {}
::OSPrintRet:ServiceOrder:Apontamentos		:= {} 

//-- carrega o model das despesas
oModelDesp := oModel:GetModel("PFMDETAIL")

//-- carrega os dados na estrutura
For nI:= 1 To oModelDesp:Length()

	oModelDesp:GoLine(nI)
                          
	AAdd( ::OSPrintRet:ServiceOrder:Despesas, WsClassNew( "stDespesaRda" ) )
	
	::OSPrintRet:ServiceOrder:Despesas[nI]:Sequencia		:= oModelDesp:GetValue("PFM_SEQID")
	::OSPrintRet:ServiceOrder:Despesas[nI]:Despesa		:= oModelDesp:GetValue("PFM_CODDES")
	::OSPrintRet:ServiceOrder:Despesas[nI]:Descricao		:= oModelDesp:GetValue("PFM_DESCR")
	::OSPrintRet:ServiceOrder:Despesas[nI]:Quantidade	:= oModelDesp:GetValue("PFM_QUANT")
	::OSPrintRet:ServiceOrder:Despesas[nI]:ValorUnit		:= oModelDesp:GetValue("PFM_VLUNIT")
	::OSPrintRet:ServiceOrder:Despesas[nI]:ValorTotal	:= oModelDesp:GetValue("PFM_VLDESP")
	::OSPrintRet:ServiceOrder:Despesas[nI]:ValorReemb	:= oModelDesp:GetValue("PFM_VLREEM")
	
Next nI

//-- carrega o model dos apontamentos
oModelApto := oModel:GetModel("AFUDETAIL")

//-- carrega os dados na estrutura
For nI:= 1 To oModelApto:Length()

	oModelApto:GoLine(nI)
                          
	AAdd( ::OSPrintRet:ServiceOrder:Apontamentos, WsClassNew( "stTaskSO" ) )

	::OSPrintRet:ServiceOrder:Apontamentos[nI]:TaskCode			:= oModelApto:GetValue("TAREFA")
	::OSPrintRet:ServiceOrder:Apontamentos[nI]:TaskName			:= oModelApto:GetValue("NMTAR")
	::OSPrintRet:ServiceOrder:Apontamentos[nI]:TaskStartTime	:= oModelApto:GetValue("HORAI")
	::OSPrintRet:ServiceOrder:Apontamentos[nI]:TaskEndTime		:= oModelApto:GetValue("HORAF")
	::OSPrintRet:ServiceOrder:Apontamentos[nI]:TaskQtdHour		:= oModelApto:GetValue("HQUANT")
	::OSPrintRet:ServiceOrder:Apontamentos[nI]:TaskNotes			:= TRATACARAC(oModelApto:GetValue("OBS"))
	::OSPrintRet:ServiceOrder:Apontamentos[nI]:TaskProgress		:= oModelApto:GetValue("PFISI")
	
	if nI == 1
		cDescAFU += 'Projeto...: ' + oModelOS:GetValue("PF9_PROJET")	+	' - ' + alltrim(oModelOS:GetValue("PF9_DESPRO"))	+ CRLF
		cDescAFU += 'Tarefa....: ' + oModelApto:GetValue("TAREFA")		+	' - ' + alltrim(oModelApto:GetValue("NMTAR"))		+ CRLF
		cDescAFU += 'Obs.......: ' + alltrim(TRATACARAC(oModelApto:GetValue("OBS"))) + CRLF + CRLF
	else
		cDescAFU += 'Tarefa....: ' + oModelApto:GetValue("TAREFA")		+	' - ' + alltrim(oModelApto:GetValue("NMTAR"))		+ CRLF + CRLF
		cDescAFU += 'Obs.......: ' + alltrim(TRATACARAC(oModelApto:GetValue("OBS"))) + CRLF + CRLF
	endif

Next nI

if !empty(cDescAFU)
	::OSPrintRet:ServiceOrder:Observacao := cDescAFU + CRLF + CRLF + alltrim(TRATACARAC(::OSPrintRet:ServiceOrder:Observacao))
endif

oModel:DeActivate()
U_xCONOUT("TSRVS001","OSPRINT","RETORNO","")
Return .T. 


//-------------------------------------------------------------------
/*/{Protheus.doc} ImprimeAgenda (TSRVS001)
Retorna dados para impressao do extrato da agenda do tecnico
<AUTHOR>
@since 17/09/2015
@param Tecnico, caracter, codigo do tecnico
@param DataDe, data, data de inicio do periodo
@param DataAte, data, data de fim do periodo
@param Detalhe, caracter, se imprime o detalhe (S/N)
@return ExtratoImprime, estrutura, dados da agenda para imprimir
/*/
//-------------------------------------------------------------------
*/

WSMETHOD ImprimeAgenda WSRECEIVE Cliente,Tecnico, DataDe, DataAte, Detalhe WSSEND ExtratoImprime WSSERVICE TSRVS001

Local cAliTmp 	:= GetNextAlias()
Local cQryTmp 	:= ""
Local nTamTec		:= TamSX3("RD0_CODIGO")[1]             
Local cTecnico	:= ""
Local cMsg     	:= ""
Local lRet      	:= .T.
Local aClientes	:= {}
Local nTotOutros	:= 0
Local nTotHoras 	:= 0
Local nTotTras	:= 0
Local cStatus    	:= ""
Local cObs   		:= ""
Local nX

Default ::Cliente	:= ""
Default ::Tecnico	:= ""
Default ::DataDe		:= MsDate()
Default ::DataAte	:= MsDate()
Default ::Detalhe	:= "S"

//-- ajusta tamanho dos campos
cTecnico := Substr(PadR(Alltrim(::Tecnico),nTamTec),1,nTamTec)

U_xCONOUT("TSRVS001","IMPRIMEAGENDA","REQUISICAO","")

dbSelectArea("RD0")
dbSetOrder(1)
dbSeek(xFilial("RD0")+cTecnico)

If Eof()
	cMsg := "Tecnico nao encontrado."
	lRet := .F.
ElseIf Empty(::DataDe)
	cMsg := "Data inicial invalida."
	lRet := .F.
ElseIf Empty(::DataAte)
	cMsg := "Data final invalida."
	lRet := .F.
ElseIf ::DataDe > ::DataAte
	cMsg := "Periodo invalido."
	lRet := .F.
ElseIf ! ( ::Detalhe $ "SN" )
	cMsg := "Imprime detalhe invalido (S=Sim, N=Nao)."
	lRet := .F.
Endif

If lRet

	cQryTmp := " SELECT PF9_IDOS, PFD_DATRAS, " 
	cQryTmp += "	      ((CASE WHEN PF9_DTPREM = ' ' THEN CAST(PF9_DATA AS DATE) ELSE CAST(PF9_DTPREM AS DATE) END) - CAST(PF9_DATA AS DATE)) DIAS, "
	cQryTmp += "        CASE WHEN ((CASE WHEN PF9_DTPREM = ' ' THEN CAST(PF9_DATA AS DATE) ELSE CAST(PF9_DTPREM AS DATE) END) - CAST(PF9_DATA AS DATE)) > CASE WHEN PFD_DATRAS IS NULL THEN 0 ELSE CAST(PFD_DATRAS AS INT) END THEN 'S' ELSE 'N' END ATRASO "
	cQryTmp += "   FROM " + RetSQLName("PF9") + " PF9 "
	cQryTmp += "  INNER JOIN " + RetSQLName("RD0") + " RD0 " 
	cQryTmp += "     ON RD0_FILIAL = '" + xFilial("RD0") + "' "
	cQryTmp += "    AND RD0_CODIGO = PF9_CODTEC " 
	cQryTmp += "   LEFT JOIN " + RetSQLName("PFD") + " PFD "
	cQryTmp += "     ON PFD_FILIAL = '" + xFilial("PFD") + "' "
	cQryTmp += "    AND PFD_ANOMES = SUBSTR(PF9_DATA,1,6) " 
	cQryTmp += "    AND PFD_UNSRV = RD0_XUNSRV " 
	cQryTmp += "    AND PFD_CARGO = RD0_XCARGO " 
	cQryTmp += "    AND PFD_EMPFIL = RD0_EMPATU || RD0_FILATU " 
	cQryTmp += "    AND PFD.D_E_L_E_T_ = ' ' "
	cQryTmp += "  WHERE PF9_FILIAL = '" + xFilial("PF9") + "' "
	cQryTmp += "    AND PF9_DATA >= '" + DtoS(::DataDe) + "' "
	cQryTmp += "    AND PF9_DATA <= '" + DtoS(::DataAte) + "' "
	cQryTmp += "    AND PF9_CODTEC = '" + cTecnico + "' "
	
	if !empty(::Cliente)
		cQryTmp += "    AND PF9_CLIENT = '" + ::Cliente + "' " 
	endif
	
	cQryTmp += "    AND PF9.D_E_L_E_T_ = ' ' "

	cQryTmp := ChangeQuery(cQryTmp)
	
	dbUseArea(.T., "TOPCONN", TcGenQry(,,cQryTmp), cAliTmp, .F., .T.)
	
	If ! (cAliTmp)->(EOF())
	
		//-- preenche dados do cabecalho do extrato da agenda
		::ExtratoImprime:Cabecalho:Tecnico		:= cTecnico
		::ExtratoImprime:Cabecalho:NomeTecnico	:= RD0->RD0_NOME
		::ExtratoImprime:Cabecalho:Hora			:= Time()
		::ExtratoImprime:Cabecalho:DataAtu		:= MsDate()
		
		::ExtratoImprime:Agendas 				:= {}
	
		While ! (cAliTmp)->(EOF())
		
			dbSelectArea("PF9")
			dbSetOrder(1)
			dbSeek(xFilial("PF9")+(cAliTmp)->PF9_IDOS)
			
			If PF9->PF9_STATUS == "1"
				cStatus := "Agenda aguardando liberação"
			ElseIf PF9->PF9_STATUS == "2"
				cStatus := "Agenda confirmada"
			ElseIf PF9->PF9_STATUS == "3"
				cStatus := "O.S. confirmada"
			Else
				cStatus := PF9->PF9_STATUS 	
			Endif
			
			If PF9->PF9_STATUS == "3"
				If (cAliTmp)->ATRASO == "S"
					cObs := "Confirmada com ATRASO"
				Else
					cObs := "Confirmada no prazo"	
				Endif 
			Else
				cObs := PF9->PF9_OBSERV	
			Endif 

			//-- preenche dados do corpo do extrato da agenda
			AAdd( ::ExtratoImprime:Agendas, WsClassNew( "stCorpoExtrAge" ) )
			
			nX := Len(::ExtratoImprime:Agendas)
			
			::ExtratoImprime:Agendas[nX]:DataAgenda		:= PF9->PF9_DATA
			::ExtratoImprime:Agendas[nX]:Cliente			:= PF9->PF9_CLIENT
			::ExtratoImprime:Agendas[nX]:Loja				:= PF9->PF9_LOJA
			::ExtratoImprime:Agendas[nX]:NomeCliente		:= Posicione("SA1",1,xFilial("SA1")+PF9->PF9_CLIENT+PF9->PF9_LOJA,"A1_NOME")
			::ExtratoImprime:Agendas[nX]:Servico			:= PF9->PF9_PRODUT
			::ExtratoImprime:Agendas[nX]:HoraInicio		:= PF9->PF9_HRINI
			::ExtratoImprime:Agendas[nX]:HoraFim			:= PF9->PF9_HRFIM
			::ExtratoImprime:Agendas[nX]:HoraOutros		:= PF9->PF9_HRABN
			::ExtratoImprime:Agendas[nX]:HoraTraslado		:= PF9->PF9_HRTRA
			::ExtratoImprime:Agendas[nX]:HoraTotal			:= PF9->PF9_HRTOT
			::ExtratoImprime:Agendas[nX]:StatusAgenda		:= cStatus
			::ExtratoImprime:Agendas[nX]:Observacao		:= TRATACARAC(cObs)

			nTotOutros	:= nTotOutros + HoraToInt(PF9->PF9_HRABN)
			nTotHoras 	:= nTotHoras + HoraToInt(PF9->PF9_HRTOT)
			nTotTras	:= nTotTras + HoraToInt(PF9->PF9_HRTRA)
			
			If AScan(aClientes,PF9->PF9_CLIENT+PF9->PF9_LOJA) == 0
				AAdd(aClientes,PF9->PF9_CLIENT+PF9->PF9_LOJA)
			Endif			
		
			(cAliTmp)->(dbSkip()) 
			
		Enddo
		
		(cAliTmp)->(dbCloseArea()) 
		
		//-- preenche dados do total do extrato da agenda
		::ExtratoImprime:Total:TotalOutros		:= IntToHora(nTotOutros)
		::ExtratoImprime:Total:TotalTraslado	:= IntToHora(nTotTras)
		::ExtratoImprime:Total:TotalHoras		:= IntToHora(nTotHoras)
	
		//-- preenche dados adicionais do extrato da agenda
	
		::ExtratoImprime:DadosAdic := {}
		
		For nX:=1 To Len(aClientes)
		
			dbSelectArea("SA1")
			dbSetOrder(1)
			dbSeek(xFilial("SA1")+aClientes[nX])
	
			AAdd( ::ExtratoImprime:DadosAdic, WsClassNew( "stAdicExtrAge" ) )
			
			::ExtratoImprime:DadosAdic[nX]:Cliente				:= SA1->A1_COD
			::ExtratoImprime:DadosAdic[nX]:Loja				:= SA1->A1_LOJA
			::ExtratoImprime:DadosAdic[nX]:NomeCliente		:= SA1->A1_NOME
			::ExtratoImprime:DadosAdic[nX]:NomeFantasia		:= SA1->A1_NREDUZ
			::ExtratoImprime:DadosAdic[nX]:Endereco		 	:= SA1->A1_END
			::ExtratoImprime:DadosAdic[nX]:Bairro			 	:= SA1->A1_BAIRRO
			::ExtratoImprime:DadosAdic[nX]:Municipio		 	:= SA1->A1_MUN
			::ExtratoImprime:DadosAdic[nX]:Estado			 	:= SA1->A1_ESTADO
			::ExtratoImprime:DadosAdic[nX]:Telefone		 	:= SA1->A1_TEL
			::ExtratoImprime:DadosAdic[nX]:Contato			 	:= SA1->A1_CONTATO
			::ExtratoImprime:DadosAdic[nX]:EMail			 	:= SA1->A1_EMAIL
	
		Next nX	
		
		cMsg := ""
		lRet := .T.
	
	Else
	
		cMsg := "Não a dados para imprimir neste periodo para o tecnico."
		lRet := .F.
	
	Endif	

Endif

If ! lRet
	SetSoapFault( "Impressao Agenda", cMsg )
Endif	
U_xCONOUT("TSRVS001","IMPRIMEAGENDA","RETORNO","")	
Return(lRet)


//-------------------------------------------------------------------
/*/{Protheus.doc} GetListaEDTPMS (TSRVS001)
Retorna lista de EDT's do projeto PMS
<AUTHOR>
@since 21/09/2015
@param Tecnico, caracter, codigo do tecnico      
@param DataDe, data, data inicial do periodo
@param DataAte, data, data final do periodo
@param Tipo, caracter, se testa bloqueio no periodo (S) ou (N)
@return FechaRdaRet, estrutura, dados do retorno (se fechou, mensagem, se tem pergunta - bloqueio)  
/*/
//-------------------------------------------------------------------

WSMETHOD GetListaEDTPMS WSRECEIVE ProjetoPMS, EDTPai, Tecnico WSSEND dtListaEdtTarPMS WSSERVICE TSRVS001

Local lRet  		:= .T.
Local cProjeto 	:= ::ProjetoPMS
Local cEDTPai		:= ::EDTPai 
Local cTecnico	:= ::Tecnico 
Local nPosIte  	:= 0
Local aEdts       := {}
Local nX          := 0
Local nProgresso  := 0 

//-- chama funcao para retonar as EDT's
aEdts := u_RetEdtPms(cProjeto, cEdtPai, cTecnico)
	
::dtListaEdtTarPMS := {}

U_xCONOUT("TSRVS001","GETLISTAEDTPMS","REQUISICAO","")

For nX:=1 To Len(aEdts)	

	dbSelectArea("AFC")
	dbSetOrder(1)
	dbSeek(xFilial("AFC")+aEdts[nX,1]+aEdts[nX,2]+aEdts[nX,3])
	
	nProgresso := PMSPOCAFC(AFC->AFC_PROJET,AFC->AFC_REVISA,AFC->AFC_EDT,dDataBase)
		
	AAdd(::dtListaEdtTarPMS, WsClassNew("stEDTTarPMS"))
	
	nPosIte := Len(::dtListaEdtTarPMS)
	
	::dtListaEdtTarPMS[nPosIte]:Projeto 			:= aEdts[nX,1]
	::dtListaEdtTarPMS[nPosIte]:Revisao			:= aEdts[nX,2]		
	::dtListaEdtTarPMS[nPosIte]:Tarefa 			:= aEdts[nX,3]
	::dtListaEdtTarPMS[nPosIte]:Nivel				:= aEdts[nX,4]		
	::dtListaEdtTarPMS[nPosIte]:Descricao 			:= aEdts[nX,5]
	::dtListaEdtTarPMS[nPosIte]:HorasDuracao		:= aEdts[nX,6]	
	::dtListaEdtTarPMS[nPosIte]:DataInicio			:= aEdts[nX,7] 
	::dtListaEdtTarPMS[nPosIte]:DataFim			:= aEdts[nX,8]
	::dtListaEdtTarPMS[nPosIte]:EDTPai				:= aEdts[nX,9]		
	::dtListaEdtTarPMS[nPosIte]:EDTTarefa			:= aEdts[nX,10]
	::dtListaEdtTarPMS[nPosIte]:EhTraslado			:= "N"
	::dtListaEdtTarPMS[nPosIte]:ProgressoFisico	:= nProgresso
		
Next nX

If Empty(::dtListaEdtTarPMS)
	lRet := .F.
	SetSoapFault("Projetos PMS","Não foram encontrados EDT's para listar.")
	U_xCONOUT("TSRVS001","GETLISTAEDTPMS","(NAO FORAM ENCONTRADOS EDT's PARA LISTAR) - RETORNO","S")
Endif	
U_xCONOUT("TSRVS001","GETLISTAEDTPMS","RETORNO","")
Return(lRet)


//-------------------------------------------------------------------
/*/{Protheus.doc} GetListaTarEdtPMS (TSRVS001)
Retorna lista de tarefas da EDT do projeto PMS
<AUTHOR>
@since 21/09/2015 
@param ProjetoPMS, caracter, codigo projeto PMS
@param EDTPai, caracter, EDT pai PMS
@param Tecnico, tecnico
@return dtListaEdtTarPMS, estrutura, dados da tarefa (codigo descricao)  
/*/
//-------------------------------------------------------------------

WSMETHOD GetListaTarEdtPMS WSRECEIVE ProjetoPMS, EDTPai, Tecnico WSSEND dtListaEdtTarPMS WSSERVICE TSRVS001

Local lRet  		:= .T.
Local cProjeto 	:= ::ProjetoPMS 
Local cEDTPai		:= ::EDTPai 
Local cTecnico	:= ::Tecnico 
Local nPosIte  	:= 0
Local aTars     := {}
Local nX        := 0
Local nProgresso := 0

aTars := u_RetTarPms(cProjeto, cEdtPai, cTecnico)
	
//-- chama funcao para retonar as tarefas 
::dtListaEdtTarPMS := {}
U_xCONOUT("TSRVS001","GETLISTATAREDTPMS","REQUISICAO","")

For nX :=1 To Len(aTars)
	
	dbSelectArea("AF9")
	dbSetOrder(1)
	dbSeek(xFilial("AF9")+aTars[nX,1]+aTars[nX,2]+aTars[nX,3])
	
	nProgresso := PMSPOCAF9(AF9->AF9_PROJET,AF9->AF9_REVISA,AF9->AF9_TAREFA,dDataBase)
	
	AAdd(::dtListaEdtTarPMS, WsClassNew("stEdtTarPMS"))
		
	nPosIte := Len(::dtListaEdtTarPMS)
	
	::dtListaEdtTarPMS[nPosIte]:Projeto 			:= aTars[nX,1] 
	::dtListaEdtTarPMS[nPosIte]:Revisao			:= aTars[nX,2]		
	::dtListaEdtTarPMS[nPosIte]:Tarefa 			:= aTars[nX,3]
	::dtListaEdtTarPMS[nPosIte]:Nivel				:= aTars[nX,4]		
	::dtListaEdtTarPMS[nPosIte]:Descricao 			:= aTars[nX,5]
	::dtListaEdtTarPMS[nPosIte]:HorasDuracao		:= aTars[nX,6]	
	::dtListaEdtTarPMS[nPosIte]:DataInicio			:= aTars[nX,7] 
	::dtListaEdtTarPMS[nPosIte]:DataFim			:= aTars[nX,8]
	::dtListaEdtTarPMS[nPosIte]:EDTPai				:= aTars[nX,9]		
	::dtListaEdtTarPMS[nPosIte]:EDTTarefa			:= aTars[nX,10]
	::dtListaEdtTarPMS[nPosIte]:EhTraslado			:= IIf(aTars[nX,11],"S","N") // S-sim e tarefa de traslado , N-nao e tarefa de traslado
	::dtListaEdtTarPMS[nPosIte]:ProgressoFisico 	:= nProgresso

Next nX

If Empty(::dtListaEdtTarPMS)
	lRet := .F.
	SetSoapFault("EDTs PMS","Nao foram encontrados tarefas para listar.")
	U_xCONOUT("TSRVS001","GETLISTATAREDTPMS","(NAO FORAM ENCONTRADOS TAREFAS PARA LISTAR)","S")
Endif	
U_xCONOUT("TSRVS001","GETLISTATAREDTPMS","RETORNO","")
Return(lRet)


//-------------------------------------------------------------------
/*/{Protheus.doc} GetListaTarEdtPMS (TSRVS001)
Metodo para retornar dados para impressao do novo RDA
<AUTHOR>
@since 22/09/2015 
@param Tecnico, caracter, codigo do tecnico
@param DataDe, data, data inicial do periodo
@param DataAte, data, data final do periodo
@return NewRDAImprime, estrutura, dados para impressao do RDA   
/*/
//-------------------------------------------------------------------

WSMETHOD ImprimeNewRDA WSRECEIVE Tecnico, DataDe, DataAte WSSEND NewRDAImprime WSSERVICE TSRVS001

Local nTamTec		:= TamSX3("RD0_CODIGO")[1]             
Local lRet    	:= .T.
Local cMsg     	:= ""
Local nX			:= 0
Local cStatus		:= ""
Local cDescrStat	:= ""
Local oModelRDA
Local oModelCab  
Local oModelDesp
Local oModelOSEx
Local oModelDeb

//-- ajusta tamanho dos campos
::Tecnico := Substr(PadR(Alltrim(::Tecnico),nTamTec),1,nTamTec)
U_xCONOUT("TSRVS001","IMPRIMENEWRDA","REQUISICAO","")

dbSelectArea("RD0")
dbSetOrder(1)
dbSeek(xFilial("RD0")+::Tecnico)

If RD0->(Eof())
	cMsg := "Tecnico nao encontrado."
	lRet := .F.
ElseIf Empty(::DataDe)
	cMsg := "Data inicial invalida."
	lRet := .F.
ElseIf Empty(::DataAte)
	cMsg := "Data final invalida."
	lRet := .F.
ElseIf ::DataDe > ::DataAte
	cMsg := "Periodo invalido."
	lRet := .F.
Else
	dbSelectArea("PFO")
	dbSetOrder(3)
	dbSeek(xFilial("PFO")+::Tecnico+DtoS(::DataDe))
	If PFO->(Eof())
		cMsg := "Fechamento de RDA não encontrado para este técnico neste período."
		lRet := .F.
	Endif	
Endif

If lRet	

	//-- carrega o model do fechamento do RDA
	oModelRDA := FwLoadModel("TSRVA154")
	oModelRDA:SetOperation(1)
	oModelRDA:Activate()

	//-- carrega o model do cabecalho do fechamento 
	oModelCab := oModelRDA:GetModel("PFOMASTER")
	
	cStatus := oModelCab:GetValue("PFO_STATUS")
	
	If cStatus == "0"
		cDescrStat := "Aberto"
	ElseIf cStatus == "1"
		cDescrStat := "Em Aprovacao"
	ElseIf cStatus == "2"
		cDescrStat := "Reprovado"
	ElseIf cStatus == "3"
		cDescrStat := "Aprovado"
	ElseIf cStatus == "4"
		cDescrStat := "Conferido"
	ElseIf cStatus == "5"
		cDescrStat := "Título Gerado"
	ElseIf cStatus == "6"
		cDescrStat := "Negativo"
	Endif	

	dbSelectArea("RD0")
	dbSetOrder(1)
	dbSeek(xFilial("RD0")+::Tecnico)
	
	//-- preenche dados do cabecalho do RDA
	::NewRDAImprime:DadosTecnico:Tecnico			:= oModelCab:GetValue("PFO_CODTEC")
	::NewRDAImprime:DadosTecnico:NomeTecnico		:= oModelCab:GetValue("PFO_NOMTEC")
	::NewRDAImprime:DadosTecnico:Banco				:= Posicione("SA2",1,xFilial("SA2")+RD0->RD0_FORNEC+RD0->RD0_LOJA,"A2_BANCO")
	::NewRDAImprime:DadosTecnico:DataImprime		:= MsDate()
	::NewRDAImprime:DadosTecnico:PeriodoDe			:= oModelCab:GetValue("PFO_DATINI")
	::NewRDAImprime:DadosTecnico:PeriodoAte	  	:= oModelCab:GetValue("PFO_DATFIM")
	::NewRDAImprime:DadosTecnico:CentroCusto		:= RD0->RD0_CC + " - " + Posicione("CTT",1,xFilial("CTT")+RD0->RD0_CC,"CTT_NOME")
	::NewRDAImprime:DadosTecnico:Conferencia		:= oModelCab:GetValue("PFO_DTCONF")
	::NewRDAImprime:DadosTecnico:Fechamento		:= oModelCab:GetValue("PFO_CODFEC")
	::NewRDAImprime:DadosTecnico:StatusRDA			:= cDescrStat
	::NewRDAImprime:DadosTecnico:EmpPag			:= RD0->RD0_EMPATU + RD0->RD0_FILATU + " - " + Posicione("SM0",1,RD0->RD0_EMPATU + RD0->RD0_FILATU,"M0_NOME + ' - ' + M0_FILIAL") 
	::NewRDAImprime:DadosTecnico:Titulo			:= oModelCab:GetValue("PFO_KEYSE2")
	::NewRDAImprime:DadosTecnico:Aprovador			:= oModelCab:GetValue("PFO_WFIDAP") + " - " + Posicione("RD0",1,xFilial("RD0")+oModelCab:GetValue("PFO_WFIDAP"),"RD0_NOME")
	::NewRDAImprime:DadosTecnico:TotDespValor		:= oModelCab:GetValue("PFO_VLDESP")
	::NewRDAImprime:DadosTecnico:TotExcOS			:= oModelCab:GetValue("PFO_VLOSEX")
	::NewRDAImprime:DadosTecnico:TotDebRDA			:= oModelCab:GetValue("PFO_VLDEB")
	::NewRDAImprime:DadosTecnico:TotalRda			:= oModelCab:GetValue("PFO_VLRDA")
	::NewRDAImprime:DadosTecnico:DataFechamento	:= oModelCab:GetValue("PFO_DATFEC")
	::NewRDAImprime:DadosTecnico:Coordenador		:= RD0->RD0_XCOORD + " - " + Posicione("RD0",1,xFilial("RD0")+RD0->RD0_XCOORD,"RD0_NOME")

	//-- carrega o model das despesas
	oModelDesp := oModelRDA:GetModel("PFMDETAIL")

	//-- preenche dados das despesas
	::NewRDAImprime:Despesa := {}

	For nX:=1 To oModelDesp:Length()

		oModelDesp:GoLine(nX)
	
		AAdd( ::NewRDAImprime:Despesa, WsClassNew( "stDespesaNewRDA" ) )
		
		::NewRDAImprime:Despesa[nX]:DataRDA  	:= oModelDesp:GetValue("PFM_DATA")
		::NewRDAImprime:Despesa[nX]:Despesa	:= oModelDesp:GetValue("PFM_CODDES")
		::NewRDAImprime:Despesa[nX]:Descricao	:= oModelDesp:GetValue("PFM_DESCR")
		::NewRDAImprime:Despesa[nX]:Quantidade	:= oModelDesp:GetValue("PFM_QUANT")
		::NewRDAImprime:Despesa[nX]:ValorUnit	:= oModelDesp:GetValue("PFM_VLUNIT")		
		::NewRDAImprime:Despesa[nX]:ValorReemb	:= oModelDesp:GetValue("PFM_VLREEM")
		::NewRDAImprime:Despesa[nX]:WF			:= oModelDesp:GetValue("PFM_WFSTAT")
		::NewRDAImprime:Despesa[nX]:Observacao	:= oModelDesp:GetValue("PFM_HIST")

	Next nX

	//-- carrega o model das OS excluidas 
	oModelOSEx := oModelRDA:GetModel("PF8DETAIL")
	
	//-- preenche os dados da exclusao de OS
	::NewRDAImprime:ExclusaoOS := {}

	For nX:=1 To oModelOSEx:Length()

		oModelOSEx:GoLine(nX)

		AAdd( ::NewRDAImprime:ExclusaoOS, WsClassNew( "stExclOSNewRDA" ) )

		::NewRDAImprime:ExclusaoOS[nX]:OrdemServico	:= oModelOSEx:GetValue("PF8_IDOS")
		::NewRDAImprime:ExclusaoOS[nX]:DataOS			:= oModelOSEx:GetValue("PF8_DATOS") 
		::NewRDAImprime:ExclusaoOS[nX]:Cliente			:= oModelOSEx:GetValue("PF8_CLIANT")  
		::NewRDAImprime:ExclusaoOS[nX]:Loja			:= oModelOSEx:GetValue("PF8_LJANT")  
		::NewRDAImprime:ExclusaoOS[nX]:NomeCliente	:= Posicione("SA1",1,xFilial("SA1")+oModelOSEx:GetValue("PF8_CLIANT")+oModelOSEx:GetValue("PF8_LJANT"),"A1_NOME")
		::NewRDAImprime:ExclusaoOS[nX]:Projeto			:= oModelOSEx:GetValue("PF8_PRJANT")
		::NewRDAImprime:ExclusaoOS[nX]:Motivo			:= oModelOSEx:GetValue("PF8_MOTANT")
		::NewRDAImprime:ExclusaoOS[nX]:DataExclusao	:= oModelOSEx:GetValue("PF8_DTRECL") 
		::NewRDAImprime:ExclusaoOS[nX]:ValorRda		:= oModelOSEx:GetValue("PF8_RDAEST")

	Next nX
	
	//-- carrega o model dos debitos futuros
	oModelDeb := oModelRDA:GetModel("PFODETAIL")
	
	//-- Estrtura de debitos de fechamentos anteriores do novo RDA
	::NewRDAImprime:DebitoAnt := {}

	For nX:=1 To oModelDeb:Length()

		oModelDeb:GoLine(nX)

		AAdd( ::NewRDAImprime:DebitoAnt, WsClassNew( "stDebAntNewRDA" ) )

		::NewRDAImprime:DebitoAnt[nX]:Fechamento		:= oModelDeb:GetValue("PFO_CODFEC")
		::NewRDAImprime:DebitoAnt[nX]:DataInicio		:= oModelDeb:GetValue("PFO_DATINI")
		::NewRDAImprime:DebitoAnt[nX]:DataFim			:= oModelDeb:GetValue("PFO_DATFIM")
		::NewRDAImprime:DebitoAnt[nX]:ValorDespesa	:= oModelDeb:GetValue("PFO_VLDESP")
		::NewRDAImprime:DebitoAnt[nX]:ValorExclOS		:= oModelDeb:GetValue("PFO_VLOSEX")
		::NewRDAImprime:DebitoAnt[nX]:ValorDebAnt		:= oModelDeb:GetValue("PFO_VLDEB")
		::NewRDAImprime:DebitoAnt[nX]:ValorRDA			:= oModelDeb:GetValue("PFO_VLRDA")

	Next nX
	
	oModelRDA:DeActivate()

Else

	SetSoapFault( "Impressao RDA", cMsg )

Endif
U_xCONOUT("TSRVS001","IMPRIMENEWRDA","RETORNO","")	
Return(lRet)


//-------------------------------------------------------------------
/*/{Protheus.doc} RetFrenteCFP (TSRVS001)
Retorna lista das frentes de entrega do projeto CFP
<AUTHOR>
@since 22/09/2015 
@param ProjetoCFP, caracter, codigo do projeto CFP 
@return FrentesCFP, estrutura, dados da frente de entrega do projeto (codigo, descricao)    
/*/
//-------------------------------------------------------------------

WSMETHOD RetFrenteCFP WSRECEIVE ProjetoCFP WSSEND FrentesCFP WSSERVICE TSRVS001

Local nTamPrj	:= TamSX3("PE8_PROJET")[1]
Local nX		:= 0
Local lRet	:= .T.             

//-- ajusta tamanho dos campos
::ProjetoCFP := Substr(PadR(Alltrim(::ProjetoCFP),nTamPrj),1,nTamPrj)

::FrentesCFP := {}

U_xCONOUT("TSRVS001","RETFRENTECFP","REQUISICAO","")

dbSelectArea("PE8")
dbSetOrder(1)
dbSeek(xFilial("PE8")+::ProjetoCFP)

If ! Eof()

	While ! Eof() .And. PE8->PE8_FILIAL == xFilial("PE8") .And. PE8->PE8_PROJET == ::ProjetoCFP

		If ! Empty(PE8->PE8_FRTAPO)
		
			AAdd( ::FrentesCFP, WsClassNew( "stFrenteEntrega" ) )
			
			nX := Len(::FrentesCFP)
		
			::FrentesCFP[nX]:Frente			:= PE8->PE8_CODIGO
			::FrentesCFP[nX]:Descricao		:= PE8->PE8_DESCR
			::FrentesCFP[nX]:Motivo			:= PE8->PE8_MOTIVO
			::FrentesCFP[nX]:Coordenador	:= PE8->PE8_COORD	
			::FrentesCFP[nX]:ProjetoPMS		:= PE8->PE8_PRJPMS
			::FrentesCFP[nX]:EdtPms			:= PE8->PE8_EDT	

		Endif

		dbSkip()
		
	Enddo

	If nX == 0
		lRet := .F.
	Else
		lRet := .T.
	Endif	
	
Else

	lRet := .F.
	
Endif

If ! lRet
	SetSoapFault( "Frente Projeto", "Não foram encontradas frentes de entrega cadastradas para este projeto." )
	U_xCONOUT("TSRVS001","RETFRENTECFP","(NAO FORAM ENCONTRADAS FRENTES DE ENTREGA CADASTRADAS PARA ESTE PROJETO)","S")
Endif
U_xCONOUT("TSRVS001","RETFRENTECFP","RETORNO","")
Return(lRet)	


//-------------------------------------------------------------------
/*/{Protheus.doc} ImprimeRV (TSRVS001)
Retorna dados para impressao do relatorio de RV 
<AUTHOR>
@since 22/09/2015 
@param Tecnico, caracter, codigo do tecnico
@param DataDe, data, data de inicio do periodo 
@param DataAte, data, data de fim do periodo 
@return RVImprime, estrutura, dados da RV para imprimir     
/*/
//-------------------------------------------------------------------

WSMETHOD ImprimeRV WSRECEIVE Tecnico, DataDe, DataAte WSSEND RVImprime WSSERVICE TSRVS001

Local lRet			:= .T.
Local cAliTmp		:= GetNextAlias()
Local cQryTmp		:= ""
Local cMsg			:= ""
Local nTamTec		:= TamSX3("RD0_CODIGO")[1]  
Local nX			:= 0
Local cStartPath 	:= GetSrvProfString("Startpath","")
Local cEmpTec 		:= readValue('RD0',1, xfilial('RD0') + ::Tecnico, 'RD0_EMPATU' )
Local cFilTec 		:= readValue('RD0',1, xfilial('RD0') + ::Tecnico, 'RD0_FILATU' )
Local cArqPFF       := ""

//-- ajusta tamanho dos campos
::Tecnico := Substr(PadR(Alltrim(::Tecnico),nTamTec),1,nTamTec)
U_xCONOUT("TSRVS001","IMPRIMERV","REQUISICAO","")

dbSelectArea("RD0")
dbSetOrder(1)
dbSeek(xFilial("RD0")+::Tecnico)

If Eof()
	cMsg := "Tecnico nao encontrado."
	lRet := .F.
ElseIf Empty(::DataDe)
	cMsg := "Data inicial invalida."
	lRet := .F.
ElseIf Empty(::DataAte)
	cMsg := "Data final invalida."
	lRet := .F.
ElseIf ::DataDe > ::DataAte
	cMsg := "Periodo invalido."
	lRet := .F.
Endif

If lRet

	// Trocar as tabelas para a nova empresa e filial do tecnico
	if ( ! empty(cFilTec) .and. len(cFilTec) == 11 .and. ! empty(cEmpTec) ) .and. alltrim(cEmpTec) <> alltrim(cEmpAnt) .and. alltrim(cFilTec) <> alltrim(cFilAnt)
		cArqPFF := RetFullName("PFF", cEmpTec)
	else
		cFilTec := cFilAnt
		cEmpTec := cEmpAnt
		cArqPFF := RetFullName("PFF", cEmpAnt)
	endif

	cQryTmp := " SELECT * "
	cQryTmp += "   FROM " + alltrim(cArqPFF) + " PFF "
	if alltrim(cEmpTec) == '00'
		cQryTmp += "  WHERE PFF_FILIAL = '" + xFilial("PFF") + "' "
	else
		cQryTmp += "  WHERE PFF_FILIAL = '" + cFilTec + "' "
	endif
	cQryTmp += "    AND PFF_DATA >= '" + DtoS(::DataDe) + "' "
	cQryTmp += "    AND PFF_DATA <= '" + DtoS(::DataAte) + "' "
	cQryTmp += "    AND PFF_CODTEC = '" + ::Tecnico + "' "
	cQryTmp += "    AND ( (PFF_IDOS <> 'MANUAL') OR ( PFF_IDOS = 'MANUAL' AND PFF_TOTHRS <> 0 ) ) "	
	cQryTmp += "    AND PFF.D_E_L_E_T_ = ' ' "
	
	cQryTmp := ChangeQuery(cQryTmp)
	
	dbUseArea(.T., "TOPCONN", TcGenQry(,,cQryTmp), cAliTmp, .F., .T.)

	If ! (cAliTmp)->(EOF())
		
		While ! (cAliTmp)->(EOF())
		
			cSqlPF9 := "SELECT * FROM PF9000 WHERE PF9_FILIAL ='" + space(tamSX3('PF9_FILIAL')[1]) + "' AND PF9_IDOS ='" + (cAliTmp)->PFF_IDOS + "'"
			cSqlPF9 := ChangeQuery(cSqlPF9)
			TCQuery cSqlPF9 New Alias "TSVSSA9"
			TSVSSA9->(dbGoTop())
			
			cSqlSA1 := "SELECT * FROM SA1000 WHERE A1_FILIAL ='" + space(tamSX3('A1_FILIAL')[1]) + "' AND A1_COD ='" + TSVSSA9->PF9_CLIENT + "' AND A1_LOJA ='" + TSVSSA9->PF9_LOJA + "'"
			cSqlSA1 := ChangeQuery(cSqlSA1)
			TCQuery cSqlSA1 New Alias "TSVSSA1"
			TSVSSA1->(dbGoTop())
			
			AAdd( ::RVImprime, WsClassNew( "stItemRV" ) )
			
			nX := Len(::RVImprime)
			
			// as regra abaixo estao conforme o relatório TSRVR203
			
			::RVImprime[nX]:IdOS		:= (cAliTmp)->PFF_IDOS
			::RVImprime[nX]:Atraso		:= (cAliTmp)->PFF_ATRASO // incluir atraso
			::RVImprime[nX]:NomeCliente	:= TSVSSA9->PF9_CLIENT+"-"+TSVSSA9->PF9_LOJA + " - " + TSVSSA1->A1_NREDUZ
			::RVImprime[nX]:DataOS		:= STOD( (cAliTmp)->PFF_DATA )		// incluir DataOS
			::RVImprime[nX]:Horas		:= (cAliTmp)->PFF_QTDHRS
			::RVImprime[nX]:VlrHoras	:= iif( (cAliTmp)->PFF_HRSEXC > 0, (cAliTmp)->PFF_VLHREX , (cAliTmp)->PFF_VALHR) 
			::RVImprime[nX]:HorasExtras	:= (cAliTmp)->PFF_HRSEXC
			::RVImprime[nX]:VlrHE		:= (cAliTmp)->( PFF_HRSEXC * PFF_VLEXCA )
			::RVImprime[nX]:HorasTras	:= (cAliTmp)->PFF_HRTRAN
			::RVImprime[nX]:VlrHT		:= (cAliTmp)->PFF_VALTRA
			::RVImprime[nX]:TotalDSR 	:= (cAliTmp)->PFF_DSRSRV
			::RVImprime[nX]:CodProduto	:= TSVSSA9->PF9_PRODUT
			::RVImprime[nX]:Motivo		:= TSVSSA9->PF9_CODMOT
			::RVImprime[nX]:TotalPremio	:= (cAliTmp)->PFF_TOTHRS
			::RVImprime[nX]:HrTot		:= TSVSSA9->PF9_TOTAL
			::RVImprime[nX]:FatPon		:= (cAliTmp)->PFF_FATOR
			::RVImprime[nX]:SemTrasl	:= iif( (cAliTmp)->PFF_HRSEXC > 0, 0, (cAliTmp)->(PFF_TOTHRS-PFF_VALTRA) )
			::RVImprime[nX]:TotalRV		:= (cAliTmp)->(PFF_TOTHRS + PFF_DSRSRV)
			
			(cAliTmp)->(dbSkip())
			
			TSVSSA9->(dbCloseArea())
			TSVSSA1->(dbCloseArea())
			
		Enddo
		
		lRet := .T.
		cMsg := ""
			
	Else
	
		lRet := .F.
		cMsg := "Nao foram encontrados dados para este técnico neste período."
			
	Endif	
	
Endif

If ! lRet
	SetSoapFault( "Imprime RV", cMsg )
	U_xCONOUT("TSRVS001","IMPRIMERV",cMsg,"S")
Endif	
U_xCONOUT("TSRVS001","IMPRIMERV","RETORNO","")
Return(lRet)


//-------------------------------------------------------------------
/*/{Protheus.doc} RetornaTecnico (TSRVS001)
Retorna grupo/filial do tecnico pelo segmento, localidade e e-mail (utilizado no Jira)
<AUTHOR>
@since 14/10/2015 
@param cSegmento, caracter, nome do segmento 
@param cLocalidade, caracter, nome da localidade 
@param cEmail, caracter, e-mail do tecnico 
@return RetTecnico, estrutura, estrutura com os dados do tecnico      
/*/
//-------------------------------------------------------------------
/*
WSMETHOD RetornaTecnico WSRECEIVE cSegmento, cLocalidade, cEmail WSSEND RetTecnico WSSERVICE TSRVS001

Local nLinRet		:= 0
Local lContinua	:= .T.
Local cMsg		:= ""
Local cQuery		:= ""
Local cCdTec 		:= ""
Local cNmTec 		:= ""
Local cCdEmp 		:= ""
Local cCdFil 		:= ""
Local cNmEmp 		:= ""	
Local cNmFil 		:= ""
Local cCdSeg		:= ""
Local cCdLoc		:= ""
Local aArea		:= GetArea()	

DEFAULT ::cSegmento		:= ""
DEFAULT ::cLocalidade	:= ""
DEFAULT ::cEmail			:= ""

::RetTecnico := {}

//-- Validacoes
If ZQF->(FieldPos("ZQF_CODSEG")) == 0 .OR. ZQF->(FieldPos("ZQF_CODLOC")) == 0
	lContinua := .F.
	cMsg := "Para pesquisa é necessario a criacao dos campos ZQF_CODSEG e ZQF_CODLOC, entre em contato com equipe de TDI."
EndIf 

If lContinua .AND. Empty(::cSegmento)
	lContinua := .F.
	cMsg	:= "Informe Segmento"
EndIf
If lContinua .AND. Empty(::cLocalidade)
	lContinua := .F.
	cMsg	:= "Informe Localidade"
EndIf
If lContinua .AND. Empty(::cEmail)
	lContinua := .F.
	cMsg	:= "Informe Email do tecnico"
EndIf

//-- Pesquisa segmento
If lContinua
	cQuery := " SELECT AOV_CODSEG, AOV_DESSEG "
	cQuery += "   FROM " + RetSQLName("AOV") + " AOV "
	cQuery += "  WHERE AOV.D_E_L_E_T_ = ' ' "
	cQuery += "    AND AOV_FILIAL = '" + xFilial("AOV") + "' "
	cQuery += "    AND UPPER(AOV_DESSEG) LIKE '"+Upper(Alltrim(::cSegmento))+"%' "
	cQuery := ChangeQuery( cQuery )
	dbUseArea(.T., "TOPCONN", TcGenQry(,,cQuery), "RETTECSEG", .F., .T.)
	If !RETTECSEG->(EOF())
		cCdSeg := RETTECSEG->AOV_CODSEG 
	EndIf
	RETTECSEG->(DbCloseArea())
	RestArea(aArea)
	If Empty(cCdSeg)
		lContinua := .F.
		cMsg	:= "Segmento informado ("+::cSegmento+") não foi encontrado no cadastro de segmentos - AOV."
	EndIf
EndIf

//-- Pesquisa Localidade
If lContinua
	cQuery := " SELECT ZX5_CHAVE, ZX5_DESCRI "
	cQuery += "   FROM " + RetSQLName("ZX5") + " ZX5 "
	cQuery += "  WHERE ZX5.D_E_L_E_T_ = ' ' "
	cQuery += "    AND ZX5_TABELA = 'SRV035' "
	cQuery += "    AND UPPER(ZX5_DESCRI) LIKE '"+Upper(Alltrim(::cLocalidade))+"%' "
	cQuery := ChangeQuery( cQuery )
	dbUseArea(.T., "TOPCONN", TcGenQry(,,cQuery), "RETTECLOC", .F., .T.)
	If !RETTECLOC->(EOF())
		cCdLoc := RETTECLOC->ZX5_CHAVE 
	EndIf
	RETTECLOC->(DbCloseArea())
	RestArea(aArea)
	If Empty(cCdLoc)
		lContinua := .F.
		cMsg	:= "Localidade informada ("+::cLocalidade+") não foi encontrado no cadastro de tabelas - ZX5 - Tabela 'SRV035'"
	EndIf
EndIf

//-- Pesquisa email do tecnico
If lContinua
	cQuery := " SELECT RD0_CODIGO, RD0_NOME, RD0_MSBLQL "
	cQuery += "   FROM " + RetSQLName("RD0") + " RD0 "
	cQuery += "  WHERE RD0.D_E_L_E_T_ = ' ' "
	cQuery += " 	 AND UPPER(RD0_EMAIL) = '" + Alltrim(Upper(::cEmail)) + "' "
	cQuery := ChangeQuery(cQuery)
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"QRYRD0",.F.,.T.)
	If QRYRD0->(EOF())
		lContinua	:= .F.
		cMsg		:= "Email informado ("+Alltrim(Upper(::cEmail))+") não foi encontrado no cadastro de tecnicos - RD0"
	Else
		While QRYRD0->(!EOF())
			If UPPER(QRYRD0->RD0_MSBLQL) == "1" // inativo
				lContinua := .F.
				cMsg	:= "Email informado ("+Alltrim(Upper(::cEmail))+") encontra-se em um técinico Inativo - RD0"	
			Else
				cCdTec		:= QRYRD0->RD0_CODIGO
				cNmTec		:= QRYRD0->RD0_NOME
				lContinua 	:= .T.
				Exit
			Endif
			QRYRD0->(dbSkip())
		Enddo 
	EndIf
	QRYRD0->(DbCloseArea())
EndIf

//-- Pesquisa Empresa/Filial na tabela de/Para - ZQF
If lContinua 
	cQuery := " SELECT ZQF_EMPORI, ZQF_FILORI "
	cQuery += "   FROM " + RetSQLName("ZQF") + " ZQF "
	cQuery += "  WHERE ZQF.D_E_L_E_T_ = ' ' "
	cQuery += " 	 AND ZQF_CODSEG = '" + cCdSeg + "' "
	cQuery += " 	 AND ZQF_CODLOC = '" + cCdLoc + "' "
	cQuery := ChangeQuery(cQuery)
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),"QRYZQF",.F.,.T.)
	If QRYZQF->(EOF())
		lContinua := .F.
		cMsg	:= "Nao existe Grupo/Filial relacionada ao Segmento ("+::cSegmento+") / Localidade ("+::cLocalidade+") informados"
	Else
		cCdEmp := QRYZQF->ZQF_EMPORI
		cCdFil := QRYZQF->ZQF_FILORI
	EndIf
	QRYZQF->(DbCloseArea())

	aSM0Area := SM0->(GetArea())
	SM0->(DbSetOrder(1))
	If SM0->(DbSeek(cCdEmp+cCdFil))
		cNmEmp := SM0->M0_NOMECOM	
		cNmFil := SM0->M0_FILIAL
	Else
		lContinua := .F.
		cMsg	:= "Grupo ["+cCdEmp+"] / Filial ["+cCdFil+"] informados no de/para (ZQF) nao encontrado no SIGAMAT!"
	EndIf
	SM0->(RestArea(aSM0Area))
EndIf

If !lContinua
	SetSoapFault( "Tecnico", cMsg )
	Return(.F.)
EndIf

nLinRet++

aAdd( ::RetTecnico , WsClassNew( "StruRetTecnico" ) )

::RetTecnico[nLinRet]:cCdEmpresa	:= cCdEmp
::RetTecnico[nLinRet]:cNmEmpresa	:= cNmEmp
::RetTecnico[nLinRet]:cCdFilial		:= cCdFil
::RetTecnico[nLinRet]:cNmFilial		:= cNmFil
::RetTecnico[nLinRet]:cCdTecnico	:= cCdTec
::RetTecnico[nLinRet]:cNmTecnico	:= cNmTec

Return(.T.)
*/

//-------------------------------------------------------------------
/*/{Protheus.doc} RetDuracaoApto (TSRVS001)
Retorna a duracao do apontamento com base no calendario
<AUTHOR>
@since 28/10/2015 
@param Tecnico, caracter, codigo do tecnico
@param DataApto, data, data do apontamento
@param HoraIni, caracter, hora inicio
@param HoraFim, caracter, hora fim
@param ProjetoPMS, caracter, projeto PMS
@return Duracao, numerico, retorna a duracao do apontamento
/*/
//-------------------------------------------------------------------

WSMETHOD RetDuracaoApto WSRECEIVE Tecnico, DataApto, HoraIni, HoraFim, ProjetoPMS WSSEND Duracao WSSERVICE TSRVS001

Local nTamCod		:= TamSX3("RD0_CODIGO")[1]             
Local cTecnico	:= Substr(PadR(Alltrim(::Tecnico),nTamCod),1,nTamCod)
Local cRecurAE8	:= ""
Local cCalend		:= ""
Local nHQuant		:= 0
U_xCONOUT("TSRVS001","RETDURACAOAPTO","REQUISICAO","")

If Empty(::HoraIni)
	SetSoapFault( "Duracao Apontamento", "Informe a hora inicio." )
	U_xCONOUT("TSRVS001","RETDURACAOAPTO","(INFORME A HORA INICIO) - RETORNO","")
	Return(.F.)
EndIf

If Empty(::HoraFim)
	SetSoapFault( "Duracao Apontamento", "Informe a hora final." )
	U_xCONOUT("TSRVS001","RETDURACAOAPTO","(INFORME A HORA FINAL) - RETORNO","")
	Return(.F.)
EndIf

If ::HoraIni > ::HoraFim
	SetSoapFault( "Duracao Apontamento", "Intervalo invalido. Hora inicial maior que a final." )
	U_xCONOUT("TSRVS001","RETDURACAOAPTO","(INTERVALO INVALIDO. HORA INICIAL MAIOR QUE A FINAL) - RETORNO","")
	Return(.F.)
EndIf

//-- verifica se o tecnico existe
dbSelectArea("RD0")
dbSetOrder(1)
dbSeek(xFilial("RD0")+cTecnico)

If EOF()
	SetSoapFault( "Duracao Apontamento", "Tecnico nao cadastrado." )
	U_xCONOUT("TSRVS001","RETDURACAOAPTO","(TECNICO NAO CADASTRADO) - RETORNO","")
	Return(.F.)
EndIf

//-- verifica se o projeto existe
dbSelectArea("AF8")
dbSetOrder(1)
dbSeek(xFilial("AF8")+::ProjetoPMS)

If EOF()
	SetSoapFault( "Duracao Apontamento", "Projeto PMS nao cadastrado." )
	U_xCONOUT("TSRVS001","RETDURACAOAPTO","(PROJETO PMS NAO CADASTRADO) - RETORNO","")
	Return(.F.)
EndIf

cRecurAE8	:= U_SrvXTecAE8(cTecnico)
cCalend	:= Posicione("AE8",1,FWxFilial("AE8")+cRecurAE8,"AE8_CALEND") 	
nHQuant 	:= PmsHrsItvl(::DataApto, ::HoraIni, ::DataApto, ::HoraFim, cCalend, ::ProjetoPMS, cRecurAE8,,.T.)

::Duracao := nHQuant
U_xCONOUT("TSRVS001","RETDURACAOAPTO","RETORNO","")
Return(.T.)


//-------------------------------------------------------------------
/*/{Protheus.doc} RetKM (TSRVS001)
Retorna a quilometram para um cliente, loja, projeto, frente, data
<AUTHOR>
@since 30/11/2015 
@param Cliente, caracter, codigo do cliente 
@param Loja, caracter, loja do cliente
@param CodLocal, caracter, codigo do local de atendimento  
@param ProjetoCFP, caracter, codigo do projeto CFP   
@param FrenteCFP, caracter, codigo da frente do CFP   
@param DataOS, data, data da OS 
@return DadosRetKm, estrutura, retorna os dados do km (codigo local, quantidade de km, valor km, total km) 
/*/
//-------------------------------------------------------------------

WSMETHOD RetKM WSRECEIVE Cliente, Loja, CodLocal, ProjetoCFP, FrenteCFP, DataOS WSSEND DadosRetKM WSSERVICE TSRVS001

Local cCodKM		:= ""
Local nQtKM 		:= 0
Local nVlKM 		:= 0
Local nTotalKM 	:= 0
Local nTamCli		:= TamSX3("A1_COD")[1]             
Local nTamLoj		:= TamSX3("A1_LOJA")[1]
Local nTamProj	:= TamSX3("PE5_PROJET")[1]	             
Local nTamFrt		:= TamSX3("PE8_CODIGO")[1]
Local nTamLoc		:= TamSX3("PF9_LOCATE")[1]
Local cCliente 	:= ""
Local cLoja 		:= ""
Local cCodLocal 	:= ""
Local cProjeto 	:= ""
Local cFrente 	:= ""
Local dDataOS 	:= CtoD("")
Local aIta 		:= {}

cCliente	:= Substr(PadR(Alltrim(::Cliente),nTamCli),1,nTamCli)
cLoja		:= Substr(PadR(Alltrim(::Loja),nTamLoj),1,nTamLoj)
cCodLocal	:= Substr(PadR(Alltrim(::CodLocal),nTamLoc),1,nTamLoc)
cProjeto	:= Substr(PadR(Alltrim(::ProjetoCFP),nTamProj),1,nTamProj)
cFrente	:= Substr(PadR(Alltrim(::FrenteCFP),nTamFrt),1,nTamFrt)
dDataOS	:= ::DataOS

U_xCONOUT("TSRVS001","RETKM","REQUISICAO","")

//-- verifica cliente/loja 
dbSelectArea("SA1")
dbSetOrder(1)
dbSeek(xFilial("SA1")+cCliente+cLoja)

If Eof()
	SetSoapFault( "KM", "Cliente nao cadastrado." )
	U_xCONOUT("TSRVS001","RETKM","(CLIENTE NAO CADASTRADO) - RETORNO","")
	Return(.F.)
EndIf

//-- verifica local  
dbSelectArea("PFH")
dbSetOrder(1)
dbSeek(xFilial("PFH")+cCliente+cLoja+cCodLocal)

If Eof()
	SetSoapFault( "KM", "Local de atendimento nao cadastrado para o cliente." )
	U_xCONOUT("TSRVS001","RETKM","(LOCAL DE ATENDIMENTO NAO CADASTRADO PARA O CLIENTE) - RETORNO","")
	Return(.F.)
EndIf

//-- verifica projeto  
dbSelectArea("PE5")
dbSetOrder(1)
dbSeek(xFilial("PE5")+cProjeto)

If Eof()
	SetSoapFault( "KM", "Projeto nao cadastrado." )
	U_xCONOUT("TSRVS001","RETKM","(PROJETO NAO CADASTRADO) - RETORNO","")
	Return(.F.)
EndIf

//-- verifica frente   
dbSelectArea("PE8")
dbSetOrder(1)
dbSeek(xFilial("PE8")+cProjeto+cFrente)

If Eof()
	SetSoapFault( "KM", "Frente nao cadastrada para o projeto." )
	U_xCONOUT("TSRVS001","RETKM","(FRENTE NAO CADASTRADA PARA O PROJETO) - RETORNO","")
	Return(.F.)
EndIf
		
If !Empty( cCodLocal )

	cCodKM 	:= U_SrvXCdKm()
	nQtKM 		:= 0
	nVlKM 		:= 0
	nTotalKM 	:= 0

	PFH->(DbSetOrder(1)) // PFH_FILIAL+PFH_CLIENT+PFH_LOJA+PFH_IDSEQ+PFH_FILORI
	If PFH->(MsSeek(FWxFilial("PFH")+cCliente+cLoja+cCodLocal)) 
		nQtKM := (PFH->PFH_KM * 2)
	EndIf

	If !Empty(cCodKM)
		aIta := U_SrvXIta(cCodKM,dDataOS,cCliente,cLoja,cProjeto,cFrente,cCodLocal)
		If Len(aIta) >= 6
			nVlKM	:= aIta[6]
		Else	
			nVlKM	:= 0
		EndIf
		nTotalKM := Round(nQtKM*nVlKM,TamSX3("PFM_VLDESP")[2])
	EndIf

EndIf

::DadosRetKM:CodLocal:= cCodLocal
::DadosRetKM:QtdeKm	:= nQtKM 	
::DadosRetKM:ValorKm	:= nVlKM
::DadosRetKM:TotalKm	:= nTotalKM

U_xCONOUT("TSRVS001","RETKM","RETORNO","")

Return(.T.)


//-------------------------------------------------------------------
/*/{Protheus.doc} RetCodKm (TSRVS001)
Retorna o codigo da despesa usada para quilometragem 
<AUTHOR>
@since 06/04/2016
@param Dummy, caracter, sempre passar vazio 
@return CodigoKm, caracter, codigo da despesa de quilometragem    
/*/
//-------------------------------------------------------------------

WSMETHOD RetCodKm WSRECEIVE Dummy WSSEND CodigoKm WSSERVICE TSRVS001
U_xCONOUT("TSRVS001","RETCODKM","REQUISICAO","")

::CodigoKm := U_SrvXCdKm()

U_xCONOUT("TSRVS001","RETCODKM","RETORNO","")
Return(.T.)


//-------------------------------------------------------------------
/*/{Protheus.doc} GetListaProjetoCoorde (TSRVS001)
Retorna lista de Projetos de um coordenador 
<AUTHOR>
@since 11/05/2016 
@param Dummy, caracter, sempre passar vazio 
@return CodigoKm, caracter, codigo da despesa de quilometragem    
/*/
//-------------------------------------------------------------------

WSMETHOD GetListaProjetoCoorde WSRECEIVE dtCoordenador,dtOrigem WSSEND dtListaPrjCoord WSSERVICE TSRVS001

Local cAliTmp := GetNextAlias()
Local cQuery  := ""

Default ::dtCoordenador := CriaVar("PE8_COORD", .F.)
Default ::dtOrigem := "PMS"

U_xCONOUT("TSRVS001","GETLISTAPROJETOCOORDE","REQUISICAO","")

RD0->(dbSetOrder(1))
If ! RD0->(dbSeek(xFilial("RD0") + ::dtCoordenador))
	
	SetSoapFault("Projetos Coordenador", "Coordenador nao localizado.")
	U_xCONOUT("TSRVS001","GETLISTAPROJETOCOORDE","(COORDENADOR NAO LOCALIZADO) - RETORNO","")
	Return .F.
ElseIf RD0->RD0_XTPCGO <> 'CP' .And. Upper(AllTrim(::dtOrigem)) <> "PMS"
	
	SetSoapFault("Projetos Coordenador", "Tecnico não é coordenador.")
	U_xCONOUT("TSRVS001","GETLISTAPROJETOCOORDE","(TECNICO NAO EH COORDENADOR) - RETORNO","")
	Return .F.
EndIf

//-- Query para retornar os projetos do coordenador

cQuery := " SELECT PE8_PROJET, PE5_DESPRO, PE8_CODIGO, PE8_DESCR, PE5_CLIENT, PE5_LOJA, A1_NOME, PE5_CODMOT, PE0_TPSOS " 
cQuery += "   FROM "+RetSqlName("PE8")+" PE8 "
cQuery += "  INNER JOIN "+RetSqlName("PE5")+" PE5 ON PE5.D_E_L_E_T_ = ' ' AND PE5_FILIAL = '"+xFilial("PE5")+"' AND PE5_PROJET = PE8_PROJET "
cQuery += "  INNER JOIN "+RetSqlName("SA1")+" SA1 ON SA1.D_E_L_E_T_ = ' ' AND A1_FILIAL = '"+xFilial("SA1")+"' AND A1_COD = PE5_CLIENT AND A1_LOJA = PE5_LOJA "
cQuery += "   LEFT JOIN "+RetSqlName("PE0")+" PE0 ON PE0.D_E_L_E_T_ = ' ' AND PE0_FILIAL = '"+xFilial("PE0")+"' AND PE0_COD = PE5_CODMOT "
cQuery += "  WHERE PE8.D_E_L_E_T_ = ' ' "
cQuery += "    AND PE8_FILIAL = '"+xFilial("PE8")+"' "
cQuery += "    AND PE8_COORD = '"+::dtCoordenador+"' "
cQuery += "  ORDER BY PE8_PROJET, PE8_CODIGO "

cQuery := ChangeQuery(cQuery)

dbUseArea(.T., "TOPCONN", TcGenQry(,, cQuery), cAliTmp, .F., .T.)

If (cAliTmp)->(Eof()) 
	SetSoapFault("Projetos Coordenador","Coordenador sem projetos para listar.")
	U_xCONOUT("TSRVS001","GETLISTAPROJETOCOORDE","(COORDENADOR SEM PROJETOS PARA LISTAR) - RETORNO","")
	Return .F.
EndIf

::dtListaPrjCoord := {}

While ! (cAliTmp)->(Eof()) 

	aAdd(::dtListaPrjCoord, WsClassNew('stListaPrjCoord'))
	nPosIte := Len(::dtListaPrjCoord)

	::dtListaPrjCoord[nPosIte]:Projeto 			:= (cAliTmp)->PE8_PROJET
	::dtListaPrjCoord[nPosIte]:Frente	 			:= (cAliTmp)->PE8_CODIGO
	::dtListaPrjCoord[nPosIte]:Cliente				:= (cAliTmp)->PE5_CLIENT
	::dtListaPrjCoord[nPosIte]:NomeCliente			:= (cAliTmp)->A1_NOME
	::dtListaPrjCoord[nPosIte]:DescricaoProjeto	:= (cAliTmp)->PE5_DESPRO
	::dtListaPrjCoord[nPosIte]:DescricaoFrente	:= (cAliTmp)->PE8_DESCR
	::dtListaPrjCoord[nPosIte]:MotivoProjeto   	:= (cAliTmp)->PE5_CODMOT 
	::dtListaPrjCoord[nPosIte]:MotivosValidos  	:= Alltrim((cAliTmp)->PE0_TPSOS)

	(cAliTmp)->(dbSkip())

EndDo
U_xCONOUT("TSRVS001","GETLISTAPROJETOCOORDE","RETORNO","")
Return .T.


//-------------------------------------------------------------------
/*/{Protheus.doc} BrwSegCli (TSRVS001)
Retorna lista segmentos do cliente  
<AUTHOR>
@since 23/09/2015 
@param Segmento, caracter, codigo do segmento
@return dtSegmentoCliente, estrutura, dados do segmento do cliente (codigo, descricao)
@obs se passar produto vazio e segmento vazio lista todos os grupos, passando um ou outro faz o filtro  
/*/
//-------------------------------------------------------------------

WSMETHOD BrwSegCli WSRECEIVE Segmento WSSEND dtSegmentoCliente WSSERVICE TSRVS001

Local cAliTmp := GetNextAlias()
Local cQryTmp := ""
Local nConTmp := 0

Default ::Segmento := ""

::dtSegmentoCliente := {}
U_xCONOUT("TSRVS001","BRWSEGCLI","REQUISICAO","")

cQryTmp := " SELECT AOV_CODSEG, AOV_DESSEG " 
cQryTmp += "   FROM " +RetSQLName('AOV') + " AOV " 
cQryTmp += "  WHERE AOV_FILIAL = '" + xFilial('AOV') + "' "
If ! Empty(::Segmento)
	cQryTmp += " AND AOV_CODSEG = '" + ::Segmento + "' "
Endif	 
cQryTmp += "    AND AOV.D_E_L_E_T_ = ' ' " 

cQryTmp := ChangeQuery(cQryTmp)

dbUseArea(.T., 'TOPCONN', TcGenQry(,,cQryTmp), cAliTmp, .F., .T.)

(cAliTmp)->(dbGoTop())

If (cAliTmp)->(Eof())
	SetSoapFault("Segmento Cliente","Sem segmentos para listar.")
	(cAliTmp)->(dbCloseArea())
	U_xCONOUT("TSRVS001","BRWSEGCLI","(SEM SEGMENTOS PARA LISTAR) - RETORNO","")
	Return .F.
EndIf

While ! (cAliTmp)->(Eof())

	aAdd(::dtSegmentoCliente , WsClassNew("stSegmentoCliente"))

	nConTmp := Len(::dtSegmentoCliente)

	::dtSegmentoCliente[nConTmp]:Codigo	:= (cAliTmp)->AOV_CODSEG 
	::dtSegmentoCliente[nConTmp]:Descricao	:= (cAliTmp)->AOV_DESSEG

	(cAliTmp)->(dbSkip())

EndDo

(cAliTmp)->(dbCloseArea())
U_xCONOUT("TSRVS001","BRWSEGCLI","RETORNO","")
Return .T.


/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºPrograma  ³ PMO01Prc    º Autor ³ Moisés Osti        º Data ³ 14/06/2016  º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºDescricao ³ Gerar os pedidos de venda através dos chamados/tickets        º±±
±±º          ³                                                               º±±
±±ÈÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
// Método e estrutura copiados do fonte wspvsuptectarifado.prw que estava no
// chamado TTAYLR
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
WSMETHOD INCPVSUPTRF WSRECEIVE DadosPv WSSEND cRetInfo WSSERVICE TSRVS001

//Function PMO01Prc(aGravacao)
Local aItens		:= {} //Itens PV (SC6)
Local aCab			:= {} //Cabeçalho do PV (SC5)
Local _cTes			:= "" //TES
Local _cCondPg		:= ALLTRIM(GetMv("TI_CONCHTK",,"019")) //Condição de pagamento
Local _cMoeda		:= Val(GetMv("TI_MOECHTK",,"1"))	//Moeda utilizada para o Pedido de Venda
Local _cMenNota		:= ALLTRIM(GetMv("TI_NOTCHTK",,"Pedido de venda gerado a partir do(s) chamado(s): ")) //Descrição da Nota PV
Local cRecISS		:= "N"
Local _cCposEsp		:= GetMv("MV_#CRP01A",,"06/43")
Local dVencto
Local cNatureza		:= ""
Local cTpCliVip		:= GetMV("TI_TPCLIVP",,"4")
Local cPrdScc		:= GetMv("TI_PRDSCC",,"2010105-1;2010103-9;2010106-8") //Produtos sem Centro de Custo
Local cC6_CC	:= ""
Local _cTime		:= Time() //Tempo do processamento com TimeOut definido em inicialmente em 60 Segundos
Local _lSincrono	:= getMV("TI_#TPVTKT",,.F.) // tipo de pedido de venda de ticket (.T.=sincrono, .F.=assincrono)
Local cIdTicket		:= ""
//Local aSM0Area 
Local lCliente  := .F.
Local lFranquia := .F.
Local lValFranquia := GetMV("TI_TIFATFR",,.T.) // Faturamento para franquia 
Local aAreaSA1 := {}
Local aAreaSB1 := {}
Local _cEmpPed := ""
Local _cFilPed := ""

//Private para nao solicitar moeda no GetMoeda() se for preciso chamar o U_TDIPrepEnv() na U_TFATA012()
Private xNaoGetMoeda := .T.

//ÉÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ»
//º RECEBE OS DADOS ENVIADOS	º
//ÈÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼
_cEmpPed	:= ALLTRIM(DadosPv:cEmpMax)
_cFilPed	:= ALLTRIM(DadosPv:cFilMax)
cCliente	:= ALLTRIM(DadosPv:cCliente)
cCodProduto	:= ALLTRIM(DadosPv:cProduto)
nVlHora		:= DadosPv:nVlHora
dDataAt		:= DadosPv:dDataPV
cHoraT		:= DadosPv:cHrTotal
U_xCONOUT("TSRVS001","INCPVSUPTRF","REQUISICAO","")

//Posiciona na empresa correta
//aSM0Area := SM0->(GetArea())
SM0->(DbSetOrder(1))
SM0->(DbSeek(_cEmpPed + _cFilPed))

if ! _lSincrono
	cIdTicket := ALLTRIM(DadosPv:cIdTicket)
endIf

//Valida campo de Hora
If !(":" $ cHoraT)
	cRetInfo:lGravou	:= .F.
	cRetInfo:cMsg		:= "Total de horas inválido, por favor informe as horas no formato HH:MM"
	U_xCONOUT("TSRVS001","INCPVSUPTRF","(TOTAL DE HORAS INVALIDO, POR FAVOR INFORME AS HORAS NO FORMATO HH:MM) - RETORNO","")
	Return .T.
EndIf

//Validação de usuário Totvs12
PswOrder(2)
If(PswSeek(ALLTRIM(DadosPv:cUserMxm)))
	
	__CUSERID 	:= PswID() // RECUPERA ID DO USUÁRIO INFORMADO
	aUsers		:= FWSFLoadUser(__CUSERID)
	
Else
	cRetInfo:lGravou	:= .F.
	cRetInfo:cMsg		:= "Não foi possível se conectar com usuário: " + DadosPv:cUserMxm + " Usuário ou senha inválidos"
	U_xCONOUT("TSRVS001","INCPVSUPTRF","(NAO FOI POSSIVEL SE CONECTAR COM USUARIO '"+ DadosPv:cUserMxm +"' .USUARIO OU SENHA INVALIDOS) - RETORNO","")
	Return .T.
EndIf

//AREA PARA VERIFICAÇÃO DO CLIENTE SA1=================================================================================================
DbSelectArea("SA1")
SA1->(dbGoTop())

cQuery := "SELECT R_E_C_N_O_ RECNO FROM " + RetSqlName("SA1") + " WHERE A1_COD ='" + SUBSTR(cCliente,1,6) + "' AND A1_LOJA ='" + SUBSTR(cCliente,7,2) + "'"
cQuery := ChangeQuery(cQuery)

TCQuery cQuery New Alias "CSA1"
CSA1->(DbGoTop())

If ! CSA1->(EOF())
	SA1->(dbGoTo(CSA1->RECNO))
	lCliente := .T.

Elseif lValFranquia .AND. ! lCliente

   DbSelectArea("ADK")
   ADK->(dbSetOrder(1))
   If ADk->(dbSeek(xFilial('ADK')+cCliente))
      
	  SA1->(dbSetOrder(3))
	  If SA1->(dbSeek(xFilial('SA1')+ADK->ADK_CNPJ))
            SA1->( dbGoTo( SA1->(Recno()) ) )
	    	lFranquia := .T.
      EndIf

	EndIf    
EndIf

 
IF ! lCliente .AND. ! lFranquia

 	CSA1->(dbCloseArea())
	cRetInfo:lGravou	:= .F.
	cRetInfo:cMsg		:= "Cliente " + cCliente + " inexistente"
	U_xCONOUT("TSRVS001","INCPVSUPTRF","(CLIENTE OU FRANQUIA '"+ cCliente +"' INEXISTENTE) - RETORNO","")
	Return .T.

ENDIF	
//====================================================================================================================================

//Guarda a area do cliente posicionado
aAreaSA1 := SA1->(GetArea())

//SE CLIENTE É PRIVATE E AS FILIAIS VIER DE (JOINVILLE OU SANTA CATARINA) ENTÃO SETA PARA FILIAL ALPHAVILLE
If (SA1->A1_XCLIVIP $ cTpCliVip) .AND. (_cFilPed == '00001001700' .OR. _cFilPed == '00001000600')
	_cEmpPed	:= SuperGetMv("TI_FATEMPM",,"00")
	_cFilPed	:= SuperGetMv("TI_FATFILM",,"00001001000")
EndIf

CSA1->(dbCloseArea())
//TRATAMENTO DE QUANTIDADES PARA EVITAR CORTE QUANDO MAIOR QUE 100 HORAS

_qtdVen := Round(VAL(STRTOKARR(cHoraT,":")[1]) + HoraToInt("00:" + STRTOKARR(cHoraT,":")[2] ),4)
	
//TES padrao de faturamento de parcelas
_cTES := U_TFATA019(SA1->A1_COD, SA1->A1_LOJA,cCodProduto)

DbSelectArea("SE4")
DbSetOrder(1)
If !dbSeek(xFilial("SE4")+_cCondPg,.F.)
	cRetInfo:lGravou	:= .F.
	cRetInfo:cMsg		:= "Atencao ... Condicao de Pagamento nao encontrada, Codigo"  + _cCondPg + " .Verifique !!!"
	U_xCONOUT("TSRVS001","INCPVSUPTRF","(ATENCAO ! CONDICAO DE PAGTO NAO ENCONTRADA, CODIGO '"+ _cCondPg + "' .Verifique !) - RETORNO","")
	Return .T.
Endif

//Ajuste moeda para MI
if _cEmpPed <> "00" 
	_cMoeda	:= Val(GetMv("TI_MOECHTK",,"2"))	//Moeda utilizada para o Pedido de Venda no MI
ENDIF

//ANALISA ISS
If cPaisLoc == "BRA" //ajuste feito por solicitacao da Franquia de Curitiba
	cIss := If(GetMV("MV_INCISS",,.T.),"S","N")
ElseIf _cEmpPed+_cFilPed == "0000001000600"
	cIss := "S"
Else
	cIss := "N"
EndIf

//ANALISA COFINS
cCof := GetMV("MV_AJUSCOF",,"N")
//-- tratamento para a opcao 0 - nao cobra impostos, somente ISS

//ANALISA RECOLHIMENTO DE ISS
If Alltrim(UPPER(SA1->A1_MUN)) $ GetMv("MV_MUNISS",,"")
	cRecISS := SA1->A1_RECISS
Else
	cRecISS := "2"
EndIf

dVencto	:= DataValida(dDataAt + GetMv("MV_#VPSSIM",,20)) //Soma fixo 20 dias na data de geracao e traz o primeiro dia util apos a data, caso seja fim de semana
dVencto	:= PMOA02RtDt(Day(dVencto),Month(dVencto),Year(dVencto)) //Monta data de vencimento com dia do contrato e mes atual
nDia	:= Day(dVencto) 
nMes	:= Month(dVencto) // atualiza mes pois data pode ter cido alterada
nAno	:= Year(dVencto)  // atualiza mes pois data pode ter cido alterada

// Se o vencimento do contrato for menor que a data de geracao, joga para o mes seguinte
If dVencto < dDataBase
	nMes    := nMes + 1
	dVencto := PMOA02RtDt(nDia,nMes,nAno)
	nMes    := Month(dVencto) // atualiza mes pois data pode ter sido alterada
	nAno    := Year(dVencto)  // atualiza mes pois data pode ter sido alterada
Endif	 

dbSelectArea("SB1")
dbSetOrder(1)
If SB1->(dbSeek(xFilial("SB1") + ALLTRIM(cCodProduto)))

	//Guarda a area do produto posicionado
	aAreaSB1 := SB1->(GetArea())
	
	//CALCULA ALIQUOTA E VALOR BRUTO
	nAliq := U_TFATA012(cCodProduto,"D ", _cEmpPed, _cFilPed)

	//Restaura o posicionamento da SA1 e SB1 apos execucao da TFATA012
	SA1->(RestArea(aAreaSA1))
	SB1->(RestArea(aAreaSB1))
	
	nVlrHrConv := nVlHora / nAliq
	nVlrHrConv := A410Arred(nVlrHrConv,"C6_VALOR")
			
	//GRUPO DE PRODUTO DA BPO RECOLHE ISS SEMPRE "SIM"
    If cRecISS <> "1" .AND. SB1->B1_GRUPO $ GETMV("MV_#GRPROD",,"4001")
    	cRecISS := "1"
    EndIf
	
	//RECUPERA NATUREZA CADASTRADA PARA O CLIENTE + PRODUTO
	cNatureza := U_TFATA018(SA1->A1_COD, SA1->A1_LOJA, SB1->B1_COD)
	
	nItem	:= 1
	cItPV 	:= StrZero(nItem,TamSX3("C6_ITEM")[1])	
	
	If !(cCodProduto $ cPrdScc)
	
		//RECUPERA O CENTRO DE CUSTO DE ACORDO COM A FILIAL LOGADA
		cQueryZX5	:= "SELEC ZX5_CHAVE2, ZX5_DESCRI FROM " + RetSqlName("ZX5") + " WHERE ZX5_TABELA = 'ATCCTR' AND ZX5_CHAVE2 ='" + _cFilPed + "'"
		cQueryZX5 := ChangeQuery(cQueryZX5)
		
		TCQuery cQueryZX5 New Alias "FATC"
		FATC->(DbGoTop())
		If !FATC->(EOF())
			aCC			:= STRTOKARR(FATC->ZX5_DESCRI,"=")
			cC6_CC	:= aCC[2]
		EndIf
		FATC->(dbCloseArea())
		
	Else
		
		//RECUPERA O CENTRO DE CUSTO DE ACORDO COM O PRODUTO INFORMADO
		cQueryZX5	:= "SELEC ZX5_CHAVE2, ZX5_DESCRI FROM " + RetSqlName("ZX5") + " WHERE ZX5_TABELA = 'ATCCT2' AND ZX5_CHAVE2 ='" + cCodProduto + "'"
		cQueryZX5 := ChangeQuery(cQueryZX5)
		
		TCQuery cQueryZX5 New Alias "FATC"
		FATC->(DbGoTop())
		If !FATC->(EOF())
			aCC			:= STRTOKARR(FATC->ZX5_DESCRI,"=")
			cC6_CC	:= aCC[2]
		EndIf
		FATC->(dbCloseArea())
		
	EndIf
	
	AAdd(aItens,{{"C6_FILIAL"	,_cFilPed			,Nil},;	// Filial
				 {"C6_ITEM"		,cItPV				,Nil},;	// Item
				 {"C6_CLI"		,SA1->A1_COD		,Nil},;	// Cliente
				 {"C6_LOJA"		,SA1->A1_LOJA		,Nil},;	// Cliente
				 {"C6_DESCRI"	,SB1->B1_DESC		,nil},;	// Descricao Produto
				 {"C6_PRODUTO"	,cCodProduto		,nil},;	// Cod. Produto					 
				 {"C6_QTDVEN"	,_qtdVen			,nil},;	// Quantidade Venda
				 {"C6_PRCVEN"	,nVlrHrConv			,nil},;	// Preco Unit.
				 {"C6_PRUNIT"	,nVlrHrConv			,nil},;	// Preco Item
				 {"C6_TES"		,_cTES				,nil},;	// Tipo de Saida
				 {"C6_ENTREG"	,dDataBase			,nil},;	// Dt.Entrega
				 {"C6_QTDLIB"	,0					,nil},;	// Gerar Pedido Venda Bloqueado
				 {"C6_CC"	    ,cC6_CC			    ,nil},;	// Centro de Custo
				 {"C6_CCUSTO"   ,cC6_CC			    ,nil},;	// Centro de Custo a regra da tabela PIK 
				 {"C6_XFATOR"	,nAliq				,nil}})	// Aliquota
	
	Aadd(aCab,{"C5_FILIAL"	,_cFilPed			,Nil})
	AAdd(aCab,{"C5_TIPO"	,"N"				,nil})
	AAdd(aCab,{"C5_CLIENTE"	,SA1->A1_COD		,nil})
	AAdd(aCab,{"C5_LOJACLI"	,SA1->A1_LOJA		,nil})
	AAdd(aCab,{"C5_LOJAENT"	,SA1->A1_LOJA		,nil})
	AAdd(aCab,{"C5_TIPOCLI"	,SA1->A1_TIPO		,nil})
	AAdd(aCab,{"C5_CONDPAG"	,_cCondPg			,nil})
	AAdd(aCab,{"C5_CNDBKP"	,_cCondPg			,nil})
	AAdd(aCab,{"C5_MOEDA"	,_cMoeda			,nil})
	AAdd(aCab,{"C5_MENNOTA"	,StrTran(StrTran(_cMenNota + TRATACARAC(DadosPv:cObsNota),CHR(10),''),CHR(13),'')	,nil})
	AAdd(aCab,{"C5_INCISS"	,cIss				,nil})
	AAdd(aCab,{"C5_AJUSCOF"	,cCof				,nil})
	AAdd(aCab,{"C5_RECISS"	,cRecISS			,nil})
	AAdd(aCab,{"C5_PARC1"	,Round((nVlrHrConv * _qtdVen),2)	,nil}) //Arredonda para apenas duas casas decimais (Igual campo total C6_VALOR)
	AAdd(aCab,{"C5_DATA1"	,dVencto			,nil})
	AAdd(aCab,{"C5_NATUREZ"	,cNatureza			,nil})
	AAdd(aCab,{"C5_XMENOT4"	,cIdTicket			,nil})
	
	If _cEmpPed+_cFilPed $ _cCposEsp
		AAdd(aCab,{"C5_FECDSE" ,FirstDay(dDataBase)	,nil})
		AAdd(aCab,{"C5_FECHSE" ,LastDay(dDataBase)	,nil})
		AAdd(aCab,{"C5_TPVENT" ,"S"					,nil})
		
		//-------------------------------------------
		// Campos obrigatorios para NFe Argentina  //   
		//-------------------------------------------
		If SC5->(FieldPos("C5_INCOTER")) > 0
			Aadd(aCab,{"C5_INCOTER"	,GetMv("MV_#AGINCO",,"EXW")	,nil})
		EndIf
		If SC5->(FieldPos("C5_IDIOMA")) > 0	
			Aadd(aCab,{"C5_IDIOMA"	,GetMv("MV_#AGIDIO",,"1")	,nil})
		EndIf
		If SC5->(FieldPos("C5_PAISENT")) > 0
			Aadd(aCab,{"C5_PAISENT"	,AllTrim(SA1->A1_PAIS)		,nil})
		EndIf
	EndIf
	
	//PE PARA VERIFICAR SE PODE GRAVAR O PEDIDO
	/*If ExistBlock("PMO01WSA")
		aRetorno := ExecBlock("PMO01WSA",.F.,.F.,{})
		If ValType(aRetorno) == "A"
			If Len(aRetorno) == 2
				If ValType(aRetorno[1]) == "L" .And. ValType(aRetorno[2]) == "C"
					cMsg := aRetorno[2]
					If ! aRetorno[1]						
						cRetInfo:lGravou	:= .F.
						cRetInfo:cMsg		:= "Erro ao gravar Pedido de Venda para o cliente " + SA1->A1_COD
						Return .T.
					Endif
				Endif
			Endif
		Endif
	Endif*/
	
	If !Empty(aCab) .AND. !Empty(aItens)
		
		//Funcao que tem por objetivo, verificar se ja existe Pedido de Venda
		//gravado na tabela de Pedidos com a sequencia seguinte a ser utilizado (hardlock)
		//Solucao preventiva ativada atraves do parametro abaixo.
		/*If GetMV("MV_#HLCKPE",,.F.)
			U_NextHLock("SC5","C5_NUM",1,.T.)
		EndIf*/
		
		_cUserPed := aUsers[3]
		if _lSincrono
			aRet := StartJob("U_FATTICKET", GetEnvServer(), .T.,_cEmpPed,_cFilPed,_cUserPed,DadosPv:cPswMxm,nVlrHrConv,aCab,aItens,_cTime,_lSincrono,cIdTicket,.T.)
		else
            U_FFTLog(_cEmpPed,_cFilPed,_cUserPed,DadosPv:cPswMxm,nVlrHrConv,aCab,aItens,cIdTicket)
			//StartJob("U_FATTICKET", GetEnvServer(), .F.,_cEmpPed,_cFilPed,_cUserPed,DadosPv:cPswMxm,nVlrHrConv,aCab,aItens,_cTime,_lSincrono,cIdTicket,.T.)
			aRet := {.T., "Enviado para geracao o pedido do ticket Id "+cIdTicket, 0}
		endIf
		
		
		//Estrutra de array no retorno da função U_FATTICKET
		If ValType(aRet) == "A"
			If aRet[1]
				cRetInfo:lGravou	:= aRet[1]
				cRetInfo:cMsg		:= aRet[2]
				cRetInfo:nValorHora	:= aRet[3]
			Else
				cRetInfo:lGravou	:= aRet[1]
				cRetInfo:cMsg		:= aRet[2]
			EndIf
		EndIf
		
	EndIf
Else
	cRetInfo:lGravou	:= .F.
	cRetInfo:cMsg		:= "Atencao ... Produto "  + ALLTRIM(cCodProduto) + " não encontrado. Verifique !!!"
	U_xCONOUT("TSRVS001","INCPVSUPTRF","("+cRetInfo:cMsg+") - RETORNO","S")
EndIf
U_xCONOUT("TSRVS001","INCPVSUPTRF","RETORNO","")
Return .T.


/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºPrograma  ³ PMOA02RtDt  º Autor ³                    º Data ³ 21/06/2016  º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºDescricao ³ Funcao retornar data a partir do dia,mes,ano ajustando ultimo º±±
±±º          ³ dia do mes                                                    º±±
±±ÈÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
Static Function PMOA02RtDt(nDiaP,nMesP,nAnoP)

Local dDataRet

// valida dia
If nDiaP < 0 .Or. nDiaP > 31
	nDiaP := Day(dDataBase)
Endif
             
// valida mes
If nMesP > 12
	nMesP := 1
	nAnoP := nAnoP + 1
Endif	

dDataRet := CtoD( StrZero(nDiaP,2) + "/" + StrZero(nMesP,2) + "/" + StrZero(nAnoP,4) )

If Empty(dDataRet) // data invalida - pegar ultimo dia do mes   
    // pega primeiro data do proximo mes
	nDiaP := 1
	nMesP := nMesP + 1
	If nMesP > 12
		nMesP := 1
		nAnoP := nAnoP + 1
	Endif                   
	// tira 1 dia da primeira data do mes seguinte para saber qual a ultimo 
	// dia do mes do vencimento
	dDataRet := CtoD( StrZero(nDiaP,2) + "/" + StrZero(nMesP,2) + "/" + StrZero(nAnoP,4) ) - 1
Endif

Return dDataRet

//-------------------------------------------------------------------
/*/{Protheus.doc} CONFRDA (TSRVS001)
Conferencia RDA
<AUTHOR> fernandes
@since 28/07/2016
@param Tecnico, caracter, codigo do tecnico
@param DataDe, data, data inicial do periodo
@param DataAte, data, data final do periodo
@return ConfRDARet, array, estrutura dos dados
/*/
//-------------------------------------------------------------------

WSMETHOD CONFRDA WSRECEIVE Tecnico, dtConfDe, dtConfAte WSSEND ConfRDARet WSSERVICE TSRVS001
            
Local lRet			:= .T.
Local cAliasQry		:= getNextAlias()
Local cAliasQry1	:= getNextAlias()
Local cAliasQry2	:= getNextAlias()
Local nTamCodDes	:= TamSx3('PFM_CODDES')[1]
Local nTamClient	:= TamSx3('PF9_CLIENT')[1]
Local cDespDe		:= space(nTamCodDes)
Local cDespAte		:= ''
Local nI			:= 0
Local nItem			:= 0
Local TOTGERDESPE	:= 0
Local TOTGERREEMB	:= 0
Local TOTSESDESPE	:= 0
Local TOTSESREEMB	:= 0
Local nPosMesAno	:= 0
Local cCodTec		:= ::Tecnico
Local dDtIni		:= ::dtConfDe
Local dDtFim		:= ::dtConfAte
Local cCliDe		:= space(TamSx3('PF9_CLIENT')[1])
Local cCliAte		:= ''
Local cUnSrv		:= ''
Local cMesAno		:= ''
Local cAux			:= ''
Local cWhere		:= "%PFM.PFM_CODFEC = ' '%"
Local cOrdem		:= "%PFM.PFM_TECNIC, PFM.PFM_DATA%"
Local aTOTSESSAO	:= {}
Local nLimRda    := GetMV("TI_#154LIM",,60)  // quantos dias para traz deve considerar as despesas de RDA para fechar
Local cTipo      := Upper(GetMV("TI_#154TIP",,"M")) // tipo de controle do fechamento M (mensal) ou D (diario) 
Local dDataLim   := CtoD("")

U_xCONOUT("TSRVS001","CONFRDA","REQUISICAO","")

//-- obs: se mudar esta regra mudar tambem no relatorio TSRVR155
If cTipo == "D"
	dDataLim := dDtFim - nLimRda
ElseIf cTipo == "M"
	dDataLim := FirstDay( LastDay(dDtFim) - nLimRda )
Endif

// A DIFERENÇA DESSE METODO PARA O RELATORIO TSRVR155
// todas unidades de serviços

for nI := 1 to nTamCodDes
	cDespAte += 'Z'
next nI

for nI := 1 to nTamClient
	cCliAte += 'Z'
next nI
	
	
	// QUERY PRINCIPAL
	BeginSql Alias cAliasQry
		
		SELECT PFM.PFM_TECNIC, RD0.RD0_NOME,   PFM.PFM_DATA,  PFM.PFM_IDOS,   PF9.PF9_CLIENT, PF9.PF9_LOJA, RD0.RD0_XUNSRV, 
		       SA1.A1_NREDUZ,  PFM.PFM_CODDES, PFG.PFG_DESCR, PFM.PFM_VLDESP, PFM.PFM_VLREEM,
		       CASE WHEN LENGTH(TRIM(PFM.PFM_KEYNDC)) + LENGTH(TRIM(PFM.PFM_PEDVEN)) > 0 THEN 'S' ELSE 'N' END AS PFM_FATUR,
		       CASE WHEN LENGTH(TRIM(PFM.PFM_CHVSE2)) > 0 THEN 'S' ELSE 'N' END as PFM_PAGO,
		       PFM.PFM_DTINCL, PFM.PFM_CODFEC
         FROM %table:PFM% PFM
          JOIN %table:RD0% RD0 ON RD0.RD0_FILIAL = %exp:xFilial("RD0")%
                              AND RD0.RD0_CODIGO = PFM.PFM_TECNIC
                              AND RD0.%NotDel%
          JOIN %table:PF9% PF9 ON PF9.PF9_FILIAL = %exp:xFilial("PF9")%
                              AND PF9.PF9_IDOS   = PFM.PFM_IDOS
                              AND PF9.%NotDel%                   
          JOIN %table:SA1% SA1 ON SA1.A1_FILIAL  = %exp:xFilial("SA1")%
                              AND SA1.A1_COD     = PF9.PF9_CLIENT
                              AND SA1.%NotDel%             
          LEFT JOIN %table:PFG% PFG ON PFG.PFG_FILIAL = %exp:xFilial("PFG")%
                              AND PFG.PFG_CODIGO = PFM.PFM_CODDES
                              AND PFG.%NotDel%                          
         WHERE PFM.PFM_FILIAL = %exp:xFilial("PFM")%
           AND PFM.PFM_DATA   BETWEEN %exp:dDataLim% AND %exp:dDtFim%
           AND PFM.PFM_TECNIC BETWEEN %exp:cCodTec% AND %exp:cCodTec%
           AND PF9.PF9_CLIENT BETWEEN %exp:cCliDe% AND %exp:cCliAte%
           //AND RD0.RD0_XUNSRV IN ( %Exp:StrTran(alltrim(cUnSrv),";","','")% )
           AND PFM.PFM_CODDES BETWEEN %exp:cDespDe% AND %exp:cDespAte%
           AND %exp:cWhere% 
           AND PFM.%notDel%
		 ORDER BY %exp:cOrdem%
	EndSql
	
	
	// QUERY TOTAL GERAL DESPESAS / TOTAL GERAL REEMBOLSO
	BeginSql Alias cAliasQry1
		
		SELECT SUM(PFM_VLDESP) AS TOTGERDESPE, SUM(PFM_VLREEM) AS TOTGERREEMB
         FROM %table:PFM% PFM
          JOIN %table:RD0% RD0 ON RD0.RD0_FILIAL = %exp:xFilial("RD0")%
                              AND RD0.RD0_CODIGO = PFM.PFM_TECNIC
                              AND RD0.%NotDel%
          JOIN %table:PF9% PF9 ON PF9.PF9_FILIAL = %exp:xFilial("PF9")%
                              AND PF9.PF9_IDOS   = PFM.PFM_IDOS
                              AND PF9.%NotDel%                   
          JOIN %table:SA1% SA1 ON SA1.A1_FILIAL  = %exp:xFilial("SA1")%
                              AND SA1.A1_COD     = PF9.PF9_CLIENT
                              AND SA1.%NotDel%             
          LEFT JOIN %table:PFG% PFG ON PFG.PFG_FILIAL = %exp:xFilial("PFG")%
                              AND PFG.PFG_CODIGO = PFM.PFM_CODDES
                              AND PFG.%NotDel%                          
         WHERE PFM.PFM_FILIAL = %exp:xFilial("PFM")%
           AND PFM.PFM_DATA   BETWEEN %exp:dDataLim% AND %exp:dDtFim%
           AND PFM.PFM_TECNIC BETWEEN %exp:cCodTec% AND %exp:cCodTec%
           AND PF9.PF9_CLIENT BETWEEN %exp:cCliDe% AND %exp:cCliAte%
           //AND RD0.RD0_XUNSRV IN ( %Exp:StrTran(alltrim(cUnSrv),";","','")% )
           AND PFM.PFM_CODDES BETWEEN %exp:cDespDe% AND %exp:cDespAte%
           AND %exp:cWhere% 
           AND PFM.%notDel%
		 ORDER BY %exp:cOrdem%
	EndSql
	
	if (cAliasQry1)->(!eof())
		TOTGERDESPE := (cAliasQry1)->TOTGERDESPE
		TOTGERREEMB := (cAliasQry1)->TOTGERREEMB
	endif
	(cAliasQry1)->(dbCloseArea())
	
	
	// QUERY TOTAL SESSAO DESPESAS / TOTAL SESSAO REEMBOLSO
	BeginSql Alias cAliasQry2
		
		SELECT SUBSTR(PFM.PFM_DATA,1,6) AS ANOMES, SUM(PFM_VLDESP) AS TOTSESDESPE , SUM(PFM_VLREEM) AS TOTSESREEMB
         FROM %table:PFM% PFM
          JOIN %table:RD0% RD0 ON RD0.RD0_FILIAL = %exp:xFilial("RD0")%
                              AND RD0.RD0_CODIGO = PFM.PFM_TECNIC
                              AND RD0.%NotDel%
          JOIN %table:PF9% PF9 ON PF9.PF9_FILIAL = %exp:xFilial("PF9")%
                              AND PF9.PF9_IDOS   = PFM.PFM_IDOS
                              AND PF9.%NotDel%                   
          JOIN %table:SA1% SA1 ON SA1.A1_FILIAL  = %exp:xFilial("SA1")%
                              AND SA1.A1_COD     = PF9.PF9_CLIENT
                              AND SA1.%NotDel%             
          LEFT JOIN %table:PFG% PFG ON PFG.PFG_FILIAL = %exp:xFilial("PFG")%
                              AND PFG.PFG_CODIGO = PFM.PFM_CODDES
                              AND PFG.%NotDel%                          
         WHERE PFM.PFM_FILIAL = %exp:xFilial("PFM")%
           AND PFM.PFM_DATA   BETWEEN %exp:dDataLim% AND %exp:dDtFim%
           AND PFM.PFM_TECNIC BETWEEN %exp:cCodTec% AND %exp:cCodTec%
           AND PF9.PF9_CLIENT BETWEEN %exp:cCliDe% AND %exp:cCliAte%
           //AND RD0.RD0_XUNSRV IN ( %Exp:StrTran(alltrim(cUnSrv),";","','")% )
           AND PFM.PFM_CODDES BETWEEN %exp:cDespDe% AND %exp:cDespAte%
           AND %exp:cWhere% 
           AND PFM.%notDel%
		 GROUP BY SUBSTR(PFM.PFM_DATA,1,6)
		 ORDER BY ANOMES
	EndSql
	
	while (cAliasQry2)->(!eof())
		cAux := right(alltrim((cAliasQry2)->ANOMES),2) + '/' + left(alltrim((cAliasQry2)->ANOMES),4)
		aadd(aTOTSESSAO,{ cAux,(cAliasQry2)->TOTSESDESPE,(cAliasQry2)->TOTSESREEMB })
		(cAliasQry2)->(dbSkip())
	end
	(cAliasQry2)->(dbCloseArea())
	
	
if (cAliasQry)->(eof()) 
	SetSoapFault("Conferência RDA","Analista sem RDA para conferência.")
	U_xCONOUT("TSRVS001","CONFRDA","(ANALISTA SEM RDA PARA CONFERENCIA) - RETORNO","")
	return .F.
endif

while (cAliasQry)->(!eof()) 

	aAdd(::ConfRDARet, WsClassNew('structConfRDA'))
	nItem := Len(::ConfRDARet)
	
	 TOTSESDESPE	:= 0
	 TOTSESREEMB	:= 0
	 nPosMesAno		:= 0
	 
	 cMesAno := StrZero(Month(stod((cAliasQry)->PFM_DATA)),2) + '/' + cvaltochar(Year(stod((cAliasQry)->PFM_DATA)))
	
	if len(aTOTSESSAO) > 0
		if AScan(aTOTSESSAO,{|x| x[1] == cMesAno}) > 0
			nPosMesAno := AScan(aTOTSESSAO,{|x| x[1] == cMesAno})
			TOTSESDESPE := aTOTSESSAO[nPosMesAno,2]
			TOTSESREEMB := aTOTSESSAO[nPosMesAno,3]
		endif
	endif

	::ConfRDARet[nItem]:MesAno			:=  cMesAno					
	::ConfRDARet[nItem]:Fechamento  	:= (cAliasQry)->PFM_CODFEC
	::ConfRDARet[nItem]:DataRDA			:= stod((cAliasQry)->PFM_DATA)	
	::ConfRDARet[nItem]:OrdemServico   	:= (cAliasQry)->PFM_IDOS
	::ConfRDARet[nItem]:Cliente        	:= (cAliasQry)->PF9_CLIENT
	::ConfRDARet[nItem]:Loja	       	:= (cAliasQry)->PF9_LOJA	
	::ConfRDARet[nItem]:NomeCliente    	:= (cAliasQry)->A1_NREDUZ	
	::ConfRDARet[nItem]:Despesa			:= (cAliasQry)->PFM_CODDES	
	::ConfRDARet[nItem]:Descricao		:= (cAliasQry)->PFG_DESCR
	::ConfRDARet[nItem]:ValorDespesa	:= Round((cAliasQry)->PFM_VLDESP,2)	
	::ConfRDARet[nItem]:ValorReemb		:= Round((cAliasQry)->PFM_VLREEM,2)	
	::ConfRDARet[nItem]:Fat				:= iif((cAliasQry)->PFM_FATUR == 'S','Sim','Não') 	
	::ConfRDARet[nItem]:Pag				:= iif((cAliasQry)->PFM_PAGO == 'S','Sim','Não')	
	::ConfRDARet[nItem]:UniSrv			:= (cAliasQry)->RD0_XUNSRV	
	::ConfRDARet[nItem]:TotSesDespe		:= Round(TOTSESDESPE,2)	
	::ConfRDARet[nItem]:TotSesReemb		:= Round(TOTSESREEMB,2)	
	::ConfRDARet[nItem]:TotGerDespe		:= Round(TOTGERDESPE,2)	
	::ConfRDARet[nItem]:TotGerReemb		:= Round(TOTGERREEMB,2)	


	(cAliasQry)->(dbSkip())

enddo

(cAliasQry)->(dbCloseArea())
U_xCONOUT("TSRVS001","CONFRDA","RETORNO","")
return lRet

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÍÍÍÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍËÍÍÍÍÍÍÑÍÍÍÍÍÍÍÍÍÍÍÍÍ»±±
±±ºPrograma  ³TRATACARACºAutor  ³Microsiga           º Data ³  11/17/09   º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÊÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºDesc.     ³                                                            º±±
±±º          ³                                                            º±±
±±ÌÍÍÍÍÍÍÍÍÍÍØÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¹±±
±±ºUso       ³ AP                                                         º±±
±±ÈÍÍÍÍÍÍÍÍÍÍÏÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍÍ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
STATIC FUNCTION TRATACARAC(cTEXTO)
Local cRet 		:= ""
Local cRetorno	:= ""
Local cChar		:= ""
Local nX		:= 0

cRet := AllTrim(cTEXTO)
cRet := StrTran(cRet, ";", ",")
cRet := StrTran(cRet, Chr(13), "")
cRet := StrTran(cRet, Chr(10), "")
cRet := StrTran(cRet, Chr(13) + Chr(10), "")


cRet := AnsiToOem(cRet)

cRet := StrTran(cRet, Chr(128), "C") //= €
cRet := StrTran(cRet, Chr(129), "u") //= ?
cRet := StrTran(cRet, Chr(130), "e") //= ‚
cRet := StrTran(cRet, Chr(131), "a") //= ƒ
cRet := StrTran(cRet, Chr(132), "a") //= „
cRet := StrTran(cRet, Chr(133), "a") //= …
cRet := StrTran(cRet, Chr(134), "a") //= †
cRet := StrTran(cRet, Chr(135), "c") //= ‡
cRet := StrTran(cRet, Chr(136), "e") //= ˆ
cRet := StrTran(cRet, Chr(137), "e") //= ‰
cRet := StrTran(cRet, Chr(138), "e") //= Š
cRet := StrTran(cRet, Chr(139), "i") //= ‹
cRet := StrTran(cRet, Chr(140), "i") //= Œ
cRet := StrTran(cRet, Chr(141), "i") //= ?
cRet := StrTran(cRet, Chr(142), "A") //= Ž
cRet := StrTran(cRet, Chr(143), "A") //= ?
cRet := StrTran(cRet, Chr(144), "E") //= ?

cRet := StrTran(cRet, Chr(147), "o") //= “
cRet := StrTran(cRet, Chr(148), "o") //= ”
cRet := StrTran(cRet, Chr(149), "o") //= •
cRet := StrTran(cRet, Chr(150), "u") //= –
cRet := StrTran(cRet, Chr(151), "u") //= —
cRet := StrTran(cRet, Chr(152), "y") //= ˜
cRet := StrTran(cRet, Chr(153), "O") //= ™
cRet := StrTran(cRet, Chr(154), "U") //= š
cRet := StrTran(cRet, Chr(159), "f") //= Ÿ

cRet := StrTran(cRet, Chr(161), "i") //= ¡
cRet := StrTran(cRet, Chr(162), "o") //= ¢
cRet := StrTran(cRet, Chr(163), "u") //= £
cRet := StrTran(cRet, Chr(164), "n") //= ¤
cRet := StrTran(cRet, Chr(165), "N") //= ¥

For nX := 1 To Len(cRet)
    cChar := Substr(cRet, nX, 1)
    If Asc(cChar) >= 32 .And. Asc(cChar) <= 126
    	cRetorno += cChar
    Else
    	cRetorno += " "
    EndIf
Next nX

If Empty(cRetorno)
	cRetorno := Space(1)
EndIf
RETURN(cRetorno)


Static Function noacento()

Local cChar  := ""
Local nX     := 0 
Local nY     := 0
Local cVogal := "aeiouAEIOU"
Local cAgudo := "áéíóú"+"ÁÉÍÓÚ"
Local cCircu := "âêîôû"+"ÂÊÎÔÛ"
Local cTrema := "äëïöü"+"ÄËÏÖÜ"
Local cCrase := "àèìòù"+"ÀÈÌÒÙ" 
Local cTio   := "ãõ"+"ÃÕ"
Local cCecid := "çÇ"
Local cDiversos	:= "'><ï»¿"
cDiversos += '"'
cString := STRTRAN(cString,"	","")
cString := STRTRAN(cString,"ï»¿","")
cString := STRTRAN(cString,"¿","")

For nX:= 1 To Len(cString)
	cChar:=SubStr(cString, nX, 1)
	IF cChar$cAgudo+cCircu+cTrema+cCecid+cTio+cCrase+cDiversos
		nY:= At(cChar,cAgudo)
		If nY > 0
			cString := StrTran(cString,cChar,SubStr(cVogal,nY,1))
		EndIf
		nY:= At(cChar,cCircu)
		If nY > 0
			cString := StrTran(cString,cChar,SubStr(cVogal,nY,1))
		EndIf
		nY:= At(cChar,cTrema)
		If nY > 0
			cString := StrTran(cString,cChar,SubStr(cVogal,nY,1))
		EndIf
		nY:= At(cChar,cCrase)
		If nY > 0
			cString := StrTran(cString,cChar,SubStr(cVogal,nY,1))
		EndIf		
		nY:= At(cChar,cTio)
		If nY > 0
			cString := StrTran(cString,cChar,SubStr("ao",nY,1))
		EndIf		
		nY:= At(cChar,cCecid)
		If nY > 0
			cString := StrTran(cString,cChar,SubStr("cC",nY,1))
		EndIf
		nY:= At(cChar,cDiversos)
		If nY > 0
			cString := StrTran(cString,cChar,SubStr("",nY,1))
		EndIf
	Endif
Next                                                                                                                                                      

For nX:=1 To Len(cString)
	cChar:=SubStr(cString, nX, 1)
	If Asc(cChar) <> 9 // Para não fubistituir tab por ponto
		If Asc(cChar) < 32 .Or. Asc(cChar) > 123 .Or. cChar $ '&'
			cString:=StrTran(cString,cChar,".")
		Endif
	EndIf 
Next nX

cString := _NoTags(cString)

Return cString

/*
	
*/
//-------------------------------------------------------------------
/*/{Protheus.doc} FATTICKET (TSRVS001)
Gera pedido de venda para os tickets a faturar enviados pelo Zendesk.
<AUTHOR> osti
@since 24/01/2018
@param _cEmpPed,_cFilPed, caracter, Empresa e Filial que será aberto o pedido de venda
@param _cUserPed, caracter, Usuário que irá gravar no pedido
@param _cPswPad, caracter, Senha para se autenticar no ambiente
@param nVlrHrConv, numerico, Valor hora do pedido a faturar
@param aCab, aItens, array, Cabeçalho e Itens do pedido
@return ConfRDARet, array, estrutura dos dados
/*/
User Function FATTICKET(_cEmpPed,_cFilPed,_cUserPed,_cPswPad,nVlrHrConv,aCab,aItens,_cTime,_lSincrono, cIdTicket, _lJob)
Local _aRet := {.F.,"",nVlrHrConv,cIdTicket,.F.}
Local _lRestOk := .F.

Default _lJob := .T.

ConOut("FatTicket: INICIO")

If _lJob 
    RpcClearEnv()
    RpcSetType(3)
    _lAmbiente := RpcSetEnv(_cEmpPed,_cFilPed,_cUserPed,_cPswPad,,GetEnvServer(),{ })
Else
    _lAmbiente := .T.
EndIf

If _lAmbiente

	ConOut("FatTicket: AMBIENTE OK")

	//Altera variável que armazena usuário para incluir pedido no nome dele
	cUsername := _cUserPed
	lMsHelpAuto := .T.                                         
	lMsErroAuto := .F.
	
	Begin Transaction

		MSExecAuto({|x,y,z| Mata410(x,y,z)},aCab,aItens,3)
		
		If lMsErroAuto

			ConOut("FatTicket: ERRO NA GRAVACAO DO PEDIDO")

			_aRet := {}
			AADD(_aRet,.F.)
			AADD(_aRet,"Erro ao gravar Pedido de Venda para o cliente " + SA1->A1_COD	+ " |DETALHES|" + U_SrvXGetMostraErro())
			AADD(_aRet,nVlrHrConv)
			AADD(_aRet,cIdTicket)
			
			DisarmTransaction()
			RollBackSX8()

			_lRestOk := .F.
			If ! _lSincrono
				// chama API REST para retornar erro na geracao do pedido
				cRet := u_AtuTicket(cIdTicket,_aRet)
				If Alltrim(Upper(cRet)) <> "OK"
					_lRestOk := .F.
					ConOut("FatTicket: ERRO NO REST 1: "+cRet)
				Else	
					_lRestOk := .T.
				Endif	
			Endif		

			AADD(_aRet,_lRestOk)
			
		Else

			ConOut("FatTicket: PEDIDO " + SC6->C6_NUM + " GERADO")

			_aRet := {}
			AADD(_aRet,.T.)
			AADD(_aRet,"PV:" + SC6->C6_NUM + " gerado ")
			AADD(_aRet,nVlrHrConv)
			AADD(_aRet,cIdTicket)

			_lRestOk := .F.
			If ! _lSincrono
				// chama API REST para retornar numero do pedido gerado
				cRet := u_AtuTicket(cIdTicket,_aRet)
				If Alltrim(Upper(cRet)) <> "OK"
					ConOut("FatTicket: ERRO NO REST 2: "+cRet)
					DisarmTransaction()
					RollBackSX8()
					_aRet := {}
					AADD(_aRet,.F.)
					AADD(_aRet,cRet)
					AADD(_aRet,nVlrHrConv)
					AADD(_aRet,cIdTicket)
				Else
					_lRestOk := .T.	
				Endif
			Endif

			AADD(_aRet,_lRestOk)

		EndIf
		
		If _lSincrono

			If ElapTime(_cTime,Time()) >= GetMv("TI_FTICKET",,"00:00:27")
				ConOut("FatTicket: TIMEOUT")
				//Realiza rollback da transação
				DisarmTransaction()
				RollBackSX8()
				_aRet := {}
				AADD(_aRet,.F.)
				AADD(_aRet,"TimeOut MSExecAuto")
				AADD(_aRet,nVlrHrConv)
				AADD(_aRet,cIdTicket)
				AADD(_aRet,_lRestOk)
			EndIf

		EndIf

	End Transaction
Else
	_aRet := {}
	AADD(_aRet,.F.)
	AADD(_aRet,"Erro ao logar no ambiente Totvs12 Empresa: " + _cEmpPed + " Filial: " + _cFilPed + " Usuário: " + _cUserPed)
	AADD(_aRet,nVlrHrConv)
	AADD(_aRet,cIdTicket)
	_lRestOk := .F.
	If ! _lSincrono
		ConOut("FatTicket: ERRO AO LOGAR NO AMBIENTE - Empresa: " + _cEmpPed + " Filial: " + _cFilPed + " Usuário: " + _cUserPed)
		// como nao conseguiu se logar na matriz como administrador
		RpcClearEnv()
		RpcSetType(3)
		_lAmbiente := RpcSetEnv("00","00001000100",,,,GetEnvServer(),{})
		If _lAmbiente
			// chama API REST para retornar erro na geracao do pedido
			cRet := u_AtuTicket(cIdTicket,_aRet)
			If Alltrim(Upper(cRet)) <> "OK"
				ConOut("FatTicket: ERRO NO REST 3: "+cRet)
			Else
				_lRestOk := .T.	
			Endif	
		Else	
			ConOut("FatTicket: ERRO AO LOGAR NO AMBIENTE - Empresa: 00 Filial: 00001000100 Usuário: ADMIN")
		Endif	
	Endif		
	AADD(_aRet,_lRestOk)
EndIf

ConOut("FatTicket: FIM")

Return _aRet

//-------------------------------------------------------------------
/*/{Protheus.doc} AtuTicket
Chama metodo para gravar o numero do pedido gerado no ticket - REST
<AUTHOR>
@since      11/03/19
/*/
//-------------------------------------------------------------------
User Function AtuTicket(cIdTicket, _aDados) 

Local aHeader	  := {} 
Local aLog        := {}
Local cRet        := ''
Local aResponse   := {}
Local cRetJson    := ''
Local _cToken      := U_TokenZendesk()
Local nTimeOut    := GetMV('TI_TOUTTKT',,008)
// producao
Local cURLTicket  := Alltrim(GetMV("TI_#URLTKT",,"https://mid.totvs.com:8086/"))      // caminho do WS a ser chamado para gravar o numero do pedido
Local cPathTicket := Alltrim(GetMV("TI_#PATTKT",,""))            			 		  // path a ser chamado para gravar o numero do pedido
// teste
// Local cURLTicket  := Alltrim(GetMV("TI_#URLTKT",,"http://10.171.31.11:4015/"))    // caminho do WS a ser chamado para gravar o numero do pedido
// Local cPathTicket := Alltrim(GetMV("TI_#PATTKT",,""))                             // path a ser chamado para gravar o numero do pedido
Local cMethod     := "finalizarFaturamentoLote"                                      // nome do metodo a ser utilizado
Local cJson       := ''
Local cFieldID    := 'cRet' // campo com o retorno do metodo 
Local lTimeOut    := .F.
Local oRest 	  := NIL
Local oObjJson    := NIL 
Local objError    := NIL
Local oReq        := NIL
Local oJson		  := FWJsonObject():New()
Local cTexto 	  := ""
Local oRetJSon    := JsonUtil():New()


ConOut("AtuTicket: INICIO")

// prod    url https://mid.totvs.com:8086/finalizarFaturamentoLote
// homolog url https://mid-homolog.totvs.com:4011/api/v1/faturamento/finalizarFaturamentoLote
// dev     url http://10.80.15.23:3000/finalizarFaturamentoLote
oRest := FWRest():New( cURLTicket )

aadd(aHeader, "Content-Type: application/json")
aadd(aHeader, "Accept: application/json")
aadd(aHeader, "Authorization: Bearer "+ _cToken)

cTexto := U_LimpaTxt(_aDados[2])

oRetJson:PutVal("CIDTICKET" , cIdTicket )
oRetJson:PutVal("LGRAVOU"   , IIf(_aDados[1],'true','false') )
oRetJson:PutVal("CMSG"      , cTexto )
oRetJson:PutVal("NVALOHORA" , Alltrim(Str(_aDados[3])) )

cJson := oRetJson:ToJson()

oRest:setPath( cPathTicket + cMethod )
oRest:SetPostParams( cJson )

if lTimeOut
	oRest:nTimeOut := nTimeOut
endif

ConOut("AtuTicket: URL = "+cURLTicket)
ConOut("AtuTicket: PATH = "+cPathTicket)
ConOut("AtuTicket: METODO = "+cMethod)
ConOut("AtuTicket: CJSON = "+cJson)

if oRest:POST(aHeader)
	if oJson:Activate(alltrim(oRest:GetResult()))
		if !empty(cFieldID)
			oJson:GetValueStr(@cRet, cFieldID )
			if cRet == nil
				cRet := 'Error: Sem resultado'
			endif
		else
			cRet := 'Error: Falha no retorno do metodo'
		endif
	else
		cRet := 'Error: Falha no parser do Json'
	endif
else
	// GRAVA A FALHA QUE HOUVE NA INTEGRACAO 
	if alltrim( oRest:GetLastError() ) != '204 NoContent' .and. alltrim( oRest:GetLastError() ) != '200 OK'
		cRet := 'Error: Falha na gravacao no Zendesk'
		if fWJsonDeserialize(alltrim(oRest:GetResult()),@objError)
			if type("objError:Fault") <> 'U'
				cRet += 'Error' + objError:Fault:Description + ' ' + objError:Fault:Message
			elseif type("objError:Error") <> 'U'
				cRet += 'Error' + objError:Error:Message
			else
				if !empty(oRest:cResult)
					if !( fWJsonDeserialize(alltrim(oRest:cResult),@oReq) )
						cRet += 'Error: Falha no destrinchamento do Json'
					else
						if '{"error":{' $ oRest:cResult
							cRet += 'Error: ' + oReq:error:message
						elseif '{"Fault":{' $ oRest:cResult
							cRet += 'Error: ' + oReq:fault:faultcode + ' ' + oReq:fault:faultstring
						else
							cRet += 'Error: ' + oRest:GetLastError()
						endif
					endif
				else
					cRet += ' ' + oRest:GetLastError()
				endif
			endif
		else
			cRet += ' ' + oRest:GetLastError()
		endif
	endif
endif

If Empty(cRet)
	cRet := "OK"
Endif

ConOut("AtuTicket: CRET = "+cRet)
ConOut("AtuTicket: FIM")

Return(cRet)


//-------------------------------------------------------------------
/*/{Protheus.doc} FFTLog
Funcao para gravar log na PEM para fazer envio via job (do PSA)
Vai chamar efetivamente a funcao de geracao de pedido FATTICKET
<AUTHOR>
@since      22/05/19
/*/
//-------------------------------------------------------------------
User Function FFTLog(_cEmpPed,_cFilPed,_cUserPed,_cPswMxm,_nVlrHrConv,_aCab,_aItens,_cIdTicket)

Local cHeader  := ""
Local oObjZdk  := JsonUtil():New()
Local oFaturam := FatZendeskPSA():New()
Local nX       := 0
Local nY       := 0
Local oCab     := JsonUtil():New()
Local oCab2    := JsonUtil():New()
Local oItem    := JsonUtil():New()
Local oItem2   := JsonUtil():New()
Local aNewCab  := {}
Local aNewItem := {}
Local aLog     := {}
Local cCodLog  := ""
Local cJCab    := ""
Local cJItem   := ""
Local cURLTicket  := Alltrim(GetMV("TI_#URLTKT",,"https://mid.totvs.com:8086/"))      // caminho do WS a ser chamado para gravar o numero do pedido
Local cPathTicket := Alltrim(GetMV("TI_#PATTKT",,""))            			 		  // path a ser chamado para gravar o numero do pedido
Local cMethod     := "finalizarFaturamentoLote"                                      // nome do metodo a ser utilizado

//-- converte o array de cabecalho em objeto
For nX:=1 To Len(_aCab)
	oCab2:PutVal( _aCab[nX,1] , _aCab[nX,2] )
Next nX

oCab:PutVal("cCab" , oCab2 )

//-- converte o array de itens em array de objetos
oItem:PutVal("cItem" , aNewItem )

For nX:=1 To Len(_aItens)
	oItem2 := JsonUtil():New()
	For nY:=1 To Len(_aItens[nX])
		oItem2:PutVal( _aItens[nX,nY,1] , _aItens[nX,nY,2] )
	Next	
	AAdd(aNewItem, oItem2)
Next nX

cJCab  := oCab:ToJson()
cJItem := oItem:ToJson()

oObjZdk:PutVal("cEmpPed"    , _cEmpPed )
oObjZdk:PutVal("cFilPed"    , _cFilPed )
oObjZdk:PutVal("cUserPed"   , _cUserPed )
oObjZdk:PutVal("cPswMxm"    , _cPswMxm )
oObjZdk:PutVal("nVlrHrConv" , _nVlrHrConv )
oObjZdk:PutVal("cIdTicket"  , _cIdTicket )
oObjZdk:PutVal("oCab"       ,  cJCab )
oObjZdk:PutVal("oItens"     ,  cJItem )

cHeader := oObjZdk:ToJson()

//-- ajusta json
cHeader := StrTran(cHeader, '"{"cCab": ', '')
cHeader := StrTran(cHeader, '}}"', '}')

cHeader := StrTran(cHeader, '"{"cItem": ', '')
cHeader := StrTran(cHeader, '}]}"', '}]')

// 1=ALIAS;2=USER;3=ERROR;4=RECNO;5=BODY;6=STATUS;7=METHOD;8=SE DELETA O REGISTRO;9=URI;10=HEADER;11=PARTES;12=REGRA
aLog    := { '#ZP' , __cUserID , 'Geracao de pedido Zendesk - id '+_cIdTicket+' - Enviado para processamento', 0, '','2','POST', .F.,'',,'',''} 
cCodLog := oFaturam:SetLog(aLog)

PEM->( dbSetOrder(1) )
PEM->( dbSeek( xfilial('PEM') + cCodLog  ) )
PEM->( MsUnlock() )
Reclock('PEM',.F.)
	PEM->PEM_URI    := cURLTicket + cPathTicket + cMethod + '/' + 'execlogpsa' + '?codigo=' + cCodLog + '&metodo=PostPedidoZendesk'
	PEM->PEM_HEADER := cHeader
PEM->( MsUnlock() )

FreeObj(oObjZdk)
FreeObj(oFaturam)
FreeObj(oCab)
FreeObj(oCab2)
FreeObj(oItem)
FreeObj(oItem2)

Return(.T.)

//-------------------------------------------------------------------
/*/{Protheus.doc} FFTObj
Funcao para preencher array do pedido com objetos gravados na retentativa (PEM)
<AUTHOR>
@since      22/05/19
/*/
//-------------------------------------------------------------------
User Function FFTObj(_cEmpPed, _cFilPed, _oCab, _oItens)

Local _cCposEsp	:= GetMv("MV_#CRP01A",,"06/43")
Local _aCab     := {}
Local _aItens   := {}     
Local nX        := 0

For nX:=1 To Len(_oItens)

	AAdd(_aItens,{{"C6_FILIAL"	,_oItens[nX]:C6_FILIAL	,Nil},;	// Filial
				 {"C6_ITEM"		,_oItens[nX]:C6_ITEM	,Nil},;	// Item
				 {"C6_CLI"		,_oItens[nX]:C6_CLI		,Nil},;	// Cliente
				 {"C6_LOJA"		,_oItens[nX]:C6_LOJA    ,Nil},;	// Cliente
				 {"C6_DESCRI"	,_oItens[nX]:C6_DESCRI	,nil},;	// Descricao Produto
				 {"C6_PRODUTO"	,_oItens[nX]:C6_PRODUTO ,nil},;	// Cod. Produto					 
				 {"C6_QTDVEN"	,_oItens[nX]:C6_QTDVEN	,nil},;	// Quantidade Venda
				 {"C6_PRCVEN"	,_oItens[nX]:C6_PRCVEN	,nil},;	// Preco Unit.
				 {"C6_PRUNIT"	,_oItens[nX]:C6_PRUNIT	,nil},;	// Preco Item
				 {"C6_TES"		,_oItens[nX]:C6_TES		,nil},;	// Tipo de Saida
				 {"C6_ENTREG"	,CtoD(_oItens[nX]:C6_ENTREG)	,nil},;	// Dt.Entrega
				 {"C6_QTDLIB"	,_oItens[nX]:C6_QTDLIB	,nil},;	// Gerar Pedido Venda Bloqueado
				 {"C6_CC"	    ,_oItens[nX]:C6_CC	,nil},;	// Centro de Custo
				 {"C6_CCUSTO"   ,_oItens[nX]:C6_CC	,nil},;	// Centro de Custo
				 {"C6_XFATOR"	,_oItens[nX]:C6_XFATOR	,nil}})	// Aliquota

Next nX

Aadd(_aCab,{"C5_FILIAL"	,_oCab:C5_FILIAL	 ,Nil})
AAdd(_aCab,{"C5_TIPO"	,_oCab:C5_TIPO		 ,nil})
AAdd(_aCab,{"C5_CLIENTE",_oCab:C5_CLIENTE    ,nil})
AAdd(_aCab,{"C5_LOJACLI",_oCab:C5_LOJACLI    ,nil})
AAdd(_aCab,{"C5_LOJAENT",_oCab:C5_LOJAENT    ,nil})
AAdd(_aCab,{"C5_TIPOCLI",_oCab:C5_TIPOCLI    ,nil})
AAdd(_aCab,{"C5_CONDPAG",_oCab:C5_CONDPAG    ,nil})
AAdd(_aCab,{"C5_CNDBKP"	,_oCab:C5_CNDBKP     ,nil})
AAdd(_aCab,{"C5_MOEDA"	,_oCab:C5_MOEDA      ,nil})
AAdd(_aCab,{"C5_MENNOTA",_oCab:C5_MENNOTA    ,nil})
AAdd(_aCab,{"C5_INCISS"	,_oCab:C5_INCISS     ,nil})
AAdd(_aCab,{"C5_AJUSCOF",_oCab:C5_AJUSCOF    ,nil})
AAdd(_aCab,{"C5_RECISS"	,_oCab:C5_RECISS     ,nil})
AAdd(_aCab,{"C5_PARC1"	,_oCab:C5_PARC1      ,nil}) //Arredonda para apenas duas casas decimais (Igual campo total C6_VALOR)
AAdd(_aCab,{"C5_DATA1"	,CtoD(_oCab:C5_DATA1),nil})
AAdd(_aCab,{"C5_NATUREZ",_oCab:C5_NATUREZ    ,nil})
AAdd(_aCab,{"C5_XMENOT4",_oCab:C5_XMENOT4    ,nil})

If _cEmpPed+_cFilPed $ _cCposEsp
	AAdd(_aCab,{"C5_FECDSE" ,_oCab:C5_FECDSE	,nil})
	AAdd(_aCab,{"C5_FECHSE" ,_oCab:C5_FECHSE	,nil})
	AAdd(_aCab,{"C5_TPVENT" ,_oCab:C5_TPVENT	,nil})
	//-------------------------------------------
	// Campos obrigatorios para NFe Argentina  //   
	//-------------------------------------------
	If SC5->(FieldPos("C5_INCOTER")) > 0
		Aadd(_aCab,{"C5_INCOTER" ,_oCab:C5_INCOTER	,nil})
	EndIf
	If SC5->(FieldPos("C5_IDIOMA")) > 0	
		Aadd(_aCab,{"C5_IDIOMA"	,_oCab:C5_IDIOMA	,nil})
	EndIf
	If SC5->(FieldPos("C5_PAISENT")) > 0
		Aadd(_aCab,{"C5_IDIOMA"	,_oCab:C5_IDIOMA	,nil})
	EndIf
EndIf

Return( { aClone(_aCab), aClone(_aItens) } )

/*/{Protheus.doc} TokenZendesk
	Gera Token Zendesk
	<AUTHOR>
	@since 15/05/2024
/*/
User Function TokenZendesk() 
	Local aHeader	  := {}
	Local cRet        := ''
	Local cURLTicket  := "https://ti-services.totvs.com.br/"
	Local cPathTicket := "api/v1/totvs-faturamento-lote/"
	Local cMethod     := "login"
	Local cJson       := ''
	Local oRetJSon    := JsonUtil():New()
	Local oJson		  := FWJsonObject():New()
	Local cUserZend	  := Alltrim(GetMV("TI_SRVZDUS",,"<EMAIL>")) //Usuario
	Local cSenhaZend  := SubStr(Embaralha(GetMV("TI_SRVZDPS",,"}V41U$P52EM0cdV5#<82THR06^B2"), 1), 9) //Senha

	oRest := FWRest():New( cURLTicket )

	aadd(aHeader, "Content-Type: application/json")
	aadd(aHeader, "Accept: application/json")

	oRetJson:PutVal("email" , cUserZend)
	oRetJson:PutVal("password" , cSenhaZend)

	cJson := oRetJson:ToJson()

	oRest:setPath( cPathTicket + cMethod )
	oRest:SetPostParams( cJson )

	if oRest:POST(aHeader)
		if oJson:Activate(alltrim(oRest:GetResult()))
			cRet := oJson:Avalues[1]
		endif
	endif

Return(cRet)
