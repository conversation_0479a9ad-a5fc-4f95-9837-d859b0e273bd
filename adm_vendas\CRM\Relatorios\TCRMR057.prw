#INCLUDE "PROTHEUS.CH" 
#INCLUDE "TOPCONN.CH"	
#INCLUDE "MSOLE.CH"

/*/{Protheus.doc} R057GruEco
//Realiza carga na tabela do dot 'por_valores_condicoes_grupo_economico_INTERA_2B.dot'
<AUTHOR>
@since 15/04/2019
@version 1.0
@return ${return}, ${return_description}

@type function
/*/
user Function R057GrEc()
local aAreaZAV		:= ZAV->(GetArea())
local aAreaADY		:= ADY->(GetArea())
local hWord 		:= ParamIXB[1]	
local nQTDLINHA		:= 0


dbSelectArea("ZAV")
dbSetOrder(1)
dbSelectArea("ADY")
dbSetOrder(1)


If	ZAV->(dbSeek(xFilial("ZAV")+ADY->(ADY_PROPOS+ADY_PREVIS)))

	While ZAV->(!Eof()) .and. ADY->(ADY_FILIAL+ADY_PROPOS+ADY_PREVIS) == xFilial("ZAV")+ZAV->(ZAV_CODIGO+ZAV_VERSAO)

		nQTDLINHA++

		OLE_SetDocumentVar( hWord,"GrpEcoIntPlu"	+Alltrim(str(nQTDLINHA))+"01"	,Transform(ZAV->ZAV_CGCAGR, X3Picture('ZAV_CGCAGR')) )  
		OLE_SetDocumentVar( hWord,"GrpEcoIntPlu"	+Alltrim(str(nQTDLINHA))+"02"	,alltrim(ZAV->ZAV_NOMAGR) ) 		
		
	
		ZAV->(DbSkip())

	End
EndIf

If	nQTDLINHA > 0 
	
	OLE_SetDocumentVar(hWord,'numLinhas',alltrim(Str(nQTDLINHA)))
	OLE_SetDocumentVar(hWord,'numColunas',alltrim(Str(02)))
	OLE_SetDocumentVar(hWord,'nomeColuna',alltrim("GrpEcoIntPlu"))
	OLE_SetDocumentVar(hWord,'nomeIndicador',alltrim("nGrupoEconomicoInteraPlus"))

	OLE_ExecuteMacro(hWord,"montaColunas")

	OLE_SetDocumentVar(hWord,'numLinhas',"")
	OLE_SetDocumentVar(hWord,'numColunas',"")
	OLE_SetDocumentVar(hWord,'nomeColuna',"")
	OLE_SetDocumentVar(hWord,'nomeIndicador',"")

Endif

RestArea(aAreaZAV)
RestArea(aAreaADY)

Return
