#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVF012     
Callback de  envio do arquivo para actio
@type  Function
<AUTHOR> Menabue Lima
@since 11/11/2023
@version 1.0
	/*/
User Function TIRVF013(nPar)
	Local oJson         := JsonObject():new()
	Local oBody
	Local lFalhou       := .F.
	Local cFalha        := ""
	Local aLancam		:= {}
	Local oResponse 	:=  JsonObject():new()
	Local nI			:= 0
	Private cCodReq		:= ""
	Default nPar        := 0
	if(nPar==0)
		oBody:= oJson:FromJson(PARAMIXB[2])
		cCodReq		:= PARAMIXB[1]
	Else
		DBSELECTAREA( "P37" )
		P37->(DBSETORDER(1))
		P37->(DBGOTO(nPar))//14189199

		//oBody:= oJson:FromJson(P37->P37_BODY)
		oBody:= oJson:FromJson(cJson)
	EndIF
	oResponse["status" ] := 200
	//Desmarca o registro para evitar sujeira
	P37->(RecLock("P37",.F.))
	P37->P37_STATUS := "F"
	P37->P37_TIMERE := TIME()
	P37->P37_BODYRP := '{ "mensagem":"Preparando pra processamento"}'
	P37->(MsUnLock())
	__cUserID := "000000"
	If VALTYPE(oBody) == 'U'
		aLancam := oJson:GetJsonObject("items")
		If ValType(aLancam) == "A"
			If Len(aLancam) > 0
				For nI:= 1 to len(aLancam)
					U_TIRVR002(Alltrim(aLancam[nI]["competencia"]),Alltrim(aLancam[nI]["indicador"]))
				Next nI
			 
				P37->(RecLock("P37",.F.))

				P37->P37_STATUS := "2"
				P37->P37_BODYRP := '{"status":200, "mensagem":"Registros incluidos com sucesso"}'
				P37->P37_TIMERP:= TIME()

				P37->(MsUnLock())
				oResponse := Nil
				FreeObj(oResponse)
			Else
				P37->(RecLock("P37",.F.))
				oResponse["status" ] := 500
				oResponse["mensagem" ] := "no items"
				P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
				P37->P37_STATUS := "3"
				P37->P37_TIMERP:= TIME()
				P37->(MsUnLock())
			EndIF
		Else
			P37->(RecLock("P37",.F.))
			oResponse["status" ] := 500
			oResponse["mensagem" ] := "items is not Array"
			P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
			P37->P37_STATUS := "3"
			P37->P37_TIMERP:= TIME()
			P37->(MsUnLock())
		EndIF
	Else
		P37->(RecLock("P37",.F.))
		oResponse["status" ] := 500
		oResponse["mensagem" ] := "Body is Empty"
		P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
		P37->P37_STATUS := "3"
		P37->P37_TIMERP:= TIME()
		P37->(MsUnLock())
	EndIF
Return
