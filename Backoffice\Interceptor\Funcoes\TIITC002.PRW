#INCLUDE 'TOTVS.CH'
#INCLUDE "FWBROWSE.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "TOPCONN.CH"

#DEFINE CRLF		Chr(13)+Chr(10)

#DEFINE THREAD_IDLE		'TIINTEXEC - ##IDLE'
#DEFINE POS_USER 		01
#DEFINE POS_PC	 		02
#DEFINE POS_IDTHREAD	03
#DEFINE POS_INSTRUC		09
#DEFINE POS_OBS 		11

/*/{Protheus.doc} TIITC002
    Funcoes Responsavel por retornar os dados da P37 conforme API retornada
    @type  Function
    <AUTHOR> Menabue Lima
    @since 22/11/2022
    @version 1.0
    /*/
User Function TIITC002()
	Local aRequest  := PARAMIXB //Parametros recebidos do Interceptor  [1]= Codigo da Requisicao P37_COD [2]= Body Recebido da requisicao  P37_BODY
	Local cAlsPro := GetNextAlias()
	Local oJson     := JsonObject():new()
	Local oBody     := oJson:FromJson( aRequest[2] )

	Local cQry		:= ""
	If ValType( oBody ) == "U"
		cAPI:=oJson:GetJsonObject( "api" )
		cData :=oJson:GetJsonObject( "data" )
		cStatus :=oJson:GetJsonObject( "status" )
		cNoStatus :=oJson:GetJsonObject( "nostatus" )
		cCodReq :=oJson:GetJsonObject( "requisicao" )
		cDelimi := oJson:GetJsonObject( "delibody" )
		cDelrp:= oJson:GetJsonObject( "delirpbody" )
		If cAPI != Nil .And. cData != Nil
			cQry := " SELECT  P37.R_E_C_N_O_ RNOP37, P37.S_T_A_M_P_ DATASTAMP "
			cQry += " FROM "+ RetSqlName("P37") +" P37 "
			cQry += " WHERE P37.D_E_L_E_T_ = ' '
			cQry += " AND  P37.P37_CODAPI='"+cAPI+"'  "
			cQry += " AND P37.P37_DATA ='"+cData+"'"
			if (cStatus!= Nil)
				cQry += " AND P37.P37_STATUS IN "+ Formatin( cStatus, ',' )
			EndIF
			if (cNoStatus!= Nil)
				cQry += " AND P37.P37_STATUS NOT IN "+ Formatin( cNoStatus, ',' )
			EndIF

			if (cCodReq!= Nil)
				cQry += " AND   P37.P37_COD ='"+cCodReq+"'"
				cQry += "      OR
				cQry += " (P37.P37_DATA >= '"+cData+"' AND P37.P37_CODREQ ='"+cCodReq+"' )"
			EndIF

			cQry += " ORDER BY P37.R_E_C_N_O_ "
		else
			cJson:= '{ "error": "Parametros nao informados" '
			RecLock("P37", .F.)
			P37->P37_BODYRP:=cJson
			P37->(MsUnlock())
		Endif
	else
		cJson:= '{ "error": "Json formato invalido" '
		RecLock("P37", .F.)
		P37->P37_BODYRP:=cJson
		P37->(MsUnlock())
	EndIF

	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAlsPro,.T.,.T.)


	nLinha := 0
	aJson:={}
	cJson:= '{ "requisicoes": [ '
	While (cAlsPro)->(!Eof())
		DbSelectArea("P37")
		DbSetOrder(1)
		P37->(DbGoTo( (cAlsPro)->RNOP37 ))

		cJson +='{ 	"requisicao": "'+P37->P37_COD+'",
		cJson +='	"status": "'+P37->P37_STATUS+'",
		cJson +='	"codApi": "'+P37->P37_CODAPI+'",
		cJson +='	"key" : "'+P37->P37_COD+"_"+P37->P37_CODAPI+'" ,'
		cJson +='   "data": "'+DToC(P37->P37_DATA)+'",
		cJson +='   "dataLimpa": "'+DToS(P37->P37_DATA)+'",
		cJson +='   "DataStm": "'+AllTrim( DTOS((cAlsPro)->DATASTAMP) ) +'",
		cJson +='   "TimeRsp": "'+P37->P37_TIMERP+'",
		cJson +='   "requisicao_principal": "'+P37->P37_CODREQ+'"
		if (cCodReq!= Nil)
			nPosBody:=At(cDelimi,P37->P37_BODY)
			nPosResp:=At(cDelrp,P37->P37_BODYRP)
			cJson +=  ', "body" : '
			oJBody:=oJson:FromJSON( P37->P37_BODY )
			If(oJBody!= nil)
				cJson += '"'
				if nPosBody==0
					cJson +=  StrTran(StrTran(SubStr(P37->P37_BODY,nPosBody,Len(P37->P37_BODY)),'"',"'")+' ',CRLF,'')
				else
					cJson +=  StrTran(StrTran(SubStr(P37->P37_BODY,nPosBody,Len(P37->P37_BODY)),'"',"'"),CRLF,'')
				EndIF
				cJson +='"'
			Else
				cJson += P37->P37_BODY
			EndIF

			cJson +=  ', "body_response" : '
			oJBodyRp:=oJson:FromJSON( P37->P37_BODYRP )
			If(oJBodyRp!= nil)
				cJson += '"'
				if nPosResp==0
					cJson += StrTran(StrTran(SubStr(P37->P37_BODYRP,nPosResp,Len(P37->P37_BODYRP)),'"',"'")+' ',CRLF,'')
				else
					cJson += StrTran(StrTran(SubStr(P37->P37_BODYRP,nPosResp,Len(P37->P37_BODYRP)),'"',"'")+' ',CRLF,'')
				EndIF
				cJson +='"'
			Else
				cJson += P37->P37_BODYRP
			EndIF
		EndIF


		cJson +=  ' },'
		(cAlsPro)->(DbSkip())
	EndDO

	cJson:= SubStr(cJson,1,len(cJson)-1)+'] }'

	(cAlsPro)->(DbCloseArea())


Return cJson

