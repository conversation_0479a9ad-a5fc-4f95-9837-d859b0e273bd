#include "TOTVS.ch"  

/*/{Protheus.doc} TCRMG004
Gatilho para preenchimento do campo A1_XCLIVIP de acordo com A1_XESTRAT
@type function
<AUTHOR> Mobile Costa
@since 05/12/2015
@version 1.0
/*/

User Function TCRMG004()

Local cRet := M->A1_XCLIVIP

If INCLUI .OR. (M->A1_XESTRAT == "1" .AND. SA1->A1_XESTRAT <> "1")
   If M->A1_XMOTEST <> '04'
      If M->A1_XCLIVIP == "2"
         cRet := "8"
      EndIf
   EndIf
EndIf

Return(cRet)
