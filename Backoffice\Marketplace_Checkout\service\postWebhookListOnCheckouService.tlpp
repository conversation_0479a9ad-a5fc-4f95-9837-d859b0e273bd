#include "tlpp-core.th"

Class   postWebhookListOnCheckouService
    Private Data    oWebhookList                                As  Object
    Private Data    cResponseIpaas                              As  Character
    
    Public  Method  new(oVar    As  Object)                     As  Object
    Public  Method  execute()                                   As  Object
    Public  Method  sendToIpaas(cJson   As  Character)          As  Object

EndClass


Method  new(oVar    As  Object) As  Object  Class   postWebhookListOnCheckouService
    ::oWebhookList      :=  oVar
    ::cResponseIpaas    :=  ''
Return  Self


Method  execute()   As  Object  Class   postWebhookListOnCheckouService
    ::sendToIpaas(::oWebhookList:ToString())
Return  Self



Method  sendToIpaas(cJson   As  Character)           As  Object  Class   postWebhookListOnCheckouService
    Local   cUrl    :=  'https://api-ipaas.totvs.app/sync-hook/api/v1/integrations/cbcb8647-921c-425e-b5fc-38020a70db89/api-key/d9fe014a-a107-4991-b221-2257a37c0c05'
    Local   oIpaas  :=  Nil

    oIpaas := TIiPaaS():New()
	oIpaas:SetWebHook(cUrl)
	oIpaas:SetBodyWebHook(cJson)
	oIpaas:SendToiPaaS()

    ::cResponseIpaas  :=  oIpaas:getIpaasCharacterResponse()

    FreeObj(oIpaas)

Return  Self
