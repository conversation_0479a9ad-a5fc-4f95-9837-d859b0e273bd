#INCLUDE "Totvs.ch"
#INCLUDE "Restful.ch"
#INCLUDE "FWMVCDEF.CH"

/*/{Protheus.doc} tcrms003
	WS Gestão de ofertas CRM x Fluig
<AUTHOR>
@since 02/03/2020
@version undefined
@type function
/*/
WSRESTFUL tcrms003 DESCRIPTION "Integrações Regra de comercialização CRM"

	WsData  codigoNivel As String
	WsData  lSimulacao As Boolean

	WSMETHOD GET    Description "Retorna regras de comercialização existentes no CRM" WSSyntax "/"
	WSMETHOD POST   Description "Inclui regra de comercialização no CRM vinculando todos os cadastros necessários"
	WSMETHOD PUT    Description "Atualiza regra de comercialização no CRM incluindo novos produtos"

END WSRESTFUL

/*/{Protheus.doc} GET
	Retorna regras de comercialização existentes no CRM
<AUTHOR>
@since 02/03/2020
@version undefined
@type function
/*/
WsMethod Get WsService tcrms003

	::SetContentType("application/json")

	oObj := JsonUtil():New()

	aret := X188NivPai()
	oObj:PutVal("source",aret)
	::SetResponse(oObj:toJson())

Return .T.

/*/{Protheus.doc} PUT
	Grava nova regra de comercialização no CRM
<AUTHOR> Osti
@since 02/03/2020
@version undefined
@type function
/*/
WsMethod PUT WsReceive codigoNivel, lSimulacao WsService TCRMS003
	Local cContent  := Alltrim(Self:GetContent())
	Local aRetPost  := {}
	Local lRet      := .F.
	Local cCodAgr   := ""
	Local cAliasAOM := GetNextAlias()
	Local oContent

	Private lTest   := Iif(Self:lSimulacao <> Nil,Self:lSimulacao,.F.)

	::SetContentType("application/json")

// Variáveis de entrada
	If Len(self:aURLParms) > 0
		cCodAgr := Alltrim(self:aURLParms[1])
	Else
		cCodAgr := Alltrim(Self:codigoNivel)
	EndIf

	If ValType(cContent) == "U" .Or. Empty(cContent)
		SetRestFault(400, EncodeUTF8("Conteúdo do Body não enviado na requisição!"))
	ElseIf !Empty(cCodAgr)

		BeginSql Alias cAliasAOM
        SELECT
            AOM_CODNIV,
            AZ0_DE
        FROM
            %Table:AOM% AOM INNER JOIN
            %Table:AZ0% AZ0 ON
                AOM_FILIAL = AZ0_FILIAL
                AND AOM_CODAGR = AZ0_CODAGR
                AND AOM_CODNIV = AZ0_CODNIV
                AND AZ0_ENTIDA = "DA0"
                AND AZ0.%NotDel%
        WHERE
            AOM.%NotDel%
            AND AOM_FILIAL = %xFilial:AOM%
            AND AOM_CODAGR = '000112'
            AND AOM_CODNIV = %exp:cCodAgr%
        GROUP BY AZ0_DE, AOM_CODNIV
		EndSql

		If !(cAliasAOM)->(EOF())
			If fWJsonDeserialize(alltrim(cContent),@oContent)
				// Atualiza Regra de comercialização incluindo novos produtos em sua(s) tabela(s) de preço
				Begin Transaction
					aRetPost := S03Put(cAliasAOM, oContent)
					if aRetPost[1] .And. !lTest
						Self:SetResponse(EncodeUTF8(aRetPost[2]))
						lRet := .T.
					else
						DisarmTransaction()
						SetRestFault(400, EncodeUTF8(aRetPost[2]))
					endIf
				End Transaction
			else
				SetRestFault(400, EncodeUTF8("Nao foi possivel realizar parser do Body enviado na requisição!"))
			EndIf
		Else
			SetRestFault(404, EncodeUTF8("Nível de regra " + cCodAgr + " não encontrado na estrutura CRM ou não possui tabela de preço." ))
		EndIf

		(cAliasAOM)->(dbCloseArea())

	else
		SetRestFault(404, EncodeUTF8("codigoNivel não informado"))
	EndIf

Return lRet

/*/{Protheus.doc} POST
	Grava nova regra de comercialização no CRM
<AUTHOR> Osti
@since 02/03/2020
@version undefined
@type function
/*/
WsMethod POST WsReceive codigoNivel, lSimulacao WsService TCRMS003
	Local cContent  := Alltrim(Self:GetContent())
	Local oContent
	Local cCodAOMPai
	Local cCodAgr   := Alltrim(GetMV('TI_CODAGR'))
	Local aRetPost  := {}
	Local lRet      := .F.
	Local cAliasAOM := GetNextAlias()

	Private lTest     := Iif(Self:lSimulacao <> Nil,Self:lSimulacao,.F.)

	::SetContentType("application/json")

// Variáveis de entrada
	If Len(self:aURLParms) > 0
		cCodAOMPai := Alltrim(self:aURLParms[1])
	Else
		cCodAOMPai := Alltrim(Self:codigoNivel)
	EndIf

	If ValType(cContent) == "U" .Or. Empty(cContent)
		SetRestFault(400, EncodeUTF8("Conteúdo do Body não enviado na requisição!"))
	ElseIf !Empty(cCodAOMPai)

		BeginSql Alias cAliasAOM
        SELECT
            AOM_NIVPAI
        FROM %Table:AOM%
        WHERE D_E_L_E_T_ = ' '
        AND AOM_FILIAL = %xFilial:AOM%
        AND AOM_CODAGR = %exp:cCodAgr%
        AND (AOM_NIVPAI = %exp:cCodAOMPai% OR AOM_CODNIV = %exp:cCodAOMPai%)
		EndSql

		If !(cAliasAOM)->(EOF())
			if fWJsonDeserialize(alltrim(cContent),@oContent)
				// Grava Regra de comercialização completa
				Begin Transaction

					aRetPost := S03Post(cCodAgr, cCodAOMPai, oContent)
					if aRetPost[1] .And. !lTest
						Self:SetResponse(EncodeUTF8(aRetPost[2]))
						lRet := .T.
					else
						DisarmTransaction()
						SetRestFault(400, EncodeUTF8(aRetPost[2]))
					endIf
				End Transaction
			else
				SetRestFault(400, EncodeUTF8("Nao foi possivel realizar parser do Body enviado na requisição!"))
			EndIf
		Else
			SetRestFault(404, EncodeUTF8("Nível pai " + cCodAOMPai + " não encontrado na estrutura CRM" ))
		EndIf

		(cAliasAOM)->(dbCloseArea())

	else
		SetRestFault(404, EncodeUTF8("codigoNivel não informado"))
	EndIf

Return lRet

/*/{Protheus.doc} X188NivPai
	Query retorna agrupadores conforme nivel pai
<AUTHOR> Osti
@since 02/03/2020
@version undefined
@type function
/*/
Static Function X188NivPai(cNivPai)
	Local cAliasNiv := GetNextAlias()
	Local oItem     := Nil
	Local aRet      := {}
	Local aAux      := Nil

	Default cNivPai := '0000'

	BeginSql Alias cAliasNiv
    SELECT
        AOM.AOM_CODNIV,
        AOM.AOM_DESCRI,
        AOM.AOM_NIVPAI,
        AOM.AOM_MSBLQL
    FROM
        %Table:AOM% AOM
    WHERE
        AOM.AOM_CODAGR = '000112'
        AND AOM.AOM_NIVPAI = %Exp:cNivPai%
        AND AOM.%NotDel%
    ORDER BY AOM.AOM_CODNIV||AOM.AOM_NIVPAI||AOM.AOM_IDINT
	EndSql

	If (cAliasNiv)->(!EOF())
		While (cAliasNiv)->(!EOF())
			aAux    := {}
			oItem   := Nil
			oItem   := JsonUtil():New()
			oItem:Putval("key",(cAliasNiv)->AOM_CODNIV)
			oItem:Putval("title",(cAliasNiv)->AOM_CODNIV + " - " + Alltrim(FwCutOff( (cAliasNiv)->AOM_DESCRI, .t.)))
			oItem:Putval("iconclass",Iif((cAliasNiv)->AOM_MSBLQL <> '1','fluigicon fluigicon-folder-open','fluigicon fluigicon-remove-sign'))

			aAux := X188NivPai((cAliasNiv)->AOM_CODNIV)
			oItem:PutVal("children", aAux)

			aAdd(aRet,oItem)
			(cAliasNiv)->(dbSkip())
		EndDo
	EndIf

	(cAliasNiv)->(dbCloseArea())

Return aRet

/*/{Protheus.doc} S03LevChild
	Query retorna cadeia de niveis conforme o primeiro nível filho
<AUTHOR> Osti
@since 01/07/2021
@version undefined
@type function
/*/
Static Function S03LevChild(cLevChild)
	Local cAliasNiv := GetNextAlias()
	Local oResNiv   := Nil
	Local aRet      := {}

	Default cLevChild := '0000'

	BeginSql Alias cAliasNiv
    SELECT
        AOM.AOM_NIVPAI,
        AOM.AOM_CODNIV,
        AOM.AOM_DESCRI
    FROM
        AOM000 AOM
    WHERE
        AOM.AOM_CODAGR = '000112'
        AND AOM.AOM_CODNIV = %Exp:cLevChild%
        AND AOM.%NotDel%
    ORDER BY AOM.AOM_CODNIV||AOM.AOM_NIVPAI||AOM.AOM_IDINT
	EndSql

	If (cAliasNiv)->(!EOF())

		While (cAliasNiv)->(!EOF())

			If (cAliasNiv)->AOM_NIVPAI <> "0000"
				aRet := S03LevChild((cAliasNiv)->AOM_NIVPAI)
			EndIf

			oResNiv := Nil
			oResNiv := JsonObject():New()
			oResNiv["label"] := (cAliasNiv)->AOM_CODNIV + "-" + Alltrim(FwCutOff( (cAliasNiv)->AOM_DESCRI, .t.))

			AADD(aRet, oResNiv)

			(cAliasNiv)->(dbSkip())

		EndDo
	EndIf

	(cAliasNiv)->(dbCloseArea())

Return aRet

/*/{Protheus.doc} S03Put
	Atualiza Regra de comercialização incluindo novos produtos/grupos a ela
<AUTHOR> Osti
@since 23/03/2020
@version undefined
@type function
/*/
Static Function S03Put(cAliasAOM, oContent)
	Local aRet      := {}
	Local aProdutos := {}
	Local aProdDA1  := {}
	Local aProdPDX  := {}
	Local aTabsAlt  := {}
	Local aExecutivs:= {}
	Local aProdPNP  := {}
	Local oRet		:= JsonUtil():New()

	Private oJson     := oContent

// Tratativa informações da integração
// Tratativa de informações e gravação de Produtos
	If (AttIsMemberOf(oJson,'produtos'))
		cRet := S03VldProd(oJson:produtos, @aProdutos, @aProdDA1, @aProdPDX, @aProdPNP)
		If !Empty(cRet)
			Return S03Ret2Err(cRet)
		EndIf
	Else
		Return S03Ret2Err('array de produtos vazio')
	EndIf

// Tratativa de informações Pessoas/Grupos
	If (AttIsMemberOf(oJson,'executivos'))
		cRet := S03VldExec(oJson:executivos, @aExecutivs)
		If !Empty(cRet)
			Return S03Ret2Err(cRet)
		EndIf
	EndIf

// Fim tratativa informações
// Inicio Persistência de dados

// Replica os dados do Produto (SB1/SB5/SBM) para empresas MI
	If ExistBlock("TCADX004") .And. !lTest
		S03RepSB1(aProdutos,"A")
	EndIf

// Grava Desconto por produto (PDX)
	aRet := S03GrvPDX(aProdPDX)
	If !aRet[1]
		Return aRet
	EndIf

// Acessórios
	aRet := S03GrvSUG(aProdutos)
	If !aRet[1]
		Return aRet
	EndIf

// Atualiza Tabela de preço (DA0/DA1) com os novos produtos
	cCodNivAgr := Alltrim((cAliasAOM)->AOM_CODNIV)
	dbSelectArea("AOM")
	AOM->(dbSetOrder(1))
	AOM->(dbSeek(xFilial("AOM") + "000112" + cCodNivAgr))
	While (cAliasAOM)->(!EOF())
		aRet := S03UpdDA1( aProdDA1, Alltrim((cAliasAOM)->AZ0_DE) )
		If !aRet[1]
			Return aRet
		Else
			AAdd(aTabsAlt, Alltrim((cAliasAOM)->AZ0_DE) )
		EndIf
		(cAliasAOM)->(dbSkip())
	EndDo

// Atualiza com novos Grupos/Pessoas (PL2)
	If Len(aExecutivs) > 0
		S03UpdPL2(aExecutivs, cCodNivAgr)
	EndIf

// Grava Regra de Dependência (PNP/PNQ/PNR)
	If Len(aProdPNP) > 0
		aRet := S03GrvPNP(aProdPNP, cCodNivAgr, AOM->AOM_DESCRI)
		If !aRet[1]
			Return aRet
		EndIf
	EndIf

	oRet:PutVal("codNivel",cCodNivAgr)
	oRet:PutVal("tabelasPreco",aTabsAlt)
	oRet:PutVal("Produtos", aProdutos)
	AAdd(aRet, oRet:ToJson() )

//Fim Persistência de dados

Return aRet

/*/{Protheus.doc} S03Post
	Inclui Regra de comercialização
<AUTHOR> Osti
@since 02/03/2020
@version undefined
@type function
/*/
Static Function S03Post(cAgrupador, cNivPai, oContent)
	Local cNiv
	Local cIdInt
	Local aRet      := { .T. }
	Local aExpressao:= {}
	Local aAux      := {}
	Local aProdutos := {}
	Local aProdDa1  := {}
	Local aProdPDX  := {}
	Local aEquipes  := {}
	Local aExecutivs:= {}
	Local aProdPNP  := {}
	Local aAgns     := {}
	Local nFor      := 1
	Local cXml      := ""
	Local cCodTab   := ""
	Local cAgns     := ""
	Local cAliasADK := GetNextAlias()
	Local oRet		:= JsonUtil():New()
	Local oFiltro

	Private oJson     := oContent

// Validações
// Tratativa para campos AOM obrigatórios
	cDescricao  := IIf(AttIsMemberOf(oJson,"descricao"),oJson:descricao, '')
	If Empty(cDescricao)
		Return S03Ret2Err('descricao da regra não informada')
	EndIf

// Tratativa de informações e gravação de Produtos
	If (AttIsMemberOf(oJson,'produtos'))
		cRet := S03VldProd(oJson:produtos, @aProdutos,  @aProdDA1, @aProdPDX, @aProdPNP)
		If !Empty(cRet)
			Return S03Ret2Err(cRet)
		EndIf
	EndIf

// Tratativa de informações Pessoas/Grupos
	If (AttIsMemberOf(oJson,'executivos'))
		cRet := S03VldExec(oJson:executivos, @aExecutivs)
		If !Empty(cRet)
			Return S03Ret2Err(cRet)
		EndIf
	EndIf

	dbSelectArea("AOM")
	AOM->(dbSetOrder(1))

// Persistência de dados

// Replica os dados do Produto (SB1/SB5/SBM) para empresas MI
	If Len(aProdutos) > 0
		If ExistBlock("TCADX004") .And. !lTest
			S03RepSB1(aProdutos,"A")
		EndIf
	EndIf

// Grava Desconto por produto (PDX)
	If Len(aProdPDX) > 0
		aRet := S03GrvPDX(aProdPDX)
		If !aRet[1]
			Return aRet
		EndIf
	EndIf

// Acessórios
	If Len(aProdutos) > 0
		aRet := S03GrvSUG(aProdutos)
		If !aRet[1]
			Return aRet
		EndIf
	EndIf

// Grava Tabela de preço (DA0/DA1)
	aRet := S03GrvDA1(aProdDA1, @cCodTab)
	If !aRet[1]
		Return aRet
	EndIf

	cNiv    := S003GetNiv()          // Get Nível principal
	cIdInt  := S003SubNiv(cNivPai)   // SubNível

// Grava Regra de Dependência (PNP/PNQ/PNR)
	If Len(aProdPNP) > 0
		aRet := S03GrvPNP(aProdPNP, cNiv, cDescricao)
		If !aRet[1]
			Return aRet
		EndIf
	EndIf

// Garante que existe uma tabela de preço DA0/DA1
	If !Empty(cCodTab)

		// Transforma objeto em array necessário para executar função do padrão CRMA580MXml
		cFiltroPad := '{"filtros":{'+CRLF
		cFiltroPad += '		"nome":"' + cNiv + '/' + cDescricao + '",'+CRLF
		cFiltroPad += '		"id":"1",'+CRLF
		cFiltroPad += '		"expressoes":'+CRLF
		cFiltroPad += '		{'+CRLF
		cFiltroPad += ' 		"nome":"Gestao_Ofertas_Fluig",'+CRLF
		cFiltroPad += '			"expADVPL":"DA1_CODTAB== ' + chr(39) + 'CODTABELA' + chr(39) + ' .AND. DA1_ATIVO== ' + chr(39) + '1' + chr(39) + '",'+CRLF
		cFiltroPad += '			"expSql":"DA1_CODTAB=' + chr(39) + 'CODTABELA' + chr(39) + ' AND DA1_ATIVO=' + chr(39) + '1' + chr(39) + '",'+CRLF
		cFiltroPad += '			"regras":['+CRLF
		cFiltroPad += ' 			{'+CRLF
		cFiltroPad += ' 				"exp1":"DA1_CODTAB",'+CRLF
		cFiltroPad += '					"exp2":"FIELD",'+CRLF
		cFiltroPad += '					"exp3":"Cod. Tabela Igual a '+ chr(39) + 'CODTABELA' + chr(39) +'",'+CRLF
		cFiltroPad += '					"exp4":"DA1_CODTAB== '+ chr(39) + 'CODTABELA' + chr(39) +'",'+CRLF
		cFiltroPad += '					"exp5":"DA1_CODTAB='+ chr(39) + 'CODTABELA' + chr(39) +'"'+CRLF
		cFiltroPad += '				},'+CRLF
		cFiltroPad += '				{'+CRLF
		cFiltroPad += '					"exp1":"==",'+CRLF
		cFiltroPad += '					"exp2":"OPERATOR",'+CRLF
		cFiltroPad += '					"exp3":"",'+CRLF
		cFiltroPad += '					"exp4":"",'+CRLF
		cFiltroPad += '					"exp5":""'+CRLF
		cFiltroPad += '				},'+CRLF
		cFiltroPad += '				{'+CRLF
		cFiltroPad += '					"exp1":"CODTABELA",'+CRLF
		cFiltroPad += '					"exp2":"EXPRESSION",'+CRLF
		cFiltroPad += '					"exp3":"",'+CRLF
		cFiltroPad += '					"exp4":"",'+CRLF
		cFiltroPad += '					"exp5":""'+CRLF
		cFiltroPad += '				},'+CRLF
		cFiltroPad += '				{'+CRLF
		cFiltroPad += '					"exp1":"AND",'+CRLF
		cFiltroPad += '					"exp2":"",'+CRLF
		cFiltroPad += '					"exp3":"",'+CRLF
		cFiltroPad += '					"exp4":"",'+CRLF
		cFiltroPad += '					"exp5":""'+CRLF
		cFiltroPad += '				},'+CRLF
		cFiltroPad += ' 			{'+CRLF
		cFiltroPad += ' 				"exp1":"DA1_ATIVO",'+CRLF
		cFiltroPad += '					"exp2":"FIELD",'+CRLF
		cFiltroPad += '					"exp3":"Ativo Igual a '+ chr(39) + 'Sim' + chr(39) +'",'+CRLF
		cFiltroPad += '					"exp4":"DA1_ATIVO== '+ chr(39) + '1' + chr(39) +'",'+CRLF
		cFiltroPad += '					"exp5":"DA1_ATIVO='+ chr(39) + '1' + chr(39) +'"'+CRLF
		cFiltroPad += '				},'+CRLF
		cFiltroPad += '				{'+CRLF
		cFiltroPad += '					"exp1":"==",'+CRLF
		cFiltroPad += '					"exp2":"OPERATOR",'+CRLF
		cFiltroPad += '					"exp3":"",'+CRLF
		cFiltroPad += '					"exp4":"",'+CRLF
		cFiltroPad += '					"exp5":""'+CRLF
		cFiltroPad += '				},'+CRLF
		cFiltroPad += '				{'+CRLF
		cFiltroPad += '					"exp1":"1",'+CRLF
		cFiltroPad += '					"exp2":"EXPRESSION",'+CRLF
		cFiltroPad += '					"exp3":"",'+CRLF
		cFiltroPad += '					"exp4":"",'+CRLF
		cFiltroPad += '					"exp5":""'+CRLF
		cFiltroPad += '				}'+CRLF
		cFiltroPad += '			],'+CRLF
		cFiltroPad += '         "exp2":false,'+CRLF
		cFiltroPad += ' 	    "exp3":false,'+CRLF
		cFiltroPad += '		    "exp4":false,'+CRLF
		cFiltroPad += '		    "tabela":"DA1",'+CRLF
		cFiltroPad += '		    "id":"123456",'+CRLF
		cFiltroPad += '		    "exp5":false'+CRLF
		cFiltroPad += '		},'+CRLF
		cFiltroPad += '		"campoDominio":"B1_COD",'+CRLF
		cFiltroPad += '		"contraDominio":"DA1_CODPRO"'+CRLF
		cFiltroPad += '}}'+CRLF

		If fWJsonDeserialize(alltrim(cFiltroPad),@oFiltro)
			aAux := GetFiltro(oFiltro:filtros, cCodTab)
		EndIf
		AAdd(aExpressao, aAux)

		If Len(aExpressao) > 0
			cXml := CRMA580MXml(aExpressao)
		EndIf

		// Grava Regra de comercialização (AOM)
		RecLock("AOM", .T.)
		AOM->AOM_CODAGR := cAgrupador
		AOM->AOM_CODNIV := cNiv
		AOM->AOM_DESCRI := cDescricao
		AOM->AOM_MSBLQL := "2"
		AOM->AOM_NIVPAI := cNivPai
		AOM->AOM_IDINT  := cIdInt
		AOM->AOM_FILXML := cXml
		AOM->(MsUnLock())

		// Grava Regras (AZ0)
		RecLock("AZ0", .T.)
		AZ0->AZ0_CODAGR := cAgrupador
		AZ0->AZ0_CODNIV := cNiv
		AZ0->AZ0_ENTIDA := "DA0"
		AZ0->AZ0_CONDIC := "1"
		AZ0->AZ0_CONENT := "DA0"
		AZ0->AZ0_DE     := cCodTab
		AZ0->(MsUnLock())

		// Grava Complementos (PKG)
		If	AttIsMemberOf(oJson,"complementos")

			cTpVenda  := IIf(AttIsMemberOf(oJson:complementos,"tipovenda")              ,oJson:complementos:tipovenda, '')
			cEmpFat   := IIf(AttIsMemberOf(oJson:complementos,"empresafaturamento")     ,oJson:complementos:empresaFaturamento, '')
			cFilFat   := IIf(AttIsMemberOf(oJson:complementos,"filialfaturamento")      ,oJson:complementos:filialFaturamento, '')
			cAssiElet := IIf(AttIsMemberOf(oJson:complementos,"assinaturaeletronica")   ,oJson:complementos:assinaturaEletronica, '')
			cDescTemp := IIf(AttIsMemberOf(oJson:complementos,"descontotemporario")     ,oJson:complementos:descontoTemporario, '')
			cValidPrp := IIf(AttIsMemberOf(oJson:complementos,"validadeproposta")       ,oJson:complementos:validadeProposta, '')
			cTpProd   := IIf(AttIsMemberOf(oJson:complementos,"tipoProduto")            ,oJson:complementos:tipoProduto, '')
			cDefSite  := IIf(AttIsMemberOf(oJson:complementos,"definicaoSite")          ,oJson:complementos:definicaoSite, '')
			lHabRat   := IIf(AttIsMemberOf(oJson:complementos,"habilitarateio")         ,oJson:complementos:habilitaRateio, '')

			RecLock("PKG", .T.)
			PKG->PKG_CODAGR := cAgrupador
			PKG->PKG_CODNIV := cNiv
			PKG->PKG_TPVEND := cTpVenda
			PKG->PKG_TABB   := cCodTab
			PKG->PKG_TABC   := cCodTab
			PKG->PKG_EMPFAT := cEmpFat
			PKG->PKG_FILFAT := cFilFat
			PKG->PKG_OPCUPL := cAssiElet
			PKG->PKG_DESTEM := cDescTemp
			PKG->PKG_VALID  := cValidPrp
			PKG->PKG_TPPROD := cTpProd
			PKG->PKG_DEFSIT := cDefSite
			PKG->PKG_HABRAT := lHabRat

			PKG->(MsUnLock())

		EndIf

		// Grava permissão por unidade de venda (AZA) caso seja informado o agrupamento AGN
		If (AttIsMemberOf(oJson,'grupoAGN')) .And.;
				Valtype(oJson:grupoAGN) == "A" .And.;
				Len(oJson:grupoAGN) > 0

			// Lista de AGNs do novo agrupador
			aAgns := oJson:grupoAGN
			For nFor := 1 To Len(aAgns)
				If !Empty(cAgns)
					cAgns += "|"
				EndIf
				cAgns += aAgns[nFor]
			Next
			cAgns := "%" +FormatIn(cAgns,"|") + "%"

			cGrupoAGN := oJson:grupoAGN
			BeginSql Alias cAliasADK
            SELECT
                ADK_COD FROM %Table:ADK%
            WHERE ADK_FILIAL = %xFilial:ADK%
            AND %NotDel%
            AND ADK_MSBLQL <> '1'
            AND ADK_XAGN IN %Exp:cAgns%
			EndSql

			While (cAliasADK)->(!EOF())

				RecLock("AZA", .T.)
				AZA->AZA_CODAGR := cAgrupador
				AZA->AZA_CODNIV := cNiv
				AZA->AZA_COD    := (cAliasADK)->ADK_COD
				AZA->(MsUnLock())
				(cAliasADK)->(dbSkip())

			EndDo

			(cAliasADK)->(dbCloseArea())

		EndIf

		// Grava permissão por Usuários (AZ2) conforme configurado em parâmetro
		dbSelectArea("AO3")
		AO3->(dbSetOrder(1))
		cEqpModel   := GetMv("TI_S03USRM",,"000982;003702;004850;277756;007589;")   // Usuário do grupo de Modelos Comerciais
		cEqpEstra   := GetMv("TI_S03USRE",,"282932;001318;270562;008333;")          // Usuário do grupo de Estratégia Comercial
		aEquipes    := StrTokarr(cEqpModel + cEqpEstra, ";")
		For nFor := 1 To Len(aEquipes)
			If AO3->(dbSeek(xFilial("AO3") + aEquipes[nFor]))
				RecLock("AZ2",.T.)
				AZ2->AZ2_CODAGR := cAgrupador
				AZ2->AZ2_CODNIV := cNiv
				AZ2->AZ2_ENTIDA := "AO3"
				AZ2->AZ2_CODENT := aEquipes[nFor]
				AZ2->(MsUnLock())
			EndIf
		Next

		// Atualiza com novos Grupos/Pessoas (PL2)
		If Len(aExecutivs) > 0
			S03UpdPL2(aExecutivs, cNiv)
		EndIf

		oRet:PutVal("codNivel", cNiv)
		oRet:PutVal("idPai", cNivPai)
		oRet:PutVal("descricao", Alltrim(AOM->AOM_DESCRI))
		oRet:PutVal("Produtos", aProdutos)
		AAdd(aRet, oRet:ToJson())

	EndIf
//Fim Persistência de dados

Return aRet

/*/{Protheus.doc} S03VldProd
	array com estrutura dos campos de produto (SB1 e SB5), tabela de preço (DA1) e Desconto por produto (PDX)
<AUTHOR> Osti
@since 22/07/2020
@version undefined
@type function
/*/
Static Function S03VldProd(aJsonProd, aPrdSB1, aPrdDA1, aPrdPDX, aProdPNP)
	Local aProdAux  := {}
	Local aProdGrv  := {}
	Local aAuxPA8PAD:= {}
	Local aDadosAdic:= {}
	Local cRet      := ''
	Local cProdExis := ''
	Local cCategDA1 := ''
	Local cProdOld  := ''
	Local nMaxOld   := 0
	Local nX
	Local nRt
	Local aPrdxOfer := {}
	Local nOferta   := 0
	Local aOfertas  := {}
	Local aRet      := {}
	Local lLicSer   := GetMv("TI_OPCLSER",.F., .T. )

	For nX := 1 To Len(aJsonProd)
		aProdAux    := {}
		aAuxPA8PAD  := {}
		aProdGrv    := {}

		// Campos obrigatórios a serem informados
		If AttIsMemberOf(aJsonProd[nX], 'produto')
			aAdd(aProdAux, {"B1_COD", aJsonProd[nX]:produto} )

			// Para Produtos SAAS deve ser preenchido categoria 000040 na tabela de preço
			If Alltrim(aJsonProd[nX]:produto) == "04"
				cCategDA1 := '000040'
			Else
				cCategDA1 := ''
			EndIf
		else
			Return 'Campo "produto" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'descricaoReduzida')

			// Não permite incluir produto com descrição repetida
			If Empty(cProdExis := vldDescSb1( Substr( Alltrim( Upper(aJsonProd[nX]:descricaoReduzida) ), 1, TamSx3("B1_DESC")[01] ) ) )
				aAdd(aProdAux, {"B1_DESC", Upper(aJsonProd[nX]:descricaoReduzida) } )
			Else
				Return'Produto com a descrição reduzida ' + Alltrim(aJsonProd[nX]:descricaoReduzida) + ' já existe no CRM no produto ' + cProdExis + '. Considere outra descrição reduzida para seu produto.'
			EndIf

		else
			Return 'Campo "descricaoReduzida" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'descricaoCompleta')
			aAdd(aProdAux, {"B1_XDESC1", aJsonProd[nX]:descricaoCompleta} )
		else
			Return 'Campo "descricaoCompleta" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'valorBase')
			aAdd(aProdAux, {"B1_PRV1", aJsonProd[nX]:valorBase} )
		else
			Return 'Campo "valorBase" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'grupoComissao')
			aAdd(aProdAux, {"B1_XGC", aJsonProd[nX]:grupoComissao} )
		else
			Return 'Campo "grupoComissao" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'grupo')
			aAdd(aProdAux, {"B1_GRUPO", aJsonProd[nX]:grupo} )
		else
			Return 'Campo "grupo" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'suporteTelefonico')
			aAdd(aProdAux, {"B1_XDISKSI", aJsonProd[nX]:suporteTelefonico} )
		else
			Return 'Campo "suporteTelefonico" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'cloud')
			aAdd(aProdAux, {"B1_XCLOUD", aJsonProd[nX]:cloud} )
		else
			Return 'Campo "cloud" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'codTributoMunicipal')
			aAdd(aProdAux, {"B1_TRIBMUN", aJsonProd[nX]:codTributoMunicipal} )
		else
			Return 'Campo "codTributoMunicipal" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'retemCSLL')
			aAdd(aProdAux, {"B1_CSLL", aJsonProd[nX]:retemCSLL} )
		else
			Return 'Campo "retemCSLL" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'retemCofins')
			aAdd(aProdAux, {"B1_COFINS", aJsonProd[nX]:retemCofins} )
		else
			Return 'Campo "retemCofins" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'cnae')
			aAdd(aProdAux, {"B1_CNAE", aJsonProd[nX]:cnae} )
		else
			Return 'Campo "cnae" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'tipoRegime')
			aAdd(aProdAux, {"B1_TPREG", aJsonProd[nX]:tipoRegime} )
		else
			Return 'Campo "tipoRegime" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'retemPIS')
			aAdd(aProdAux, {"B1_PIS", aJsonProd[nX]:retemPIS} )
		else
			Return 'Campo "retemPIS" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'percentualPIS')
			aAdd(aProdAux, {"B1_PPIS", aJsonProd[nX]:percentualPIS} )
		else
			Return 'Campo "percentualPIS" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'percentualCofins')
			aAdd(aProdAux, {"B1_PCOFINS", aJsonProd[nX]:percentualCofins} )
		else
			Return 'Campo "percentualCofins" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'impostoRenda')
			aAdd(aProdAux, {"B1_IRRF", aJsonProd[nX]:impostoRenda} )
		else
			Return 'Campo "impostoRenda" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'codServicoISS')
			aAdd(aProdAux, {"B1_CODISS", aJsonProd[nX]:codServicoISS} )
		else
			Return 'Campo "codServicoISS" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'aliqISS')
			aAdd(aProdAux, {"B1_ALIQISS", aJsonProd[nX]:aliqISS} )
		else
			Return 'Campo "aliqISS" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'billing')
			aAdd(aProdAux, {"B1_XBILLI", aJsonProd[nX]:billing} )
		else
			Return 'Campo "billing" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'qtdMinima')
			aAdd(aProdAux, {"B1_XQTDMIN", aJsonProd[nX]:qtdMinima} )
		else
			Return 'Campo "qtdMinima" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'qtdMaxima')
			aAdd(aProdAux, {"B1_XQTDMAX", aJsonProd[nX]:qtdMaxima} )
		else
			Return 'Campo "qtdMaxima" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'percentualParceiro')
			aAdd(aProdAux, {"B1_XPERPAR", aJsonProd[nX]:percentualParceiro} )
		else
			Return 'Campo "percentualParceiro" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'tipoProduto')
			aAdd(aProdAux, {"B1_XTPPROD", aJsonProd[nX]:tipoProduto} )
		else
			Return 'Campo "tipoProduto" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'codigoParceiro')
			aAdd(aProdAux, {"B1_XCODPAR", aJsonProd[nX]:codigoParceiro} )
		else
			Return 'Campo "codigoParceiro" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'tes')
			aAdd(aProdAux, {"B1_TS", aJsonProd[nX]:tes} )
		else
			Return 'Campo "tes" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'execucaoServico')
			aAdd(aProdAux, {"B1_MEPLES", aJsonProd[nX]:execucaoServico} )
		else
			Return 'Campo "execucaoServico" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'mailParceiro')
			aAdd(aProdAux, {"B1_XMAILPA", aJsonProd[nX]:mailParceiro} )
		else
			Return 'Campo "mailParceiro" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'maisInformacao')
			aAdd(aProdAux, {"B1_XINFORM", aJsonProd[nX]:maisInformacao} )
		else
			Return 'Campo "maisInformacao" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'moeda')
			aAdd(aProdAux, {"B1_XMOEDA", aJsonProd[nX]:moeda} )
		else
			Return 'Campo "moeda" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'politicasComerciais')
			aAdd(aProdAux, {"B1_XDNEGOC", aJsonProd[nX]:politicasComerciais} )
		else
			Return 'Campo "politicasComerciais" não informado no objeto json "produtos" '
		endIf

		// Campos Base portfólio e não obrigatórios
		If AttIsMemberOf(aJsonProd[nX], 'tipoLicenca')
			aAdd(aProdAux, {"B1_XTPLIC", aJsonProd[nX]:tipoLicenca} )
		EndIf
		If AttIsMemberOf(aJsonProd[nX], 'modeloComercial')
			aAdd(aProdAux, {"B1_XMODCOM", aJsonProd[nX]:modeloComercial} )
		EndIf
		If AttIsMemberOf(aJsonProd[nX], 'abrangencia')
			aAdd(aProdAux, {"B1_XABRANG", aJsonProd[nX]:abrangencia} )
		EndIf
		If AttIsMemberOf(aJsonProd[nX], 'marcaTotvs')
			aAdd(aProdAux, {"B1_XFAMILI", aJsonProd[nX]:marcaTotvs} )
		EndIf
		If AttIsMemberOf(aJsonProd[nX], 'areaResponsavel')
			aAdd(aProdAux, {"B1_XSEGMEN", aJsonProd[nX]:areaResponsavel} )
		EndIf
		If AttIsMemberOf(aJsonProd[nX], 'licenciamento')
			aAdd(aProdAux, {"B1_XTPLICE", aJsonProd[nX]:licenciamento} )
		EndIf
		If AttIsMemberOf(aJsonProd[nX], 'grpSolucao')
			aAdd(aProdAux, {"B1_XGRPSLU", aJsonProd[nX]:grpSolucao} )
		EndIf
		If AttIsMemberOf(aJsonProd[nX], 'tabPrecoOficial')
			aAdd(aProdAux, {"B1_XTBPREC", aJsonProd[nX]:tabPrecoOficial} )
		EndIf
		If AttIsMemberOf(aJsonProd[nX], 'qtdLicencas')
			aAdd(aProdAux, {"B1_XQTDLI", aJsonProd[nX]:qtdLicencas} )
		EndIf
		If AttIsMemberOf(aJsonProd[nX], 'descricaoSobConsumo')
			aAdd(aProdAux, {"B1_XDSOBCO", aJsonProd[nX]:descricaoSobConsumo} )
		EndIf
		If AttIsMemberOf(aJsonProd[nX], 'tipo')
			aAdd(aProdAux, {"B1_TIPO", aJsonProd[nX]:tipo} )
		EndIf
		If AttIsMemberOf(aJsonProd[nX], 'codProcProdutivo')
			aAdd(aProdAux, {"B1_CODPROC", aJsonProd[nX]:codProcProdutivo} )
		else
			Return 'Campo "codProcProdutivo" não informado no objeto json "produtos" '
		endIf
		If AttIsMemberOf(aJsonProd[nX], 'descProcProdutivo')
			aAdd(aProdAux, {"B1_VM_PROC", aJsonProd[nX]:descProcProdutivo} )
		else
			Return 'Campo "descProcProdutivo" não informado no objeto json "produtos" '
		endIf

		// Campos padrões
		aAdd(aProdAux, {"B1_UM",'UN'} )
		aAdd(aProdAux, {"B1_LOCPAD",'01'} )
		aAdd(aProdAux, {"B1_INSS",'N'} )
		aAdd(aProdAux, {"B1_PCSLL",0} )
		aAdd(aProdAux, {"B1_TIPCONV","M"} )
		aAdd(aProdAux, {"B1_MCUSTD","1"} )
		aAdd(aProdAux, {"B1_TIPODEC","N"} )
		aAdd(aProdAux, {"B1_RASTRO","N"} )
		aAdd(aProdAux, {"B1_UREV",ddatabase} )
		aAdd(aProdAux, {"B1_DATREF",ddatabase} )
		aAdd(aProdAux, {"B1_MRP","S"} )
		aAdd(aProdAux, {"B1_LOCALIZ","N"} )
		aAdd(aProdAux, {"B1_CONTRAT","N"} )
		aAdd(aProdAux, {"B1_ANUENTE","2"} )
		aAdd(aProdAux, {"B1_IMPORT","N"} )
		aAdd(aProdAux, {"B1_TIPOCQ","M"} )
		aAdd(aProdAux, {"B1_SOLICIT","N"} )
		aAdd(aProdAux, {"B1_AGREGCU","2"} )
		aAdd(aProdAux, {"B1_DESPIMP","N"} )
		aAdd(aProdAux, {"B1_FLAGSUG","1"} )
		aAdd(aProdAux, {"B1_CLASSVE","1"} )
		aAdd(aProdAux, {"B1_MIDIA","2"} )
		aAdd(aProdAux, {"B1_QTDSER","1"} )
		aAdd(aProdAux, {"B1_ENVOBR","0"} )
		aAdd(aProdAux, {"B1_ATIVO","S"} )
		aAdd(aProdAux, {"B1_CPOTENC","2"} )
		aAdd(aProdAux, {"B1_USAFEFO","1"} )
		aAdd(aProdAux, {"B1_ESCRIPI","3"} )
		aAdd(aProdAux, {"B1_MSBLQL","2"} )
		aAdd(aProdAux, {"B1_PRODSBP","P"} )
		aAdd(aProdAux, {"B1_CRICMS","0"} )
		aAdd(aProdAux, {"B1_RETOPER","2"} )
		aAdd(aProdAux, {"B1_FETHAB","N"} )
		aAdd(aProdAux, {"B1_RICM65","2"} )
		aAdd(aProdAux, {"B1_PRN944I","S"} )
		aAdd(aProdAux, {"B1_CARGAE","2"} )
		aAdd(aProdAux, {"B1_GARANT","2"} )
		aAdd(aProdAux, {"B1_IDHIST",Iif(FindFunction("IdHistFis"), IdHistFis(),"")} )
		aAdd(aProdAux, {"B1_XRV","2"} )
		aAdd(aProdAux, {"B1_XIMSEGM","2"} )
		aAdd(aProdAux, {"B1_XAPROSA","2"} )
		aAdd(aProdAux, {"B1_XALTQT","1"} )

		If X3Uso("B1_XMIGINT")
			aAdd(aProdAux, {"B1_XMIGINT","2"} )
		EndIf

		If X3Uso("B1_XCANCIN")
			aAdd(aProdAux, {"B1_XCANCIN", "2"} )
		EndIf

		AAdd(aProdGrv, aProdAux)

		// Complemento de Produto - B5_XDESOFE
		If AttIsMemberOf(aJsonProd[nX], 'adicionaisProduto')
			If AttIsMemberOf(aJsonProd[nX]:adicionaisProduto, 'apresentacaoResumida')
				AADD(aDadosAdic, aJsonProd[nX]:adicionaisProduto:apresentacaoResumida)
			Else
				AADD( aDadosAdic, '' )
			EndIf
			If AttIsMemberOf(aJsonProd[nX]:adicionaisProduto, 'dimensao')
				AADD(aDadosAdic, aJsonProd[nX]:adicionaisProduto:dimensao)
			Else
				AADD( aDadosAdic, '' )
			EndIf
			If AttIsMemberOf(aJsonProd[nX]:adicionaisProduto, 'modeloPagamento')
				AADD(aDadosAdic, aJsonProd[nX]:adicionaisProduto:modeloPagamento)
			Else
				AADD( aDadosAdic, '' )
			EndIf
			If AttIsMemberOf(aJsonProd[nX]:adicionaisProduto, 'percentualRepasse')
				AADD(aDadosAdic, aJsonProd[nX]:adicionaisProduto:percentualRepasse)
			Else
				AADD( aDadosAdic, 0 )
			EndIf
		Else
			AADD( aDadosAdic, '' )
			AADD( aDadosAdic, '' )
			AADD( aDadosAdic, '' )
			AADD( aDadosAdic, 0 )
		EndIf

		// Efetua gravação
		aRet := S03GrvProdx(aProdGrv,aDadosAdic)

		If !aRet[1]
			aAdd(aRet,{.F.,aRet[2]})
		Else

			aAdd(aPrdxOfer,{SB1->B1_COD,Alltrim(SB1->B1_DESC),SB1->B1_PRV1,If ( AttIsMemberOf(aJsonProd[nX], 'indiceAcessorio') .And. aJsonProd[nX]:indiceAcessorio >=0 , aJsonProd[nX]:indiceAcessorio , "") })

			// Após gravação com alias posicionado, constroi demais arrays para gravação x amarração
			// Tratativa para Regra de Dependência
			If AttIsMemberOf(aJsonProd[nX], 'regraDependencia') .And. aJsonProd[nX]:regraDependencia
				AAdd(aProdPNP, {cProdOld,;
					SB1->B1_COD,;
					Iif(Empty(cProdOld), SB1->B1_XQTDMIN, nMaxOld) })

				cProdOld    := SB1->B1_COD
				nMaxOld     := SB1->B1_XQTDMAX
			EndIf

			// Gravação do Produto no Cadastro de Oferta
			If AttIsMemberOf(aJsonProd[nX], 'cadastroofertasProduto')

				If AttIsMemberOf(aJsonProd[nX]:cadastroofertasProduto, 'codigoOferta') .AND. AttIsMemberOf(aJsonProd[nX]:cadastroofertasProduto, 'descricaoOferta')

					If  !Empty(aJsonProd[nX]:cadastroofertasProduto:codigoOferta)
						dbSelectArea("PA8")
						dbSetOrder(1)
						If  !dbSeek(xFilial("PA8 ") + Alltrim(aJsonProd[nX]:cadastroofertasProduto:codigoOferta))
							Return '"codigoOferta" Informado, não existe no Cadastro'
						Endif

					Endif

					AAdd(aAuxPA8PAD, {"PA8_CODIGO",    aJsonProd[nX]:cadastroofertasProduto:codigoOferta} )         // Código da Oferta
					AAdd(aAuxPA8PAD, {"PA8_DESCRI",    aJsonProd[nX]:cadastroofertasProduto:descricaoOferta} )      // Descrição da Oferta
					AAdd(aAuxPA8PAD, {"PAD_CODIGO",    SB1->B1_COD } )                                              // Código do Produto

					//Novos campos - TINDIN-3421
					If AttIsMemberOf(aJsonProd[nX]:cadastroofertasProduto, 'apresentacaoOferta')

						aAdd(aAuxPA8PAD, {"PA8_APRESE", AllTrim( aJsonProd[nX]:cadastroofertasProduto:apresentacaoOferta) } )

					Else

						aAdd(aAuxPA8PAD, {"PA8_APRESE", "#NOTALT" } )

					endIf

					If AttIsMemberOf(aJsonProd[nX]:cadastroofertasProduto, 'licenseServer')
						If !lLicSer
							aAdd(aAuxPA8PAD, {"PA8_LICSER", If(aJsonProd[nX]:cadastroofertasProduto:licenseServer,"1","2") } )
						Else
						    aAdd(aAuxPA8PAD, {"PA8_LICSER", aJsonProd[nX]:cadastroofertasProduto:licenseServer } )
                    	EndIf
                	Else
                    	If !lLicSer
							aAdd(aAuxPA8PAD, {"PA8_LICSER", "#NOTALT" } )
	                    Else
							aAdd(aAuxPA8PAD, {"PA8_LICSER", Alltrim(If(Empty(GetSx3Cache( "PA8_LICSER" ,"X3_RELACAO" )),"",&(GetSx3Cache( "PA8_LICSER" ,"X3_RELACAO" )))) } )
						EndIf
					EndIf

					If AttIsMemberOf(aJsonProd[nX]:cadastroofertasProduto, 'email')

						aAdd(aAuxPA8PAD, {"PA8_EMAIL", AllTrim(aJsonProd[nX]:cadastroofertasProduto:email) } )

					Else

						aAdd(aAuxPA8PAD, {"PA8_EMAIL", "" } )
					EndIf

					//----------------------------------------------------
					//https://jiraproducao.totvs.com.br/browse/TIPDBP-1935
					If AttIsMemberOf(aJsonProd[nX]:cadastroofertasProduto, 'flagAtivacao')
						aAdd(aAuxPA8PAD, {"PA8_FLGBIL", AllTrim(aJsonProd[nX]:cadastroofertasProduto:flagAtivacao) } )

					Else
						aAdd(aAuxPA8PAD, {"PA8_FLGBIL", "" } )
					EndIf


					If AttIsMemberOf(aJsonProd[nX]:cadastroofertasProduto, 'dataFechamento')
						aAdd(aAuxPA8PAD, {"PA8_DIFECN", aJsonProd[nX]:cadastroofertasProduto:dataFechamento } )
					Else
						aAdd(aAuxPA8PAD, {"PA8_DIFECN", 0 } )
					EndIf

					If AttIsMemberOf(aJsonProd[nX]:cadastroofertasProduto, 'modeloBilling')
						aAdd(aAuxPA8PAD, {"PA8_MODBIL", AllTrim(aJsonProd[nX]:cadastroofertasProduto:modeloBilling) } )
					Else
						aAdd(aAuxPA8PAD, {"PA8_MODBIL", "" } )
					EndIf

					If AttIsMemberOf(aJsonProd[nX]:cadastroofertasProduto, 'modeloMetrica')
						aAdd(aAuxPA8PAD, {"PA8_XMETRI", AllTrim(aJsonProd[nX]:cadastroofertasProduto:modeloMetrica) } )
					Else
						aAdd(aAuxPA8PAD, {"PA8_XMETRI", "" } )
					EndIf

					If AttIsMemberOf(aJsonProd[nX]:cadastroofertasProduto, 'formaEnvio')
						aAdd(aAuxPA8PAD, {"PA8_XTPENV", AllTrim(aJsonProd[nX]:cadastroofertasProduto:formaEnvio) } )
					Else
						aAdd(aAuxPA8PAD, {"PA8_XTPENV", "" } )
					EndIf

					AAdd(aOfertas, aAuxPA8PAD)
				Else
					Return 'Campo "codigoOferta / descricaoOferta" não informado no objeto json "cadastroofertasProduto" Item: ' + cValtochar(nX)
				EndIf

			Else
				Return 'Estrutura "cadastroofertasProduto" não informado no objeto json "produtos" Item: ' + cValtochar(nX)
			EndIf

			// Tratativa para tabela de preço
			If AttIsMemberOf(aJsonProd[nX], 'recorrenciaFaturamento')
				AAdd(aPrdDA1, { SB1->B1_COD , aJsonProd[nX]:valorBase, aJsonProd[nX]:recorrenciaFaturamento, cCategDA1 } )
			Else
				Return 'Campo "recorrenciaFaturamento" não informado no objeto json "produtos" Item: ' + cValtochar(nX)
			EndIf

			// Tratativa para Desconto por Produto
			If AttIsMemberOf(aJsonProd[nX], 'descontoPorProduto')
				AAdd(aPrdPDX, {;
					SB1->B1_COD,;
					Iif(AttIsMemberOf(aJsonProd[nX]:descontoPorProduto, 'permiteDesconto'),Iif(aJsonProd[nX]:descontoPorProduto:permiteDesconto,"1","2"),"2"),;
					Iif(AttIsMemberOf(aJsonProd[nX]:descontoPorProduto, 'percentualDesconto'),Round(aJsonProd[nX]:descontoPorProduto:percentualDesconto,2),0),;
					Iif(AttIsMemberOf(aJsonProd[nX]:descontoPorProduto, 'permiteOver'),Iif(aJsonProd[nX]:descontoPorProduto:permiteOver,"1","2"),"2"),;
					Iif(AttIsMemberOf(aJsonProd[nX]:descontoPorProduto, 'percentualOver'),Round(aJsonProd[nX]:descontoPorProduto:percentualOver,2),0),;
					Iif(AttIsMemberOf(aJsonProd[nX]:descontoPorProduto, 'valorDesconto'),Round(aJsonProd[nX]:descontoPorProduto:valorDesconto,2),0),;
					Iif(AttIsMemberOf(aJsonProd[nX]:descontoPorProduto, 'valorOver'),Round(aJsonProd[nX]:descontoPorProduto:valorOver,2),0),;
					Iif(AttIsMemberOf(aJsonProd[nX]:descontoPorProduto, 'permiteDescontoTemporario'),Iif(aJsonProd[nX]:descontoPorProduto:permiteDescontoTemporario,"1","2"),"2"),;
					Iif(AttIsMemberOf(aJsonProd[nX]:descontoPorProduto, 'percentualMaxDescontoTemporario'),Round(aJsonProd[nX]:descontoPorProduto:percentualMaxDescontoTemporario,2),0),;
					Iif(AttIsMemberOf(aJsonProd[nX]:descontoPorProduto, 'valorMaxDescontoTemporario'),Round(aJsonProd[nX]:descontoPorProduto:valorMaxDescontoTemporario,2),0),;
					Iif(AttIsMemberOf(aJsonProd[nX]:descontoPorProduto, 'qtdMesesDescontoTermporario'),Round(aJsonProd[nX]:descontoPorProduto:qtdMesesDescontoTermporario,2),0) } )
			Else
				Return 'Estrutura "descontoPorProduto" não informado no objeto json "produtos" Item: ' + cValtochar(nX)
			EndIf

		Endif

	Next

	If aRet[1]

		// Gravação de Produtos x Oferta
		aRet := S03GrvOferta(aOfertas)

		If  aRet[1]

			For nRt := 1 To Len(aPrdxOfer)

				AAdd(aPrdSB1, JsonUtil():New() )
				nPos := Len(aPrdSB1)
				aPrdSB1[nPos]:Putval('partnumber',aPrdxOfer[nRt,1])
				aPrdSB1[nPos]:Putval('descricaoReduzida',aPrdxOfer[nRt,2])
				aPrdSB1[nPos]:Putval('valorBase',aPrdxOfer[nRt,3])
				If  !Empty(aPrdxOfer[nRt,4])
					aPrdSB1[nPos]:Putval('acessorio',aPrdxOfer[nRt,4])
				EndIf

				nOferta := Ascan(aRet[3], {|x| Alltrim(x[2]) == Alltrim(aPrdxOfer[nRt,1])})

				IF  nOferta > 0
					aPrdSB1[nPos]:Putval('oferta',aRet[3][nOferta][1])
					aPrdSB1[nPos]:Putval('descricaooferta',Alltrim(aRet[3][nOferta][3]))
				ENDIF

			Next nRt

		else

			Return aRet[2]

		ENDIF

	else

		Return aRet[2]

	EndIf

Return cRet

/*/{Protheus.doc} vldDescSb1
	Valida descrição do produto
<AUTHOR> Osti
@since 03/09/2020
@version undefined
@type function
/*/
Static Function vldDescSb1(cDescProd)
	Local cCodPro   := ""
	Local cAliasDes := GetNextAlias()

	BeginSql Alias cAliasDes
    SELECT B1_COD
    FROM %Table:SB1%
    WHERE
        B1_FILIAL = %xFilial:SB1%
        AND B1_DESC = %Exp:cDescProd%
        AND %NotDel%
	EndSql

	If (cAliasDes)->(!EOF())
		cCodPro := Alltrim((cAliasDes)->B1_COD)
	EndIf

	(cAliasDes)->(dbCloseArea())

Return cCodPro

/*/{Protheus.doc} S03VldExec
	Estrutura dos campos Grupos/Pessoas (Executivos)
<AUTHOR> Osti
@since 28/07/2020
@version undefined
@type function
/*/
Static Function S03VldExec(aJsonExec, aExecutivs)
	Local cRet      := ''
	Local aAuxExec  := {}
	Local nX

	For nX := 1 To Len(aJsonExec)
		aAuxExec    := {}

		// Campos obrigatórios a serem informados
		If AttIsMemberOf(aJsonExec[nX], 'tipoEntidade')
			aAdd(aAuxExec, {"PL2_TIPO", aJsonExec[nX]:tipoEntidade} )
		else
			Return 'Campo "tipoEntidade" não informado no objeto json "executivos" '
		endIf
		If AttIsMemberOf(aJsonExec[nX], 'entidade')
			aAdd(aAuxExec, {"PL2_ENTIDA", aJsonExec[nX]:entidade} )
		else
			Return 'Campo "entidade" não informado no objeto json "executivos" '
		endIf
		If AttIsMemberOf(aJsonExec[nX], 'chave')
			aAdd(aAuxExec, {"PL2_CHAVE", aJsonExec[nX]:chave} )
		else
			Return 'Campo "chave" não informado no objeto json "executivos" '
		endIf

		AAdd(aExecutivs, aAuxExec)
	Next

Return cRet

/*/{Protheus.doc} S03GrvProdx
	Grava produtos e seu vinculo grupo de comissão
<AUTHOR> Osti
@since 22/07/2020
@version undefined
@type function
/*/
Static Function S03GrvProdx(aProdutos,aDadosAdic)
	Local aRet      := { .T. }
	Local nPosGc    := 0
	Local oModelPZ5
	Local oMasterPZ5
	Local nX
	Local nY
	Local lDesc 	:= .F.
	Local cCodProcI	:= ""
	Local cNextCod	:= ""

	For nX := 1 To Len(aProdutos)

		dbSelectArea("SB1")
		SB1->(dbSetOrder(1))

		RecLock("SB1", .T.)
		For nY := 1 To Len(aProdutos[nX])
			If aProdutos[nX][nY][1] == "B1_COD"
				cNextCod := "AUT."+ aProdutos[nX][nY][2] +"."+ S03VldCodp(aProdutos[nX][nY][2])
				SB1->B1_COD := cNextCod
			Else
				SB1->&(aProdutos[nX][nY][1]) := aProdutos[nX][nY][2]
				If aProdutos[nX][nY][1] == "B1_CODPROC" 
					cCodProcI := aProdutos[nX][nY][2]
					SB1->B1_CODPROC := cCodProcI
				Endif
		    	If aProdutos[nX][nY][1] == "B1_VM_PROC" 
					cDescProcI := aProdutos[nX][nY][2]
					If !Empty(cCodProcI) .and. !Empty(cDescProcI)
						lDesc := .T.
					Endif
				EndIf
			EndIf
		Next
		SB1->(MsUnLock())

		If lDesc
			SYPDesProc(cCodProcI,cDescProcI)
		EndIf

		dbSelectArea("SB5")
		SB5->(dbSetOrder(1))

		RecLock("SB5", .T.)
		SB5->B5_COD     := SB1->B1_COD
		SB5->B5_UMIND   := '1'
		SB5->B5_XDESOFE := aDadosAdic[1]
		SB5->B5_XDIMENS := aDadosAdic[2]
		SB5->B5_XMDPGTO := aDadosAdic[3]
		SB5->B5_XPERREP := aDadosAdic[4]
		SB5->(MsUnLock())

		// Grava amarração entre Grupo de Comissão e Produto
		If (nPosGc := Ascan(aProdutos[nX], {|x| x[1] == "B1_XGC"})) > 0
			oModelPZ5 	:=  FWLoadModel( 'ACCSTA91' )
			oModelPZ5:SetOperation(MODEL_OPERATION_INSERT)
			If oModelPZ5:Activate()
				oMasterPZ5  := oModelPZ5:GetModel("PZ5MASTER")
				oMasterPZ5:LoadValue("PZ5_PROD", SB1->B1_COD )
				oMasterPZ5:SetValue("PZ5_GCOMIS", aProdutos[nX][nPosGc][2] )
				oMasterPZ5:SetValue("PZ5_DVIGDE", CTOD("01/01/1980") ) // Solicitação de usuário para set data 01/01/1980
				oMasterPZ5:SetValue("PZ5_DVIGAT", CTOD("31/12/2049") )
				If oModelPZ5:VldData()
					oModelPZ5:CommitData()
					oModelPZ5:DeActivate()
				else
					Return s003RetErro(oModelPZ5)
				endIf
			else
				Return s003RetErro(oModelPZ5)
			EndIf
			oModelPZ5:Destroy()
		EndIf

	Next

Return aRet


/*/{Protheus.doc} SYPAltProc
	Realiza inclusao ou alteracao no cadastro de produtos no campo memo referenciado no 
	campo B1_CODPROC como chave de busca
	@type  Static Function
	<AUTHOR> Jorge
	@since 12/06/2024
	@version version 1.0
	@param cCodProc, cDescProc, codigo do processo e descricao do processo
	@return nenhum
	@see (links_or_references)
/*/
Static Function SYPDesProc(pCodProc,pDescProc)

    Local aArea     := GetArea()
	Local cString 	:= pDescProc
    Local aPart 	:= {}
    Local nLen  	:= 48
    Local nPos  	:= 1
    Local nSeq  	:= 1
    Local cTemp 	:= ""
	Local i 		:= 1

	While nPos <= Len(cString)
        cTemp := SubStr(cString, nPos, nLen)
        AAdd(aPart, {Padl(cvaltochar(nSeq),3,"0"), cTemp})
        nPos += nLen
        nSeq++
    End

	SYP->(dbSelectArea("SYP"))
	SYP->(dbSetOrder(1))
	If SYP->(!dbSeek(xFilial("SYP") + pCodProc))

		For i := 1 to Len(aPart)

			RecLock("SYP", .T.)
			SYP->YP_FILIAL  := xFIlial("SYP")
			SYP->YP_CHAVE   := pCodProc
			SYP->YP_SEQ     := aPart[i][1]
			SYP->YP_TEXTO   := alltrim(aPart[i][2])
			SYP->YP_CAMPO   := "B1_CODPROC"
			SYP->(MsUnLock())

		Next
	Else
		For i := 1 to Len(aPart)

			RecLock("SYP", .F.)
			SYP->YP_FILIAL  := xFIlial("SYP")
			SYP->YP_CHAVE   := pCodProc
			SYP->YP_SEQ     := aPart[i][1]
			SYP->YP_TEXTO   := alltrim(aPart[i][2])
			SYP->YP_CAMPO   := "B1_CODPROC"
			SYP->(MsUnLock())

		Next
	EndIf
	
	RestArea(aArea)

Return 

/*/{Protheus.doc} S03GrvOferta
Realiza inclusão ou alteração no cadastro de ofertas
@param	aOfertas, Array, Relação de Ofertas x Produtos para criação/atualização do cadastro de oferas
@return Nenhum
<AUTHOR> Gomes Júnior
@since		01/06/2021
@version	1.0
/*/
Static Function S03GrvOferta(aOfertas)
	Local aArea         := GetArea()
	Local nX
	Local nXP
	Local nOferta       := 0
	Local nOfertaNew    := 0
	Local aNewOferta    := {}
	Local oTCRMA185
	Local oPA8MASTER
	Local oPADDETAIL
	Local aRet          := {}
	Local aPrdxOfertas  := {}

	aOfertasTra := aSort(aOfertas,,,{|x,y| x[1] < y[1] })

	For nX := 1 To Len(aOfertasTra)

		If  Empty(aOfertasTra[nX][1][2])

			//Oferta Nova
			nOfertaNew := aScan(aNewOferta, { |X| Alltrim(X[2]) == Alltrim(aOfertasTra[nX][2][2]) } )

			If  nOfertaNew == 0
				aAdd(aNewOferta,{ "" ,aOfertasTra[nX][2][2] ,{aOfertasTra[nX][3][2] } , StrZero(nX,6), aOfertasTra[nX][4][2], aOfertasTra[nX][5][2], aOfertasTra[nX][6][2],aOfertasTra[nX][7][2],aOfertasTra[nX][8][2],aOfertasTra[nX][9][2],aOfertasTra[nX][10][2],aOfertasTra[nX][11][2] } )
			Else
				aAdd(aNewOferta[nOfertaNew,3],aOfertasTra[nX][3][2])
			Endif

		Else

			//Oferta Existente
			nOferta := aScan(aNewOferta, { |X| Alltrim(X[1]) == aOfertasTra[nX][1][2] } )

			If  nOferta == 0
				aAdd(aNewOferta,{ aOfertasTra[nX][1][2] ,aOfertasTra[nX][2][2] ,{aOfertasTra[nX][3][2] } , "000000", aOfertasTra[nX][4][2], aOfertasTra[nX][5][2], aOfertasTra[nX][6][2],aOfertasTra[nX][7][2],aOfertasTra[nX][8][2],aOfertasTra[nX][9][2],aOfertasTra[nX][10][2],aOfertasTra[nX][11][2] } )
			Else
				aAdd(aNewOferta[nOferta,3],aOfertasTra[nX][3][2])
			Endif

		Endif

	Next nX

	If  Len(aNewOferta) > 0

		For nX := 1 To Len(aNewOferta)

			IF  aNewOferta[nX,4] <> "000000" .AND. !U_S061VldDesc(aNewOferta[nX,2])
				aAdd(aRet,.F.)
				aAdd(aRet,'Já existe oferta com a descrição informada: (' +aNewOferta[nX,2]+ ') ')
				Exit
			ENDIF

			If  aNewOferta[nX,4] == "000000"

				dbSelectArea("PA8")
				PA8->(dbSetOrder(1))
				PA8->(dbSeek(xFilial("PA8") + aNewOferta[nX,1]))

				//Alteração de dados permitidos
				PA8->(RecLock('PA8',.F.))

				If aNewOferta[nX,5] != "#NOTALT"
					PA8->PA8_APRESE := aNewOferta[nX,5]
				EndIf
				If aNewOferta[nX,5] != "#NOTALT"
					PA8->PA8_LICSER := aNewOferta[nX,6]
				EndIf
				If aNewOferta[nX,5] != "#NOTALT"
					PA8->PA8_EMAIL  := aNewOferta[nX,7]
				EndIf
				//------------
				//https://jiraproducao.totvs.com.br/browse/TIPDBP-1935
				PA8->PA8_FLGBIL:=aNewOferta[nX,8]
				PA8->PA8_DIFECN:=aNewOferta[nX,9]
				PA8->PA8_MODBIL:=aNewOferta[nX,10]
				PA8->PA8_XMETRI:=aNewOferta[nX,11]
				PA8->PA8_XTPENV:=aNewOferta[nX,12]
				//------------
				PA8->(MsUnlock())

				For nXP := 1 To Len(aNewOferta[nX,3])
					RecLock("PAD", .T.)
					PAD->PAD_CODIGO := PA8->PA8_CODIGO
					PAD->PAD_REVISA := PA8->PA8_REVISA
					PAD->PAD_CODPRD := aNewOferta[nX,3][nXP]
					PAD->PAD_STATUS := "1"
					PAD->PAD_DATCAD := dDataBase
					PAD->PAD_USRCAD := "000000"
					PAD->PAD_DATREV := dDataBase
					PAD->PAD_USUREV := "000000"
					PAD->(MsUnlock())
					AAdd(aPrdxOfertas,{ PA8->PA8_CODIGO , aNewOferta[nX,3][nXP], PA8->PA8_DESCRI })
				Next nXP

				AAdd(aRet,.T.)
				AAdd(aRet,'Oferta Gravada')
				AAdd(aRet, aPrdxOfertas)

			Else

				oTCRMA185 :=  FWLoadModel( 'TCRMA185' )
				oTCRMA185:SetOperation(MODEL_OPERATION_INSERT)

				If  oTCRMA185:Activate()

					oPA8MASTER  := oTCRMA185:GetModel("PA8MASTER")
					oPADDETAIL  := oTCRMA185:GetModel("PADDETAIL")

					oPA8MASTER:SetValue("PA8_DESCRI", aNewOferta[nX,2])
					oPA8MASTER:SetValue("PA8_APRESE", aNewOferta[nX,5])
					oPA8MASTER:SetValue("PA8_LICSER", aNewOferta[nX,6])
					oPA8MASTER:SetValue("PA8_EMAIL", aNewOferta[nX,7])
					//------------------------------------------------
					//https://jiraproducao.totvs.com.br/browse/TIPDBP-1935
					oPA8MASTER:SetValue("PA8_FLGBIL", aNewOferta[nX,8])
					oPA8MASTER:SetValue("PA8_DIFECN", aNewOferta[nX,9])
					oPA8MASTER:SetValue("PA8_MODBIL", aNewOferta[nX,10])
					oPA8MASTER:SetValue("PA8_XMETRI", aNewOferta[nX,11])
					oPA8MASTER:SetValue("PA8_XTPENV", aNewOferta[nX,12])
					//------------------------------------------------

					For nXP := 1 To Len(aNewOferta[nX,3])
						oPADDETAIL:AddLine()
						oPADDETAIL:SetValue("PAD_CODPRD",aNewOferta[nX,3][nXP])
						aAdd(aPrdxOfertas,{oPA8MASTER:GetValue("PA8_CODIGO") , aNewOferta[nX,3][nXP], oPA8MASTER:GetValue("PA8_DESCRI") })
					Next nXP

					If  oTCRMA185:VldData()
						oTCRMA185:CommitData()
						oTCRMA185:DeActivate()
						aAdd(aRet,.T.)
						aAdd(aRet,'Oferta Gravada')
						aAdd(aRet,aPrdxOfertas)

					else
						Return s003RetErro(oTCRMA185)
					EndIf

				Else
					Return s003RetErro(oTCRMA185)
				EndIf

				oTCRMA185:Destroy()
			Endif

		Next nX

	Endif

	RestArea(aArea)

Return aRet


/*/{Protheus.doc} S03GrvPDX
	Grava desconto por produto
<AUTHOR> Osti
@since 23/07/2020
@version undefined
@type function
/*/
Static Function S03GrvPDX(aProdPDX)
	Local aRet      := { .T. }
	Local nX

	For nX := 1 To Len(aProdPDX)
		oModelPDX 	:=  FWLoadModel( 'TCRMA017' )
		oModelPDX:SetOperation(MODEL_OPERATION_INSERT)
		If oModelPDX:Activate()

			oMasterPDX  := oModelPDX:GetModel("PDXMASTER")
			oMasterPDX:LoadValue("PDX_COD"      ,aProdPDX[nX][1])
			oMasterPDX:SetValue("PDX_DESCON"    ,aProdPDX[nX][2])
			oMasterPDX:LoadValue("PDX_PEMAXD"   ,aProdPDX[nX][3])
			oMasterPDX:SetValue("PDX_OVER"      ,aProdPDX[nX][4])
			oMasterPDX:LoadValue("PDX_PEMAXO"   ,aProdPDX[nX][5])
			oMasterPDX:LoadValue("PDX_VLMAXD"   ,aProdPDX[nX][6])
			oMasterPDX:LoadValue("PDX_VLMAXO"   ,aProdPDX[nX][7])
			oMasterPDX:LoadValue("PDX_XDESCT"   ,aProdPDX[nX][8])
			oMasterPDX:LoadValue("PDX_XPMXDT"   ,aProdPDX[nX][9])
			oMasterPDX:LoadValue("PDX_VLRMAX"   ,aProdPDX[nX][10])
			oMasterPDX:LoadValue("PDX_QTMEST"   ,aProdPDX[nX][11])

			If oModelPDX:VldData()
				oModelPDX:CommitData()
				oModelPDX:DeActivate()
			else
				Return s003RetErro(oModelPDX)
			EndIf
		else
			Return s003RetErro(oModelPDX)
		EndIf
		oModelPDX:Destroy()
	Next

Return aRet

/*/{Protheus.doc} S03UpdDA1
	Atualiza com novos produtos na Tabela de Preço
<AUTHOR> Osti
@since 23/07/2020
@version undefined
@type function
/*/
Static Function S03UpdDA1(aProdDA1, cCodTab)
	Local cAliasDA1 := GetNextAlias()
	Local aRet      := { .T. }
	Local cNextItem
	Local nX

// Grava Tabela de preço (DA0 e DA1)
	dbSelectArea("DA0")
	DA0->(dbSetOrder(1))
	If DA0->(dbSeek(xFilial("DA0") + cCodTab ))

		BeginSql Alias cAliasDA1
        SELECT
            MAX(DA1_ITEM) MAX_ITEM
        FROM %Table:DA1%
        WHERE D_E_L_E_T_ = ' '
            AND DA1_CODTAB = %Exp:cCodTab%
		EndSql

		If (cAliasDA1)->(!EOF())
			cNextItem := (cAliasDA1)->MAX_ITEM
			For nX := 1 To Len(aProdDa1)
				cNextItem := Soma1(cNextItem)
				RecLock("DA1", .T.)
				DA1->DA1_CODTAB := cCodTab
				DA1->DA1_ITEM   := cNextItem
				DA1->DA1_CODPRO := aProdDa1[nX][1]
				DA1->DA1_PRCVEN := aProdDa1[nX][2]
				DA1->DA1_XTPFAT := aProdDa1[nX][3]
				DA1->DA1_XCATPR := aProdDa1[nX][4]
				DA1->DA1_DATVIG := CTOD("31/12/2049")
				DA1->DA1_ATIVO  := '1'
				DA1->DA1_TPOPER := '4'
				DA1->DA1_QTDLOT := 999999.99
				DA1->DA1_MOEDA  := 1
				DA1->DA1_XTPIND := '1'
				DA1->DA1_XTIPPR := '1'
				DA1->(MsUnlock())
			Next
		EndIf
		(cAliasDA1)->(dbCloseArea())

	EndIf

Return aRet

/*/{Protheus.doc} S03UpdPL2
	Atualiza com novos grupos/pessoas
<AUTHOR> Osti
@since 23/07/2020
@version undefined
@type function
/*/
Static Function S03UpdPL2(aExecPL2, cCodNiv)
	Local aItens    := {""}
	Local nNextItem
	Local nX
	Local nY
	Local cAliasPL2 := GetNextAlias()

	BeginSQL Alias cAliasPL2
    SELECT PL2_ITEM
    FROM %Table:PL2% PL2
    WHERE
        PL2_FILIAL = %xFilial:PL2%
        AND PL2_CODAGR = '000112'
        AND PL2_CODNIV = %Exp:cCodNiv%
        AND PL2.%NotDel%
	EndSql

	While (cAliasPL2)->(!EOF())
		AAdd(aItens, (cAliasPL2)->PL2_ITEM )
		(cAliasPL2)->(dbSkip())
	EndDo
	(cAliasPL2)->(dbCloseArea())

// Garante que ultima posição seja o maior item
	ASort(aItens)
	nNextItem := Val(ATail(aItens))

// Inclui novas linhas (PL2)
	For nX := 1 To Len(aExecPL2)
		RecLock("PL2",.T.)
		PL2->PL2_FILIAL := xFilial("PL2")
		PL2->PL2_CODAGR := "000112"
		PL2->PL2_CODNIV := cCodNiv
		PL2->PL2_ITEM   := StrZero(++nNextItem, 2)

		For nY := 1 To Len(aExecPL2[nX])
			PL2->&(aExecPL2[nX][nY][1]) := aExecPL2[nX][nY][2]
		Next
		PL2->(MsUnLock())
	Next

Return

/*/{Protheus.doc} S03GrvDA1
	Grava Tabela de Preço
<AUTHOR> Osti
@since 23/07/2020
@version undefined
@type function
/*/
Static Function S03GrvDA1(aProdDA1, cCodTab)
	Local aRet      := { .T. }
	Local oModelDa0
	Local oDetailDA1
	local nItemDa1  := 1
	Local nX

// Grava Tabela de preço (DA0 e DA1)
	oModelDa0 	:=  FWLoadModel( 'OMSA010' )
	oModelDa0:SetOperation(MODEL_OPERATION_INSERT)
	oMasterDA0  := oModelDa0:GetModel("DA0MASTER")
	oDetailDA1  := oModelDa0:GetModel("DA1DETAIL")
	oDetailDA1:SetOptional(.T.)

	If oModelDa0:Activate()
		cCodTab     := oMasterDA0:GetValue("DA0_CODTAB")
		oMasterDA0:SetValue("DA0_DESCRI",cDescricao)
		oMasterDA0:SetValue("DA0_DATATE",CTOD("31/12/2049"))
		oMasterDA0:SetValue("DA0_XTPVEN",'1')
		oMasterDA0:SetValue("DA0_XPAIS",'105')

		For nX := 1 To Len(aProdDa1)
			If nItemDa1 > 1
				oDetailDA1:AddLine()
			EndIf
			oDetailDA1:SetValue("DA1_ITEM",StrZero(nItemDa1,TamSX3("DA1_ITEM")[1]))
			oDetailDA1:LoadValue("DA1_CODPRO",aProdDa1[nX][1])
			oDetailDA1:SetValue("DA1_PRCVEN",aProdDa1[nX][2])
			oDetailDA1:SetValue("DA1_XTPFAT",aProdDa1[nX][3])
			oDetailDA1:SetValue("DA1_XCATPR",aProdDa1[nX][4])
			oDetailDA1:SetValue("DA1_DATVIG",CTOD("31/12/2049"))
			nItemDa1++
		Next

		If Len(aProdDa1) == 0
			oDetailDA1:GoLine(1)
			oDetailDA1:DeleteLine()
		EndIf

		If oModelDa0:VldData()
			If lTest
				RollBackSX8()
			Else
				oModelDa0:CommitData()
				oModelDa0:DeActivate()
			EndIf
		else
			Return s003RetErro(oModelDa0)
		EndIf
	else
		Return s003RetErro(oModelDa0)
	EndIf

Return aRet

/*/{Protheus.doc} S03GrvPNP
	Grava Regra de Dependência
<AUTHOR> Osti
@since 10/09/2020
@version undefined
@type function
/*/
Static Function S03GrvPNP(aProdPNP, cNivelAgr, cDescAOM)
	Local aRet      := { .T. }
	Local cAliasPNP := GetNextAlias()

	dbSelectArea("PNP")
	PNP->(dbSetOrder(1))

	BeginSQL Alias cAliasPNP
    SELECT R_E_C_N_O_ RECNO
    FROM %Table:PNP%
    WHERE PNP_FILIAL = %xFIlial:PNP%
        AND PNP_CODNIV = %Exp:cNivelAgr%
        AND %NotDel%
	EndSql

	If (cAliasPNP)->(!EOF())
		PNP->(dbGoTo( (cAliasPNP)->RECNO) )
		aRet := S03UpdPNP(aProdPNP)
	Else
		aRet := S03InsPNP(aProdPNP, cNivelAgr, cDescAOM)
	EndIf
	(cAliasPNP)->(dbCloseArea())

Return aRet

/*/{Protheus.doc} S03UpdPNP
	Atualiza Regra de Dependência
<AUTHOR> Osti
@since 10/09/2020
@version undefined
@type function
/*/
Static Function S03UpdPNP(aProdPNP)
	Local oModelPNP
	Local oDetailPNQ
	Local oDetailPNR
	Local aRet      := { .T. }
	Local aItens    := {""}
	Local nNextItem
	Local nX

	oModelPNP 	:=  FWLoadModel('TCRMA098')
	oModelPNP:SetOperation(MODEL_OPERATION_UPDATE)

	If oModelPNP:Activate()
		oDetailPNQ := oModelPNP:GetModel("PNQDETAIL")

		For nX := 1 To oDetailPNQ:Length()
			AAdd(aItens, oDetailPNQ:GetValue("PNQ_ITEM", nX))
		Next
		// Garante que ultima posição seja o maior item
		ASort(aItens)
		nNextItem := Val(ATail(aItens))

		For nX := 1 To Len(aProdPNP)
			oDetailPNQ:AddLine()
			oDetailPNQ:SetValue("PNQ_CODIGO", PNP->PNP_CODIGO)
			oDetailPNQ:SetValue("PNQ_ITEM", StrZero(++nNextItem, 3))
			oDetailPNQ:SetValue("PNQ_CHAVE", "1")   //1=Produto / 2=Grupo
			oDetailPNQ:SetValue("PNQ_CODPRO", aProdPNP[nX][2])

			oDetailPNR := oModelPNP:GetModel("PNRDETAIL")
			oDetailPNR:AddLine()
			If !Empty(aProdPNP[nX][1])
				oDetailPNR:SetValue("PNR_CODPRO", aProdPNP[nX][1])
			Else
				oDetailPNR:SetValue("PNR_CODPRO", aProdPNP[nX][2])
			EndIf
			oDetailPNR:SetValue("PNR_CODIGO", PNP->PNP_CODIGO)
			oDetailPNR:SetValue("PNR_ITEM", oDetailPNQ:GetValue("PNQ_ITEM"))
			oDetailPNR:SetValue("PNR_CHAVE", "1")   //1=Produto / 2=Grupo
			oDetailPNR:SetValue("PNR_SEQ", '01')
			oDetailPNR:SetValue("PNR_TIPVAL", '1')
			oDetailPNR:SetValue("PNR_VALREF", aProdPNP[nX][3])
		Next

		If oModelPNP:VldData()
			If lTest
				RollBackSX8()
			Else
				oModelPNP:CommitData()
				oModelPNP:DeActivate()
			EndIf
		else
			Return s003RetErro(oModelPNP)
		EndIf
	else
		Return s003RetErro(oModelPNP)
	EndIf

Return aRet

/*/{Protheus.doc} S03InsPNP
	Insere Regra de Dependência
<AUTHOR> Osti
@since 10/09/2020
@version undefined
@type function
/*/
Static Function S03InsPNP(aProdPNP, cNivelAgr, cDescAOM)
	Local oModelPNP
	Local oMasterPNP
	Local oDetailPNQ
	Local oDetailPNR
	Local aRet      := { .T. }
	Local nItemPNQ  := 1
	Local nX

	oModelPNP 	:=  FWLoadModel('TCRMA098')
	oModelPNP:SetOperation(MODEL_OPERATION_INSERT)

	If oModelPNP:Activate()
		oMasterPNP := oModelPNP:GetModel("PNPMASTER")
		oMasterPNP:SetValue("PNP_DESCRI", cDescAOM)
		oMasterPNP:SetValue("PNP_STATUS", '1')
		oMasterPNP:SetValue("PNP_CODAGR", '000112')
		oMasterPNP:SetValue("PNP_CODNIV", cNivelAgr)
		oMasterPNP:SetValue("PNP_TIPO", '1')

		oDetailPNQ := oModelPNP:GetModel("PNQDETAIL")
		For nX := 1 To Len(aProdPNP)
			oDetailPNQ:AddLine()
			oDetailPNQ:SetValue("PNQ_ITEM",StrZero(nItemPNQ,3))
			oDetailPNQ:SetValue("PNQ_CODIGO", oMasterPNP:GetValue("PNP_CODIGO"))
			oDetailPNQ:SetValue("PNQ_CHAVE", "1")   //1=Produto / 2=Grupo
			oDetailPNQ:SetValue("PNQ_CODPRO", aProdPNP[nX][2])

			oDetailPNR := oModelPNP:GetModel("PNRDETAIL")
			oDetailPNR:AddLine()
			If !Empty(aProdPNP[nX][1])
				oDetailPNR:SetValue("PNR_CODPRO", aProdPNP[nX][1])
			Else
				oDetailPNR:SetValue("PNR_CODPRO", aProdPNP[nX][2])
			EndIf
			oDetailPNR:SetValue("PNR_CODIGO", oMasterPNP:GetValue("PNP_CODIGO"))
			oDetailPNR:SetValue("PNR_ITEM", oDetailPNQ:GetValue("PNQ_ITEM"))
			oDetailPNR:SetValue("PNR_CHAVE", "1")   //1=Produto / 2=Grupo
			oDetailPNR:SetValue("PNR_SEQ", '01')
			oDetailPNR:SetValue("PNR_TIPVAL", '1')
			oDetailPNR:SetValue("PNR_VALREF", aProdPNP[nX][3])
			nItemPNQ++
		Next

		If oModelPNP:VldData()
			If lTest
				RollBackSX8()
			Else
				oModelPNP:CommitData()
				oModelPNP:DeActivate()
			EndIf
		else
			Return s003RetErro(oModelPNP)
		EndIf
	else
		Return s003RetErro(oModelPNP)
	EndIf

Return aRet

/*/{Protheus.doc} S03GrvSUG
	Grava Acessórios do Produto
<AUTHOR> Osti
@since 11/09/2020
@version undefined
@type function
/*/
Static Function S03GrvSUG(aProdAcess)
	Local oModelSUG
	Local oMasterSUG
	Local oDetailSU1
	Local nx        := 0
	Local aRet      := { .T. }

	oModelSUG 	:=  FWLoadModel('TMKA030')

	For nx := 1 To Len(aProdAcess)
		If !Empty(aProdAcess[nx]:GetCharac("acessorio"))
			oModelSUG:SetOperation(MODEL_OPERATION_INSERT)

			If oModelSUG:Activate()
				oMasterSUG := oModelSUG:GetModel("SUGMASTER")
				oMasterSUG:SetValue("UG_PRODUTO", aProdAcess[nx]:GetCharac("partnumber"))
				oMasterSUG:SetValue("UG_XEXCLUI", '2')

				oDetailSU1 := oModelSUG:GetModel("SU1DETAIL")
				oDetailSU1:AddLine()

				nIndiceVal := val(aProdAcess[nx]:GetCharac("acessorio")) + 1
				oDetailSU1:SetValue("U1_ACESSOR", aProdAcess[nIndiceVal]:GetCharac("partnumber"))
				oDetailSU1:SetValue("U1_LOCAL", '01')
				oDetailSU1:SetValue("U1_XCODPAI", aProdAcess[nx]:GetCharac("partnumber"))

				If oModelSUG:VldData()
					If lTest
						RollBackSX8()
					Else
						oModelSUG:CommitData()
					EndIf
				else
					Return s003RetErro(oModelSUG)
				EndIf
				oModelSUG:DeActivate()
			else
				Return s003RetErro(oModelSUG)
			EndIf
		EndIf
	Next

Return aRet

/*/{Protheus.doc} GetFiltro
	Retorna Filtro a ser utilizado na nova regra de comercialização
<AUTHOR> Osti
@since 08/04/2020
@version undefined
@type function
/*/
Static Function GetFiltro(oFiltros, cCodTab)
	Local aRet      := {}
	Local aAuxCond  := {}
	Local aAuxExp   := {}
	Local aAuxExps  := {}
	Local nForFil   := 0

	AAdd(aRet, .F.)
	AAdd(aRet, oFiltros:nome)
	AAdd(aRet, oFiltros:id)
	If (AttIsMemberOf(oFiltros,'expressoes'))
		oExpress := oFiltros:expressoes
		oExpress:expADVPL   := StrTran(oExpress:expADVPL, "CODTABELA", cCodTab)
		oExpress:expSql     := StrTran(oExpress:expSql, "CODTABELA", cCodTab)
		AAdd(aAuxExp, oExpress:nome )
		AAdd(aAuxExp, oExpress:expadvpl )
		AAdd(aAuxExp, oExpress:expsql )
		If (AttIsMemberOf(oExpress,'regras'))
			aAuxCond := oExpress:regras
			For nForFil := 1 To Len(aAuxCond)
				aAuxCond[nForFil]:exp1 := StrTran(aAuxCond[nForFil]:exp1, "CODTABELA", cCodTab)
				aAuxCond[nForFil]:exp3 := StrTran(aAuxCond[nForFil]:exp3, "CODTABELA", cCodTab)
				aAuxCond[nForFil]:exp4 := StrTran(aAuxCond[nForFil]:exp4, "CODTABELA", cCodTab)
				aAuxCond[nForFil]:exp5 := StrTran(aAuxCond[nForFil]:exp5, "CODTABELA", cCodTab)
				AAdd(aAuxExps, {aAuxCond[nForFil]:exp1, aAuxCond[nForFil]:exp2, aAuxCond[nForFil]:exp3, aAuxCond[nForFil]:exp4, aAuxCond[nForFil]:exp5 })
			Next
			AAdd(aAuxExp,aAuxExps)
		endIf
		AAdd(aAuxExp, .F.)
		AAdd(aAuxExp, .F.)
		AAdd(aAuxExp, .F.)
		AAdd(aAuxExp, oExpress:tabela)
		AAdd(aAuxExp, oExpress:id)
		AAdd(aAuxExp, .F.)
	endIf
	AAdd(aRet, aAuxExp)
	AAdd(aRet, oFiltros:campoDominio)
	AAdd(aRet, oFiltros:contraDominio)

Return aRet

/*/{Protheus.doc} S003GetNiv
	Retorna próximo código nível disponível a ser incluido na AOM
<AUTHOR> Osti
@since 02/03/2020
@version undefined
@type function
/*/
Static Function S003GetNiv()
	Local cAliasNiv     := GetNextAlias()
	Local cRetNiv

	BeginSQL Alias cAliasNiv
    SELECT MAX(AOM.AOM_CODNIV) AOM_CODNIV
    FROM %Table:AOM% AOM
    WHERE AOM.AOM_CODAGR = '000112' AND AOM.%NotDel%
	EndSql

	cRetNiv := StrZero(Val((cAliasNiv)->AOM_CODNIV) + 1, Len(Alltrim((cAliasNiv)->AOM_CODNIV)))

	(cAliasNiv)->(dbCloseArea())

Return cRetNiv

/*/{Protheus.doc} S003SubNiv
	Retorna próximo código sub nível disponível a ser incluido na AOM
<AUTHOR> Osti
@since 02/03/2020
@version undefined
@type function
/*/
Static Function S003SubNiv(cWhereNiv)
	Local cAliSubNiv    := GetNextAlias()
	Local cRetSubNiv    := ""
	Local cWhere        := "% %"
	Local nValNivPai    := 0
	Local nTamIdInt     := 0
	Local aArea         := GetArea()
	Local aAreaAOM      := AOM->(GetArea())

	Default cWhereNiv   := ""

	If !Empty(cWhereNiv)
		cWhere := "% AND AOM.AOM_NIVPAI = '" + cWhereNiv + "' %"
	EndIf

	BeginSQL Alias cAliSubNiv
    SELECT Max(AOM.AOM_IDINT) AOM_IDINT
    FROM %Table:AOM% AOM
    WHERE AOM.AOM_CODAGR = '000112' AND AOM.%NotDel%
    %Exp:cWhere%
	EndSql

	nValNivPai := Val((cAliSubNiv)->AOM_IDINT)
	nTamIdInt := Len(Alltrim((cAliSubNiv)->AOM_IDINT))

	If Select(cAliSubNiv) > 0
		(cAliSubNiv)->(DbCloseArea())
	EndIf

	If nValNivPai == 0 .And. !Empty(cWhereNiv)

		AOM->(DbSetOrder(1))
		AOM->(DbSeek(xFilial('AOM') + '000112' + cWhereNiv))

		nValNivPai := Val(AOM->AOM_IDINT) * 100
		nTamIdInt := Len(AllTrim(AOM->AOM_IDINT)) + 2

	EndIf

	cRetSubNiv := StrZero( nValNivPai + 1, nTamIdInt)

	AOM->(RestArea(aAreaAOM))
	RestArea(aArea)

Return cRetSubNiv

/*/{Protheus.doc} S003RetErro
    (long_description)
    @type  Static Function
    <AUTHOR>
    @since 06/03/2020
    @version version
    @param param_name, param_type, param_descr
    @return return_var, return_type, return_description
    @example
    (examples)
    @see (links_or_references)
    /*/
Static Function S003RetErro(oModel)
	Local cMsgErro  := ""
	Local aRet      := {}

	aErro := oModel:GetErrorMessage()

	cMsgErro := AllTrim(AllToChar(aErro[6]))
	If ! Empty(aErro[4])
		cMsgErro += " - Campo: " + aErro[4]
	EndIf
	AAdd(aRet, .F.)
	AAdd(aRet, cMsgErro)

Return aRet

/*/{Protheus.doc} S03Ret2Err
	Retorna array com erro conforme mensagem de model MVC
<AUTHOR> Osti
@since 02/03/2020
@version undefined
@type function
/*/
Static Function S03Ret2Err(cMsg)
	Local aRet      := {}

	AAdd(aRet, .F.)
	AAdd(aRet, cMsg)

Return aRet

/*/{Protheus.doc} S03VldCodp
	Retorna Id disponível da SB1 através do tipo
<AUTHOR> Osti
@since 29/07/2020
@version undefined
@type function
/*/
Static Function S03VldCodp(cTpId)
	Local cRetNextId    := "000001"
	Local cAliasSB1     := GetNextAlias()

	BeginSql Alias cAliasSB1
    SELECT
        DISTINCT SUBSTR(MAX(B1_COD),8,6) B1_COD
    FROM
        %Table:SB1%
    WHERE
        B1_FILIAL = %xFilial:SB1%
        AND %NotDel%
        AND SUBSTR(B1_COD,1,6) = %Exp:'AUT.'+cTpId%

	EndSql

	If (cAliasSB1)->(!EOF()) .And. !Empty((cAliasSB1)->B1_COD)
		cRetNextId := StrZero(++Val(Alltrim((cAliasSB1)->B1_COD)), 6)
	EndIf

	(cAliasSB1)->(dbCloseArea())

Return cRetNextId

/*/{Protheus.doc} S03RepSb1
	Replica os dados do Produto (SB1/SB5/SBM) para empresas MI

<AUTHOR> Carneiro
@since 19/02/2021
@version undefined

@type function
/*/

Static Function S03RepSB1(aProdutos,cTipo)
	Local aArea     := GetArea()
	Local aAreaSB1  := SB1->(GetArea())
	Local aAreaSB5  := SB5->(GetArea())
	Local aAreaSBM  := SBM->(GetArea())
	Local cGetEmps 	:= SuperGetMv("TI_REPPEMP",,"01/02/04") //Empresas do grupo Replica Produto
	Local aGetEmps  := Strtokarr2( cGetEmps, "/")
	Local cCodCons  := GetMv("TI_X004POX",,"000073") // Registro da POR para replicação dos produtos MI
	Local nX        := 1
	Local cB1COD    := ""
	Local aJsonProd := {}

	If cEmpAnt == "00"

		For nX := 1 To Len(aProdutos)
			cB1COD := Alltrim(aProdutos[nx]:GetCharac("partnumber"))
			If Alltrim(SB1->B1_COD) <> cB1COD
				SB1->(DbSetOrder(1))
				If !SB1->(DbSeek(xFilial("SB1")+cB1COD))
					Loop
				EndIf
			EndIf

			// Adiciona estrutura Json para processo na POX de cada empresa MI
			AAdd( aJsonProd, { cB1COD, U_TCADX004(cTipo, .T.) } )
		Next

		nRecnoSM0 := SM0->(Recno())
		For nX := 1 To len(aGetEmps)

			If (aGetEmps[nX] <> cEmpAnt)
				SM0->( DbSeek(aGetEmps[nX]) )
				StartJob("U_CDX04POX", GetEnvServer(), .F., SM0->M0_CODIGO, Alltrim(SM0->M0_CODFIL), cCodCons, aJsonProd)
			EndIf

		Next
		SM0->(DbGoTo(nRecnoSM0))

	EndIf

	SBM->(RestArea(aAreaSBM))
	SB5->(RestArea(aAreaSB5))
	SB1->(RestArea(aAreaSB1))
	RestArea(aArea)
Return

/*/{Protheus.doc} regraComercializacao
	Serviço 2.0 para regra de comercialização CRM
<AUTHOR> Osti
@since 30/06/2021
@version undefined
@type function
/*/
	WSRESTFUL regraComercializacao DESCRIPTION "Integrações Regra de comercialização CRM"

		WsData codNivel   As String
		WsData codigo     As String
		WsData codUnidade As String
		WsDAta tabela     As String
		WsData fields     AS String	    OPTIONAL
		WsData order	  AS String	    OPTIONAL
		WsData page		  AS Integer	OPTIONAL
		WsData pageSize   AS Integer	OPTIONAL
		WsData filter     AS String	    OPTIONAL
		WsData tipoEntidade       AS String	    OPTIONAL

		WSMETHOD GET LstNiv;
			DESCRIPTION 'Retorna regras de comercialização existentes no CRM';
			WSSYNTAX '/api/crm/regracomercializacao/v1/niveis';
			PATH '/api/crm/regracomercializacao/v1/niveis';
			TTALK 'v1';
			PRODUCES APPLICATION_JSON

		WSMETHOD GET NivRegras;
			DESCRIPTION 'Cadastro da regra de comercialização';
			WSSYNTAX '/api/crm/regracomercializacao/v1/niveis/{codNivel}';
			PATH '/api/crm/regracomercializacao/v1/niveis/{codNivel}';
			TTALK 'v1';
			PRODUCES APPLICATION_JSON

		WsMethod POST NovoNivel;
			Description "Atualiza Regra de Comercialização";
			WSSYNTAX "/api/crm/regracomercializacao/v1/niveis/{codNivel}";
			PATH '/api/crm/regracomercializacao/v1/niveis/{codNivel}';
			TTALK 'v1';
			PRODUCES APPLICATION_JSON

		WsMethod PUT NivRegras;
			Description "Atualiza Regra de Comercialização";
			WSSYNTAX "/api/crm/regracomercializacao/v1/niveis/{codNivel}";
			PATH '/api/crm/regracomercializacao/v1/niveis/{codNivel}';
			TTALK 'v1';
			PRODUCES APPLICATION_JSON

		WSMETHOD GET LookupTabs;
			DESCRIPTION 'Dados de tabelas para campos lookup';
			WSSYNTAX '/api/crm/regracomercializacao/v1/lookup/{tabela}/?{fields, order, page, pageSize, filter}';
			PATH '/api/crm/regracomercializacao/v1/lookup/{tabela}';
			TTALK 'v1';
			PRODUCES APPLICATION_JSON

		WSMETHOD GET UnidNiveis;
			DESCRIPTION 'Retorna os níveis conforme as unidades de venda existentes no CRM';
			WSSYNTAX '/api/crm/regracomercializacao/v1/niveis/unidade/{codUnidade}';
			PATH '/api/crm/regracomercializacao/v1/niveis/unidade/{codUnidade}';
			TTALK 'v1';
			PRODUCES APPLICATION_JSON

		WSMETHOD DELETE UnidNiveis;
			DESCRIPTION 'Deleta as unidades dos níveis existentes no CRM';
			WSSYNTAX '/api/crm/regracomercializacao/v1/niveis/unidade/{codUnidade}';
			PATH '/api/crm/regracomercializacao/v1/niveis/unidade/{codUnidade}';
			TTALK 'v1';
			PRODUCES APPLICATION_JSON

		WSMETHOD POST niveisLock;
			DESCRIPTION 'Retorna usuário que bloqueou regra de comercialização (AOL)';
			WSSYNTAX '/api/crm/regracomercializacao/v1/niveis/lock';
			PATH '/api/crm/regracomercializacao/v1/niveis/lock';
			TTALK 'v1';
			PRODUCES APPLICATION_JSON

		WSMETHOD GET unidUsersNiveis;
			DESCRIPTION 'Retorna Unidadades de Negocio ou usuarios do CRM no Agrupador';
			WSSYNTAX '/api/crm/regracomercializacao/v1/niveis/unidades-usuarios/?{fields, order, page, pageSize, filter}';
			PATH '/api/crm/regracomercializacao/v1/niveis/unidades-usuarios/';
			TTALK 'v1';
			PRODUCES APPLICATION_JSON

		WSMETHOD POST unidUsersNiveis;
			DESCRIPTION 'Atualiza Unidadade de Negocio ou usuarios do CRM no Agrupador';
			WSSYNTAX '/api/crm/regracomercializacao/v1/niveis/unidades-usuarios/{codigo}/?{tipoEntidade}';
			PATH '/api/crm/regracomercializacao/v1/niveis/unidades-usuarios/{codigo}';
			TTALK 'v1';
			PRODUCES APPLICATION_JSON

		WSMETHOD GET niveisAOM;
			DESCRIPTION 'Retorna niveis ';
			WSSYNTAX '/api/crm/regracomercializacao/v1/niveis/niveis/?{fields, order, page, pageSize, filter}';
			PATH '/api/crm/regracomercializacao/v1/niveis/niveis/';
			TTALK 'v1';
			PRODUCES APPLICATION_JSON

	END WSRESTFUL

/*/{Protheus.doc} GET
    /api/crm/regraComercializacao/v1/nivel
Retorna todos os Clientes
<AUTHOR> Osi
@since 30/06/2021
/*/
WSMETHOD GET LstNiv WSSERVICE regraComercializacao
	Local cUsrLock  := ""
	Local oObj      := JsonUtil():New()

	aret := X188NivPai()
	oObj:PutVal("source",aret)

	If !(vldLockAOL(@cUsrLock))
		oObj:PutVal("userLock", cUsrLock)
	Else
		oObj:PutVal("userLock", "")
	EndIf

	::SetResponse(oObj:toJson())

Return .T.

/*/{Protheus.doc} GET
    /api/crm/regraComercializacao/v1/nivel
Retorna todos os Clientes
<AUTHOR> Osi
@since 30/06/2021
/*/
WSMETHOD GET NivRegras PATHPARAM codNivel WSSERVICE regraComercializacao
	Local cFldRegras
	Local cOrder
	Local cQuery
	Local cWhere
	Local oResFinal := JsonObject():New()
	Local lRet      := .T.

	Default ::codNivel := ""

	If !Empty(::codNivel)

		// Dados das regras por nivel (AZ0)
		S03RegAZ0(@cFldRegras, @cOrder, @cQuery, @cWhere, ::codNivel)
		oResFinal['regras']     := S03ExeAdapter(cFldRegras, cOrder, cQuery, cWhere )

		// Dados dos arquivos de impressão por nivel (AZ1)
		S03ArqAZ1(@cFldRegras, @cOrder, @cQuery, @cWhere, ::codNivel)
		oResFinal['arquivos']   := S03ExeAdapter(cFldRegras, cOrder, cQuery, cWhere)

		// Dados dos usuários por nivel (AZ2)
		S03UsrAZ2(@cFldRegras, @cOrder, @cQuery, @cWhere, ::codNivel)
		oResFinal['usuarios']   := S03ExeAdapter(cFldRegras, cOrder, cQuery, cWhere)

		// Dados das unidades de negócio por nivel (AZ2)
		S03UniAZA(@cFldRegras, @cOrder, @cQuery, @cWhere, ::codNivel)
		oResFinal['unidades']   := S03ExeAdapter(cFldRegras, cOrder, cQuery, cWhere)

		// Dados dos modelos de impressão HTML (PT3)
		S03ModPT3(@cFldRegras, @cOrder, @cQuery, @cWhere, ::codNivel)
		oResFinal['modelos']   := S03ExeAdapter(cFldRegras, cOrder, cQuery, cWhere)

		// Dados de complementos (PKG)
		S03ComPKG(@cFldRegras, @cOrder, @cQuery, @cWhere, ::codNivel)
		oResFinal['complementos']   := S03ExeAdapter(cFldRegras, cOrder, cQuery, cWhere, .F.)

		// Dados da Condição de Pagamento (PW1)
		S03CondPw1(@cFldRegras, @cOrder, @cQuery, @cWhere, ::codNivel)
		oResFinal['condicoes']   := S03ExeAdapter(cFldRegras, cOrder, cQuery, cWhere)

		// Dados dos Níveis Vinluados (PWP)
		S03CondPWP(@cFldRegras, @cOrder, @cQuery, @cWhere, ::codNivel)
		oResFinal['vinculos']   := S03ExeAdapter(cFldRegras, cOrder, cQuery, cWhere)

		// Retorna nivel + descrição
		dbSelectArea("AOM")
		AOM->(dbSetOrder(1))
		AOM->(dbSeek( xFilial("AOM") + "000112" + ::codNivel ))

		oResFinal['descricao']  := AOM->AOM_CODNIV + " - " + Alltrim(AOM->AOM_DESCRI)
		oResFinal['bloqueado']  := AOM->AOM_MSBLQL
		oResFinal['breadCrumbs']:= S03LevChild(::codNivel)

		self:SetResponse( oResFinal:toJson() )

	Else
		SetRestFault(400, "'codNivel' não enviado")
		lRet := .F.
	EndIf

Return lRet

/*/{Protheus.doc} PUT
    /api/crm/regraComercializacao/v1/nivel
Retorna todos os Clientes
<AUTHOR> San
@since 28/07/2021
/*/
WsMethod PUT NivRegras PATHPARAM codNivel WsService regraComercializacao

	Local cContent  := Alltrim(Self:GetContent())
	Local oBodyReq  := JsonObject():New()
	Local oJsonRet  := JsonObject():New()
	Local lRet      := .F.
	Local cErrBody  := nil
	Local nStatus   := 200

	cErrBody := oBodyReq:fromJson(cContent)

	If !(lRet := Empty(cErrBody) .AND. !Empty(cContent))

		oJsonRet['erros'] := {"Erro no body da requisição."}
		Self:SetStatus(400)
		Self:SetResponse(oJsonRet:toJson())
		Return lRet

	EndIf

	If !(lRet := !Empty(Self:codNivel))

		oJsonRet['erros'] := {"Nível do agrupador não informado."}
		Self:SetStatus(400)
		Self:SetResponse(oJsonRet:toJson())
		Return lRet

	EndIf

	//Inicia controle de transações
	Begin Transaction

		lRet := S03UPDREGRA( Self:codNivel,oBodyReq , @oJsonRet, @nStatus)

		Self:setStatus(nStatus)
		Self:SetResponse(oJsonRet:toJson())

		If !lRet
			DisarmTransaction()
		EndIf


	End Transaction

Return .T.

/*/{Protheus.doc} POST
    /api/crm/regraComercializacao/v1/niveis/{idNivel}
Retorna Novo Nível incluido
<AUTHOR> Osti
@since 17/11/2021
/*/
WsMethod POST NovoNivel PATHPARAM codNivel WsService regraComercializacao

	Local cContent  := Alltrim(Self:GetContent())
	Local cCodAOMPai
	Local cDescriAOM:= ''
	Local cCodAgr   := Alltrim(GetMV('TI_CODAGR'))
	Local cAliasAOM := GetNextAlias()

	Local oBodyReq  := JsonObject():New()
	Local oJsonRet  := JsonObject():New()
	Local cErrBody  := nil
	Local nStatus   := 201
	Local lRet      := .F.

	cErrBody := oBodyReq:fromJson(cContent)

	If !(lRet := Empty(cErrBody) .AND. !Empty(cContent))
		oJsonRet['erros'] := {"Erro no body da requisição."}
		Self:SetStatus(400)
		Self:SetResponse(oJsonRet:toJson())
		Return lRet
	EndIf

	cDescriAOM := Alltrim(oBodyReq['descricao'])
	If Empty(cDescriAOM)
		oJsonRet['erros'] := {"Descrição não informada."}
		Self:SetStatus(400)
		Self:SetResponse(oJsonRet:toJson())
		Return lRet
	EndIf

	If !(lRet := !Empty(Self:codNivel))
		oJsonRet['erros'] := {"Nível do agrupador não informado."}
		Self:SetStatus(400)
		Self:SetResponse(oJsonRet:toJson())
		Return lRet
	Else
		cCodAOMPai := Self:codNivel
	EndIf

	::SetContentType("application/json")

	BeginSql Alias cAliasAOM
    SELECT
        AOM_NIVPAI
    FROM %Table:AOM% AOM
    WHERE AOM.%NotDel%
        AND AOM.AOM_FILIAL = %xFilial:AOM%
        AND AOM.AOM_CODAGR = %exp:cCodAgr%
        AND AOM.AOM_CODNIV = %exp:cCodAOMPai%
	EndSql

	If !(cAliasAOM)->(EOF())
		// Grava Regra de comercialização completa
		Begin Transaction

			S03NovoNivel( cCodAOMPai, cDescriAOM, @oJsonRet)

			Self:setStatus(nStatus)
			Self:SetResponse(oJsonRet:toJson())

		End Transaction
		(cAliasAOM)->(dbCloseArea())
	Else
		oJsonRet['erros'] := {"Nível pai " + cCodAOMPai + " não encontrado na estrutura CRM"}
		Self:SetStatus(400)
		Self:SetResponse(oJsonRet:toJson())
		(cAliasAOM)->(dbCloseArea())
		Return .T.
	EndIf

Return .T.

/*/{Protheus.doc} GET
    /api/crm/regraComercializacao/v1/nivel/unidade
Retorna todos os Clientes
<AUTHOR> Osi
@since 30/06/2021
/*/
WSMETHOD GET UnidNiveis PATHPARAM codUnidade WSSERVICE regraComercializacao
	Local cFldRegras
	Local cOrder
	Local cQuery
	Local cWhere
	Local oResFinal := JsonObject():New()
	Local lRet      := .T.

	Default ::codUnidade := ""

	If !Empty(::codUnidade)

		// Dados das regras por nivel (AZ0)
		S03NivsAZA(@cFldRegras, @cOrder, @cQuery, @cWhere, ::codUnidade)
		oResFinal['items'] := S03ExeAdapter(cFldRegras, cOrder, cQuery, cWhere )

		self:SetResponse( oResFinal:toJson() )

	Else
		SetRestFault(400, "'codUnidade' não enviado")
		lRet := .F.
	EndIf

Return lRet

/*/{Protheus.doc} DELETE
    /api/crm/regraComercializacao/v1/nivel/unidade
Retorna todos os Clientes
<AUTHOR> Osi
@since 30/06/2021
/*/
WSMETHOD DELETE UnidNiveis PATHPARAM codUnidade WSSERVICE regraComercializacao
	Local cContent  := Alltrim(Self:GetContent())
	Local oBodyReq  := JsonObject():New()
	Local oResFinal := JsonObject():New()
	Local aNiveis   := {}
	Local lRet      := .T.

	Default ::codUnidade := ""

	cErrBody := oBodyReq:fromJson(cContent)

	If !(lRet := Empty(cErrBody) .AND. !Empty(cContent))
		oResFinal['erros'] := {"Erro no body da requisição."}
		Self:SetStatus(400)
		Self:SetResponse(oResFinal:toJson())
		Return .T.
	EndIf

	aNiveis := oBodyReq['items']
	If ValType(aNiveis) == "A" .And. Len(aNiveis) > 0

		If !Empty(::codUnidade)
			// Deleta unidade dos níveis aNiveis
			S03DelUnid(::codUnidade, aNiveis, @oResFinal)

			self:SetResponse( oResFinal:toJson() )
		Else
			oResFinal['erros'] := {"'codUnidade' não enviado"}
			Self:SetStatus(400)
			Self:SetResponse(oResFinal:toJson())
		EndIf
	Else
		oResFinal['erros'] := {"Nenhum nível informado."}
		Self:SetStatus(400)
		Self:SetResponse(oResFinal:toJson())
	EndIf

Return .T.

/*/{Protheus.doc} GET
    /api/crm/regraComercializacao/v1/niveis/lock
Retorna todos os Clientes
<AUTHOR> Osi
@since 30/06/2021
/*/
WSMETHOD POST niveisLock WSSERVICE regraComercializacao
	Local cUserLock   := ""
	Local cNewUsLock  := ""
	Local lProcess    := .T.
	Local cContent    := Alltrim(Self:GetContent())
	Local oBodyReq    := JsonObject():New()
	Local oRes        := JsonObject():New()
	Local nTimeAPI    := TimeCounter()
	Local nTimeAllow  := GetMv("TI_S03TIME",,30000) // Total máximo em Milesegundos que API aguarda para retorno

	cErrBody := oBodyReq:fromJson(cContent)

	If !(lRet := Empty(cErrBody) .AND. !Empty(cContent))
		oRes['erros'] := {"Erro no body da requisição."}
		Self:SetStatus(400)
		Self:SetResponse(oRes:toJson())
		Return .T.
	EndIf

	cUserLock := Alltrim(oBodyReq['userLock'])

	While lProcess

		If !(vldLockAOL(@cNewUsLock))

			If cNewUsLock == cUserLock
				lProcess := S003TimeWait(nTimeAPI, nTimeAllow)
			Else
				lProcess := .F.
			EndIf
			oRes['userLock'] := cNewUsLocK

		ElseIf !Empty(cUserLock)
			oRes['userLock'] := ""
			lProcess := .F.
		Else
			lProcess := S003TimeWait(nTimeAPI, nTimeAllow)
			oRes['userLock'] := cUserLock
		EndIf

	EndDo

	::SetResponse(oRes:toJson())

Return .T.

/*/{Protheus.doc} GET
    /api/crm/regraComercializacao/v1/niveis/unidades-usuarios
 Retorna dados de entidades unidades de negocio e usuarios do CRM
 <AUTHOR>
 @since 14/10/2024
/*/

WSMETHOD GET unidUsersNiveis WSRECEIVE fields, order, page, pageSize, filter WSSERVICE regraComercializacao
	Local oRes        := JsonObject():New()
	Local lRet		  := .T.
	Local cErroBlk	  := ''
	Local oException  := ErrorBlock({|e| cErroBlk := + e:Description + e:ErrorStack, lRet := .F. })

	Default self:fields		:= 'tipo,codigo,nome,bloqueado'
    Default self:order		:= 'row_number'
    Default self:page		:= 1
    Default self:pageSize	:= 20
    Default self:filter		:= ''

	Begin Sequence
		oRes := dadosADKeAO3(self:fields,self:page,self:pageSize,self:filter,self:order)
	End Sequence

	ErrorBlock(oException)

	If !lRet .and.  !empty(cErroBlk) .and. !oRes:hasProperty('status')
		oRes['code'] := 1
        oRes['status'] := 500
        oRes['message'] := 'Aconteceu um erro inesperado no serviço!'
        oRes['detailedMessage'] := cErroBlk
	Endif

	If oRes:hasProperty('message')
		oRes['message']:= EncodeUTF8(oRes['message'])
	Endif

	If oRes:hasProperty('detailedMessage')
		oRes['detailedMessage']	 := EncodeUTF8(oRes['detailedMessage'])
	Endif

	Self:SetStatus(iif(oRes:hasProperty('status'),oRes['status'],200))
	Self:SetResponse(oRes:toJson())

    FreeObj(oRes)   

Return .T.

/*/{Protheus.doc} dadosADKeAO3
Retorna dados de entidades unidades de negocio e usuarios do CRM
<AUTHOR>
@since 14/10/2024
/*/
Static Function dadosADKeAO3(cFields, nPage, nPageSize, cFilter, cOrder )
Local oDataBase := FWAdapterBaseV2():new( 'GET', .T. )
Local oResponse := JsonObject():New()
Local cQuery    := ""
Local cWhere    := ""

Default nPage       := 1
Default nPageSize   := 20
Default cFilter     := ''
Default cOrder      := 'row_number'
Default cFields     := ''

oDataBase:AddMapFields( 'codigo'	, 'CODIGO'		, .T., .F., { 'ADK_COD'  , TamSX3( 'ADK_COD' )[3], TamSX3( 'ADK_COD' )[1], TamSX3( 'ADK_COD' )[2] } )
oDataBase:AddMapFields( 'nome'		, 'NOME'  		, .T., .F., { 'ADK_NOME'  , TamSX3( 'ADK_NOME' )[3], TamSX3( 'ADK_NOME' )[1], TamSX3( 'ADK_NOME' )[2] } )
oDataBase:AddMapFields( 'tipo'		, 'TIPO'  		, .T., .F., { 'TIPO', 'C', 8, 0 })
oDataBase:AddMapFields( 'bloqueado'	, 'BLOQUEADO'	, .T., .F., { 'ADK_MSBLQL'  , TamSX3( 'ADK_MSBLQL' )[3], TamSX3( 'ADK_MSBLQL' )[1], TamSX3( 'ADK_MSBLQL' )[2] } )
oDataBase:AddMapFields( 'row_number'   	, 'ROW_NUMBER'	, .T., .F., { 'ROW_NUMBER', 'N', 12, 0 } )

cQuery := " SELECT 'unidade' TIPO ,adk_filial filial, adk_cod codigo, adk_nome nome, adk_msblql bloqueado, ROW_NUMBER() OVER (ORDER BY adk_cod) row_number "
cQuery += " FROM " + RetSqlName('ADK') + " ADK  "
cQuery += " WHERE ADK.D_E_L_E_T_ = ' ' "

cQuery += " union all "

cQuery += " SELECT 'usuario' TIPO, ao3_filial filial, ao3_codusr,USR_NOME, AO3_MSBLQL, ROW_NUMBER() OVER (ORDER BY ao3_codusr) row_number"
cQuery += " FROM " + RetSqlName('AO3') + " AO3  "
cQuery += " INNER JOIN SYS_USR USR ON AO3.AO3_CODUSR = USR.USR_ID AND USR.D_E_L_E_T_ = ' '  "
cQuery += " WHERE AO3.D_E_L_E_T_ = ' ' "

cQuery := " SELECT #QueryFields# FROM ( " +cQuery+ ") TAB1"

cQuery += " WHERE #QueryWhere#"
	
cWhere := " FILIAL ='" +xFilial("ADK")+"'"

oDataBase:setPage(nPage)
oDataBase:setPageSize(nPageSize)
oDataBase:SetOrderQuery(cOrder)
oDataBase:SetUrlFilter({{'FILTER', cFilter}})
oDataBase:SetFields( cFields )
oDataBase:SetQuery( cQuery )
oDataBase:SetWhere( cWhere )
oDataBase:SetOrder( cOrder )

If oDataBase:Execute()
  oDataBase:FillGetResponse()
EndIf

If oDataBase:lOk
  oResponse:fromJson(oDataBase:getJSONResponse())
  oResponse['status'] := 200
EndIf

Return oResponse

/*/{Protheus.doc} GET
    /api/crm/regraComercializacao/v1/niveis/niveis
 Retorna dados dos niveis de acordo com a tabela AOM
 <AUTHOR>
 @since 14/10/2024
/*/

WSMETHOD GET niveisAOM WSRECEIVE fields, order, page, pageSize, filter WSSERVICE regraComercializacao
	Local oRes        := JsonObject():New()
	Local lRet		  := .T.
	Local cErroBlk	  := ''
	Local oException  := ErrorBlock({|e| cErroBlk := + e:Description + e:ErrorStack, lRet := .F. })

	Default self:fields		:= 'aom_codagr, aom_codniv,aom_descri , aom_msblql'
    Default self:order		:= 'aom_codniv'
    Default self:page		:= 1
    Default self:pageSize	:= 20
    Default self:filter		:= ''

	Begin Sequence
		oRes := dadosNiveisAOM(self:fields,self:page,self:pageSize,self:filter,self:order)
	End Sequence

	ErrorBlock(oException)

	If !lRet .and.  !empty(cErroBlk) .and. !oRes:hasProperty('status')
		oRes['code'] := 1
        oRes['status'] := 500
        oRes['message'] := 'Aconteceu um erro inesperado no serviço!'
        oRes['detailedMessage'] := cErroBlk
	Endif

	If oRes:hasProperty('message')
		oRes['message']:= EncodeUTF8(oRes['message'])
	Endif

	If oRes:hasProperty('detailedMessage')
		oRes['detailedMessage']	 := EncodeUTF8(oRes['detailedMessage'])
	Endif

	Self:SetStatus(iif(oRes:hasProperty('status'),oRes['status'],200))
	Self:SetResponse(oRes:toJson())

    FreeObj(oRes)   

Return .T.

/*/{Protheus.doc} dadosADKeAO3
Retorna dados de entidades unidades de negocio e usuarios do CRM
<AUTHOR>
@since 14/10/2024
/*/
Static Function dadosNiveisAOM(cFields, nPage, nPageSize, cFilter, cOrder )
Local oDataBase := FWAdapterBaseV2():new( 'GET', .T. )
Local oResponse := JsonObject():New()
Local cQuery    := ""
Local cWhere    := ""
Local nX		:= 1

Default nPage       := 1
Default nPageSize   := 20
Default cFilter     := ''
Default cOrder      := ''
Default cFields     := ''

aFields :=iif(!empty(cFields), StrToKArr2(upper(cFields), ','),u_tc134Brw())
For nX := 1 to Len(aFields)
  cField := alltrim(aFields[nX])
  If !Empty(FWGetSx3Cache(Upper(cField), 'X3_CAMPO'))
    oDataBase:AddMapFields( cField   , cField  , .T., .F., { cField  , TamSX3( cField )[3], TamSX3( cField )[1], TamSX3( cField )[2] } )
  EndIf
Next

cQuery :=  " SELECT #QueryFields# "    
cQuery += " FROM " + RetSqlName('AOM') + " AOM  "

If 'AZA_' $ upper(cFields) .or. 'AZA_' $ upper(cFilter)  
	cQuery += " INNER JOIN " + RetSqlName( 'AZA' ) + " AZA "
	cQuery += "   ON AZA.AZA_FILIAL = '" + xFilial('AZA') + "' "
	cQuery += "   AND AZA_CODAGR = AOM_CODAGR AND AZA_CODNIV = AOM_CODNIV AND AZA.D_E_L_E_T_ = ' '"

	cQuery += " INNER JOIN " + RetSqlName( 'ADK' ) + " ADK "
	cQuery += "   ON ADK.ADK_FILIAL = '" + xFilial('ADK') + "' "
	cQuery += "   AND ADK_COD=AZA_COD AND ADK.D_E_L_E_T_ = ' ' "

Elseif 'AZ2_' $ upper(cFields) .or. 'AZ2_' $ upper(cFilter)  
	cQuery += " INNER JOIN " + RetSqlName( 'AZ2' ) + " AZ2 "
	cQuery += "   ON AZ2.AZ2_FILIAL = '" + xFilial('AZ2') + "' "
	cQuery += "   AND AZ2_CODAGR = AOM_CODAGR AND AZ2_CODNIV = AOM_CODNIV AND AZ2.D_E_L_E_T_ = ' ' "

	cQuery += " INNER JOIN SYS_USR USR ON USR.USR_ID =AZ2_CODENT  AND USR.D_E_L_E_T_ = ' '  "
Endif

cQuery += " WHERE #QueryWhere#"

cWhere := " AOM_FILIAL = '"+ FWxFilial('AOM') + "' "
cWhere += " AND AOM_CODAGR='000112'"
cWhere += " AND AOM.D_E_L_E_T_ = ' ' "

oDataBase:setPage(nPage)
oDataBase:setPageSize(nPageSize)
oDataBase:SetOrderQuery(cOrder)
oDataBase:SetUrlFilter({{'FILTER', cFilter}})
oDataBase:SetFields( cFields )
oDataBase:SetQuery( cQuery )
oDataBase:SetWhere( cWhere )
oDataBase:SetOrder( cOrder )

If oDataBase:Execute()
  oDataBase:FillGetResponse()
EndIf

If oDataBase:lOk
  oResponse:fromJson(oDataBase:getJSONResponse())
  oResponse['status'] := 200
EndIf

Return oResponse

/*/{Protheus.doc} POST
    /api/crm/regraComercializacao/v1/niveis/unidades-usuarios
 Processa atualização das entidades na tabela AZA e AZ2
 <AUTHOR>
 @since 14/10/2024
/*/

WSMETHOD POST unidUsersNiveis PATHPARAM codigo WSRECEIVE tipoEntidade WSSERVICE regraComercializacao
	Local cContent    := Alltrim(Self:GetContent())
	Local oBodyReq    := JsonObject():New()
	Local oRes        := JsonObject():New()
	Local lRet		  := .T.
	Local cErrBody    := ''
	Local cErroBlk	  := ''
	Local oException  := ErrorBlock({|e| cErroBlk := + e:Description + e:ErrorStack, lRet := .F. })

	Default self:codigo     	:= ''
    Default self:tipoEntidade	:= ''

	Begin Sequence

		cErrBody := oBodyReq:fromJson(cContent)

		If !(lRet := Empty(cErrBody) .AND. !Empty(cContent))
			oRes['code'] := 1
			oRes['status'] := 400
			oRes['message'] := 'Erro no body da requisição.'
			oRes['detailedMessage'] := 'Erro no body da requisição.'
			lRet := .F. 
			Break
		ElseIf EMPTY(self:codigo)
			oRes['code'] := 2
			oRes['status'] := 400
			oRes['message'] := 'Código da unidade/usuario não informado'
			oRes['detailedMessage'] := 'Código da unidade/usuario não informado. Informe um código de Unidade ou de Usuário.'
			lRet := .F. 
			Break
		ElseIf EMPTY(self:tipoEntidade)
			oRes['code'] := 3
			oRes['status'] := 400
			oRes['message'] := 'Tipo da entidade não foi informado'
			oRes['detailedMessage'] := 'Tipo da entidade não foi informado. Informe o tipo de entidade: ADK = Unidade ou AO3 = Usuário.'
			lRet := .F. 
			Break
		ElseIf !(upper(Alltrim(self:tipoEntidade)) $ 'ADK|AO3')
			oRes['code'] := 4
			oRes['status'] := 400
			oRes['message'] := 'Tipo da entidade informada é invalida.'
			oRes['detailedMessage'] := 'Tipo da entidade informada é invalida. Informe o tipo de entidade: ADK = Unidade ou AO3 = Usuário.'
			lRet := .F. 
			Break
		Endif

		If self:tipoEntidade == 'AO3'
			DbSelectArea("AO3")
			AO3->(DBSetOrder(1))
			If !(AO3->(DbSeek(xFilial("AO3")+self:codigo)))
				oRes['code'] := 5
				oRes['status'] := 400
				oRes['message'] := 'Código de usuario informado '+ self:codigo + ' não foi encontrado. Informe um código de usuario válido.'
				oRes['detailedMessage'] := 'Código de usuario informado '+ self:codigo + ' não foi encontrado. Informe um código de usuario válido.'
				lRet := .F. 
				Break
			Endif
		ElseIf self:tipoEntidade == 'ADK'
			DbSelectArea("ADK")
			ADK->(DBSetOrder(1))
			If !(ADK->(DbSeek(xFilial("ADK")+self:codigo)))
				oRes['code'] := 6
				oRes['status'] := 400
				oRes['message'] := 'Código de unidade informado '+ self:codigo + ' não foi encontrado. Informe um código de unidade válido.'
				oRes['detailedMessage'] := 'Código de unidade informado '+ self:codigo + ' não foi encontrado. Informe um código de unidade válido.'
				lRet := .F. 
				Break
			Endif
		Endif

		If !oBodyReq:HasProperty('movimentacoes') .or. Len(oBodyReq['movimentacoes']) <= 0
			oRes['code'] := 7
			oRes['status'] := 400
			oRes['message'] := 'Movimentações não foram enviadas.'
			oRes['detailedMessage'] := 'Movimentações não foram enviadas. Informe as movimentações para a correta atualização dos cadastros'
			lRet := .F. 
			Break
		Endif

		oRes := s03PrcEnt(self:tipoEntidade,self:codigo,oBodyReq['movimentacoes'])
		
	End Sequence

	ErrorBlock(oException)

	If !empty(cErroBlk)
		oRes['code'] := 1
        oRes['status'] := 500
        oRes['message'] := 'Aconteceu um erro inesperado no serviço!'
        oRes['detailedMessage'] := cErroBlk
	Endif

	If oRes:hasProperty('message')
		oRes['message']:= EncodeUTF8(oRes['message'])
	Endif

	If oRes:hasProperty('detailedMessage')
		oRes['detailedMessage']	 := EncodeUTF8(oRes['detailedMessage'])
	Endif

	Self:SetStatus(oRes['status'])
	Self:SetResponse(oRes:toJson())

    FreeObj(oRes)
    FreeObj(oBodyReq)

Return .T.

/*/{Protheus.doc} s03PrcEnt
    Processa atualização das entidades nas tabelas AZA e AZ2
<AUTHOR>
@since 14/10/2024
/*/
Static Function s03PrcEnt(cEntida,cCodigo,aDados)

Local oRes	:= JsonObject():New()
Local cErro := ""
Local n1	:= 1

Default cEntida := ''
Default cCodigo := '' 
Default aDados  := {}

for n1 :=1 to len(aDados)
	cErro := s03AttEnt(cEntida,cCodigo,aDados[n1])
	if !empty(cErro)
		exit
	Endif
next n1

If !empty(cErro)
  oRes['code'] := 10
  oRes['status'] := 400
  oRes['message'] := cErro
  oRes['detailedMessage'] := cErro
Else
  oRes['status'] := 201
  oRes['message'] := "Item Atualizado com Sucesso."
Endif

Return oRes

/*/{Protheus.doc} s03AttEnt
    Processa atualização da entidade nas tabelas AZA e AZ2
<AUTHOR>
@since 14/10/2024
/*/
Static Function s03AttEnt(cEntida,cCodigo,oItem)
Local nTamNiv   := TamSX3("AOM_CODNIV")[1]
Local cErro 	:= ""
Local lSeek     := .T.

If (AOM->(DbSeek(xFilial("AOM") + oItem['agrupador'] + Padr(oItem['nivel'], nTamNiv))))
  	If cEntida == 'ADK'
    	DbSelectArea("AZA")
		AZA->(DbSetOrder(1))
		lSeek := AZA->(DbSeek(xFilial("AZA")+AOM->AOM_CODAGR+AOM->AOM_CODNIV+cCodigo))
  	Else
    	DbSelectArea("AZ2")
		AZ2->(DbSetOrder(1))                                                                                                          
		lSeek := AZ2->(DbSeek(xFilial("AZ2")+AOM->AOM_CODAGR+AOM->AOM_CODNIV+"AO3"+cCodigo))
  	Endif

	If lower(oItem['operacao']) == 'inclusao'
		If cEntida == 'ADK'
			If !lSeek
				Reclock("AZA",.T.)
				AZA->AZA_FILIAL := xFilial("AZA")
				AZA->AZA_CODAGR := AOM->AOM_CODAGR
				AZA->AZA_CODNIV := AOM->AOM_CODNIV
				AZA->AZA_COD	:= cCodigo
				AZA->(MsUnLock())
			Else
				cErro := "A unidade " +cCodigo+" já está cadastrada no nível : "+AOM->AOM_CODNIV +" - " +Alltrim(AOM->AOM_DESCRI)
			Endif
		ElseIf cEntida == 'AO3'
			If !lSeek
				Reclock("AZ2",.T.)
				AZ2->AZ2_FILIAL 	:= xFilial("AZ2")
				AZ2->AZ2_CODAGR 	:= AOM->AOM_CODAGR
				AZ2->AZ2_CODNIV 	:= AOM->AOM_CODNIV
				AZ2->AZ2_ENTIDA 	:= "AO3"
				AZ2->AZ2_CODENT	:= cCodigo
				AZ2->(MsUnLock())
			Else
				cErro := "O usuário " +cCodigo+" já está cadastrado no nível : "+AOM->AOM_CODNIV +" - " +Alltrim(AOM->AOM_DESCRI)
			Endif
		Endif 
	ElseIf lower(oItem['operacao']) == 'exclusao'
		If cEntida == 'ADK'
			if lSeek
				RecLock("AZA", .F.)
				AZA->(DbDelete())
				AZA->(MsUnLock())
			Else
				cErro := "A unidade " +cCodigo+" não foi encontrada no nível : "+AOM->AOM_CODNIV +" - " +Alltrim(AOM->AOM_DESCRI)
			Endif
		ElseIf cEntida == 'AO3'
			if lSeek
				RecLock("AZ2", .F.)
				AZ2->(DbDelete())
				AZ2->(MsUnLock())
			Else
				cErro := "O usuario " +cCodigo+" não foi encontrado no nível : "+AOM->AOM_CODNIV +" - " +Alltrim(AOM->AOM_DESCRI)
			Endif
		Endif
	Else
		cErro := "Operação "+oItem['operacao']  +" é invalida." 
	Endif 
Else
  	cErro := "Nível "+oItem['nivel']+" não encontrado no agrupador '000112'."
EndIf

return cErro

/*/{Protheus.doc} S003TimeWait
    Tempo de execução permitido da API
<AUTHOR> Osi
@since 04/03/2022
/*/
Static Function S003TimeWait(nTimeAPI, nTimeAllow)
	Local lRet  := .T.

	Sleep(2000)
	If (timecounter() - nTimeAPI) > nTimeAllow
		lRet := .F.
	EndIf

Return lRet

/*/{Protheus.doc} S03NovoNivel
	Efetiva gravação de novo nível na estrutura AOM
<AUTHOR> Osti
@since 17/11/2021
@version undefined
@type function
/*/
Static Function S03NovoNivel(cNivelPai, cDescriAOM, oJsonRet)

	Local cXml          := ""

	dbSelectArea("AOM")
	AOM->(dbSetOrder(1))

// Persistência de dados
	cNovoNivel  := S003GetNiv()             // Get Nível principal
	cIdInt      := S003SubNiv(cNivelPai)    // SubNível

// Obtem o filtro padrão
	cXml := S003GetFil(cNivelPai,cNovoNivel,cDescriAOM)

// Grava Regra de comercialização (AOM)
	RecLock("AOM", .T.)
	AOM->AOM_CODAGR := "000112"
	AOM->AOM_CODNIV := cNovoNivel
	AOM->AOM_DESCRI := cDescriAOM
	AOM->AOM_MSBLQL := "2"
	AOM->AOM_NIVPAI := cNivelPai
	AOM->AOM_IDINT  := cIdInt
	AOM->AOM_FILXML := cXml
	AOM->(MsUnLock())

	oJsonRet["codNivel"]  := cNovoNivel
	oJsonRet["idPai"]     := cNivelPai
	oJsonRet["descricao"] := Alltrim(AOM->AOM_DESCRI)

Return

/*/{Protheus.doc} S03DelUnid
	Deleta unidade de um nível específico
<AUTHOR> Osti
@since 22/11/2021
@version undefined
@type function
/*/
Static Function S03DelUnid(codUnidade, aNiveis, oResFinal)
	Local nFor      := 0
	Local cAgrupador:= "000112"
	Local cNivel    := ""
	Local aNivEx    := {}

	dbSelectArea("AZA")
	AZA->(dbSetOrder(1))
	dbSelectArea("AOM")
	AOM->(dbSetOrder(1))

	codUnidade := Alltrim(codUnidade)

	Begin Transaction
		For nFor := 1 To Len(aNiveis)

			cNivel := Alltrim(aNiveis[nFor]['aom_codniv'])

			If AZA->( dbSeek( xFilial("AZA") + cAgrupador + cNivel + codUnidade ) ) .And.;
					AOM->( dbSeek( xFilial("AOM") + cAgrupador + cNivel ) )

				// Exclui nivel
				AAdd(aNivEx, AOM->AOM_CODNIV + ' - ' + Alltrim(AOM->AOM_DESCRI))
				RecLock("AZA", .F.)
				AZA->(DbDelete())
				AOM->(MsUnLock())

			EndIf

		Next

		oResFinal['items'] = aNivEx

	End Transaction

Return

/*/{Protheus.doc} S03RegAZ0
	Monta Query para retorno de Regras pelo cNivel enviado por parâmetro
<AUTHOR> Osti
@since 30/06/2021
@version undefined
@type function
/*/
Static Function S03RegAZ0(cFldRegras, cOrder, cQuery,  cWhere, cNivel)

	cFldRegras  := GetMv('TI_S3FLAZ0',,'AZ0_ENTIDA,AZ0_CONDIC,AZ0_DE,AZ0_CONENT,AZ0.R_E_C_N_O_')
	cOrder      := GetMv('TI_S3ODAZ0',,'AZ0_ENTIDA')

// Query com dados da Regra (AZ0)
	cQuery := " SELECT #QueryFields#"
	cQuery +=   " FROM " + RetSqlName( 'AZ0' ) + " AZ0 "
	cQuery += " WHERE #QueryWhere#"

	cWhere := " AZ0_FILIAL = '"+ FWxFilial('AZ0') +"' AND AZ0.D_E_L_E_T_ = ' ' "
	cWhere += " AND AZ0_CODAGR = '000112' AND AZ0_CODNIV = '" + cNivel + "' "

Return

/*/{Protheus.doc} S03ArqAZ1
	Monta Query para retorno dos arquivos de impressão pelo cNivel enviado por parâmetro
<AUTHOR> Osti
@since 30/06/2021
@version undefined
@type function
/*/
Static Function S03ArqAZ1(cFldRegras, cOrder, cQuery,  cWhere, cNivel)

	cFldRegras  := GetMv('TI_S3FLAZ1',,'AZ1_CODDOT,AG1_DESCRI,AZ1_XVLDES,AZ1.R_E_C_N_O_')
	cOrder      := GetMv('TI_S3ODAZ1',,'AZ1_CODDOT')

// Query com dados dos Arquivos (AZ0)
	cQuery := " SELECT #QueryFields#"
	cQuery += " FROM " + RetSqlName( 'AZ1' ) + " AZ1 INNER JOIN "
	cQuery += RetSqlName( 'AG1' ) + " AG1 ON "
	cQuery += "     AZ1.AZ1_FILIAL = AG1.AG1_FILIAL "
	cQuery += "     AND AZ1.AZ1_CODDOT = AG1.AG1_CODIGO "
	cQuery += "     AND AG1.D_E_L_E_T_ = ' ' "
	cQuery += " WHERE #QueryWhere#"

	cWhere := " AZ1_FILIAL = '"+ FWxFilial('AZ1') +"' AND AZ1.D_E_L_E_T_ = ' ' "
	cWhere += " AND AZ1_CODAGR = '000112' AND AZ1_CODNIV = '" + cNivel + "' "

Return

/*/{Protheus.doc} S03UsrAZ2
	Monta Query para retorno dos usuários pelo cNivel enviado por parâmetro
<AUTHOR> Osti
@since 30/06/2021
@version undefined
@type function
/*/
Static Function S03UsrAZ2(cFldRegras, cOrder, cQuery,  cWhere, cNivel)

	cFldRegras  := GetMv('TI_S3FLAZ2',,'AZ2_CODENT,USR_NOME,AZ2_ENTIDA,AZ2.R_E_C_N_O_')
	cOrder      := GetMv('TI_S3ODAZ2',,'AZ2_CODENT')

// Query com dados dos Usuários (AZ2)
	cQuery := " SELECT #QueryFields# "
	cQuery += " FROM " + RetSqlName( 'AZ2' ) + " AZ2 INNER JOIN "
	cQuery += " SYS_USR USR ON "
	cQuery += "     AZ2.AZ2_ENTIDA = 'AO3' "
	cQuery += "     AND AZ2.AZ2_CODENT = USR.USR_ID "
	cQuery += "     AND USR.D_E_L_E_T_ = ' ' "
	cQuery += " WHERE #QueryWhere#"

	cWhere := " AZ2_FILIAL = '"+ FWxFilial('AZ2') +"' AND AZ2.D_E_L_E_T_ = ' ' "
	cWhere += " AND AZ2_CODAGR = '000112' AND AZ2_CODNIV = '" + cNivel + "' "

Return

/*/{Protheus.doc} S03UniAZA
	Monta Query para retorno das Unidades de Negócio pelo cNivel enviado por parâmetro
<AUTHOR> Osti
@since 30/06/2021
@version undefined
@type function
/*/
Static Function S03UniAZA(cFldRegras, cOrder, cQuery,  cWhere, cNivel)

	cFldRegras  := GetMv('TI_S3FLAZA',,'AZA_COD,ADK_NOME,ADK_CNPJ,ADK_RAZAO,ADK_MSBLQL,AZA.R_E_C_N_O_')
	cOrder      := GetMv('TI_S3ODAZA',,'AZA_COD')

// Query com dados das Unidades de Negócio (AZA)
	cQuery := " SELECT #QueryFields# "
	cQuery += " FROM " + RetSqlName( 'AZA' ) + " AZA INNER JOIN "
	cQuery += RetSqlName( 'ADK' ) + " ADK ON "
	cQuery += "     AZA.AZA_FILIAL = ADK.ADK_FILIAL "
	cQuery += "     AND AZA.AZA_COD = ADK.ADK_COD "
	cQuery += "     AND ADK.D_E_L_E_T_ = ' ' "
	cQuery += " WHERE #QueryWhere#"

	cWhere := " AZA_FILIAL = '"+ FWxFilial('AZA') +"' AND AZA.D_E_L_E_T_ = ' ' "
	cWhere += " AND AZA_CODAGR = '000112' AND AZA_CODNIV = '" + cNivel + "' "

Return

/*/{Protheus.doc} S03ModPT3
	Monta Query para retorno dos modelos de impressão HTML pelo cNivel enviado por parâmetro
<AUTHOR> Osti
@since 30/06/2021
@version undefined
@type function
/*/
Static Function S03ModPT3(cFldRegras, cOrder, cQuery,  cWhere, cNivel)

	cFldRegras  := GetMv('TI_S3FLPT3',,'PT3_MODELO,PT1_DESCRI,PT3_VLDESC,PT3.R_E_C_N_O_')
	cOrder      := GetMv('TI_S3ODPT3',,'PT3_MODELO')

// Query com dados das Unidades de Negócio (AZA)
	cQuery := " SELECT #QueryFields# "
	cQuery += " FROM " + RetSqlName( 'PT3' ) + " PT3 INNER JOIN "
	cQuery += RetSqlName( 'PT1' ) + " PT1 ON "
	cQuery += "     PT3.PT3_FILIAL = PT1.PT1_FILIAL "
	cQuery += "     AND PT3.PT3_MODELO = PT1.PT1_CODIGO "
	cQuery += "     AND PT1.D_E_L_E_T_ = ' ' "
	cQuery += " WHERE #QueryWhere#"

	cWhere := " PT3_FILIAL = '"+ FWxFilial('PT3') +"' AND PT3.D_E_L_E_T_ = ' ' "
	cWhere += " AND PT3_CODAGR = '000112' AND PT3_CODNIV = '" + cNivel + "' "

Return

/*/{Protheus.doc} S03ComPKG
	Monta Query para retorno dos complementos pelo cNivel enviado por parâmetro
<AUTHOR> Osti
@since 30/06/2021
@version undefined
@type function
/*/
Static Function S03ComPKG(cFldRegras, cOrder, cQuery,  cWhere, cNivel)
	Local aFieldsPKG := FWSX3Util():GetListFieldsStruct( "PKG" , .F. )
	Local nPkg := 1

// Campos listados no SX3 da PKG
	cFldRegras := ""
	For nPkg := 1 to Len(aFieldsPKG)
		cFldRegras += aFieldsPKG[nPkg][1] + ","
	Next
	cFldRegras += ",PKG.R_E_C_N_O_"

	cOrder     := GetMv('TI_S3ODPKG',,'PKG_CODNIV')

// Query com dados das Unidades de Negócio (AZA)
	cQuery := " SELECT #QueryFields# "
	cQuery += " FROM " + RetSqlName( 'PKG' ) + " PKG "
	cQuery += " WHERE #QueryWhere#"

	cWhere := " PKG_FILIAL = '"+ FWxFilial('PKG') +"' AND PKG.D_E_L_E_T_ = ' ' "
	cWhere += " AND PKG_CODAGR = '000112' AND PKG_CODNIV = '" + cNivel + "' "

Return

/*/{Protheus.doc} S03CondPw1
Monta Query para retorno da Condição de Pagamento
<AUTHOR> Alves de Oliveira
@since 03/02/2023
@version undefined
@type function
/*/
Static Function S03CondPw1(cFldRegras, cOrder, cQuery,  cWhere, cNivel)

	cFldRegras  := GetMv('TI_S3FLPW1',,'PW1_TIPO,PW1_CONDPG,E4.E4_DESCRI,PW1_PADRAO,PW1.R_E_C_N_O_')
	cOrder      := GetMv('TI_S3ODPW1',,'PW1_CODNIV')

	cQuery := " SELECT #QueryFields# "
	cQuery += " FROM " + RetSqlName( 'PW1' ) + " PW1 "
	cQuery += " INNER JOIN " + RetSqlName("SE4") + " E4 ON E4.E4_FILIAL = PW1.PW1_FILIAL "
	cQuery += " AND E4.E4_CODIGO = PW1.PW1_CONDPG AND E4.D_E_L_E_T_ = ' ' "
	cQuery += " WHERE #QueryWhere#"

	cWhere := " PW1_FILIAL = '"+ FWxFilial('PW1') +"' AND PW1.D_E_L_E_T_ = ' ' "
	cWhere += " AND PW1_CODAGR = '000112' AND PW1_CODNIV = '" + cNivel + "' "

Return

/*/{Protheus.doc} S03CondPWP
Monta Query para retorno dos níveis de agrupadores vinculados
<AUTHOR> Giacomozzi
@since 20/05/2024
@version undefined
@type function
/*/
Static Function S03CondPWP(cFldRegras, cOrder, cQuery,  cWhere, cNivel)

	cFldRegras  := GetMv('TI_S3FLPWP',,'PWP_CODVI,AOM_DESCRI,PWP.R_E_C_N_O_')
	cOrder      := GetMv('TI_S3ODPWP',,'PWP_CODVI')

	cQuery := " SELECT #QueryFields# "
	cQuery += " FROM " + RetSqlName( 'PWP' ) + " PWP "
	cQuery += " INNER JOIN " + RetSqlName("AOM") + " AOM ON AOM.AOM_FILIAL = PWP.PWP_FILIAL "
	cQuery += " AND AOM.AOM_CODNIV = PWP.PWP_CODVI AND AOM.AOM_CODAGR = '000112' AND AOM.D_E_L_E_T_ = ' ' "
	cQuery += " WHERE #QueryWhere#"

	cWhere := " PWP_FILIAL = '"+ FWxFilial('PWP') +"' AND PWP.D_E_L_E_T_ = ' ' "
	cWhere += " AND PWP_CODAGR = '000112' AND PWP_CODNIV = '" + cNivel + "' "

Return

/*/{Protheus.doc} S03NivsAZA
	Monta Query para retorno dos níveis pelo cCodUnid enviado por parâmetro
<AUTHOR> Osti
@since 22/11/2021
@version undefined
@type function
/*/
Static Function S03NivsAZA(cFldRegras, cOrder, cQuery,  cWhere, cCodUnid)

	cFldRegras  := GetMv('TI_S3FLAOM',,'AOM_CODNIV,AOM_DESCRI,AOM_MSBLQL')
	cOrder      := GetMv('TI_S3ODAOM',,'AOM_CODNIV')

// Query com dados das Unidades de Negócio (AZA)
	cQuery := " SELECT DISTINCT #QueryFields# "
	cQuery += " FROM " + RetSqlName( 'AZA' ) + " AZA INNER JOIN "
	cQuery += RetSqlName( 'AOM' ) + " AOM ON "
	cQuery += "     AZA.AZA_FILIAL = AOM.AOM_FILIAL "
	cQuery += "     AND AZA.AZA_CODNIV = AOM.AOM_CODNIV"
	cQuery += "     AND AZA.AZA_CODAGR = AOM.AOM_CODAGR"
	cQuery += "     AND AOM.D_E_L_E_T_ = ' ' "
	cQuery += " WHERE #QueryWhere#"

	cWhere := " AZA_FILIAL = '"+ FWxFilial('AZA') +"' AND AZA.D_E_L_E_T_ = ' ' "
	cWhere += " AND AZA_CODAGR = '000112' AND AZA_COD = '" + Alltrim(cCodUnid) + "' "

Return

/*/{Protheus.doc} S03ExeAdapter
	Executa query pelo FWAdapter
<AUTHOR> Osti
@since 30/06/2021
@version undefined
@type function
/*/
Static Function S03ExeAdapter(cFieldsAdp, cOrderAdp, cQueryAdp, cWhereAdp, lItems)
	Local oDataBase
	Local aFields
	Local nx
	Local cTipoSx3
	Local nTamSx3
	Local cNameFld

	Default lItems := .T.

	oDataBase := FWAdapterBaseV2():new( 'GET', lItems )

	aFields := StrToKArr2(cFieldsAdp, ',')
	for nx := 1 to Len(aFields)

		// Tratamento dos fields
		If "R_E_C_N_O_" $ aFields[nx]
			cTipoSx3    := "N"
			nTamSx3     := 999999999
		ElseIf Len( TamSX3( aFields[nx] ) ) > 0
			cTipoSx3    := TamSX3( aFields[nx] )[3]
			nTamSx3     := TamSX3( aFields[nx] )[1]
		Else
			cTipoSx3    := "C"
			nTamSx3     := 100
		EndIf

		If '.' $ aFields[nx]
			cNameFld := StrToKArr2(aFields[nx], '.')[2]
			cFieldsAdp  += ',' + cNameFld
			oDataBase:AddMapFields( cNameFld, cNameFld, .T., .F., { cNameFld, cTipoSx3, nTamSx3, 0 }, aFields[nx])
		Else
			oDataBase:AddMapFields( aFields[nx], aFields[nx], .T., .F., { aFields[nx], cTipoSx3, nTamSx3, 0 } )
		EndIf

	next

	oDataBase:SetQuery( cQueryAdp )
	oDataBase:SetWhere( cWhereAdp )
	oDataBase:SetFields( cFieldsAdp )
	oDataBase:SetOrder( cOrderAdp )
	oDataBase:SetPageSize(999999999)

	If oDataBase:Execute()
		oDataBase:FillGetResponse()
		If oDataBase:lOk
			oResponse := JsonObject():New()
			oResponse:fromJson(oDataBase:getJSONResponse())

			If lItems
				Return oResponse["items"]
			Else
				Return oResponse
			EndIf

		EndIf
	EndIf

Return Nil

/*/{Protheus.doc} S03UPDREGRA
	Update Nivel
@version undefined
@type function
/*/
Static Function S03UPDREGRA(cNivel, oBodyReq , oJsonRet, nStatus)

	Local oModel    := Nil
	Local oModelAux	:= Nil
	Local oAux      := JsonObject():New()
	Local lRet      := .T.
	local nAux      := 0
	Local nKey      := 0
	Local nModel    := 0
	Local aErros    := {}
	Local aAux      := {}
	Local nTamNiv   := TamSX3("AOM_CODNIV")[1]
	Local aModels   := {}
	Local lOnlyAdd  := iif(!empty(oBodyReq['onlyAdd']),oBodyReq['onlyAdd'],.f.)
	Local aCheck    := {}

	If !(AOM->(DbSeek(xFilial("AOM") + "000112" + Padr(cNivel, nTamNiv))))
		oAux['erro'] := "Nível não encontrado no agrupador '000112'."
		aAdd(aErros, oAux)
	EndIf

	aModels := {{'regras', "A",'AZ0REGRAS'},;
		{'arquivos', "A",'AZ1ARQUIVO'},;
		{'usuarios', "A",'AZ2USERS'},;
		{'unidades', "A",'AZAUNIDADE'},;
		{'complementos',  "J",'PKGCOMPLEM'},;
		{'condicoes',  "A",'PW1CONDPAG'},;
		{'modelos',  "A",'PT3MODELOS'},;
		{'vinculos', "A",'PWPVINCULO'}}

	oModel 	:=  FWLoadModel( 'TCRMA195' )
	oModel:SetOperation(MODEL_OPERATION_UPDATE)

	If Len(aErros) == 0 .AND. ( lRet := oModel:Activate() )

		For nModel := 1 to Len(aModels)

			oAux := Nil

			//Valida propriedade e tipo da propriedade.
			If oBodyReq:hasProperty(aModels[nModel, 1]) .AND. Valtype(oBodyReq[aModels[nModel, 1]]) == aModels[nModel, 2]

				oModelAux := Nil
				oModelAux := oModel:GetModel(aModels[nModel, 3])

				//Percorre formularios que são listas
				If aModels[nModel, 2] == "A"

					If !lOnlyAdd
						limpaGrid(oModelAux, oBodyReq[aModels[nModel, 1]])
					Endif

					For nAux := 1 to Len(oBodyReq[aModels[nModel, 1]])

						If !Empty(oBodyReq[aModels[nModel, 1]][nAux]['r_e_c_n_o_'])
							Loop
						EndIf

						oAux := oBodyReq[aModels[nModel, 1]][nAux] //Json Atual
						oModelAux:AddLine()
						aAux := oBodyReq[aModels[nModel, 1]][nAux]:GetNames()//Campos do json
						aCheck := {}

						For nKey := 1 to Len(aAux)

							If lOnlyAdd
								aAdd(aCheck,{Upper(aAux[nKey]),oAux[aAux[nKey]]})
							Endif

							If 'r_e_c_n_o_' $ Lower(aAux[nKey])
								Loop
							ElseIf lOnlyAdd .and. oModelAux:SeekLine(aCheck)
								AAdd(aErros, "Entidade ja cadastrada")
							ElseIf  AScan(oModelAux:aHeader, {|x| x[2] == Upper(aAux[nKey])}) > 0 .And.;
									GetSx3Cache(Upper(aAux[nKey]), "X3_CONTEXT") <> 'V' .And.;
									!oModelAux:SetValue(Upper(aAux[nKey]), oAux[aAux[nKey]] )
								AAdd(aErros, getError(oModel) )
							EndIf
						Next nKey

					Next nAux

				Else //Modelos FormField
					oAux := oBodyReq[aModels[nModel, 1]]//Json Atual
					aAux := oBodyReq[aModels[nModel, 1]]:GetNames( )//Campos do json

					For nKey := 1 to Len(aAux)
						If 'r_e_c_n_o_' $ Lower(aAux[nKey])
							Loop
						ElseIf GetSx3Cache(Upper(aAux[nKey]), "X3_CONTEXT") <> 'V' .And. !oModelAux:SetValue(Upper(aAux[nKey]), oAux[aAux[nKey]] )
							AAdd(aErros, getError(oModel) )
						EndIf
					Next nKey
				EndIf

			EndIf
		Next nModel

		If Len(aErros) == 0
			If lRet := oModel:VldData()
				If oModel:CommitData()

					// Atualiza campo de bloqueio de regra de comercialização
					If oBodyReq['bloqueado'] != nil
						AOM->(dbSeek(xFilial("AOM") + "000112" + Padr(cNivel, nTamNiv)))
						If AOM->AOM_MSBLQL <> oBodyReq['bloqueado']
							If RecLock("AOM",.F.)
								AOM->AOM_MSBLQL := oBodyReq['bloqueado']
								AOM->(MsUnLock())
							EndIf
						EndIf
					EndIf

					oJsonRet['messages'] := {'Atualização realizada com sucesso!'}
				EndIf
			Else
				aAdd(aErros, getError(oModel))
			EndIf
		EndIf

		oModel:DeActivate()
	Else
		If !lRet //Apenas se tiver erro no modelo.
			aAdd(aErros, getError(oModel))
		EndIf
	EndIf

	//Caso tenha algum erro, limpa o json de retorno e retorna os erros.
	If !(lRet := Len(aErros) == 0)
		nStatus  := 400
		oJsonRet := NIL
		oJsonRet := JsonObject():New()
		oJsonRet['erros'] := aErros
	EndIf

	oModel:Destroy()

Return lRet

/*/{Protheus.doc} getError
	Get Error
@version undefined
@type function
/*/
Static Function getError(oModel)

	Local aError    := {}
	Local oJsonErro := JsonObject():New()

	aError := oModel:GetErrorMessage()

	If !Empty(aError)

		oJsonErro['Origem'] := IIF(Len(aError) >= 1 .AND. !Empty(aError[1]), cValToChar(aError[1]),'')
		oJsonErro['idOrigem'] := IIF(Len(aError) >= 2 .AND. !Empty(aError[2]), cValToChar(aError[2]),'')
		oJsonErro['formulario'] := IIF(Len(aError) >= 3 .AND. !Empty(aError[3]), cValToChar(aError[3]),'')
		oJsonErro['campoErro'] := IIF(Len(aError) >= 4 .AND. !Empty(aError[4]), cValToChar(aError[4]),'')
		oJsonErro['idErro'] := IIF(Len(aError) >= 5 .AND. !Empty(aError[5]), cValToChar(aError[5]),'')
		oJsonErro['erro'] := IIF(Len(aError) >= 6 .AND. !Empty(aError[6]), cValToChar(aError[6]),'')
		oJsonErro['solucao'] := IIF(Len(aError) >= 7 .AND. !Empty(aError[7]), cValToChar(aError[7]),'')
		oJsonErro['valorAtribuido'] := IIF(Len(aError) >= 8 .AND. !Empty(aError[8]), cValToChar(aError[8]),'')
		oJsonErro['valorAnterior'] := IIF(Len(aError) >= 9 .AND. !Empty(aError[8]), cValToChar(aError[9]),'')
		oJsonErro['erro'] += ' - ' + oJsonErro['valorAtribuido']
	EndIf

	aError := Nil

Return oJsonErro

/*/{Protheus.doc} limpaGrid
	Limpa Grid
@version undefined
@type function
/*/
Static function limpaGrid(oModelAux, aNewData)

	Local nX    := 0
	Local nRecno := 0
	Local nPos  := 0

	For nX := 1 to oModelAux:Length()
		oModelAux:GoLine(nX)
		nRecno := oModelAux:GetDataId(nX)

		nPos := aScan(aNewData, {|x| x['r_e_c_n_o_'] == nRecno } )

		If nPos < 1
			oModelAux:DeleteLine()
		EndIf
	Next nX

Return

/*/{Protheus.doc} GET
/api/crm/regraComercializacao/v1/lookup/{tabela}
Retorna dados de uma tabela para Lookup no Portal

@param	fields		, caracter, Campos que serão retornados no GET.
@param	order		, caracter, Ordenação da tabela principal
@param	page		, numérico, Número da página inicial da consulta
@param	pageSize	, numérico, Número de registro por páginas
@param	filter	, Filtro para busca simples ou no padrão oData

@return lRet	, Lógico, Informa se o processo foi executado com sucesso.

<AUTHOR>
@since		29/07/2021
@version	1.0
/*/
WSMETHOD GET LookupTabs PATHPARAM tabela WSRECEIVE fields, order, page, pageSize, filter WSSERVICE regraComercializacao

	Local lRet      := .T.
	Local oResponse := JsonObject():New()
	Local oDataBase := nil

	Local cAllowTabs    := GetMV("TI_CRMLKUP",,"ADK,AG1,PT1,SYS_USR,DA0,AO3,SM0,SE4")

	Local cWhere    := ''
	Local cQuery    := ''
	Local cSqlName  := ''

	Local aStruct   := {}
	Local aFields   := {}
	Local nX        := 0

	Local cErroBlk      := ''
	Local oException	:= ErrorBlock({|e| cErroBlk := + e:Description + e:ErrorStack, lRet := .F. })

	Private oTempoSM0

	Default self:fields   := ''
	Default self:order    := ''
	Default self:page     := 1
	Default self:pageSize := 20

	Begin Sequence

		self:tabela := AllTrim(Upper(self:tabela))
		self:fields := AllTrim(Upper(self:fields))

		If self:tabela $ cAllowTabs

			//Trata campos
			If !Empty(self:fields)
				aFields := StrToKArr2(self:fields,',')
			ElseIf self:tabela == "SYS_USR"
				self:fields := "USR_ID, USR_NOME"
				aFields := {"USR_ID", "USR_NOME"}
			Else
				aStruct := (self:tabela)->(DbStruct())
				aEval(aStruct, { | _aS | aAdd(aFields, Upper(_aS[1])), self:fields += Upper(_aS[1]) + ','})
				self:fields := Left(self:fields,Len(self:fields)-1)
			EndIf

			If Empty(self:order)
				self:order := STRTRAN(aFields[1],"M0_","")
			EndIf

			if self:tabela == 'SM0'
				self:fields := STRTRAN(self:fields,"M0_","")
			endif

			oDataBase := FWAdapterBaseV2():new( 'GET', .T. )
			oDataBase:setPage(self:page)
			oDataBase:setPageSize(self:pageSize)
			oDataBase:SetOrderQuery(self:order)
			oDataBase:SetUrlFilter(self:aQueryString )
			oDataBase:SetFields( self:fields )

			If self:tabela == "SYS_USR"
				oDataBase:AddMapFields( "USR_ID"   , "USR_ID"  , .T., .F., { "USR_ID"  , "C", 6, 0 } )
				oDataBase:AddMapFields( "USR_NOME"   , "USR_NOME"  , .T., .F., { "USR_NOME"  , "C", 60, 0 } )
			ElseIf self:tabela == "AO3"
				For nX := 1 to Len(aFields)
					If aFields[nX] == "USR_NOME"
						oDataBase:AddMapFields( "USR_NOME"   , "USR_NOME"  , .T., .F., { "USR_NOME"  , "C", 60, 0 } )
					ElseIf aFields[nX] == "USR_EMAIL"
						oDataBase:AddMapFields( "USR_EMAIL"   , "USR_EMAIL"  , .T., .F., { "USR_EMAIL"  , "C", 150, 0 } )
					Else
						oDataBase:AddMapFields( aFields[nX]   , aFields[nX]  , .T., .F., { aFields[nX]  , TamSX3( aFields[nX] )[3], TamSX3( aFields[nX] )[1], TamSX3( aFields[nX] )[2] } )
					EndIf
				Next
			ElseIf self:tabela == "SM0"
				If 'CODIGO' $ upper(self:fields)
					oDataBase:AddMapFields( 'CODIGO', 'CODIGO', .T., .F., { 'CODIGO', 'C', 2, 0})
				EndIf
				If 'NOME' $ upper(self:fields)
					oDataBase:AddMapFields( 'NOME'  , 'NOME'  , .T., .F., { 'NOME', 'C', 40, 0})
				EndIf
				If 'CODFIL' $ upper(self:fields)
					oDataBase:AddMapFields( 'CODFIL', 'CODFIL', .T., .F., { 'CODFIL', 'C', 11, 0})
				EndIf
				If 'FILIAL' $ upper(self:fields)
					oDataBase:AddMapFields( 'FILIAL', 'FILIAL', .T., .F., { 'FILIAL', 'C', 41, 0})
				EndIf
			Else
				For nX := 1 to Len(aFields)
					oDataBase:AddMapFields( aFields[nX]   , aFields[nX]  , .T., .F., { aFields[nX]  , TamSX3( aFields[nX] )[3], TamSX3( aFields[nX] )[1], TamSX3( aFields[nX] )[2] } )
				Next
			EndIf

			oDataBase:AddMapFields( "S_T_A_M_P_" , ;
				"TO_CHAR(S_T_A_M_P_, 'yyyy-mm-dd"+'"T"'+"HH24:MI:SS"+'"Z"'+"')" , ;
				.T., .F., { "S_T_A_M_P_"  , 'C', 127, 0 } )


			// Trata Queryes
			If self:tabela == "SYS_USR"
				cQuery := " SELECT #QueryFields# "
				cQuery += " FROM SYS_USR USR "
				cQuery += " WHERE #QueryWhere#"
				cWhere := " USR.D_E_L_E_T_ = ' ' "
			ElseIf self:tabela == "AO3"
				cQuery := " SELECT #QueryFields# "
				cQuery += " FROM " + RetSqlName( self:tabela ) + " AO3 "
				cQuery += " INNER JOIN SYS_USR USR ON "
				cQuery += "     AO3.AO3_CODUSR = USR.USR_ID "
				cQuery += "     AND USR.D_E_L_E_T_ = ' ' "
				cQuery += " WHERE #QueryWhere#"
				cWhere := " AO3.AO3_FILIAL = '"+ FWxFilial(self:tabela) +"' AND AO3.D_E_L_E_T_ = ' '"
			ElseIf self:tabela == "SM0"
				cSqlName := SM0Table()
				cQuery := " SELECT #QueryFields# "
				cQuery += " FROM " + cSqlName + " SM0 "
				cQuery += " WHERE #QueryWhere#"
				cWhere := " SM0.D_E_L_E_T_ = ' ' "

				If Empty(self:filter)
					cWhere += " GROUP BY " + oDataBase:oJsonObj:cFields
				EndIf
			Else
				cQuery := " SELECT #QueryFields#"
				cQuery +=   " FROM " + RetSqlName( self:tabela ) + " " + self:tabela + " "
				cQuery += " WHERE #QueryWhere#"
				cWhere := " " + If(Left(self:tabela,1)=="S", Substr(self:tabela,2,2), self:tabela) + "_FILIAL = '"+ FWxFilial(self:tabela) +"' AND " + self:tabela + ".D_E_L_E_T_ = ' '"
			EndIf

			oDataBase:SetQuery( cQuery )
			oDataBase:SetWhere( cWhere )
			oDataBase:SetOrder( self:order )

			If oDataBase:Execute()
				oDataBase:FillGetResponse()
			EndIf

			If oDataBase:lOk

				oResponse := JsonObject():New()
				oResponse:fromJson(oDataBase:getJSONResponse())

				If self:tabela == 'AO3'
					For nX := 1 To Len(oResponse['items'])
						If 'AO3_CODUND' $ self:fields
							oResponse['items'][nX]['nomeund'] := ''
							If !Empty(oResponse['items'][nX]['ao3_codund'])
								oResponse['items'][nX]['nomeund'] := AllTrim(Posicione('ADK',1,xFilial('ADK')+oResponse['items'][nX]['ao3_codund'],'ADK_NOME'))
							EndIf
						EndIf
						If 'AO3_CODEQP' $ self:fields
							oResponse['items'][nX]['nomeeqp'] := ''
							If !Empty(oResponse['items'][nX]['ao3_codeqp'])
								oResponse['items'][nX]['nomeeqp'] := AllTrim(Posicione('ACA',1,xFilial('ACA')+oResponse['items'][nX]['ao3_codeqp'],'ACA_DESCRI'))
							EndIf
						EndIf
					Next nX
				ElseIf self:tabela == "SM0"
					oTempoSM0:Delete()
				EndIf

			EndIf

		Else
			oResponse['code'] := 003
			oResponse['status'] := 400
			oResponse['message'] := 'Este serviço não tem permissão para consultar a tabela [' + self:tabela + '].'
			oResponse['detailedMessage'] := ''
			lRet := .F.
		EndIf

	End Sequence

	ErrorBlock(oException)

// Verifica errorBlock
	If lRet
		// Verifica execução da query
		If oDataBase:lOk
			self:SetResponse(oResponse:toJson())
		Else
			oResponse['code'] := 002 // oDataBase:GetCode()
			oResponse['status'] := 400
			oResponse['message'] := 'Não foi possível realizar o filtro dos registros!'
			oResponse['detailedMessage'] := oDataBase:GetMessage()
			lRet := .F.
		EndIf

//Erro de programa
	ElseIf !Empty(cErroBlk)
		oResponse['code'] := 001
		oResponse['status'] := 500
		oResponse['message'] := 'Aconteceu um erro inesperado no serviço!'
		oResponse['detailedMessage'] := cErroBlk
	EndIf

	If !lRet
		SetRestFault(   oResponse['code'],;
			oResponse['message'],;
			.T.,;
			oResponse['status'],;
			oResponse['detailedMessage'];
			)
	EndIf

	If Type('oDataBase') == 'O'
		oDataBase:DeActivate()
		oDataBase := nil
	EndIf
	If Type('oResponse') == 'O'
		oResponse:DeActivate()
		oResponse := nil
	EndIf
	aFields := nil

Return lRet

/*/{Protheus.doc} SM0Table
Cria tabela temporaria da SM0 para consumo no Adapter Lookup

@return cAlias	, string, Alias para uso em query

<AUTHOR>
@since		15/10/2021
@version	1.1
/*/
Static Function SM0Table()

	Local aFields   := {}

	Local cAlias    := GetNextAlias()
	Local aSM0Data := FWLoadSM0()
	Local i


	oTempoSM0 := FWTemporaryTable():New(cAlias)
	aadd(aFields,{"CODIGO","C",2,0})
	aadd(aFields,{"NOME","C",40,0})
	aadd(aFields,{"CODFIL","C",11,0})
	aadd(aFields,{"FILIAL","C",41,0})
	oTempoSM0:SetFields( aFields )
	oTempoSM0:AddIndex("01", {"CODIGO", "CODFIL"} )
	oTempoSM0:Create()

	for i := 1 to len(aSM0Data)

		Reclock(cAlias, .T.)
		(cAlias)->CODIGO := aSM0Data[i][1]
		(cAlias)->NOME   := ALLTRIM(aSM0Data[i][6])
		(cAlias)->CODFIL := aSM0Data[i][2]
		(cAlias)->FILIAL := ALLTRIM(aSM0Data[i][7])
		(cAlias)->(MsUnLock())

	next i
return oTempoSM0:GetRealName()

/*/{Protheus.doc} vldLockAOL
	Verifica se regra de comercialização esta bloqueada para alteração
<AUTHOR> Osti
@since 03/03/2022
@version undefined
@type function
/*/
Static Function vldLockAOL(cMsg)
	Local lRet    := .F.
	Local cUsrCRM := ""

	dbSelectArea("AOL")
	AOL->(dbSetOrder())
	AOL->(dbSeek(xFilial("AOL") +  Alltrim(GetMv("TI_CODAGR")) ) )

	If AOL->(DbRLock())
		AOL->(DBRUnlock())
		lRet := .T.
	Else
		cUsrCRM := TcInternal(53)
		cUsrCRM := Alltrim(Substr(cUsrCRM, 1, AT("|", cUsrCRM ) - 1))

		PswOrder(2)
		If (PswSeek(cUsrCRM, .T.))
			cMsg := cUsrCRM
		EndIf

	EndIf

Return lRet

/*/{Protheus.doc} S003GetFil
    Cria filtro padrão para regra de comercialização
    @type Function
    <AUTHOR> Giacomozzi
    @since 14/06/2023
    @version undefined
    @param cNivelPai , Character, Nível pai da regra de comercialização
    @param cNovoNivel, Character, Nível de regra de comercialização que será criada
    @Param cDescriAOM, Character, Descrição da regra da comercialização
    @return Character, Filtro que será gravado no campo AOM_FILXML
    /*/
Static Function S003GetFil(cNivelPai,cNovoNivel,cDescriAOM)
	Local cFiltroXML := ''
	Local cXml       := ''
	Local aAux       := {}
	Local aExpressao := {}
	Local oFiltro

	// Transforma objeto em array necessário para executar função do padrão CRMA580MXml
	cFiltroXML := '{"filtros":{'+CRLF
	cFiltroXML += '		"nome":"' + cNovoNivel + '/' + cDescriAOM + '",'+CRLF
	cFiltroXML += '		"id":"1",'+CRLF
	cFiltroXML += '		"expressoes":'+CRLF
	cFiltroXML += '		{'+CRLF
	cFiltroXML += ' 		"nome":"Codigo Diferente de",'+CRLF
	cFiltroXML += '			"expADVPL":"B1_COD!= ' + chr(39) + '               ' + chr(39) + '",'+CRLF
	cFiltroXML += '			"expSql":"B1_COD<>'+ chr(39) + '               ' + chr(39) + '",'+CRLF
	cFiltroXML += '			"regras":['+CRLF
	cFiltroXML += ' 			{'+CRLF
	cFiltroXML += ' 				"exp1":"B1_COD",'+CRLF
	cFiltroXML += '					"exp2":"FIELD",'+CRLF
	cFiltroXML += '					"exp3":"Codigo Diferente de",'+CRLF
	cFiltroXML += '					"exp4":"B1_COD!= '+ chr(39) + '               ' + chr(39) +'",'+CRLF
	cFiltroXML += '					"exp5":"B1_COD<>'+ chr(39) + '               ' + chr(39) +'"'+CRLF
	cFiltroXML += '				},'+CRLF
	cFiltroXML += '				{'+CRLF
	cFiltroXML += '					"exp1":"!=",'+CRLF
	cFiltroXML += '					"exp2":"OPERATOR",'+CRLF
	cFiltroXML += '					"exp3":"",'+CRLF
	cFiltroXML += '					"exp4":"",'+CRLF
	cFiltroXML += '					"exp5":""'+CRLF
	cFiltroXML += '				},'+CRLF
	cFiltroXML += '				{'+CRLF
	cFiltroXML += '					"exp1":"",'+CRLF
	cFiltroXML += '					"exp2":"EXPRESSION",'+CRLF
	cFiltroXML += '					"exp3":"",'+CRLF
	cFiltroXML += '					"exp4":"",'+CRLF
	cFiltroXML += '					"exp5":""'+CRLF
	cFiltroXML += '				}'+CRLF
	cFiltroXML += '			],'+CRLF
	cFiltroXML += '         "exp2":false,'+CRLF
	cFiltroXML += ' 	    "exp3":false,'+CRLF
	cFiltroXML += '		    "exp4":false,'+CRLF
	cFiltroXML += '		    "tabela":"SB1",'+CRLF
	cFiltroXML += '		    "id":"'+"000112"+cNovoNivel+"01"+'",'+CRLF
	cFiltroXML += '		    "exp5":false'+CRLF
	cFiltroXML += '		},'+CRLF
	cFiltroXML += '		"campoDominio":"B1_COD",'+CRLF
	cFiltroXML += '		"contraDominio":"DA1_CODPRO"'+CRLF
	cFiltroXML += '}}'+CRLF

	If fWJsonDeserialize(alltrim(cFiltroXML),@oFiltro)
		aAux := GetFiltro(oFiltro:filtros, cNivelPai)
	EndIf
	AAdd(aExpressao, aAux)

	if Len(aExpressao) > 0
		cXml := CRMA580MXml(aExpressao)
	EndIf

Return(cXml)
