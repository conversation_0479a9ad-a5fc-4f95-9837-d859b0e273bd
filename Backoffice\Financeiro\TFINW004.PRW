#Include 'Protheus.ch'
#Include "TopConn.ch"

/***
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÝÝÝÝÝÝÝÝÝÝÑÝÝÝÝÝÝÝÝÝÝËÝÝÝÝÝÝÝÑÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝËÝÝÝÝÝÝÑÝÝÝÝÝÝÝÝÝÝÝÝÝ»±±
±±ºPrograma  TFINW004  ºAutor  ³Maur�cio Madureira       º Data ³  19/11/21   º±±
±±ÌÝÝÝÝÝÝÝÝÝÝØÝÝÝÝÝÝÝÝÝÝÊÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÊÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝ¹±±
±±ºDesc.     ³Retorna valor de cliente inadimplente                       º±±
±±ºItem e CCusto.														  º±±
±±ÌÝÝÝÝÝÝÝÝÝÝØÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝ¹±±
±±ºUso       ³ AP                                                         º±±
±±ÈÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
***/

User Function TFINW004()

	Local aRequest	:= PARAMIXB //Parametros recebidos do Interceptor  [1]= Codigo da Requisicao P37_COD [2]= Body Recebido da requisicao  P37_BODY
	Local oJson		:= JsonObject():new()
	Local oBody     := oJson:FromJson( aRequest[2] )
	Local lRet 		:= .F.

	Private cAlsCli	:= GetNextAlias()
		
	If ValType( oBody ) == "U"
		
		c_par01:=oJson:GetJsonObject( "cliente" )
		c_par02:=oJson:GetJsonObject( "loja" )
		c_par03:=oJson:GetJsonObject( "cnpj_cpf" )

		If !Empty(c_par01) .and. !Empty(c_par02)

			lRet := .T.

		ElseIf !Empty(c_par03)

			DbSelectArea('SA1')
			SA1->(DbSetOrder(3))
			If SA1->(DbSeek(xFilial()+c_par03))		

				lRet := .T.
				
				c_par01 := SA1->A1_COD
				c_par02 := SA1->A1_LOJA

			Else

				cJson:= '{ "erros": "cnpj_cpf informado não encontrado."}'

			EndIf
	
		Else
			
			cJson:= '{ "erros": "Informe cliente e loja ou cnpj_cpf."}'

		EndIf

		If lRet

			cJson:=fProcessa(c_par01, c_par02)

		EndIf

	Else

		cJson:= '{ "erros": "Json com formato invalido"}  '

	EndIF

	RecLock("P37", .F.)
		P37->P37_BODYRP:= cJson
		P37->P37_STATUS:= "2"
	MsUnlock()

Return


/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÉÝÝÝÝÝÝÝÝÝÝÑÝÝÝÝÝÝÝÝÝÝËÝÝÝÝÝÝÝÑÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝËÝÝÝÝÝÝÑÝÝÝÝÝÝÝÝÝÝÝÝÝ»±±
±±ºPrograma  ³fProcessa ºAutor  ³Microsiga           º Data ³  03/05/13   º±±
±±ÌÝÝÝÝÝÝÝÝÝÝØÝÝÝÝÝÝÝÝÝÝÊÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÊÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝ¹±±
±±ºDesc.     ³Filtra registros e apresenta na markbrowse.                 º±±
±±º          ³                                                            º±±
±±ÌÝÝÝÝÝÝÝÝÝÝØÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝ¹±±
±±ºUso       ³ AP                                                         º±±
±±ÈÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝÝ¼±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
Static Function fProcessa(cCliente, cLoja)

	Local cQuery     := ""
	
	cQuery := " SELECT E1_CLIENTE, SUM(E1_SALDO) SALDO FROM "+RetSqlName("SE1")+" SE1 "
    cQuery += " WHERE "
    cQuery += " SE1.E1_CLIENTE = '" + cCliente + "' AND " 
    cQuery += " SE1.E1_LOJA    = '" + cLoja    + "' AND "
	cQuery += " E1_SALDO > 0 AND E1_VENCREA < '" + DTOS(dDataBase) + "' AND "
    cQuery += " SE1.D_E_L_E_T_ = ' ' "
	cQuery += " GROUP BY E1_CLIENTE "

	cQuery := ChangeQuery(cQuery)

	TCQUERY cQuery NEW ALIAS (cAlsCli)
	
	dbSelectArea(cAlsCli)
	(cAlsCli)->(dbGoTop())


	aJson:={}
	cJson:= '{"SALDO":'+cValToChar((cAlsCli)->SALDO) + "}"
	
    (cAlsCli)->(dbCloseArea())

Return cJson
