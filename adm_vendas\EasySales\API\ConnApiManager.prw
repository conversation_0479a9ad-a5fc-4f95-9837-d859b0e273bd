#Include "TOTVS.ch"
#Include "ConnApiManager.ch"

STATIC URLMANAGER 	:= ''
STATIC PATHMANAGER 	:= ''

//-------------------------------------------------------------------
/*/{Protheus.doc} ConnApiManager
CLASSE do Easy Sales para consumir serviços REST genericos
@protected
<AUTHOR>
@since	11/04/2019
@version
/*/
//-------------------------------------------------------------------
class ConnApiManager

	// ATRIBUTOS
	data TokenAPI
	data ambEAS
	data UrlEasy
	data PathEasy

	// METODOS GETTERS AND SETTERS
	method setTokenAPI()
	method getTokenAPI()
	method setAmbEAS()
	method getAmbEAS()

	// METODOS
	method New() CONSTRUCTOR
	method GetPathGen()
	method GetTkMan()
	method GetUrlMan()
	method SetHeader()
	method AccessToken()
	method GetGen()
	method PostGen()
	method DeleteGen()
endClass


//-------------------------------------------------------------------
/*/{Protheus.doc} NEW
Metodo Contrutor CLASSE ConnApiManager
@protected
<AUTHOR>
@param cAmbTotvs, caracter, Identifica qual o ambiente Protheus que est� sendo chamado (DEV, PRE, PROD)
@since	11/04/2019
@version
/*/
//-------------------------------------------------------------------
method New(cAmbTotvs) class ConnApiManager
	
	If valtype(cAmbTotvs) == 'C'
		self:setAmbEAS(cAmbTotvs)
	Else
		If "_DEV" $ Upper(GetEnvServer()) .OR. "DEV_" $ Upper(GetEnvServer())
			self:setAmbEAS( "D" )
		ElseIf "_PRE" $ Upper(GetEnvServer()) .OR. "PRE_" $ Upper(GetEnvServer())
			self:setAmbEAS( "H" )
		else
			self:setAmbEAS( "P" )
		EndIf
	EndIf

	URLMANAGER	:= self:GetUrlMan()
	PATHMANAGER := self:GetPathGen()
	
return self

//GETTERS AND SETTERS
method setTokenAPI(TokenAPI) class ConnApiManager
	self:TokenAPI := TokenAPI
return self

method getTokenAPI() class ConnApiManager
return self:TokenAPI

method setAmbEAS(ambEAS) class ConnApiManager
	self:ambEAS := ambEAS
return self

method getAmbEAS() class ConnApiManager
return self:ambEAS

//-------------------------------------------------------------------
/*/{Protheus.doc} GetUrlMan
RETORNA URL API-MANAGER
@protected
<AUTHOR>
@since	11/04/2019
@version
/*/
//-------------------------------------------------------------------
method GetUrlMan() class ConnApiManager
Local aAreaZX5		:= ZX5->( GetArea() )
Local cRet			:= ""
Local cTabelaZX5	:= "EAS003"
Local cChvZX5		:= "URLDEV"

if !(self:getAmbEAS() == "D")
	cChvZX5		:= "URLWSO"
EndIf

DbSelectArea("ZX5")
ZX5->( DbSetOrder(1) )

If ZX5->( DbSeek(xFilial("ZX5")+cTabelaZX5+cChvZX5) )
	cRet := Alltrim( ZX5->ZX5_DESCRI )
EndIf

RestArea(aAreaZX5)

return cRet

//-------------------------------------------------------------------
/*/{Protheus.doc} GetPathGen
RETORNA Path DAS APIs
@protected
<AUTHOR>
@since	11/04/2019
@version
/*/
//-------------------------------------------------------------------
method GetPathGen() class ConnApiManager
Local aAreaZX5		:= ZX5->( GetArea() )
Local cRet  		:= '/'
Local cTbPath		:= "EAS001"

if !(self:getAmbEAS() == "D")
	DbSelectArea("ZX5")
	ZX5->( DbSetOrder(1) )

	if self:getAmbEAS() == "P"
		cTbPath += "000001PROD"
	Else
		cTbPath += "000001PRE"
	EndIf

	If ZX5->( DbSeek(xFilial("ZX5")+cTbPath) )
		cRet := Alltrim( ZX5->ZX5_DESCRI )
	EndIf
EndIf

RestArea(aAreaZX5)

return  cRet


//-------------------------------------------------------------------
/*/{Protheus.doc} SetHeader
Adiciona o header das requisi��es
<AUTHOR>
@since 11/04/2019
@method SetHeader
@verbo GET
@return header
/*/
//-------------------------------------------------------------------
method SetHeader(cVerbo,cToken,cCodbatch,lManager) class ConnApiManager

Local aHeader	   := {}

Default cToken	  := ''
Default cCodbatch   := ''
Default lManager	:= self:getAmbEAS() $ "H;P"

if alltrim(upper(cVerbo)) == 'BATCH'
	aadd(aHeader, "Content-Type: multipart/mixed;boundary=batch_"+cCodbatch+"")
	aadd(aHeader, "Accept: application/json")
elseif alltrim(upper(cVerbo)) == 'POST' .or. alltrim(upper(cVerbo)) == 'PATCH' .or. alltrim(upper(cVerbo)) == 'PUT'
	aadd(aHeader, "Content-Type: application/json;charset=utf-8")
	aadd(aHeader, "Accept: application/json")
	aadd(aHeader, "Prefer: return=representation")
elseif alltrim(upper(cVerbo)) == 'GET'
	Aadd( aHeader, 'content-Type:application/json' )
	Aadd( aHeader, 'accept:application/json' )
endif

if lManager
	if empty(cToken)
		aadd(aHeader, "Authorization: "+self:accessToken()[2]+"")
	else
		aadd(aHeader, "Authorization: "+cToken+"") // Key Securit
	endif
endif

return aHeader


//-------------------------------------------------------------------
/*/{Protheus.doc} AccessToken
Busca o access o token api manager
<AUTHOR>
@since 11/04/2019
@method AccessToken
@verbo GET
@return o access token para autenticacao no EAS
/*/
//-------------------------------------------------------------------
method AccessToken() class ConnApiManager
Local aAreaZX5		:= ZX5->( GetArea() )
Local aRet 			:= {}
Local cAuthApiMng	:= GetMv("TI_WSOAUTH",.F.,"Basic ")
Local cPathWSO2		:= GetMv("TI_WSOPATH",.F.,"/api/token")
Local aHeaderMng	:= {}
Local cTbKEY		:= "EAS002"
Local cKEYWSO2		:= ""
Local cParam		:= ""
Local oJson			:= JsonObject():new()
Local oRestClient

DbSelectArea("ZX5")
ZX5->( DbSetOrder(1) )

if self:getAmbEAS() == "D"
	aRet := {.T.,"","","", URLMANAGER, PATHMANAGER}
Else
	if self:getAmbEAS() == "P"
		//1	Gerando Autorization BASIC da Api Manager
		If ZX5->( DbSeek(xFilial("ZX5")+cTbKEY+"PRODUC"+"KEYCON") )	//CONSUMER_KEY
			cKEYWSO2 += Alltrim( ZX5->ZX5_DESCRI )
		EndIf

		If ZX5->( DbSeek(xFilial("ZX5")+cTbKEY+"PRODUC"+"SECRET") )	//CONSUMER_SECRET
			cKEYWSO2 += ":" + Alltrim( ZX5->ZX5_DESCRI )
		EndIf
	Else
		//1	Gerando Autorization BASIC da Api Manager
		If ZX5->( DbSeek(xFilial("ZX5")+cTbKEY+"PREPRO"+"KEYCON") )	//CONSUMER_KEY
			cKEYWSO2 += Alltrim( ZX5->ZX5_DESCRI )
		EndIf

		If ZX5->( DbSeek(xFilial("ZX5")+cTbKEY+"PREPRO"+"SECRET") )	//CONSUMER_SECRET
			cKEYWSO2 += ":" + Alltrim( ZX5->ZX5_DESCRI )
		EndIf
	EndIf

	cAuthApiMng := cAuthApiMng + Encode64(cKEYWSO2)

	//SETA O HEADER
	Aadd(aHeaderMng, "Authorization:" + cAuthApiMng)
	Aadd(aHeaderMng, "Content-Type:application/x-www-form-urlencoded")
	aadd(aHeaderMng, "Accept: application/json")

	//SETA OS PARAMETROS
	cParam	:= "grant_type=" + GetMv("TI_WSO2TYP",,"client_credentials") //Grant_Type da API MANAGER

	oRestClient := FWRest():New( URLMANAGER )
	oRestClient:setPath(cPathWSO2)
	oRestClient:SetPostParams(cParam)

	//Recupera token do WSO2
	If oRestClient:POST(aHeaderMng)
		oJson:fromJson( alltrim( oRestClient:GetResult() ) )

		aRet := {.T.,oJson:GetJsonText("access_token"), oJson:GetJsonText("token_type"), oJson:GetJsonText("expires_in"), URLMANAGER, PATHMANAGER}
	Else
		if Valtype( oRestClient:cResult ) <> 'U'
			oJson:fromJson( alltrim( oRestClient:GetResult() ) )
			
			aRet := {.F., 'Erro: ' + oJson:GetJsonText("errorCode") +" - "+ oJson:GetJsonText("errorMessage"),"","","",""}
		else
			aRet := {.F., 'Erro: ' + oRestClient:GetLastError(),"","","",""}
		endif
	EndIf

	IIf( ValType( oRestClient ) == 'O', ( FreeObj( oRestClient ), oRestClient := Nil ), Nil )
	IIf( ValType( oJson ) == 'O', ( FreeObj( oJson ), oJson := Nil ), Nil )
EndIf

RestArea(aAreaZX5)

Return aRet


//-------------------------------------------------------------------
/*/{Protheus.doc} GetGen
EFETUA UM GET CONFORME PARAMETROS ENVIADOS
<AUTHOR>
@since 11/04/2019
@method GetGen
@return xValue
/*/
//-------------------------------------------------------------------
method GetGen(cMethod, cBodyModel, cCpoRet) class ConnApiManager
Local cMsg			:=""
Local lRet			:= .F.
Local aRet			:= {.F., ""}
Local aHeader		:= {}
Local aEasyParam	:= {}
Local cUrl			:= ""
Local cPathEasy		:= ""
Local oRest
Local oReq			:= JsonObject():new()

Default cCpoRet := ""

aEasyParam := Self:AccessToken()

If aEasyParam[1]
	cUrl  		:= aEasyParam[5]
	cPathEasy	:= aEasyParam[6]

	oRest := FWRest():New( cUrl )
	oRest:SetPath( cPathEasy + cMethod + "?" + cBodyModel) 

	//Tratamento para o Header da API
	Aadd( aHeader, 'content-Type:application/json' )
	Aadd( aHeader, 'accept:application/json' )
	If !Empty(aEasyParam[2])
		AADD( aHeader, 'authorization:' + aEasyParam[3] + ' ' + aEasyParam[2] )
	EndIf
	
	//Enviando o objeto oRest
	lRet := oRest:Get(aHeader)
	
	if Valtype( oRest:cResult ) <> 'U'
		If lRet
			If !Empty( cCpoRet )
				oReq:fromJson( alltrim( oRest:GetResult() ) )
				cMsg := &( "oReq:GetJsonText('"+Alltrim(cCpoRet)+ "')" )
			Else
				cMsg := alltrim( oRest:GetResult() )
			EndIf
		Else
			cMsg := Alltrim( CVALTOCHAR( oReq:GetJsonText("errorCode") ) ) +" - "+ oReq:GetJsonText("errorMessage")
		EndIf
	Else
		cMsg := "Error: "+ oRest:GetLastError()
	EndIf
Else
	cMsg := STR0002		//"Erro ao buscar parametros de conex�o com o serviços do Easy Sales!"
EndIf

aRet := {lRet, cMsg}

iif( ValType( oReq ) == 'O', ( FreeObj( oReq ), oReq := Nil ), Nil )
iif( ValType( oRest ) == 'O', ( FreeObj( oRest ), oRest := Nil ), Nil )

return aRet


//-------------------------------------------------------------------
/*/{Protheus.doc} PostGen
EFETUA UM POST CONFORME PARAMETROS ENVIADOS
<AUTHOR>
@since 12/08/2019
@method GetGen
@return xValue
/*/
//-------------------------------------------------------------------
method PostGen(cMethod, cBodyModel) class ConnApiManager
Local aRet 			:= { .T., ""}
Local aEasyParam	:= {}
Local cUrl			:= ""
Local cPathEasy		:= ""
Local aHeader		:= {}
Local lResult		:= .T.
Local oJson			:= JsonObject():new()
Local oRest

aEasyParam := Self:AccessToken()

If aEasyParam[1]
	cUrl  		:= aEasyParam[5]
	cPathEasy	:= aEasyParam[6]

	//Prepara o Rest.
	oRest := FWRest():New( cUrl )
	oRest:SetPath( cPathEasy + cMethod ) 

	//Tratamento para o Header da API
	Aadd( aHeader, 'content-Type:application/json' )
	Aadd( aHeader, 'accept:application/json' )
	If !Empty(aEasyParam[2])
		AADD( aHeader, 'authorization:' + aEasyParam[3] + ' ' + aEasyParam[2] )
	EndIf
	
	//Setando os parametros no objeto oRest
	oRest:SetPostParams( cBodyModel )

	//Enviando o objeto oRest
	lResult := oRest:POST(aHeader)

	if lResult
		oJson:fromJson( alltrim( oRest:GetResult() ) )
		
		cMsg := oJson:GetJsonText("message")
	else
		if Valtype( oRest:cResult ) <> 'U'
			oJson:fromJson( alltrim( oRest:GetResult() ) )
			
			cMsg := oJson:GetJsonText("errorCode") +" - "+ oJson:GetJsonText("errorMessage")
		else
			cMsg := 'Erro: ' + oRest:GetLastError()
		endif
	endif
Else
	cMsg := STR0002		//"Erro ao buscar parametros de conex�o com o serviços do Easy Sales!"
EndIf

IIf( ValType( oRest ) == 'O', ( FreeObj( oRest ), oRest := Nil ), Nil )
IIf( ValType( oJson ) == 'O', ( FreeObj( oJson ), oJson := Nil ), Nil )

aRet := {lResult, cMsg}

Return aRet

//-------------------------------------------------------------------
/*/{Protheus.doc} DeleteGen
EFETUA UM DELETE CONFORME PARAMETROS ENVIADOS
<AUTHOR>
@since 12/08/2019
@method DeleteGen
@return xValue
/*/
//-------------------------------------------------------------------

method DeleteGen(cMethod, cParam) class ConnApiManager
Local aRet 			:= { .T., ""}
Local aEasyParam	:= {}
Local cUrl			:= ""
Local cPathEasy		:= ""
Local aHeader		:= {}
Local lResult		:= .T.
Local oJson			:= JsonObject():new()
Local nTimeOut		:= getMv('TI_TMDELRT',,60) //DEFAULT FRAMEWORK
Local oRest

aEasyParam := Self:AccessToken()

If aEasyParam[1]
	cUrl  		:= aEasyParam[5]
	cPathEasy	:= aEasyParam[6]

	//Prepara o Rest.
	oRest := FWRest():New( cUrl )
	oRest:SetPath( cPathEasy + cMethod + "?" + cParam) 

	//Tratamento para o Header da API
	Aadd( aHeader, 'accept:application/json' )
	If !Empty(aEasyParam[2])
		AADD( aHeader, 'authorization:' + aEasyParam[3] + ' ' + aEasyParam[2] )
	EndIf
	

	// MUITO ARRISCADO � O TEMPO MAXIMO E NAO O MINIMO !!!
	// OU SEJA SE O SERVER OU A REDE TIVER DE P� VIRADA � PERIGOSO....
	// em segundos - ele vai retornar exatamente os segundos informados, se nao informar o default � 120 segundos / 2 minutos
	// ISSO PQ O RETORNO O STATUS DO RESPONSE � 204, E NAO 200 OU 201 QUE SERIA O COMUM
	//oRest:nTimeOut := nTimeOut
	oRest:nTimeOut := nTimeOut

	//Enviando o objeto oRest
	lResult := oRest:DELETE(aHeader)

	if lResult
		oJson:fromJson( alltrim( oRest:GetResult() ) )
		
		cMsg := oJson:GetJsonText("message")
	else
		If alltrim( oRest:GetLastError() ) != '204 NoContent' .and. alltrim( oRest:GetLastError() ) != '200 OK'
			if Valtype( oRest:cResult ) <> 'U'
				oJson:fromJson( alltrim( oRest:GetResult() ) )
				
				cMsg := oJson:GetJsonText("errorCode") +" - "+ oJson:GetJsonText("errorMessage")
			else
				cMsg := 'Erro: ' + oRest:GetLastError()
			endif
		Else
			cMsg := "OK"
		EndIf
	endif
Else
	cMsg := STR0002		//"Erro ao buscar parametros de conex�o com o serviços do Easy Sales!"
EndIf

IIf( ValType( oRest ) == 'O', ( FreeObj( oRest ), oRest := Nil ), Nil )
IIf( ValType( oJson ) == 'O', ( FreeObj( oJson ), oJson := Nil ), Nil )

aRet := {lResult, cMsg}

Return aRet
