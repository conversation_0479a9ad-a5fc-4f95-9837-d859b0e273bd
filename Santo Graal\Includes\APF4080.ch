#IFDEF SPANISH
   #define STR0001 "Atualiza Resumo de contratos"
   #define STR0002 "Cliente de"
   #define STR0003 "Cliente ate"
   #define STR0004 "Atualizacao"
   #define STR0005 "Somente Contrato"
   #define STR0006 "Somente Bloq Financeiro"
   #define STR0007 "Ambos"
   #define STR0008 "Resumo de contrato"
   #define STR0009 "Resumo de contratos"
   #define STR0010 "Aguarde..."
   #define STR0011 "Cliente"
   #define STR0012 "Loja"
   #define STR0013 "Nome"
   #define STR0014 "Erro"
   #define STR0015 "APF4080 : Inicio do processamento: "
   #define STR0016 " as "
   #define STR0017 "  Range de clientes:["
   #define STR0018 "]"
   #define STR0019 "Levantamento de clientes..."
   #define STR0020 " de "
   #define STR0021 " clientes"
   #define STR0022 "Analise financeira atualizado com sucesso!"
   #define STR0023 "Resumo atualizado com sucesso!"
   #define STR0024 " = "
   #define STR0025 "  Tempo de atualizacao :  "
   #define STR0026 " - Tempo de processamento :"
   #define STR0027 " Total de clientes analisados:("
   #define STR0028 "). Clientes processados ("
   #define STR0029 ").  Clientes com erros ("
   #define STR0030 ")"
   #define STR0031 " Deseja Visualizar as criticas ? "
   #define STR0032 "Log Atualizacao"
   #define STR0033 "APF4080 : Cliente: "
   #define STR0034 "APF4080 : Fim do processamento: "
#ELSE
   #IFDEF ENGLISH
      #define STR0001 "Atualiza Resumo de contratos"
      #define STR0002 "Cliente de"
      #define STR0003 "Cliente ate"
      #define STR0004 "Atualizacao"
      #define STR0005 "Somente Contrato"
      #define STR0006 "Somente Bloq Financeiro"
      #define STR0007 "Ambos"
      #define STR0008 "Resumo de contrato"
      #define STR0009 "Resumo de contratos"
      #define STR0010 "Aguarde..."
      #define STR0011 "Cliente"
      #define STR0012 "Loja"
      #define STR0013 "Nome"
      #define STR0014 "Erro"
      #define STR0015 "APF4080 : Inicio do processamento: "
      #define STR0016 " as "
      #define STR0017 "  Range de clientes:["
      #define STR0018 "]"
      #define STR0019 "Levantamento de clientes..."
      #define STR0020 " de "
      #define STR0021 " clientes"
      #define STR0022 "Analise financeira atualizado com sucesso!"
      #define STR0023 "Resumo atualizado com sucesso!"
      #define STR0024 " = "
      #define STR0025 "  Tempo de atualizacao :  "
      #define STR0026 " - Tempo de processamento :"
      #define STR0027 " Total de clientes analisados:("
      #define STR0028 "). Clientes processados ("
      #define STR0029 ").  Clientes com erros ("
      #define STR0030 ")"
      #define STR0031 " Deseja Visualizar as criticas ? "
      #define STR0032 "Log Atualizacao"
	  #define STR0033 "APF4080 : Cliente: "
	  #define STR0034 "APF4080 : Fim do processamento: "
   #ELSE
      #define STR0001 "Atualiza Resumo de contratos"
      #define STR0002 "Cliente de"
      #define STR0003 "Cliente ate"
      #define STR0004 "Atualizacao"
      #define STR0005 "Somente Contrato"
      #define STR0006 "Somente Bloq Financeiro"
      #define STR0007 "Ambos"
      #define STR0008 "Resumo de contrato"
      #define STR0009 "Resumo de contratos"
      #define STR0010 "Aguarde..."
      #define STR0011 "Cliente"
      #define STR0012 "Loja"
      #define STR0013 "Nome"
      #define STR0014 "Erro"
      #define STR0015 "APF4080 : Inicio do processamento: "
      #define STR0016 " as "
      #define STR0017 "  Range de clientes:["
      #define STR0018 "]"
      #define STR0019 "Levantamento de clientes..."
      #define STR0020 " de "
      #define STR0021 " clientes"
      #define STR0022 "Analise financeira atualizado com sucesso!"
      #define STR0023 "Resumo atualizado com sucesso!"
      #define STR0024 " = "
      #define STR0025 "  Tempo de atualizacao :  "
      #define STR0026 " - Tempo de processamento :"
      #define STR0027 " Total de clientes analisados:("
      #define STR0028 "). Clientes processados ("
      #define STR0029 ").  Clientes com erros ("
      #define STR0030 ")"
      #define STR0031 " Deseja Visualizar as criticas ? "
      #define STR0032 "Log Atualizacao"
	  #define STR0033 "APF4080 : Cliente: "
	  #define STR0034 "APF4080 : Fim do processamento: "
   #ENDIF
#ENDIF
