#include 'protheus.ch'
#include 'parmtype.ch'

User Function TCTBX008(nOper)

Local cRet := ''
Local aAreaFO7 := FO7->(GetArea())
Local aAreaFLF := FLF->(GetArea())

DbSelectArea("FO7")
DbSetOrder(3)
If DBSeek( XFILIAL("FO7")+"R"+SE1->(E1_PREFIXO+E1_NUM+E1_PARCELA) )  
	If nOper == 1 
		If !Empty(FO7->FO7_PRESTA)  .And. !Empty(FO7->FO7_PARTIC)
			cRet := POSICIONE("FLF", 1, XFILIAL("FLF")+"1"+FO7->(FO7_PRESTA+FO7_PARTIC),"FLF_CC")
		Endif
	ElseIf nOper == 2 
		If !Empty(FO7->FO7_PRESTA)  .And. !Empty(FO7->FO7_PARTIC)
			cRet := POSICIONE("FLF", 1, XFILIAL("FLF")+"1"+FO7->(FO7_PRESTA+FO7_PARTIC),"FLF_ITECTA")
		Endif
	Else 
		If !Empty(FO7->FO7_PRESTA)  .And. !Empty(FO7->FO7_PARTIC)
			cRet := POSICIONE("FLF", 1, XFILIAL("FLF")+"1"+FO7->(FO7_PRESTA+FO7_PARTIC),"FLF_CLVL")
		Endif
	EndIf
EndIf

FO7->(RestArea(aAreaFO7))
FLF->(RestArea(aAreaFLF))

aSize(aAreaFO7,0)
aSize(aAreaFLF,0)

Return cRet