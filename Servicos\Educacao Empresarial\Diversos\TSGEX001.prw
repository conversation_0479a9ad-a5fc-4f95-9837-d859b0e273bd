#INCLUDE "TOTVS.CH"
#INCLUDE "RESTFUL.CH"


User Function TSGEX001()
    Local cRet      := ""
    Local cResource := ""
    local cURL     	:= "" 
    Local oRest     
    Local aHeadStr  := {}
    Local cQuery  := ""
    Local cAlias	:= GetNextAlias()


        RpcClearEnv()
        RpcSetType(3)
        RpcSetEnv("00", "00001000100")

        cResource := GetMv('TI_SGETREI',,'/treinamento')
        cURL      := GetMv('TI_ISGEURL',,"http://10.172.216.20:7054/api")  // http://10.172.216.20:7054 OU 10.171.77.54:5054
        oRest       := FwRest():New(cURL)

        aadd(aHeadStr,'Content-Type:application/json')
        aadd(aHeadStr,"Accept: */*")
        //aadd(aHeadStr,'Authorization: Basic ' + cToken )    

        cQuery := " SELECT PDM_FILIAL,PDM_CURSO,PDM_NOME, "  
        cQuery += "	 CASE PDM_CATEG " 				        
        cQuery += "	    WHEN '1' THEN '1' " // 1-Presencial        
        cQuery += "		WHEN '2' THEN '2' " // 2-Elearning	        
        cQuery += "		WHEN '3' THEN '4' " // 4-Controller        
        cQuery += "		WHEN '4' THEN '3' " // 3-EAD 		        
        cQuery += "	 END PDM_CATEG, "                        
        cQuery += "	 CASE PDM_TPOSIS "                       
        cQuery += "	    WHEN 'P' THEN '1' "         // 1-PROTHEUS 	        
        cQuery += "		WHEN 'R' THEN '2' "         // 2-RM		        
        cQuery += "		WHEN 'G' THEN '3' " 	    // 3-FLUIG    
        cQuery += "		WHEN 'D' THEN '5' "         // 5-DATASUL       
        cQuery += "		WHEN 'L' THEN '6' "         // 6-LOGIX 
        cQuery += "		WHEN 'F' THEN '7' "         // 7-FORMACAO
        cQuery += "		WHEN 'E' THEN '8' "         // 8-EAD   
        cQuery += "		WHEN 'H' THEN '9' "         // 9-PCSISTEMAS     
        cQuery += "		WHEN 'I' THEN '10' "        //10-VIRTUALAGE
        cQuery += "	 END PDM_TPOSIS, "
        cQuery += "	 PDM_QTDHOR, "
        cQuery += "	 PDM_VALOR, "
        cQuery += "	 PDM_CODSB1 "
        cQuery += "  FROM " + RetSQLName('PDM') + " PDM "
        cQuery += "  WHERE PDM.D_E_L_E_T_=' ' AND PDM.PDM_FILIAL='" + xFilial('PDM') + "'"

        
        cAlias := MPSysOpenQuery(cQuery)

        While !(cAlias)->(eof())
            
            oJSObjeto := JsonObject():new()
            oJSObjeto["nome"]            := AllTrim((cAlias)->PDM_NOME)
            oJSObjeto["sistema"]         := AllTrim((cAlias)->PDM_TPOSIS)
            oJSObjeto["categoria"]       := Alltrim((cAlias)->PDM_CATEG)
            oJSObjeto["familiagestao"]   := 0
            oJSObjeto["linhaproduto"]    := 0
            oJSObjeto["bloqueado"]       := .F.
            oJSObjeto["cargahoraria"]    := (cAlias)->PDM_QTDHOR
            oJSObjeto["valorliquido"]    := (cAlias)->PDM_VALOR
            oJSObjeto["codigosb1"]       := Alltrim((cAlias)->PDM_CODSB1)
            

            oRest:SetPath(cResource)
            oRest:SetPostParams( EncodeUTF8(oJSObjeto:ToJson()) )


            oRest:Post(aHeadStr)
            //ConOut("POST: " + DecodeUtf8(oRest:GetResult()) )
            
            
            (cAlias)->(dbSkip())
    
        EndDo

        (cAlias)->(dbCloseArea())

        RPCClearEnv()

Return cRet

