#include    "tlpp-core.th"

Class   AddWebHookOnCheckoutDTO
    Public  Data    userCheckout                            As  Character
    Public  Data    endpoints                               As  Array

    Public  Method  new()                                   As  Object
    Public  Method  addEndPoints(cVal   As  Character)      As  Object
    Public  Method  toString()                              As  Character

EndClass



Method  new()   As  Object   Class   AddWebHookOnCheckoutDTO
    ::userCheckout  :=  ''
    ::endpoints :=  {}
Return  Self


Method  addEndPoints(cVal   As  Character)  As  Object  Class   AddWebHookOnCheckoutDTO
    Aadd(::endpoints, cVal)
Return  Self


Method  toString()      As  Character   Class   AddWebHookOnCheckoutDTO
    Local cJsonString   :=  ''  As  Character
    Local   oJson   :=  JsonObject():new()

    oJson['user']       :=  ::userCheckout
    oJson['endpoints']  :=  ::endpoints

    cJsonString :=  oJson:toJson()
    FreeObj(oJson)
Return cJsonString
