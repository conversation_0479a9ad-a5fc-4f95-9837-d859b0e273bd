#Include 'TOTVS.ch'

/*/ {Protheus.doc} User Function
Reserva de <PERSON>

<AUTHOR>
@version P12
@since 22/12/2021
/*/

User Function TCRMC026()

    Local lRet 	  := .F., nRetorno := 0, cQuery := ""
    Local aFields := { "E4_TIPO", "E4_DESCRI", "E4_CODIGO" }
    Local cPerm   := TC26Cond("1")
    Local cBloq   := TC26Cond("2")
    
    cQuery := "  SELECT E4_TIPO, E4_DESCRI, E4_CODIGO, R_E_C_N_O_ AS RECNO FROM " + RetSqlName("SE4") + " SE4 "
    cQuery += "  WHERE SE4.E4_FILIAL = '" + FWxFilial('SE4') + "' " 

    If !Empty(cPerm)    
        cQuery += "  AND SE4.E4_CODIGO IN "+ FORMATIN(cPerm,",") +" "
    ElseIf !Empty(cBloq)    
        cQuery += "  AND SE4.E4_CODIGO NOT IN "+ FORMATIN(cBloq,",") +" "
    EndIf
    
    cQuery += "  AND SE4.D_E_L_E_T_ = ' ' " 

    If JurF3Qry(cQuery, "SE4QRY", "RECNO", @nRetorno,,aFields )
        SE4->(DbGoto(nRetorno))
        lRet := .T.
    EndIf

Return lRet


/*/ {Protheus.doc} User Function
Reserva de Fonte

<AUTHOR> Ribeiro
@version P12
@since 22/12/2021
/*/

Static Function TC26Cond(cTipo)
    Local cAlias    := GetNextAlias()
    Local cCond     := ""

    BeginSQL Alias cAlias
        SELECT PW1_CONDPG
        FROM %table:PW1% PW1
        WHERE PW1_FILIAL = %exp:xFilial('PW1')%
            AND PW1_CODAGR = %exp:FWFldGet('ADY_XCODAG')%
            AND PW1_CODNIV = %exp:FWFldGet('ADY_XMODAL')%
            AND PW1_TIPO = %exp:cTipo%
            AND D_E_L_E_T_ = ' '
    EndSql

	While (cAlias)->( ! EOF() )
        cCond += PW1_CONDPG + ","

    	(cAlias)->( DbSkip() )
	EndDo

Return cCond
