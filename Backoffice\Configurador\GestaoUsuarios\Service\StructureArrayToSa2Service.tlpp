#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Service.StructureArrayToSa2Service

using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.CollectionOfParamUtils

/*/{Protheus.doc} StructureArrayToSa2
This class aims to create the array to use on SA2 Table
@type class
@version  1.0
<AUTHOR>
@since 10/31/2024
/*/
Class   StructureArrayToSa2
	static  method  getArrayFromJson(oJsonSA2   As  Json, oSupplier  As  Object, oCity   As  Object)  As  Array
EndClass


/*/{Protheus.doc} StructureArrayToSa2::getArrayFromJson
This method aims to use some object to create the the array data to SA2 Table
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@param oJsonSA2, json, The Object Json of the table SA2 derivate from the income character json
@param oSupplier, object, The object Supplier
@param oCity, object, The object City
@return array, The array data with the information to create any register on SA2 Table
/*/
Method getArrayFromJson(oJsonSA2   As  Json, oSupplier  As   Object, oCity   As  Object)   as  Array   Class   StructureArrayToSa2
	Local aData             :=  {}  As  Array
	Local cAccountDefault   :=  ''  As  Character
	Local nOperation        :=  0   As  Numeric
	Local cCodeSupplier     :=  ''  As  Character
	Local cStoreSupplier    :=  ''  As  Character
	Local cCodeCity         :=  ''  As  Character
	Local oParamStore

	oParamStore     :=  CollectionOfParam():new()
	cAccountDefault :=  oParamStore:getBankAccountDefault()
	nOperation      :=  oSupplier:getOperation()
	cCodeSupplier   :=  oSupplier:getCodeSupplier()
	cStoreSupplier  :=  oSupplier:getStoreSupplier()
	cCodeCity       :=  oCity:getCodeCity()


    
	If nOperation == 4
		aAdd( aData, { "A2_COD"  , cCodeSupplier, Nil } )
		aAdd( aData, { "A2_LOJA" , cStoreSupplier, Nil } )
	Endif

	aAdd( aData, { "A2_NOME"   ,Upper( Left( oJsonSA2:GetJsonObject( "A2_NOME" ), TamSX3( "A2_NOME")[1] ) )     , Nil } )
	aAdd( aData, { "A2_NREDUZ" , Upper( Left( oJsonSA2:GetJsonObject( "A2_NOME" ), TamSX3( "A2_NREDUZ")[1] ) )  , Nil } )
	aAdd( aData, { "A2_END"    , Upper( oJsonSA2:GetJsonObject( "A2_END" ) )                                    , Nil } )
	aAdd( aData, { "A2_NR_END" , oJsonSA2:GetJsonObject( "A2_NR_END" )                                          , Nil } )
	aAdd( aData, { "A2_BAIRRO" , Upper( oJsonSA2:GetJsonObject( "A2_BAIRRO" ) )                                 , Nil } )
	aAdd( aData, { "A2_EST"    , Upper( oJsonSA2:GetJsonObject( "A2_EST" ) )                                    , Nil } )

	If ! Empty( cCodeCity )
		aAdd( aData, { "A2_COD_MUN", cCodeCity, Nil } )
	Endif

	aAdd( aData, { "A2_MUN"    , Upper( oJsonSA2:GetJsonObject( "A2_MUN" ) )   , Nil } )
	aAdd( aData, { "A2_CEP"    , Upper( oJsonSA2:GetJsonObject( "A2_CEP" ) )   , Nil } )
	aAdd( aData, { "A2_TIPO"   , "F"                                           , Nil } )
	aAdd( aData, { "A2_DDD"    , oJsonSA2:GetJsonObject( "A2_DDD" )            , Nil } )
	aAdd( aData, { "A2_CGC"    , oJsonSA2:GetJsonObject( "A2_CGC" )            , Nil } )
	
	If nOperation == 3
	    aAdd( aData, { "A2_DDI"    , StrTran( oJsonSA2:GetJsonObject( "A2_DDI" ), "+", "" )    , Nil } )
	Endif

    aAdd( aData, { "A2_TEL"    , oJsonSA2:GetJsonObject( "A2_TEL" )            , Nil } )
	aAdd( aData, { "A2_BANCO"  , oJsonSA2:GetJsonObject( "A2_BANCO" )          , Nil } )
	aAdd( aData, { "A2_AGENCIA", oJsonSA2:GetJsonObject( "A2_AGENCIA" )        , Nil } )
	aAdd( aData, { "A2_NUMCON" , oJsonSA2:GetJsonObject( "A2_NUMCON" )         , Nil } )
	aAdd( aData, { "A2_DVCTA"  , oJsonSA2:GetJsonObject( "A2_DVCTA" )          , Nil } )
	aAdd( aData, { "A2_CONTA"  , cAccountDefault                                        , Nil } )
	aAdd( aData, { "A2_PAIS"   , oJsonSA2:GetJsonObject( "A2_PAIS" )           , Nil } )
	aAdd( aData, { "A2_CODPAIS", oJsonSA2:GetJsonObject( "A2_CODPAIS" )        , Nil } )
	aAdd( aData, { "A2_EMAIL"  , oJsonSA2:GetJsonObject( "A2_EMAIL" )          , Nil } )
	aAdd( aData, { "A2_TIPCTA" , oJsonSA2:GetJsonObject( "A2_TIPCTA" )         , Nil } )
	aAdd( aData, { "A2_COMPLEM", Upper( oJsonSA2:GetJsonObject( "A2_COMPLEM" )), Nil } )
	aAdd( aData, { "A2_XXDTINC", SubStr( DtoC( dDataBase ), 1, 5) + "/" + SubStr( DtoS( dDataBase ), 3, 2 ) , Nil } )

	If oJsonSA2:GetJsonObject( "A2_XTPFORN" ) != Nil
		aAdd( aData, { "A2_XTPFORN", Upper( oJsonSA2:GetJsonObject( "A2_XTPFORN" )), Nil } )
	Endif
	If oJsonSA2:GetJsonObject( "A2_XROTORI" ) != Nil
		aAdd( aData, { "A2_XROTORI", Upper( oJsonSA2:GetJsonObject( "A2_XROTORI" )), Nil } )
	Endif
	If oJsonSA2:GetJsonObject( "A2_FORMPAG" ) != Nil
		aAdd( aData, { "A2_FORMPAG", Upper( oJsonSA2:GetJsonObject( "A2_FORMPAG" )), Nil } )
	Endif

	// If nOperation == 4
	// 	aAdd( aData, { "A2_MSBLQL"  , "2", Nil } )
	// Endif

	FreeObj(oParamStore)

Return aData




