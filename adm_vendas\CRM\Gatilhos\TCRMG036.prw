#Include "totvs.ch"

/*/{Protheus.doc} User Function nomeFunction
	(long_description)
	@type  Function
	<AUTHOR>
	@since 18/09/2023
	@version version
	@param param_name, param_type, param_descr
	@return return_var, return_type, return_description
	@example
	(examples)
	@see (links_or_references)
	/*/
User Function TCRMG036()
Local aArea := GetArea()
Local dtVig 

dbSelectArea("PT4")
PT4->(DbSetOrder(1))
if PT4->(dbSeek(xFilial("PT4") + FwFldGet("ADZ_XCDCUP"))) .And. !Empty(PT4->PT4_XNUMES)
	dtVig := LastDate(MonthSum(dDatabase,PT4->PT4_XNUMES))
else
	dbSelectArea("DA1")
	DA1->(dbSetOrder(1))
	If DA1->(dbSeek(xFilial("DA1") + FwFldGet("ADY_TABELA") + FwFldGet("ADZ_PRODUT"))) .And. DA1->DA1_XVIGPR > 0
		dtVig := LastDate(MonthSum(dDatabase,DA1->DA1_XVIGPR))
	else
		dtVig :=  CTOD("31/12/2049")
	endif
endif
    
RestArea(aArea)

Return dtVig
