#INCLUDE "PROTHEUS.CH"

#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} TCTBA068 
Funcao responsavel pela callback do interceptor referente a gravar dados na CT2 DO ILP - PRIS
@type  Function
<AUTHOR> Menabue Lima
@since 21/03/2023
@version 1.0
/*/
User Function TCTBA068()

	Local oJson         := JsonObject():new()
	Local oBody
	Local lFalhou       := .F.
	Local cFalha        := ""
	Local aLancam		:= {}
	Local oResponse 	:=  JsonObject():new()
	Local aHeader       := {}
	Local nHeader		:= 0
	Local cFil 			:=""
	Private cCodReq		:= PARAMIXB[1]
	oBody:= oJson:FromJson(PARAMIXB[2])
	oResponse["status" ] := 200
	//Desmarca o registro para evitar sujeira
	P37->(RecLock("P37",.F.))
	P37->P37_STATUS := "2"
	P37->(MsUnLock())
	__cUserID := "000000"
	If VALTYPE(oBody) == 'U'
		aHeader := oJson:GetJsonObject("header")
		If ValType(aHeader) == "A"

			for nHeader :=1 to Len(aHeader)
				cFil:=aHeader[nHeader]:GetJsonObject("filial")
				if(!Empty(Alltrim(cFil)))
					cFilAnt:=cFil
					aLancam := aHeader[nHeader]:GetJsonObject("items")
					If ValType(aLancam) == "A"
						If Len(aLancam) > 0

							cFalha:=SaveCt2(aHeader[nHeader],aLancam)
							lFalhou:=!Empty(Alltrim(cFalha))
							P37->(RecLock("P37",.F.))
							If lFalhou
								P37->P37_STATUS := "5"
								oResponse["status" ]  := 500
								oResponse["mensagem" ]:=cFalha
								P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
							Else
								P37->P37_STATUS := "2"
								P37->P37_BODYRP := '{"status":200, "mensagem":"Registros incluidos com sucesso"}'
							EndIf
							P37->(MsUnLock())
							oResponse := Nil
							FreeObj(oResponse)
						Else
							P37->(RecLock("P37",.F.))
							oResponse["status" ] := 500
							oResponse["mensagem" ] := "Invalid Body"
							P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
							P37->P37_STATUS := "3"
							P37->(MsUnLock())
						EndIF
					Else
						P37->(RecLock("P37",.F.))
						oResponse["status" ] := 500
						oResponse["mensagem" ] := "Company/Branch not found!"
						P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
						P37->P37_STATUS := "3"
						P37->(MsUnLock())
					EndIF
				Else
					P37->(RecLock("P37",.F.))
					oResponse["status" ] := 500
					oResponse["mensagem" ] := "Filial is Empty"
					P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
					P37->P37_STATUS := "3"
					P37->(MsUnLock())

				EndIF
			Next aHeader
		Else
			P37->(RecLock("P37",.F.))
			oResponse["status" ] := 500
			oResponse["mensagem" ] := "Header is not Array"
			P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
			P37->P37_STATUS := "3"
			P37->(MsUnLock())
		EndIF
	Else
		P37->(RecLock("P37",.F.))
		oResponse["status" ] := 500
		oResponse["mensagem" ] := "Body is Empty"
		P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
		P37->P37_STATUS := "3"
		P37->(MsUnLock())
	EndIF
Return
/*/{Protheus.doc} SaveCt2
	Salva os lancamentos Contabeis ""
	@type   Function
	<AUTHOR> Menabue Lima
	@since 29/08/2022
	@version 12.1.33
 
/*/
Static Function SaveCt2(oCabec,aLcto)
	Local nI
	Local aArea         := GetArea()
	Local aCab          := {}
	Local aItens        := {}
	Local dDataLanc     := ctod("")
	Local nLinha        := '001'
	Local cError		:= ""
	Local cDebito 		:= ""
	Local cCCD			:= ""
	Local cItemD		:= ""
	Local cClVlDb		:= ""
	Local cCredit		:= ""
	Local cCCC			:= ""
	Local cItemC		:= ""
	Local cClVlCr		:= ""
	Local cDC			:= ""
	Local cOrigem		:= ""
	Local cHist 		:= ""
	Local nValor 		:= 0
	Local cItem 		:= "   "
	Default cEmp 		:= cEmpAnt
	Default cFil 		:= cFilAnt
	Private lMsErroAuto := .F.
	Private lMsHelpAuto := .T.
	Private CTF_LOCK    := 0
	Private lSubLote    := .T.


	SetFunName("TCTBA068")

	dDataLanc:=StoD(oCabec["data"])

	aCab := {}
	if  Empty(dDataLanc)
		dDataLanc:= ddatabase
	EndIF
	aAdd(aCab, {'DDATALANC' ,dDataLanc			,NIL} )
	aAdd(aCab, {'CLOTE' 	,oCabec["lote"] 	,NIL} )
	aAdd(aCab, {'CSUBLOTE'  ,oCabec["sublote"]  ,NIL} )
	aAdd(aCab, {'CPADRAO' 	,'' 				,NIL} )
	aAdd(aCab, {'NTOTINF' 	,0 					,NIL} )
	aAdd(aCab, {'NTOTINFLOT',0 					,NIL} )

	nLinha:=0
	For nI:= 1 to Len(aLcto)
		nLinha ++
		//cFilAnt		:= aLcto[nI]["CT2_FILIAL"]
		cOrigem		:= aLcto[nI]["CT2_ORIGEM"]
		cHist		:= aLcto[nI]["CT2_HIST"]
		cMoeda		:= aLcto[nI]["CT2_MOEDLC"]
		nValor		:= Val( strtran(aLcto[nI]["CT2_VALOR"],",","."))
		cDC			:= aLcto[nI]["CT2_DC"]
		cDebito 	:= aLcto[nI]["CT2_DEBITO"]
		cCCD		:= aLcto[nI]["CT2_CCD"]
		cItemD		:= aLcto[nI]["CT2_ITEMD"]
		cClVlDb		:= aLcto[nI]["CT2_CLVLDB"]
		cCredit		:= aLcto[nI]["CT2_CREDIT"]
		cCCC		:= aLcto[nI]["CT2_CCC"]
		cItemC		:= aLcto[nI]["CT2_ITEMC"]
		cClVlCr		:= aLcto[nI]["CT2_CLVLCR"]
		cItem		:= Soma1(cItem)
		aAdd(aItens,  { ;
			{'CT2_FILIAL' 	,cFilAnt 					, NIL},;
			{'CT2_LINHA' 	,cItem						, NIL},;
			{'CT2_MOEDLC'	,cMoeda					 	, NIL},;
			{'CT2_DC' 		,cDC				 		, NIL},;
			{'CT2_DEBITO' 	,cDebito				 	, NIL},;
			{'CT2_CREDIT' 	,cCredit				 	, NIL},;
			{'CT2_CCD' 		,cCCD				 		, NIL},;
			{'CT2_CCC' 		,cCCC				 		, NIL},;
			{'CT2_ITEMD' 	,cItemD						, NIL},;
			{'CT2_ITEMC' 	,cItemC					 	, NIL},;
			{'CT2_CLVLDB' 	,cClVlDb 					, NIL},;
			{'CT2_CLVLCR' 	,cClVlCr 					, NIL},;
			{'CT2_VALOR' 	,nValor						, NIL},;
			{'CT2_ORIGEM'   ,cOrigem				 	, NIL},;
			{'CT2_HIST' 	,cHist						, NIL},;
			{'CT2_EMPORI' 	,cEmpAnt 					, NIL},;
			{'CT2_FILORI' 	,cFilAnt 					, NIL}})
	Next nI

	MSExecAuto({|x, y,z| CTBA102(x,y,z)}, aCab ,aItens, 3,.T.)

	If !lMsErroAuto
		//UPD para trocar o CT2_MANUAL PARA 2
		cQry := " UPDATE "+ RetSQLName( 'CT2' ) +" CT2 "
		cQry += " SET CT2_MANUAL = '2' , CT2_ROTINA ='TCTBA068'"
		cQry += " WHERE CT2.CT2_FILIAL = '"+cFilAnt+"' "
		cQry += " AND CT2.CT2_ORIGEM = '"+cOrigem+"' "
		cQry += " AND CT2.CT2_MANUAL = '1' "
		cQry += " AND CT2.D_E_L_E_T_ = '"+Space(1)+"' "

		If TcSQLExec( cQry ) == 0
			TcSQLExec( 'COMMIT' )
		Else
			TcSQLExec( 'ROLLBACK' )
		EndIf
	Else // EM ESTADO DE JOB
		cTime:= dTos(Date()) + strtran(IncTime(time() ,3,0,0 ),":")
		cError := MostraErro("/system/PRIS/", "TCTBA068_"+cTime+"_"+cCodReq+".log")
		cError := SubStr(cError,1,3000)
	EndIf

	RestArea(aArea)

Return cError


/*/{Protheus.doc} SearchFil
	Busca a filial do RM no cadastro de DEPARA filial ZX5
	@type   Function
	<AUTHOR> Menabue Lima
	@since 22/03/2023
	@version 12.1.33
/*/
Static Function SearchFil(cChave2)
	Local cFil := " "
	LOcal cQry := ''
	Local cAliasZX5 := GetNextAlias()


	cQry := "SELECT * FROM "+RETSQLNAME("ZX5")+" where D_E_L_E_T_= ' '  "
	cQry += " AND ZX5_CHAVE2 = '"+cChave2+"'"
	cQry += " AND ZX5_TABELA = 'CTBA47' "
	cQry += " AND ZX5_CHAVE = 'CTBA47'
	cQry := ChangeQuery(cQry)
	//ZX5_CHAVE2 -->'77-1'
	//ZX5_DESCRI -->'00001000100'
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAliasZX5,.T.,.T.)

	IF !(cAliasZX5)->(EOF())
		cFil:=(cAliasZX5)->ZX5_DESCRI
	ENDIF
	(cAliasZX5)->(DBCLOSEAREA())


Return cFil
