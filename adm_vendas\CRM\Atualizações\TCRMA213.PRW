#include 'totvs.ch'
#include 'fwmvcdef.ch'
#include 'tcrma127.ch'

/*/{Protheus.doc} TCA127Ma()
Mostra browse de Produtos a Migrar
@type function
<AUTHOR>
@since 21/12/2018
@version 1.0
@return 
/*/

User Function TCRMA213

    Local aArea     := GetArea()
    Local oBrwPOQ   := Nil
    Local cCodNiv   := AOM->AOM_CODNIV
    Local cFunName  := funname()

    SetFunName("TCRMA213")

    DbSelectArea('POQ')
    //Browse secundário - POQ
    oBrwPOQ:= FWMBrowse():New()
    oBrwPOQ:SetAlias('POQ')
    oBrwPOQ:SetDescription('Cadastro de Produtos a Migrar') //
    oBrwPOQ:SetMenuDef('TCRMA213')
    //Filtra somente agrupador do CRM e nível selecionado no browse anterior ( AOM )
    oBrwPOQ:SetFilterDefault( "POQ_CODAGR=='" + GetMV("MV_CRMAGOP",,"000112") + "' .And. POQ_CODNIV=='" + cCodNiv + "' " )
    oBrwPOQ:Activate()

    RestArea(aArea)

    SetFunName(cFunName)

Return

Static Function MenuDef

    Local aRotina := {}

    ADD OPTION aRotina TITLE "Pesquisar"    ACTION "PesqBrw"                                    OPERATION 1 ACCESS 0 
    ADD OPTION aRotina TITLE "Visualizar"   ACTION "VIEWDEF.TCRMA213"                           OPERATION 2 ACCESS 0 
    ADD OPTION aRotina TITLE "Incluir"      ACTION "VIEWDEF.TCRMA213"                           OPERATION 3 ACCESS 0 
    ADD OPTION aRotina TITLE "Alterar"      ACTION "VIEWDEF.TCRMA213"                           OPERATION 4 ACCESS 0 
    ADD OPTION aRotina TITLE "Excluir"      ACTION "VIEWDEF.TCRMA213"                           OPERATION 5 ACCESS 0 
    ADD OPTION aRotina TITLE "Importar"     ACTION "U_TCA213Im('" + AOM->AOM_CODNIV + "')"      OPERATION 5 ACCESS 0 

Return aRotina


/*/{Protheus.doc} ModelDef
View MVC
@type function
<AUTHOR>
@since 11/04/2018
@version 1.0
@return oModel, Model MVC
/*/
Static Function ModelDef

Local oStruPOQ	:= FWFormStruct(1, 'POQ')
Local oModel	:= Nil

oModel := MPFormModel():New('MCRMA213',/*bPreValidacao*/, {|oModel| FwPosValid(oModel)} ,/*bCommit*/, /*bCancel*/ )

oModel:AddFields('POQMASTER',, oStruPOQ)
oModel:SetDescription(STR0001)  // 'Cadastro de Produtos a Migrar'
oModel:GetModel('POQMASTER'):SetDescription(STR0003) // 'Produtos a Migrar'

Return(oModel)

/*/{Protheus.doc} ViewDef
View MVC
@type function
<AUTHOR>
@since 11/04/2018
@version 1.0
@return oView, View MVC
/*/
Static Function ViewDef()

Local oView		:= Nil
Local oModel	:= FWLoadModel('TCRMA213')
Local oStruPOQ	:= FWFormStruct(2, 'POQ')

oStruPOQ:RemoveField('POQ_CODAGR')
oStruPOQ:RemoveField('POQ_CODNIV')

oView := FWFormView():New()
oView:SetModel(oModel)
oView:AddField('FORM1', oStruPOQ, 'POQMASTER')
oView:EnableTitleView('FORM1' , STR0003) // 'Produtos a Migrar'

Return(oView)

/*/{Protheus.doc} TCA213Im
Interface de importação de arquivo TXT
@type function
<AUTHOR>
@since 21/12/2018
@version 1.0
@return 
/*/
User Function TCA213Im(cCodNiv)

Local aPerg     := {}
Local aRet      := {}

aAdd( aPerg	,{6,STR0005,Space(150),"@!",".t.",".t.",80,.t.,STR0006+" .TXT |*.TXT","",GETF_LOCALHARD+GETF_LOCALFLOPPY+GETF_NETWORKDRIVE}) // 'Arquivo' ## ' Arquivos'

If ParamBox(aPerg,STR0007,aRet) // 'Selecione arquivo de origem'
    If MsgNoYes('Deseja excluir os registros existentes antes da importação?')
        FWMsgRun(,{||TCA213Del()},"Exclusão de registros","Aguarde")
    EndIf
	Processa({|| t213iprods(cCodNiv,aRet[1])})
EndIf

Return

Static Function t213iprods(cCodNiv,cFile)

Local nQtd          := 0
Local nTot          := 0
Local lFound        := .f.
Local aLine         := {}
Local aArea         := GetArea()
Local aAreaPOQ      := POQ->(GetArea())
Local cCodAgr       := SuperGetMV("MV_CRMAGOP",,"000112")

If !file(cFile)
    MsgAlert(STR0005+' '+cFile+STR0008) // 'Arquivo' ## ' inexistente'
    Return
EndIf

SB1->(dbSetOrder(1))

POQ->(dbSetOrder(1))

FT_FUSE(cFile)
nTot := FT_FLASTRE()
FT_FGOTOP()
Do While !(FT_FEOF())

    IncProc("Processando...  "+Alltrim(Str(++nQtd))+" de "+Alltrim(Str(nTot))+" ("+Alltrim(Str((nQtd/nTot)*100,6,2))+"%)")

    aLine := StrtoKarr(FT_FREADLN(),";")

    If Len(aLine) > 0 .and. !Empty(aLine[1]) .and. SB1->(dbSeek(xFilial("SB1")+Alltrim(aLine[1])))

        lFound := POQ->(DbSeek(xFilial('POQ') + cCodAgr + cCodNiv + SB1->B1_COD))

        POQ->(RecLock('POQ',!lFound))

        POQ->POQ_FILIAL := xFilial('POQ')
        POQ->POQ_CODAGR := cCodAgr
        POQ->POQ_CODNIV := cCodNiv
        POQ->POQ_CODIGO := SB1->B1_COD

        If POQ->(FieldPos("POQ_CANCIT")) > 0 //Inclusão do campo de cancelamento obrigatorio ou opcional
            If Len(aLine) > 1 .And. !Empty(aLine[2])
                POQ->POQ_CANCIT := Alltrim(aLine[2])
            Else
                POQ->POQ_CANCIT := " "
            EndIf
        EndIf

        If POQ->(FieldPos("POQ_CANCOB")) > 0 //Inclusão do campo de cancelamento obrigatorio ou opcional
            If Len(aLine) > 2 .And. !Empty(aLine[3])
                POQ->POQ_CANCOB := Alltrim(aLine[3])
            Else
                POQ->POQ_CANCOB := "1"
            EndIf
        EndIf

        POQ->(MSUnlock())

    EndIf

    FT_FSKIP()

EndDo

POQ->(RestArea(aAreaPOQ))
RestArea(aArea)

Return

//------------------------------------------------
Static Function TCA213Del()

    Local aArea     := GetArea()
    Local cAliasQry := GetNextAlias()

	BeginSql alias cAliasQry
		SELECT POQ.R_E_C_N_O_ POQRECNO
		FROM %table:POQ% POQ
		WHERE POQ.POQ_FILIAL=%exp:xFilial("POQ")%
            AND POQ.POQ_CODAGR = %exp:AOM->AOM_CODAGR%
            AND POQ.POQ_CODNIV = %exp:AOM->AOM_CODNIV%
			AND POQ.%notDel%	  
	EndSql

    While !(cAliasQry)->(Eof())

        POQ->(DbGoTo((cAliasQry)->POQRECNO))
        POQ->(RecLock('POQ',.F.))
        POQ->(DbDelete())
        POQ->(MSUnlock())

        (cAliasQry)->(dbSkip())

    EndDo

    RestArea(aArea)

Return Nil







//-------------------------------------------------------------------
/*/{Protheus.doc} FwPosValid(oModel)
Função para validar o modelo.


<AUTHOR> Wise
@since 27/12/2018
@version P12
/*/
//-------------------------------------------------------------------
Static Function FwPosValid(oModel)

    Local lRet			:= .T.
    Local aArea			:= GetArea()
    Local oModelPOQ		:= oModel:GetModel('POQMASTER')

    //Somente para alteração e inclusão
    If oModel:GetOperation() == MODEL_OPERATION_INSERT 

        oModelPOQ:LoadValue('POQ_CODAGR',AOM->AOM_CODAGR)
        oModelPOQ:LoadValue('POQ_CODNIV',AOM->AOM_CODNIV)

    EndIf

    RestArea(aArea)

Return lRet
