﻿#INCLUDE "PROTHEUS.CH"
#INCLUDE "TopConn.ch"
#INCLUDE "TbiConn.ch"
//Constantes
#Define CRLF		Chr(13)+Chr(10)


//-------------------------------------------------------------------
/*/{Protheus.doc} TFINJ035()
Job para consultar o limite do cliente
@type  Function
<AUTHOR>
@since 03/10/2020
/*/
//-------------------------------------------------------------------

User Function TFINJ035()
	Local cAlsCli := ''
	Local aRetLim := {}
	Local uStaCon := Nil
	Local cQuery  := ""
	Local cStatus       := "1"
	Local nLimite       := 0
	Local cAlsPZY := ""
	Local cUpDate := ""
	Local cIdHist := ""
	Local cObs    := ""
	Local cErroAuto := ""
	Local cPathDir := "\CPROVA\"
	Local cNameLog := ""
	Local lPedCre  := .F.
	Local nDiasLi	:= 30
	Local cAtivo	:= ""
	Local nDiasAl	:= 0
	Local lPayPar	:= .F.
	Local lPayLong  := .f.
	Local nMinParL  := 0
	Local cPay      := "2"//Nao
	Local nPosPay   := 11

	If Select("SM0") == 0
		RpcSetEnv( '00', '00001000100')
	EndIf
	nDiasAl	:= SuperGetMv("TI_FX005DL", , 30)
	nDiasLi	:= SuperGetMv("TI_FX005OV", , 10)// Limite minimo de dias para pedir uma nova concessao Menor que isso entra na regra do Over Limit
	lPedCre	:= SuperGetMv("TI_FX005PE", , .T.)//Pede uma nova concessao em caso de sem limite
	lPayLong:= SuperGetMv("TI_FX005PL", , .F.)//Pede uma nova concessao em caso de parcelas maiores que 12 x
	nMinParL:= SuperGetMv("TI_FX005PC", , 13)//PARCELA MIN PARA pedir uma concessao de 
	cTipoPa	:= SuperGetMv("TI_FX005TP", , "NF")//Tipos do titulo SE1 PARA O INNER JOIN pedir uma concessao de paylongo
	FwLogMsg("INFO", /*cTransactionId*/, "SUPPLIER", FunName(), "", "01", 'Inicio do Job TFINJ035 - consulta de limite...', 0, 0, {})

	cAlsCli := GetNextAlias()

	cQuery := " SELECT MAX(PZY_STATUS) STATUS, PZY_FILIAL, PZY_DOC, PZY_SERIE, PZY_CLIENT, PZY_LOJA"
	if lPayLong
		cQuery += " ,MAX(E1_PARCELA) PARCELA "
	EndIF
	cQuery += " FROM " + RetSqlName("PZY") +" PZY "
	If lPayLong
		cQuery += " INNER JOIN " + RetSqlName("SE1") +" E1 ON "
		cQuery += " E1_FILIAL 	= PZY_FILIAL AND "
		cQuery += " E1_NUM 		= PZY_DOC 	 AND "
		cQuery += " E1_PREFIXO	= PZY_SERIE  AND "
		cQuery += " E1_CLIENTE 	= PZY_CLIENT AND "
		cQuery += " E1_LOJA 	= PZY_LOJA 	 AND "
		cQuery += " E1_EMISSAO 	= PZY_EMISSA AND "
		cQuery += " E1_TIPO    IN " + Formatin(cTipoPa,"/") + " AND "
		cQuery += " E1.D_E_L_E_T_ = ' '  		 "
	EndIF
	cQuery += " WHERE PZY.D_E_L_E_T_ = ' '  "
	cQuery += " GROUP BY PZY_FILIAL, PZY_DOC, PZY_SERIE, PZY_CLIENT, PZY_LOJA "
	cQuery += " HAVING MAX(PZY_STATUS) IN('0','1') "
	cQuery += " ORDER BY SUM(PZY_VALBRU) DESC " // ORDENACAO POR VALOR
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsCli,.T.,.T.)

	While (cAlsCli)->(!Eof())
		cPay      := "2"//Nao
		lPayPar	:= .F.
		cObs:=""
		cAlsPZY := PosicPZY( (cAlsCli)->PZY_FILIAL, (cAlsCli)->PZY_DOC, (cAlsCli)->PZY_SERIE, (cAlsCli)->PZY_CLIENTE, (cAlsCli)->PZY_LOJA, (cAlsCli)->STATUS )
		cHasCon:=(cAlsPZY)->PZY_XTPCON
		// Pega o próximo PZY_IDHIST
		cIdHist := U_TFIdHist(.T.,(cAlsPZY)->PZY_FILIAL,(cAlsPZY)->PZY_DOC,(cAlsPZY)->PZY_SERIE,(cAlsPZY)->PZY_CLIENT,(cAlsPZY)->PZY_LOJA)

		If (cAlsCli)->STATUS == "0"
			// Cria o registro Aguardando Validaçao Crédito
			CRIAPZY("1",cIdHist,cAlsPZY, "AGUARDANDO VALIDAÇAO CRÉDITO", , (cAlsPZY)->PZY_FILIAL,"")
		EndIf
		//Valida PayLongo
		if lPayLong
			aRetCli := U_TFX005CON( cEmpAnt, (cAlsPZY)->PZY_FILIAL,.F.,1, (cAlsPZY)->PZY_CLIENT, (cAlsPZY)->PZY_LOJA, 0,,cPay)
			cPayCli:="2"
			IF  ValType(aRetCli) == "A"
				IF (Len(aRetCli) > 0)
					cPayCli:=aRetCli[nPosPay]
				Endif
			EndIF
			lPayPar 	:=  Val((cAlsCli)->PARCELA)  >= nMinParL
			cPay		:=cPayCli
			if lPayPar
				If cPayCli !="1"
					cPay:="1"//Sim
				EndIF
			EndIF

		EndIf
		// Verifica/solicita concessao.
		uStaCon := U_TFX005CON( cEmpAnt, (cAlsPZY)->PZY_FILIAL,.F.,3, (cAlsPZY)->PZY_CLIENT, (cAlsPZY)->PZY_LOJA, (cAlsPZY)->PZY_VALBRU, (cAlsPZY)->PZY_CODINT,cPay)

		If ValType(uStaCon)  == 'L'
			//Reprova a Nota por falta cadastral
			cNameLog:=(cAlsPZY)->PZY_FILIAL+(cAlsPZY)->PZY_CLIENT+(cAlsPZY)->PZY_DOC + StrTran(Time(), ":", "_")+".sup"
			cErroAuto:="SEM CONCESSAO - INADIMPLENCIA CADASTRAL"
			cErroAuto+=Mostraerro(cPathDir,cNameLog)
			FERASE(cPathDir+cNameLog)
			CRIAPZY("3",cIdHist,cAlsPZY, cErroAuto, , (cAlsPZY)->PZY_FILIAL,(cAlsPZY)->PZY_XTPCON)
			(cAlsCli)->(DbSkip())
			(cAlsPZY)->(DbCloseArea())
			Loop
		/*else
			if Empty(Alltrim(cHasCon))// Se Tiver Vazio atualiza a  PZY Com o status da ultima concessao
				cHasCon:=uStaCon[06]
				//Atualiza o registro pois pedimos uma alteracao de limite
				SetHasCon(cAlsPZY,cHasCon,cIdHist)
			EndIF*/
		EndIf

		Do Case
		Case uStaCon[01] $ "2" // TEM CONCESSAO
			cStatus := "2"
			 
		Case uStaCon[01] $ "3:4:5" // rejeitado/negado/cancelado Quando for 5 negar e alterar a msg para  o msm da AR5
			cObs    := "CREDITO NEGADO"
			cStatus := "3"
			aRetLim := U_TFX005CON( cEmpAnt, (cAlsPZY)->PZY_FILIAL,.F.,1, (cAlsPZY)->PZY_CLIENT, (cAlsPZY)->PZY_LOJA, 0,,cPay)//Cassio Menabue Lima 06/12/2021
			IF  ValType(aRetLim) == "A"//Quando se nega uma alteracao de limite porem o cliente ainda tem limite suficiente pra faturar
				IF (Len(aRetLim) > 0)
					cAtivo	:=aRetLim[7]
					If  aRetLim[1] > 1 // se For maior que 1 Real altera para status 2 Cassio Menabue Lima 06/12/2021 gravar uma variavel para o bloqueio
						cStatus := "2"
					EndIf
				Endif
			EndIF
		Case uStaCon[01] $ "0:1:6" // em analise/pendente

			//Acrescentado por Fernando Radu Muscalu em 15/12/2020
			//Checar o Tempo Limite no processo de crédito Supplier. Se excedeu, nega o crédito por tempo limite
			U_TFxChkTimeOut(dDataBase,(cAlsPZY)->PZY_FILIAL,(cAlsPZY)->PZY_DOC,(cAlsPZY)->PZY_SERIE,(cAlsPZY)->PZY_CLIENT,(cAlsPZY)->PZY_LOJA,.T.)

			(cAlsCli)->(DbSkip())
			(cAlsPZY)->(DbCloseArea())
			Loop
		EndCase

		If cStatus == '2'//Tem concess�o

			//Verifica se o cliente tem limite.
			aRetLim := U_TFX005CON( cEmpAnt, (cAlsPZY)->PZY_FILIAL,.F.,1, (cAlsPZY)->PZY_CLIENT, (cAlsPZY)->PZY_LOJA, 0,,cPay)
			if lPayLong
				cPayCli:="2"
				IF  ValType(aRetLim) == "A"
					IF (Len(aRetLim) > 0)
						cPayCli:=aRetLim[nPosPay]
					Endif
				EndIF
				//Força uma concessao somente para paylongo
				if lPayPar 
					// Parametro Suppier .And.  campo AR5_LONGTE, se supplier tiver como N mas o da AR5 Estiver como S nao pede uma nova, evitando ficar em loop
					If (cPayCli !="1" .And. uStaCon[nPosPay] !="1") .OR.  (cPayCli !="1" .And. uStaCon[05]+nDiasAl < dDataBase)
						cPay:="1"//Sim
						uStaCon :=  U_TFX005CON( cEmpAnt, (cAlsPZY)->PZY_FILIAL,.F.,2, (cAlsPZY)->PZY_CLIENT, (cAlsPZY)->PZY_LOJA, (cAlsPZY)->PZY_VALBRU, (cAlsPZY)->PZY_CODINT,cPay,"P")
						If ValType(uStaCon)  == 'L'
							//Reprova a Nota por falta cadastral
							cNameLog:=(cAlsPZY)->PZY_FILIAL+(cAlsPZY)->PZY_CLIENT+(cAlsPZY)->PZY_DOC + StrTran(Time(), ":", "_")+".sup"
							Mostraerro()
							ccErroAuto:="PAYLONGO - SEM CONCESSAO - INADIMPLENCIA CADASTRAL"
							cErroAuto+=Mostraerro(cPathDir,cNameLog)
							FERASE(cPathDir+cNameLog)
							CRIAPZY("3",cIdHist,cAlsPZY, cErroAuto, , (cAlsPZY)->PZY_FILIAL,(cAlsPZY)->PZY_XTPCON)
						EndIf
						 
						SetHasCon(cAlsPZY,"P",cIdHist)
						(cAlsCli)->(DbSkip())
						(cAlsPZY)->(DbCloseArea())
						Loop
					Else
						cPay:=cPayCli
					EndIF
				EndIF
			EndIF
			Do Case
				// Serviço esteja fora
			Case ( ValType(aRetLim) <> "A" ) //aRetLim = nil
				cUpDate := " UPDATE "+ RetSqlName("PZY")+" SET PZY_APIDIS = '2' "
				cUpDate += " WHERE "
				cUpDate += "   AND PZY_FILIAL= '" + (cAlsPZY)->PZY_FILIAL + "'"
				cUpDate += "   AND PZY_CLIENT='"  + (cAlsPZY)->PZY_CLIENT + "'"
				cUpDate += "   AND PZY_LOJA  ='"  + (cAlsPZY)->PZY_LOJA   + "'"
				cUpDate += "   AND PZY_DOC   = '" + (cAlsPZY)->PZY_DOC    + "'"
				cUpDate += "   AND PZY_SERIE = '" + (cAlsPZY)->PZY_SERIE  + "'"
				cUpDate += "   AND D_E_L_E_T_=' ' "
				If TcSqlExec(cUpDate) = 0
					TcSqlExec("COMMIT")
				EndIf
				(cAlsCli)->(DbSkip())
				(cAlsPZY)->(DbCloseArea())
				Loop
			Case Empty(aRetLim)//Veio Vazio pede uma nova concessao
				cStatus := "3"  // Tem concessao mas nao retornou nenhum limite
				nLimite := (cAlsPZY)->PZY_VALBRU
				if lPedCre
					cStatus := "1"  // Tem concessao mas nao retornou nenhum limite
					uStaCon := U_TFX005CON( cEmpAnt, (cAlsPZY)->PZY_FILIAL,.F.,4, (cAlsPZY)->PZY_CLIENT, (cAlsPZY)->PZY_LOJA, nLimite, (cAlsPZY)->PZY_CODINT,cPay)
					If ValType(uStaCon)  == 'L'
						//Reprova a Nota por falta cadastral
						cNameLog:=(cAlsPZY)->PZY_FILIAL+(cAlsPZY)->PZY_CLIENT+(cAlsPZY)->PZY_DOC + StrTran(Time(), ":", "_")+".sup"
						Mostraerro()
						ccErroAuto:="SEM CONCESSAO - INADIMPLENCIA CADASTRAL"
						cErroAuto+=Mostraerro(cPathDir,cNameLog)
						FERASE(cPathDir+cNameLog)
						CRIAPZY("3",cIdHist,cAlsPZY, cErroAuto, , (cAlsPZY)->PZY_FILIAL,(cAlsPZY)->PZY_XTPCON)
						(cAlsCli)->(DbSkip())
						(cAlsPZY)->(DbCloseArea())
						Loop
					EndIf
					//Atualiza o registro pois pedimos uma alteracao de limite
					cHasCon:=uStaCon[06]//Como foi uma alteracao de Limite atualiza a PZY com o tipo da Concessao
					SetHasCon(cAlsPZY,cHasCon,cIdHist)
				EndIF
			Case aRetLim[1] >= (cAlsPZY)->PZY_VALBRU
				cStatus := "2" // com limite
				if ( "BLOQUEADO" $ UPPER(cAtivo))
					cObs:= "LIMITE "+UPPER(cAtivo)+", ENVIANDO PARA A REGRA DO OVER LIMIT"
					//cStatus := "3" //Sem Limite verificar com o Diego e Rafa se vamos bloquer direto ou mandar pro over
				ELSE
					cObs:="COM LIMITE"
				EndIF
								// Limite menor que a NF
			Case aRetLim[1] < (cAlsPZY)->PZY_VALBRU
				cStatus := "3" // sem limite
				// Subtrai para trazer a diferenca e adicionar o saldo faltante no limite total da concessao solicitando uma alteracao de limite
				nLimite :=  aRetLim[4]+Abs((cAlsPZY)->PZY_VALBRU - aRetLim[1] ) 
				//solicita concessao.
				if lPedCre
					cHasCon:=uStaCon[06]//Como foi uma alteracao de Limite atualiza a PZY com o tipo da Concessao
					if  !(cHasCon $ "A|P") .Or.  uStaCon[05]+nDiasAl < dDataBase	// Verifica se a ultima concessao pedida e que nao foi de alteracao
						cStatus := "1" // sem limite
						uStaCon := U_TFX005CON( cEmpAnt, (cAlsPZY)->PZY_FILIAL,.F.,4, (cAlsPZY)->PZY_CLIENT, (cAlsPZY)->PZY_LOJA, nLimite, (cAlsPZY)->PZY_CODINT,cPay)
						If ValType(uStaCon)  == 'L'
							//Reprova a Nota por falta cadastral
							cNameLog:=(cAlsPZY)->PZY_FILIAL+(cAlsPZY)->PZY_CLIENT+(cAlsPZY)->PZY_DOC + StrTran(Time(), ":", "_")+".sup"
							Mostraerro()
							cErroAuto:=Alltrim(Mostraerro(cPathDir,cNameLog))//Mensagem do erro:
							cErroAuto:="SEM CONCESSAO - INADIMPLENCIA CADASTRAL "+cErroAuto
							FERASE(cPathDir+cNameLog)
							CRIAPZY("3",cIdHist,cAlsPZY, cErroAuto, , (cAlsPZY)->PZY_FILIAL,(cAlsPZY)->PZY_XTPCON)
							(cAlsCli)->(DbSkip())
							(cAlsPZY)->(DbCloseArea())
							Loop
						EndIf
						cHasCon:=uStaCon[06]
						//Atualiza o registro pois pedimos uma alteracao de limite
						SetHasCon(cAlsPZY,cHasCon,cIdHist)
					Else //Se for menor que 30 dias aprova e envia pra pre-TicketS
						cStatus:="2"
						cObs:= "SEM LIMITE, ENVIANDO PARA A REGRA DO OVER LIMIT"
					EndIF
				EndIF
			EndCase
			if(Empty(Alltrim(cObs)))
				cObs:= If(cStatus=="2","COM LIMITE", IIf(cStatus=="1",iif(cHasCon <>"A","NOVA CONCESSAO","ALTERACAO LIMITE"), "SEM LIMITE"))
			EndIF

		EndIf

		//Pega o proximo PZY_IDHIST
		cIdHist := U_TFIdHist(.T.,(cAlsPZY)->PZY_FILIAL,(cAlsPZY)->PZY_DOC,(cAlsPZY)->PZY_SERIE,(cAlsPZY)->PZY_CLIENT,(cAlsPZY)->PZY_LOJA)
		//Cria linha informando com limite / sem limite
		CRIAPZY(cStatus, cIdHist, cAlsPZY, cObs,"", (cAlsPZY)->PZY_FILIAL,cHasCon)

		(cAlsCli)->(DbSkip())
		(cAlsPZY)->(DbCloseArea())

	End

	(cAlsCli)->(dbCloseArea())

	FwLogMsg("INFO", /*cTransactionId*/, "SUPPLIER", FunName(), "", "01", 'Término do Job TFINJ035 - consulta de limite...', 0, 0, {})

Return

//-------------------------------------------------------------------
/*/{Protheus.doc} CriaPZY()
Cria PZY após consultar o limite
@type  Function
<AUTHOR> Madureira
@since 03/10/2020
/*/
//-------------------------------------------------------------------

Static Function CriaPZY(cStatus, cIdHist, cAls, cOBS, cCodCon, cFil,cHasCon)

	Default cCodCon := ""


	U_TFX005PZY(cEmpAnt,cFil,.F.,;
		{{'PZY_FILIAL',cFil                 },;
		{'PZY_DOC'   ,(cAls)->PZY_DOC	       },;
		{'PZY_SERIE' ,(cAls)->PZY_SERIE	   },;
		{'PZY_CLIENT',(cAls)->PZY_CLIENT	   },;
		{'PZY_LOJA'  ,(cAls)->PZY_LOJA        },;
		{'PZY_COND'  ,(cAls)->PZY_COND        },;
		{'PZY_EMISSA',STOD((cAls)->PZY_EMISSA)},;
		{'PZY_STATUS',cStatus      		   },;
		{'PZY_IDHIST',cIdHist    			   },;
		{'PZY_DUPL'  ,(cAls)->PZY_DUPL	       },;
		{'PZY_ORIGEM',"JOB"				   },;
		{'PZY_VALBRU',(cAls)->PZY_VALBRU	   },;
		{'PZY_APIDIS',(cAls)->PZY_APIDIS	   },;
		{'PZY_CODINT',cCodCon          	   },;
		{'PZY_OBS'   ,cObs                    },;
		{'PZY_XTPCON',cHasCon          	   }},.F.)

Return





//-------------------------------------------------------------------
/*/{Protheus.doc} PosicPZY()
Posicionar no cliente e loja da PZY cujo só tem a nf criada
@type  Function
<AUTHOR> Madureira
@since 03/10/2020
/*/
//-------------------------------------------------------------------
Static Function PosicPZY(cFil, cDoc, cSerie, cCodCli, cCodLoja, cStatus)

	Local cQuery  := ""
	Local cAlsRet := GetNextAlias()

	cQuery := " SELECT PZY_FILIAL, PZY_CLIENT, PZY_LOJA, PZY_FILIAL, PZY_DOC, PZY_SERIE, PZY_COND, "
	cQuery += " PZY_EMISSA, PZY_DUPL, PZY_VALBRU, PZY_APIDIS, PZY_STATUS, PZY_CODINT,PZY_XTPCON,PZY_IDHIST "
	cQuery += " FROM " + RetSqlName("PZY")
	cQuery += " WHERE PZY_FILIAL = '"+cFil+"' "
	cQuery += " AND PZY_DOC = '" + cDoc + "' AND PZY_SERIE = '" + cSerie + "' "
	cQuery += " AND PZY_CLIENT = '" + cCodCli + "' AND PZY_LOJA = '" + cCodLoja + "' "
	cQuery += " AND PZY_STATUS = '" + cStatus + "'"
	cQuery += " AND D_E_L_E_T_ = ' ' "
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsRet,.T.,.T.)

Return cAlsRet

/*/{Protheus.doc} SetHasCon
	Atualiza se e uma alteracao de limite 
	@type  Function
	<AUTHOR> Menabue Lima 
	@since 24/03/2022
	@version 1.0
	/*/
Static Function SetHasCon(cAlsPZYs,cHasCons,cIdHists)
	cUpDate := " UPDATE "+ RetSqlName("PZY")+" SET PZY_XTPCON = '"+cHasCons+"' "
	cUpDate += " WHERE "
	cUpDate += "    PZY_FILIAL= '" + (cAlsPZYs)->PZY_FILIAL + "'"
	cUpDate += "   AND PZY_CLIENT='"  + (cAlsPZYs)->PZY_CLIENT + "'"
	cUpDate += "   AND PZY_LOJA  ='"  + (cAlsPZYs)->PZY_LOJA   + "'"
	cUpDate += "   AND PZY_DOC   = '" + (cAlsPZYs)->PZY_DOC    + "'"
	cUpDate += "   AND PZY_SERIE = '" + (cAlsPZYs)->PZY_SERIE  + "'"
	cUpDate += "  AND PZY_IDHIST = '" + cIdHists + "'"
	cUpDate += "   AND D_E_L_E_T_=' ' "
	If TcSqlExec(cUpDate) == 0
		TcSqlExec("COMMIT")
	EndIf

Return
