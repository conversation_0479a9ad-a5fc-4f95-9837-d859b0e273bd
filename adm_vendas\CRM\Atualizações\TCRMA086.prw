#include 'protheus.ch'
#include 'parmtype.ch'

User Function TCRMA086()

	Local aCores		:= {}
	Private cCadastro	:= "Produtos Cloud"
	Private aRotina		:= {{"Pesquisar"	,"AxPesqui"			,0,1},;
							{"Visualizar"	,"AxVisual"			,0,2},;
							{"Incluir"		,"U_FINCLUI"		,0,3},;
							{"Alterar"		,"U_FALTERA"		,0,4} }
				
	MBrowse(6,1,22,75,"PIR",,,,,, aCores)
Return Nil


User Function FINCLUI(clAlias,nlReg, nlOpc)
	Local aArea	:= PIR->(GetArea())
	Local alParam:= {}
	
	aAdd( alParam, {|| .T.  } ) 		//antes da abertur a
	aAdd( alParam, {|| .T.	} )			//ao clicar no botao ok
	aAdd( alParam, {|| .T.	} ) 		//durante a transacao
	aAdd( alParam, {|| U_FimInc() } )	//termino da transacao
	
	AxInclui( "PIR", PIR->(RECNO()), nlOpc, , , , , , , , alParam )
	
	RestArea(aARea)
Return()

User Function FIMINC()
	
	MSMM(,TAMSX3("YP_TEXTO")[1],,alltrim(M->PIR_MEMO1),1,4,.t.,"PIR","PIR_CMEMO1")
	
	MSMM(,TAMSX3("YP_TEXTO")[1],,alltrim(M->PIR_MEMO2),1,4,.t.,"PIR","PIR_CMEMO2")
	
	MSMM(,TAMSX3("YP_TEXTO")[1],,alltrim(M->PIR_MEMO3),1,4,.t.,"PIR","PIR_CMEMO3")

Return()

User Function FALTERA(clAlias,nReg, nOpc)
	
	Local aArea	:= PIR->(GetArea())
	Local alParam:= {}
	
	aAdd( alParam, {|| .T.  } ) 		//antes da abertura
	aAdd( alParam, {|| .T.	} ) 		//ao clicar no botao ok
	aAdd( alParam, {|| .T. 	} ) 		//durante a transacao
	aAdd( alParam, {|| U_FimAlt() } )	//termino da transacao
	
	AxAltera( "PIR", PIR->(RECNO()), nOpc, , , , , , , , , alParam )
	
	RestArea(aARea)
Return(.T.)

User Function FIMALT()
	
	MSMM(,TAMSX3("YP_TEXTO")[1],,alltrim(M->PIR_MEMO1),1,4,.t.,"PIR","PIR_CMEMO1")
	
	MSMM(,TAMSX3("YP_TEXTO")[1],,alltrim(M->PIR_MEMO2),1,4,.t.,"PIR","PIR_CMEMO2")
	
	MSMM(,TAMSX3("YP_TEXTO")[1],,alltrim(M->PIR_MEMO3),1,4,.t.,"PIR","PIR_CMEMO3")
	
Return()