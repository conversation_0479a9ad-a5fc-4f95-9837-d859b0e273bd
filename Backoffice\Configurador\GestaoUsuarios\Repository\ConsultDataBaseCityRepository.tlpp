#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Repository.ConsultDataBaseCityRepository

/*/{Protheus.doc} ConsultDataBaseCity
Class to consult the City on Table CC2
@type class
@version  1.0
<AUTHOR>
@since 10/30/2024
/*/
Class   ConsultDataBaseCity
    private data    aAreaCC2   As   Array
    private data    oJsonSa2   As   Json
    private data    cCodeCity  As   Character

    public  method  new(oJson   As   Json)
    public  method  finish()
    public  method  searchCity()
    public  method  getCodeCity() As Character
EndClass

/*/{Protheus.doc} ConsultDataBaseCity::new
Method to create create a new instance of the Class
@type method
@version  1.0
<AUTHOR>
@since 10/30/2024
@param oJson, json, Object Json from the Table SA2 that was created with the Json String 
@return Object, Return the instantiated object itself
/*/
Method  new(oJson   As  Json)  Class   ConsultDataBaseCity
    ::aAreaCC2  :=  CC2->( GetArea() )
    ::oJsonSa2  :=  oJson
Return Self

/*/{Protheus.doc} ConsultDataBaseCity::finish
This method aims to apply a command RestArea on DataBase
@type method
@version  1.0
<AUTHOR>
@since 10/30/2024
@return Object, Return the instantiated object itself
/*/
Method  finish()    Class   ConsultDataBaseCity
    RestArea( ::aAreaCC2 )
Return Self

/*/{Protheus.doc} ConsultDataBaseCity::SearchCity
This method search the city on Table CC2 
@type method
@version  1.0
<AUTHOR>
@since 10/30/2024
@return object, Return the instantiated object itself
/*/
Method  SearchCity()    Class   ConsultDataBaseCity
    Local   cState    := ''  As  Character
    Local   cCity     := ''  As  Character

    cState  := ::oJsonSA2:GetJsonObject( "A2_EST" )
    cCity   := Upper( FwNoAccent( ::oJsonSA2:GetJsonObject( "A2_MUN" ) ) )


    CC2->( dbSetOrder( 4 ) )
    If CC2->( dbSeek( xFilial( "CC2" ) + cState + cCity ) )
        ::cCodeCity   := CC2->CC2_CODMUN
    Else
        ::cCodeCity   := ''
    Endif
Return Self

/*/{Protheus.doc} ConsultDataBaseCity::getCodeCity
This method get de Code of the City on Table CC2
@type method
@version  1.0
<AUTHOR>
@since 10/30/2024
@return character, The code CC2_CODMUN of the table CC2
/*/
Method  getCodeCity()    Class   ConsultDataBaseCity
Return ::cCodeCity
