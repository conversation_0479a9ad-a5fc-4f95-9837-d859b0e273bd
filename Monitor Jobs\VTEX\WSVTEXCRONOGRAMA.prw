#include 'totvs.ch'
#include 'restful.ch'
#INCLUDE "TOPCONN.CH"

#DEFINE CRLF Chr(10) + Chr(13)

/*/{Protheus.doc} 
Webservice de resgate de informações de propostas
@type function
@version  1.0
<AUTHOR> JACOME
@since 14/04/2021
/*/

WSRESTFUL WSVTEXCRONOGRAMA Description "Webservice retorna Cronograma VTEX" Format APPLICATION_JSON

	//WSDATA anomes  AS STRING
	WSDATA initialDate  AS STRING
	WSDATA finalDate    AS STRING
	WSDATA tipo      AS STRING

	WSMETHOD GET     DESCRIPTION "Get proposal information"  WSSYNTAX "/WSVTEXPREBILLING?initialDate=, finalDate=, idVind = "
	//WSSYNTAX "/WSVTEXCRONOGRAMA?anomes= "



END WSRESTFUL

/*/{Protheus.doc} 
Webservice de resgate de informações de propostas
@type function
@version  1.0
<AUTHOR> JACOME
@since 14/04/2021
/*/
WSMETHOD GET  WSRECEIVE initialDate, finalDate, tipo WSSERVICE WSVTEXCRONOGRAMA

	local oJsonStat
	Local lGet      := .T.
	//Local cDtIni,cDtFim


	self:SetContentType("application/json")

	oJsonStat  := JsonObject():new()

	oJsonStat := MontaQuery(self:initialDate,self:finalDate,self:tipo)//Função para montar a query

	cJson := oJsonStat:toJson()
	//------------------------------------
	//Convertendo em UTF8 o JSON
	//------------------------------------
	cJson := encodeUtf8(cJson)
	//--------------------------------------
	//Retorna como resposta o JSON do Model
	//--------------------------------------
	if !empty(cJson)
		self:setResponse(cJson)
	endif

	freeObj(oJsonStat)

return lGet

/*/
------------------------------------------------------------------
{Protheus.doc} MontaQuery
<AUTHOR> B. Melo
@since Out/2021
@version 1.00
------------------------------------------------------------------
/*/
Static Function MontaQuery(cDtIni,cDtFim,ctipo)


	local oJsonStat,oJsonAux as object
	Local cQuery 	:= ""
	Local nAux 		:= 0
	Local nTotReg 		:= 0

	oJsonStat  := JsonObject():new()

	if ctipo == "valor"
		cQuery += " SELECT SUM(PH5_VLRFAT) PH5_VLRFAT  FROM PH5000 PH5" + CRLF
	elseif ctipo == "quantidade"
		cQuery += " SELECT SUM(PH5_QTDFAT) PH5_QTDFAT  FROM PH5000 PH5 " + CRLF
	endif

	cQuery += " INNER JOIN CN9000 CN9 ON " + CRLF
	cQuery += " CN9_FILIAL = ' ' " + CRLF
	cQuery += " AND CN9.D_E_L_E_T_ = ' ' " + CRLF
	cQuery += " AND CN9_ESPCTR = '2' " + CRLF
	cQuery += " AND CN9_SITUAC = '05' " + CRLF
	cQuery += " AND CN9_REVATU = ' ' " + CRLF
	cQuery += " AND CN9_NUMERO = PH5_CONTRA " + CRLF
	cQuery += " AND CN9_REVISA = PH5_REVISA " + CRLF
	cQuery += " INNER JOIN SB1000 SB1 ON " + CRLF
	cQuery += " SB1.D_E_L_E_T_ <> '*' " + CRLF
	cQuery += " AND B1_COD = PH5_PRODUT " + CRLF
	cQuery += " AND B1_XBILLI = '1' " + CRLF
	cQuery += " WHERE PH5.D_E_L_E_T_ <> '*' " + CRLF
	cQuery += " AND PH5_ANOMES = '"+SubString(cDtIni,1,6)+"' "
	cQuery += " AND PH5_UNINEG = '00501000100' " 

	if SELECT("T01") > 0
		T01->(DbCloseArea())
	endif
	TCQuery cQuery new Alias T01

	nTotReg := Contar("T01","!Eof()")

	oJsonStat["total"] := nTotReg
	
	T01->(DbGoTop())
	oJsonStat["pedidos"] := {}
	while !T01->(EOF())

		oJsonAux := JsonObject():new()

		if ctipo == "valor"
			oJsonAux["valor"]        	:=  T01->PH5_VLRFAT
			nAux += T01->PH5_VLRFAT
			aadd(oJsonStat["pedidos"], oJsonAux)

		elseif ctipo == "quantidade"

			oJsonAux["quantidade"]     :=  T01->PH5_QTDFAT
			nAux += T01->PH5_QTDFAT
			aadd(oJsonStat["pedidos"], oJsonAux)

		endif

		T01->(DbSkip())
	Enddo

	oJsonStat["valortotal"] := nAux
	
	T01->(DbCloseArea())

Return oJsonStat
