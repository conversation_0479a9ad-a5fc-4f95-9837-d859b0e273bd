#include 'protheus.ch'
#Include 'parmtype.ch'
#Include 'RestFul.ch'
#Include 'topconn.ch'
#Include 'totvs.ch'


/*/{Protheus.doc} GWMSWSUSERACESSOS
//Api para monitorar os jobs de schedules
<AUTHOR> silva
@since 27/04/2023
@version 1.0
@example
(examples)
@see (links_or_references)
/*/

WsRestFul GWMSWSUSERACESSOS Description "Retorna informações referente aos jobs de schedules"

	WSDATA action AS STRING
	WsMethod Get Description "Get expenses information"  WSSYNTAX "/GWMSWSUSERACESSOS?action=initialDate=, finalDate=,fila="

End WsRestFul

WsMethod Get WSRECEIVE action WsService GWMSWSUSERACESSOS

	Local oJson        as object
	Local oJsonAux     as object
	Local cTipo        := ""
	Local cQuery       := ""
	Local cJson        := ""
	Local cStatus      := ""
    Local cTipos       := ""
    Local nx           := 0 
	Local nTipo:= aScan(Self:aQueryString, { |x| Upper(Alltrim(x[1])) == "ACTION"})
	Local nDataIn := aScan(Self:aQueryString, { |x| Upper(Alltrim(x[1])) == "INITIALDATE"})
	Local nDataFim := aScan(Self:aQueryString, { |x| Upper(Alltrim(x[1])) == "FINALDATE"})
	Local nFila := aScan(Self:aQueryString, { |x| Upper(Alltrim(x[1])) == "FILA"})
	Local nStatus := aScan(Self:aQueryString, { |x| Upper(Alltrim(x[1])) == "STATUS"})


	::SetContentType("application/json")
	::SetResponse('[')

	If !Empty(nTipo)
		cTipo := Self:aQueryString[nTipo][2]
	EndIf

	If !Empty(nFila)
		cFila := Self:aQueryString[nFila][2]
	EndIf

	If !Empty(nStatus)
		cStatus := Self:aQueryString[nStatus][2]
	EndIf

	If Empty(cTipo)
		cMsg := "Informar o tipo de consulta. total ou titulos"
		::SetResponse('{')
		::SetResponse('"Retorno" : false,')
		::SetResponse('"Mensagem" : "' + EncodeUtf8(cMsg) + '"')
		::SetResponse('}')
		Return .T.
	endif

	if !empty(nDataIn) .and. !empty(nDataFim)
		cInitialDate := Self:aQueryString[nDataIn][2]
		cFinalDate := Self:aQueryString[nDataFim][2]
	else
		cMsg := "Informar a data inicio e data fim no parametro "
		::SetResponse('{')
		::SetResponse('"Retorno" : false,')
		::SetResponse('"Mensagem" : "' + EncodeUtf8(cMsg) + '"')
		::SetResponse('}')
		Return .T.
	endif


	if cTipo == 'detalhes'


		oJson := JsonObject():New()

		oJson["detalhes"] := {}

		if !Empty(cStatus)
			if cStatus == "-1"
				cQuery := " SELECT P36.P36_DESCRI, P37.P37_HORA,P37.P37_DATA, P37.P37_STATUS '-1' FROM "+RetSqlName("P37")+" P37 "
			Else
				cQuery := " SELECT P36.P36_DESCRI, P37.P37_HORA,P37.P37_DATA, " 
				cQuery += " CASE WHEN P37_STATUS =  '0'  THEN 'Nao Enviado/Recebido'  "       
				cQuery += " WHEN P37_STATUS =  '1'  THEN 'Pendente enviado/recebido porem em processamento' "        
				cQuery += " WHEN P37_STATUS =  '2'  THEN 'Enviado/Recebido com sucesso a requisicao principal' "         
				cQuery += " WHEN P37_STATUS =  '3'  THEN 'Erro no envio/Recebimento' "        
				cQuery += " WHEN P37_STATUS =  '4'  THEN 'Enviado/Recebido com sucesso a requisicao  Assincrona' "         
				cQuery += " WHEN P37_STATUS =  '5'  THEN 'Enviado/Recebido porem com erro no processamento' ELSE 'Status nao encontrado' END P37_STATUS   " 
				cQuery +=  " FROM "+RetSqlName("P37")+" P37 "
			Endif
		Else
				cQuery := " SELECT P36.P36_DESCRI, P37.P37_HORA,P37.P37_DATA, " 
				cQuery += " CASE WHEN P37_STATUS =  '0'  THEN 'Nao Enviado/Recebido'  "       
				cQuery += " WHEN P37_STATUS =  '1'  THEN 'Pendente enviado/recebido porem em processamento' "        
				cQuery += " WHEN P37_STATUS =  '2'  THEN 'Enviado/Recebido com sucesso a requisicao principal' "         
				cQuery += " WHEN P37_STATUS =  '3'  THEN 'Erro no envio/Recebimento' "        
				cQuery += " WHEN P37_STATUS =  '4'  THEN 'Enviado/Recebido com sucesso a requisicao  Assincrona' "         
				cQuery += " WHEN P37_STATUS =  '5'  THEN 'Enviado/Recebido porem com erro no processamento' ELSE 'Status nao encontrado' END P37_STATUS   " 
				cQuery +=  " FROM "+RetSqlName("P37")+" P37 "
		endif
		cQuery += " INNER JOIN "+RetSqlName("P36")+" P36 ON (P36.P36_FILIAL = '"+xFilial("P36")+"' AND P36.P36_CODIGO = P37.P37_CODAPI AND P36.D_E_L_E_T_=' ') "
		cQuery += " WHERE P37.P37_FILIAL IS NOT NULL
		cQuery += " AND P37.P37_CODAPI IN ('000018','000022','000023','000026') " 

		if !Empty(cStatus)
			if cStatus == "-1"
				cQuery += " AND P37.P37_STATUS NOT IN ('0','1','2','3','4','5')  "
			else

				If !Empty(cStatus)
					For nx := 1 to len(cStatus) step 2
						cAux := SubStr(cStatus,nx,1)
						If cAux <> '***'
							cTipos += "'"+cAux+"',"
						EndIf
					Next
				EndIf

				cTipos := SubStr(cTipos,1,Len(cTipos)-1)
				cQuery += " AND P37.P37_STATUS IN ("+cTipos+") "
			Endif
		Endif
		cQuery += " AND P37.P37_DATA >= '"+cInitialDate+"' and P37.P37_DATA <= '"+cFinalDate+"'  "
		cQuery += " AND P37.D_E_L_E_T_=' ' "

		if SELECT("T01") > 0
			T01->(dbCloseArea())
		endif

		ConOut(cQuery)

		TcQuery cQuery new Alias T01

		T01->(DbGoTop())
		while T01->(!eof())

			oJsonAux := JsonObject():new()
			oJsonAux["Item"]	:= T01->P36_DESCRI
			oJsonAux["Hora"]	:= T01->P37_HORA
			oJsonAux["Data Processamento"]	:= T01->P37_DATA
			oJsonAux["Status"]	:= T01->P37_STATUS

			aadd(oJson["detalhes"], oJsonAux)

			T01->(dbSkip())
		EndDo

		cJson := oJson:toJson()
		cJson := encodeUtf8(cJson)

		Conout(cJson)

	Elseif cTipo == 'status'


		oJson := JsonObject():New()

		oJson["Status_Envio"] := {}

		if !Empty(cStatus)
			if cStatus == "-1"
				cQuery := " SELECT 'Status nao encontrado' P37_STATUS, COUNT(*) TOTAL FROM "+RetSqlName("P37")+" P37 "
			Else
				cQuery := " SELECT CASE WHEN P37_STATUS =  '0'  THEN 'Nao Enviado/Recebido'  "       
				cQuery += " WHEN P37_STATUS =  '1'  THEN 'Pendente enviado/recebido porem em processamento' "        
				cQuery += " WHEN P37_STATUS =  '2'  THEN 'Enviado/Recebido com sucesso a requisicao principal' "         
				cQuery += " WHEN P37_STATUS =  '3'  THEN 'Erro no envio/Recebimento' "        
				cQuery += " WHEN P37_STATUS =  '4'  THEN 'Enviado/Recebido com sucesso a requisicao  Assincrona' "         
				cQuery += " WHEN P37_STATUS =  '5'  THEN 'Enviado/Recebido porem com erro no processamento' ELSE 'Status nao encontrado' END P37_STATUS,   "
				cQuery += " COUNT(*) TOTAL FROM "+RetSqlName("P37")+" P37 "
			Endif
		Else
				cQuery := " SELECT CASE WHEN P37_STATUS =  '0'  THEN 'Nao Enviado/Recebido'  "       
				cQuery += " WHEN P37_STATUS =  '1'  THEN 'Pendente enviado/recebido porem em processamento' "        
				cQuery += " WHEN P37_STATUS =  '2'  THEN 'Enviado/Recebido com sucesso a requisicao principal' "         
				cQuery += " WHEN P37_STATUS =  '3'  THEN 'Erro no envio/Recebimento' "        
				cQuery += " WHEN P37_STATUS =  '4'  THEN 'Enviado/Recebido com sucesso a requisicao  Assincrona' "         
				cQuery += " WHEN P37_STATUS =  '5'  THEN 'Enviado/Recebido porem com erro no processamento' ELSE 'Status nao encontrado' END P37_STATUS,   "
				cQuery += " COUNT(*) TOTAL FROM "+RetSqlName("P37")+" P37 "
		endif
		cQuery += " WHERE P37.P37_FILIAL IS NOT NULL
		cQuery += " AND P37.P37_CODAPI IN ('000018','000022','000023','000026') " 

		if !Empty(cStatus)
			if cStatus == "-1"
				cQuery += " AND P37.P37_STATUS NOT IN ('0','1','2','3','4','5')  "
			else

				If !Empty(cStatus)
					For nx := 1 to len(cStatus) step 2
						cAux := SubStr(cStatus,nx,1)
						If cAux <> '***'
							cTipos += "'"+cAux+"',"
						EndIf
					Next
				EndIf

				cTipos := SubStr(cTipos,1,Len(cTipos)-1)
				cQuery += " AND P37.P37_STATUS IN ("+cTipos+") "
			Endif
		Endif
		cQuery += " AND P37.P37_DATA >= '"+cInitialDate+"' and P37.P37_DATA <= '"+cFinalDate+"'  "
		cQuery += " AND P37.D_E_L_E_T_=' ' "
		cQuery += " GROUP BY P37.P37_STATUS "

		if SELECT("T01") > 0
			T01->(dbCloseArea())
		endif

		TcQuery cQuery new Alias T01

		T01->(DbGoTop())
		while T01->(!eof())


			oJsonAux := JsonObject():new()
			oJsonAux["Status"]  	:= T01->P37_STATUS
			oJsonAux["Total"]  	    := T01->TOTAL

			aadd(oJson["Status_Envio"], oJsonAux)

			T01->(DBSkip())

		enddo

		cJson := oJson:toJson()
		cJson := encodeUtf8(cJson)

	Endif
	if !empty(cJson)
		::SetResponse(oJson:ToJson())
	Endif

	FreeObj(oJson)
	::SetResponse(']')
	FreeObj(oJsonAux)

return .T.
