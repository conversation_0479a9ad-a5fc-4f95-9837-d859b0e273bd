#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "TGCVA018.CH"
#INCLUDE "GCTXDEF.CH"




Static nDiasAbt	:= GetMv("TI_ZQEDIA")		//30
Static nLimina	:= GetMV("TI_LIMINA")		//500

Static cGrpSaas	:= GetMv( 'TI_GRCLOUD',,'0250|0251|0252|0253|0254|0255|0256|0257|0258|0259' )






//---------------------------------------------------------------------
Static cGrpSla	:= GetMv( 'TDI_GRPSLA',,'000036')//tibackop-1176 Grupo de SLA
Static cGrpGEs	:= GetMv( 'TDI_GRPGES',,'0180' )		//// Grupo de Garantia Estendida
//Static cGrpSla	:= GetMv( 'TDI_GRPSLA',,'0164|0168' )	//// Grupo de SLA
Static cGrpMID	:= GetMv( 'TDI_GRPMID',,'0191' )
Static cGrpBso	:= GetMv( 'TDI_GRPBSO',,'0137|0138|0139|0140|1120|1121|1122|1123|1125|1127|1128|1130|1133' )
Static cGrpGE	:= GetMv( 'TDI_GRPGE' ,,'' ) //
Static cGrpBcs	:= GetMv( 'TDI_GRPBCS',,'' )
//Static cZQELRec	:= GetMv( 'TDI_PICPAD',,'03|07' )	
Static cCtrSitu	:= GetMv( 'TDI_SITUAC',,'A|G|P' )
Static cCDUCod	:= GetMv( 'TDI_CODCDU',,'02' )//
Static cCodNCdu	:= GetMv( 'TDI_100CDU',,'00' )
Static cCDUSer	:= GetMv( 'TDI_SERCDU',,'0201|0202|0203|0251' )
Static cSaasPrd := GetMv( 'TDI_SAAPRD',,'0124101003|0124101004' )
Static cSerSaas := GetMv( 'TDI_SERSAA',,'01' )
Static cFilExc 	:= GetMv( 'TDI_FILEXC',,'01002000100 ' )  //// Filial BPO da Totvs

Static cGrpAMS	:= GetMv( 'TI_GRPAMS',,'1114|0272' )


User Function MyZqe()
Return U_TGCVA018( 'CONT30953', '000021' )



User Function TGCVA018( cContra, cRevisa, cExeReceiv )
	Local lRet		:= .T.
	Local aArea		:= GetArea()
	Local aZQEItm	:= {}
	Local aZQGItm	:= {}
	Local cRevZQE	:= IIf( Empty( cRevisa ), cRevisa ,IIf( cRevisa == '000001', ' ', Tira1( cRevisa ) ) ) 
	Local lPubConsAnt := GetMv("TI_CNT2PUB",,.T.)//como não ha definicao das regras, criamos este parametro 	
	Default cExeReceiv := "" 
	
	//Retirando variáveis Staticas para Private
	Private aProdTotvs := {}
	Private aProdLight := {}
	Private aProdFull  := {}
	Private lSimSetPub := .F. 
	
	If lPubConsAnt
		If CliPublico( cContra, cRevisa )
			lSimSetPub := .T.
		EndIf
	EndIf

	aZQEItm 	:= ZQEGetItTHr( cContra, cRevZQE )
	aZQGItm 	:= ZQGGetDtaEq( cContra, cRevZQE )
	
	If !TSetCliCNC( cContra, cRevisa )
		lRet := .F.
	Else
		lRet := TdiZQEPut( cContra, cRevisa, cExeReceiv )
		
		//// Rotina para tratar ZQE de Contrato Cancelado - tickets 475046 / 575056
		If !lRet
			lRet :=  TdiZQECan( cContra, cRevisa )
		EndIf
	EndIf 
	
	RestArea( aArea )

	// inicia processo de limpeza da memória
	aSize(aArea,0)
	aSize(aZQEItm,0)
	aSize(aZQGItm,0)

	aArea	:= NIL
	aZQEItm	:= NIL
	aZQGItm	:= NIL

Return lRet

Static Function TSetCliCNC( cContra, cRevisa )
	Local lRet		:= .T.
	Local lInadim 	:= .F.
	Local lAtende	:= .T.
	Local nPosPri	:= 0 
	Local nInd		:= 0
	Local nTotReg	:= 0
	Local aAux		:= {}
	Local aClientes	:= {}
	Local aRetCli	:= {}
	Local cQuery	:= Nil
	Local cCliPri 	:= Nil
	Local cLojPri 	:= Nil
	Local cTipCli	:= Nil
	Local cCliCol	:= Nil 
	Local cLojCol	:= Nil 
	Local cCliente 	:= Nil 
	Local cLojaCli  := Nil
	Local cInadim	:= Nil
	Local cAtende	:= Nil
	Local cEmpIna	:= Nil
	Local cFilIna	:= Nil
	Local cCNCAlias := GetNextAlias()
	Local __lCliExt	:=  .F.
	Local cLgMsg    := ""
	Local lReguaCobr:= (GetMv( 'TI_ZQEINAD',,'A' ) == "R") 
	
	dbSelectArea( 'CNC' )
	CNC->( dbSetOrder( 5 ) )//CNC_FILIAL+CNC_NUMERO+CNC_REVISA+CNC_TIPCLI+CNC_CLIENT+CNC_LOJACL

//// Inicio da alteração ref.a clientes com rateio > 0 - ticket 474865 e 541149
////cQuery := " SELECT DISTINCT CNC_NUMERO, CNC_REVISA, CNC_CLIENT, CNC_LOJACL, CNC_TIPCLI, CNC_INADIM, CNC_ATENDE, CNC_DTATUA, CNC_GRPEMP, CNC_UNINEG "+CRLF
    cQuery := " SELECT DISTINCT CNC.CNC_NUMERO, CNC.CNC_REVISA, CNC.CNC_CLIENT, CNC.CNC_LOJACL, CNC.CNC_TIPCLI, CNC.CNC_INADIM, "+CRLF
    cQuery += "  ( SELECT ( CASE WHEN EXISTS( SELECT DISTINCT 'X' EXISTE" + CRLF  
    cQuery += "  										  FROM "+ RetSQLName( 'CNC' ) +" CNCX" + CRLF 
    cQuery += "                                           INNER JOIN "+RetSqlName("CN9")+" CN9 ON ( CN9.CN9_FILIAL = '"+xFilial("CN9")+"' AND" + CRLF
    cQuery += "  											CN9.CN9_NUMERO = CNCX.CNC_NUMERO AND" + CRLF
    cQuery += "												CN9.CN9_REVISA = CNCX.CNC_REVISA AND" + CRLF
    cQuery += "												CN9.CN9_SITUAC = '05' AND CN9.D_E_L_E_T_ = '"+ Space( 1 ) +"' )" + CRLF 
    cQuery += "                WHERE CNCX.CNC_FILIAL = '"+ xFilial( 'CNC' ) +"' AND "+CRLF
    cQuery += " 					 CNCX.CNC_NUMERO = CNC.CNC_NUMERO AND "+CRLF
    cQuery += " 					 CNCX.CNC_REVISA = CNC.CNC_REVISA AND "+CRLF
    cQuery += "                      ( 	CNCX.CNC_TIPCLI IN ('01','03', '04') OR "+CRLF
    cQuery += "   					EXISTS( SELECT PHG.PHG_CLIENT  "+CRLF
    cQuery += "   							FROM "+RetSqlName("PHG")+" PHG  "+CRLF
//// Inicio do Rateio de Recorrente - 474865
    cQuery += " 							INNER JOIN "+RetSqlName("CNB")+" CNB  "+CRLF
    cQuery += " 								ON (CNB.CNB_FILIAL = PHG.PHG_FILIAL AND CNB.CNB_CONTRA = PHG.PHG_CONTRA AND "+CRLF 
    cQuery += " 								CNB.CNB_REVISA = PHG.PHG_REVISA AND CNB.CNB_NUMERO = PHG.PHG_NUMERO AND "+CRLF 
//// Relacionamento entre CNB e PHG deve ser feito também pelo Numero do Item - ticket 541129 / 1-Recorrente e Não Cancelados - Ticket 474865
    cQuery += "				    CNB.CNB_PRODUT = PHG.PHG_PRODUT AND CNB.CNB_ITEM = PHG.PHG_ITEM AND "+CRLF
    cQuery += "				    CNB.CNB_TIPREC = '1' AND CNB.CNB_SITUAC NOT IN ('C','S') AND CNB.D_E_L_E_T_ = ' ' ) "+CRLF 
//// Final do Rateio de Recorrente - 474865
    cQuery += "   							WHERE 	PHG.PHG_FILIAL = '"+xFilial("PHG")+"' AND  "+CRLF
    cQuery += "    									PHG.PHG_CONTRA = CNCX.CNC_NUMERO AND "+CRLF 
    cQuery += "   									PHG.PHG_REVISA = CNCX.CNC_REVISA AND "+CRLF
    cQuery += " 									PHG.PHG_PERRAT > 0 AND "+CRLF						
    cQuery += "    									PHG.D_E_L_E_T_ = '  ' )) AND "+CRLF
    cQuery += " 									CNCX.CNC_ATENDE = 'N' AND CNCX.D_E_L_E_T_ = '"+ Space( 1 ) +"' )"+CRLF 
    cQuery += " 						THEN 'N' ELSE 'S' END ) ATENDE FROM DUAL ), "+CRLF
    cQuery += " 						CNC.CNC_DTATUA, CNC.CNC_GRPEMP, CNC.CNC_UNINEG "+CRLF
	cQuery += " FROM "+ RetSQLName( 'CNC' ) +" CNC "+CRLF
	cQuery += " WHERE CNC.CNC_FILIAL IS NOT NULL "+CRLF
	cQuery += " 	AND CNC.CNC_NUMERO = '"+ cContra +"' "+CRLF
	cQuery += " 	AND CNC.CNC_REVISA = '"+ cRevisa +"' "+CRLF
	cQuery += " 	AND CNC.D_E_L_E_T_ = ' ' "+CRLF
	cQuery += " 	AND (	CNC.CNC_TIPCLI IN ('01','03', '04') OR "+CRLF
	cQuery += " 	EXISTS( SELECT PHG.PHG_CLIENT FROM "+RetSqlName("PHG")+" PHG  "+CRLF
//// Inicio do Rateio de Recorrente e não Cancelado - Ticket 474865- 474865
    cQuery += "				INNER JOIN "+RetSqlName("CNB")+" CNB  "+CRLF
    cQuery += "				ON (CNB.CNB_FILIAL = PHG.PHG_FILIAL AND CNB.CNB_CONTRA = PHG.PHG_CONTRA AND "+CRLF 
    cQuery += "					CNB.CNB_REVISA = PHG.PHG_REVISA AND CNB.CNB_NUMERO = PHG.PHG_NUMERO AND "+CRLF 
//// Relacionamento entre CNB e PHG deve ser feito também pelo Numero do Item - ticket 541129 / 1-Recorrente e Não Cancelados - Ticket 474865
    cQuery += " 							    CNB.CNB_PRODUT = PHG.PHG_PRODUT AND CNB.CNB_ITEM = PHG.PHG_ITEM AND "+CRLF 
    cQuery += " 							    CNB.CNB_TIPREC = '1'  AND CNB.CNB_SITUAC NOT IN ('C','S') AND CNB.D_E_L_E_T_ = ' ' ) "+CRLF 
//// Final do Rateio de Recorrente - 474865 
    cQuery += " 	WHERE PHG.PHG_FILIAL = '           ' "+CRLF
	cQuery += " 			 AND PHG.PHG_CONTRA = CNC.CNC_NUMERO "+CRLF
	cQuery += " 			 AND PHG.PHG_REVISA = CNC.CNC_REVISA "+CRLF
	cQuery += " 		   	 AND PHG.PHG_CLIENT = CNC.CNC_CLIENT "+CRLF
	cQuery += "			     AND PHG.PHG_LOJA   = CNC.CNC_LOJACL "+CRLF 
	cQuery += " 			 AND PHG.PHG_PERRAT > 0 AND  PHG.D_E_L_E_T_ = '  ' )) "+CRLF
//// Final do Rateio de Recorrente e não Cancelado - Ticket 474865- 474865	
	cQuery += " GROUP BY CNC.CNC_NUMERO, CNC.CNC_REVISA, CNC.CNC_CLIENT, CNC.CNC_LOJACL, CNC.CNC_TIPCLI, CNC.CNC_INADIM, CNC.CNC_ATENDE, CNC.CNC_DTATUA, CNC.CNC_GRPEMP, CNC.CNC_UNINEG"+CRLF
	cQuery += " ORDER BY CNC.CNC_TIPCLI, CNC.CNC_CLIENT, CNC.CNC_LOJACL"

	dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cCNCAlias, .T., .F. )
	dbSelectArea( cCNCAlias )
	AEval( CNC->( dbStruct() ), { | x | IIf( !( x[2] == 'C' ), TcSetField( ( cCNCAlias ), AllTrim( x[ 1 ] ), x[ 2 ], x[ 3 ], x[ 4 ] ) , Nil ) } )
	( cCNCAlias )->( dbEval( { || nTotReg ++ },,{ || !Eof() } ) )

	If nTotReg == 0
		lRet := .F.
	Else
		( cCNCAlias )->( dbGoTop() )
		While !( cCNCAlias )->( Eof() )
			aAux := {}
			For nInd := 1 To ( cCNCAlias )->( FCount() )
				Aadd( aAux, { ( cCNCAlias )->( FieldName( nInd ) ), ( cCNCAlias )->( FieldGet( nInd ) ) } )
			Next nInd
			Aadd( aClientes, AClone( aAux ) )

			( cCNCAlias )->( dbSkip() )
		EndDo

		For nInd := 1 To Len( aClientes )
			nPosPri	:= TGetPos( aClientes, 'CNC_TIPCLI', '01' )
			nPosPri	:= IIf( nPosPri == 0, 1, nPosPri )
			cCliPri := TGetVet( aClientes[ nPosPri ], 'CNC_CLIENT' )
			cLojPri := TGetVet( aClientes[ nPosPri ], 'CNC_LOJACL' )

			//Atualiza os Clientes
			cTipCli		:= TGetVet( aClientes[ nInd ], 'CNC_TIPCLI' )
			cCliCol		:= IIf( cTipCli == '03', TGetVet( aClientes[ nInd ], 'CNC_CLIENT' ), '' ) 
			cLojCol		:= IIf( cTipCli == '03', TGetVet( aClientes[ nInd ], 'CNC_LOJACL' ), '' ) 
			cCliente 	:= IIf( cTipCli == '01' .Or. cTipCli == '04', TGetVet( aClientes[ nInd ], 'CNC_CLIENT' ), cCliPri ) 
			cLojaCli  	:= IIf( cTipCli == '01' .Or. cTipCli == '04', TGetVet( aClientes[ nInd ], 'CNC_LOJACL' ), cLojPri )
//// Inicio do tratamento de Inadimplencia de Ciente Internacional da Totvs - ticket 542659			
			__lCliExt 	:= .F.
			__lCliExt 	:= TGetClEx( IIf( Empty( cCliCol ), cCliente, cCliCol ), IIf( Empty( cLojCol ), cLojaCli, cLojCol ) )
			If !__lCliExt 
				aRetCli	 	:= TGetInad( IIf( Empty( cCliCol ), cCliente, cCliCol ), IIf( Empty( cLojCol ), cLojaCli, cLojCol ) )
			Else
				aRetCli	 	:= {}
			EndIf				
//// Final do tratamento de Inadimplencia de Ciente Internacional da Totvs - ticket 542659			
			If Len( aRetCli ) == 0
				lInadim := .F.
				lAtende	:= .T.
				cInadim	:= IIf( lInadim, 'S', 'N' )
				cAtende	:= IIf( lAtende, 'S', 'N' )
				cEmpIna	:= CriaVar( 'CNC_GRPEMP', .F. )
				cFilIna	:= CriaVar( 'CNC_UNINEG', .F. )
			Else
				lInadim := .T.
				lAtende	:= Atende( IIf( Empty( cCliCol ), cCliente, cCliCol ), IIf( Empty( cLojCol ), cLojaCli, cLojCol ) , lInadim )
				cInadim	:= IIf( lInadim, 'S', 'N' )
				cAtende	:= IIf( lAtende, 'S', 'N' )
				cEmpIna	:= TGetVet( aRetCli[ 1 ], 'E1_MSEMP' )
				cFilIna	:= TGetVet( aRetCli[ 1 ], 'E1_FILIAL' )					
			EndIf

			If !CNC->( dbSeek( FWxFilial( 'CNC' ) + TGetVet( aClientes[ nInd ], 'CNC_NUMERO' ) + TGetVet( aClientes[ nInd ], 'CNC_REVISA' ) + cTipCli + IIf( Empty( cCliCol ), cCliente, cCliCol ) + IIf( Empty( cLojCol ), cLojaCli, cLojCol )  ) )//  CNC_NUMERO+CNC_REVISA+CNC_TIPCLI+CNC_CLIENT+CNC_LOJACL
				Loop
			Else
				cLgMsg := "TSetCliCNC - At De " + AllTrim( CNC->CNC_ATENDE ) + " Para " + AllTrim( cAtende ) + ",Inam De " + AllTrim( CNC->CNC_INADIM ) + " Para " + AllTrim( cInadim )
				
				RecLock( 'CNC', .F. )
				CNC->CNC_ATENDE	:= cAtende
				CNC->CNC_INADIM	:= cInadim
				CNC->CNC_GRPEMP	:= cEmpIna
				CNC->CNC_UNINEG	:= cFilIna
				CNC->CNC_DTATUA	:= MsDate()
				
				If lReguaCobr//somente brasil(o qual possui regua) tem o campo abaixo na estrutura da tabela
					CNC->CNC_XOBS := ''
				EndIf
				
				CNC->( MsUnLock() )
				
			EndIf

		Next nInd
	EndIf

	IIf( Select( cCNCAlias ) > 0, ( cCNCAlias )->( dbCloseArea() ), Nil )
Return lRet




Static Function ZQEGetItTHr( cContra, cRevisa )
	Local aAux			:= {}
	Local aRet			:= {}
	Local nInd			:= 0
	Local nTotReg		:= 0
	Local cQuery		:= Nil
	Local cZQEAlias := GetNextAlias()

	Default cRevisa := ''

	dbSelectArea( 'ZQE' )
	ZQE->( dbSetOrder( 1 ) )

	cQuery := " SELECT DISTINCT ZQE.ZQE_CODCLI, ZQE.ZQE_LJCLI, ZQE.ZQE_CONTR, ZQE.ZQE_SERVIC, ZQE.ZQE_EMPFIL, ZQE.ZQE_ATENDE, ZQE.ZQE_INADIM, ZQE.ZQE_TOTHR "+CRLF
	cQuery += " FROM "+ RetSQLName( 'ZQE' ) +" ZQE "+CRLF
	cQuery += " WHERE ZQE.ZQE_FILIAL = '"+ FWxFilial( 'ZQE' ) +"' "+CRLF
	cQuery += " 	AND ZQE.ZQE_CONTR = '"+ cContra +"' "+CRLF

	If !Empty( cRevisa )
		cQuery += " 	AND ZQE.ZQE_VERSAO = '"+ cRevisa +"' "+CRLF
	EndIf

	cQuery += " 	AND ZQE.D_E_L_E_T_ = '"+ Space( 1 ) +"' "+CRLF
	cQuery += " 	AND ZQE.ZQE_TOTHR > 0 "+CRLF

	cQuery += " GROUP BY ZQE.ZQE_CODCLI, ZQE.ZQE_LJCLI, ZQE.ZQE_CONTR, ZQE.ZQE_SERVIC, ZQE.ZQE_EMPFIL, ZQE.ZQE_ATENDE, ZQE.ZQE_INADIM, ZQE.ZQE_TOTHR "+CRLF
	cQuery += " HAVING( ZQE.ZQE_TOTHR > 0 )"+CRLF
	cQuery += " ORDER BY ZQE_CODCLI, ZQE_LJCLI, ZQE_CONTR, ZQE_SERVIC, ZQE_EMPFIL"

	dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cZQEAlias, .T., .F. )
	dbSelectArea( cZQEAlias )
	( cZQEAlias )->( dbEval( { || nTotReg ++ },,{ || !Eof() } ) )

	If nTotReg > 0

		( cZQEAlias )->( dbGoTop() )
		While ( cZQEAlias )->( !Eof() )

			aAux	:= {}
			For nInd := 1 To ( cZQEAlias )->( FCount() )
				Aadd( aAux, { ( cZQEAlias )->( FieldName( nInd ) ), ( cZQEAlias )->( FieldGet( nInd ) ) } )
			Next nInd
			Aadd( aRet, AClone( aAux ) )

			( cZQEAlias )->( dbSkip() )
		EndDo

	EndIf

	IIf( Select( cZQEAlias ) > 0, ( cZQEAlias )->( dbCloseArea() ), Nil )
Return aRet


Static Function ZQGGetDtaEq( cContra, cRevisa )
	Local aAux		:= {}
	Local aRet		:= {}
	Local nInd		:= 0
	Local nTotReg	:= 0
	Local cQuery	:= Nil
	Local cZQGAlias := GetNextAlias()

	Default cRevisa := ''

	dbSelectArea( 'CNB' )
	CNB->( dbSetOrder( 1 ) )

	cQuery := " SELECT DISTINCT CNB.CNB_CONTRA, CNB.CNB_REVISA, CNB.CNB_NUMERO, CNB.CNB_ITEM, CNB.CNB_PRODUT, CNB.CNB_SERVIC, CNB.CNB_DTINEQ, CNB.CNB_DTFIEQ, CNB.CNB_TABSER, CNB.CNB_NIVSEV "+CRLF
	cQuery += " FROM "+ RetSQLName( 'CNB' ) +" CNB "+CRLF

	cQuery += " WHERE CNB.CNB_FILIAL = '"+ FWxFilial( 'CNB' ) +"' 	"+CRLF
	cQuery += "   AND CNB.CNB_CONTRA = '"+ cContra +"' 	"+CRLF

	If !Empty( cRevisa )
		cQuery += " 	AND TRIM( CNB.CNB_REVISA ) = '"+ AllTrim( cRevisa ) +"' "+CRLF
	EndIf

	cQuery += " 	AND CNB.D_E_L_E_T_ = '"+ Space( 1 ) +"' "+CRLF
	cQuery += " GROUP BY CNB.CNB_CONTRA, CNB.CNB_REVISA, CNB.CNB_NUMERO, CNB.CNB_ITEM, CNB.CNB_PRODUT, CNB.CNB_SERVIC, CNB.CNB_DTINEQ, CNB.CNB_DTFIEQ, CNB.CNB_TABSER, CNB.CNB_NIVSEV "+CRLF
	cQuery += " HAVING( TRIM( CNB.CNB_DTINEQ ) <> ' ' OR TRIM( CNB.CNB_DTFIEQ ) <> ' ' OR TRIM( CNB.CNB_TABSER ) <> ' ' OR TRIM( CNB.CNB_NIVSEV ) <> ' ' )"+CRLF
	cQuery += " ORDER BY CNB.CNB_CONTRA, CNB.CNB_REVISA, CNB.CNB_NUMERO, CNB.CNB_ITEM "

	dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cZQGAlias, .T., .F. )
	dbSelectArea( cZQGAlias )
	( cZQGAlias )->( dbEval( { || nTotReg ++ },,{ || !Eof() } ) )

	If nTotReg > 0

		( cZQGAlias )->( dbGoTop() )
		While ( cZQGAlias )->( !Eof() )

			aAux	:= {}
			For nInd := 1 To ( cZQGAlias )->( FCount() )
				Aadd( aAux, { ( cZQGAlias )->( FieldName( nInd ) ), ( cZQGAlias )->( FieldGet( nInd ) ) } )
			Next nInd
			Aadd( aRet, AClone( aAux ) )

			( cZQGAlias )->( dbSkip() )
		EndDo

	EndIf

	IIf( Select( cZQGAlias ) > 0, ( cZQGAlias )->( dbCloseArea() ), Nil )
Return aRet

Static Function TGetVet( aVet, cCpo )
	Local nPos	:= Ascan( aVet, { | x | AllTrim( x[ 1 ] ) == cCpo } )
	Local xRet	:= Nil

	xRet := IIf( nPos > 0 , aVet[ nPos ][ 02 ], Nil ) 

Return xRet 

Static Function TSetVet( aVet, cCpo, xValor )
	Local lRet	:= Nil
	Local nPos	:= Ascan( aVet, { | x | AllTrim( x[ 1 ] ) == cCpo } )

	If nPos == 0
		lRet := .F.
	Else
		aVet[ nPos ][ 02 ] :=  xValor
		lRet := .T.
	EndIf

Return lRet


Static Function TGetPos( aVet, cCpo, xValor )
	Local nRet	:= 0
	Local nInd	:= 0
	Local xRet	:= Nil

	For nInd := 1 To Len( aVet )
		xRet := TGetVet( aVet[ nInd ], cCpo )
		nRet := IIf( AllTrim( xRet ) == AllTrim( xValor ), nInd, 0 )
		If nRet > 0
			Exit
		EndIf

	Next nInd

Return nRet

Static Function TGetInad( cCliente, cLojaCl )
	Local nInd		:= 0
	Local nTotReg	:= 0
	Local aAux		:= {}
	Local aRet		:= {}
	Local cQuery	:= ""
	Local cTabQry	:= GetNextAlias()
	////// Alterações do Denison - VirtualAge
	Local cFilxParV   := GetMv("TI_PVFILIAL", , "00001003100")//Filiais que terão clientes inadimplentes caso tenham "x" parcelas vencidas.
	Local nQtdParV 	:= GetMv("TI_PVQTPARC", , 2)				//Quantidade de parcelas vencidas.
	Local cTiposTit	:= GetMv("TI_PVTIPTIT", , "NF")			//Tipos de títulos a serem considerados.
	Local lBloqParV	:= GetMv("TI_PVLIGBQ", , .T.)			//Liga regra de inadimplência por quantidade de parcelas vencidas.
	Local nSaldoDev	:= GetMv("TI_PVSALDV", , 0)				//Valor do saldo em aberto do título para considerar a parcela devedora.
	Local nDVencPV		:= GetMv("TI_PVDVENC", , 1)				//// Qtde de Dias de Vencto para considerar parcela vencida

	// Parametro para definir qual a regra de inadimplencia será utilizada (R=Regua de Cobranca / Z=Logica atual na ZQE / A=Ambos) // jonas 18/05/2018
	// Projeto StopGo Regua de Cobranca
	If GetMv( 'TI_ZQEINAD',,'A' ) == "R" //.And. !FwIsInCallStack("U_GC004Proc")//Liberação manual de Help Desk.
		Return aRet 
	EndIf

	cQuery += " SELECT DISTINCT SE1.E1_MSEMP, SE1.E1_FILIAL, SE1.E1_CLIENTE, SE1.E1_LOJA, 'S' INADIM, SUM( SE1.E1_SALDO ) E1_SALDO " + CRLF
	cQuery += " FROM	 "+ RetSQLName( "SE1" ) +" SE1" + CRLF
//// Desprezar os titulos da filial BPO - ticket 531433
////cQuery += " WHERE SE1.E1_FILIAL IS NOT NULL "
	cQuery += " WHERE SE1.E1_FILIAL NOT IN "+ FormatIn(cFilExc,'|') +" "+CRLF
	cQuery += " 	AND SE1.E1_CLIENTE = '" + cCliente + "' AND SE1.E1_LOJA = '" + cLojaCl +"' "+CRLF
	cQuery += " 	AND  TRIM(SE1.E1_TIPO) = 'NF' " + CRLF
	cQuery += " 	AND  SE1.E1_PREFIXO BETWEEN '" + CriaVar("E1_PREFIXO", .F.) + "' AND '" + Replicate("Z", TamSx3("E1_PREFIXO")[01]) + "'" + CRLF
	cQuery += " 	AND  SE1.E1_NUM BETWEEN '" + CriaVar("E1_NUM", .F.) + "' AND '" + Replicate("Z", TamSx3("E1_NUM")[01]) + "'" + CRLF
	cQuery += " 	AND  SE1.E1_PARCELA BETWEEN '" + CriaVar("E1_PARCELA", .F.) + "' AND '" + Replicate("Z", TamSx3("E1_PARCELA")[01]) + "'" + CRLF
	cQuery += " 	AND	 SE1.E1_VENCREA < '" + DtoS( DataValidacao() ) + "'" + CRLF
	cQuery += "		AND	 SE1.D_E_L_E_T_ = ' '  " + CRLF

	cQuery += " GROUP BY SE1.E1_MSEMP, SE1.E1_FILIAL, SE1.E1_CLIENTE, SE1.E1_LOJA " + CRLF
	cQuery += " HAVING SUM( SE1.E1_SALDO ) > " + AllTrim(Str( nLimina ) ) + CRLF
	If lBloqParV
		// ---------------------------------------------------------------------
		// denison.franca | 08.11.2017
		//
		// Regra de inadimplência de títulos emitidos na filial da Virtual Age.
		//
		// Caso um ciente tenha 2 parcelas em atraso na filial da Virtual, será
		// considerado inadimplente.
		//
		// Se o cliente possuir títulos atrasados em outras filiais, será apli-
		// cada a regra de bloqueio do Select acima.
		//
		// Caso o cliente tenha títulos em diversas filiais (Virtual Age e Ou-
		// tras), a regra que for atendida primeiramente, em sua respectiva fi-
		// lial, será a responsável por tornar o cliente inadimplente.
		// ---------------------------------------------------------------------
		cQuery += " UNION " + CRLF
		cQuery += " SELECT 	SE1.E1_MSEMP, SE1.E1_FILIAL, SE1.E1_CLIENTE, SE1.E1_LOJA, 'S' INADIM, SUM( SE1.E1_SALDO ) E1_SALDO " + CRLF
		cQuery += " FROM	SE1000 SE1 " + CRLF
		cQuery += " WHERE 	E1_FILIAL IN "+ FormatIn(cFilxParV,'/') +" " + CRLF
		cQuery += " 		AND SE1.E1_CLIENTE = '" + cCliente + "' AND SE1.E1_LOJA = '" + cLojaCl +"' "+CRLF
		cQuery += " 		AND TRIM(SE1.E1_TIPO) IN "+ FormatIn(cTiposTit,'/') +" " + CRLF
		cQuery += "         AND E1_VENCREA < '"+DToS(dDataBase - nDVencPV)+"' " + CRLF
		cQuery += "         AND E1_SALDO > " + AllTrim(Str( nSaldoDev ) ) + CRLF
		cQuery += " 		AND SE1.D_E_L_E_T_ = ' ' " + CRLF
		cQuery += " GROUP BY SE1.E1_MSEMP, SE1.E1_FILIAL, SE1.E1_CLIENTE, SE1.E1_LOJA " + CRLF
		cQuery += " HAVING COUNT(*) >= "+cValToChar(nQtdParV)+" " + CRLF 
		
	EndIf

	cQuery += " ORDER BY E1_CLIENTE, E1_LOJA, E1_SALDO DESC "+CRLF
	cQuery += " FETCH FIRST 1 ROWS ONLY "

	cQuery := ChangeQuery( cQuery )
	dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cTabQry, .T., .T.)
	TcSetField( ( cTabQry ), 'E1_SALDO', TamSx3( 'E1_SALDO' )[ 3 ], TamSx3( 'E1_SALDO' )[ 1 ], TamSx3( 'E1_SALDO' )[ 2 ]  )

	( cTabQry )->( dbEval( { || nTotReg ++ },,{ || !Eof() } ) )

	If nTotReg == 0
		aRet := {}
	Else
		( cTabQry )->( dbGoTop() )
		While !( cTabQry )->( Eof() )
			aAux := {}
			For nInd := 1 To ( cTabQry )->( FCount() )
				Aadd( aAux, { ( cTabQry )->( FieldName( nInd ) ) , ( cTabQry )->( FieldGet( nInd ) ) } )
			Next nInd 
			Aadd( aRet, AClone( aAux ) )

			( cTabQry )->( dbSkip() )
		EndDo
	EndIf

	IIf( Select( cTabQry ) > 0, ( cTabQry )->( dbCloseArea() ), Nil )
Return aRet 

Static Function Atende( cCliente, cLojaCl, lInadim )
	Local aRet		:= {}	
	Local lRetorno 	:= If( lInadim, .F., .T.)

	dbSelectArea( "SA1" )
	SA1->( dbSetOrder( 1 ) )		//A1_FILIAL, A1_COD, A1_LOJA
	If !lInadim
		lRetorno := .T. //Inadimplente falso, deve retornar true.
	Else
		If SA1->( dbSeek( FWxFilial("SA1") +  cCliente + cLojaCl ) )
			aRet := U_TFINA007(  cCliente, cLojaCl )			
			lRetorno := aScan(aRet,{|x|x[1]== .T.}) > 0			
		EndIf
	EndIf

Return lRetorno


Static Function VerProdAnt(cProduto)
	Local cRetorno		:= ''
	Local cProdMDI	   	:= GetMv( 'TDI_MIDBY' ,, 'MB.SMS' )
	Local cEmpRM       	:= GetMv( 'TI_EMPRM'  ,, '0000001001200' )
	Local cEmpDTS      	:= GetMv( 'TI_EMPDTS' ,, '0000001001700' ) //"0011,1801,1901"
	Local cEmpLogix		:= GetMv( 'TI_EMPLOGO',, '0000001000600' )
	Local cEmpProtheus 	:= GetMv( 'TI_EMPPROT',, '0000001000100|0000102000100|0000202000100|0000302000100|0220001155100|0110001000100|0000801000100' ) //"0001,0101,0201,0301,0601,0701,2701"
	Local cProdFIRST   	:= GetMv( 'TDI_FIRST' ,, '0114101001-0|0114101003-8|1114101001-0|1114101004-8|000000000005241|000000000005242|000000000005243' )

	//TODO - VERIFICAR DEPARA DAS EMPRESAS ANTIGAS PARA AS NOVAS
	Do Case
		Case cEmpAnt + cFilAnt $ cEmpProtheus
		cRetorno := "02"

		Case cEmpAnt + cFilAnt $ cEmpRM
		cRetorno := "03"

		Case cEmpAnt + cFilAnt $ cEmpDTS
		cRetorno := "06"

		Case cEmpAnt + cFilAnt $ cEmpLogix
		cRetorno := "01"

		Case Alltrim( cProduto ) $ cProdFIRST
		cRetorno := "FS"

		Case Left(cProduto, 6) == cProdMDI
		cRetorno := "VT"

		OtherWise
		cRetorno := 'SR'

	EndCase

Return cRetorno


Static Function DataValidacao()
	Local dRet := dDatabase - nDiasAbt
Return dRet


Static Function TdiZQEPut( cContrato, cRevisa, cExeReceiv, dDtBase, cEmpFil, aProdTotvs, aProdLight, aProdFull, lSerie1, cGrpEmp  )
	Local lRet			:= .T.
	Local lLight		:= .F.
	Local aArea			:= GetArea()
	Local nY,nX			:= 0
	Local nTotReg		:= 0
	Local nAscan 		:= 0
	Local aProdZQE  	:= {}
	Local cPrdRet		:= ''
	Local cQuery		:= Nil
	Local cMigrac 		:= Nil
	Local cCliente 		:= Nil
	Local cLoja    		:= Nil
	Local cVerOld		:= Nil
	Local cVerNew		:= Nil
	Local cProduto 		:= Nil
	Local cGeSla		:= Nil
	Local cGrvProd 		:= Nil
	Local cSla 			:= Nil
	Local cProdut 		:= Nil
	Local cServico 		:= Nil
	Local ni            := 0
	Local aPrg1Descart  := {}
	Local aPrg2Descart  := {}  
	Local cSD6Alias	:= GetNextAlias()
	Local lExecuta  := .T.  
	Local aAuxProd	:= {}
	Local aAux    	:= {}
	Local aClientes	:= {}
	Local aFiltCli  := {}
	Local nIndFilt  := 0 
	Local cXCCCVtex := SuperGetMV("TI_CCCVTEX",.T.,"700999999")
	Local lCobrRegua:= (GetMv('TI_ZQEINAD',,'A') == "R")  
	Local lTF11ZQE  :=  GetMv('TI_TF11ZQE', , .T.)   
	Local lTF11ZEN  :=  GetMv('TI_TF11ZEN', , .T.)
	Local lCCCYLR   :=  GetMv("TI_CCCYLR", , .T.)
	Local cCDUGrup  := GetMV("TI_CDU2GRP",.T.,"XXXX")
	Local lDiaAnt 	:= GetMV("TI_ZANAD",,.T.) 	
	Local cGrFiltr
	
	//----- Grupo Cliente Intera ------------------------------------------
	Local cGRPINT2  := GetMv('TI_GRPINT2',,"17T6")
	Local cGRPINT   := GetMv('TI_GRPINT' ,,"17T7/17T8/17T9")
	
	Local cGRPISMB  := GetMv('TI_GRPISMB',,'Z7TF')
	Local cGrpIts	:= GetMv( 'TI_GRPITS',,'17T1|17T2|17T7|17T8|17T9' )	

	Local nTotHr    := 0
    Local nZQERec   := 0 

	Default lSerie1	:= .F.
	Default aProdTotvs	:= {}
	Default aProdLight	:= {}
	Default aProdFull	:= {}
	Default dDtBase	:= dDataBase
	Default cGrpEmp	:= cEmpAnt
	Default cEmpFil	:= cFilAnt
	Default cExeReceiv := "" 
	cGrFiltr := cGrpIts + "|" + cGRPISMB + "|" + cGRPINT  + "|" +  cGRPINT2
	
	//observei que em cada parâmetro usaram operadores distintos, apenas efetuo saneamento por precaução
	cGrFiltr := strtran(cGrFiltr, "/", "|")
	cGrFiltr := strtran(cGrFiltr, "\", "|")
	cGrFiltr := strtran(cGrFiltr, "||", "|")
	
	cQuery := " SELECT DISTINCT CNC.CNC_CLIENT, CNC.CNC_LOJACL, CNC.CNC_TIPCLI, CNC.CNC_ATENDE, CNC.CNC_INADIM, CNC.CNC_GRPEMP, CNC.CNC_UNINEG, CNC.CNC_DTATUA, "+CRLF
	cQuery += "					CNB.CNB_CONTRA, CNB.CNB_REVISA, CNB.CNB_NUMERO, CNB.CNB_PRODUT, CNB.CNB_VIGFIM, "+CRLF
	cQuery += "					SB1.B1_GRUPO, SB1.B1_DESC, SBM.BM_XSERIE, SBM.BM_XLINREC, SBM.BM_XCCC, SBM.BM_XCLVL, CNA.CNA_PICPAD, "+CRLF
	cQuery += "	 POV.POV_PRODUT, "+CRLF
	
	cQuery += "	( CASE WHEN EXISTS( SELECT DISTINCT 1 EXIST FROM "+ RetSQLName( 'ZQE' ) +" ZQE WHERE ZQE.ZQE_FILIAL = '"+ FWxFilial( 'ZQE' ) +"' AND ZQE.ZQE_CONTR = CNB.CNB_CONTRA AND ZQE.D_E_L_E_T_ = ' '  ) "+CRLF
	cQuery += "		   THEN( SELECT DISTINCT MAX( ZQE.ZQE_VERSAO ) VERSAO FROM "+ RetSQLName( 'ZQE' ) +" ZQE WHERE ZQE.ZQE_FILIAL = '"+ FWxFilial( 'ZQE' ) +"' AND ZQE.ZQE_CONTR = CNB.CNB_CONTRA AND ZQE.D_E_L_E_T_ = ' ' GROUP BY ZQE.ZQE_VERSAO FETCH FIRST 1 ROWS ONLY ) "+CRLF	
	cQuery += "	  ELSE CNB.CNB_REVISA 	"+CRLF
	cQuery += "	  END ) ZQE_VERSAO, 	"+CRLF
	

	//cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrpSla, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) SLA, "+CRLF //tibackop-1176
	cQuery += "				    ( CASE WHEN( SB1.B1_XFAMILI IN "+ FormatIn( cGrpSla, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) SLA, "+CRLF
	cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrpGEs, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) GES, "+CRLF
	cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrpBso, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) BSO, "+CRLF
	
	cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrpAMS, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) AMS, "+CRLF
	
	cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrpBcs, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) BCS, "+CRLF
	cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrFiltr, '|' ) +" ) THEN 'SIM' "+CRLF
	cQuery += "					       WHEN (POV.POV_PRODUT IS NOT NULL) THEN 'SIM' " + CRLF
	cQuery += "	                           ELSE 'NAO' END ) ITA, "+CRLF

	cQuery += "					( SELECT DISTINCT SA1.A1_CGC FROM "+ RetSQLName( 'SA1' ) +" SA1 WHERE SA1.A1_FILIAL  = '"+ FWxFilial( 'SA1' ) +"' AND SA1.A1_COD = CNC.CNC_CLIENT AND SA1.A1_LOJA = CNC.CNC_LOJACL AND SA1.D_E_L_E_T_ = ' ' GROUP BY SA1.A1_CGC FETCH FIRST 1 ROWS ONLY ) CNPJ, "+CRLF

	cQuery += "					( CASE 	WHEN ( SUBSTR(SBM.BM_XCCC,1,3) = '4A0' OR SBM.BM_XLINREC IN "+ FormatIn( cGrpSaas, '|' ) +" ) THEN '200000' "+CRLF	
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '4I0' THEN '300000'	 "+CRLF
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '4B0' THEN '400000'	 "+CRLF
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '520' THEN '800000'	 "+CRLF
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '4C0' THEN 'A00000'	 "+CRLF
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '4D0' THEN 'B00000'	 "+CRLF
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '4E0' THEN 'D00000'	 "+CRLF
	cQuery += "							WHEN SBM.BM_XCCC = '" + cXCCCVtex + "' THEN '310000' "+CRLF
	cQuery += "					 ELSE '100000'  "+CRLF
	cQuery += "					 END  ) ZQE_SERVIC, "+CRLF

	cQuery += "					SUM( CNB.CNB_QUANT ) QTDLIC "+CRLF

	cQuery += "	FROM "+ RetSQLName( 'CNC' ) +" CNC "+CRLF
	cQuery += "			INNER JOIN "+ RetSQLName( 'CNB' ) +" CNB ON ( CNB.CNB_FILIAL = '"+ FWxFilial( 'CNB' ) +"' AND CNC.CNC_FILIAL = '"+ FWxFilial( 'CNC' ) +"' AND CNB.CNB_CONTRA = CNC.CNC_NUMERO AND CNB.CNB_REVISA = CNC.CNC_REVISA ) "+CRLF
	cQuery += "			INNER JOIN "+ RetSQLName( 'CNA' ) +" CNA ON ( CNA.CNA_FILIAL = '"+ FWxFilial( 'CNA' ) +"' AND CNB.CNB_FILIAL = '"+ FWxFilial( 'CNB' ) +"' AND CNA.CNA_CONTRA = CNB.CNB_CONTRA AND CNA.CNA_REVISA = CNB.CNB_REVISA AND CNA.CNA_NUMERO = CNB.CNB_NUMERO ) "+CRLF
	cQuery += "			INNER JOIN "+ RetSQLName( 'SB1' ) +" SB1 ON ( CNB.CNB_FILIAL = '"+ FWxFilial( 'CNB' ) +"' AND SB1.B1_FILIAL  = '"+ FWxFilial( 'SB1' ) +"' AND CNB.CNB_PRODUT = SB1.B1_COD ) "+CRLF
	cQuery += "			INNER JOIN "+ RetSQLName( 'SBM' ) +" SBM ON ( SBM.BM_FILIAL  = '"+ FWxFilial( 'SBM' ) +"' AND SB1.B1_FILIAL  = '"+ FWxFilial( 'SB1' ) +"' AND SBM.BM_GRUPO = SB1.B1_GRUPO ) "+CRLF
	
	cQuery += "		LEFT JOIN POV000 POV ON ( POV.POV_FILIAL  = '           ' AND POV.POV_CODAGR = '000112' AND POV.POV_CODNIV = '0304' AND POV.POV_PRODUT = SB1.B1_COD AND POV_GERFOT = '1' AND POV.D_E_L_E_T_ = ' ' ) "+CRLF
	
	cQuery += "	WHERE CNB.CNB_CONTRA = '"+ cContrato +"' "+CRLF

	If !Empty( cRevisa )
		cQuery += "	  AND CNB.CNB_REVISA = '"+ cRevisa +"' "+CRLF
	Else
		cQuery += "	  AND CNB.CNB_REVISA IN (  	SELECT MAX( CN9.CN9_REVISA ) CN9_REVISA "+CRLF 
		cQuery += "	  							FROM "+ RetSQLName( 'CN9' ) +" CN9  "+CRLF
		cQuery += "	  							WHERE CN9.CN9_FILIAL = CNB.CNB_FILIAL  "+CRLF
		cQuery += "	  								AND CN9.CN9_NUMERO = CNB.CNB_CONTRA  "+CRLF
		cQuery += "	  								AND CN9.CN9_ESPCTR = '2'  "+CRLF
		cQuery += "	  								AND SUBSTR( CN9.CN9_NUMERO, 1, 3 ) = 'CON' "+CRLF 
		cQuery += "	  								AND CN9.CN9_SITUAC = '05'  "+CRLF
		cQuery += "	  								AND CN9.CN9_REVATU = ' '  "+CRLF
		cQuery += "	  								AND CN9.D_E_L_E_T_ = ' '  ) "+CRLF
	EndIf

	cQuery += "	  AND CNB.CNB_SITUAC IN "+ FormatIn( cCtrSitu , '|' ) +" "+CRLF
	cQuery += "	  AND (CNB.CNB_TIPREC = '1' OR SB1.B1_GRUPO IN " + FormatIn( cCDUGrup, '|' ) + ") " + CRLF // 1-Recorrente OU Produto CDU
	

//// Inicio da seleção de cliente destino de faturamento com rateio ( NAO Histórico ) deve ser relacionado - ticket 541149 e que seja Recorrente - ticket 474865
	cQuery += " 	AND (	CNC.CNC_TIPCLI IN ('01','03', '04') OR 
	cQuery += " 	EXISTS( SELECT PHG.PHG_CLIENT FROM "+ RetSQLName( 'PHG' ) +" PHG  "+CRLF 
	cQuery += " 			 WHERE PHG.PHG_FILIAL = CNB.CNB_FILIAL "+CRLF
	cQuery += " 			 AND PHG.PHG_CONTRA = CNB.CNB_CONTRA "+CRLF
	cQuery += " 			 AND PHG.PHG_REVISA = CNB.CNB_REVISA "+CRLF
	cQuery += " 		   	 AND PHG.PHG_NUMERO = CNB.CNB_NUMERO "+CRLF
	cQuery += " 		     AND PHG.PHG_PRODUT = CNB.CNB_PRODUT "+CRLF
//// Relacionamento entre CNB e PHG deve ser feito também pelo Numero do Item - ticket 541129
    cQuery += " 			 AND PHG.PHG_ITEM = CNB.CNB_ITEM "+CRLF 
	cQuery += " 		   	 AND PHG.PHG_CLIENT = CNC.CNC_CLIENT "+CRLF
	cQuery += "			     AND PHG.PHG_LOJA   = CNC.CNC_LOJACL "+CRLF 
	cQuery += " 			 AND PHG.PHG_PERRAT > 0 AND  PHG.D_E_L_E_T_ = '  ' )) "+CRLF
//// Final da seleção de cliente destino de faturamento com rateio ( NAO Histórico ) deve ser relacionado - ticket 541149 e que seja Recorrente - ticket 474865
	cQuery += "	  AND CNB.D_E_L_E_T_ = ' ' "+CRLF
	cQuery += "	  AND CNC.D_E_L_E_T_ = ' ' "+CRLF
	cQuery += "	  AND SB1.D_E_L_E_T_ = ' ' "+CRLF
	cQuery += "	  AND SBM.D_E_L_E_T_ = ' ' "+CRLF
	cQuery += "	  AND CNA.D_E_L_E_T_ = ' ' "+CRLF
	
	If lSimSetPub
		cQuery += "	  AND CNB.CNB_VIGFIM <> ' ' "+CRLF
	EndIf

	cQuery += "	GROUP BY CNC.CNC_CLIENT, CNC.CNC_LOJACL, CNC.CNC_TIPCLI, CNC.CNC_ATENDE, CNC.CNC_INADIM, CNC.CNC_GRPEMP, CNC.CNC_UNINEG, CNC.CNC_DTATUA, "+CRLF
	cQuery += "	  		 CNB.CNB_CONTRA, CNB.CNB_REVISA, CNB.CNB_NUMERO, CNB.CNB_PRODUT, CNB.CNB_VIGFIM, "+CRLF
	cQuery += "	  		 SB1.B1_GRUPO,SB1.B1_XFAMILI, SB1.B1_DESC, SBM.BM_XSERIE, SBM.BM_XLINREC, SBM.BM_XCCC, SBM.BM_XCLVL, CNA.CNA_PICPAD "+CRLF //INCLUINDO SB1.B1_XFAMILI  TIBACKOP-1176
	cQuery += "	 ,POV.POV_PRODUT "+CRLF
	
	If lSimSetPub
		cQuery += "	HAVING( '" + Dtos( dDtBase ) + "' <=  last_day ( ADD_MONTHS(cnb_vigfim, 48) )   ) " + CRLF
	Else		
		cQuery += "	HAVING( CNB.CNB_VIGFIM >= '"+ Dtos( dDtBase ) +"'   ) "+CRLF
	EndIf

	cQuery += "	ORDER BY CNC_TIPCLI, CNC_CLIENT, CNC_LOJACL, CNB_CONTRA, CNB_REVISA, CNB_NUMERO  "

	dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cSD6Alias, .T., .F. )
	dbSelectArea( cSD6Alias )
	AEval( CNA->( dbStruct() ), { | x | IIf( !( x[2] == 'C' ), TcSetField( ( cSD6Alias ), AllTrim( x[ 1 ] ), x[ 2 ], x[ 3 ], x[ 4 ] ) , Nil ) } )
	AEval( CNB->( dbStruct() ), { | x | IIf( !( x[2] == 'C' ), TcSetField( ( cSD6Alias ), AllTrim( x[ 1 ] ), x[ 2 ], x[ 3 ], x[ 4 ] ) , Nil ) } )
	AEval( CNC->( dbStruct() ), { | x | IIf( !( x[2] == 'C' ), TcSetField( ( cSD6Alias ), AllTrim( x[ 1 ] ), x[ 2 ], x[ 3 ], x[ 4 ] ) , Nil ) } )

	( cSD6Alias )->( dbEval( { || nTotReg ++ },,{ || !Eof() } ) )

	If nTotReg == 0
		lRet := .F.
	Else
		( cSD6Alias )->( dbGoTop() )
		( cSD6Alias )->( dbEval( { || Aadd( aClientes, { ( cSD6Alias )->CNC_CLIENT, ( cSD6Alias )->CNC_LOJACL, ( cSD6Alias )->CNC_TIPCLI, ( cSD6Alias )->CNC_ATENDE, ( cSD6Alias )->CNC_INADIM  } ) },,{ || !Eof() }  ) )
		( cSD6Alias )->( dbGoTop() )
		While ( cSD6Alias )->( !Eof() )
			cSLA 		:= ''
			cGrvProd 	:= ''
			cMigrac 	:= "N"

			cContrato	:= ( cSD6Alias )->CNB_CONTRA
			cVerOld		:= ( cSD6Alias )->ZQE_VERSAO
			cVerNew		:= ( cSD6Alias )->CNB_REVISA
			dVigAte		:= ( cSD6Alias )->CNB_VIGFIM
			cProduto 	:= AllTrim( ( cSD6Alias )->CNB_PRODUT )

			cGES		:= AllTrim( IIf( AllTrim(( cSD6Alias )->GES) == 'SIM', 'GE', '' ) )
			cBSO		:= AllTrim( IIf( AllTrim(( cSD6Alias )->BSO) == 'SIM', 'BS', '' ) )
			cBCS		:= AllTrim( IIf( AllTrim(( cSD6Alias )->BCS) == 'SIM', 'BC', '' ) )
			cAMS		:= AllTrim( IIf( AllTrim(( cSD6Alias )->AMS) == 'SIM', 'AM', '' ) )
			cINT		:= AllTrim( IIf( AllTrim(( cSD6Alias )->ITA) == 'SIM', 'IT', '' ) )

			cCliente 	:= IIf( Ascan( aClientes, { | x | AllTrim( x[ 3 ]) == '01'  } ) > 0, aClientes[ Ascan( aClientes, { | x | AllTrim( x[ 3 ]) == '01'  } ) ][ 01 ], '' )
			cLoja    	:= IIf( Ascan( aClientes, { | x | AllTrim( x[ 3 ]) == '01'  } ) > 0, aClientes[ Ascan( aClientes, { | x | AllTrim( x[ 3 ]) == '01'  } ) ][ 02 ], '' )

			cClient3	:= IIf( ( cSD6Alias )->CNC_TIPCLI == '03', ( cSD6Alias )->CNC_CLIENT, ' ' ) 
			cLoja3		:= IIf( ( cSD6Alias )->CNC_TIPCLI == '03', ( cSD6Alias )->CNC_LOJACL, ' ' )

			cClient4	:= IIf( ( cSD6Alias )->CNC_TIPCLI == '04', ( cSD6Alias )->CNC_CLIENT, ' ' )
			cLoja4		:= IIf( ( cSD6Alias )->CNC_TIPCLI == '04', ( cSD6Alias )->CNC_LOJACL, ' ' )


			aZQEPrd		:= {}
			aZQEPrd		:= { cProduto, dVigAte, cGES , cBSO, cBCS, cINT, cAMS}
			cGrvProd 	:= TdiGetFamily( aZQEPrd, cCliente, cLoja, cEmpFil, @lLight, @cPrdRet, @aProdTotvs, @aProdLight, @aProdFull )

			cServico 	:= ( cSD6Alias )->ZQE_SERVIC
			//SAIDA 1 TIBACKOP-1176
			If AllTrim(( cSD6Alias )->SLA) == 'SIM'
				If AT('SLA',AllTrim((cSD6Alias )->B1_DESC)) > 0
					cSla := AllTrim( ( cSD6Alias )->B1_DESC)
				else
					cSla := AllTrim( ( cSD6Alias )->B1_DESC)+' SLA'
				EndIf
			else
				cSla := ''
			EndIf

			cProdut 	:= AllTrim( cGrvProd )
			nAscan 		:= Ascan( aProdZQE,{ | x | AllTrim( x[ 03 ] ) + AllTrim( x[ 04 ] ) + AllTrim( x[ 05 ] ) == AllTrim( cContrato ) + AllTrim( cVerNew ) + AllTrim( cServico ) } )

			If nAscan <> 0
				If lLight
					If !( "|02" $ aProdZQE[nAscan,6]) .and. ("|02" $ AllTrim( cGrvProd ) )
						aProdZQE[nAscan,6] := aProdZQE[nAscan,6] + "|02"
					ElseIf !( "|03" $ aProdZQE[nAscan,6]) .and. ("|03" $ AllTrim( cGrvProd ) )
						aProdZQE[nAscan,6] := aProdZQE[nAscan,6] + "|03"
					ElseIf !( "|01" $ aProdZQE[nAscan,6]) .and. ("|01" $ AllTrim( cGrvProd ) )
						aProdZQE[nAscan,6] := aProdZQE[nAscan,6] + "|01"
					ElseIf !( "|06" $ aProdZQE[nAscan,6]) .and. ("|06" $ AllTrim( cGrvProd ) )
						aProdZQE[nAscan,6] := aProdZQE[nAscan,6] + "|06"
					ElseIf !( "|FS" $ aProdZQE[nAscan,6]) .and. ("|FS" $ AllTrim( cGrvProd ) )
						aProdZQE[nAscan,6] := aProdZQE[nAscan,6] + "|FS"
					EndIf

				ElseIf !( AllTrim( cProdut ) $ AllTrim( aProdZQE[nAscan,6] ) ) .And. !Empty( cProdut )
					aProdZQE[nAscan,6] := AllTrim( aProdZQE[nAscan,6] ) + AllTrim( cProdut )
				EndIf

				If !( AllTrim( cSLA ) $ AllTrim( aProdZQE[nAscan,7] ) ) .And. !Empty( cSla )
					aProdZQE[nAscan,7] := AllTrim( aProdZQE[nAscan,7] ) + "|" + AllTrim( cSla )
				EndIf

				aProdZQE[nAscan,17] := aProdZQE[nAscan,17] + ( cSD6Alias )->QTDLIC

			Else
				Aadd( aProdZQE ,{ cCliente, cLoja, ( cSD6Alias )->CNB_CONTRA, ( cSD6Alias )->CNB_REVISA, cServico, cProdut, cSla, "N", "", "", "S", cEmpFil , cMigrac, ( cSD6Alias )->ZQE_VERSAO, cGeSla, cEmpFil, ( cSD6Alias )->QTDLIC, cGrpEmp	} )
			EndIf

			//// Quando o cliente tiver a linha de serviço 200000 (Cloud) replica-se as mesmas famílias para a linha de serviço 100000 	
			If AllTrim(cServico) == "200000"
				If Ascan( aProdZQE,{ | x | AllTrim( x[ 03 ] ) + AllTrim( x[ 04 ] ) + AllTrim( x[ 05 ] ) == AllTrim( cContrato ) + AllTrim( cVerNew ) + AllTrim( "100000" ) } ) == 0
					Aadd( aProdZQE ,{ cCliente, cLoja, ( cSD6Alias )->CNB_CONTRA, ( cSD6Alias )->CNB_REVISA, "100000", cProdut, cSla, "N", "", "", "S", cEmpFil , cMigrac, ( cSD6Alias )->ZQE_VERSAO, cGeSla, cEmpFil, ( cSD6Alias )->QTDLIC, cGrpEmp	} )
				EndIf
			EndIf

			//// Inicio da rotina para incluir um ZQE para cada cliente coligada

			If !Empty( cClient3 ) .And. !( AllTrim( cCliente ) == AllTrim( cClient3 ) )
				_nElem := Ascan( aProdZQE,{ | x | AllTrim( x[ 01 ] ) + AllTrim( x[ 02 ] )  + AllTrim( x[ 05 ] ) == AllTrim( cClient3 ) + AllTrim( cLoja3 ) + AllTrim( cServico ) } )

				//CLIENTE3
				If _nElem == 0
					Aadd( aProdZQE, { cClient3, cLoja3, ( cSD6Alias )->CNB_CONTRA, ( cSD6Alias )->CNB_REVISA, cServico, cProdut, cSla, "N", cCliente, cLoja, "S", cEmpFil, cMigrac, ( cSD6Alias )->ZQE_VERSAO, cGeSla, cEmpFil, ( cSD6Alias )->QTDLIC, cGrpEmp	} )
				Else
					aProdZQE[_nElem,6] := cProdut + "|"
				EndIf
			EndIf

			//// Inicio da rotina para incluir um ZQE para cada cliente Dest. Faturamento
			If !Empty( cClient4 ) .And. !( AllTrim( cCliente ) == AllTrim( cClient4 ) )
				_nElem := Ascan( aProdZQE,{ | x | AllTrim( x[ 01 ] ) + AllTrim( x[ 02 ] )  + AllTrim( x[ 05 ] ) == AllTrim( cClient4 ) + AllTrim( cLoja4 ) + AllTrim( cServico ) } )

				If _nElem == 0
					Aadd( aProdZQE, { cClient4, cLoja4, ( cSD6Alias )->CNB_CONTRA, ( cSD6Alias )->CNB_REVISA, cServico, cProdut, cSla, "N", cCliente, cLoja, "S", cEmpFil, cMigrac, ( cSD6Alias )->ZQE_VERSAO, cGeSla, cEmpFil, ( cSD6Alias )->QTDLIC, cGrpEmp	} )
				Else
					aProdZQE[_nElem,6] := cProdut + "|"
				EndIf

			EndIf

			( cSD6Alias )->( dbSkip() )
		EndDo

		aProdZQE := ASort( aProdZQE,,,{ | x,y | x[ 01 ]+x[ 02 ]+x[ 05 ] < y[ 01 ]+y[ 02 ]+y[ 05 ] } )

		TdiDelete( 'ZQE',, cContrato, "TdiZQEPut" )
		
		For nY := 1 To Len( aProdZQE )
			nTotHr	:= 0
			nZQERec	:= ZQEExistReg( xFilial( "ZQE" ), aProdZQE[ nY,01 ], aProdZQE[ nY,02 ], aProdZQE[ nY,03 ], aProdZQE[nY,14], aProdZQE[ nY,05 ]  )
			ZQEGetTHr( nZQERec, @nTotHr )
			ZQEDelReg( nZQERec )

			nZQERec	:= ZQEExistReg( xFilial( "ZQE" ), aProdZQE[ nY,01 ], aProdZQE[ nY,02 ], aProdZQE[ nY,03 ], aProdZQE[nY,14], aProdZQE[ nY,05 ]  )
			ZQEAtuReg( nZQERec, aProdZQE[ nY ], nTotHr, cExeReceiv )
		Next nY

		
		// Tratamento para Campo Cliente Ativo Sim / Nao no Cadastro de Clientes - Inicio
		/*
		SA1->( dbSetOrder( 1 ) )
		SA1->( dbSeek( xFilial( 'SA1' ) + cCodCli + cLojCli ) )
		RecLock( 'SA1', .F. )
		SA1->A1__SITCLI := IIf( nTotReg > 0, 'A', 'I' )
		SA1->( MsUnLock() )
		*/
	
		If Len(aClientes) > 0 
			For nX:=1 to Len(aClientes)	//observamos duplicidade de clientes nesta array em casos eventuais, removemos isto para otimizar trecho adiante
				nIndFilt := aScan(aFiltCli,{|x|  x[1] == aClientes[nX][1] .And.  x[2] == aClientes[nX][2]    }) 
				If nIndFilt == 0
					AAdd (aFiltCli, {aClientes[nX,1],aClientes[nX,2]}  )
					
				EndIf
			Next nX	
		EndIf
			
		If GetMv("TI_CCCXL", , .T.) 
			For nX:=1 to Len(aFiltCli)	
				If lCobrRegua	
					U_TFJ11ZQE(aFiltCli[nX,1],aFiltCli[nX,2],cContrato, cRevisa, lTF11ZQE, lTF11ZEN, lCobrRegua, lCCCYLR )
				Else
					U_TFJ11ZQE(aFiltCli[nX,1],aFiltCli[nX,2])
				Endif		
			Next nX												
		EndIf
		
		//Altera complemento do cliente para ser reprocessado e alterado também a organização na Zendesk
		U_TCRMJ011(cCliente)
	EndIf

	MsUnLockAll()
		
	If  Len(aClientes)>0 .And. GetMv("TI_CCCOL", , .T.) .And. lCobrRegua	
		aPrg1Descart := StrTokArr(GetMv("TI_RD1IGN", , ""),"|")
		aPrg2Descart := StrTokArr(GetMv("TI_RD2IGN", , ""),"|")
			
		If Len(aPrg1Descart) > 0
			For ni := 1 to Len(aPrg1Descart)
				If IsInCallStack(aPrg1Descart[ni])
					lExecuta := .F.
					exit
				EndIf
			Next
		Endif
			
		If lExecuta .And. Len(aPrg2Descart) > 0
			For ni := 1 to Len(aPrg2Descart)
				If IsInCallStack(aPrg2Descart[ni])
					lExecuta := .F.
					exit
				EndIf
			Next
		EndIf
			
		If lExecuta
			For nX:=1 to Len(aFiltCli) 
				If IsBlind()
					U_TFJ011RC( aFiltCli[nX,1],aFiltCli[nX,2], cContrato, cRevisa, cExeReceiv )
				Else
					FWMsgRun(,{||  U_TFJ011RC( aFiltCli[nX,1],aFiltCli[nX,2], cContrato, cRevisa, cExeReceiv )  },"Verificando Regua para o cliente " + aFiltCli[nX,1] ,"Aguarde")
				EndIf
			Next nX	
			
			If IsBlind()
				U_TFJ11COLIG (Iif (lDiaAnt, dDataBase-1, dDataBase),'',cContrato, cRevisa, cExeReceiv) 
			Else
				FWMsgRun(,{|| U_TFJ11COLIG (Iif (lDiaAnt, dDataBase-1, dDataBase),'',cContrato, cRevisa, cExeReceiv)  },"Help Desk-Clientes vinculados. Contrato: "+ Alltrim(cContrato)+"-"+AllTrim(cRevisa)  ,"Aguarde")
			EndIf
		EndIf
				
	EndIf

	IIf( Select( cSD6Alias ) > 0, ( cSD6Alias )->( dbCloseArea() ), Nil )
	RestArea( aArea )

	// inicia processo de limpeza da memória
	aSize(aArea,0)
	aSize(aProdZQE,0)
	aSize(aPrg1Descart,0)
	aSize(aPrg2Descart,0)  
	aSize(aAuxProd,0)
	aSize(aAux,0)
	aSize(aClientes,0)
	aSize(aFiltCli,0)

	aArea		  := NIL
	aProdZQE  	  := NIL
	aPrg1Descart  := NIL
	aPrg2Descart  := NIL 
	aAuxProd	  := NIL
	aAux    	  := NIL
	aClientes	  := NIL
	aFiltCli  	  := NIL

Return lRet 


Static Function TdiGetFamily( aZQEPrd, cCliente, cLojaCli, cEmpFil, lLight, cPrdRet, aProdTotvs, aProdLight, aProdFull )
	Local cRet		:= ''
	Local lCont		:= .F.
	Local nSlot		:= 0
	Local nPosProd	:= 0
	Local nPosLight	:= 0
	Local aArea		:= GetArea()
	Local cEnv		:= GetMv( 'TI_HCSRVEN',,'HOMO_SIGACLI' )
	Local oRpcSrv 	:= Nil
	Local cServer 	:= GetMv( 'TI_HCSRVIP',,'*************' )
	Local cPort   	:= GetMv( 'TI_HCSRVPT',, 6025 )
	Local cGrvProd	:= ''
	Local nX
	Local cGES
	Local cBSO
	Local cAMS
	Local cBCS
	Local cINT
	Local cMigrac
	Local nPosFull
	Local _cProdAnt
	Local nCodModulo
	Local nCount := 0

	If ( ( Len( aProdTotvs ) > 0 ) .Or. ( Len( aProdLight ) > 0 ) .Or. ( Len( aProdFull ) > 0 ) )
		lCont := .T.
	Else
		If FindFunction( 'VPEGetProds' ) .And. FindFunction( 'VPEGetLights' ) .And. FindFunction( 'VPEGetFulls' )
			aProdTotvs 	:= VPEGetProds()
			aProdLight 	:= VPEGetLights()
			aProdFull  	:= VPEGetFulls()
			lCont 		:= .T.
		Else
			While !lCont
				If nCount >= 10
					Exit
				Else
					oRpcSrv :=TRpc():New( cEnv )
					If ( oRpcSrv:Connect( cServer, cPort ) )
						lCont := .T.
						
						aProdTotvs := {}
						aProdLight := {}
						aProdFull  := {}
						aProdTotvs := oRpcSrv:CallProc('VPEGetProds'	)
						aProdLight := oRpcSrv:CallProc('VPEGetLights'	)
						aProdFull  := oRpcSrv:CallProc('VPEGetFulls'	)
						oRpcSrv:Disconnect()

					Else
						
						lCont 	:= .F.
						nCount	++
					EndIf
				EndIf
			EndDo

		EndIf
	EndIf

	cPrdRet	:= CriaVar( 'ZQE_FAMILI', .F. )
	If 	lCont
		cProduto	:= aZQEPrd[ 01 ]
		dVigAte		:= aZQEPrd[ 02 ]
		cGES		:= aZQEPrd[ 03 ]
		cBSO		:= aZQEPrd[ 04 ]
		cBCS		:= aZQEPrd[ 05 ]
		cINT		:= aZQEPrd[ 06 ]
		cAMS		:= aZQEPrd[ 07 ]
		nPosProd 	:= Ascan( aProdTotvs, { | x | x[ 5 ] == AllTrim( cProduto ) } )

		If nPosProd == 0
			nPosProd := Ascan( aProdTotvs, { | x | AllTrim( x[ 06 ] ) == AllTrim( cProduto ) } )
		EndIf

		If nPosProd == 0
			nPosProd := Ascan( aProdTotvs, { | x | AllTrim( x[ 07 ] ) == AllTrim( cProduto ) } )
		EndIf

		If nPosProd == 0
			nPosProd := Ascan( aProdTotvs, { | x | AllTrim( x[ 08 ] ) == AllTrim( cProduto ) } )
		EndIf

		lLight := .F.
		If nPosProd > 0
			nSlot := aProdTotvs[ nPosProd ][ 03 ]

			nPosLight := 0
			nPosLight := GetSlotPrd( aProdLight, nSlot )
			If nPosLight > 1
				cMigrac := "S"
				lLight 	 := .T.

				For nX := 1 To Len( aProdLight[ nPosLight ][ 03 ] )
					nCodModulo := aProdLight[ nPosLight ][ 03 ][ nX ][ 01 ]

					If nCodModulo > 0 .And. nCodModulo <= 499 .And. !("|02" $ AllTrim( cGrvProd ) )//Protheus
						cGrvProd += "|02"
					ElseIf nCodModulo > 500 .And. nCodModulo <= 999 .And. !("|03" $ AllTrim( cGrvProd ) )//Rm
						cGrvProd += "|03"
					ElseIf nCodModulo > 5000 .And. nCodModulo <= 5499 .And. !("|01" $ AllTrim( cGrvProd ) )//Logix
						cGrvProd += "|01"
					ElseIf nCodModulo > 5500 .And. nCodModulo <= 6999 .And. !("|06" $ AllTrim( cGrvProd ) )//Datasul
						cGrvProd += "|06"
					EndIf
				Next nX
			Else //VERIFICA SE PRODUTO EH FULL
				nPosFull := Ascan( aProdFull, { | x | x[ 01 ] == nSlot } )
				If  nPosFull > 1
					cMigrac := "S"
					cGrvProd += "|TF" // TF = TOTVS FULL aProdFull[nPosFull][2]
				EndIf
			EndIf
		Else //SE NAO FOR LIGHT NEM FULL BUSCA LINHA DO PRODUTO PELA EMPRESA QUE GEROU O CONTRATO
			_cProdAnt := AllTrim( fVerProdAnt( cEmpFil , cProduto ) ) //produtos antigos, busca pela empresa de faturamento para saber linha do produto
			If !( _cProdAnt $ AllTrim( cGrvProd ) )
				cGrvProd += "|" + AllTrim( _cProdAnt )
			EndIf
		EndIf

		//Se tem produto de Garantia Extendida
		If !Empty( cGES ) .And. !( cGES $ AllTrim( cGrvProd ) )
			cGrvProd += "|" + cGES
		EndIf
		//Se tem produto BSO
		If !Empty( cBSO ) .And. !( cBSO $ AllTrim( cGrvProd ) )
			cGrvProd += "|" + cBSO
		EndIf
		//Se tem produto AMS
		If !Empty( cAMS ) .And. !( cAMS $ AllTrim( cGrvProd ) )
			cGrvProd += "|" + cAMS
		EndIf
		//Se tem produto BCS
		If !Empty( cBCS ) .And. !( cBCS $ AllTrim( cGrvProd ) )
			cGrvProd += "|" + cBCS			
		EndIf
		//Se tem produto INT
		If !Empty( cINT ) .And. !( cINT $ AllTrim( cGrvProd ) )
			cGrvProd += "|" + cINT			
		EndIf

	EndIf

	If !Empty( cGrvProd ) .And. Len( AllTrim( cGrvProd ) ) > 1
		cRet 		:= IIf( !( Right( cGrvProd, 1 ) == '|' ), cGrvProd + '|', cGrvProd )
		cPrdRet	:= IIf( !( Right( cGrvProd, 1 ) == '|' ), cGrvProd + '|', cGrvProd )
	EndIf

	RestArea( aArea )
Return cRet

Static Function fVerProdAnt(cEmpFil, cProduto)
	Local cEmpProtheus := "0001,0101,0201,0301,0601,0701,2701"
	Local cEmpRM       := "0007"
	Local cEmpDTS      := "0011,1801,1901"
	Local cEmpLogix    := "0002"
	Local cRetorno 	   := "SR"
	Local cProdFIRST   := GetMV("TDI_FIRST",,"0114101001-0/0114101003-8/1114101001-0/1114101004-8/000000000005241/000000000005242/000000000005243")
	Local cEmpPMD      := "2001"
	Local cProdMDI	   := GetMV("TDI_MIDBY",,"MB.SMS")
	Local cGrpVTEX     := SuperGetMV("TI_GRPVTEX",.T.,"17TQ") 

	If cEmpFil $ cEmpProtheus
		cRetorno := "02"
	ElseIf cEmpFil $ cEmpRM
		cRetorno := "03"
	ElseIf cEmpFil $ cEmpDTS
		cRetorno := "06"
	ElseIf cEmpFil $ cEmpLogix
		cRetorno := "01"
	ElseIf cEmpFil $ cEmpPMD
		cRetorno := "PX"
	EndIf

	If Posicione("SB1",1,xFilial("SB1")+Alltrim(cProduto),"B1_GRUPO") $ cGrpVTEX
		cRetorno := "VX"
	Endif
	
	If Alltrim(cProduto) $ cProdFIRST
		cRetorno := "FS"
	Else
		If Left(cProduto,6) == cProdMDI
			cRetorno := "VT"
		EndIf
	EndIf

Return(cRetorno)

Static Function ZQEExistReg( ___Filial, ___Cliente, ___Loja, ___Numero, ___Versao, ___Servico, ___EmpFil )
	Local nReturn		:= 0
	Local cQuery		:= Nil
	Local cZQEAlias	:= GetNextAlias()

	Default ___Filial		:= Nil
	Default ___Cliente	:= Nil
	Default ___Loja     := Nil
	Default ___Numero		:= Nil
	Default ___Versao		:= Nil
	Default ___Servico	:= Nil
	Default ___EmpFil		:= Nil

	cQuery := " SELECT DISTINCT ZQE.R_E_C_N_O_ ZQERECNO "+CRLF
	cQuery += " FROM "+ RetSQLName( 'ZQE' ) +" ZQE "+CRLF
	cQuery += " WHERE "+CRLF

	If !( ValType( ___Filial ) == 'U' )
		cQuery += " 	ZQE.ZQE_FILIAL = '"+ ___Filial 	+"' "+CRLF
	EndIf

	If !( ValType( ___Cliente ) == 'U' )
		cQuery += " 	AND ZQE.ZQE_CODCLI = '"+ ___Cliente +"' "+CRLF
	EndIf

	If !( ValType( ___Loja ) == 'U' )
		cQuery += " 	AND ZQE.ZQE_LJCLI  = '"+ ___Loja    +"' "+CRLF
	EndIf

	If !( ValType( ___Numero ) == 'U' )
		cQuery += " 	AND ZQE.ZQE_CONTR  = '"+ ___Numero  +"' "+CRLF
	EndIf

	If !( ValType( ___Versao ) == 'U' )
		cQuery += " 	AND ZQE.ZQE_VERSAO = '"+ ___Versao  +"' "+CRLF
	EndIf

	If !( ValType( ___Servico ) == 'U' )
		cQuery += " 	AND ZQE.ZQE_SERVIC = '"+ ___Servico +"' "+CRLF
	EndIf

	If !( ValType( ___EmpFil ) == 'U' )
		cQuery += " 	AND ZQE.ZQE_EMPFIL = '"+ ___EmpFil  +"' "+CRLF
	EndIf

	cQuery += " 	AND ZQE.D_E_L_E_T_ = '"+ Space( 1 ) +"'	"+CRLF

	cQuery += " GROUP BY ZQE.R_E_C_N_O_ "+CRLF
	cQuery += " ORDER BY ZQERECNO 			"+CRLF
	cQuery += " FETCH FIRST 1 ROWS ONLY "

	dbUseArea( .T., __cRdd, TcGenQry( ,,cQuery ), cZQEAlias, .T., .F. )
	dbSelectArea( cZQEAlias )
	nReturn := IIf( ( cZQEAlias )->( !Eof() ), ( cZQEAlias )->ZQERECNO, nReturn )

	IIf( Select( cZQEAlias ) > 0, ( cZQEAlias )->( dbCloseArea() ), Nil )
Return nReturn

Static Function ZQEGetTHr( nZQERec, nTotHr )

	If ( nZQERec ) == 0
		nTotHr	:= 0
	Else
		ZQE->( dbGoto( nZQERec ) )
		nTotHr	:= ZQE->ZQE_TOTHR
	EndIf

Return

Static Function ZQEDelReg( nZQERec )
	Local lRet	:= .T.

	If  nZQERec == 0
		lRet	:= .F.
	ElseIf nZQERec > 0
		TdiDelete( 'ZQE', nZQERec, '', "ZQEDelReg" )
	EndIf


Return lRet

Static Function TdiDelete( cTabNam, ___Recno, cContra, cInvocFunc )
	Local lRet		:= .T.
	Local lExec		:= .F.
	Local aZqeArea	:= {}

	Default ___Recno := 0
	Default cContra	 := ''
	Default cInvocFunc := ''
	
	If SELECT("ZQE") > 0
		aZqeArea := ZQE->(GetArea())
	EndIf
	
	DbSelectArea("ZQE")
	DbSetOrder(2) //ZQE_FILIAL+ZQE_CONTR
	
	Do Case
		Case Empty( cContra ) .And. ___Recno > 0
			ZQE->(DbGoto(___Recno))
			If ZQE->(!EOF())
			
				If RecLock("ZQE",.F.)
					ZQE->(DbDelete())
					ZQE->(MsUnLock())
				EndIf
			EndIf
			lExec := .T.
		Case !( Empty( cContra ) )
			If ZQE->(DbSeek(xFilial("ZQE") + cContra))
				Do While ZQE->(!EOF()) .And. ZQE->ZQE_CONTR == cContra
					
					If RecLock("ZQE",.F.)
						ZQE->(DbDelete())
						ZQE->(MsUnLock())
					EndIf
					ZQE->(DbSkip())
				End Do
			EndIf
			lExec := .T.
		OtherWise
			lExec := .F.
	EndCase
	
	/*
	cQry := " DELETE FROM "+ RetSQLName( cTabNam ) +" "+CRLF 
	cQry += " WHERE ZQE_FILIAL = '"+ FWxFilial( 'ZQE' ) +"' "+CRLF 

	Do Case
		Case Empty( cContra ) .And. ___Recno > 0 
		cQry += " AND R_E_C_N_O_ = "+ cValToChar( ___Recno ) +" "+CRLF
		lExec := .T.
		Case !( Empty( cContra ) ) .And. ___Recno == 0
		cQry += " AND ZQE_CONTR = '"+ cContra +"' "+CRLF
		lExec := .T.
		Case !( Empty( cContra ) ) .And. ___Recno > 0
		cQry += " AND ZQE_CONTR = '"+ cContra +"' "+CRLF
		lExec := .T.
		OtherWise
		lExec := .F.
	EndCase
	cQry += " AND D_E_L_E_T_ = ' ' "

	If lExec
		If TcSQLExec( cQry ) # 0
			lRet := .F.
		EndIf
	EndIf

	TcReFresh( cTabNam )
	*/
	If Len(aZqeArea) > 0
		ZQE->(RestArea(aZqeArea))
	EndIf

	aSize(aZqeArea,0)
	aZqeArea := NIL
	
Return lRet

Static Function ZQEAtuReg( nZQERec, aProdZQE,nTotHr, cExeReceiv)
	Local lRet			:= .T.
	Local cCtrInadi	:= Nil
	Local nZQEVlTot	:= TdiAtuzSx5( aProdZQE[ 01 ] )
	Local cCtrAtend	:= TdiGetAtend( aProdZQE[ 03 ], aProdZQE[ 04 ] )
	Local aCtrInadi	:= TdiGetInadi( aProdZQE[ 03 ], aProdZQE[ 04 ], aProdZQE[ 01 ], aProdZQE[ 02 ] )
	Local cLogMsg   := "ZQEAtuReg," 
	Local lReguaCobr:= (GetMv( 'TI_ZQEINAD',,'A' ) == "R") 
	Local lZqeOffLine := Iif( cEmpAnt $ GetMV("TI_ZQEOFFL",,"ND"), .T., .F.)

	cCtrInadi	:= IIf( Len( aCtrInadi ) > 0, 'S', 'N' )

	If nZQERec == 0
		//ZQE->( dbAppend( .T. ) )
		
		If RecLock("ZQE",.T.)
			cLogMsg += "Insere At. " + AllTrim( cCtrAtend ) + ",Inadim  " + AllTrim( cCtrInadi )
			
			ZQE->ZQE_FILIAL	 	:= xFilial( 'ZQE' )
			ZQE->ZQE_CODCLI	 	:= aProdZQE[ 01 ]
			ZQE->ZQE_LJCLI 	 	:= aProdZQE[ 02 ]
			ZQE->ZQE_CONTR   	:= aProdZQE[ 03 ]
			ZQE->ZQE_VERSAO  	:= aProdZQE[ 04 ]
			ZQE->ZQE_SERVIC  	:= aProdZQE[ 05 ]
			ZQE->ZQE_FAMILI  	:= aProdZQE[ 06 ]
			ZQE->ZQE_SLA 	 		:= aProdZQE[ 07 ]
			ZQE->ZQE_INADIM  	:= cCtrInadi //___Inadim //aProdZQE[ 08 ]
			ZQE->ZQE_CLICOL  	:= aProdZQE[ 09 ]
			ZQE->ZQE_LJCOL   	:= aProdZQE[ 10 ]
			ZQE->ZQE_ATENDE  	:= cCtrAtend //aProdZQE[ 11 ]
			ZQE->ZQE_EMPFIL  	:= aProdZQE[ 12 ]
			ZQE->ZQE_MIGRAC  	:= aProdZQE[ 13 ]
			ZQE->ZQE_TOTHR   	:= nTotHr
			ZQE->ZQE_VLRHR   	:= nZQEVlTot
			ZQE->ZQE_USERGA		:= "Append"
			ZQE->ZQE_CLICPF		:= Posicione( 'SA1', 1, FWxFilial( 'SA1' ) + aProdZQE[ 01 ] + aProdZQE[ 02 ], 'A1_CGC' ) 
			ZQE->ZQE_GRPEMP		:= aProdZQE[ 18 ]
			
			ZQE->ZQE_DTATU	:= MsDate()
			ZQE->ZQE_HRATU	:=	Time()
			ZQE->ZQE_ROTINA	:= "Ação Manual."

			If !empty(cExeReceiv)
				ZQE->ZQE_ROTINA	:= cExeReceiv 
			EndIf

			If IsInCallStack("U_GCVA102G") .or. IsInCallStack("U_GCVA102HL")
				ZQE->ZQE_ROTINA := "Job Inicio do Mes(GCT)"
			Endif
			If IsInCallStack("U_TGC001CA") .OR.  IsInCallStack("U_TGC001AP") .OR. IsInCallStack("U_TGC001CI") .OR. IsInCallStack("U_TGC001ST") .OR. IsInCallStack("U_TGC001TR")
				ZQE->ZQE_ROTINA	:= ""
			EndIf
			
			If IsInCallStack("U_TGC001CA")
				ZQE->ZQE_ROTINA := Alltrim(ZQE->ZQE_ROTINA) +  " Alt.Contrato" 
			EndIf	
	
			If IsInCallStack("U_TGC001AP")
				ZQE->ZQE_ROTINA := Alltrim(ZQE->ZQE_ROTINA) +  " Aprv.Revisão" 
			EndIf	
	
			If IsInCallStack("U_TGC001CI")
				ZQE->ZQE_ROTINA := Alltrim(ZQE->ZQE_ROTINA) +  " Inc.Contrato" 
			EndIf	
	
			If IsInCallStack("U_TGC001ST")
				ZQE->ZQE_ROTINA := Alltrim(ZQE->ZQE_ROTINA) +  " Sit.Contrato" 
			EndIf	
	
			If IsInCallStack("U_TGC001TR")
				ZQE->ZQE_ROTINA := Alltrim(ZQE->ZQE_ROTINA) +  " Tranf.Contrato" 
			EndIf	
	
			If lZqeOffLine .And. IsInCallStack("U_JOBOFFLINE")
				ZQE->ZQE_ROTINA := PU3->PU3_ROTINA
			EndIf
			//-|-//
			//-|-//
	
			If ZQE->( FieldPos( 'ZQE_SERIE' ) ) > 0
				ZQE->ZQE_SERIE	:= TdiGetSerie( aProdZQE[ 03 ], aProdZQE[ 04 ] )
			EndIf
	
			If ZQE->( FieldPos( 'ZQE_QTDLIC' ) ) > 0
				//Verifica se o campo de quantidade de licenças comporta a quantidade que está sendo passado
				If ZQE->( dbStruct()[aScan(ZQE->( dbStruct() ),{|x| x[1] == "ZQE_QTDLIC"})][3]) > 4;
				.AND. aProdZQE[ 17 ] > 9999
					ZQE->ZQE_QTDLIC	:= 9999
				Else
					ZQE->ZQE_QTDLIC	:= aProdZQE[ 17 ]
				EndIf
			EndIf
	
			//Processo para garantir que o registro será enviado para réplica de dados
			If ZQE->( FieldPos( 'ZQE_MSEXP' ) ) > 0
				ZQE->ZQE_MSEXP := Space(TamSX3("ZQE_MSEXP")[1])
			EndIf
	
			ZQE->( MsUnLock() )
			
		EndIf
	Else

		ZQE->( dbGoto( nZQERec ) )
		If !Empty( aProdZQE[ 06 ] ) .And. Right( AllTrim( aProdZQE[ 06 ] ) , 1 ) <> '|'
			aProdZQE[ 06 ] := AllTrim( aProdZQE[ 06 ] ) + '|'
		EndIf

		If !Empty( aProdZQE[ 07 ] ) .And. Right( Alltrim( aProdZQE[ 07 ] ) , 1 ) <> '|'
			aProdZQE[ 07 ] := AllTrim( aProdZQE[ 07 ] ) + '|'
		EndIf

		cInadim		:= ZQE->ZQE_INADIM

		If ZQE->(RecLock("ZQE",.F.))
			cLogMsg += "At.De " + AllTrim( ZQE->ZQE_ATENDE ) + " Para " + AllTrim( cCtrAtend ) + ",Inadim De " + AllTrim( ZQE->ZQE_INADIM ) + " Para " + AllTrim( cCtrInadi )
	
			ZQE->ZQE_FILIAL  	:= xFilial( 'ZQE' )
			ZQE->ZQE_INADIM  	:= cCtrInadi
			ZQE->ZQE_TOTHR   	:= nTotHr
			ZQE->ZQE_CONTR   	:= aProdZQE[ 03 ]
			ZQE->ZQE_VERSAO  	:= aProdZQE[ 04 ]
			ZQE->ZQE_CLICOL  	:= aProdZQE[ 09 ]
			ZQE->ZQE_LJCOL   	:= aProdZQE[ 10 ]
			ZQE->ZQE_ATENDE  	:= cCtrAtend  //aProdZQE[ 11 ]
			ZQE->ZQE_EMPFIL  	:= aProdZQE[ 12 ]
			ZQE->ZQE_MIGRAC  	:= aProdZQE[ 13 ]
			ZQE->ZQE_FAMILI  	:= AllTrim( aProdZQE[ 06 ] )
			ZQE->ZQE_SLA 	 	:= AllTrim( aProdZQE[ 07 ] )
			ZQE->ZQE_VLRHR   	:= nZQEVlTot
			ZQE->ZQE_USERGA		:= "RLock"
			ZQE->ZQE_CLICPF		:= Posicione( 'SA1', 1, FWxFilial( 'SA1' ) + aProdZQE[ 01 ] + aProdZQE[ 02 ], 'A1_CGC' )
			ZQE->ZQE_GRPEMP		:= aProdZQE[ 18 ]
			
			ZQE->ZQE_DTATU	:= MsDate()
			ZQE->ZQE_HRATU	:=	Time()
			ZQE->ZQE_ROTINA	:= "Ação Manual." 
			
			If !empty(cExeReceiv)
				ZQE->ZQE_ROTINA	:= cExeReceiv
			EndIf
			
			If IsInCallStack("U_GCVA102G") .or. IsInCallStack("U_GCVA102HL")
				ZQE->ZQE_ROTINA := "Job Inicio do Mes(GCT)"
			Endif
			If IsInCallStack("U_TGC001CA") .OR.  IsInCallStack("U_TGC001AP") .OR. IsInCallStack("U_TGC001CI") .OR. IsInCallStack("U_TGC001ST") .OR. IsInCallStack("U_TGC001TR")
				ZQE->ZQE_ROTINA	:= ""
			EndIf
			
			If IsInCallStack("U_TGC001CA")
				ZQE->ZQE_ROTINA := Alltrim(ZQE->ZQE_ROTINA) +  " Alt.Contrato" 
			EndIf	
	
			If IsInCallStack("U_TGC001AP")
				ZQE->ZQE_ROTINA := Alltrim(ZQE->ZQE_ROTINA) +  " Aprv.Revisão" 
			EndIf	
	
			If IsInCallStack("U_TGC001CI")
				ZQE->ZQE_ROTINA := Alltrim(ZQE->ZQE_ROTINA) +  " Inc.Contrato" 
			EndIf	
	
			If IsInCallStack("U_TGC001ST")
				ZQE->ZQE_ROTINA := Alltrim(ZQE->ZQE_ROTINA) +  " Sit.Contrato" 
			EndIf	
	
			If IsInCallStack("U_TGC001TR")
				ZQE->ZQE_ROTINA := Alltrim(ZQE->ZQE_ROTINA) +  " Tranf.Contrato" 
			EndIf		
				
			If lZqeOffLine .And. IsInCallStack("U_JOBOFFLINE")
				ZQE->ZQE_ROTINA := PU3->PU3_ROTINA
			EndIf

			If ZQE->( FieldPos( 'ZQE_SERIE' ) ) > 0
				ZQE->ZQE_SERIE	:= TdiGetSerie( ZQE->ZQE_CONTR, ZQE->ZQE_VERSAO )
			EndIf
	
			If ZQE->( FieldPos( 'ZQE_QTDLIC' ) ) > 0
				If ZQE->( dbStruct()[aScan(ZQE->( dbStruct() ),{|x| x[1] == "ZQE_QTDLIC"})][3]) > 4;
				.AND. aProdZQE[ 17 ] > 9999
					ZQE->ZQE_QTDLIC	:= 9999
				Else
					ZQE->ZQE_QTDLIC	:= aProdZQE[ 17 ]
				EndIf
			EndIf		
	
			//Processo para garantir que o registro será enviado para réplica de dados
			If ZQE->( FieldPos( 'ZQE_MSEXP' ) ) > 0
				cLogMsg += ", De " + ZQE->ZQE_MSEXP + " Para " + Space(TamSX3("ZQE_MSEXP")[1])
			
				ZQE->ZQE_MSEXP := Space(TamSX3("ZQE_MSEXP")[1])
			EndIf
			
			If lReguaCobr//somente brasil(o qual possui regua) tem o campo abaixo na estrutura da tabela
				ZQE->ZQE_XOBS := ''
			EndIf
			//ZQE->( dbCommit() )
			ZQE->(MsUnLock())
		EndIf
	EndIf

	//Altera complemento do cliente para ser reprocessado e alterado também a organização na Zendesk
	U_TCRMJ011(aProdZQE[ 01 ])

Return lRet

Static Function TDIATUZSX5(_cChavCli)
Local _nVlHrDi 	:= 0
Local _cQry    	:= ""
Local _nTtRg   	:= 0
Local nVlrHrDia := GetMv("TDI_VLRHOR"	,,150.00	)

_cQry := "SELECT SX5.X5_DESCRI AS X5DESCRI, SX5.* FROM SX5000 SX5 "
_cQry += " WHERE SX5.D_E_L_E_T_ = ' ' AND SX5.X5_FILIAL = '  ' AND "
_cQry += "  SX5.X5_TABELA = 'ZQ' AND SX5.X5_CHAVE = '"+_cChavCli+"'


dbUseArea(.T., "TOPCONN", TCGenQry(,,_cQry), "NEWARQ", .F., .T.)
NEWARQ->( dbEval( { || _nTtRg ++ }, { || .T. }, { || NEWARQ->( !Eof() ) } ) )
NEWARQ->( dbGoTop() )

If _nTtRg == 0
	_nVlHrDi := nVlrHrDia
Else
	_nVlHrDi := Val(StrTran(AllTrim(NEWARQ->X5DESCRI),",","."))
EndIf

IIF( Select("NEWARQ") > 0, ("NEWARQ")->( dbCloseArea() ) , Nil )
Return(_nVlHrDi)

Static Function TdiGetAtend( cContra , cRevisa  )
Local nTotReg		:= 0
Local nFalse		:= 0
Local cRet 			:= Nil
Local cQuery		:= Nil
Local cSD6Alias		:= GetNextAlias()
Local __lCliExt 	:= .F.

Default cRevisa	:= ''

//// Inicio do tratamento de Inadimplencia de Ciente Internacional da Totvs - ticket 542659			
__lCliExt 	:= TGetClEx( Substr(cContra,4,6) )
If __lCliExt 
	cRet := "S"
Else			
//// Final do tratamento de Inadimplencia de Ciente Internacional da Totvs - ticket 542659			
	cQuery := "  SELECT DISTINCT CNC_NUMERO, CNC_ATENDE "+CRLF
	cQuery += "  FROM (  "+CRLF
	cQuery += "  SELECT DISTINCT CNC.CNC_NUMERO , CNC.CNC_CLIENT , CNC.CNC_LOJACL LOJA, CNC.CNC_TIPCLI, CNC.CNC_INADIM, CNC.CNC_ATENDE "+CRLF
	cQuery += "  FROM "+ RetSQLName( 'CNC' ) +" CNC "+CRLF

	cQuery += "  WHERE CNC.CNC_FILIAL = '"+ FWxFilial( 'CNC' ) +"'  "+CRLF              
	cQuery += "  	AND CNC.CNC_NUMERO = '"+ cContra +"' "+CRLF 							
	If !( Empty( cRevisa ) )
		cQuery += "  	AND CNC.CNC_REVISA = '"+ cRevisa +"'  "+CRLF
	Else
		cQuery += "  	AND CNC.CNC_REVISA IN ( SELECT MAX( CN9.CN9_REVISA ) CN9_REVISA "+CRLF 
		cQuery += "  							FROM "+ RetSQLName( 'CN9' ) +" CN9   	"+CRLF
		cQuery += "  							WHERE CN9.CN9_FILIAL = CNC.CNC_FILIAL  	"+CRLF 
		cQuery += "  								AND CN9.CN9_NUMERO = CNC.CNC_NUMERO "+CRLF 
		cQuery += "  								AND CN9.CN9_ESPCTR = '2'  			"+CRLF 
		cQuery += "  								AND SUBSTR( CN9.CN9_NUMERO, 1, 3 ) = 'CON'  "+CRLF 
		cQuery += "  								AND CN9.CN9_SITUAC = '05'  			"+CRLF 
		cQuery += "  								AND CN9.CN9_REVATU = ' '  			"+CRLF 
		cQuery += "  								AND CN9.D_E_L_E_T_ = ' '  )  		"+CRLF
	EndIf
//// Inicio da seleção de cliente destino de faturamento com rateio ( NAO Histórico ) deve ser relacionado - ticket 541149 e que seja Recorrente - ticket 474865 
	cQuery += " 								AND (	CNC.CNC_TIPCLI IN ('01','03', '04') OR "+CRLF
	cQuery += " 	EXISTS( SELECT PHG.PHG_CLIENT FROM "+ RetSQLName( 'PHG' ) +" PHG "+CRLF
    cQuery += "				INNER JOIN "+RetSqlName("CNB")+" CNB  "+CRLF
    cQuery += "				ON (CNB.CNB_FILIAL = PHG.PHG_FILIAL AND CNB.CNB_CONTRA = PHG.PHG_CONTRA AND "+CRLF 
    cQuery += "					CNB.CNB_REVISA = PHG.PHG_REVISA AND CNB.CNB_NUMERO = PHG.PHG_NUMERO AND "+CRLF 
    cQuery += "				    CNB.CNB_PRODUT = PHG.PHG_PRODUT AND CNB.CNB_TIPREC = '1'  AND CNB.D_E_L_E_T_ = ' ' ) "+CRLF // 1-Recorrente - Ticket 474865
	cQuery += " 	 		WHERE PHG.PHG_FILIAL = CNC.CNC_FILIAL "+CRLF
	cQuery += " 			AND PHG.PHG_CONTRA = CNC.CNC_NUMERO AND PHG.PHG_REVISA = CNC.CNC_REVISA "+CRLF
	cQuery += " 			AND PHG.PHG_CLIENT = CNC.CNC_CLIENT AND PHG.PHG_LOJA   = CNC.CNC_LOJACL "+CRLF  
	cQuery += " 			AND PHG.PHG_PERRAT > 0 AND  PHG.D_E_L_E_T_ = '  ' )) "+CRLF
//// Final da seleção de cliente destino de faturamento com rateio ( NAO Histórico ) deve ser relacionado - ticket 541149 e que seja Recorrente - ticket 474865
	cQuery += "  	AND CNC.D_E_L_E_T_ = ' ' "+CRLF
	cQuery += "  GROUP BY CNC.CNC_NUMERO , CNC.CNC_CLIENT , CNC.CNC_LOJACL , CNC.CNC_TIPCLI, CNC.CNC_INADIM, CNC.CNC_ATENDE "+CRLF
	cQuery += "  ORDER BY CNC_TIPCLI, CNC_CLIENT, CNC_LOJACL "+CRLF
	cQuery += "  ) TABELA "

	dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cSD6Alias, .T., .F. )
	dbSelectArea( cSD6Alias )
	( cSD6Alias )->( dbEval( { || nTotReg ++ },,{ || !Eof() } ) )

	If nTotReg == 0
		cRet := 'N'
	Else
		( cSD6Alias )->( dbGoTop() )
		While !( cSD6Alias )->( Eof() )
			If AllTrim( ( cSD6Alias )->CNC_ATENDE ) == 'N'
				nFalse ++
			EndIf
			( cSD6Alias )->( dbSkip() )
		EndDo

		cRet := IIf( nFalse > 0, 'N', 'S' )
	EndIf

	IIf( Select( cSD6Alias ) > 0, ( cSD6Alias )->( dbCloseArea() ), Nil )
EndIf

Return cRet

Static Function TdiGetInadi( cContrato, cCodVer, cCliente, cLojaCl )
	Local aRet		:= {}
	Local aArea		:= GetArea()
	Local cQuery	:= Nil
	Local nInd 		:= 0
	Local aAux		:= {}
	Local cZQZAlias	:= GetNextAlias()

	cQuery := " SELECT DISTINCT CNC.CNC_NUMERO , CNC.CNC_CLIENT , CNC.CNC_LOJACL LOJA, CNC.CNC_TIPCLI, CNC.CNC_INADIM "+CRLF
	cQuery += " FROM "+ RetSQLName( 'CNC' ) +" CNC "+CRLF

	cQuery += " WHERE CNC.CNC_FILIAL = '"+ FWxFilial( 'CNC' ) +"' "+CRLF               
	cQuery += " 	AND CNC.CNC_NUMERO = '"+ cContrato +"' "+CRLF
	cQuery += " 	AND CNC.CNC_REVISA = '"+ cCodVer   +"' "+CRLF
	cQuery += " 	AND CNC.CNC_CLIENT = '"+ cCliente  +"' "+CRLF
	cQuery += " 	AND CNC.CNC_LOJACL = '"+ cLojaCl   +"' "+CRLF
	cQuery += " 	AND CNC.D_E_L_E_T_ = ' ' "+CRLF

	cQuery += " GROUP BY CNC.CNC_NUMERO , CNC.CNC_CLIENT , CNC.CNC_LOJACL , CNC.CNC_TIPCLI, CNC.CNC_INADIM "+CRLF
	cQuery += " HAVING( CNC.CNC_INADIM = 'S' ) "+CRLF
	cQuery += " ORDER BY CNC_TIPCLI, CNC_CLIENT, CNC_LOJACL "

	dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cZQZAlias, .T., .F. )
	dbSelectArea( cZQZAlias )

	While !( cZQZAlias )->( Eof() )
		aAux := {}
		For nInd := 1 To ( cZQZAlias )->( FCount() )
			Aadd( aAux, ( cZQZAlias )->( FieldGet( nInd ) ) )
		Next nInd

		Aadd( aRet, AClone( aAux ) )

		( cZQZAlias )->( dbSkip() )
	EndDo

	IIf( Select( cZQZAlias ) > 0, ( cZQZAlias )->( dbCloseArea() ), Nil )
	RestArea( aArea )
Return aRet

Static Function TdiGetSerie( cContra, cRevisa )
	Local aArea		:= GetArea()
	Local cQuery		:= ""
	Local cTabQry		:= GetNextAlias()
	Local cRetorno 	:= ""
	Local lPosCpo		:= SBM->(FieldPos("BM_XSERIE")) > 0

	If lPosCpo
		cQuery += " SELECT MAX( BM_XSERIE ) BM_XSERIE "+CRLF
		cQuery += " FROM "+ RetSQLName( "SBM" ) +" SBM" + CRLF
		cQuery += "		INNER JOIN "+ RetSQLName( "SB1" ) +" SB1 ON ( SB1.B1_FILIAL  = '"+ FWxFilial( "SB1" ) +"' AND SBM.BM_FILIAL  = '"+ FWxFilial( "SBM" ) +"' AND SB1.B1_GRUPO = SBM.BM_GRUPO ) "+ CRLF
		cQuery += "		INNER JOIN "+ RetSQLName( "CNB" ) +" CNB ON	( SB1.B1_FILIAL  = '"+ FWxFilial( "SB1" ) +"' AND CNB.CNB_FILIAL = '"+ FWxFilial( "CNB" ) +"' AND CNB.CNB_PRODUT = SB1.B1_COD ) "+ CRLF 
		cQuery += "	WHERE CNB.CNB_CONTRA = '"+ cContra +"' "+CRLF  
		cQuery += "	  AND CNB.CNB_REVISA = '"+ cRevisa +"' "+CRLF
		cQuery += "	  AND SB1.D_E_L_E_T_ = ' ' "+CRLF
		cQuery += "	  AND CNB.D_E_L_E_T_ = ' ' "		
		cQuery += "	GROUP BY BM_XSERIE "

		cQuery := ChangeQuery( cQuery )

		dbUseArea(.T., __cRdd, TcGenQry(NIL, NIL, cQuery), cTabQry, .T., .T.)
		dbSelectArea( cTabQry )

		While !(cTabQry)->(Eof())
			cRetorno := (cTabQry)->BM_XSERIE
			(cTabQry)->(DbSkip())
		EndDo
	EndIf

	IIf( Select(cTabQry) > 0, (cTabQry)->(DbCloseArea()), Nil )
	RestArea(aArea)
Return cRetorno

//// Rotina para tratar ZQE de Contrato Cancelado - tickets 475046 / 575056
Static Function TdiZQECan( __cCont, __cRevi )
Local aArea		:= GetArea()
Local aCNBArea	:= CNB->( GetArea() )
Local aZQEArea	:= ZQE->( GetArea() )
Local __lRet 	:= .F.
Local cQuery	:= ""
Local cTabQry	:= GetNextAlias()
Local cReviAnterior := '' 
Local lConsAnt    := GetMv("TI_CNTANT",,.F.)//como não ha definicao das regras, criamos este parametro 	
Local lConsDelAnt := GetMv("TI_CNTDNT",,.F.) 	
Local lPubConsAnt := GetMv("TI_CNTPUB",,.T.)//como não ha definicao das regras, criamos este parametro 	
Local lAjuJob     := GetMv("TI_CNTPBJ",,.T.)
Local lRegrOld    := GetMv("TI_CNTROLD",,.F.)

If !lRegrOld
	U_AtuCanZQE( __cCont, __cRevi )
Else
	If lPubConsAnt
		If CliPublico( __cCont, __cRevi )
			lConsAnt := .T.
		EndIf
	EndIf
	cQuery += " SELECT DISTINCT	CNB.CNB_FILIAL, CNB.CNB_CONTRA, CNB.CNB_REVISA, CNB.CNB_NUMERO, CNB.CNB_PRODUT, CNB.CNB_VIGFIM, CNB.CNB_SITUAC, "+CRLF
	cQuery += "  CNC.CNC_DTATUA "+CRLF
	cQuery += " FROM "+ RetSQLName( "CNB" ) +" CNB " + CRLF
	cQuery += " INNER JOIN "+ RetSQLName( "CNC" ) +" CNC " + CRLF
	cQuery += "  ON (CNC.CNC_FILIAL = CNB.CNB_FILIAL AND CNC.CNC_NUMERO = CNB.CNB_CONTRA AND "+CRLF 
	cQuery += "      CNC.CNC_REVISA = CNB.CNB_REVISA AND CNC.D_E_L_E_T_ = ' ' ) "+CRLF

	cQuery += "	WHERE CNB.CNB_CONTRA = '"+ __cCont +"' "+CRLF  
	cQuery += "	  AND CNB.CNB_REVISA = '"+ __cRevi +"' "+CRLF
	If lConsAnt
		cQuery += "	  AND CNB.CNB_SITUAC IN ('C','S') "+CRLF
		cQuery += "	  AND CNB.CNB_TIPREC = '1' "+CRLF
	EndIf
	cQuery += "	  AND CNB.D_E_L_E_T_ = ' ' "+CRLF	

	cQuery += "	GROUP BY CNB.CNB_FILIAL, CNB.CNB_CONTRA, CNB.CNB_REVISA, CNB.CNB_NUMERO, CNB.CNB_PRODUT, CNB.CNB_VIGFIM, CNB.CNB_SITUAC, "+CRLF
	cQuery += "  CNC.CNC_DTATUA "+CRLF

	If lConsAnt
		cQuery += "	HAVING( CNB.CNB_VIGFIM >= '"+ DtoS(dDataBase)+"'   ) "+CRLF 
	EndIf
	cQuery += "	ORDER BY CNB.CNB_FILIAL, CNB_CONTRA, CNB_REVISA, CNB_NUMERO "+CRLF 

	cQuery := ChangeQuery( cQuery )

	dbUseArea(.T., __cRdd, TcGenQry(NIL, NIL, cQuery), cTabQry, .T., .T.)
	dbSelectArea( cTabQry )

	If !(cTabQry)->(Eof()) 
		If !lConsDelAnt
			(cTabQry)->(DbCLoseArea())
		EndIf
		U_AtuCanZQE( __cCont, __cRevi )
	Else	
		If !lConsDelAnt
			(cTabQry)->(DbCLoseArea())
		EndIf

		If lAjuJob .And. (IsInCallStack("U_GCVA102G") .or. IsInCallStack("U_GCVA102HL"))  .And. !empty(__cRevi) //.And. ;
			cReviAnterior := __cRevi

			ZQE->(DbSetOrder(7))
			ZQE->( DbSeek(Fwxfilial("ZQE") + __cCont  + __cRevi  ) )

			If ZQE->(!Eof()) .And. (ZQE->ZQE_FILIAL+ZQE->ZQE_CONTR+ZQE->ZQE_VERSAO) == (Fwxfilial("ZQE")+__cCont+__cRevi) 
				//ja existe zqe na versao atual
			Else
				If Val(cReviAnterior) > 1
					cReviAnterior := Alltrim ( STR((Val(cReviAnterior)-1) ) )
					cReviAnterior := padl(cReviAnterior,  TamSX3('ZQE_VERSAO')[1] , '0' )
					cReviAnterior := AllTrim(cReviAnterior)
				Else
					cReviAnterior := "      "
				EndIf

				DbSelectarea("ZQE")
				ZQE->(DbSetOrder(7))
				ZQE->( DbSeek(Fwxfilial("ZQE") + __cCont  + cReviAnterior  ) )
					
				While ZQE->(!Eof()) .And. (ZQE->ZQE_FILIAL+ZQE->ZQE_CONTR+ZQE->ZQE_VERSAO) == (Fwxfilial("ZQE")+__cCont+cReviAnterior) 
					reclock('ZQE',.F.)
					ZQE->ZQE_VERSAO := __cRevi
					Msunlock()
					ZQE->(DbSkip())
				End
			EndIf

		EndIf
	EndIf

	If lConsDelAnt
		(cTabQry)->(DbCLoseArea())
	EndIf
EndIf

CNB->(RestArea( aCNBArea ))
ZQE->(RestArea( aZQEArea ))
RestArea( aArea )
// inicia processo de limpeza da memória
aSize(aArea,0)
aSize(aCNBArea,0)
aSize(aZQEArea,0)

aArea		:= NIL
aCNBArea	:= NIL
aZQEArea	:= NIL

Return __lRet


Static Function TGetClEx( __cCli, __cLj )
Local aArea		:= GetArea()
Local aSA1Area	:= SA1->( GetArea() )
Local __lRet 	:= .F.

Default __cLj   := "00"

dbSelectArea( "SA1" )
SA1->( dbSetOrder( 1 ) )

__cCli := Padr( __cCli, TamSx3( 'A1_COD' )[ 01 ] )
__cLj  := Padr( __cLj , TamSx3( 'A1_LOJA')[ 01 ] )

If !SA1->( dbSeek( FWxFilial( "SA1" ) + __cCli +__cLj ) )
	__lRet 	:= .F.
Else
	Do Case
		Case SA1->A1_EST == 'EX'
			__lRet 	:= .T.
		Case !( SA1->A1_PAIS == '105' )
			__lRet 	:= .T.
		Case !(SA1->A1_CODPAIS $ ("105,1058,01058") )
			__lRet 	:= .T.
		OtherWise
			__lRet 	:= .F.
	EndCase
EndIf

RestArea( aSA1Area )
RestArea( aArea )
Return __lRet

Static Function GetSlotPrd( aProdLight, nSlot )
	Local nSrcRet		:= 0
	Local nCodAux 	:= 0
	Local nCodLeft	:= 0
	Local nCodRight	:= 0
	
	If Len( AllTrim( cValToChar( nSlot ) ) ) == 4
		nCodAux := nSlot
		nSrcRet := Ascan( aProdLight, { | x | x[ 01 ] == nCodAux } )
		
	ElseIf Len( AllTrim( cValToChar( nSlot ) ) ) > 4
		nCodLeft	:= Val( SubStr( AllTrim( cValToChar( nSlot ) ), 2, Len( AllTrim( cValToChar( nSlot ) ) ) ) )
		nCodRight	:= Val( SubStr( AllTrim( cValToChar( nSlot ) ), 1, Len( AllTrim( cValToChar( nSlot ) ) ) - 1 ) )
		
		nSrcRet := Ascan( aProdLight, { | x | x[ 01 ] == nCodLeft } )
		If nSrcRet == 0
			nSrcRet := Ascan( aProdLight, { | x | x[ 01 ] == nCodRight } )
		EndIf
		
	EndIf
	
Return nSrcRet

User Function GCVA018R()
Processa({|| ProcZqe() },"zqe","Processando, aguarde...", .F. )

Return


STATIC Function ProcZqe()


Local cQuery := ""
Local cAliZQE := GetNextAlias()
Local ntotreg := 0

//Local ___EmlEqp	:= AllTrim( GetMv( 'TDI_ZQEMAIL',,"<EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>" ) )


/*
cQuery := " SELECT DISTINCT CN9_NUMERO, CN9_REVISA"
cQuery += " FROM " + RetSqlName("CN9") + " CN9"

cQuery += " INNER JOIN " + RetSqlName("CNC") + " CNC"
cQuery += " ON CNC.D_E_L_E_T_ = ' '"
cQuery += " AND CNC_FILIAL = CN9_FILIAL"
cQuery += " AND CNC_NUMERO = CN9_NUMERO"
cQuery += " AND CNC_REVISA = CN9_REVISA"

cQuery += " INNER JOIN " + RetSqlName("ZQE") + " ZQE"
cQuery += " ON ZQE.D_E_L_E_T_ = '*'"
cQuery += " AND ZQE_FILIAL IS NOT NULL"
cQuery += " AND ZQE_CONTR = CNC_NUMERO"
cQuery += " AND ZQE_CODCLI = CNC_CLIENT"

cQuery += " WHERE CN9.D_E_L_E_T_ = ' '"
cQuery += " AND CN9.CN9_FILIAL IS NOT NULL"
cQuery += " AND CN9_NUMERO LIKE 'CON%'"
cQuery += " AND CN9_NUMERO > 'CONT75036'" 
cQuery += " AND CN9_SITUAC = '05'"
cQuery += " AND CN9_ESPCTR = '2'"

cQuery += " ORDER BY CN9_NUMERO"

DbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAliZQE,.F.,.T.)
(cAliZQE)->( dbEval( { || ntotreg ++ }, { || .T. }, { || (cAliZQE)->( !Eof() ) } ) )
(cAliZQE)->(DbGoTop())
ProcRegua(ntotreg)
Do While !(cAliZQE)->(EOF())
	IncProc("Processando " + AllTrim(Str(ntotreg--)) + " Contrato: "+AllTrim((cAliZQE)->(CN9_NUMERO))+" de "+AllTrim((cAliZQE)->(CN9_REVISA)))
	U_TGCVA018((cAliZQE)->(CN9_NUMERO),(cAliZQE)->(CN9_REVISA))
	
	LogMsg('tlogmsg', 22, 5, 1, '', '', 'Contrato:' + (cAliZQE)->(CN9_NUMERO))
	If ncontador == 0 .Or. ncontador == 500
		U_xSendMail(___EmlEqp , "Contrato CNB - reprocessando: " + (cAliZQE)->(CN9_NUMERO) , (cAliZQE)->(CN9_NUMERO),, .F.,,,,,)
		ncontador := 1
	EndIf
	
	cContrato := (cAliZQE)->(CN9_NUMERO)
	ncontador++
	(cAliZQE)->(Dbskip())
EndDo

U_xSendMail(___EmlEqp , "Contrato CNB - reprocessando: " + cContrato , cContrato,, .F.,,,,,)
(cAliZQE)->(DbCloseArea())
/*/


cQuery := " SELECT DISTINCT CN9_NUMERO, CN9_REVISA" 
cQuery += " FROM " + RetSqlName("CN9") + " CN9"

cQuery += " INNER JOIN " + RetSqlName("CNC") + " CNC"
cQuery += " ON CNC.D_E_L_E_T_ = ' '"
cQuery += " AND CNC_FILIAL = CN9_FILIAL"
cQuery += " AND CNC_NUMERO = CN9_NUMERO"
cQuery += " AND CNC_REVISA = CN9_REVISA"

cQuery += " INNER JOIN " + RetSqlName("ZQE") + " ZQE"
cQuery += " ON ZQE.D_E_L_E_T_ = ' '"
cQuery += " AND ZQE_FILIAL IS NOT NULL"
cQuery += " AND ZQE_CONTR = CNC_NUMERO"
cQuery += " AND ZQE_CODCLI = CNC_CLIENT"


cQuery += " WHERE CN9.D_E_L_E_T_ = ' '"
cQuery += " AND CN9.CN9_FILIAL IS NOT NULL"
cQuery += " AND CN9_NUMERO LIKE 'CON%'"
cQuery += " AND CN9_SITUAC = '05'" 
cQuery += " AND CN9_ESPCTR = '2'"

cQuery += " AND NOT EXISTS (SELECT 1 FROM " + RetSqlName("ZQE") + " ZQE WHERE ZQE.D_E_L_E_T_ = ' ' AND ZQE_FILIAL IS NOT NULL AND ZQE_CODCLI = CNC_CLIENT AND ZQE_VERSAO = CNC_REVISA)"


DbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAliZQE,.F.,.T.)
(cAliZQE)->( dbEval( { || ntotreg ++ }, { || .T. }, { || (cAliZQE)->( !Eof() ) } ) )
(cAliZQE)->(DbGoTop())
ProcRegua(ntotreg)
Do While !(cAliZQE)->(EOF())
	IncProc("Processando " + AllTrim(Str(ntotreg--)) + " Contrato: "+AllTrim((cAliZQE)->(CN9_NUMERO))+" de "+AllTrim((cAliZQE)->(CN9_REVISA)))
	U_TGCVA018((cAliZQE)->(CN9_NUMERO),(cAliZQE)->(CN9_REVISA))
	(cAliZQE)->(Dbskip())
EndDo

(cAliZQE)->(DbCloseArea())


MsgAlert("FIM")
Return



/*/{Protheus.doc} AtuCanZQE

Objetivo 1: Função foi criada para ser invocada de dentro do cronograma em ponto no qual já sabe-se que o contrato está cancelado!
            Ela remove os atributos de atendimento do cliente na CNC e faz a transmissao para o Zendesk.
Objetivo 2: Função pode ser chamada dentro do TdiZQECan() o qual primeiro testa se o contrato está com item suspenso\cancelado

<AUTHOR> Leite
@since  05/09/2019
/*/
user Function AtuCanZQE( cContra, cRevi, cObs, cRotina )
Local   aArea       := GetArea()
Local   lIntegracao := GetMv('TI_TF11ZEN', , .T.) //Atualiza Organização na Zendesk
Local   lReguaCobr  := (GetMv( 'TI_ZQEINAD',,'A' ) == "R") 
Local   lCYCXLR     :=  GetMv("TI_CYCXLR", , .T.)
Default cObs        := 'Cancelamento de Contrato'
Default cRotina     := 'Cancelamento de Contrato' 

DbSelectarea("SA1")
SA1->(DbSetOrder(1))

DbSelectarea("ZQE")
ZQE->(DbSetOrder(7))

DbSelectArea("AI0")
AI0->(DbSetOrder(1))

DbSelectarea("CNC")
CNC->(DbSetOrder(1))
CNC->(DbSeek(Fwxfilial("CNC") + cContra + cRevi ))

While CNC->(!Eof()) .And. (CNC->CNC_FILIAL + CNC->CNC_NUMERO + CNC->CNC_REVISA) ==  (Fwxfilial("CNC") + cContra + cRevi)

	If 	lCYCXLR .or. ( !lCYCXLR .And. CNC->CNC_DTATUA == dDataBase ) //retornamos consistência de data do changeset 53889	

		reclock('CNC',.F.)
		CNC->CNC_ATENDE := "N"
		If lReguaCobr
			CNC->CNC_XOBS   := cObs
		EndIf
		MsUnLock()

		TdiDelete( 'ZQE',, cContra, "AtuCanZQE" )

		If lIntegracao  //comunica o cancelamento ao ZenDesk (a rotina entende o zqe deletado normalmente)  
			U_TCRMJ011(SA1->A1_COD)
		EndIF

	EndIf

	CNC->(DbSkip())
End

RestArea( aArea )
Return 

//-------------------------------------------------------------------
/*{Protheus.doc} JOBOFFZEQ
Solicitado parametro TI_ZQEOFFL(Brasil) apenas para que telas do GCT jamais rodem integração com zendesk. Ao inves disto populam
tabela temporaria e somente apos 18:00 um job integraria com zendesk. 
Sendo assim zqe não seria mais uma rotina on-line. 

<AUTHOR>
@since 06/08/2015
@version P12
*/
//-------------------------------------------------------------------
User function JOBOFFZEQ (cContra, cRevisa)
Local cChave 
Local cDelet

If !Empty(cContra)
	cChave := Fwxfilial('PU3') + cContra +  cRevisa
	DbSelectarea("PU3")
	PU3->(DbSetOrder(2))
	PU3->( DbSeek( cChave ) )

	If PU3->(Eof()) 

		//caso haja duas ou mais revisoes no mesmo dia mantem registrado  apenas a ultima
		cDelet := " DELETE FROM " + RetSqlName("PU3")  + " WHERE "
		cDelet += " PU3_NUMERO = '" + cContra       + "' AND "
		cDelet += " PU3_DATA   = '" + dtos(Date())  + "' AND "
		cDelet += " D_E_L_E_T_ = ' ' "
		TcSqlExec( cDelet )   

		reclock('PU3',.T.)
		PU3->PU3_FILIAL := Fwxfilial('PU3')
		PU3->PU3_NUMERO := cContra
		PU3->PU3_REVISA := cRevisa
		PU3->PU3_SEQ    := "000000001"//sem uso , apenas precuacao caso mudem definicao
		PU3->PU3_DATA   := Date()	
		PU3->PU3_ROTINA := "GCT OFF-LINE"
				
		If IsInCallStack("U_TGC001CA")
			PU3->PU3_ROTINA := Alltrim(PU3->PU3_ROTINA) + ", Alt.Contrato" 
		EndIf	
		
		If IsInCallStack("U_TGC001AP")
			PU3->PU3_ROTINA := Alltrim(PU3->PU3_ROTINA) + ", Aprv.Revisão" 
		EndIf	
		
		If IsInCallStack("U_TGC001CI")
			PU3->PU3_ROTINA := Alltrim(PU3->PU3_ROTINA) + ", Inc.Contrato" 
		EndIf	
		
		If IsInCallStack("U_TGC001ST")
			PU3->PU3_ROTINA := Alltrim(PU3->PU3_ROTINA) + ", Sit.Contrato" 
		EndIf	
		
		If IsInCallStack("U_TGC001TR")
			PU3->PU3_ROTINA := Alltrim(PU3->PU3_ROTINA) + ", Tranf.Contrato" 
		EndIf	
		
		//demais campos da tabela sem uso mas deixados de precaucao caso mudem definicao novamente
		MsUnLock()
	EndIf
Endif

return 


Static Function CliPublico(cContrato, cRevisa)
Local aAreaCNC := CNC->(GetArea("CNC"))
Local lRet     := .F. 

CNC->(DbSetOrder(5))  
CNC->(DbSeek(xFilial("CNC") + cContrato + cRevisa + "01"))

If CNC->(!Eof())
	AI0->(DbSetOrder(1))  
	AI0->(DbSeek(xFilial("AI0") +  CNC->(CNC_CLIENT + CNC_LOJACL) )) 
		
	If AI0->(!Eof())
		If AI0->AI0_SETPUB == '1'
			lRet := .T. 
		EndIf
	EndIf
EndIf

RestArea(aAreaCNC)
Return lRet


		