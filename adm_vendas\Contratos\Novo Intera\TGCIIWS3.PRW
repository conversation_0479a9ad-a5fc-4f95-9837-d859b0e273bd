﻿#Include "Totvs.Ch"
#Include "Restful.Ch"
#Include "FwMVCDef.Ch"

//--------------------------------------------------------------
/*/{Protheus.doc} INTERAWS
WS - Rest de integracoes porta vs Protheus - Intera
/*/
//--------------------------------------------------------------
User Function TGCIIWS3()
Return

//---------------------------------------------------------
/*/{Protheus.doc} InteraEnvioECD
Metodo para enviar o arquivo de comprovação do ECD.
/*/
//---------------------------------------------------------

WsRestFul InteraEnvioECD Description "Metodo para enviar o arquivo de comprovacao do ECD"

	WsMethod Post Description "Metodo para enviar o arquivo de comprovacao do ECD" WsSyntax "/InteraEnvioECD"
		 
End WsRestFul

WsMethod Post WsService InteraEnvioECD

Local oJson         := Nil
Local aJSonField    := {}
Local lRet          := .F.
Local cPropDados    := IIf( Empty( Self:GetContent() ), '', Self:GetContent() )
Local cJson         := ""
Local nRetParser    := 0
Local nPosCnpj      := 0
Local nPosCodigo    := 0
Local nPosArquivo   := 0
Local nPosTent      := 0
Local nPosAnoRef    := 0
Local nPosTipComp   := 0
Local aDados        := {}

    oJSon := TJSonParser():New()
    lRet  := oJSon:Json_Parser( cPropDados /*strJson*/,Len( cPropDados ) /*lenStrJson*/, @aJSonField /*@jsonfields*/, @nRetParser /*@nRetParser*/ )

    // ----------------------------------
	// Define o tipo de retorno do método
	// ----------------------------------
    ::SetContentType("application/json")

    If Len( aJSonField ) <= 0 .Or. !lRet
	
		cJson += '{"Retorno":{  '
        cJson += '"Status":"2",
        cJson += '"Observacao":"Nao foi possivel realizar o parser do JSON enviado, favor conferir layout."},'
        cJson += '"Comprovacao":[{
        cJson += '"ValorComprovado":0}]}
        
	Else

        aDados := aJSonField[1][2]

        nPosCnpj    := aScan(aDados, {|x| Alltrim(x[1]) == "CNPJPai"            })
        nPosCodigo  := aScan(aDados, {|x| Alltrim(x[1]) == "CodigoCliente"      })
        nPosArquivo := aScan(aDados, {|x| Alltrim(x[1]) == "Arquivo"            })
        nPosTent    := aScan(aDados, {|x| Alltrim(x[1]) == "NumeroTentativas"   })
        nPosAnoRef  := aScan(aDados, {|x| Alltrim(x[1]) == "AnoReferencia"      })
        nPosTipComp := aScan(aDados, {|x| Alltrim(x[1]) == "TipoComprovacao"    })

        If nPosCnpj == 0 .Or. nPosCodigo == 0 .Or. nPosArquivo == 0 .Or. nPosTent == 0 .Or. nPosAnoRef == 0 .Or. nPosTipComp == 0

            cJson += '{"Retorno":{  '
            cJson += '"Status":"2",
            cJson += '"Observacao":"Nao foi passado todos parametros que sao obrigatorios, ou algum veio em branco/nulo, favor conferir layout do JSON enviado."},'
            cJson += '"Comprovacao":[{
            cJson += '"ValorComprovado":0}]}

            Self:SetResponse( cJson )

            Return(.T.)

        EndIf

        If lRet //Caso tenha encontrado valor para comparação

            cJson += '{"Retorno":{  '
            cJson += '"Status":"1",
            cJson += '"Observacao":""},'
            cJson += '"Comprovacao":[{
            cJson += '"ValorComprovado":123456}]}
        
        Else

            cJson += '{"Retorno":{  '
            cJson += '"Status":"2",
            cJson += '"Observacao":"Nao foi possivel Identificar o valor de compracao no arquivo ECD."},'
            cJson += '"Comprovacao":[{
            cJson += '"ValorComprovado":0}]}

        EndIf

    EndIf

    Self:SetResponse( cJson )

Return(.T.)

//---------------------------------------------------------------
/*/{Protheus.doc} InteraMetrica
Metodo para consultar a metrica apos a confirmação dos valores.
/*/
//---------------------------------------------------------------

WsRestFul InteraMetrica Description "Metodo para enviar a metrica apos a confirmacao dos valores de comprovacao."

	WsData CNPJPai          AS String
    WsData CodigoCliente    AS String
    WsData AnoReferencia    AS String
    WsData TipoComprovacao  AS String
    
    WsMethod Get Description "Metodo para enviar a metrica apos a confirmacao dos valores de comprovacao." WsSyntax "/InteraMetrica"
		 
End WsRestFul

WsMethod Get WsService InteraMetrica

Local cJson         := ""
Local cCNPJ         := ""
Local cCodigo       := ""
Local cAnoRef       := ""
Local cTpComprov    := ""
Local lRet          := .F.

    // ----------------------------------
	// Define o tipo de retorno do método
	// ----------------------------------
	::SetContentType("application/json")

    If Len(::aURLParms) > 0
		cCNPJ	    := ::aURLParms[1]
		cCodigo	    := ::aURLParms[2]
		cAnoRef	    := ::aURLParms[3]
		cTpComprov  := ::aURLParms[4]
    Else
		cCNPJ	    := ::CNPJPai
		cCodigo 	:= ::CodigoCliente
		cAnoRef 	:= ::AnoReferencia
        cTpComprov  := ::TipoComprovacao
    EndIf

    If Empty(cCNPJ) .Or. Empty(cCodigo) .Or. Empty(cAnoRef) .Or. Empty(cTpComprov)
        
        cJson += '{"Retorno":{'  
        cJson += '"Status":"2",'
        cJson += '"Observacao":"Nao foi passado todos parametros que sao obrigatorios, ou algum veio em branco/nulo."},'
        cJson += '"Metrica":[{'  
        cJson += '"AnoReferencia":""}]}'

        Self:SetResponse( cJson )
        Return(.T.)

    EndIf

    If lRet //Se encontrar dados para retornar.
        
        cJson += '{"Retorno":{'  
        cJson += '"Status":"1",'
        cJson += '"Observacao":""},'
        cJson += '"Metrica":[{'  
        cJson += '"AnoReferencia":"05/2018"}]}'

    Else

        cJson += '{"Retorno":{'  
        cJson += '"Status":"2",'
        cJson += '"Observacao":"Sem registros para retornar."},'
        cJson += '"Metrica":[{'  
        cJson += '"AnoReferencia":""}]}'

    EndIf

    Self:SetResponse( cJson )

Return(.T.)

//---------------------------------------------------------
/*/{Protheus.doc} InteraTicketComp
Metodo para enviar o codigo do ticket gerado no zendesk.
/*/
//---------------------------------------------------------

WsRestFul InteraTicketComp Description "Metodo para enviar o codigo do TICKET gerado no Zendesk, referente a comprovacao."

	WsMethod Post Description "Metodo para enviar o codigo do TICKET gerado no Zendesk, referente a comprovacao." WsSyntax "/InteraTicketComp"
		 
End WsRestFul

WsMethod Post WsService InteraTicketComp

Local oJson         := Nil
Local aJSonField    := {}
Local lRet          := .F.
Local cPropDados    := IIf( Empty( Self:GetContent() ), '', Self:GetContent() )
Local cJson         := ""
Local nRetParser    := 0
Local aDados        := {}
Local nPosCnpj      := 0
Local nPosCodigo    := 0
Local nPosTpTkt     := 0
Local nPosTicket    := 0
Local nPosAtuSts    := 0
Local nPosCnpAgr    := 0
Local oRest         := Nil
Local cUrlII        := GETMV("TI_IIWSURL",,"https://wscorp.totvs.com.br/")
Local cJSONPost     := ""
Local cRetPost      := ""
Local aHeader       := {}
Local cQuery        := ""
Local cAlsRev       := CriaTrab(Nil,.F.)
Local aJSonPost     := {}
Local aRetJson      := {}

    oJSon := TJSonParser():New()
    lRet  := oJSon:Json_Parser( cPropDados /*strJson*/,Len( cPropDados ) /*lenStrJson*/, @aJSonField /*@jsonfields*/, @nRetParser /*@nRetParser*/ )

    // ----------------------------------
	// Define o tipo de retorno do método
	// ----------------------------------
    ::SetContentType("application/json")

    If Len( aJSonField ) <= 0 .Or. !lRet
	
		cJson += '{"Retorno":{'
        cJson += '"Status":"2",'
        cJson += '"Observacao":"Nao foi possivel realizar o parser do JSON enviado, favor conferir layout."}}'
        
	Else

        aDados := aJSonField[1][2]

        nPosCnpj    := aScan(aDados, {|x| Alltrim(x[1]) == "CNPJPai"        })
        nPosCodigo  := aScan(aDados, {|x| Alltrim(x[1]) == "CodigoCliente"  })
        nPosTpTkt   := aScan(aDados, {|x| Alltrim(x[1]) == "TipoTicket"     })
        nPosTicket  := aScan(aDados, {|x| Alltrim(x[1]) == "NumTicket"      })
        nPosAtuSts  := aScan(aDados, {|x| Alltrim(x[1]) == "AtuStatus"      })
        nPosCnpAgr  := aScan(aDados, {|x| Alltrim(x[1]) == "CNPJAgr"        })
        
        If nPosCnpj == 0 .Or. nPosCodigo == 0 .Or. nPosTpTkt == 0 .Or. nPosTicket == 0 .Or. nPosAtuSts == 0

            cJson += '{"Retorno":{'
            cJson += '"Status":"2",'
            cJson += '"Observacao":"Nao foi passado todos parametros que sao obrigatorios, ou algum veio em branco/nulo, favor conferir layout do JSON enviado."}}'
            
            Self:SetResponse( cJson )
            Return(.T.)

        EndIf

        If aDados[nPosAtuSts][2] == "S" .And. nPosCnpAgr == 0

            cJson += '{"Retorno":{'
            cJson += '"Status":"2",'
            cJson += '"Observacao":"Foi informado para atualizar o status da metrica e nao foi passado o CNPJ do agrupador."}}'
            
            Self:SetResponse( cJson )
            Return(.T.)

        EndIf

        DbSelectArea("SX5")
        SX5->(DbSetOrder(1))

        If !SX5->(MsSeek(xFilial("SX5")+"IL"+aDados[nPosTpTkt][2]))

            cJson += '{"Retorno":{'
            cJson += '"Status":"2",'
            cJson += '"Observacao":"Tipo de ticket informado nao esta cadastrado."}}'

            Self:SetResponse( cJson )
            Return(.T.)

        EndIf

        DbSelectArea("CN9")
        CN9->(DbSetOrder(1))    

        If !CN9->(MsSeek(xFilial("CN9")+"CON"+aDados[nPosCodigo][2]))

            cJson += '{"Retorno":{'
            cJson += '"Status":"2",'
            cJson += '"Observacao":"O Codigo do cliente informado nao possui contrato."}}'

            Self:SetResponse( cJson )
            Return(.T.)

        Else

            If aDados[nPosAtuSts][2] == "S"

                oRest := FWREST():New(cUrlII)
	            oJSon := TJSonParser():New()

                oRest:SetPath("/InteraAtuStatus")

                //----------------------------------------------------
                // tratamento para o Header da API
                //----------------------------------------------------
                Aadd( aHeader, 'Content-Type: application/json' )

                //Busco a ultima revisao do intera e a ultima revisao do contrato.
                cQuery := " SELECT PQA_CONTRA, MAX(PQA_REVISA) REVCON, MAX(PQA_IIREVS) REVII, MAX(PQA_ANOREF) ANOREF "
                cQuery += " FROM " + RetSqlName("PQA") + " PQA "
                cQuery += " WHERE PQA_CONTRA = 'CON" + aDados[nPosCodigo][2] + "' "
                cQuery += " AND PQA_CGCPRI = '" + aDados[nPosCnpj][2] + "' "
                cQuery += " AND PQA_CGCAGR = '" + aDados[nPosCnpAgr][2] + "' "
                cQuery += " AND PQA.D_E_L_E_T_ = ' ' "
                cQuery += " GROUP BY PQA_CONTRA "

                If Select(cAlsRev) > 0; (cAlsRev)->(dbCloseArea()); Endif  
	            dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsRev,.T.,.T.)

                If Empty( (cAlsRev)->PQA_CONTRA )

                    cJson += '{"Retorno":{'
                    cJson += '"Status":"2",'
                    cJson += '"Observacao":"Foi informado para atualizar o status e não foi encontrado metrica informada para o CNPJPai + CNPJAgr informado."}}'

                    Self:SetResponse( cJson )
                    Return(.T.)

                Else
                    
                    cJSONPost := '{'
                    cJSONPost += '"Contrato":"' + (cAlsRev)->PQA_CONTRA + '",'
                    cJSONPost += '"RevisaoContra":"' + (cAlsRev)->REVCON + '",'
                    cJSONPost += '"CNPJPai":"' + aDados[nPosCnpj][2] + '",'
                    cJSONPost += '"CNPJAgr":"' + aDados[nPosCnpAgr][2] + '",'
                    cJSONPost += '"RevIntera":"' + (cAlsRev)->REVII + '",'
                    cJSONPost += '"AnoRef":"' + (cAlsRev)->ANOREF + '"'
                    cJSONPost += '}

                    //Sevo o JSON como parametro
                    oRest:SetPostParams(cJSONPost)
                    
                    //Realizo o Post do JSON
                    lRet := oRest:Post(aHeader)

                    If lRet

                        cRetPost := oRest:GetResult()

                        oJSon:Json_Parser( cRetPost /*strJson*/,Len( cRetPost ) /*lenStrJson*/, @aJSonPost /*@jsonfields*/, @nRetParser /*@nRetParser*/ )

                        aRetJson := aJSonPost[1][2][2][2]

                        nPosRet     := aScan(aRetJson, { |x| Alltrim(x[1]) == "Status" })
                        nPosRetObs  := aScan(aRetJson, { |x| Alltrim(x[1]) == "Observacao" })

                        //Se o status retornou sucesso
                        If aRetJson[nPosRet][2] == "1"

                            DbSelectArea("PQK")

                            PQK->(RecLock("PQK",.T.))

                                PQK->PQK_FILIAL := xFilial("PQK")
                                PQK->PQK_CONTRA := "CON" + aDados[nPosCodigo][2]
                                PQK->PQK_CLIENT := aDados[nPosCodigo][2]
                                PQK->PQK_TICKET := aDados[nPosTicket][2]
                                PQK->PQK_CNPJP  := aDados[nPosCnpj][2]
                                PQK->PQK_TIPO   := aDados[nPosTpTkt][2]

                            PQK->(MsUnlock())
                        
                        Else

                            cJson += '{"Retorno":{'
                            cJson += '"Status":"2",'
                            cJson += '"Observacao":"Foi informado para atualizar o status e foi retornado erro do metodo de atualizacao, motivo: ' + aRetJson[nPosRetObs][2] + '"}}'

                            Self:SetResponse( cJson )
                            Return(.T.)

                        EndIf

                    EndIf

                EndIf

                (cAlsRev)->(dbCloseArea())

            Else

                DbSelectArea("PQK")

                PQK->(RecLock("PQK",.T.))

                    PQK->PQK_FILIAL := xFilial("PQK")
                    PQK->PQK_CONTRA := "CON" + aDados[nPosCodigo][2]
                    PQK->PQK_CLIENT := aDados[nPosCodigo][2]
                    PQK->PQK_TICKET := aDados[nPosTicket][2]
                    PQK->PQK_CNPJP  := aDados[nPosCnpj][2]
                    PQK->PQK_TIPO   := aDados[nPosTpTkt][2]

                PQK->(MsUnlock())

            EndIf

        EndIf

        If lRet //Se conseguir atualizar ticket no zendesk.

            cJson += '{"Retorno":{'
            cJson += '"Status":"1",'
            cJson += '"Observacao":""}}'

        Else

            cJson += '{"Retorno":{'
            cJson += '"Status":"2",'
            cJson += '"Observacao":"Nao foi encontrado contrato vinculado ao cliente informado, favor conferir conteudo CodigoCliente."}}'

        EndIf

    EndIf

    Self:SetResponse( cJson )

Return(.T.)

//---------------------------------------------------------
/*/{Protheus.doc} InteraEnvForm
Metodo para enviar o formulario e comprovacao.
/*/
//---------------------------------------------------------

WsRestFul InteraEnvForm Description "Metodo para enviar o formulario e comprovacao."

	WsMethod Post Description "Metodo para enviar o formulario e comprovacao." WsSyntax "/InteraEnvForm"
		 
End WsRestFul

WsMethod Post WsService InteraEnvForm

Local oJson         := Nil
Local aJSonField    := {}
Local lRet          := .F.
Local cPropDados    := IIf( Empty( Self:GetContent() ), '', Self:GetContent() )
Local cJson         := ""
Local nRetParser    := 0
Local aDados        := {}
Local nPosCnpj      := 0
Local nPosCodigo    := 0
Local nPosDados     := 0
Local nPosTent      := 0
Local nPosAnoRef    := 0
Local nPosTipComp   := 0

    oJSon := TJSonParser():New()
    lRet  := oJSon:Json_Parser( cPropDados /*strJson*/,Len( cPropDados ) /*lenStrJson*/, @aJSonField /*@jsonfields*/, @nRetParser /*@nRetParser*/ )

    // ----------------------------------
	// Define o tipo de retorno do método
	// ----------------------------------
    ::SetContentType("application/json")

    If Len( aJSonField ) <= 0 .Or. !lRet
	
		cJson += '{"Retorno":{  '
        cJson += '"Status":"2",
        cJson += '"Observacao":"Nao foi possivel realizar o parser do JSON enviado, favor conferir layout."},'
        cJson += '"Comprovacao":[{
        cJson += '"ValorComprovado":0}]}
        
	Else

        aDados := aJSonField[1][2]

        nPosCnpj    := aScan(aDados, {|x| Alltrim(x[1]) == "CNPJPai"            })
        nPosCodigo  := aScan(aDados, {|x| Alltrim(x[1]) == "CodigoCliente"      })
        nPosDados   := aScan(aDados, {|x| Alltrim(x[1]) == "DadosFormulario"    })
        nPosTent    := aScan(aDados, {|x| Alltrim(x[1]) == "NumeroTentativas"   })
        nPosAnoRef  := aScan(aDados, {|x| Alltrim(x[1]) == "AnoReferencia"      })
        nPosTipComp := aScan(aDados, {|x| Alltrim(x[1]) == "TipoComprovacao"    })

        If nPosCnpj == 0 .Or. nPosCodigo == 0 .Or. nPosDados == 0 .Or. nPosTent == 0 .Or. nPosAnoRef == 0 .Or. nPosTipComp == 0

            cJson += '{"Retorno":{  '
            cJson += '"Status":"2",
            cJson += '"Observacao":"Nao foi passado todos parametros que sao obrigatorios, ou algum veio em branco/nulo, favor conferir layout do JSON enviado."},'
            cJson += '"Comprovacao":[{
            cJson += '"ValorComprovado":0}]}

            Self:SetResponse( cJson )

            Return(.T.)

        EndIf

        If lRet //Caso tenha encontrado valor para comparação

            cJson += '{"Retorno":{  '
            cJson += '"Status":"1",
            cJson += '"Observacao":""},'
            cJson += '"Comprovacao":[{
            cJson += '"ValorComprovado":123456}]}
        
        Else

            cJson += '{"Retorno":{  '
            cJson += '"Status":"2",
            cJson += '"Observacao":"Nao foi possivel Identificar o valor de compracao no formulario."},'
            cJson += '"Comprovacao":[{
            cJson += '"ValorComprovado":0}]}

        EndIf

    EndIf

    Self:SetResponse( cJson )

Return(.T.)

//---------------------------------------------------------
/*/{Protheus.doc} InteraConfirmComp
Metodo para enviar que o cliente está  de acordo com o valor comprovado.
/*/
//---------------------------------------------------------

WsRestFul InteraConfirmComp Description "Metodo para enviar que o cliente está  de acordo com o valor comprovado."

	WsMethod Post Description "Metodo para enviar que o cliente está  de acordo com o valor comprovado." WsSyntax "/InteraConfirmComp"
		 
End WsRestFul

WsMethod Post WsService InteraConfirmComp

Local oJson         := Nil
Local aJSonField    := {}
Local lRet          := .F.
Local cPropDados    := IIf( Empty( Self:GetContent() ), '', Self:GetContent() )
Local cJson         := ""
Local nRetParser    := 0
Local aDados        := {}
Local nPosCnpj      := 0
Local nPosCodigo    := 0
Local nPosValAprov  := 0
Local nPosAnoRef    := 0
Local nPosTipComp   := 0
Local nPosTipDoc    := 0

    oJSon := TJSonParser():New()
    lRet  := oJSon:Json_Parser( cPropDados /*strJson*/,Len( cPropDados ) /*lenStrJson*/, @aJSonField /*@jsonfields*/, @nRetParser /*@nRetParser*/ )

    // ----------------------------------
	// Define o tipo de retorno do método
	// ----------------------------------
    ::SetContentType("application/json")

    If Len( aJSonField ) <= 0 .Or. !lRet
	
		cJson += '{"Retorno":{'
        cJson += '"Status":"2",'
        cJson += '"Observacao":"Nao foi possivel realizar o parser do JSON enviado, favor conferir layout."}}'
        
	Else

        aDados := aJSonField[1][2]

        nPosCnpj        := aScan(aDados, {|x| Alltrim(x[1]) == "CNPJPai"            })
        nPosCodigo      := aScan(aDados, {|x| Alltrim(x[1]) == "CodigoCliente"      })
        nPosValAprov    := aScan(aDados, {|x| Alltrim(x[1]) == "ValorAprovado"      })
        nPosAnoRef      := aScan(aDados, {|x| Alltrim(x[1]) == "AnoReferencia"      })
        nPosTipComp     := aScan(aDados, {|x| Alltrim(x[1]) == "TipoComprovacao"    })
        nPosTipDoc      := aScan(aDados, {|x| Alltrim(x[1]) == "TipoDocumento"      })

        If nPosCnpj == 0 .Or. nPosCodigo == 0 .Or. nPosValAprov == 0 .Or. nPosAnoRef == 0 .Or. nPosTipComp == 0 .Or. nPosTipDoc == 0

            cJson += '{"Retorno":{'
            cJson += '"Status":"2",'
            cJson += '"Observacao":"Nao foi passado todos parametros que sao obrigatorios, ou algum veio em branco/nulo, favor conferir layout do JSON enviado."}}'

            Self:SetResponse( cJson )

            Return(.T.)

        EndIf

        If lRet //Caso consiga confirmar o valor comprovado.

            cJson += '{"Retorno":{'
            cJson += '"Status":"1",'
            cJson += '"Observacao":""}}'
        
        Else

            cJson += '{"Retorno":{'
            cJson += '"Status":"2",'
            cJson += '"Observacao":"Nao foi possivel realizar a confirmacao do valor comprovado."}}'

        EndIf

    EndIf

    Self:SetResponse( cJson )

Return(.T.)

//---------------------------------------------------------------
/*/{Protheus.doc} InteraMemoCalc
Metodo para retornar a memoria de Calculo.
/*/
//---------------------------------------------------------------

WsRestFul InteraMemoCalc Description "Metodo para retornar a memoria de Calculo."

	WsData CNPJPai          AS String
    WsData CodigoCliente    AS String
    WsData CNPJFilho        AS String Optional 
    WsData AnoReferencia    AS String
    
    WsMethod Get Description "Metodo para retornar a memoria de Calculo." WsSyntax "/InteraMemoCalc"
		 
End WsRestFul

WsMethod Get WsService InteraMemoCalc

Local cJson         := ""
Local cCNPJ         := ""
Local cCodigo       := ""
Local cCnpjFilho    := ""
Local cAnoRef       := ""
Local lRet          := .F.

    // ----------------------------------
	// Define o tipo de retorno do método
	// ----------------------------------
	::SetContentType("application/json")

    If Len(::aURLParms) > 0
		cCNPJ	    := ::aURLParms[1]
		cCodigo	    := ::aURLParms[2]
        cCnpjFilho  := ::aURLParms[3]
		cAnoRef	    := ::aURLParms[4]
    Else
		cCNPJ	    := ::CNPJPai
		cCodigo 	:= ::CodigoCliente
        cCnpjFilho  := ::CNPJFilho
		cAnoRef 	:= ::AnoReferencia
    EndIf

    If Empty(cCNPJ) .Or. Empty(cCodigo) .Or. Empty(cAnoRef) 
        
        cJson += '{"Retorno":{'
        cJson += '"Status":"2",'
        cJson += '"Observacao":"Nao foi passado todos parametros que sao obrigatorios, ou algum veio em branco/nulo."},'
        cJson += '"Calculos":[{'
        cJson += '"AnoReferencia":""}]}'

        Self:SetResponse( cJson )
        Return(.T.)

    EndIf

    If lRet //Se encontrar dados para retornar.
        
        cJson += '{"Retorno":{'
        cJson += '"Status":"1",'
        cJson += '"Observacao":""},'
        cJson += '"Calculos":[{'
        cJson += '"AnoReferencia":"2018"}]}'

    Else

        cJson += '{"Retorno":{'
        cJson += '"Status":"2",'
        cJson += '"Observacao":"Sem registros para retornar."},'
        cJson += '"Calculos":[{'
        cJson += '"AnoReferencia":""}]}'

    EndIf

    Self:SetResponse( cJson )

Return(.T.)

//---------------------------------------------------------
/*/{Protheus.doc} InteraAtuStatus
Altera o status do registro caso a metrica seja confirmada.
/*/
//---------------------------------------------------------

WsRestFul InteraAtuStatus Description "Altera o status do registro caso a metrica seja confirmada."

	WsMethod Post Description "Altera o status do registro caso a metrica seja confirmada." WsSyntax "/InteraAtuStatus"
		 
End WsRestFul

WsMethod Post WsService InteraAtuStatus

Local oJson         := Nil
Local aJSonField    := {}
Local lRet          := .F.
Local cPropDados    := IIf( Empty( Self:GetContent() ), '', Self:GetContent() )
Local cJson         := ""
Local nRetParser    := 0
Local aDados        := {}
Local nPosContra    := 0
Local nPosRevContra := 0
Local nPosCnpjPai   := 0
Local nPosCnpjAgr   := 0
Local nPosRevIntera := 0
Local nPosAnoRef    := 0

    oJSon := TJSonParser():New()
    lRet  := oJSon:Json_Parser( cPropDados /*strJson*/,Len( cPropDados ) /*lenStrJson*/, @aJSonField /*@jsonfields*/, @nRetParser /*@nRetParser*/ )

    // ----------------------------------
	// Define o tipo de retorno do método
	// ----------------------------------
    ::SetContentType("application/json")

    If Len( aJSonField ) <= 0 .Or. !lRet
	
		cJson += '{"Retorno":{  '
        cJson += '"Status":"2",
        cJson += '"Observacao":"Nao foi possivel realizar o parser do JSON enviado, favor conferir layout."}}'
        
	Else

        aDados := aJSonField[1][2]

        nPosContra    := aScan(aDados, {|x| Alltrim(x[1]) == "Contrato"           })
        nPosRevContra := aScan(aDados, {|x| Alltrim(x[1]) == "RevisaoContra"      })
        nPosCnpjPai   := aScan(aDados, {|x| Alltrim(x[1]) == "CNPJPai"            })
        nPosCnpjAgr   := aScan(aDados, {|x| Alltrim(x[1]) == "CNPJAgr"            })
        nPosRevIntera := aScan(aDados, {|x| Alltrim(x[1]) == "RevIntera"          })
        nPosAnoRef    := aScan(aDados, {|x| Alltrim(x[1]) == "AnoRef"             })

        If nPosContra == 0 .Or. nPosRevContra == 0 .Or. nPosCnpjPai == 0 .Or. nPosCnpjAgr == 0 .Or. nPosRevIntera == 0 .Or. nPosAnoRef == 0

            cJson += '{"Retorno":{  '
            cJson += '"Status":"2",
            cJson += '"Observacao":"Nao foi passado todos parametros ou algum veio em branco e todos sao obrigatorios."}}'

            Self:SetResponse( cJson )

            Return(.T.)

        EndIf

        DbSelectArea("PQA")
        DbOrderNickName("ATUSTATUS")

        If PQA->(DbSeek(xFilial("PQA") + AvKey(aDados[nPosContra][2],"PQA_CONTRA") + aDados[nPosRevContra][2] + aDados[nPosCnpjPai][2] + aDados[nPosCnpjAgr][2] + aDados[nPosRevIntera][2] + aDados[nPosAnoRef][2]))
		
			PQA->(RecLock("PQA",.F.))
			
				PQA->PQA_STATUS := "A"
			
			PQA->(MsUnlock())

            cJson += '{"Retorno":{  '
            cJson += '"Status":"1",
            cJson += '"Observacao":""}}'
           
        Else

            cJson += '{"Retorno":{  '
            cJson += '"Status":"2",
            cJson += '"Observacao":"Nao foi encontrado metrica informada com esses dados, favor conferir informacoes enviadas no JSON"}}'
            
        EndIf

    EndIf

    Self:SetResponse( cJson )

Return(.T.)