﻿#Include 'TOTVS.CH'
#Include 'FWMVCDEF.CH'
#Include 'TCRMA159.CH'
 
/*/{Protheus.doc} TCRMA159
//TODO Monta a tela de seleção de produto x arquiteto
<AUTHOR> 
@since 11/12/2019
@version 1.0
@type function
/*/
User Function TCRMA159(cCodAgr, cCodNiv, oModel, oView)
Local aCordsMain	:= FWGetDialogSize(oMainWnd)				// Objeto que recebe as cordenadas da Dialog Principal do Protheus.
Local oDlgPrinc		:= Nil
Local oFWLayer		:= Nil										// Outros Objetos necessarios.
Local nOpca			:= 0
Local oTable		:= Nil

	Private oPanelUp, oPanelBot										// Objetos que recebem os Paineis que irÃ£o conter os Browses.
	Private aStrUp		:= {}										// Arrays que recebem todos os campos, no elemento [1] os da tabela temporaria e no [2] os do Browse. 
	Private oBrowseUp	:= Nil										// Objetos que recebem os Browses.

	Default cCodAgr		:= ""
	Default cCodNiv		:= ""
	Default oModel		:= FWModelActive()
	Default oView		:= FWViewActive()

	//MsDialog que cobre 100% da tela, e que ira conter os FWLayer.
	oDlgPrinc := MSDialog():New(aCordsMain[1], aCordsMain[2], aCordsMain[3], aCordsMain[4], STR0001,,,,,CLR_BLACK,CLR_WHITE,,,.T.) 	//'Produtos x Arquitetos'

	oFWLayer :=  T159LAYER(oDlgPrinc)	// Cria as Layers(divisores)

	// Obtem os objetos(Conteiners) das colunas das Layers. 
	oPanelUp	:= oFWLayer:GetColPanel( 'ALL', 'CIMA' )	// Retorna o Panel que esta contido na linha de cima.
	oPanelBot	:= oFWLayer:GetColPanel( 'ALL', 'BOTAO' )	// Retorna o Panel que esta contido na linha do botão.
	
	// Monta as Estruturas dos Campos das Tabelas Temporarias e dos Browses.
	aStrUp		:= T159STRUT()		// Monta Estrutura dos Campos de Cima da Tabela Temporaria e do Browse.
	
	// Cria as Tabelas Temporarias e grava os dados 
	oTable	:= T159TMP(cCodAgr, cCodNiv)		// Tabela do Browse de Cima

	// Cria os Browses
	oBrowseUp		:= T159MRBROW()		// Browse que contem os Registros de produtos

	@  10,530 Button STR0002	Size 50,15 Of oPanelBot Pixel Action ( nOpca := 1, oDlgPrinc:End() )	//"Confirmar"
	@  10,600 Button STR0003	Size 50,15 Of oPanelBot Pixel Action ( nOpca := 0, oDlgPrinc:End() )	//"Cancelar"

	oDlgPrinc:Activate(,,,.T.,{|| T159VLD(oModel, oView, nOpca) },,{|| })

	If nOpca == 1
		T159CONF(oModel, oView)
	EndIf
	
	// Remove a tabela temporᲩa  
	oTable:Delete()	
Return nil

/*/{Protheus.doc} T159LAYER
//TODO Monta o Layer e divide a tela em percentuais para acoplar os browses
<AUTHOR> Ribeiro 
@since 11/12/2019
@version 1.0
@return object, objeto layer

@type function
/*/
Static Function T159LAYER(oDlgPrinc)
	Local oFWLayer

	// Instancia no FWLayer.
	oFWLayer := FWLayer():New() // FWLayer, classe utilizada para dividir a tela em conteiners para guardar outros objetos.
	oFWLayer:Init(oDlgPrinc, .F., .T.) // Inicializa o FWLayer na MsDialog Principal.

	//Divida as linhas.
	oFWLayer:AddLine( 'CIMA', 90, .F. )		// Cria uma Linha com o ID de CIMA que ocupa 45% superior da tela.
	oFWLayer:AddLine( 'BOTAO', 10, .F. )	// Cria uma linha com o ID de BOTAO que ocupada 10% inferior da tela.

	// Divide as colunas da linha de cima.
	oFWLayer:AddCollumn( 'ALL', 100, .T., 'CIMA' ) // Cria uma coluna que ocupa 100% da parte de cima da linha de ID UP.

	// Divide as colunas da linha do Bot㯮
	oFWLayer:AddCollumn( 'ALL', 100, .T., 'BOTAO' )	// Cria uma coluna que ocupa 100% da parte do Bot㯠da linha de ID BOTAO.

Return oFWLayer

/*/{Protheus.doc} T159STRUT
//TODO Cria Estrutura da tabela
<AUTHOR> Ribeiro 
@since 11/12/2019
@version 1.0
@return array, estrutura tabela temporᲩa

@type function
/*/
Static Function T159STRUT()
	Local aStrUp		:= {} // Array Com a Estrutura de Ambos
	Local aStruTmpUp	:= {} // Array Com a estrutura dos campos da tabela temporaria
	Local aStruBrwUp	:= {} // Array com a estrutura dos campos do Browse

	// Estrutura Para Tabela Temporaria
	// {Nome, Tipo, Tamanho, Decimal}
	aAdd(aStruTmpUp, {"PTB_OK"		,"C"	,TamSX3("PTB_OK")[1]		,TamSX3("PTB_OK")[2]		})	// Mark
	aAdd(aStruTmpUp, {"PTB_CATEGO"	,"C"	,TamSX3("PTB_CATEGO")[1]	,TamSX3("PTB_CATEGO")[2]	})	// Categoria
	aAdd(aStruTmpUp, {"PTB_DESCAT"	,"C"	,TamSX3("PTB_DESCAT")[1]	,TamSX3("PTB_DESCAT")[2]	})	// descri磯
	aAdd(aStruTmpUp, {"PTB_VEND"	,"C"	,TamSX3("PTB_VEND")[1]		,TamSX3("PTB_VEND")[2]		})	// Vendedor
	aAdd(aStruTmpUp, {"PTB_NOME"	,"C"	,TamSX3("PTB_NOME")[1]		,TamSX3("PTB_NOME")[2]		})	// Nome

	// Estrutura Para os campos do Browse
	// {descri磯 do campo, Nome do campo, Tipo, Tamanho, Decimal, Picture}
	aAdd(aStruBrwUp, {"Categoria"	, "PTB_CATEGO"	, "C", TamSX3("PTB_CATEGO")[1]	, TamSX3("PTB_CATEGO")[2]	, PesqPict("PTB","PTB_CATEGO")	}) // Produto
	aAdd(aStruBrwUp, {"Descri磯"	, "PTB_DESCAT"	, "C", TamSX3("PTB_DESCAT")[1]	, TamSX3("PTB_DESCAT")[2]	, PesqPict("PTB","PTB_DESCAT")	}) // descri磯
	aAdd(aStruBrwUp, {"Arquiteto"	, "PTB_VEND"	, "C", TamSX3("PTB_VEND")[1]	, TamSX3("PTB_VEND")[2]		, PesqPict("PTB","PTB_VEND")	}) // Vendedor
	aAdd(aStruBrwUp, {"Nome"		, "PTB_NOME"	, "C", TamSX3("PTB_NOME")[1]	, TamSX3("PTB_NOME")[2]		, PesqPict("PTB","PTB_NOME")	}) // Nome

	aadd(aStrUp, aStruTmpUp) // Primeira Posi磯 recebe os campos da tabela temporaria
	aadd(aStrUp, aStruBrwUp) // Segunda Posi磯 recebe os campos do browse

Return aStrUp 

/*/{Protheus.doc} T159TMP
//TODO Cria a Tabela temporaria do Browse
<AUTHOR> Ribeiro 
@since 11/12/2019
@version 1.0
@return object, objeto de tabela temporᲩa

@type function
/*/
Static Function T159TMP(cCodAgr, cCodNiv)
Local cQuery	:= ""
Local oTable	:= Nil
Local aField	:= {}
	
	// Define o objeto da tabela temporᲩa  
	oTable := FWTemporaryTable():New( "TMP1" )
	
	// Monta a estrutua da tabela
	aAdd( aField, {"PTB_CATEGO"		,"C" ,TamSX3("PTB_CATEGO")[01]	,TamSX3("PTB_CATEGO")[01]} )
	aAdd( aField, {"PTB_DESCAT"		,"C" ,TamSX3("ACU_DESC")[01]	,TamSX3("ACU_DESC")[02]})
	aAdd( aField, {"PTB_VEND"		,"C" ,TamSX3("PTB_VEND")[01]	,TamSX3("PTB_VEND")[02]})
	aAdd( aField, {"PTB_NOME"		,"C" ,TamSX3("A3_NOME")[01]		,TamSX3("A3_NOME")[02]})
	aAdd( aField, {"PTB_OK"			,"C" ,TamSX3("PTB_OK")[01]		,TamSX3("PTB_OK")[02]})

	// Atribui campo
	oTable:SetFields( aField )

	// Define os �ices  
	oTable:AddIndex("1", {"PTB_CATEGO"})

	// Cria a tabela
	oTable:Create()

	cQuery := "SELECT PTB_CATEGO, ACU_DESC PTB_DESCAT, PTB_VEND, A3_NOME PTB_NOME, PTB_OK "
	cQuery += "FROM " + RetSqlName("PTB") + " PTB "
	cQuery += "INNER JOIN " + RetSqlName("ACU") + " ACU ON ACU_FILIAL = '" + xFilial("ACU") + "' AND ACU_COD = PTB_CATEGO AND ACU.D_E_L_E_T_ = ' ' "
	cQuery += "INNER JOIN " + RetSqlName("SA3") + " SA3 ON A3_FILIAL = '" + xFilial("SA3") + "' AND A3_COD = PTB_VEND AND SA3.D_E_L_E_T_ = ' ' "
	cQuery += "WHERE PTB_FILIAL = '" + xFilial("PTB") + "' "
	cQuery += "  AND PTB_CODAGR = '" + cCodAgr + "' "
	cQuery += "  AND PTB_CODNIV = '" + cCodNiv + "' "
	cQuery += "  AND PTB.D_E_L_E_T_ = ' ' "
                                   
	SqlToTrb(cQuery, aStrUp[1], 'TMP1')

Return oTable


/*/{Protheus.doc} T159MRBROW
//TODO Fun磯 que cria o markbrowse
<AUTHOR> Ribeiro 
@since 11/12/2019
@version 1.0
@return object, objetc markbrowse

@type function
/*/
Static Function T159MRBROW()
	Local oBrowseUp // Variavel que recebe o Browse

	oBrowseUp:= FWMarkBrowse():New()		// Instancia o Browse 
	oBrowseUp:SetOwner( oPanelUp )			// Vincula o Browse a tela de cima                                             
	oBrowseUp:SetDescription( STR0001 )		// Define descri磯 - "Arquitetos x Produtos"
	oBrowseUp:SetFieldMark("PTB_OK")		// Define campo de marca磯
	oBrowseUp:SetAlias( 'TMP1' )			// Define a Tabela
	oBrowseUp:SetFields(aStrUp[2])			// Define a estrutura dos campos
	oBrowseUp:SetTemporary()				// Define que usa uma tabela temporaria
	oBrowseUp:SetMenuDef( '' )				// Define de onde virao os Botoes do browse
	oBrowseUp:SetIgnoreARotina(.T.)			// Ignora o arotina
	oBrowseUp:DisableDetails()				// Desabilita os detalhes
	oBrowseUp:DisableReport()				// Desabilita a impressÃ£o do browse
	oBrowseUp:SetProfileID( '1' )			// identificador (ID) para o Browse 
	oBrowseUp:ForceQuitButton()				// for硠exibi磯 do Bot㯠de Sair                                    
	oBrowseUp:Activate()					// Ativa o Browse

Return oBrowseUp

/*/{Protheus.doc} T159VLD
//TODO Fun磯 criada para validar se hᠡlgum registro selecionado
<AUTHOR> Ribeiro 
@since 11/12/2019
@version 1.0
@return logical, valida磯

@type function
/*/
Static Function T159VLD(oModel, oView, nOpca)
	Local lRet 		:= .F.
	Local lRetTMP1	:= .F.
	Local aArea		:= GetArea()
	Local aAreaTMP1	:= TMP1->(GetArea())
	Local aCateg	:= {}
	Local cError	:= ""

	Default oModel	:= FWModelActive()
	Default oView	:= FWViewActive()
	Default nOpca	:= 0

	If nOpca == 1

		If !Empty(oModel:aErrorMessage[3])

			cError += oModel:aErrorMessage[3] + CRLF
			cError += oModel:aErrorMessage[4] + CRLF
			cError += oModel:aErrorMessage[5] + CRLF
			cError += oModel:aErrorMessage[6] + CRLF
			cError += oModel:aErrorMessage[7] + CRLF

			Help(NIL, NIL, "Inclus㯠de Arquiteto", NIL, "Inconsistꮣia na sele磯 de arquitetos", 1, 0, NIL, NIL, NIL, NIL, NIL, {cError})
			lRet := .F.
			FwClearHLP()
			oModel:aErrorMessage[3] := ""
			oModel:aErrorMessage[4] := ""
			oModel:aErrorMessage[5] := ""
			oModel:aErrorMessage[6] := ""
			oModel:aErrorMessage[7] := ""
			oModel:aErrorMessage[8] := ""
			oModel:aErrorMessage[9] := ""
		Else

			dbSelectArea("TMP1")
			TMP1->(dbGoTop())

			While !TMP1->(Eof())
				If !Empty(TMP1->PTB_OK)
					If aScan(aCateg,{|x| x == TMP1->PTB_CATEGO}) == 0
						aAdd(aCateg, TMP1->PTB_CATEGO)
					EndIf
					lRetTMP1 := .T.
				EndIf
				TMP1->(dbSkip())
			End

			If lRetTMP1
				// Valida磯 para verificar se todos os arquitetos pertencem a mesma categoria
				If Len(aCateg) > 1
					lRet := .F.
					MsgStop('S󠰯dem ser selecionados arquitetos da mesma categoria')
				Else
					Help( ,, 'T159VLD',,"Arquiteto(s) adicionado(s) com sucesso!", 1, 0 ) //"Participante preenchido com sucesso no time de vendas. "
					lRet := .T.
				EndIf
			Else
				MsgStop(STR0005)	//'Pelo menos um registro deverᠳer selecionado!'
			EndIf
		EndIf
	Else
		
		lRet := .T.
	EndIf

	RestArea(aArea)
	RestArea(aAreaTMP1)
Return(lRet)

/*/{Protheus.doc} T159CONF
//TODO Fun磯 criada para adicionar o arquiteto na oportunidade
<AUTHOR> Ribeiro 
@since 11/12/2019
@version 1.0
@type function
/*/
Static Function T159CONF(oModel, oView, cCodVend)
	Local aArea		:= GetArea()
	Local aAreaTMP1	:= TMP1->(GetArea())
	Local oMdlAD2	:= Nil
	Local nI		:= 0
	Local nLinha	:= 0

	Default oModel	:= FWModelActive()
	Default oView	:= FWViewActive()
	Default	cCodVend := ''

	oMdlAD2 := oModel:GetModel("AD2DETAIL")

	dbSelectArea("TMP1")
	TMP1->(dbGoTop())

	While !TMP1->(Eof())
		If !Empty(TMP1->PTB_OK)
			If oMdlAD2:Length() > 1
				For nI := 1 To oMdlAD2:Length()
					oMdlAD2:GoLine(nI)
					nLinha++
					If !oMdlAD2:IsDeleted()
						If Empty(oMdlAD2:GetValue("AD2_VEND"))
							Exit
						EndIf
					EndIf
				Next nI
				If nLinha == oMdlAD2:Length()
					oMdlAD2:GoLine(nLinha)
					If !Empty(oMdlAD2:GetValue("AD2_VEND")) .Or. oMdlAD2:IsDeleted()
						oMdlAD2:AddLine()
					EndIf
					nLinha := 0
				Else
					oMdlAD2:GoLine(nLinha)
					nLinha := 0
				EndIf
			Else
				oMdlAD2:GoLine(1)
			EndIf
			oMdlAD2:SetValue("AD2_VEND",TMP1->PTB_VEND)
			cCodVend	:= TMP1->PTB_VEND
		EndIf
		TMP1->(dbSkip())
	End

	oView:Refresh()

	RestArea(aArea)
	RestArea(aAreaTMP1)
Return nil
