#Include 'PROTHEUS.CH'
#Include 'FWMVCDEF.CH'
#Include 'TCRMA158.CH'
  
/*/{Protheus.doc} TCRMA158
//TODO Função que cria a tela de cadastro de Agrupador x Nível x Arquiteto x Categoria
<AUTHOR> 
@since 05/12/2019
@version 1.0
@return ${return}, ${return_description} 

@type function
/*/
User Function TCRMA158()
	Local oBrowse := Nil

	oBrowse := FWMBrowse():New()
	oBrowse:SetAlias('PTB')
	oBrowse:SetDescription(STR0001)	//Cadastro Arquiteto x Categoria
	oBrowse:Activate()

Return

/*/{Protheus.doc} MenuDef
//TODO Função que cria o menu funcional
<AUTHOR> Ribeiro 
@since 05/12/2019
@version 1.0
@return ${return}, ${return_description}

@type function
/*/
Static Function MenuDef()
	Local aRotina := {}

	ADD OPTION aRotina TITLE STR0002 ACTION 'VIEWDEF.TCRMA158' OPERATION 2 ACCESS 0	// 'Visualizar'
	ADD OPTION aRotina TITLE STR0003 ACTION 'VIEWDEF.TCRMA158' OPERATION 3 ACCESS 0	// 'Incluir'
	ADD OPTION aRotina TITLE STR0004 ACTION 'VIEWDEF.TCRMA158' OPERATION 4 ACCESS 0	// 'Alterar'
	ADD OPTION aRotina TITLE STR0005 ACTION 'VIEWDEF.TCRMA158' OPERATION 5 ACCESS 0	// 'Excluir'
	ADD OPTION aRotina TITLE STR0006 ACTION 'VIEWDEF.TCRMA158' OPERATION 8 ACCESS 0	// 'Imprimir'

Return aRotina

/*/{Protheus.doc} ModelDef
//TODO Função que cria o modelo de dados
<AUTHOR> Ribeiro 
@since 05/12/2019
@version 1.0
@return ${return}, ${return_description}

@type function
/*/
Static Function ModelDef()
	Local oStruPTB		:= FWFormStruct(1, 'PTB', {|cCampo| AllTrim(cCampo) $ "PTB_CODAGR|PTB_RESUMO|PTB_CODNIV|PTB_DESCRI"}/*bAvalCampo*/, /*lViewUsado*/)
	Local oStruPTB2		:= FWFormStruct(1, 'PTB', {|cCampo| !(AllTrim(cCampo) $ "PTB_CODAGR|PTB_RESUMO|PTB_CODNIV|PTB_DESCRI")}/*bAvalCampo*/, /*lViewUsado*/)
	Local oModel		:= Nil
	Local bLoadPTB		:= {|oModel,Y| LoadPTB(oModel,Y) }

	oModel := MPFormModel():New('MODELPTB', /*bPreValidacao*/, /*bPosValidacao*/, /*bCommit*/, /*bCancel*/ )
	oModel:AddFields('DADOSPTB'	, /*cOwner*/, oStruPTB, /*bPreValidacao*/, /*bPosValidacao*/, /*bCarga*/ )
	oModel:AddGrid('DADOSPTB2'	,'DADOSPTB'	, oStruPTB2,,,,,bLoadPTB/*bLoadItens*/)
	oModel:SetDescription(STR0007)							// "Dados AmarraÃ§Ã£o Arquitetos x Categoria"
	oModel:SetPrimaryKey({"PTB_FILIAL", "PTB_CODAGR", "PTB_CODNIV", "PTB_VEND", "PTB_CATEGO"})
	oModel:GetModel('DADOSPTB2'):SetUniqueLine({ 'PTB_VEND', 'PTB_CATEGO' })
	oModel:GetModel('DADOSPTB'):SetDescription(STR0008)		// "Agrupadores"
	oModel:GetModel('DADOSPTB2'):SetDescription(STR0009)	// "Arquitetos x Categoria"
	oModel:SetRelation('DADOSPTB2',{{'PTB_FILIAL','xFilial("PTB")'},{'PTB_CODAGR','PTB_CODAGR'},{'PTB_CODNIV','PTB_CODNIV'}},PTB->(IndexKey(1)))

Return(oModel)

/*/{Protheus.doc} ViewDef
//TODO Função que cria a view
<AUTHOR>
@since 05/12/2019
@version 1.0
@return ${return}, ${return_description}

@type function
/*/
Static Function ViewDef()
	Local oModel	:= FWLoadModel('TCRMA158')
	Local oStruPTB	:= FWFormStruct(2,'PTB',{|cCampo| AllTrim(cCampo) $ "PTB_CODAGR|PTB_RESUMO|PTB_CODNIV|PTB_DESCRI"})
	Local oStruPTB2	:= FWFormStruct(2,'PTB',{|cCampo| !(AllTrim(cCampo) $ "PTB_CODAGR|PTB_RESUMO|PTB_CODNIV|PTB_DESCRI")})
	Local oView		:= Nil

	oStruPTB:RemoveField('PTB_VEND')
	oStruPTB:RemoveField('PTB_NOME')
	oStruPTB:RemoveField('PTB_CATEGO')
	oStruPTB:RemoveField('PTB_DESCAT')

	oStruPTB2:RemoveField('PTB_CODAGR')
	oStruPTB2:RemoveField('PTB_RESUMO')
	oStruPTB2:RemoveField('PTB_CODNIV')
	oStruPTB2:RemoveField('PTB_DESCRI')

	oView := FWFormView():New()
	oView:SetModel(oModel)
	oView:AddField('FORM1', oStruPTB, 'DADOSPTB')
	oView:AddGrid('FORM2', oStruPTB2, 'DADOSPTB2')
	oView:CreateHorizontalBox('SUPERIOR', 30)
	oView:CreateHorizontalBox('INFERIOR', 70)
	oView:SetOwnerView('FORM1', 'SUPERIOR')
	oView:SetOwnerView('FORM2', 'INFERIOR')

	oView:EnableTitleView('FORM1' , STR0008)	// "Agrupadores"
	oView:EnableTitleView('FORM2' , STR0009)	// "Arquitetos x Categoria"

Return(oView)

/*/{Protheus.doc} T158PTBVld
//TODO Verifica amarraÃ§Ã£o de Agrupador x Arquiteto X Categoria
<AUTHOR> Ribeiro 
@since 05/12/2019
@version 1.0
@return ${return}, ${return_description}

@type function
/*/
User Function T158PTBVld(cCodAgr, cCodNiv)
	Local aArea		:= GetArea()
	Local lRet		:= .F.
	Local cQryPTB	:= GetNextAlias()

	BeginSQL Alias cQryPTB

		SELECT COUNT(PTB_CODNIV) PTB_CODNIV
		FROM %table:PTB% PTB
		WHERE PTB_FILIAL = %exp:xFilial("PTB")%
		  AND PTB_CODAGR = %exp:cCodAgr%
		  AND PTB_CODNIV = %exp:cCodNiv%
		  AND PTB.%notdel%

	EndSQL
			
	If (cQryPTB)->PTB_CODNIV > 0
		lRet := .T.
	EndIf

	(cQryPTB)->( DbCloseArea() )

	RestArea(aArea)
Return(lRet)

/*/{Protheus.doc} LoadPTB
//TODO Função que carrega os dados da grip da tabela PTB
<AUTHOR> Ribeiro 
@since 05/12/2019
@version 1.0
@return ${return}, ${return_description}

@type function
/*/
Static Function LoadPTB(oModel)
	Local aArea		:= GetArea()
	Local aAreaPTB	:= PTB->(GetArea())
	Local cCodAgr	:= PTB->PTB_CODAGR
	Local cCodNiv	:= PTB->PTB_CODNIV
	Local cVend		:= PTB->PTB_VEND
	Local cProduto	:= PTB->PTB_CATEGO
	Local aRet		:= {}

	If oModel:GetOperation() == MODEL_OPERATION_UPDATE

		dbSelectArea("PTB")
		dbSetOrder(1)

		PTB->(dbGoTop())

		dbSeek(xFilial("PTB") + cCodAgr + cCodNiv)

		While !PTB->(Eof()) .And. PTB->(PTB_FILIAL+PTB_CODAGR+PTB_CODNIV) == xFilial("PTB")+cCodAgr+cCodNiv
			aAdd(aRet, {PTB->(Recno()), {	xFilial("PTB"),;
											PTB->PTB_VEND,;
											Posicione("SA3",1,xFilial("SA3")+PTB->PTB_VEND,"A3_NOME"),;
											PTB->PTB_CATEGO,;
											Posicione("ACU",1,xFilial("ACU")+PTB->PTB_CATEGO,"ACU_DESC"),;
											"  " } } )
			PTB->(dbSkip())
		End

	EndIf

	RestArea(aArea)
	RestArea(aAreaPTB)
Return(aRet)
