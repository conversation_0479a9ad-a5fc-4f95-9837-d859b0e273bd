#Include 'Protheus.ch'
#Include 'ACCSTM10.ch'
#Include "TopConn.ch"

/*/{Protheus.doc} TCMSS003
	Api Callback do Interceptor para trazer os dados dos pedidos de vendas para comissoes
	@type   Function
	<AUTHOR> Menabue Lima
	@since 14/12/2021
	@version 1.0
/*/
 

User Function TCMSS003()
Local aRequest  	:= PARAMIXB //Parametros recebidos do Interceptor  [1]= Codigo da Requisicao P37_COD [2]= Body Recebido da requisicao  P37_BODY
Local oJson     	:= JsonObject():new()
Local oBody     	:= oJson:FromJson( aRequest[2] )
Local cPerg			:= PADR("ACCSTM10",10)
local cTpforn       := GetMV("TI_TPFORN",,"1*2*3*4")//TIBACKOP-1354
Private aList		:= {{{"",""},"","","","",0,.F.,""}}
Private cAliasSE3	:= GetNextAlias()
Private oObjList
Private cMsgError	:= ""
Private dDtEmissE3
Private lNMG := GetMv("MV_#NEWCC")	
If ValType( oBody ) == "U"
  
	c_par01:=oJson:GetJsonObject( "codigo_ar_de" )
	c_par02:=oJson:GetJsonObject( "codigo_ar_ate" )
	c_par03:=oJson:GetJsonObject( "dtRef" )
	c_par04:=oJson:GetJsonObject( "tp_forn" )
	c_par05:=oJson:GetJsonObject( "lst_gerados" )
	c_par06:=oJson:GetJsonObject( "tp_agn" )

	If (c_par01 != Nil .And. c_par02 != Nil .And. c_par03 != Nil .And. c_par04 != Nil .And. c_par05 != Nil .And. c_par06 != Nil )
		If cValtochar(c_par04) $ cTpforn//TIBACKOP-1354
	 		Pergunte(cPerg,.F.)
			mv_par01:=c_par01
			mv_par02:=c_par02
			mv_par03:=sTOd(c_par03)
			mv_par04:=c_par04
			mv_par05:=c_par05
			mv_par06:=c_par06
			cJson:=fProcessa()
		else
			cJson:= '{ "erros": "Valor do Parametro tp_forn ('+cValtochar(c_par04)+') nao e valido, valores validos: '+cTpforn+'"}  '//TIBACKOP-1354
		EndIf
	Else
		cJson:= '{ "erros": "Parametros com formato invalidos"}  '
	EndIF
Else
	cJson:= '{ "erros": "Json com formato invalido"}  '
EndIF
RecLock("P37", .F.)
	P37->P37_BODYRP:= cJson
MsUnlock()
 

Return


/*
ÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœ
Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±
Â±Â±Ã‰Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‘Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‹Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‘Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‹Ã�Ã�Ã�Ã�Ã�Ã�Ã‘Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â»Â±Â±
Â±Â±ÂºPrograma  Â³fProcessa ÂºAutor  Â³Microsiga           Âº Data Â³  03/05/13   ÂºÂ±Â±
Â±Â±ÃŒÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã˜Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�ÃŠÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�ÃŠÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â¹Â±Â±
Â±Â±ÂºDesc.     Â³Filtra registros e apresenta na markbrowse.                 ÂºÂ±Â±
Â±Â±Âº          Â³                                                            ÂºÂ±Â±
Â±Â±ÃŒÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã˜Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â¹Â±Â±
Â±Â±ÂºUso       Â³ AP                                                         ÂºÂ±Â±
Â±Â±ÃˆÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â¼Â±Â±
Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±
ÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸ
*/
Static Function fProcessa()

	Local cQuery
	Local cParceiro 	:= GetMV("TDI_PARCTB",,"'P  ','S  ','NEO','PAR'")
	Local cParcSA3		:= GetMV("TDI_PARSA3",,"'PPC'")
	Local cAgnNAO		:= GetMV("TDI_PEDAGN",,"'CC5'")
	Local aStruQry		:= {}
	Local cAGN 			:= ""

	dDtEmissE3 := MV_PAR03

	If MV_PAR04 == 1  .OR. MV_PAR04 == 3
		cAgn := " ADK.ADK_XAGN "
	else
		cAgn := " SA3.A3_XAGN  "
	EndIf

	ADK->( dbSetOrder(1) ) //ADK_FILIAL+ADK_COD
	SA3->( dbSetOrder(1) ) //A3_FILIAL+A3_COD
	SA2->( dbSetOrder(1) )
	SC7->( dbSetOrder(1) )
	cQuery := " SELECT " + cAgn + " AGN, SE3.E3_FILIAL, SE3.E3_VEND, SE3.E3_PEDIDO, SE3.E3_EMISSAO, SUM(SE3.E3_COMIS) AS COMPARC"
	cQuery += " FROM "+RetSqlName("SE3")+" SE3"

	If MV_PAR04 == 1  .OR. MV_PAR04 == 3 //Franquia ou Parceiro
		cQuery += " INNER JOIN "+RetSqlName("ADK")+" ADK ON ( ADK.ADK_FILIAL = '"+xFilial("ADK")+"' AND "
		cQuery += "                                           ADK.ADK_COD = SE3.E3_VEND AND "

		//Parceiros
		If MV_PAR04 == 3
			cQuery += "                                        	  ADK.ADK_XAGN IN ("+cParceiro+") AND "
		Else

			If !Empty(mv_par06) .And. mv_par06 != "***"
				cQuery += " 									  ADK.ADK_XAGN IN "+Formatin( mv_par06, ',' )+" AND "
			EndIf

			cQuery += "                                        	  ADK.ADK_XAGN NOT IN ("+cParceiro+") AND "

		EndIf

		cQuery += "                                       	  ADK.D_E_L_E_T_ = ' ' )"

	ElseIf MV_PAR04 == 2  .OR. MV_PAR04 == 4 //Executivos/Franquias PC ou  Parceiros PC

		cQuery += " INNER JOIN "+RetSqlName("SA3")+" SA3 ON ( SA3.A3_FILIAL = '"+xFilial("SA3")+"' AND"
		cQuery += "                                           SA3.A3_COD = SE3.E3_VEND AND"

		//Parceiros PC
		If MV_PAR04 == 4
			cQuery += "										  SA3.A3_XAGN IN ("+cParcSA3+") AND "

		Else

			If !Empty(mv_par06) .And. mv_par06 != "***"
				cQuery += "										  SA3.A3_XAGN IN "+Formatin( mv_par06, ',' )+" AND "
			EndIf


			cQuery += "	SA3.A3_XAGN NOT IN ("+cParcSA3+") AND "

		EndIf

		cQuery += " SA3.A3_XAGN NOT IN ("+cAgnNAO+") AND "

		cQuery += " SA3.D_E_L_E_T_ = ' ' )
	EndIf
	cQuery += " WHERE  SE3.E3_VEND >= '"+MV_PAR01+"' AND SE3.E3_VEND <='"+MV_PAR02+"' AND"

	cQuery += "       SE3.E3_EMISSAO = '"+DTOS(MV_PAR03)+"' AND"

	If MV_PAR05 == 2
		cQuery += "   SE3.E3_PEDIDO = ' ' AND"
	EndIf

	cQuery += "       SE3.D_E_L_E_T_ = ' ' "
	cQuery += " GROUP BY SE3.E3_FILIAL,SE3.E3_VEND,SE3.E3_PEDIDO,SE3.E3_EMISSAO, " + cAgn
	cQuery += " ORDER BY SE3.E3_VEND"

	cQuery := ChangeQuery(cQuery)

	TCQUERY cQuery NEW ALIAS (cAliasSE3)
	aStruQry := (cAliasSE3)->(dbStruct())
	aEval(SE3->(dbStruct()),{|x| If( x[2] != "C" .And. aScan(aStruQry,&("{|y| Alltrim(y[1]) == AllTrim('"+x[1]+"')}")) > 0,;
		TcSetField(cAliasSE3,AllTrim(x[1]),x[2],x[3],x[4]),;
		Nil)})

	dbSelectArea(cAliasSE3)
	(cAliasSE3)->(dbGoTop())




	aList:= {}
	aJson:={}
	cJson:= '{ "pedidos": [ '

	If (cAliasSE3)->(Eof())
		cJson+= '] }'
		Return cJson
	EndIf

	While (cAliasSE3)->(!Eof())
	cFilAnt:=(cAliasSE3)->E3_FILIAL	
		dbSelectArea("SE3")
		SA3->( dbSetOrder(1) ) //A3_FILIAL+A3_COD
		dbSelectArea("SA2")
		SA2->( dbSetOrder(1) )
		dbSelectArea("SC7")
		SC7->( dbSetOrder(1) )
		cCodigoAR := ""
		cChaveSA2 := ""

		If (cAliasSE3)->COMPARC <= 0
			(cAliasSE3)->(dbSkip())
			Loop
		EndIf

		If MV_PAR04 == 2  .OR. MV_PAR04 == 4 	// Executivos/Franquia PC ou Parceiros PC

			If !SA3->( dbSeek(xFilial("SA3")+(cAliasSE3)->E3_VEND,.F.) )
				(cAliasSE3)->(dbSkip())
				Loop
			EndIf

			cCnpj := AllTrim(SA3->A3_CGC)

			SC7->(MSSeek(xFilial("SC7")+(cAliasSE3)->E3_PEDIDO ))
			SA2->(dbSeek(xFilial("SA2") + SC7->(C7_FORNECE + C7_LOJA) ))
			cJson += " {"
			cJson += '"AGN"			:"'+(cAliasSE3)->AGN+'",'
			cJson += '"GERADO"		:"'+if(Empty((cAliasSE3)->E3_PEDIDO),"N","S")+'",'
			cJson += '"FILIAL"		:"'+cFilAnt+'",' // trazer codigo
			cJson += '"CNPJ_TOTVS"	:"'+FWArrFilAtu()[18]+'",' //** Ta trazendo do canal
			cJson += '"NOME_FILIA"	:"'+FWArrFilAtu()[07]+'",'//Trazer codigo
			cJson += '"CODIGOAR"	:"'+Alltrim((cAliasSE3)->E3_VEND)+'",'
			cJson += '"NOME"		:"'+Alltrim(SA3->A3_NOME)+'",'
			cJson += '"RAZAO_SOCI"	:"'+SA2->A2_NOME+'",'
			cJson += '"EMISSAO"		:"'+DtoC((cAliasSE3)->E3_EMISSAO)+'",'
			cJson += '"PEDIDO"		:"'+(cAliasSE3)->E3_PEDIDO+'",'
			cJson += '"VALOR"		:'+cValToChar((cAliasSE3)->COMPARC)+' ,'
			cJson += '"COD_FORN"	:"'+SA2->A2_COD+'",'
			cJson += '"CNPJ_FORN"	:"'+SA2->A2_CGC+'",'
			cJson += '"EMAIL"		:"'+Alltrim(SA2->A2_XMAILCO)+'" '
			cJson +=" }, "

			(cAliasSE3)->(dbSkip())

		Else
		
			If !ADK->( dbSeek(xFilial("ADK")+(cAliasSE3)->E3_VEND,.F.) )
				(cAliasSE3)->(dbSkip())
				Loop
			EndIf

			//------------------------------------------------//
			// BAQUWC - Daniel Constantino - 11/01/2014       //
			//------------------------------------------------//
			// Destinatario de Pagamento. Se o campo          //
			// ADK_XCNPJD estiver preenchido com um CNPJ,     //
			// a rotina busca esse CNPJ na SA2 			    //
			//------------------------------------------------//

			If ADK->(FieldPos("ADK_XCNPJD"))> 0 .And. !EMPTY(ADK->ADK_XCNPJD)
				cCnpj := AllTrim(ADK->ADK_XCNPJD)
			Else
				cCnpj := AllTrim(ADK->ADK_CNPJ)
			EndIf

			cJson += ""
			SC7->(MSSeek(xFilial("SC7")+(cAliasSE3)->E3_PEDIDO ))
			SA2->(dbSeek(xFilial("SA2") + SC7->(C7_FORNECE + C7_LOJA) ))
			
			cJson += " {"
			cJson += '"AGN"			:"'+(cAliasSE3)->AGN+'",'
			cJson += '"GERADO"		:"'+if(Empty((cAliasSE3)->E3_PEDIDO),"N","S")+'",'
			cJson += '"FILIAL"		:"'+cFilAnt+'",'
			cJson += '"CNPJ_TOTVS"	:"'+FWArrFilAtu()[18]+'",'
			cJson += '"NOME_FILIA"	:"'+FWArrFilAtu()[07]+'",'
			cJson += '"CODIGOAR"	:"'+Alltrim((cAliasSE3)->E3_VEND)+'",'
			cJson += '"NOME"		:"'+Alltrim(ADK->ADK_NOME)+'",'
			cJson += '"RAZAO_SOCI"	:"'+SA2->A2_NOME+'",'
			cJson += '"EMISSAO"		:"'+DtoC((cAliasSE3)->E3_EMISSAO)+'",'
			cJson += '"PEDIDO"		:"'+(cAliasSE3)->E3_PEDIDO+'",'
			cJson += '"VALOR"		:'+cValToChar((cAliasSE3)->COMPARC)+' ,'
			cJson += '"COD_FORN"	:"'+SA2->A2_COD+'",'
			cJson += '"CNPJ_FORN"	:"'+SA2->A2_CGC+'",'
			cJson += '"EMAIL"		:"'+Alltrim(SA2->A2_XMAILCO)+'" '
			cJson +=" }, "

			(cAliasSE3)->(dbSkip())


		EndIf

	EndDo
	cJson:= SubStr(cJson,1,len(cJson)-1)+'] }'


Return cJson








 




 



 