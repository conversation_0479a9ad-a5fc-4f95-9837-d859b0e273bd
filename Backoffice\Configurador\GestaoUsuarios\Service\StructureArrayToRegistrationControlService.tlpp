#include    "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Service.StructureArrayToRegistrationControlService

/*/{Protheus.doc} StructureArrayToRegistrationControl
This Class aims to use the income Object Json to construct the date array 
@type class
@version  1.0
<AUTHOR>
@since 11/6/2024
/*/
Class   StructureArrayToRegistrationControl
    Static  Method  getArrayFromJson(oJsonUSR  As  Json, dDataBlock   As  Date, cTypeOperation As   Character)  As  Array
EndClass

/*/{Protheus.doc} StructureArrayToRegistrationControl::getArrayFromJson
This method execute the process to extract the array from the object
@type method
@version  1.0
<AUTHOR>
@since 11/6/2024
@param oJsonUSR, object, The income json object
@param dDataBlock, date, The blocking date
@return array, Returns the constructed array 
/*/
Method  getArrayFromJson(oJsonUSR  As  Json, dDataBlock   As  Date, cTypeOperation As   Character)  As  Array   Class   StructureArrayToRegistrationControl
    Local  aDados   :=  {}  As  Array
    
    Do  Case
        Case    cTypeOperation    ==  "BLOCK"
            aAdd( aDados, { "USR_CODIGO" ,  Upper( oJsonUSR:GetJsonObject( "USR_COD" ) )    } )
            aAdd( aDados, { "USR_MSBLQL" ,  "1"                                             } )
            aAdd( aDados, { "USR_MSBLQD" ,  dDataBlock                                      } )
        Case    cTypeOperation    ==  "UNBLOCK"
            aAdd( aDados, { "USR_CODIGO" ,  Upper( oJsonUSR:GetJsonObject( "USR_COD" ) )    } )
            aAdd( aDados, { "USR_MSBLQL" ,  "2"                                             } )
            aAdd( aDados, { "USR_MSBLQD" ,  CtoD( "//" )                                      } )
    EndCase
Return  aDados
