#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'RESTFUL.CH'

// --------------------------------------
// FONTE COM APIs EM REST PARA O TSS 3.0
// --------------------------------------


/*/{Protheus.doc} Bloqueia_EMPFIL_TSS
// API para bloquear a transmissao de notas do cliente.
// O envio do bloqueio ao TSS sera realizado pela funcao U_CRM13J01
<AUTHOR>
@since 27/09/2017
@version undefined

@type class
/*/
WSRESTFUL BRD_EMPFIL_TSS DESCRIPTION "Bloqueia/Reativa/Desativa Empresa/Filial do TSS 3.0"
	
	WSDATA CNPJ AS STRING
	WSDATA IE 	AS STRING
	WSDATA UF	AS STRING
	WSDATA OPC	AS STRING // 1=Bloqueia ; 2=Reativa ; 3=Desativa
	
	WSMETHOD POST Description "Bloqueia/Reativa/Desativa Empresa/Filial do TSS 3.0" WSSyntax "/"
	
	// ----------------------------------
	// Json de retorno
	// ----------------------------------
	//{
	//    "STATUS": "200",
	//    "MESSAGE": ""
    //}

END WSRESTFUL
WSMETHOD POST WSRECEIVE CNPJ, IE, UF, OPC WSSERVICE BRD_EMPFIL_TSS
	Local nX 		:= 0
	Local clCNPJ	:= ""
	Local clIE		:= ""
	Local clUF		:= ""
	Local clOpc		:= ""
	Local clCodRet	:= "200"
	Local clMsgRet	:= ""
	Local cRevAtu	:= ""
	Local alConsumo	:= {}
	Local clMes		:= "08"//alltrim(str(month(dDatabase))) 
	Local clAno		:= alltrim(str(year(dDatabase))) 
	Local llErro	:= .T.
	Local clErro	:= ""
	Local nlTransCon:= 0
	Local llFind	:= .F.
	Local llCNPJP	:= .F. // identifica nos casos de Desativação de deveexcluir em cascata as empresas e filiais
	Local aFiliais	:= {}
	
	// ----------------------------------
	// Define o tipo de retorno do método
	// ----------------------------------
	::SetContentType("application/json")
	
	// --------------------------------------
	// Tratamento dos parâmetros recebimentos
	// --------------------------------------
	
	// Recebeu parametros pela URL
	// exemplo: http://localhost:8080/sample/1/2/3
	If Len(::aURLParms) > 0
		For nX := 1 to Len( ::aURLParms )
                                         
             // CNPJ
             If nX == 1
             	clCNPJ	:= UPPER(Alltrim( ::aURLParms[nX] ))
             EndIf
            
             // IE
             If nX == 2
             	clIE	:= UPPER(Alltrim( ::aURLParms[nX] ))
             	If clIE == 'NIL'
             		clIE := ""
             	EndIF
             EndIf
             
             // UF
             If nX == 3
             	clUF	:= UPPER(Alltrim( ::aURLParms[nX] ))
             EndIf
             
             // OPCAO
             If nX == 4
             	clOpc	:= UPPER(Alltrim( ::aURLParms[nX] ))
             EndIf
             
       Next nX
	
	Else
	
		// Recebeu os valores por querystring
		// exemplo: http://localhost:8080/sample?startIndex=1&count=10
		clCNPJ		:= UPPER(ALLTRIM(::CNPJ))
		clIE		:= UPPER(ALLTRIM(::IE))
		If clIE == 'NIL'
     		clIE := ""
     	EndIF
		clUF		:= UPPER(ALLTRIM(::UF))
		clOpc		:= ALLTRIM(::OPC)
	EndIF
	
	If Empty(clCNPJ) .or. Empty(clUF) .or. Empty(clOpc)
		clCodRet := "400"		
		clMsgRet := "Parameters 'CNPJ' and 'UF' and 'OPC' are required."
	Else
		
		// valida a opcao informada
		If !(clOpc $ '123')
			clCodRet := "400"		
			clMsgRet := "Incorrect 'OPC' parameter.(1=Block;2=Reactive;3=Disable)"
		Else
			
			dbSelectArea("PIS")
			PIS->(dbSetOrder(2))
			If PIS->(dbSeek(xFilial("PIS") + PADR(clCNPJ,TAMSX3("PIS_CNPJF")[1]) + PADR(clIE,TAMSX3("PIS_IEF")[1]) + PADR(clUF,TAMSX3("PIS_UFF")[1]) ))
				
				While PIS->(!EOF()) .and. PIS->(PIS_FILIAL+PIS_CNPJF+PIS_IEF+PIS_UFF) == ( xFilial("PIS") + PADR(clCNPJ,TAMSX3("PIS_CNPJF")[1]) + PADR(clIE,TAMSX3("PIS_IEF")[1]) + PADR(clUF,TAMSX3("PIS_UFF")[1]) )
					If PIS->PIS_CANCEL <> 'S'
						
						llFind		:= .T.
						
						// necessario para posteriormente avaliar se deve desativar em cascata
						If clOpc == '3' .and. PIS->PIS_EMPFIL == '1'
							llCNPJP := .T.
						EndIF	
						
						If clOpc == '2'
						
							// -----------------------------------------------------
							// Recalcula tabela Direitos de Uso caso necessario
							// -----------------------------------------------------
						
							// posiciona na tabela de Clientes Cloud
							dbSelectArea("PIY")
							PIY->(dbSetOrder(2))
							PIY->(dbSeek(xFilial("PIY") + PIS->PIS_CODIGO + PIS->PIS_LOJA ))
							
							// antes de chamar a API para retornar o total de notas transmitidas (U_CRCLD093)
							// executa função para analisar se houve alteração no contrato 
							// e recalcular a franquia liberada
							If U_CR012J06(PIY->PIY_CONTRA, PIY->PIY_REVISA, @cRevAtu)
								
								// seleciona todos os itens ativos do contrato de Intera (Completao/Combo4)
								// ou Tradicional TSS 3.0 e atualiza a tabela direitos de uso caso necessario 
								U_CR012J07(PIS->PIS_CODIGO, PIS->PIS_LOJA)
								
								// atualiza a revisao do contrato na tabela de Clientes Cloud
								If !EMPTY(cRevAtu)							
									If RecLock("PIY",.F.) 
										PIY->PIY_REVISA := cRevAtu
										
										PIY->(msUnLock())
									EndIF
								EndIF
							EndIF
							
							// ---------------------------------------
							// Analisa se cliente pode ser Reativado
							// ---------------------------------------
							
							// retorna total de notas transmitidas e contratadas
							alConsumo := U_CRCLD093(PIS->PIS_CODIGO,clAno,clMes, "TCRMS031", @llErro, @clErro)
							
							If len(alConsumo) > 0

								nlTransCon := ( (alConsumo[1,2] + alConsumo[1,3]) * (1 + (alConsumo[1,4]/100) ) ) 
								
								// verifica se total de transmissoes ultrapassou a quantidade contratada
						 		If ( alConsumo[1,5,1] + alConsumo[1,5,2] ) > nlTransCon
						 			
						 			// cliente esta bloqueado e esta sendo ativado pelo portal
						 			// analisa somente se cliente possuir consumo adicional cadastrado, caso contrario
						 			// as transmissoes serao ILIMITADAS, podendo ser bloqueado e ativado a qualquer
						 			// momento
					 		 		If alConsumo[1,4] > 0 .and. PIS->PIS_ATIVO == 'N'
						 		 		llFind 		:= .F.
						 		 		clCodRet	:= "204"		
						 		 		clMsgRet	:= "Client can't be activated as it has exceeded the number of contracted transmissions."
						 		 		
						 		 	EndIF
						 		 	
						 		EndIF
						 	Else
						 		llFind		:= .F.
						 		clCodRet	:= "204"		
						 		clMsgRet	:= "Error in transmission consumption."
						 	EndIF
						EndIF
						
						If llFind
							
							// analisa se ha outros registros para Desativar
							If clOpc == '3' .and. llCNPJP
								// analisa se a empresa possui empresas ou filiais abaixo dela
								// e realiza a desativacao em cascata
								// Chamada da funcao recursiva
								U_CRM31S02(clCNPJ,clIE,clUF, @aFiliais)
								
								For nX := 1 to len(aFiliais)

									dbSelectArea("PIS")
									PIS->(dbSetOrder(2))
									If PIS->(dbSeek(xFilial("PIS") + PADR(aFiliais[nX,1],TAMSX3("PIS_CNPJF")[1]) + PADR(aFiliais[nX,2],TAMSX3("PIS_IEF")[1]) + PADR(aFiliais[nX,3],TAMSX3("PIS_UFF")[1]) ))
										While PIS->(!EOF()) .and. PIS->(PIS_FILIAL+PIS_CNPJF+PIS_IEF+PIS_UFF) == ( xFilial("PIS") + PADR(aFiliais[nX,1],TAMSX3("PIS_CNPJF")[1]) + PADR(aFiliais[nX,2],TAMSX3("PIS_IEF")[1]) + PADR(aFiliais[nX,3],TAMSX3("PIS_UFF")[1]) )
											If PIS->PIS_CANCEL <> 'S'

												// realiza a alteracao de Status do cliente
												CRM31S01(clOpc,@clMsgRet)
											EndIF
											
											PIS->(dbSkip())
										EndDO
									EndIF
								Next nX
								
							Else
								// realiza a alteracao de Status do cliente
								CRM31S01(clOpc,@clMsgRet)
								
							EndIF
							
							// apos bloquear, reativar ou desativar o cliente sai do laço, pois nao tera outro com a mesma chave que esteja ativado
							Exit
						EndIF
						
					EndIF
					
					PIS->(dbSkip())
				EndDO
				
				If !llFind .and. Empty(clMsgRet)
				 	clCodRet := "204"		
				 	clMsgRet := "Active client not found."
				EndIF
			Else
				clCodRet := "204"		
				clMsgRet := "Client not found."
			EndIF
			PIS->(dbCloseArea())
			
		EndIF
	EndIF
	
	::SetResponse( EncodeUtf8('{ "STATUS": "' + clCodRet  +'", "MESSAGE": "' + clMsgRet + '" }') )
	
RETURN(.T.)


/*/{Protheus.doc} CRM31S01
// Altera registro posicionado da PIS
<AUTHOR>
@since 28/09/2017
@version undefined
@param cOpc, characters, 1=Bloqueia ; 2=Reativa ; 3=Desativa
@param clMsgRet, characters, descricao
@type function
/*/
Static Function CRM31S01(cOpc,clMsgRet)
	
	If RecLock("PIS",.F.)
		
		PIS->PIS_ATIVO := IIF(cOpc $ '13','N','S')
		PIS->PIS_MSEXP := ""
		
		If cOpc == '3'
			PIS->PIS_CANCEL := 'S'
		EndIF
		
		If cOpc == '1' 
			clMsgRet := "Blocked client!"
		ElseIF cOpc == '2' 
			clMsgRet := "Reactivated client!"
		ElseIF cOpc == '3'
			clMsgRet := "Disabled client!"
		EndIF
		
		PIS->(msUnLock())
	EndIF

Return()

User Function XXTESTE()
	Local aFiliais	:= {}

	RpcSetType(3)
	RpcSetEnv('00','00001000100')
	
	U_CRM31S02('65383849000137','1236987','RJ', @aFiliais)
	
Return()

/*/{Protheus.doc} CRM31S02
// Função Recursiva para retornar as empresas e filiais
// que devem ser excluidas
<AUTHOR>
@since 28/09/2017
@version undefined
@param cCnpj, characters, descricao
@param cIE, characters, descricao
@param cUF, characters, descricao
@type function
/*/
User Function CRM31S02(cCnpj, cIE, cUF, aFiliais)
	Local clMsgRet	:= ""
	Local nCont		:= 0
	Local aRetSel	:= {}
	Local clCnpj	:= ""
	Local clIE		:= ""
	Local clUF		:= ""
	Local nI		:= 0
	
	// adiciona o CNPJ Empresa
	aadd( aFiliais, { cCNPJ, cIE, cUF } )
		
	While .T.
		
		aRetSel := CRM31S03(cCNPJ, cIE, cUF)
		
		If len(aRetSel[2]) > 0
			// adiciona o CNPJ das Filiais
			For nI := 1 to len( aRetSel[2] ) 
				aadd( aFiliais, { aRetSel[2,nI,1],aRetSel[2,nI,2],aRetSel[2,nI,3] } )
			Next nI	
		EndIF
			
		For nI := 1 to len( aRetSel[1] )
			
			cCNPJ	:= aRetSel[1,nI,1]
			cIE		:= aRetSel[1,nI,2]
			cUF		:= aRetSel[1,nI,3]

			U_CRM31S02( cCNPJ, cIE, cUF, @aFiliais  )

		Next nI
		
		Exit
		
	EndDO
	
Return()

Static Function CRM31S03(cCNPJ, cIE, cUF)
	Local clQuery	:= ""
	Local clalias	:= GetNextAlias()
	Local alRet		:= {{},{}}
	
	clQuery += " SELECT PIS.PIS_CNPJF,PIS_IEF,PIS_UFF,PIS_EMPFIL " 
	clQuery += " FROM " + RetSqlName("PIS") + " PIS "
	clQuery += " WHERE PIS.PIS_FILIAL = '"+ xFilial("PIS") +"' "
	clQuery += " AND PIS.PIS_CNPJP = '"+ cCNPJ +"' "
	clQuery += " AND PIS.PIS_IEP = '"+ cIE +"' "
	clQuery += " AND PIS.PIS_UFP = '"+ cUF +"' "
	clQuery += " AND PIS.PIS_CANCEL <> 'S' "
	clQuery += " AND PIS.D_E_L_E_T_ = ' ' "

	DBUSEAREA(.T.,"TOPCONN",TcGenQry(,,clQuery),clAlias,.F.,.T.)
	
	While (clAlias)->(!EOF())
		
		If (clAlias)->PIS_EMPFIL == '1'
			aadd(alRet[1], {(clAlias)->PIS_CNPJF, (clAlias)->PIS_IEF, (clAlias)->PIS_UFF } )
		Else
			aadd(alRet[2], {(clAlias)->PIS_CNPJF, (clAlias)->PIS_IEF, (clAlias)->PIS_UFF } )
		EndIF
		
		(clAlias)->(dbSkip())
	EndDO
	(clAlias)->(dbCloseArea())
	
Return(alRet)