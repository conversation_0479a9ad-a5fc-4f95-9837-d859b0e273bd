﻿param (
    [string]$pathjsonlog   = "D:\devops\output.json",
    [string]$pathtxtlog = "D:\devops\output.txt"
)

$countTestSuites = 0
$TotalTestSuites = 0
$TotalTestSuitesErro = 0
$txtAuxilizar = ""

$countTest = 0
$TotalTest = 0
$TotalTestErro = 0


# Carregando o arquivo json
$fileJson = Get-Content -Raw -Path $pathjsonlog -Encoding "UTF8" | ConvertFrom-Json 
 
# Alimentando as variaveis:
$TotalTestSuites = $fileJson.numTotalTestSuites

# Criando o arquivo de log para uso do googlechat
New-Item $pathtxtlog

# percorrendo as suites de teste
for ($countTestSuites = 0; $countTestSuites -lt $TotalTestSuites; $countTestSuites++ )
    {
        # Obtem o número total de testes na suíte atual
        $falhas = $fileJson.testResults[$countTestSuites]
        $countTest = 0
        $TotalTest = $falhas.assertionResults.Length

        for ($countTest = 0; $countTest -lt $TotalTest; $countTest++) 
            {
                if ($countTest -eq 0)
                    {
                        $txtAuxilizar = $falhas.assertionResults[$countTest].ancestorTitles[0] + " ====================="   
                        Add-Content $pathtxtlog $txtAuxilizar  
                    }
                $txtAuxilizar = " |- " + $falhas.assertionResults[$countTest].Title + " | " + $falhas.assertionResults[$countTest].status
                Add-Content $pathtxtlog $txtAuxilizar
            }

    }

