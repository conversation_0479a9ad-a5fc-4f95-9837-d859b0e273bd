
//-------------------------------------------------------------------
/*/{Protheus.doc} F040VAUT
evita msgs de instrucao de cobranca caso titulo esteja com prorrogacao pendente

<AUTHOR>
@since 02/11/2016
@version P12

/*/
//-------------------------------------------------------------------
User Function F040VAUT()
Local aArea		:= GetArea()
Local lRet := .F.
Local lLigAlcada      := SuperGetMV('ES_ALCPRG',,.F.)

If lLigAlcada
	If  FwIsInCallStack("U_TFINA029")
		lRet := .T.
	EndIf
EndIf

restArea(aArea)
return lRet