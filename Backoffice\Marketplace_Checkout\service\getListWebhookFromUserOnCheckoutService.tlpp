#include    "tlpp-core.th"

Class   GetListWebhookFromUserOnCheckoutService
    Private Data   cUser                                    As  Character
    Private Data    cResponseIpaas                          As  Character

    Public  Method  new()                                   As  Object
    Public  Method  setUser(cValue  As  Character)          As  Object
    Public  Method  execute()                               As  Object
    Public  Method  sendToIpaas()                           As  Object
EndClass


Method  new()   As  Object  Class   GetListWebhookFromUserOnCheckoutService
    ::cUser             :=  ''
    ::cResponseIpaas    :=  ''
Return  Self


Method  setUser(cValue  As  Character) As  Object    Class   GetListWebhookFromUserOnCheckoutService
    ::cUser :=  cValue
Return  Self



Method  execute()   As  Object  Class   GetListWebhookFromUserOnCheckoutService
    ::sendToIpaas()
Return  Self



Method  sendToIpaas()       As  Object  Class   GetListWebhookFromUserOnCheckoutService

    //Local   cUrl          := GetMV("TI_TIPDXX5",,"https://api-ipaas.totvs.app/ipaas/api/v1/integrations/bedf3666-b94d-4893-b51c-e3ddb8b20439/api-key/34084cc2-4eb4-4e09-8774-722909561c37")
    Local   cUrl    :=  'https://api-ipaas.totvs.app/sync-hook/api/v1/integrations/d8a1356a-05f2-4138-8b2b-04e0ad7febb2/api-key/1f0601d4-b395-4b16-a176-b9cfb6e68615'
    Local   oIpaas  :=  Nil

    oIpaas := TIiPaaS():New()
	oIpaas:SetWebHook(cUrl)
    oIpaas:addHeaderParam('CheckoutUser: ' + ::cUser)
	oIpaas:SendToiPaaS()

    ::cResponseIpaas  :=  oIpaas:getIpaasCharacterResponse()

    FreeObj(oIpaas)

Return  Self
