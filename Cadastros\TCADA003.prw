#INCLUDE 'TOTVS.CH'
#INCLUDE 'FWMVCDEF.CH'


//-----------------------------------------------------------------------------
/*/ {Protheus.doc} 
			Define as operacoes da aplicacao
<AUTHOR> Mobile Costa
@version 	P12
@since   	10/03/2021
@Parametros:
/*/
//-----------------------------------------------------------------------------
Static Function MenuDef()
	Local aRotina := {}

	
    ADD OPTION aRotina TITLE "Atualizar Tela" ACTION "U_TCAD002F"  	OPERATION MODEL_OPERATION_VIEW ACCESS 0
    ADD OPTION aRotina TITLE "Visualizar"     ACTION "VIEWDEF.TCADA003"  OPERATION MODEL_OPERATION_VIEW ACCESS 0

Return aRotina


Static Function ModelDef()
	
	Local oStruP29	:= FWFormStruct(1,'P29')
	Local oModel	:= MPFormModel():New( 'TCAD003A',,, )
	
	oModel:SetDescription("Clientes - Leitura CNPJ") 
	oModel:AddFields( 'P29MASTER', , oStruP29 )
	oModel:SetPrimaryKey({})

Return oModel


Static Function ViewDef()
	
	Local oModel   := FWLoadModel("TCADA003")
	Local oView	   := FWFormView():New()
	Local oStruP29 := FWFormStruct(2,'P29' )
	
	oView:SetModel( oModel )
	oView:AddField('VIEW_P29',oStruP29,'P29MASTER')
	oView:CreateHorizontalBox('CABEC',100)
	oView:SetOwnerView('VIEW_P29','CABEC' )
	oView:SetCloseOnOk({||.T.})

Return oView
