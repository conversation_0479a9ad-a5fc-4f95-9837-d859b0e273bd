#include 'totvs.ch'
#include 'restful.ch'
#INCLUDE "TOPCONN.CH"

#DEFINE CRLF Chr(10) + Chr(13)

/*/{Protheus.doc} 
Webservice de resgate de informações de propostas
@type function
@version  1.0
<AUTHOR> JACOME
@since 14/04/2021
/*/

WSRESTFUL WSVTEXPREBILLING Description "Webservice retorna as vendas do MPN" Format APPLICATION_JSON

	WSDATA initialDate  AS STRING
	WSDATA finalDate    AS STRING
	WSDATA tipo      AS STRING

	WSMETHOD GET     DESCRIPTION "Get proposal information"  WSSYNTAX "/WSVTEXPREBILLING?initialDate=, finalDate=, idVind = "



END WSRESTFUL

/*/{Protheus.doc} 
Webservice de resgate de informações de propostas
@type function
@version  1.0
<AUTHOR> JACOME
@since 14/04/2021
/*/
WSMETHOD GET  WSRECEIVE initialDate, finalDate, tipo WSSERVICE WSVTEXPREBILLING

	local oJsonStat
	Local lGet      := .T.
	//Local cDtIni,cDtFim

	self:SetContentType("application/json")

	oJsonStat  := JsonObject():new()

	oJsonStat := MontaQuery(self:initialDate,self:finalDate,self:tipo)//Função para montar a query

	cJson := oJsonStat:toJson()
	//------------------------------------
	//Convertendo em UTF8 o JSON
	//------------------------------------
	cJson := encodeUtf8(cJson)
	//--------------------------------------
	//Retorna como resposta o JSON do Model
	//--------------------------------------
	if !empty(cJson)
		self:setResponse(cJson)
	endif

	freeObj(oJsonStat)

return lGet


/*/
------------------------------------------------------------------
{Protheus.doc} MontaQuery
<AUTHOR> B. Melo
@since Out/2021
@version 1.00
------------------------------------------------------------------
/*/
Static Function MontaQuery(cDtIni,cDtFim,ctipo)

	local oJsonStat,oJsonAux as object
	Local cQuery := ""
	Local nAux 	 := 0
	Local cInteg := SuperGetMv("ES_INTEG",.F.,"000016")

	oJsonStat  := JsonObject():new()
	if ctipo == "valor"
		cQuery := "SELECT * FROM PQJ000 "
	elseif ctipo == "quantidade"
		cQuery := "SELECT SUM(PQJ_QTDFAT) AS QNT FROM PQJ000 "
	endif

	cQuery += " WHERE D_E_L_E_T_ <> '*'
	cQuery += " AND PQJ_INTEG = '"+cInteg+"'
	cQuery += " AND PQJ_PROPOS <> ' '

	if !empty(IIF(ValType(cDtIni) == 'C',cDtIni,""))
		cQuery += "AND PQJ_CMPFAT = '"+STRZERO(Month(STOD(cDtIni)),2) + "/" + CVALTOCHAR(Year(STOD(cDtIni)))+"' "
	endif

	If SELECT("PRBIL") > 0
		PRBIL->(DbCloseArea())
	Endif

	TCQuery cQuery new Alias PRBIL

	nTotReg := Contar("PRBIL","!Eof()")

	oJsonStat["total"] := nTotReg

	PRBIL->(DbGoTop())
	oJsonStat["pedidos"] := {}
	while !PRBIL->(EOF())

		oJsonAux := JsonObject():new()

		if ctipo == "valor"
			//oJsonAux["idVind"]      :=  ALLTRIM(PRBIL->C5_XFATURA)
			oJsonAux["contrato"]    :=  PRBIL->PQJ_CONTRA
			oJsonAux["proposta"]    :=  PRBIL->PQJ_PROPOS
			oJsonAux["quantidade"]  :=  PRBIL->PQJ_QTDFAT
			oJsonAux["valor"]  		:=  PRBIL->PQJ_VLRUNI
			oJsonAux["valortotal"]  :=  PRBIL->PQJ_VLRTOT
			nAux += PRBIL->PQJ_VLRTOT
			aadd(oJsonStat["pedidos"], oJsonAux)

		elseif ctipo == "quantidade"

			oJsonAux["quantidade"]     :=  PRBIL->QNT
			nAux += PRBIL->QNT
			aadd(oJsonStat["pedidos"], oJsonAux)

		endif

		PRBIL->(DbSkip())
	end
	oJsonStat["valortotal"] := nAux
	PRBIL->(DbCloseArea())

Return oJsonStat
