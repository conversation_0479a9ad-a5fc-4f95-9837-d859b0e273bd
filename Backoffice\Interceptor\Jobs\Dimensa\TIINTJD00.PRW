#INCLUDE 'TOTVS.CH'
#INCLUDE "FWBROWSE.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "TOPCONN.CH"
#DEFINE CRLF		Chr(13)+Chr(10)


/*/{Protheus.doc} TIINTJD00()
	Funcao Job Responsavel por executar o envio e recebimento do Interceptor para empresa DIMENSA
	@type  Function
	<AUTHOR> Menabue Lima
	@since 05/10/2021
	@version 1.0

	/*/ 
User Function TIINTJD00()
	Local oReceptor	:= Nil
	Local lInterR	:= .F.
	Local lInterT	:= .F.
	Local nInternT	:= .F.
	Local lInterE	:= .F.
 

	If Select("SM0") == 0
		RpcSetEnv( '60', '60001000100')
	EndIf

	lInterR	:=GetMv("TI_RCVJ00R" , .F., .T.) //Processa envio de dados do Interceptor
	lInterT	:=GetMv("TI_RCVJ00T" , .F., .T.) //Processa o Interceptor em Threads
	nInternT:=GetMv("TI_RCVJ00N" , .F.,  15 ) //Numero de Threads que o Interceptor vai gerar
	nInternR:=GetMv("TI_RCVJ0NR" , .F.,  6 ) //Numero de Threads que o Interceptor vai gerar para recebimento
	lInterE	:=GetMv("TI_RCVJ00E" , .F., .T.) //Processa o Recebimento do Interceptor

	//Inicia o Interceptor
	oReceptor:=TIINTERCEPTOR():New()
	oReceptor:lMultiThread := lInterT

	IF lInterT
		oReceptor:nMaxThreads  := nInternT
		oReceptor:nMaxThrdsR   := nInternR
	EndIF
	//Prepara e Envia Todas as Requests de envio
	IF lInterE
		FWMonitorMsg("***** Inicio Rodando Interceptors Envio ***** " )
		oReceptor:PrepareAllRequests() //p37_tpreq =1
		FWMonitorMsg( "***** Termino Rodando Interceptors Envio ***** ")
	EndIF

	//Prepara e Envia Todas as Requests de Recebimento
	IF lInterR
		FWMonitorMsg("***** Inicio Rodando Interceptors Recebimento ***** " )
		oReceptor:PrepareReceiveAll() //p37_tpreq =2
		FWMonitorMsg( "***** Termino Rodando Interceptors Recebimento ***** ")
	EndIF

	RpcClearEnv()
Return

/*/{Protheus.doc} TIINTJDE()
	Funcao Job Responsavel por executar o envio  do Interceptor
	@type  Function
	<AUTHOR> Menabue Lima
	@since 05/10/2021
	@version 1.0
	/*/ 
User Function TIINTJDE()
	Local oReceptor	:= Nil
	Local lInterR	:= .F.
	Local lInterT	:= .F.
	Local nInternT	:= .F.
	Local lInterE	:= .F.


	If Select("SM0") == 0
		RpcSetEnv( '60', '60001000100')
	EndIf

	lInterR	:=GetMv("TI_RCVJ00R" , .F., .T.) //Processa envio de dados do Interceptor
	lInterT	:=GetMv("TI_RCVJ00T" , .F., .T.) //Processa o Interceptor em Threads
	nInternT:=GetMv("TI_RCVJ00N" , .F.,  15 ) //Numero de Threads que o Interceptor vai gerar
	nInternR:=GetMv("TI_RCVJ0NR" , .F.,  6 ) //Numero de Threads que o Interceptor vai gerar para recebimento
	lInterE	:=GetMv("TI_RCVJ00E" , .F., .T.) //Processa o Recebimento do Interceptor

	//Inicia o Interceptor
	oReceptor:=TIINTERCEPTOR():New()
	oReceptor:lMultiThread := lInterT

	IF lInterT
		oReceptor:nMaxThreads  := nInternT
		oReceptor:nMaxThrdsR   := nInternR
	EndIF
	//Prepara e Envia Todas as Requests de envio
	IF lInterE
		FWMonitorMsg("***** Inicio Rodando Interceptors Envio ***** " )
		oReceptor:PrepareAllRequests() //p37_tpreq =1
		FWMonitorMsg( "***** Termino Rodando Interceptors Envio ***** ")
	EndIF

 
	RpcClearEnv()
Return
/*/{Protheus.doc} TIINTJDR()
	Funcao Job Responsavel por executar o   recebimento do Interceptor
	@type  Function
	<AUTHOR> Menabue Lima
	@since 05/10/2021
	@version 1.0
	/*/ 
User Function TIINTJDR()
	Local oReceptor	:= Nil
	Local lInterR	:= .F.
	Local lInterT	:= .F.
	Local nInternT	:= .F.
	Local lInterE	:= .F.


	If Select("SM0") == 0
		RpcSetEnv( '60', '60001000100')
	EndIf

	lInterR	:=GetMv("TI_RCVJ00R" , .F., .T.) //Processa envio de dados do Interceptor
	lInterT	:=GetMv("TI_RCVJ00T" , .F., .T.) //Processa o Interceptor em Threads
	nInternT:=GetMv("TI_RCVJ00N" , .F.,  15 ) //Numero de Threads que o Interceptor vai gerar
	nInternR:=GetMv("TI_RCVJ0NR" , .F.,  6 ) //Numero de Threads que o Interceptor vai gerar para recebimento
	lInterE	:=GetMv("TI_RCVJ00E" , .F., .T.) //Processa o Recebimento do Interceptor

	//Inicia o Interceptor
	oReceptor:=TIINTERCEPTOR():New()
	oReceptor:lMultiThread := lInterT

	IF lInterT
		oReceptor:nMaxThreads  := nInternT
		oReceptor:nMaxThrdsR   := nInternR
	EndIF
	 
	//Prepara e Envia Todas as Requests de Recebimento
	IF lInterR
		FWMonitorMsg("***** Inicio Rodando Interceptors Recebimento ***** " )
		oReceptor:PrepareReceiveAll() //p37_tpreq =2
		FWMonitorMsg( "***** Termino Rodando Interceptors Recebimento ***** ")
	EndIF

	RpcClearEnv()
Return
