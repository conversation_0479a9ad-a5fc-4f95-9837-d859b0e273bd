#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Utils.getArrayStatusProcessingUtils

/*/{Protheus.doc} getArrayStatusProcessing
This Class are a static and has the purpose of extract the Property aRetProc of the 
Object that are passed as parameter  
@type class
@version 1.0 
<AUTHOR>
@since 10/24/2024
/*/
Class   getArrayStatusProcessing
	Static Method ExtractStatusProcessing(oJsonToExtract As Object) 	As	Array
EndClass

/*/{Protheus.doc} getArrayStatusProcessing::ExtractStatusProcessing
This method extract the Property aRetProc of the Income Object
@type method
@version 1.0  
<AUTHOR>
@since 10/24/2024
@return Object, Return the array of property aRetProc 
/*/
Method ExtractStatusProcessing(oJsonToExtract As Object) As Array		Class		getArrayStatusProcessing
Return	oJsonToExtract:aRetProc
