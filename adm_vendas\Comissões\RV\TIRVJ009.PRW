#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVJ006     
Job Responsavel por criar a P37 para consultar a API do PSA View  Informacoes PSA
@type  Function
<AUTHOR> Menabue Lima
@since 27/10/2023
@version 1.0
U_TIRVJ009(,,,"2023-11-01","2023-11-30",.F.)
U_TIRVJ009(,,,"2023-10-01","2023-10-31",.F.)
U_TIRVJ009(,,,"2023-11-01","2023-11-30",.T.)
U_TIRVJ009(,,,"2023-10-01","2023-10-31",.T.)
U_TIRVJ009(,,,"2023-09-01","2023-09-30",.F.)
U_TIRVJ009(,,,"2023-08-01","2023-08-31",.F.)
U_TIRVJ009(,,,"2023-07-01","2023-07-31",.F.)
U_TIRVJ009(,,,"2023-09-01","2023-09-30",.T.,"")
U_TIRVJ009(,,,"2023-08-01","2023-08-31",.T.,"")
U_TIRVJ009(,,,"2023-07-01","2023-07-31",.T.,"")
U_TIRVJ009(,,,"2023-07-01","2023-07-31",.T.,"SET/2023")
U_TIRVJ009(,,,"2023-07-01","2023-07-31",.T.,"AGO/2023")
U_TIRVJ009(,,,"2023-07-01","2023-07-31",.T.,"JUL/2023")
	/*/
User Function TIRVJ009(nCount,nPage,cCodReq,cIni,cFim,lIni,cMesName,cCodPrj)
	Local oReceptor:=TIINTERCEPTOR():New()
	Local cFecthXml :=""
	Local cApiPSA:=GetMv("TI_RVJ009A" , .F., "RVF009")
	Local nMonth :=GetMv("TI_RVJ09NM" , .F., 5)
	Default nCount := GetMv("TI_RVJ09CT" , .F., 5000)
	Default nPage := 1
	Default cCodReq:=""
	Default cIni :=""
	Default cFim :=""
	Default lIni := .T.
	Default cMesName :=""
	Default cCodPrj :=""
	cFecthXml +='<fetch no-lock="true" latematerialize="true" count="'+cValToChar(nCount)+'" page="'+cValToChar(nPage)+'">'
	cFecthXml +='<entity name="msdyn_project">
	cFecthXml +='<attribute name="ctm_codigo" alias="NUMERO_PROJETO" />
	cFecthXml +='<attribute name="msdyn_subject" alias="NOME_PROJETO" />
	cFecthXml +='<attribute name="statuscode" alias="RAZAO_STATUS" />
	cFecthXml +='<attribute name="msdyn_totalactualcost" alias="CUSTO_REALIZADO_PROJETO" />
	cFecthXml +='<attribute name="ctm_metodocob" alias="METODO_COBRANCA" />
	cFecthXml +='<attribute name="createdon" alias="DATA_EMISSAO_PROJETO" />
	cFecthXml +='<attribute name="ctm_valorcontratado" alias="VALOR_TOTAL_PROJETO" />
	cFecthXml +='<attribute name="ctm_pl_tipobancohoras" alias="TIPO_BANCO_HORAS" />
	cFecthXml +='<attribute name="msdyn_actualhours" alias="HORAS_REALIZADAS" />
	cFecthXml +='<attribute name="ctm_ultimo_custoplanejado_aprovado" alias="CUSTO_APROVADO" />
	cFecthXml +='<attribute name="ctm_custototalplanejado" alias="CUSTO_ESPERADO" />
	cFecthXml +='<attribute name="ctm_margemprojeto" alias="MARGEM_PROJETO" />
	cFecthXml +='<attribute name="ctm_tipodeprojeto" alias="TIPO_PROJETO" />
	cFecthXml +='<attribute name="ctm_opcaotipoprojeto" alias="OPCAO_TIPO_PROJETO" />
	cFecthXml +='<attribute name="ctm_margemgerencial" alias="MARGEM_GERENCIAL" />
	if(!Empty(cCodPrj))
		cFecthXml +='<filter>
		cFecthXml +='<condition attribute="ctm_codigo" operator="eq" value="'+cCodPrj+'" />
		cFecthXml +='</filter>
	EndIF
	cFecthXml +='<link-entity name="salesorder" from="salesorderid" to="msdyn_salesorderid" link-type="inner" alias="contrato">
	cFecthXml +='<attribute name="ordernumber" alias="NUMERO_CONTRATO" />
	cFecthXml +='<attribute name="ctm_codigo" alias="NRO_PROPOSTA" />
	cFecthXml +='<attribute name="ctm_pl_modalidadeprojeto" alias="MODALIDADE_CONTRATO" />
	cFecthXml +='<attribute name="ctm_margemvenda" alias="MARGEM_VENDA_CONTRATO" />
	cFecthXml +='<attribute name="ctm_dataencerramentocontrato" alias="DATA_ENCERRAMENTO_CONTRATO" />
	cFecthXml +='<attribute name="ctm_vlrtotalliq" alias="VALOR_TOTAL_LIQUIDO" />
	cFecthXml +='<attribute name="ctm_totalhorasvendidas" alias="QUANTIDADE_ORCADA_VENDA" />
	cFecthXml +='<link-entity name="ctm_empresafilial" from="ctm_empresafilialid" to="ctm_empresafilial" link-type="outer" alias="empresa_filial">
	cFecthXml +='<attribute name="ctm_codigo" alias="CODIGO_EMPRESA_FILIAL" />
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="msdyn_organizationalunit" from="msdyn_organizationalunitid" to="msdyn_contractorganizationalunitid" link-type="outer" alias="unidade_contrato">
	cFecthXml +='<attribute name="ctm_codigo" alias="CODIGO_UNIDADE_CONTRATO" />
	cFecthXml +='<attribute name="msdyn_name" alias="NOME_UNIDADE_CONTRATO" />
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="account" from="accountid" to="msdyn_customer" link-type="outer" alias="cliente">
	cFecthXml +='<attribute name="name" alias="CLIENTE_PROJETO" />
	cFecthXml +='<attribute name="ctm_codigo" alias="CODIGO_CLIENTE_PROJETO" />
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="bookableresource" from="bookableresourceid" to="ctm_principalcp" link-type="outer" alias="cp_projeto">
	cFecthXml +='<attribute name="name" alias="CP_PROJETO" />
	cFecthXml +='<link-entity name="systemuser" from="systemuserid" to="userid" link-type="outer" alias="cp_usuario">
	cFecthXml +='<attribute name="ctm_st_cpf" alias="CP_PROJETO_CPF" />
	cFecthXml +='<attribute name="internalemailaddress" alias="CP_PROJETO_EMAIL" />
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="bookableresource" from="bookableresourceid" to="ctm_principalgpp" link-type="outer" alias="gpp_projeto">
	cFecthXml +='<attribute name="name" alias="GPP_PROJETO" />
	cFecthXml +='<link-entity name="systemuser" from="systemuserid" to="userid" link-type="outer" alias="gpp_usuario">
	cFecthXml +='<attribute name="ctm_st_cpf" alias="GPP_PROJETO_CPF" />
	cFecthXml +='<attribute name="internalemailaddress" alias="GPP_PROJETO_EMAIL" />
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="ctm_classificacaoprojetofaturavel" from="ctm_classificacaoprojetofaturavelid" to="ctm_classificacaoprojetofaturavel" link-type="outer" alias="classif">
	cFecthXml +='<attribute name="ctm_name" alias="CLAS_PRJ_FAT" />
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="ctm_reconhecimentomensalreceitaprojeto" from="ctm_projeto" to="msdyn_projectid" link-type="outer" alias="ReconhecimentoMensalProjeto">
	cFecthXml +='<attribute name="ctm_custorealizado" alias="CUSTO_REALIZADO" />
	cFecthXml +='<attribute name="ctm_custorealizadoacumulado" alias="CUSTO_REALIZADO_ACUMULADO" />
	cFecthXml +='<attribute name="ctm_receitareconhecida" alias="RECEITA_RECONHECIDA" />
	cFecthXml +='<attribute name="ctm_receitareconhecidaacumulada" alias="RECEITA_RECONHECIDA_ACUMULADA" />
	cFecthXml +='<link-entity name="ctm_mes_contabil" from="ctm_mes_contabilid" to="ctm_mes_contabil" link-type="inner" alias="mes_contabil">
	cFecthXml +='<attribute name="ctm_data_fim" alias="DT_CTB_FIN" />
	cFecthXml +='<attribute name="ctm_data_inicio" alias="DT_CTB_INI" />
	cFecthXml +='<attribute name="ctm_mes_contabilid" alias="MES_CONTABIL_ID" />
	cFecthXml +='<attribute name="ctm_name" alias="MES_CONTABIL" />
	cFecthXml +='<filter>
	cFecthXml +="<condition attribute='ctm_data_inicio' operator='on-or-after' value='"+cIni+"' />
	cFecthXml +="<condition attribute='ctm_data_inicio' operator='on-or-before' value='"+cFim+"' />
	cFecthXml +='</filter>
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='</entity>
	cFecthXml +='</fetch>
	cCodP37:= oReceptor:GetP37COD()
	BeginTran()

	Reclock("P37",.T.)
	P37->P37_FILIAL:= Xfilial("P37")
	P37->P37_COD   :=cCodP37
	P37->P37_DATA  := ddatabase
	P37->P37_HORA  := TIME()
	P37->P37_PATH2 :=EncodeUtf8("/api/data/v9.2/msdyn_projects?$count=true&fetchXml="+ESCAPE(cFecthXml))
	P37->P37_CODAPI:=cApiPSA
	If P36->(DbSeek(xFilial("P36")+P37->P37_CODAPI))
		P37->P37_URL   := P36->P36_URL
	EndIf
	P37->P37_METHOD:= "GET"
	P37->P37_ASYNC1:= "N"
	P37->P37_STATUS:= "0"
	P37->P37_TIPORE:= "1"//Envio
	P37->P37_CALLBA:= "U_TIRVF009"
	P37->P37_HEADER:= "Accept: application/json|OData-MaxVersion: 4.0|OData-Version: 4.0|"+'Prefer: odata.include-annotations="OData.Community.Display.V1.FormattedValue"'
	P37->P37_CODREQ:= cCodReq
	P37->P37_BODY  := '{"nPage":'+cValToChar(nPage)+', "nCount":'+cValToChar(nCount)+',"inicio":"'+cIni+'", "fim":"'+cFim+'","projeto":"'+cCodPrj+'","lInicio":"'+iif(lIni,'sim','nao')+'"}'
	P37->(MsUnlock())
	EndTran()
	FreeObj(oReceptor)
Return
