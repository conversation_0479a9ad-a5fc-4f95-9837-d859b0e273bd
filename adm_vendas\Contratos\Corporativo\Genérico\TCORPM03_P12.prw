#Include "Protheus.ch"
#Include "RwMake.ch"

User Function TCORPM03()

	Local _aDados 	:= {}
	
	Local _nCount 	:= 0
	Local _nCount2 	:= 0
	Local _nCount3 	:= 0
	
	Local _aMemoCal 	:= {}
	Local _aAuxi1 	:= {}
	Local _aAuxi2 	:= {}
	Local _aAuxi3 	:= {}
	Local _aAuxi4 	:= {}	
	Local _aInfoPH2 	:= {}
	
	Local _cTipoMet 	:= ""
	Local _cAux3 		:= ""
	//Local _cMemoCal	:= ""
	
	_aDados := _fMakeQry()
	
	If !Empty(_aDados)
		For _nCount := 1 To Len(_aDados)
			ZB2->(DbGoTo(_aDados[_nCount][2]))
			If !Empty(ZB2->ZB2_MEMCAL) .And. "<CSEPARA>" $ Upper(ZB2->ZB2_MEMCAL)
				_aAuxi3 := Separa(Separa(Upper(ZB2->ZB2_MEMCAL),"<CSEPARA>")[1],"<TABLE CLASS='CORP' BORDER=1 CELLSPACING=0 WIDTH=600 ALIGN='CENTER'>")
				If Len(_aAuxi3) >= 2
					_cAux3 := _aAuxi3[2]
					_aMemoCal := Separa(_cAux3,"<TABLE CLASS='CORP' BORDER=1 CELLSPACING=0 WIDTH=879 ALIGN='CENTER'>")
					
					//_cMemoCal := _aMemoCal[1] 
					_aAuxi4 := Separa(_aMemoCal[1], "<TD>",.T.)
					// _aAuxi4[1] - <descartar>
					// _aAuxi4[2] - Data
					// _aAuxi4[3] - Hora
					// _aAuxi4[4] - Ip
					// _aAuxi4[5] - Nome Maquina					
					// _aAuxi4[6] - Usuario
						
					PH0->(DbSetOrder(1))
					If PH0->(DbSeek(xFilial("PH0")+ _aDados[_nCount][1]))
				
						RecLock("PH0",.F.)
						PH0->PH0_HORCAL := Separa(_aAuxi4[3], "</TD>",.T.)[1] 
						PH0->PH0_USRCAL := Separa(_aAuxi4[6],"</TD>",.T.)[1]
						PH0->PH0_MAQCAL := Separa(_aAuxi4[4],"</TD>",.T.)[1] + ' / ' + Separa(_aAuxi4[5],"</TD>",.T.)[1]
						PH0->(MsUnLock())
					Else
						Aviso("ERRO", "NAO ENCONTROU PH0 EQUIVALENTE", {"ok"})
					EndIf
										
					
					For _nCount3 := 2 To Len(_aMemoCal)
						_aAuxi1 := Separa(_aMemoCal[_nCount3],"<TR>")
						_aInfoPH2 := {}
						
						For _nCount2 := 3 to Len(_aAuxi1)
							_aAuxi2 := Separa(_aAuxi1[_nCount2],"<TD>")
							
							If Len(_aAuxi2) >= 8
								If "INFORMADA" $ Upper(_aAuxi2[2])
									_cTipoMet := "1"
								ElseIf "COMPROVADA" $ Upper(_aAuxi2[2])
									_cTipoMet := "2"
								ElseIf "REAJUSTADA" $ Upper(_aAuxi2[2])
									_cTipoMet := "3"
								ElseIf "MAIOR" $ Upper(_aAuxi2[2])
									_cTipoMet := "4"
								ElseIf "ET/AR" $ Upper(_aAuxi2[2])				
									_cTipoMet := "7"
								ElseIf "ET" $ Upper(_aAuxi2[2])
									_cTipoMet := "5"
								ElseIf "AR" $ Upper(_aAuxi2[2])
									_cTipoMet := "6"
								EndIf
								
								Aadd(_aInfoPH2,{_aDados[_nCount][1],_cTipoMet,_aAuxi2[3],_aAuxi2[4],_aAuxi2[5],_aAuxi2[6],_aAuxi2[7],_aAuxi2[8]})
							EndIf
						Next
						
						For _nCount2 := 1 To Len(_aInfoPH2)
							RecLock("PH2",.T.)
								PH2->PH2_FILIAL := xFilial("PH2")
								PH2->PH2_CODIGO := _aInfoPH2[_nCount2][1]
								PH2->PH2_TIPMET := _aInfoPH2[_nCount2][2]
								PH2->PH2_RECEIT := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][3]),"</TD>"),"."),",","."))
								PH2->PH2_INDICE := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][4]),"</TD>"),"."),",","."))
								PH2->PH2_VLRMIN := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][5]),"</TD>"),"."),",","."))
								PH2->PH2_VLRCAL := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][6]),"</TD>"),"."),",","."))
								PH2->PH2_VLRCON := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][7]),"</TD>"),"."),",","."))
								PH2->PH2_VLRPAG := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][8]),"</TD>"),"."),",","."))
							PH2->(MsUnLock())
						Next
					Next
				EndIf
			EndIf
		Next		
	EndIf

Return

Static Function _fMakeQry()

	Local _cQuery := ""

	_cQuery := " SELECT " + CRLF
	_cQuery += "	 PH0.PH0_CODIGO " + CRLF
	_cQuery += "	,MIN(ZB2.R_E_C_N_O_) " + CRLF
	_cQuery += " FROM " + RetSqlName("PH0") + " PH0 " + CRLF
 	_cQuery += CRLF
	_cQuery += " INNER JOIN " + RetSqlName("ZB2") + " ZB2 ON " + CRLF
	_cQuery += "				ZB2.D_E_L_E_T_ = '' 		" + CRLF
//	_cQuery += "				AND ZB2.ZB2_FILIAL = '" + xFilial("ZB2") + "' " + CRLF
	_cQuery += "				AND ZB2.ZB2_FILIAL	= '' 	" + CRLF
	_cQuery += "				AND ZB2.ZB2_CLIENT	= PH0.PH0_CLIENT" + CRLF
	_cQuery += "				AND ZB2.ZB2_LOJA		= PH0.PH0_LOJA" + CRLF
	_cQuery += "				AND ZB2.ZB2_ANOREF	= PH0.PH0_ANOREF	" + CRLF
	_cQuery += CRLF	
	_cQuery += " WHERE "
	_cQuery += "			PH0.D_E_L_E_T_ = '' " + CRLF
//	_cQuery += "			AND PH0.PH0_FILIAL = '" + xFilial("PH0") + "' " + CRLF
	_cQuery += "			AND PH0.PH0_FILIAL = '" + xFilial("PH0") + "' " + CRLF
	_cQuery += "	AND NOT EXISTS ( " + CRLF
	_cQuery += "		 SELECT 1 " + CRLF
	_cQuery += "		 FROM " + RetSqlName("PH2") + " PH2 " + CRLF
	_cQuery += "		 WHERE PH2.D_E_L_E_T_ = '' " + CRLF
	_cQuery += "			AND PH2.PH2_FILIAL = '" + xFilial("PH2") + "' " + CRLF
	_cQuery += "			AND PH2.PH2_CODIGO = PH0.PH0_CODIGO " + CRLF
	_cQuery += "	) " + CRLF
	_cQuery += CRLF
	_cQuery += " GROUP BY " + CRLF
	_cQuery += " 	 PH0.PH0_CODIGO " + CRLF

Return U_TDIFUN01(_cQuery)