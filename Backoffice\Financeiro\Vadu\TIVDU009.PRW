#INCLUDE "TOTVS.CH"
#INCLUDE "TOPCONN.CH"


 /*/{Protheus.doc}TIVDU009
Funcao responsavel por enviar consultas Juricas VAdu
type  Function.
	@since 19/04/2022
	@version 1.0
 /*/
User Function TIVDU009(cCnpj)



	Local cResult := ''
	Local aResult := {}
	Local cCnpjResp:= ''
	Local cCodApi := ""
	Local nU := 0
	Local oObj1 := ''
	Local oObj2 := ''
	local aHeader := {}
	Local cCodApiAUT := ''
	Local cRetJur:= ''
	Local cQry  := ''
	Local cAlTrb := GetNextAlias()
	Local nQntDt := GetMv("TI_VDUDAT" ,,15)

	Default cCnpj := ''




	If Select("SM0") == 0
		RpcSetEnv('00', '00001000100')
		RpcSetType(3)
		FWLogMsg("INFO", /*cTransactionId*/, "VADU", /*cCategory*/, /*cStep*/, /*cMsgId*/, "Iniciando carga diaria CLIENTES Vadu...", /*nMensure*/, /*nElapseTime*/, /*aMessage*/)
	EndIf


	cCodApiAUT       := GetMv("TI_VDU000" ,,"000038")
	cCodApi          := GetMv("TI_VDU006" ,,"000045")


	cQry := "SELECT  MAX(R_E_C_N_O_) NRECNO FROM "+RETSqlname("PWA")+"  WHERE   PWA_CGC = '" +cCnpj+ "' and D_E_L_E_T_ = ' '  and PWA_DATA >= '"+Dtos((dDatabase-nQntDt))+"' "
	cQry += " HAVING MAX(R_E_C_N_O_) > 0 "
	If Select(cAlTrb) > 0
		(cAlTrb)->(dbCloseArea())
	Endif
	cQry :=  ChangeQuery(cQry)
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAlTrb,.T.,.T.)

	IF (cAlTrb)->(EOF())
		DbSelectArea("P36")
		DbSetOrder(1)
		If P36->(DbSeek(XFilial("P36")+cCodApiAUT))

			aAdd(aHeader,P36->P36_HEADER)


			//URL da aplicacao Rest
			oRest := FWRest():New(alltrim(P36->P36_URL))

			//Set Path do Rest
			oRest:setPath(alltrim(P36->P36_PATH))


			If oRest:Get(aHeader)
				cResult := oRest:GetResult()

				//Deserializa a string JSON
				FWJsonDeserialize(cResult, @oObj2)

				If P36->(DbSeek(XFilial("P36")+cCodApi))
					aHeader := {}
					aAdd(aHeader, 'Authorization: Bearer '+oObj2:token )


					//URL da aplicacao Rest
					oRest := FWRest():New(alltrim(P36->P36_URL))

					//Set Path do Rest
					oRest:setPath(alltrim(P36->P36_PATH)+cCnpj)


					If oRest:Get(aHeader)
						cResult := oRest:GetResult()

						//Deserializa a string JSON
						FWJsonDeserialize(cResult, @oObj1)

						IF ValType(oObj1) == "O"
							If !ValType(oObj1:Empresas) == "U"
								For nU := 1 to Len(oObj1:Empresas)
									if nU == 1
										cCnpjResp += oObj1:Empresas[nU]:CNPJ
									else
										cCnpjResp +=  ','+oObj1:Empresas[nU]:CNPJ
									ENDIF
								Next
								cRetJur := U_TIVDU007(cCnpjResp)
								DbSelectArea("PWA")

								aadd(aResult,cResult)
								aadd(aResult,cRetJur)
								aadd(aResult,dDatabase)
							Else
								aadd(aResult,'{ "empresas" : "Erro na API da VADU retorno invalido"}')
								aadd(aResult,'{ "data": []}'  )
								return aResult
							ENDIF
						Else
							aadd(aResult,'{ "empresas" : "Erro na API da VADU formato json invalido"}')
							aadd(aResult,'{ "data": []}'  )
							return aResult
						EndIF

					Else
						aadd(aResult,'{ "empresas" : "Erro na API da VADU P36_COD ('+cCodApi+')"}')
						aadd(aResult,'{ "data": []}'  )
						return aResult
					ENDIF
				Else
					aadd(aResult,'{ "empresas" : "Erro na API da VADU P36_COD ('+cCodApi+') nao encontrado"}')
					aadd(aResult,'{ "data": []}'  )
					return aResult
				EndIf
			Else
				aadd(aResult,'{ "empresas" : "Erro na API da VADU P36_COD ('+cCodApi+')"}')
				aadd(aResult,'{ "data": []}'  )
				return aResult
			ENDIF
		Else
			aadd(aResult,'{ "empresas" : "Erro na API da VADU P36_COD ('+cCodApi+') nao encontrado"}')
			aadd(aResult,'{ "data": []}'  )
			return aResult
		ENDIF
	else
		DbSelectArea("PWA")
		DbSetOrder(1)
		DbGoTo((cAlTrb)->NRECNO)
		if Empty(PWA->PWA_DATA)
			aadd(aResult,'{ "empresas" : "Erro na API da VADU"}')
			aadd(aResult,'{ "data": []}')
			aadd(aResult,dDatabase)
		Else
			aadd(aResult,PWA->PWA_RETVAD)
			aadd(aResult,PWA->PWA_RETJUR)
			aadd(aResult,PWA->PWA_DATA)
		EndIF

	ENDIF




Return aResult

