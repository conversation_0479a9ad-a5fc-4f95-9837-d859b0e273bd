#INCLUDE "TOTVS.CH"

User Function MSDOCOK()
    Local cProposta	:= ADY->ADY_PROPOS
    Local cAliasTrb	:= GetNextAlias()

    BeginSql Alias cAliasTrb
		SELECT ACB_CODOBJ, ACB_OBJETO
		FROM %table:ADY% ADY
		INNER JOIN %table:AOB% AOB ON ADY_FILIAL||ADY_PROPOS||ADY_PREVIS = AOB_CHAVE
			AND AOB.%notDel%
			AND AOB_ENTIDA = 'ADY'
		INNER JOIN %table:AC9% AC9 ON AOB_IDNOTA = AC9_CODENT
			AND AC9_ENTIDA = 'AOB'
			AND AC9_XTIPO = '2'
			AND AC9.%notDel%
		INNER JOIN %table:ACB% ACB ON AC9_FILIAL = ACB_FILIAL AND AC9_CODOBJ = ACB_CODOBJ
			AND ACB.%notDel%
		WHERE ADY_FILIAL = %xfilial:ADY%
		AND ADY_PROPOS = %exp:cProposta%
		AND ADY.%notDel%	 
	EndSql

    if (cAliasTrb)->(!eof())
        ADY->(RecLock("ADY", .F.))
		ADY->ADY_XURLSR := StrTran(GetURL("1"),"/orcamento","") + "download-external?type=PSM&download=" + Encode64(ADY->ADY_PROPOS+":"+(cAliasTrb)->ACB_CODOBJ)
        ADY->(MsUnLock())
    EndIf

    (cAliasTrb)->(dbCloseArea())
		
Return

/*/{Protheus.doc} GetURL
@description Busca a URL por ambiente logado
<AUTHOR>
@since 01/09/2019
@version 1.0
@param nTpURI, numerico, tipo de URL
@return cUrl
/*/

Static Function GetURL(nTpURI)
Local cUrl			:= ""
Local cTbPath		:= ""
Local cEnvOficiais	:= GetMV("TI_AMBOFIC",,"#TOTVS12|#TOTVS12_ES|#TOTVS12_EN|CRM|CRM_MI")			//Ambientes Oficiais

If nTpURI == "1"
	cTbPath		:= "EASWEB"
Else
	cTbPath		:= "SOSW01"
EndIf

DbSelectArea("ZX5")
ZX5->( DbSetOrder(1) )

If Alltrim( Upper(GetEnvServer()) ) $ cEnvOficiais
	cTbPath += "PRD001"
Elseif "_PRE" $ Upper(GetEnvServer()) .OR. "PRE_" $ Upper(GetEnvServer())
	cTbPath += "PRE001"
Else
	cTbPath += "DEV001"
EndIf

If ZX5->( DbSeek(xFilial("ZX5")+cTbPath) )
	cUrl := Alltrim( ZX5->ZX5_DESCRI )
EndIf

Return cUrl
