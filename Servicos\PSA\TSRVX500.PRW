#Include 'TOTVS.ch'

/*/{Protheus.doc} TSRVX500
Validar o agrupador de vendas se tem acesso a rotina.
<AUTHOR> | <PERSON>
@version   1.xx
@since     17/03/2022
/*/
User Function TSRVX500(cXModal)
	Local clQuery	:= "" 
	Local clAlias	:= GetNextAlias()
	Local clRet		:= ""
    Local lRet		:= .F.
	Local lSRVXPSA  := getMv('TI_XSVRPSA',,.F.)

	If lSRVXPSA
		clQuery := " SELECT ZX5_CHAVE "
		clQuery += " FROM " + RetSqlName("ZX5") + " ZX5 "
		clQuery += " WHERE ZX5.ZX5_FILIAL = '" + xFilial("ZX5") + "' "
		clQuery += " AND ZX5.ZX5_TABELA = 'XUNCOR' "
		clQuery += " AND ZX5.ZX5_CHAVE = '" + cXModal + "' "
		clQuery += " AND ZX5.D_E_L_E_T_ = ' ' "

		dbUseArea( .T., "TOPCONN", TCGENQRY(,, clQuery), clAlias, .F., .T.) 

		While (clAlias)->(!EOF())

			If !Empty(clRet)
				clRet += "/"+alltrim((clAlias)->ZX5_CHAVE)
			Else
				clRet += alltrim((clAlias)->ZX5_CHAVE)
			EndIF

			(clAlias)->(dbSkip())
		EndDO
		
		lRet := !Empty(clRet)

		(clAlias)->(dbCloseArea())
	Endif

Return(lRet)


