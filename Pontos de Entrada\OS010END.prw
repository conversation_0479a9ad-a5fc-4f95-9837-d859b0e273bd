#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWMVCDEF.CH"

//-------------------------------------------------------------------
/*/{Protheus.doc} OS010END
Pontos de entrada da tabela de Precos
<AUTHOR> 
@since  15/07/2024
@version 1.0
/*/
//-------------------------------------------------------------------
User Function OS010END()
Local nTipo     := ParamIXB[01] as numeric
Local nOpc      := ParamIXB[02] as numeric
Local bTTS      := {|| .T. } as codeblock
Local cAliasQry	:= GetNextAlias()
Local aCampos 	:= {}
Local cCodTab	:= DA1->DA1_CODTAB
Local lAtvSim   := nil
Local cEntida   := 'DA0'
Local cNivAgr	:= AllTrim(GetMv("TI_CODAGR",,"000112"))
Local nX 		:= 1

    BeginSQL Alias cAliasQry 
    SELECT DA1_ITEM,DA1_CODTAB,DA1_CODPRO,DA1_ATIVO,AZ0_CODNIV,AZ0_DE,PT6_CODNIV,POV_PRODUT,POV_EXISIM    
    FROM %Table:DA1% DA1    
    INNER JOIN  %Table:AZ0% AZ0 ON AZ0_FILIAL = %XFilial:AZ0%    
    AND AZ0.%NotDel%       
    AND AZ0_CODAGR = %Exp:AllTrim(cNivAgr)%    
    AND AZ0_ENTIDA = %Exp:AllTrim(cEntida)%
    AND AZ0_DE = DA1_CODTAB    
    INNER JOIN %Table:PT6% PT6 ON PT6_FILIAL = %XFilial:PT6%     
    AND PT6.%NotDel%      
    AND PT6_CODAGR = AZ0_CODAGR    
    AND PT6_CODNIV = AZ0_CODNIV    
    INNER JOIN  %Table:POV% POV ON POV_FILIAL = %XFilial:POV%     
    AND POV.%NotDel%        
    AND POV_CODAGR = PT6_CODAGR    
    AND POV_CODNIV = PT6_CODNIV    
    AND POV_PRODUT = DA1_CODPRO    
    WHERE DA1_FILIAL = %XFilial:DA1%     
    AND DA1.%NotDel%    
    AND DA1_CODTAB=%Exp:AllTrim(cCodTab)% 
    ORDER BY DA1_ITEM ASC
    EndSQL

	While !(cAliasQry)->(EOF()) 

         AAdd(aCampos, {(cAliasQry)->DA1_ITEM,;
                        (cAliasQry)->DA1_CODTAB,;
                        (cAliasQry)->DA1_CODPRO,;
                        (cAliasQry)->AZ0_CODNIV,;
                        (cAliasQry)->AZ0_DE,;
                        (cAliasQry)->PT6_CODNIV,;
                        (cAliasQry)->POV_PRODUT,;
                        (cAliasQry)->POV_EXISIM,;
                        (cAliasQry)->DA1_ATIVO})

       	(cAliasQry)->(dbSkip())
    EndDo
    IF nOpc == 4 .OR. nOpc == 3

        POV->(DBSelectArea("POV"))
        POV->(DBSetOrder(3))
        POV->(dbGoTop())

            For nX := 1 to Len(aCampos)
                If POV->(dbSeek(xFilial("POV")+cNivAgr+aCampos[nX][4]+aCampos[nX][3]))
                    If Upper(aCampos[nX][8]) $ "T" .OR. Upper(aCampos[nX][8]) $ "F"
                        lAtvSim := .F.
                        If aCampos[nX][9] == "1"
                            lAtvSim := .T.
                        elseif aCampos[nX][9] == "2"
                            lAtvSim := .F.
                        Endif
                    EndIf
                    RecLock("POV", .F.)
                        POV->POV_EXISIM := lAtvSim
                    MsUnLock()
                EndIf
            Next nX
    EndIf

	If Select(cAliasQry) > 0
		(cAliasQry)->(DbCloseArea())
	EndIf

Return( bTTS )
