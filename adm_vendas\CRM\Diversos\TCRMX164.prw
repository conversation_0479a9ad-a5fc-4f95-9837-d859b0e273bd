#include 'TOTVS.CH'

/*/{Protheus.doc} TCX164TS
Envio de dados de assinatura - TOTVS STORE
@type function
<AUTHOR>
@since 11/10/2018
@version 1.0
@param ${param}, ${param_type}, ${param_descr}
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
User Function TCX164TS

	Local aRet	 := {}
	Local lRet	 := .F.
	Local cUrl 	 := GetMV ("TI_URLNTFY",,"************:3000")
	Local cMetodo:= "/api/notify"
	Local oJson  := JsonUtil():New()
	Local oRest, oReq
	Local aHeader:= {}
	Local cTransaction := ''
	
	//Prepara o Rest.
	oRest:= FWREST():New(cUrl)
	oRest:SetPath(cMetodo)
	
	//Tratamento para o Header da API
	Aadd( aHeader, 'Content-Type:application/json' )
    
  	//Incluindo Valores no objeto oJson
    oJson:PutVal("entity",alltrim(POX->POX_CODIGO))
    oJson:PutVal("identifier",alltrim(POX->POX_CHAVE))
    
    //Setando os parametros no objeto oRest   
    oRest:SetPostParams(oJson:ToJson())
           
	//Enviando o objeto oRest
	oRest:Post(aHeader)

	lRet := ( AllTrim( oRest:oResponseH:cStatusCode ) == '200' .Or.;
				AllTrim( oRest:oResponseH:cStatusCode ) == '202' .Or.;
				AllTrim( oRest:oResponseH:cStatusCode ) == '204')
    If lRet
		if !empty(oRest:cResult)
        	if fWJsonDeserialize(alltrim(oRest:cResult),@oReq)
				cTransaction := oReq:transactionID

                RecLock("POX", .F.)	
					POX->POX_IDSTOR	:= cTransaction		
				MsUnLock()
			EndIf
		EndIf

		aAdd(aRet, .T.)
		aAdd(aRet, oRest:GetResult())
	Else
		If AllTrim( oRest:oResponseH:cStatusCode ) == '410' // Done, o recurso nao esta mais disponivel. deve ser retirado da fila.
			aAdd(aRet, .T.)
			aAdd(aRet, oRest:GetLastError())
		Else
			aAdd(aRet, .F.)
			aAdd(aRet, oRest:GetLastError())
		EndIf
	EndIf
	 	
Return(aRet)