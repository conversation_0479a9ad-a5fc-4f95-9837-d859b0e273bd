#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'FWMVCDEF.CH'

/*/{Protheus.doc} F770CPQ
Ponto de Entrada do Modelo de dados do Serasa
<AUTHOR>
@since  09/09/2015
@version P12
/*/
User Function F770CPQ()

Local cRet := ""
//---- identifica cliente de viagem
Local cParCliLj   := GetMV("MV_RESCLIP",,"")//no para,etro havera algo do tipo 999999|99
Local cCliViagem  := '' 
Local cLjViagem	  := ''
Local nCnt        := 1
Local cAux        := ''
Local lVerLoja    := .F.
	 	
cParCliLj := AllTrim(cParCliLj)
	
For nCnt := 1 to Len(cParCliLj)
	cAux := substr(cParCliLj,nCnt,1)
	
	If cAux == "|"
		lVerLoja := .T.
	Else
		If lVerLoja
			cLjViagem  += cAux
		Else	
			cCliViagem += cAux
		EndIf
	EndIf 
Next

//---- identifica cliente de viagem
if mv_par01==1
	
cRet := " NOT EXISTS( "
cRet += " 				SELECT * " 
cRet += " 				FROM (	SELECT A1_COD, A1_LOJA " 
cRet += " 						FROM 	"+RetFullName("SA1")+" SA1 "
cRet += " 						WHERE 	A1_COD BETWEEN ' ' AND 'ZZZZZZ'  "
cRet += " 								AND A1_LOJA BETWEEN ' ' AND 'ZZ' "

If !Empty(cCliViagem) .And. !Empty(cLjViagem)
	cRet += " AND ( A1_COD <> '" + cCliViagem + "' or A1_LOJA <> '" + cLjViagem + "' ) "
EndIf
	
cRet += " 								AND A1_XESTRAT = '1' "
cRet += " 								AND SA1.D_E_L_E_T_ = ' '  "
cRet += " 						UNION  "
cRet += " 						SELECT 	A1_COD, A1_LOJA  " 
cRet += " 						FROM 	"+RetFullName("SA1")+" SA1, "+RetFullName("PK2")+" PK2, "+RetFullName("FW2")+" FW2  "
cRet += " 						WHERE 	A1_COD BETWEEN '"+MV_PAR06+"' AND '"+MV_PAR08+"'  "
cRet += " 								AND A1_LOJA BETWEEN '"+MV_PAR07+"' AND '"+MV_PAR09+"'  "

If !Empty(cCliViagem) .And. !Empty(cLjViagem)
	cRet += " AND ( A1_COD <> '" + cCliViagem + "' or A1_LOJA <> '" + cLjViagem + "' ) "
EndIf
	cRet += " 								AND PK2_CLIENT = A1_COD AND PK2_LOJA = A1_LOJA "
	cRet += " 								AND PK2_SITUAC = FW2_SITUAC "
	cRet += " 								AND FW2_CODIGO = '"+GetMv("TI_SUSPSER",,"0012")+"'  "
	cRet += " 								AND PK2_DTSUSA <= '"+DtoS(dDataBase)+"'  "
	cRet += " 								AND PK2_DTSUSP >= '"+DtoS(dDataBase)+"'  "
	cRet += " 								AND PK2.D_E_L_E_T_ = ' '  "
	cRet += " 								AND SA1.D_E_L_E_T_ = ' '  "
	cRet += " 								AND FW2.D_E_L_E_T_ = ' '  "
	cRet += " 					)X  "
	cRet += " 					WHERE A1_COD = E1_CLIENTE  "
	cRet += " 					AND A1_LOJA = E1_LOJA  "
	cRet += " 				)  "
If !Empty(MV_PAR14)
	cRet += " AND EXISTS (  "
	cRet += " 			SELECT 	1  " 
	cRet += " 			FROM 	"+RetFullName("SA1")+" SA1  "
	cRet += " 			WHERE 	A1_COD BETWEEN '"+MV_PAR06+"' AND '"+MV_PAR08+"'  "
	cRet += " 					AND A1_LOJA BETWEEN '"+MV_PAR07+"' AND '"+MV_PAR09+"'  "
	
	If !Empty(cCliViagem) .And. !Empty(cLjViagem)
		cRet += " AND ( A1_COD <> '" + cCliViagem + "' or A1_LOJA <> '" + cLjViagem + "' ) "
	EndIf
	
	cRet += " 					AND SA1.A1_XCLIVIP IN "+FormatIn(AllTrim(MV_PAR14),";")  //('','1','2','3','4','5','6','7','8','9')FormatIn(AllTrim(MV_PAR14),";") 
	cRet += " 					AND SA1.D_E_L_E_T_ = ' '  "
	cRet += " 					AND A1_COD = E1_CLIENTE  "
	cRet += " 					AND A1_LOJA = E1_LOJA	  "
	cRet += " 	)  "
End If
end If 
Return cRet