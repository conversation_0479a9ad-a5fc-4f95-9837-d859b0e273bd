#include "tlpp-core.th"

Class   CreateCheckDTO
    Public Data    return_page             As  Character 
    Public Data    checkout_type           As  Character
    Public Data    description             As  Character
    Public Data    price                   As  Character
    Public Data    customer                As  Object
    Public Data    allowed_payment_forms   As  Array
    Public Data    tokens_to_generate      As  Array
    Public Data    valid                   As  Character

    Public  Method  new()                   As  Object
    Public  Method  toString()              As  Character
EndClass


Method  new()   Class   CreateCheckDTO
    Local oArrayForms      
    Local oArrayTokens    

    ::return_page             :=  ''
    ::checkout_type           :=  ''
    ::description             :=  ''
    ::price                   :=  ''
    ::customer                :=  JsonObject():New()

    ::customer['name'] := ''
    ::customer['document'] := ''
    ::customer['email'] := ''

    ::allowed_payment_forms   :=  {} 
    oArrayForms :=  JsonObject():New()
    oArrayForms['method'] := ''
    oArrayForms['installments'] := ''
    AAdd(::allowed_payment_forms, oArrayForms)

    ::tokens_to_generate      :=  {}
    oArrayTokens            :=  JsonObject():New()
    oArrayTokens['document']    :=  ''
    AAdd(::tokens_to_generate,oArrayTokens)

    ::valid                   :=  ''
Return Self



Method ToString() Class CreateCheckDTO
    Local oJson := JsonObject():New()
    oJson['return_page'] := ::return_page
    oJson['checkout_type'] := ::checkout_type
    oJson['description'] := ::description
    oJson['price'] := ::price
    oJson['customer'] := ::customer
    oJson['allowed_payment_forms'] := ::allowed_payment_forms
    oJson['tokens_to_generate'] := ::tokens_to_generate
    oJson['valid'] := ::valid

Return oJson:toJson()


