#Include 'Protheus.ch'

User Function TSRVA31A()
Local lRet		:= .T.
Local nInd		:= 0
Local aRet		:= {}
Local aParamBox	:= {}
Local aPergRet  := {}
Local aArea		:= GetArea()
Local oObj      := Nil

Local cPerg   	:= "TSRVMIGFIL"  // Pergunta do Relatorio
Local cTitulo   := "Selecione Grupo/Unid Negocio e Unid Servico"

Local bOk := {|| .T.}
Local aButtons := {}
Local lCentered := .T.
Local nPosx
Local nPosy
Local cLoad := "TSRVA31A_01"
Local lCanSave := .t.
Local lUserSave := .F.

Local cGrupo      := CriaVar( 'PE5_EMPPRJ', .F. )
Local cUnidNeg    := CriaVar( 'PE5_FILPRJ', .F. )
Local cGrpDest    := CriaVar( 'PE5_EMPPRJ', .F. )
Local cUniDest    := CriaVar( 'PE5_FILPRJ', .F. )

Local cClientDe	  := CriaVar( 'PE5_CLIENT', .F. )
Local cClientAte  := CriaVar( 'PE5_CLIENT', .F. )

Local cProjetDe  := CriaVar( 'PE5_PROJET', .F. )
Local cProjetAte := CriaVar( 'PE5_PROJET', .F. )

Local cUnidSrv    := Space( 99 )
Local nFilFat	 := 1

Aadd(aParamBox,{1, 'Empresa Origem'	,cGrupo	    ,"@!"	,	                      	,"SM0MRP" ,".T."	,060	,.T. } ) //Grupo de Empresa Origem
Aadd(aParamBox,{1, 'Filial Origem'	,cUnidNeg	,"@!"	, 	                      	,"SM0800" ,".T."	,060  	,.T. } ) //Unidade de Negocio Origem
Aadd(aParamBox,{1, 'Unid. Servico'	,cUnidSrv	,"@!"   ,"U_Sv011C01( 'MV_PAR03' )" ,         ,".T."	,060 	,.T. } ) //Unidade de Servico

Aadd(aParamBox,{1, 'Cliente De'		,cClientDe	,"@!"   , 							,"SA1CLI" ,".T."	,060 	,.F. } ) //Cliente De
Aadd(aParamBox,{1, 'Cliente Ate'	,cClientAte	,"@!"   , 							,"SA1CLI" ,".T."	,060 	,.T. } ) //Cliente Ate

Aadd(aParamBox,{1, 'Projeto De'		,cProjetDe	,"@!"   ,		 					,"PE5A"   ,".T."	,060 	,.F. } ) //Projeto De
Aadd(aParamBox,{1, 'Projeto Ate'	,cProjetAte ,"@!"  	,				 			,"PE5A"   ,".T."	,060 	,.T. } ) //Projeto Ate

Aadd(aParamBox,{1, 'Empresa Destino',cGrpDest   ,"@!"	,	                      	,"SM0MRP" ,".T."	,060	,.T. } ) //Grupo de Empresa Destino
Aadd(aParamBox,{1, 'Filial Destino',cUniDest	,"@!"	, 	                      	,"SM0800" ,".T."	,060  	,.T. } ) //Unidade de Negocio Destino

Aadd(aParamBox,{2, 'Alt FilFat Parc',nFilFat	,{'SIM', 'NAO' }					,60	,, .T. } ) //Valida se Altera FilFAT das Parcelas em Aberto Sim/Nao


lRet := ParamBox(aParamBox, cTitulo, @aPergRet, bOk, aButtons, lCentered, nPosx,nPosy, /*oMainDlg*/ , cLoad, lCanSave, lUserSave)
If !lRet
    lRet := .F.
Else
    If !ParamOk( aParamBox, aPergRet )
        //Help - Parametros Obrigatorios Nao Preenchidos, Processo Abortado pelo Sistema
        lRet := .F.
    Else
    	lAtuParc := IIf( ValType( aPergRet[ 10 ] ) == 'C', AllTrim( Upper( aPergRet[ 10 ] ) ) == 'SIM', IIf( ValType( aPergRet[ 10 ] ) == 'N', aPergRet[ 10 ]== 1, .F. ) )
        LjMsgRun( 'Selecionando Registros','Aguarde...', { || lRet := U_TSRVA031( aPergRet[ 1 ] , aPergRet[ 2 ], aPergRet[ 3 ], aPergRet[ 4 ], aPergRet[ 5 ], aPergRet[ 6 ], aPergRet[ 7 ], aPergRet[ 8 ], aPergRet[ 9 ], lAtuParc ) } )
    EndIf
EndIf

RestArea( aArea )
Return lRet

