#include 'protheus.ch'

/*/{Protheus.doc} TCRMX054
Retorna tipos de faturamento permitidos por modelo de escopo
<AUTHOR>
@since 13/04/2016
@version undefined
@param cModelo, characters, descricao
@param cPMS, characters, descricao
@type function
/*/
User Function TCRMX054(cModelo,cPMS)
Local aArea		:= GetArea()
Local aRet		:= {}
Local cTmp		:= GetNextAlias()

Default cModelo	:= ""

If Empty(cModelo)

	If Empty(cPMS)
		RestArea(aArea)
		Return(aRet)
	EndIf
	
	BeginSql Alias cTmp
		
	SELECT AF1_ORCAME
		FROM %table:AF5% AF5 
		INNER JOIN %table:AF1% AF1 
			ON AF1_FILIAL = %xFilial:AF1% 
			AND AF1_ORCAME = AF5_MODORI 
			AND AF1.%notDel%
		INNER JOIN %table:SZ7% SZ7 
			ON Z7_FILIAL = %xFilial:SZ7% 
			AND Z7_CODIGO = AF1_XGRUPO 
			AND SZ7.%notDel%
		WHERE AF5_FILIAL=%xFilial:AF5%
			AND AF5_ORCAME = %exp:cPMS% 
			AND AF5_MODORI <> ' ' 
			AND AF5.%notDel%
			
	EndSQl
	
	If !(cTmp)->(EoF())
		cModelo := (cTmp)->AF1_ORCAME
	EndIf
	
	(cTmp)->(dbCloseArea())
	RestArea(aArea)

	
EndIf

If Empty(cModelo)
	Return(aRet)
EndIf

dbSelectArea("PLC")
dbSetOrder(1)
dbSeek(xFilial("PLC")+cModelo)

Do While !PLC->(EoF()) .and. PLC->(PLC_FILIAL+PLC_ORCAME)==xFilial("PLC")+cModelo
	
	aAdd(aRet,PLC->PLC_TPFAT)
	
	PLC->(dbSkip())
	
EndDo

RestArea(aArea)
Return(aRet)