#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVJ008     
Job Responsavel por criar a P37 para consultar a API do PSA View  PAPEIS PSA
@type  Function
<AUTHOR> Menabue Lima
@since 27/10/2023
@version 1.0
	/*/
User Function TIRVJ008(nCount,nPage,cCodReq,cIni,cFim)
	Local oReceptor:=TIINTERCEPTOR():New()
	Local cFecthXml :=""
	Local cApiPSA:=GetMv("TI_RVJ008A" , .F., "RVF008")
	Local nMonth:=GetMv("TI_RVJ08NM" , .F., 5)
	Default nCount := GetMv("TI_RVJ08CT" , .F., 5000)
	Default nPage := 1
	Default cCodReq:=""
	Default cIni :=""
	Default cFim :=""
	cFecthXml +='<fetch no-lock="true" latematerialize="true" count="'+cValToChar(nCount)+'" page="'+cValToChar(nPage)+'">'
	cFecthXml +=	'<entity name="msdyn_timeentry">'
	cFecthXml +=		'<link-entity name="msdyn_project" from="msdyn_projectid" to="msdyn_project" link-type="outer" alias="projeto">'
	cFecthXml +=		  '<attribute name="ctm_codigo" alias="CODIGO_PROJETO" />'
	cFecthXml +=		  '<attribute name="msdyn_subject" alias="NOME_PROJETO" />'
	cFecthXml +=		  '<link-entity name="ctm_papelrecursoprojeto" from="ctm_projeto" to="msdyn_projectid" link-type="outer" alias="papel">'
	cFecthXml +=		    '<attribute name="ctm_papel" alias="PAPEL" />'
	cFecthXml +=		    '<link-entity name="bookableresource" from="bookableresourceid" to="ctm_recurso" link-type="outer" alias="recurso">'
	cFecthXml +=		    '   <attribute name="name" alias="NOME" />'
    cFecthXml +=		    '     <attribute name="ctm_st_codigo" alias="CODIGO_RECURSO" />'
	cFecthXml +=		  	  '<link-entity name="systemuser" from="systemuserid" to="userid" link-type="outer" alias="usuario">'
	cFecthXml +=		  	    '<attribute name="ctm_st_cpf" alias="CPF" />'
	cFecthXml +=		  	    '<attribute name="internalemailaddress" alias="EMAIL" />'
	cFecthXml +=		  	    '<attribute name="yomifullname" alias="NOME" />'
	cFecthXml +=		  	  '</link-entity>'
	cFecthXml +=		    '</link-entity>'
	cFecthXml +=		  '</link-entity>'
	cFecthXml +=		'</link-entity>'
	cFecthXml +=		'<filter>'
	cFecthXml +=			'<condition entityname="projeto" attribute="createdon" operator="last-x-months" value="'+cValToChar(nMonth)+'" />'
	cFecthXml +=		'</filter>'
	cFecthXml +=	'</entity>'
	cFecthXml +='</fetch>'
	cCodP37:= oReceptor:GetP37COD()
	BeginTran()

	Reclock("P37",.T.)
	P37->P37_FILIAL:= Xfilial("P37")
	P37->P37_COD :=cCodP37
	P37->P37_DATA:= ddatabase
	P37->P37_HORA:= TIME()
	P37->P37_PATH2:=EncodeUtf8("/api/data/v9.2/msdyn_timeentries?$count=true&fetchXml="+ESCAPE(cFecthXml))
	P37->P37_CODAPI:=cApiPSA
	If P36->(DbSeek(xFilial("P36")+P37->P37_CODAPI))
		P37->P37_URL   := P36->P36_URL
	EndIf
	P37->P37_METHOD:= "GET"
	P37->P37_ASYNC1:= "N"
	P37->P37_STATUS:= "0"
	P37->P37_TIPORE:= "1"//Envio
	P37->P37_CALLBA:= "U_TIRVF008"
	P37->P37_HEADER:= "Accept: application/json|OData-MaxVersion: 4.0|OData-Version: 4.0|"+'Prefer: odata.include-annotations="OData.Community.Display.V1.FormattedValue"'
	P37->P37_CODREQ:=cCodReq
	P37->P37_BODY  :='{"nPage":'+cValToChar(nPage)+', "nCount":'+cValToChar(nCount)+',"inicio":"'+cIni+'", "fim":"'+cFim+'"}'
	P37->(MsUnlock())
	EndTran()
	FreeObj(oReceptor)
Return
