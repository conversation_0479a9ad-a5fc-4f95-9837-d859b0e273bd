#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'FILEIO.CH'  

User Function ESPX010D(cTokenExt, cFileName)

    Local cFileContent := ''
    Local cLine        := ''
    Local cFilePath    := GetNewPar('FS_ACEDIR', '\system\') + cTokenExt +'\'

    Local nHdl         := 0
    Local nSize        := 0

    IF (nHdl := FOpen(cFilePath + cFileName)) > 0
        nSize := FSeek(nHdl, 0, 2)
        cFileContent := Space(nSize)

        FSeek(nHdl, 0, 0)
        FRead(nHdl, @cFileContent, nSize)

        FClose(nHdl)
    EndIF

Return cFileContent
