#INCLUDE 'FWMVCDEF.CH'
#INCLUDE 'TOTVS.CH'
#INCLUDE 'ESPA060.CH'

User Function ESPA060()

    Local oBrowse := FWMBrowse():New()

    oBrowse:Set<PERSON>lias('PVN')
    oBrowse:SetDescription(STR0001)
    oBrowse:Activate()

Return

Static Function MenuDef()

Return FWMVCMenu('ESPA060')

Static Function ModelDef()

    Local oStruPVN := FWFormStruct(1,'PVN')
    Local oStruPVO := FWFormStruct(1,'PVO')
    Local oModel   := MPFormModel():New('ESPA060M')

    oModel:AddFields('PVNMASTER', ,oStruPVN)
    oModel:SetDescription(STR0001)

    oModel:SetPrimaryKey({'PVN_FILIAL','PVN_PAIS','PVN_CODGRP'})
    oModel:AddGrid('PVO','PVNMASTER',oStruPVO)
    oModel:SetRelation('PVO',{{"PVO_FILIAL","XFilial('PVO')"},{"PVO_PAIS","PVN_PAIS"},{"PVO_CODGRP","PVN_CODGRP"}},PVO->(IndexKey(1)))

Return oModel

Static Function ViewDef()

    Local oModel   := FWLoadModel('ESPA060')
    Local oStruPVN := FWFormStruct(2,'PVN')
    Local oStruPVO := FWFormStruct(2,'PVO')
    Local oView    := FWFormView():New()

    oView:SetModel(oModel)

    oStruPVO:RemoveField('PVO_FILIAL')
    oStruPVO:RemoveField('PVO_PAIS')
    oStruPVO:RemoveField('PVO_CODGRP')

    oView:AddField('VIEW_PVN',oStruPVN,'PVNMASTER')
    oView:AddGrid('GRID_PVO',oStruPVO,'PVO')

    oView:CreateHorizontalBox('SUPERIOR',30)
    oView:CreateHorizontalBox('INFERIOR',70)

    oView:SetOwnerView('VIEW_PVN','SUPERIOR')
    oView:SetOwnerView('GRID_PVO','INFERIOR')

Return oView
