#include 'protheus.ch' 

/*/{Protheus.doc} TCRMX129
Regra primeiro vencimento acima de 90 dias
<AUTHOR>
@since 26/09/2017
@version 1.0
@return lRet, Retorna se regra foi atendida
@param cProposta, characters, descricao
@type function
/*/
User FUnction TCRMX130(cProposta)
Local aArea		:= GetArea()
Local aAreaADZ	:= ADZ->(GetArea())
Local lRet		:= .f.
Local nDIAVPAF:= GetMV("TI_DIAVPAF",,90)

dbSelectArea("SB1")
dbSetOrder(1)

dbSelectArea("ADZ")
dbSetOrder(3)
dbSeek(xFilial("ADZ")+ADY->(ADY_PROPOS+ADY_PREVIS))

__aCustomiz[1] := 0 
__aCustomiz[2] := "Regra 90 dias - VPAF"

Do While !ADZ->(EoF()) .and. ADZ->(ADZ_FILIAL+ADZ_PROPOS+ADZ_REVISA)==xFilial("ADZ")+ADY->(ADY_PROPOS+ADY_PREVIS)

	SB1->(dbSeek(xFIlial("SB1")+ADZ->ADZ_PRODUT))
	
	If ADZ->ADZ_XSITUA=="F" .and. Posicione("SBM",1,xFilial("SBM")+SB1->B1_GRUPO,"BM_XGRPREC")<>"1" 
		__aCustomiz[1] += ADZ->ADZ_TOTAL
	EndIF
	
	If DateDiffDay(ADZ->ADZ_DT1VEN , DATE() ) > nDIAVPAF
		lRet := .t.
	EndIf
	
	ADZ->(dbSkip())
	
EndDo

RestArea(aAreaADZ)
RestArea(aArea)
Return(lRet)
