#Include 'Protheus.ch'


User Function F645ALTSP() 
	Local cSitPDD	:= "  " 
	Local aAreaSE1 	:= SE1->(GetArea())
	Local cTitPai	:= SE1->E1_TITPAI
	Local cSituaca	:= " "
	Local aArea 	:= GetArea()
	Local nTamChv 	:= TamSX3("E1_PREFIXO")[1]+TamSX3("E1_NUM")[1]+TamSX3("E1_PARCELA")[1]+TamSX3("E1_TIPO")[1]
	
	
	IF !(SE1->E1_TIPO$ MVABATIM) 
		DbSelectArea("FRV")
	    FRV->(DbSetOrder(1))

		If FRV->(MsSeek(xFilial("FRV") + Alltrim(SE1->E1_SITUACA)))

			If FRV->FRV_SITPDD == "2" .And. !Empty(FRV->FRV_XSITUA)	
				cSitPDD	:= FRV->FRV_XSITUA
			EndIf

		EndIf
	else
		SE1->(DbSetOrder(1))
		If SE1->(DbSeek(xFilial("SE1",SE1->E1_FILIAL)+Substr(cTitPai,1,nTamChv) ))
			cSituaca :=SE1->E1_SITUACA
			If FRV->(MsSeek(xFilial("FRV") + Alltrim(cSituaca)))
				If FRV->FRV_SITPDD == "2" .And. !Empty(FRV->FRV_XSITUA)	
					cSitPDD	:= FRV->FRV_XSITUA
				EndIf
			endif	
		EndIf 
	Endif	
		   
	RestArea(aArea)
	RestArea(aAreaSE1)
Return cSitPDD


