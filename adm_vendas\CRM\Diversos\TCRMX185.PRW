#INCLUDE 'TOTVS.CH'

/*/{Protheus.doc} TCRMX185
	Busca proximo codigo valida de uma tabela generica ZX5
	@type  Function
	<AUTHOR>
	@since 05/08/2019
	@version version
	@param cZX5<PERSON><PERSON><PERSON>, caracter, codigo da tabela 
	@return cProxCod, caracter, proximo codigo valid
	@example
	(examples)
	@see (links_or_references)
/*/
User Function TCRMX185(cZX5Tabela)
Local cProxCod		:= "000000"
Local cQuery		:= ""
Local _cAliasRet	:= ""

cQuery := "SELECT MAX(ZX5_CHAVE) CODIGO" + CRLF
cQuery += "FROM "+RetSqlName("ZX5")+" ZX5" + CRLF
cQuery += "WHERE ZX5_FILIAL = '"+XFilial("ZX5")+"' " + CRLF
cQuery += "      AND ZX5_TABELA = '"+cZX5Tabela+"'" + CRLF
cQuery += "      AND D_E_L_E_T_ = ' '" + CRLF

_cAliasRet := MPSysOpenQuery(cQuery)

( _cAliasRet )->( DbGoTop() )

While ( _cAliasRet )->( !EoF() )

	cProxCod := ( _cAliasRet )->CODIGO

	( _cAliasRet )->( DbSkip() )
End

( _cAliasRet )->( DbCloseArea() )

cProxCod := Soma1( cProxCod )

Return cProxCod
