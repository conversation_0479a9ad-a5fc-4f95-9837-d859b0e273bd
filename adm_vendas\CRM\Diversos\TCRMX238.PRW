#Include 'TOTVS.ch'

/*/{Protheus.doc} TCRMX238
Retorno dos emails para envio de wf RMX226.
<AUTHOR> | <PERSON>
@version   1.xx
@since    30/12/2022
/*/
User Function TCRMX238(cUnidNeg)
	Local clAlias	:= GetNextAlias()
	Local cRet		:= ""
    
	BeginSql Alias clAlias

		SELECT ZX5_CHAVE2
		FROM %table:ZX5% ZX5
		WHERE ZX5.ZX5_FILIAL = %xFilial:ZX5%
			  AND ZX5.ZX5_TABELA = 'RMX226'
			  AND ZX5.ZX5_CHAVE = %exp:cUnidNeg%
			  AND ZX5.%notdel%

	EndSql
	
	(clAlias)->(dbGoTop())

	While (clAlias)->(!EOF())

		If !Empty(cRet)
			cRet += ";"+alltrim(usrRetMail(alltrim((clAlias)->ZX5_CHAVE2)))
		Else
			cRet += alltrim(usrRetMail(alltrim((clAlias)->ZX5_CHAVE2)))
		EndIF

		(clAlias)->(dbSkip())
	EndDO
    
	(clAlias)->(dbCloseArea())

Return(cRet)
