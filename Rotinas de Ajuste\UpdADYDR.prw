#INCLUDE "TOTVS.CH"
#include 'fwmvcdef.ch'
#Include "TOPCONN.CH"
// Rotina de Ajuste para alimentar os campos data
// Inicio e Fim previsto
// <PERSON> 28/09/2021

User Function UpdADYDR()
    Local oPrecif
    Local mSQL   := ""
    Local cAlias := ""
    Local aParamSch 	:= {'00','00001000100'}
    
    RPCSetEnv(aParamSch[1],aParamSch[2])
    
    dbSelectArea("ADY")

    cAlias := GetNextAlias()

	mSQL := " SELECT ADZ_PROPOS, MAX(ADZ_REVISA) ADZ_REVISA,ADY_OPORTU, "
	mSQL += " ADY_REVISA, ADY_ENTIDA, ADY_CODIGO, ADY_LOJA, ADY_STATUS, "
	mSQL += " ADY_DATA, ADY_VEND, ADZ_PMS, ADZ_PMSVER "
    mSQL += " FROM " + RetSqlName("ADZ") + " ADZ "
	mSQL += " INNER JOIN " + RetSqlName("AF1") + " AF1 ON AF1_FILIAL ='" + xFilial("AF1") + "' "
	mSQL += "   AND AF1_ORCAME = ADZ_PMS "
	mSQL += "   AND AF1_VERSAO = ADZ_PMSVER "
	mSQL += "   AND AF1.D_E_L_E_T_=' ' "
	mSQL += " INNER JOIN " + RetSqlName("ADY") + " ADY ON ADY_FILIAL ='" + xFilial("ADY") + "' "
	mSQL += "   AND ADY_PROPOS = ADZ_PROPOS "
	mSQL += "   AND ADY_PREVIS = ADZ_REVISA "
	mSQL += "   AND ADY.D_E_L_E_T_=' ' "
	mSQL += " WHERE ADZ_FILIAL ='" + xFilial("ADZ") + "' "
	mSQL += " 	AND ADZ.D_E_L_E_T_=' ' "
	mSQL += " GROUP BY ADZ_PROPOS, ADY_OPORTU, ADY_REVISA, ADY_ENTIDA, "
	mSQL += "   ADY_CODIGO, ADY_LOJA, ADY_STATUS, ADY_DATA, ADY_VEND, "
    mSQL += "   ADZ_PMS , ADZ_PMSVER "
	mSQL += " ORDER BY ADZ_PROPOS "
	


	TCQUERY mSQL NEW ALIAS (cAlias)
		
	DbSelectArea(cAlias)		
	(cAlias)->(DbGotop())
    
   			
	//Se existe um termo busco entrega do mesmo na PBU
	While !(cAlias)->(Eof())


        dbSelectArea("AF1")
        AF1->( dbSetOrder(8) )      //AF1_ORCAME+AF1_VERSAO		
        if AF1->( dbSeek(xFilial("AF1")+(cAlias)->ADZ_PMS+(cAlias)->ADZ_PMSVER) )
        
            oPrecif := JsonObject():New()
            cErro := oPrecif:fromJson(AF1->AF1_XPRECI)
            If empty(cErro)
                ADY->(dbSetOrder(1)) // ADY_FILIAL, ADZ_PROPOS.
                If ADY->(dbSeek(xFilial("ADY") + (cAlias)->ADZ_PROPOS, .F.))
				    ADY->(RecLock("ADY",.F.))
					ADY->ADY_XDTPRI := Stod(Replace(oPrecif["precificacao"]["dataini"],"-","")) 
                    ADY->ADY_XDTPRF := Stod(Replace(oPrecif["precificacao"]["datafim"],"-","")) 
				    ADY->( MsUnLock() )
                Endif    
            Endif
        
        Endif

        (cAlias)->(DbSkip())

	Enddo
    //Alert("Dados Processado....")

	(cAlias)->(DbCloseArea())	

    RPCClearEnv()
    
Return
