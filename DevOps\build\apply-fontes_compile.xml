<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project basedir="." default="compile">

	<tstamp />

	<!-- car<PERSON><PERSON> as variaveis de ambiente -->
	<property environment="env" />
	<property name="output" 	value="${env.BUILD_SOURCESDIRECTORY}\devops\build\output" />
	<property name="src" 		value="${env.BUILD_SOURCESDIRECTORY}" />
	<property name="fail" 		value="${env.ERRO_PIPELINE}" />

	<!--	Dependencias 	-->
	<taskdef resource="net/sf/antcontrib/antcontrib.properties">
		<classpath>
			<pathelement location="${env.ANT_HOME}/lib/ant-contrib-0.3.jar"/>
		</classpath>
	</taskdef>

	<!-- Preparando diretórios e arquivos  -->
	<mkdir dir="${output}" />
	<delete file="${env.APPSERVER_COMPILE_OUTPUT}\compile_errors.log" />
	<delete file="${env.APPSERVER_COMPILE_OUTPUT}\compile_success.log" />
	<!-- Target de preparação para compilação dos fontes  -->
	<target name="compile" description="Compila todos os fontes de um projeto">

		<!-- extensões reconhecidas pelo compilador AdvPL  -->
		<patternset id="sources.advPL">
			<include name="**/*.PRW" />
			<include name="**/*.PRG" />
			<include name="**/*.PRX" />
			<include name="**/*.APH" />
			<include name="**/*.AHU" />
			<include name="**/*.APL" />
			<include name="**/*.APW" />
			<include name="**/*.PPP" />
			<include name="**/*.NGC" />
			<include name="**/*.NGCO" />
			<include name="**/*.JPEG" />
			<include name="**/*.JPG" />
			<include name="**/*.PNG" />
			<include name="**/*.BMP" />
			<include name="**/*.js" />
			<include name="**/*.css" />
			<include name="**/*.TRES" />
			<include name="**/*.TLPP" />
			<include name="**/*.APP" />
		</patternset>

		<!-- extensões reconhecidas pelo compilador AdvPL  -->
		<fileset dir="${src}\" id="filesToCompile" casesensitive="false">
			<patternset refid="sources.advPL" />
		</fileset>

		<pathconvert property="programs" refid="filesToCompile">
			<mapper type="identity" />
		</pathconvert>

		<echo file="${output}\compile.lst" message="${programs}" />

		<antcall target="APPSERVERCompile">
			<param name="files" value="${output}\compile.lst" />
		</antcall>
	</target>
	
	<!-- target de compilação dos fontes indentificados -->
	<target name="APPSERVERCompile">

		<!-- Imprime no console do pipeline informações sobre a execução do comando de compilação -->
		<echo message="Dados da compilação:" />
		<echo message="- Ambiente...........: ${env.COMPILE_ENVIRONMENT}" />
		<echo message="- Nome da branch.....: ${env.BUILD_SOURCEBRANCHNAME}" />
		<echo message="- Local da branch....: ${env.BUILD_SOURCEBRANCH}" />
		<echo message="- Includes da branch.: ${src}\includes" />
		<echo message="- CMD a executar.....: ${env.APPSERVER_PATH}\${env.APPSERVER_APP} output=output\APPSERVERCompile.log -compile -files=${files} -includes=${src}\includes -env=${env.COMPILE_ENVIRONMENT} -outreport=${output} "/>

		<!-- Executa CMD de compilaçao -->
		<sequential>
		<exec executable="${env.APPSERVER_PATH}\${env.APPSERVER_APP}">
			<arg value="-compile" />
			<arg value="-files=${files}" />
			<arg value="-includes=${src}\includes" />
			<arg value="-env=${env.COMPILE_ENVIRONMENT}" />
			<arg value="-outreport=${output}" />
		</exec>
		</sequential>	

		<!-- Analisa o log gerado pelo CMD de compilação para identificar registros de erros -->
		<if>
			<length file="${output}\compile_errors.log" when="greater" length="0"/>
			<then>
				<echo message="Envio de erro na sala google meet"/>
				<exec executable="powershell" >
					<arg value="${env.BUILD_SOURCESDIRECTORY}\devops\build\msgGoogleChatv01.ps1 " />
					<arg value="-pathText ${output}\compile_errors.log " />
					<arg value="-title Compilação:${env.BUILD_SOURCEBRANCHNAME} " />
					<arg value="-subtitle Fontes:${env.BUILD_REPOSITORY_NAME} " />
					<arg value="-typeMessage E " />
				</exec>
				<if>
					<equals arg1="${fail}" arg2="true"/>
					<then>
						<loadfile property="APPSERVERCompile_errorsLog" srcfile="${output}\compile_errors.log" />
						<fail message="${APPSERVERCompile_errorsLog}" />
					</then>
				</if>

			</then>
		</if>
	</target>
</project>