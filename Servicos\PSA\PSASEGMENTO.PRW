#Include "PROTHEUS.ch"
#Include "TOTVS.ch"
#Include "RESTFUL.ch"
#Include "FWMVCDef.ch"
#Include "FILEIO.ch"
#Include "TOPCONN.ch"

//-------------------------------------------------------------------
/*/{Protheus.doc} SEGMENTOPSA
CLASSE Segmento do PSA
@protected
<AUTHOR>
@since      06/12/2018
@version
/*/
//-------------------------------------------------------------------
class SegmentoPSA from GenericoPSA

    // ATRIBUTOS

    // METODOS GETTERS AND SETTERS

    // METODOS
    method New() CONSTRUCTOR
    method PostSegmento()
    method PutSegmento()
    method List()
    method Create()

endClass

//-------------------------------------------------------------------
/*/{Protheus.doc} NEW
Metodo Contrutor CLASSE SegmentoPSA
@protected
<AUTHOR>
@since      06/12/2018
@version
/*/
//-------------------------------------------------------------------
method New(httpAmb,tvsAmb) class SegmentoPSA

    if valtype(httpAmb) == 'C'
        self:ambPSA	:= httpAmb
    else
        self:ambPSA	:= ''
    endif
    
    if valtype(tvsAmb) == 'C'
        self:ambTVS	:= tvsAmb
    else
        self:ambTVS	:= ''
    endif

    // executa o construtor da classe pai
     _Super:New(self:ambPSA, self:ambTVS)

return

//-------------------------------------------------------------------
/*/{Protheus.doc} PostSegmento
Metodo para incluir segmento no PSA 
<AUTHOR>
@since 28/07/2017
@method PostSegmento
@verbo POST
@return self
/*/
//-------------------------------------------------------------------
method PostSegmento(aDados) class SegmentoPSA

    Local cID       := ''
    Local cMethod	:= 'ctm_segmentos'
    Local cFieldID  := 'ctm_segmentoid'
    Local aLog      := {}
    Local oSegmento := JsonUtil():New()
    Local cTokenPSA := self:accessToken()
    Local cItCID    := ''
    Local nEspera   := getMv('TI_TESPPSA',,5000) // 5 segundos - tempo de espera
    Local nVezes    := getMv('TI_NTENPSA',,3) // quantidade de tentativas
    Local nX        := 0

    default aDados  := {}

    if len(aDados) == 0
        return self
    endif

    // ID - ITEM CONTABIL
    If ! Empty(aDados[1,6])
        self:setEntityID('ctm_itemcontabils')                   // entidade ou metodo
        self:setParamID('ctm_codigodoprotheus')                 // campo para busca
        self:setValueID( aDados[1,6] )       					// valor para busca
        self:setPropID('ctm_itemcontabilid')                    // campo de retorno ID
        For nX:=1 To nVezes
            cItCID := self:GetID()                              // metodo que retorna os ID, conforme parametros setados acima
            If ! Empty(cItCID) .And. Upper(Left(cItCID,4)) <> 'ERRO'    
                Exit
            EndIf    
            Sleep(nEspera)
        Next nX    
    Endif

    oSegmento:PutVal("ctm_codigo"	, aDados[1,1]	)                   //AOV_CODSEG
    oSegmento:PutVal("ctm_name"		, U_LimpaTxt(aDados[1,2]) )         //AOV_DESSEG

    if val(aDados[1,4]) > 0
    	oSegmento:PutVal("ctm_agrupa"    , val(aDados[1,4])	)  //AOV_XAGRUP -> NUMERICO NO PSA
    endif

    if !empty( aDados[1,3])
        oSegmento:PutVal("ctm_macsegid"	, aDados[1,3]	)   //AOV_MACSEG
    endif

    oSegmento:PutVal("ctm_tipsegmento"	, aDados[1,5]	)       //AOV_PRINC

    If ! Empty(cItCID)
	    oSegmento:PutVal("<EMAIL>"     , "/ctm_itemcontabils(" + cItCID + ")" )  //item contabil
    EndIf    

    If ! Empty(aDados[1,1])
        cID := self:PostPSA( aDados, oSegmento:ToJson(), cMethod, cFieldID ,cTokenPSA )
    Else
        cID := 'Erro: Codigo do segmento vazio'    
    EndIf

    if upper( left(cID,4) ) == 'ERRO'
        // GRAVA A FALHA QUE HOUVE NA INTEGRACAO COM PSA
        aLog := { 'AOV' , aDados[3] , cID , aDados[2], oSegmento:ToJson(),'2','POST', .F., self:GetUrlMAN() + self:GetPathMAN() + cMethod, '{}' , '', '' } // 1=ALIAS;2=USER;3=ERROR;4=RECNO;5=BODY;6=STATUS;7=METHOD;8=SE DELETA O REGISTRO;9=URI;10=HEADER;11=PARTES;12=REGRA
        self:SetLog(aLog)
    endif

    iif( ValType( oSegmento ) == 'O', ( FreeObj( oSegmento ), oSegmento := Nil ), Nil )

return self

//-------------------------------------------------------------------
/*/{Protheus.doc} PutSegmento
Metodo para alterar segmento no PSA 
<AUTHOR>
@since 28/07/2017
@method PutSegmento
@verbo POST
@return self
/*/
//-------------------------------------------------------------------
method PutSegmento(aDados) class SegmentoPSA

    Local aLog := {}
    Local oSegmento := JsonUtil():New()
    Local cMethod	:= 'ctm_segmentos'
    Local cFieldID  := 'ctm_segmentoid'
    Local cVerbo    := "PATCH"
    Local cRetPut	:= ''
    Local cID       := ''
    Local cMethodLog:= ''
    Local cTokenPSA := self:accessToken()
    Local cItCID    := ''
    Local nEspera   := getMv('TI_TESPPSA',,5000) // 5 segundos - tempo de espera
    Local nVezes    := getMv('TI_NTENPSA',,3) // quantidade de tentativas
    Local nX        := 0

    default aDados  := {}

    if len(aDados) == 0
        return self
    endif

    // ID - ITEM CONTABIL
    If ! Empty(aDados[1,6])
        self:setEntityID('ctm_itemcontabils')                   // entidade ou metodo
        self:setParamID('ctm_codigodoprotheus')                 // campo para busca
        self:setValueID( aDados[1,6] )       					// valor para busca
        self:setPropID('ctm_itemcontabilid')                    // campo de retorno ID
        For nX:=1 To nVezes
            cItCID := self:GetID()                              // metodo que retorna os ID, conforme parametros setados acima
            If ! Empty(cItCID) .And. Upper(Left(cItCID,4)) <> 'ERRO'    
                Exit
            EndIf    
            Sleep(nEspera)
        Next nX    
    Endif

    // SET OS VALORES PARA BUSCAR O ID
    If ! Empty(aDados[1,1])
        self:setEntityID('ctm_segmentos')   // entidade ou metodo
        self:setParamID('ctm_codigo')       // campo para busca
        self:setValueID(aDados[1,1])        // valor para busca
        self:setPropID('CTM_SEGMENTOID')    // campo de retorno ID
        For nX:=1 To nVezes
            cID := self:GetID()                 // metodo que retorna os ID, conforme parametros setados acima
            If Upper(Left(cID,4)) <> 'ERRO'    
                Exit
            EndIf    
            Sleep(nEspera)
        Next nX    
    Else
        cID := 'Erro: Codigo do segmento vazio'    
    EndIf

    cMethodLog	:= 'ctm_segmentos(' + cID + ')'

    oSegmento:PutVal("ctm_codigo"	, aDados[1,1]	)                   //AOV_CODSEG
    oSegmento:PutVal("ctm_name"		, U_LimpaTxt(aDados[1,2]) )         //AOV_DESSEG

    if val(aDados[1,4]) > 0
    	oSegmento:PutVal("ctm_agrupa"    , val(aDados[1,4])	)  //AOV_XAGRUP -> NUMERICO NO PSA
    endif

    if !empty( aDados[1,3])
        oSegmento:PutVal("ctm_macsegid"	, aDados[1,3]	)   //AOV_MACSEG
    endif

    oSegmento:PutVal("ctm_tipsegmento"	, aDados[1,5]	)       //AOV_PRINC

    If ! Empty(cItCID)
	    oSegmento:PutVal("<EMAIL>"     , "/ctm_itemcontabils(" + cItCID + ")" )  //item contabil
    EndIf    

    // ALTERACAO (se encontrou o ID do segmento)
    If ! Empty(cID) .And. Upper(Left(cID,4)) <> 'ERRO'
        cRetPut := self:PutPSA(oSegmento:ToJson(), cMethod, cID, cTokenPSA)
    // INCLUSAO (se NAO encontrou o ID do segmento)
    ElseIf Empty(cID) 
        cRetPut    := self:PostPSA( aDados, oSegmento:ToJson(), cMethod, cFieldID ,cTokenPSA )
        cVerbo     := "POST"
        cMethodLog := cMethod 
    // ERRO
    ElseIf Upper(Left(cID,4)) == 'ERRO'    
        cRetPut    := cID
    EndIf

    if upper( left(cRetPut,4) ) == 'ERRO'
        aLog := { 'AOV' , aDados[3] , cRetPut , aDados[2], oSegmento:ToJson(),'2',cVerbo, .F., self:GetUrlPathGen() + cMethodLog, '{}' , '', '' } // 1=ALIAS;2=USER;3=ERROR;4=RECNO;5=BODY;6=STATUS;7=METHOD;8=SE DELETA O REGISTRO;9=URI;10=HEADER;11=PARTES;12=REGRA
        self:SetLog(aLog)
    endif

    iif( ValType( oSegmento ) == 'O', ( FreeObj( oSegmento ), oSegmento := Nil ), Nil )

return self

/*/{Protheus.doc} List
Metodo para listar segmento no PSA 
<AUTHOR>
@since 29/09/2020
@method List
@return self
/*/
method List() class SegmentoPSA

Local cAliasTrb := GetNextAlias()
Local nTotReg   := 0
Local nMacSeg   := 0
Local cCompSeg  := ''
Local cItCID    := ''
Local aJson     := {}
Local oSegmento := Nil
Local oSegmentos:= JsonUtil():New()

BeginSql Alias cAliasTrb
    SELECT *
    FROM %table:AOV% AOV
    WHERE AOV_FILIAL = %xFilial:AOV%
    AND AOV.%notDel%
EndSql

(cAliasTrb)->( dbEval( { || nTotReg++ },,{ || !Eof() } ) )
(cAliasTrb)->(dbGoTop())

oSegmentos:PutVal('total',nTotReg)
oSegmentos:PutVal('value',aJson)

While (cAliasTrb)->(!eof())

    nMacSeg := 0
        
    iif( (cAliasTrb)->AOV_PRINC == '1', cCompSeg := ' ( Segmento )', cCompSeg := ' ( Sub-Segmento )' )

    if (cAliasTrb)->AOV_MACSEG == '000001'
        nMacSeg := 1
    elseif (cAliasTrb)->AOV_MACSEG == '000002'
        nMacSeg := 2
    endif

    oSegmento := JsonUtil():New()
    oSegmento:PutVal('ctm_codigo'       ,(cAliasTrb)->AOV_CODSEG)
    oSegmento:PutVal('ctm_name'         ,U_LimpaTxt((cAliasTrb)->AOV_DESSEG) + cCompSeg)
    oSegmento:PutVal('ctm_agrupa'       ,iif(val((cAliasTrb)->AOV_XAGRUP)>0,val((cAliasTrb)->AOV_XAGRUP),0))
    oSegmento:PutVal("ctm_macsegid"	    ,iif(nMacSeg == 0, '', nMacSeg))
    oSegmento:PutVal("ctm_tipsegmento"  ,iif( (cAliasTrb)->AOV_PRINC == '1',961600000,961600001))
    
    self:setEntityID('ctm_itemcontabils')
    self:setParamID('ctm_codigodoprotheus')
    self:setValueID( (cAliasTrb)->AOV_XITECO )
    self:setPropID('ctm_itemcontabilid')
    cItCID := self:GetID()
    oSegmento:PutVal("ctm_itemcontabil" ,iif(!Empty(cItCID) .And. Upper(Left(cItCID,4)) <> 'ERRO',cItCID,''))

    aadd(aJson,oSegmento)

    (cAliasTrb)->(dbSkip())
End

(cAliasTrb)->(dbCloseArea())

return oSegmentos:ToJson()

/*/{Protheus.doc} Create
Metodo Create Classe SegmentoPSA
Criar Create
@protected
<AUTHOR>
@since      28/09/2020
/*/
method Create() class SegmentoPSA

Local oItem 	:= JsonUtil():New()
Local nX        := 0
Local cIdGet    := ''
Local cID       := ''
Local cToken    := ''
Local oAux      := Nil
Local aJson		:= {}
Local oResp   	:= Nil
Private oData   := Nil

oItem:PutVal("value",aJson)

if FwJsonDeserialize(self:List(),@oData) 

    cToken := self:AccessToken()

    For nX := 1 to Len(oData:value)

        self:setEntityID('ctm_segmentos')
        self:setParamID('ctm_codigo')
        self:setValueID( oData:value[nX]:ctm_codigo )
        self:setPropID('ctm_segmentoid')
        self:setTokenAPI(cToken)
        cIdGet := self:GetID()

        // VERIFICA SE NAO EXISTE, PARA CADASTRAR
        if Empty(cIdGet)
            
            oAux := JsonUtil():New()
            oAux:PutVal("ctm_codigo"        , oData:value[nX]:ctm_codigo    ) 
            oAux:PutVal("ctm_name"          , oData:value[nX]:ctm_name      )
            oAux:PutVal("ctm_tipsegmento"   , oData:value[nX]:ctm_tipsegmento	)

            if oData:value[nX]:ctm_agrupa > 0
                oAux:PutVal("ctm_agrupa"    , oData:value[nX]:ctm_agrupa	) 
            endif 

            if !empty(oData:value[nX]:ctm_macsegid)
                oAux:PutVal("ctm_macsegid"	, oData:value[nX]:ctm_macsegid	)
            endif
            if !Empty(oData:value[nX]:ctm_itemcontabil)
                oAux:PutVal("<EMAIL>"     , "/ctm_itemcontabils(" + oData:value[nX]:ctm_itemcontabil + ")" )
            endif

            cID := self:PostPSA({}, oAux:ToJson(), 'ctm_segmentos', 'ctm_segmentoid', cToken, .F., .T.)
            oResp := JsonUtil():New()
            oResp:PutVal("ctm_codigo"   ,oData:value[nX]:ctm_codigo )
            oResp:PutVal("ctm_name"     ,oData:value[nX]:ctm_name )
            oResp:PutVal("message"      ,iif(upper(left(cID,5)) == 'ERROR', cID, 'Cadastrado Id: ' + cID) )
            aadd(aJson,oResp)
        endif
    Next
endif

return oItem:ToJson()