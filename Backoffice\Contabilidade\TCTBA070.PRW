#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"


 /*/{Protheus.doc} TCTBA070
API responsável por retornar os registros intercompany da P57
@type  Function
@since 20/06/2023
@version 12.1.33
tipo - "analitico", "sintetico", "empresas", "processos"
/*/
User Function TCTBA070(cBody)
	Local lRet			  := .T., nA, nB, nC, nD
	Local oResponse
	Local oDataBase   := nil


	Local cQuery      := ''
	Local oJson       := JsonObject():new()
	Local oBody       := NIL
	Local oEmpresas   := NIL
	Local oProcessos  := NIL
	Local oIds  	  := NIL
	Local oContas     := NIL

	Local cRetoJson   := NIL
	Local cOrder      := ''
	Local nPage       := 1
	Local nPageSize   := 20
	Local cErroBlk    := ''
	Local aEmpresas   := {}
	Local cEmpresas   := ""
	Local cConta      := ""

	Local aProcessos  := {}
	Local aIds  	  := {}
	Local aContas	  := {}
	Local cProcessos  := ""
	Private cWhere      := ''
	Private lEmpresas   := .F.
	Private lProcessos  := .F.
	Private lEventos    := .F.
	Private lSintetico  := .F.
	Private lDetalhes	  := .F.
	Private lContas	  := .F.
	Private oException	:= ErrorBlock({|e| cErroBlk := + e:Description + e:ErrorStack, lRet := .F. })
	DEFAULT cBody :=""
	if(!Empty(cBody))
		oBody:= oJson:FromJson(cBody)
	Else
		oBody:= oJson:FromJson(PARAMIXB[2])
	EndIF

	If VALTYPE(oBody) == 'U'
		oFilter := oJson:GetJsonObject("filter")
		If ValType(oFilter) == "J"

			nPage       :=oFilter:GetJsonObject("page")
			nPageSize   :=oFilter:GetJsonObject("pagesize")
			cTipo       :=oFilter:GetJsonObject("tipo")
			cCompet     :=oFilter:GetJsonObject("competencia")
			oContas      :=oFilter:GetJsonObject("contas")
			oEmpresas   :=oFilter:GetJsonObject("empresas")
			oProcessos  :=oFilter:GetJsonObject("processos")
			oIds 		:=oFilter:GetJsonObject("ids")
			Do Case
			Case cTipo = "empresas"
				lEmpresas  := .T.
			Case cTipo = "eventos"
				lEventos   := .T.
			Case cTipo = "processos"
				lProcessos := .T.
			Case cTipo = "sintetico"
				lSintetico := .T.
			Case cTipo = "detalhes"
				lDetalhes := .T.
			Case cTipo = "analicontas"
				lContas := .T.
			EndCase

			If !lProcessos .and. !lEventos
				For nA = 1 to Len(oEmpresas)
					AADD(aEmpresas,StrToKArr(oEmpresas[nA] ,"|"))
				Next
			EndIf

			If lProcessos
				For nB = 1 to Len(oProcessos)
					AADD(aProcessos,oProcessos[nB])
				Next
			EndIf
			For nB = 1 to Len(oContas)
				AADD(aContas,oContas[nB])
			Next
			Do Case
			Case lEventos
				cWhere  := " ZX5_FILIAL = '" + FwxFilial("ZX5") + "' "
				cWhere  += " AND ZX5_TABELA = 'INTCMP' "
				cWhere  += " AND ZX5_CHAVE = '000005' AND D_E_L_E_T_ = ' ' "
			Case lDetalhes
				cWhere      :=" P58_FILIAL = '"+ FWxFilial('P58') +"' "
				cWhere      +=" AND P58.D_E_L_E_T_ = ' '"
				cWhere      +=" AND P58_COMPET = '"+cCompet+"'"


			OtherWise
				cWhere      :=" P57_FILIAL = '"+ FWxFilial('P57') +"' "
				cWhere      +=" AND P57.D_E_L_E_T_ = ' '"
				cWhere      +=" AND P57_STATUS = 'C'"
				cWhere      +=" AND P57_COMPET = '"+cCompet+"'"
			EndCase



			If !Empty(aEmpresas)
				cEmpresas += " ( "
				For nC = 1 to Len(aEmpresas)
					cEmpresas += " ( P57_CGCORI = '"+aEmpresas[nC,1]+"' AND P57_CGCDES='"+aEmpresas[nC,2]+"') "
					If Len(aEmpresas) <> nC
						cEmpresas += " OR "
					EndIF

				Next
				cEmpresas += " )"

				cWhere += " AND " + cEmpresas
			EndIf

			If !Empty(aProcessos)
				cProcessos += " ("
				For nD = 1 to Len(aProcessos)
					cProcessos += " P57_PROC = '"+aProcessos[nD]+"'"
					If Len(aProcessos) <> nD
						cProcessos += " OR "
					EndIF

				Next
				cProcessos += " )"

				cWhere += " AND " + cProcessos
			EndIf
			If lDetalhes
				For nB = 1 to Len(oIds)
					AADD(aIds,oIds[nB])
				Next
				If !Empty(aIds)
					cWhere += " AND (P57_ID = P58_IDORI OR P57_ID = P58_IDDEST)   "
					cIds := ""
					For nC = 1 to Len(aIds)
						if(nC==Len(aIds))
							cIds+=aIds[nC]
						Else
							cIds+=aIds[nC]+";"
						EndIF
					Next
					cIds :=  "  P58_IDINT   IN " + Formatin(cIds,";") +" "
					cWhere += " AND " + cIds
				EndIf
			EndIf
			If !Empty(aContas)
				cConta:=""
				For nC = 1 to Len(aContas)
					cConta := ""
					if(nC==Len(aContas))
						cConta+=aContas[nC]
					Else
						cConta+=aContas[nC]+";"
					EndIF
				Next
			EndIF

			If(lContas)
				if(len(aContas)>0)
					cWhere      +=" AND CONTA  IN " + Formatin(cConta,";") +" "
				EndIF
			EndIF


			// Classe para consulta via Rest
			oDataBase:= FWAdapterBaseV2():new( 'GET', .T. )
			oDataBase:setPage(nPage)
			oDataBase:setPageSize(nPageSize)

			//oDataBase:SetUrlFilter({{'FILTER', cFilter}})

			Do Case
			Case lEmpresas
				cOrder := 'P57_CGCORI'
				cQuery  := RetEmp( cWhere)
			Case lProcessos
				cOrder := 'VALUE, LABEL'
				cQuery  := RetPro( cWhere)
			Case lEventos
				cOrder := 'ZX5_DESCRI'
				cQuery  := RetEven( cWhere)
			Case lSintetico
				cOrder := 'TITULO'
				cQuery := RetSinte(cWhere)
			Case lDetalhes
				cOrder := 'COMPET'
				cQuery := getQryDet()
			Case lContas
				cOrder := 'CONTA'
				cQuery := RetSinCta(cWhere)
			OtherWise
				cOrder := 'COMPET'
				//Caso Venha conta filtra os analiticos daquela conta
				if(!Empty(Alltrim(cConta)))
					cWhere +=" AND (P57_CREDIT  IN " + Formatin(cConta,";") +"  OR P57_DEBITO IN " + Formatin(cConta,";") +" )"
				EndIF
				cQuery := RetAnal(cWhere)
			EndCase

			//oDataBase:SetFields( cFields )

			LoadCampos( oDataBase )
			oDataBase:SetQuery( cQuery )
			oDataBase:SetWhere( cWhere )
			oDataBase:SetOrder( cOrder )
		EndIf


		//Executa a consulta, se retornar .T. tudo ocorreu conforme esperado
		If oDataBase:Execute()

			// Gera o arquivo Json com o retorno da Query
			oDataBase:FillGetResponse()

		EndIf

		If oDataBase:lOk

			If Type('oResponse') == 'O'
				FreeObj(oResponse)
			EndIf

			oResponse := JsonObject():New()

			//Campos de exceÃ§Ãµes
			oResponse:fromJson(oDataBase:getJSONResponse())

		EndIf

		ErrorBlock(oException)

		// Verifica errorBlock
		If lRet

			// Verifica execuÃ§Ã£o da query
			If !oDataBase:lOk

				If Type('oResponse') == 'O'
					FreeObj(oResponse)
				EndIf

				oResponse   := JsonObject():New()

				oResponse['code'] := 2 // oDataBase:GetCode()
				oResponse['status'] := 400
				oResponse['message'] := 'NÃo foi possÃ­vel realizar o filtro dos registros!'
				oResponse['detailedMessage'] := oDataBase:GetMessage()

			EndIf

		Else

			oResponse   := JsonObject():New()

			If Type('oResponse') == 'O'
				FreeObj(oResponse)
			EndIf

			If !Empty(cRetoJson)

				oResponse['code'] := 2 // oDataBase:GetCode()
				oResponse['status'] := 400
				oResponse['message'] := 'Falha ao popular JsonObject.'
				oResponse['detailedMessage'] := cRetoJson

			Else

				oResponse['code'] := 1
				oResponse['status'] := 500
				oResponse['message'] := 'Aconteceu um erro inesperado no serviço!'
				oResponse['detailedMessage'] := cErroBlk

			EndIf

		EndIf



		cRet:=EncodeUTF8(oResponse:toJson())

		oJson     := Nil
		oDataBase := Nil
		oResponse := Nil

		FreeObj(oJson)
		FreeObj(oDataBase)
		FreeObj(oResponse)

	EndIf

Return cRet



/*/{Protheus.doc} LoadCampos
	Carrega os campos para o  Objeto
	@type    Function
	<AUTHOR> Marcos Madureira
	@since 06/07/2023
	@version 12.1.33
/*/
Static Function LoadCampos( oSelf)


	Do Case
	Case lEmpresas
		oSelf:AddMapFields( 'VALUE'            , 'P57_CGCORI' , .T., .F.,{ 'P57_CGCORI'  , 'C', fwgetsx3cache('P57_CGCORI'  ,'X3_TAMANHO'), 0 }  )
		oSelf:AddMapFields( 'LABEL'            , 'ZX5_COMPL'  , .T., .F.,{ 'ZX5_COMPL'   , 'C', fwgetsx3cache('ZX5_COMPL'   ,'X3_TAMANHO'), 0 }  )

	Case lProcessos
		oSelf:AddMapFields( 'VALUE'          , 'VALUE'   , .T., .F., { 'VALUE'  , 'C'   , fwgetsx3cache('VALUE'  ,'X3_TAMANHO'), 0 }  )
		oSelf:AddMapFields( 'LABEL'          , 'LABEL' , .T., .F., { 'LABEL', 'C'   , fwgetsx3cache('LABEL','X3_TAMANHO'), 0 }  )

	Case lEventos
		oSelf:AddMapFields("VALUE"           , "ZX5_DESCRI"   , .T., .F., { 'ZX5_DESCRI', 'C', fwgetsx3cache('ZX5_DESCRI','X3_TAMANHO'  ), 0 }  )

	Case lSintetico
		oSelf:AddMapFields( 'TITULO'     , 'TITULO' , .T., .F.,{"TITULO" , 'C', 50, 0},  "A.TITULO"  )
		oSelf:AddMapFields( 'DEBITO'     , 'DEBITO' , .T., .F.,{"DEBITO" , 'N', 14, 2},  "A.DEBITO"  )
		oSelf:AddMapFields( 'CREDITO'  	 , 'CREDITO', .T., .F.,{"CREDITO", 'N', 14, 2},  "A.CREDITO" )
		oSelf:AddMapFields( 'KEY'  	 	 , 'KEY'	, .T., .F.,{"KEY"	 , 'C', 50, 0},  "A.KEY" )
	Case lContas
		oSelf:AddMapFields( 'CONTA'  	 , 'CONTA'		, .T., .F.,{"CONTA"	 	, 'C', 20, 0},  "B.CONTA" )
		oSelf:AddMapFields( 'DESCRICAO'  , 'CONTADESC'	, .T., .F.,{"CONTADESC"	, 'C', 50, 0},  "B.CONTADESC" )
		FldPivot(oSelf)


	OtherWise

		oSelf:AddMapFields( 'COMPETENCIA'    , 'COMPET'  	  , .T., .F., { 'COMPET'    , 'C', fwgetsx3cache('COMPET'     ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'PROCESSO'       , 'PROCESSO'     , .T., .F., { 'PROCESSO'  , 'C', fwgetsx3cache('PROCESSO'   ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'RPS'            , 'RPS'          , .T., .F., { 'RPS'       , 'C', fwgetsx3cache('RPS'        ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'NFE'            , 'NFE'          , .T., .F., { 'NFE'       , 'C', fwgetsx3cache('NFE'        ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'CNPJORI'  	     , 'CNPJORI'      , .T., .F., { 'CNPJORI'   , 'C', fwgetsx3cache('CNPJORI'    ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'CNPJDES'        , 'CNPJDES'      , .T., .F., { 'CNPJDES'   , 'C', fwgetsx3cache('CNPJDES'    ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'TIPO'  	     , 'TIPO'         , .T., .F., { 'TIPO'      , 'C', fwgetsx3cache('TIPO'       ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'DEBITO'         , 'DEBITO'       , .T., .F., { 'DEBITO'    , 'C', fwgetsx3cache('DEBITO'     ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'CREDITO'        , 'CREDITO'      , .T., .F., { 'CREDITO'   , 'C', fwgetsx3cache('CREDITO'    ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'CONTADEBIT'     , 'CONTADEBIT'   , .T., .F., { 'CONTADEBIT', 'C', fwgetsx3cache('CONTADEBIT' ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'CONTACREDI'     , 'CONTACREDI'   , .T., .F., { 'CONTACREDI', 'C', fwgetsx3cache('CONTACREDI' ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'EMPORI'  	     , 'EMPORI' 	  , .T., .F., { 'EMPORI'    , 'C', fwgetsx3cache('EMPORI'     ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'EMPDES'  	     , 'EMPDES'       , .T., .F., { 'EMPDES'    , 'C', fwgetsx3cache('EMPDES'     ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'VALOR'  	     , 'VALOR'        , .T., .F., { 'VALOR'     , 'N', fwgetsx3cache('VALOR'      ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'ID'  	     	 , 'ID'        	  , .T., .F., { 'ID'     	, 'C', fwgetsx3cache('P57_ID'     ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'IDINT'  	     , 'IDINT'        , .T., .F., { 'IDINT'     , 'C', fwgetsx3cache('P57_IDINT'  ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'CCDEB'  	     , 'P57_CCD'      , .T., .F., { 'P57_CCD'   , 'C', fwgetsx3cache('P57_CCD'  ,'X3_TAMANHO')	, 0 } )
		oSelf:AddMapFields( 'CCCRED'  	     , 'P57_CCC'      , .T., .F., { 'P57_CCC'   , 'C', fwgetsx3cache('P57_CCC'  ,'X3_TAMANHO')	, 0 } )
		oSelf:AddMapFields( 'ITDEB'  	     , 'P57_ITEMD'    , .T., .F., { 'P57_ITEMD' , 'C', fwgetsx3cache('P57_ITEMD'  ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'ITCRED'  	     , 'P57_ITEMC'    , .T., .F., { 'P57_ITEMC' , 'C', fwgetsx3cache('P57_ITEMC'  ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'CLDEB'  	     , 'P57_CLVLDB'   , .T., .F., { 'P57_CLVLDB', 'C', fwgetsx3cache('P57_CLVLDB'  ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'CLCRED'  	     , 'P57_CLVLCR'   , .T., .F., { 'P57_CLVLCR', 'C', fwgetsx3cache('P57_CLVLCR'  ,'X3_TAMANHO'), 0 } )
		oSelf:AddMapFields( 'HISTORICO'  	 , 'P57_HIST'     , .T., .F., { 'P57_HIST'  , 'C', fwgetsx3cache('P57_HIST'  ,'X3_TAMANHO')	, 0 } )

	EndCase

Return


/*/{Protheus.doc} RetEmp
	Retorna a querie com os dados das empresas
	@type    Function
	<AUTHOR> Marcos Madureira
	@since 06/07/2023
	@version 12.1.33
/*/
Static Function RetEmp(cWhere)
	Local cQuery := ""

	cQuery := " SELECT #QueryFields# FROM ( "
	cQuery += " SELECT DISTINCT TRIM(P57_CGCORI)||'|'||TRIM(P57_CGCDES) P57_CGCORI, TRIM(ZX5ORI.ZX5_COMPL)||'x'||TRIM(ZX5DES.ZX5_COMPL) ZX5_COMPL  FROM " + RetSqlName( 'P57' ) + " P57 "
	cQuery +=   " 	INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5ORI"
	cQuery +=   " 		ON 		ZX5ORI.ZX5_TABELA = 'INTCMP'"
	cQuery +=   " 			AND ZX5ORI.D_E_L_E_T_ =' '"
	cQuery +=   " 			AND ZX5ORI.ZX5_CHAVE = '000001'"
	cQuery +=   " 			AND ZX5ORI.ZX5_DESCRI = P57_CGCORI"

	cQuery +=   " 	INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5DES "
	cQuery +=   " 		ON 		ZX5DES.ZX5_TABELA = 'INTCMP'"
	cQuery +=   " 			AND ZX5DES.D_E_L_E_T_ =' '"
	cQuery +=   " 			AND ZX5DES.ZX5_CHAVE = '000001'"
	cQuery +=   " 			AND ZX5DES.ZX5_DESCRI = P57_CGCDES"
	cQuery +=   " WHERE #QueryWhere# ) "

Return cQuery



/*/{Protheus.doc} RetPro
	Retorna a querie com os dados dos processos
	@type    Function
	<AUTHOR> Marcos Madureira
	@since 06/07/2023
	@version 12.1.33
/*/
Static Function RetPro(cWhere)
	Local cQuery := ""

	cQuery := " SELECT #QueryFields# FROM ( "
	cQuery += " SELECT DISTINCT P57_PROC VALUE, ZX5_DESCRI LABEL FROM " + RetSqlName( 'P57' ) + " P57 "
	cQuery +=   " 	INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5PRO "
	cQuery +=   " 		ON 		ZX5PRO.ZX5_TABELA     = 'CTBA50' "
	cQuery +=   "               AND ZX5PRO.ZX5_CHAVE  = P57_PROC "
	cQuery +=   "               AND ZX5PRO.D_E_L_E_T_ =' ' "
	cQuery +=   " WHERE #QueryWhere# ) A "

Return cQuery


/*/{Protheus.doc} RetEven
	Retorna a querie das atividades do portal na Home
	@type    Function
	<AUTHOR> Marcos Madureira
	@since 06/07/2023
	@version 12.1.33
/*/
Static Function RetEven(cWhere)
	Local cQuery := ""

	cQuery += " SELECT #QueryFields#  FROM " + RetSQLName("ZX5")
	cQuery += " WHERE #QueryWhere# "

Return cQuery






/*/{Protheus.doc} RetSinte
	Retorna a querie dos dados Sinteticos da P57
	@type    Function
	<AUTHOR> Marcos Madureira
	@since 06/07/2023
	@version 12.1.33
/*/
Static Function RetSinte(cWhere)
	Local cQuery := ""

	cQuery := " SELECT #QueryFields# FROM ( "

	//cQuery += " SELECT P57_COMPET,ZX5PRO.ZX5_DESCRI,P57_CGCDES,P57_CGCORI,P57_TIPO,ZX5ORI.ZX5_COMPL,ZX5DES.ZX5_COMPL ZX5_DESESP, P57_DEBITO,P57_CREDIT,CT1D.CT1_DESC01,CT1C.CT1_DESC01 CT1_DESC02 "
	cQuery += " SELECT TRIM(ZX5ORI.ZX5_COMPL)||'x'||TRIM(ZX5DES.ZX5_COMPL) TITULO 	,"
	cQuery += " TRIM(P57_CGCORI)||'|'||TRIM(P57_CGCDES) KEY 							,"
	cQuery += " SUM( CASE WHEN P57_DC IN('2','3') THEN P57_VALOR ELSE 0 END ) DEBITO  	, "
	cQuery += " SUM( CASE WHEN P57_DC IN('1','3') THEN P57_VALOR ELSE 0 END ) CREDITO "

	cQuery +=   " FROM   " + RetSqlName( 'P57' ) + " P57 "
	cQuery +=   " 	INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5DES "
	cQuery +=   " 		ON 		ZX5DES.ZX5_TABELA = 'INTCMP'"
	cQuery +=   " 			AND ZX5DES.D_E_L_E_T_ =' '"
	cQuery +=   " 			AND ZX5DES.ZX5_CHAVE = '000001'"
	cQuery +=   " 			AND ZX5DES.ZX5_DESCRI = P57_CGCDES"
	cQuery +=   " 	INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5ORI"
	cQuery +=   " 		ON 		ZX5ORI.ZX5_TABELA = 'INTCMP'"
	cQuery +=   " 			AND ZX5ORI.D_E_L_E_T_ =' '"
	cQuery +=   " 			AND ZX5ORI.ZX5_CHAVE = '000001'"
	cQuery +=   " 			AND ZX5ORI.ZX5_DESCRI = P57_CGCORI"

//	cQuery +=   " WHERE " + cWhere
	cQuery += " WHERE #QueryWhere# "

	cQuery += " GROUP BY ZX5ORI.ZX5_COMPL,  ZX5DES.ZX5_COMPL,P57_CGCORI,P57_CGCDES  "

	cQuery +=   " )  A  "

Return cQuery
/*/{Protheus.doc} RetSinCta
	Retorna a querie dos dados Sinteticos da P57 por conta contabil
	@type    Function
	<AUTHOR> Menabue Lima
	@since 10/07/2023
	@version 12.1.33
/*/
Static Function RetSinCta(cWhere)
	Local cQuery := ""
	cQuery := " SELECT #QueryFields# "
	cQuery += " FROM (
	cQuery += " 	 SELECT * "
	cQuery += " 		FROM ( "
	cQuery += " 		   		SELECT C.TITULO,C.KEY, SUM(C.VALOR) VALOR,C.CONTA,C.CONTADESC "
	cQuery += " 		   		FROM ( " //Debito
	cQuery += " 							SELECT TRIM(ZX5ORI.ZX5_COMPL) TITULO 	,"
	cQuery += " 							TRIM(P57_CGCORI) KEY 							,"
	cQuery += " 							SUM( P57_VALOR) *-1  VALOR,P57_DEBITO CONTA,CT1D.CT1_DESC01 CONTADESC "
	cQuery += " 							FROM   " + RetSqlName( 'P57' ) + " P57 "
	cQuery += " 								INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5ORI"
	cQuery += " 									ON 		ZX5ORI.ZX5_TABELA = 'INTCMP'"
	cQuery += " 										AND ZX5ORI.D_E_L_E_T_ =' '"
	cQuery += " 										AND ZX5ORI.ZX5_CHAVE = '000001'"
	cQuery += " 										AND ZX5ORI.ZX5_DESCRI = P57_CGCORI			"
	cQuery += " 		   		             INNER JOIN " + RetSqlName( 'CT1' ) + " CT1D "
	cQuery += " 		   		                 ON      CT1D.CT1_FILIAL = '" + FWxFilial('CT1') + "'"
	cQuery += " 		   		                     AND CT1D.CT1_CONTA  = P57.P57_DEBITO "
	cQuery += " 		   		                     AND CT1D.D_E_L_E_T_ = ' ' "
	cQuery += " 							WHERE #QueryWhere#  AND P57_DC IN('2','3')"//
	cQuery += " 							GROUP BY ZX5ORI.ZX5_COMPL, P57_CGCORI,P57_DEBITO,CT1_DESC01  "
	cQuery += " 						   UNION "//Credito
	cQuery += " 							SELECT TRIM(ZX5ORI.ZX5_COMPL) TITULO 	,"
	cQuery += " 							TRIM(P57_CGCORI)KEY 							,"
	cQuery += " 							SUM( P57_VALOR)  VALOR,P57_CREDIT CONTA,CT1D.CT1_DESC01 CONTADESC"
	cQuery += " 							FROM   " + RetSqlName( 'P57' ) + " P57 "
	cQuery += " 								INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5ORI"
	cQuery += " 									ON 		ZX5ORI.ZX5_TABELA = 'INTCMP'"
	cQuery += " 										AND ZX5ORI.D_E_L_E_T_ =' '"
	cQuery += " 										AND ZX5ORI.ZX5_CHAVE = '000001'"
	cQuery += " 										AND ZX5ORI.ZX5_DESCRI = P57_CGCORI			"
	cQuery += " 								INNER JOIN " + RetSqlName( 'CT1' ) + " CT1D "
	cQuery += " 								    ON      CT1D.CT1_FILIAL = '" + FWxFilial('CT1') + "'"
	cQuery += " 								        AND CT1D.CT1_CONTA  = P57.P57_CREDIT "
	cQuery += " 								        AND CT1D.D_E_L_E_T_ = ' ' "
	cQuery += " 							WHERE #QueryWhere#  AND P57_DC IN('1','3')"
	cQuery += " 							GROUP BY ZX5ORI.ZX5_COMPL, P57_CGCORI,P57_CREDIT,CT1_DESC01  "
	cQuery += " 					  )  C GROUP BY TITULO,KEY,CONTA,CONTADESC "
	cQuery += " 				)  "
	///cQuery += " 			)    "
	cQuery += " 			"+EmpPivot(cWhere)
	cQuery += "  			A  "
	cQuery += "    ) B ORDER BY CONTA
Return cQuery



/*/{Protheus.doc} RetAnal
	Retorna a querie dos dados Analiticos da P57
	@type    Function
	<AUTHOR> Marcos Madureira
	@since 06/07/2023
	@version 12.1.33
/*/
Static Function RetAnal(cWhere)
	Local cQuery := ""

	cQuery := " SELECT #QueryFields# FROM ( "

	cQuery += " SELECT P57_COMPET COMPET,ZX5PRO.ZX5_DESCRI PROCESSO,P57_CGCDES CNPJDES,P57_CGCORI CNPJORI,P57_TIPO TIPO,ZX5ORI.ZX5_COMPL EMPORI,"
	cQuery += " ZX5DES.ZX5_COMPL EMPDES, P57_DEBITO DEBITO,P57_CREDIT CREDITO,CT1D.CT1_DESC01 CONTADEBIT,CT1C.CT1_DESC01 CONTACREDI, "
	cQuery += " P57_CCD,P57_CCC,P57_ITEMD,P57_ITEMC,P57_CLVLDB,P57_CLVLCR, P57_HIST, "
	cQuery += " P57_FLEX01 RPS, P57_FLEX02 NFE, P57_VALOR VALOR, P57_ID ID, P57_IDINT IDINT "

	cQuery +=   " FROM   " + RetSqlName( 'P57' ) + " P57 "
	cQuery +=   " 	INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5PRO "
	cQuery +=   " 		ON 		ZX5PRO.ZX5_TABELA     = 'CTBA50' "
	cQuery +=   "               AND ZX5PRO.ZX5_CHAVE  = P57_PROC "
	cQuery +=   "               AND ZX5PRO.D_E_L_E_T_ =' ' "
	cQuery +=   " 	INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5DES "
	cQuery +=   " 		ON 		ZX5DES.ZX5_TABELA = 'INTCMP'"
	cQuery +=   " 			AND ZX5DES.D_E_L_E_T_ =' '"
	cQuery +=   " 			AND ZX5DES.ZX5_CHAVE = '000001'"
	cQuery +=   " 			AND ZX5DES.ZX5_DESCRI = P57_CGCDES"
	cQuery +=   " 	INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5ORI"
	cQuery +=   " 		ON 		ZX5ORI.ZX5_TABELA = 'INTCMP'"
	cQuery +=   " 			AND ZX5ORI.D_E_L_E_T_ =' '"
	cQuery +=   " 			AND ZX5ORI.ZX5_CHAVE = '000001'"
	cQuery +=   " 			AND ZX5ORI.ZX5_DESCRI = P57_CGCORI"
	cQuery +=   "   INNER JOIN " + RetSqlName( 'CT1' ) + " CT1D "
	cQuery +=   "       ON      CT1D.CT1_FILIAL = '" + FWxFilial('CT1') + "'"
	cQuery +=   "           AND CT1D.CT1_CONTA  = P57.P57_DEBITO "
	cQuery +=   "           AND CT1D.D_E_L_E_T_ = ' ' "
	cQuery +=   "   INNER JOIN " + RetSqlName( 'CT1' ) + " CT1C "
	cQuery +=   "       ON      CT1C.CT1_FILIAL = '" + FWxFilial('CT1') + "'"
	cQuery +=   "           AND CT1C.CT1_CONTA  = P57.P57_CREDIT "
	cQuery +=   "           AND CT1C.D_E_L_E_T_ = ' ' "
//	cQuery +=   " WHERE " + cWhere
	cQuery += " WHERE #QueryWhere# "



	cQuery +=   " )  A  "

Return cQuery


/*/{Protheus.doc} getQryDet
	Retorna a querie do detalhes ( Tracker) da P58
	@type    Function
	<AUTHOR> Menabue Lima
	@since 06/07/2023
	@version 12.1.33
/*/
Static Function getQryDet()
	cQuery := " SELECT #QueryFields# FROM ( "
	cQuery += " SELECT P57_COMPET COMPET,ZX5PRO.ZX5_DESCRI PROCESSO,P57_CGCDES CNPJDES,P57_CGCORI CNPJORI,P57_TIPO TIPO,ZX5ORI.ZX5_COMPL EMPORI,"
	cQuery += " ZX5DES.ZX5_COMPL EMPDES, P57_DEBITO DEBITO,P57_CREDIT CREDITO,CT1D.CT1_DESC01 CONTADEBIT,CT1C.CT1_DESC01 CONTACREDI, "
	cQuery += " P57_CCD,P57_CCC,P57_ITEMD,P57_ITEMC,P57_CLVLDB,P57_CLVLCR, P57_HIST, "
	cQuery += " P57_FLEX01 RPS, P57_FLEX02 NFE, P57_VALOR VALOR, P57_ID ID, P57_IDINT IDINT "
	cQuery +=   " FROM   " + RetSqlName( 'P57' ) + " P57 "
	cQuery +=   "   INNER JOIN    " + RetSQLName('P58') + " P58 ON "
	cQuery +=   "   "
	cQuery +=   "   #QueryWhere#
	cQuery +=   " 	INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5PRO "
	cQuery +=   " 		ON 		ZX5PRO.ZX5_TABELA     = 'CTBA50' "
	cQuery +=   "               AND ZX5PRO.ZX5_CHAVE  = P57_PROC "
	cQuery +=   "               AND ZX5PRO.D_E_L_E_T_ =' ' "
	cQuery +=   " 	INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5DES "
	cQuery +=   " 		ON 		ZX5DES.ZX5_TABELA = 'INTCMP'"
	cQuery +=   " 			AND ZX5DES.D_E_L_E_T_ =' '"
	cQuery +=   " 			AND ZX5DES.ZX5_CHAVE = '000001'"
	cQuery +=   " 			AND ZX5DES.ZX5_DESCRI = P57_CGCDES"
	cQuery +=   " 	INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5ORI"
	cQuery +=   " 		ON 		ZX5ORI.ZX5_TABELA = 'INTCMP'"
	cQuery +=   " 			AND ZX5ORI.D_E_L_E_T_ =' '"
	cQuery +=   " 			AND ZX5ORI.ZX5_CHAVE = '000001'"
	cQuery +=   " 			AND ZX5ORI.ZX5_DESCRI = P57_CGCORI"
	cQuery +=   "   INNER JOIN " + RetSqlName( 'CT1' ) + " CT1D "
	cQuery +=   "       ON      CT1D.CT1_FILIAL = '" + FWxFilial('CT1') + "'"
	cQuery +=   "           AND CT1D.CT1_CONTA  = P57.P57_DEBITO "
	cQuery +=   "           AND CT1D.D_E_L_E_T_ = ' ' "
	cQuery +=   "   INNER JOIN " + RetSqlName( 'CT1' ) + " CT1C "
	cQuery +=   "       ON      CT1C.CT1_FILIAL = '" + FWxFilial('CT1') + "'"
	cQuery +=   "           AND CT1C.CT1_CONTA  = P57.P57_CREDIT "
	cQuery +=   "           AND CT1C.D_E_L_E_T_ = ' ' "
	cQuery +=   " WHERE   P57.D_E_L_E_T_ = ' ' "
	cQuery +=	" AND P57_FILIAL = '"+ FWxFilial('P57') +"' "

//	cQuery +=   " WHERE " + cWhere
//	cQuery += " WHERE  "
	cQuery +=   " )  A  "
Return cQuery
/*/{Protheus.doc} FldPivot
	Seta no mapfields os campos da pivot dinamico
	@type    Function
	<AUTHOR> Menabue Lima
	@since 11/07/2023
	@version 12.1.33
/*/
Static Function FldPivot(oSelf)
	Local cQry  :=""
	Local cAls	:=  GetNextAlias()
	cQry  :="	SELECT DISTINCT  REPLACE(TRIM(ZX5ORI.ZX5_COMPL),' ','_')     AS CAMPOS "
	cQry  +="	FROM   P57000 P57"
	cQry  +="	INNER JOIN ZX5000 ZX5ORI"
	cQry  +="	ON ZX5ORI.ZX5_TABELA = 'INTCMP'"
	cQry  +="	AND ZX5ORI.D_E_L_E_T_ =' '"
	cQry  +="	AND ZX5ORI.ZX5_CHAVE = '000001'"
	cQry  +="	AND ZX5ORI.ZX5_DESCRI = P57_CGCORI"
	cQry+="    WHERE "+cWhere
	//cQry := ChangeQuery(cQry)
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAls,.T.,.T.)

	While !(cAls)->(EOF())
		oSelf:AddMapFields( Alltrim((cAls)->CAMPOS)      , Alltrim((cAls)->CAMPOS)  	, .T., .F.,{Alltrim((cAls)->CAMPOS)  	, 'N', 14, 2},  "B."+Alltrim((cAls)->CAMPOS)  )
		(cAls)->(DbSkip())
	EndDo
	(cAls)->(DbCloseArea())

Return

/*/{Protheus.doc} EmpPivot
	Retorna as colunas da pivottable
	@type    Function
	<AUTHOR> Menabue Lima
	@since 11/07/2023
	@version 12.1.33
/*/
Static Function EmpPivot(cWhere)
	Local cQry  :=""
	Local cPivot:=""
	Local cAls	:=  GetNextAlias()
	cQry+=" SELECT LISTAGG(TRIM(TITULO), ',') WITHIN GROUP (ORDER BY TITULO)   AS TITULO    "
	cQry+="  FROM ("
	cQry+="    SELECT DISTINCT REPLACE(''''||TRIM(ZX5ORI.ZX5_COMPL),' ','')||''''||' AS "
	cQry+='"'
	cQry+="'||REPLACE(TRIM(ZX5ORI.ZX5_COMPL),' ','_')||'"
	cQry+='"'
	cQry+="'    AS TITULO"
	cQry+="    FROM  " + RetSqlName( 'P57' ) + " P57 "
	cQry+="    INNER JOIN " + RetSqlName( 'ZX5' ) + " ZX5ORI ON ZX5ORI.ZX5_TABELA = 'INTCMP'"
	cQry+="      AND ZX5ORI.D_E_L_E_T_ = ' '"
	cQry+="      AND ZX5ORI.ZX5_CHAVE = '000001'"
	cQry+="      AND ZX5ORI.ZX5_DESCRI = P57_CGCORI"
	cQry+="    WHERE "+cWhere
	cQry+=" ORDER BY ZX5ORI.ZX5_COMPL "
	cQry+="  )"
	//cQry := ChangeQuery(cQry)
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAls,.T.,.T.)

	While !(cAls)->(EOF())
		cPivot:="	PIVOT (  "
		cPivot+="              SUM(VALOR)  "
		cPivot+="              FOR TITULO IN ( "+Alltrim((cAls)->TITULO)+")  "
		cPivot+="            )"
		(cAls)->(DbSkip())
	EndDo
	(cAls)->(DbCloseArea())


Return   cPivot

