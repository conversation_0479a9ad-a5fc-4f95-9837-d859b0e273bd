#include 'totvs.ch'

/*/{Protheus.doc} ft600prr
retorna se permite incluir produtos repetidos na proposta comercial
<AUTHOR>
@since 03/07/2020
@version 1
@return lRet, retorna se permite incluir produtos repetidos na proposta comercial
@type function
/*/
user function ft600prr
Local aArea     := getArea()
Local lRet      := .t.
Local cNivel    := FWFldGet("ADY_XCODNV")

If Empty(cNivel)
    cNivel := FwFldGet("ADY_XMODAL")
EndIf

dbSelectArea("PKG")

If FieldPos("PKG_PRDREP") > 0

    dbSetOrder(1)

    If dbSeek(xFilial("PKG")+FwFldGet("ADY_XCODAG")+cNivel)

        lRet := PKG->PKG_PRDREP<>'1'

    EndIf

EndIf

restArea(aArea)
Return(lRet)