﻿#IFDEF SPANISH
      	 #define STR0001 "Acesso não permitido"
	 #define STR0002 "&Finalizar"
	 #define STR0003 "O Modulo CRM e Call Center apenas podem ser acessados pelo ambiente CRM (caso seja mercado internacional usar o ambiente CRM_MI)."
	 #define STR0004 "A comunicação usada para acesso interno é CRM_INTRANET e CRM_INTERNET para acesso externo."
	 #define STR0005 "Caso as configurações acima não existam no SMARTCLIENT entrar em contato com a TI através do e-mail: <EMAIL>"
#ELSE
   #IFDEF ENGLISH
      	 #define STR0001 "Acesso não permitido"
	 #define STR0002 "&Finalizar"
	 #define STR0003 "O Modulo CRM e Call Center apenas podem ser acessados pelo ambiente CRM (caso seja mercado internacional usar o ambiente CRM_MI)."
	 #define STR0004 "A comunicação usada para acesso interno é CRM_INTRANET e CRM_INTERNET para acesso externo."
	 #define STR0005 "Caso as configurações acima não existam no SMARTCLIENT entrar em contato com a TI através do e-mail: <EMAIL>"
   #ELSE
      	 #define STR0001 "Acesso não permitido"
	 #define STR0002 "&Finalizar"
	 #define STR0003 "O Modulo CRM e Call Center apenas podem ser acessados pelo ambiente CRM (caso seja mercado internacional usar o ambiente CRM_MI)."
	 #define STR0004 "A comunicação usada para acesso interno é CRM_INTRANET e CRM_INTERNET para acesso externo."
	 #define STR0005 "Caso as configurações acima não existam no SMARTCLIENT entrar em contato com a TI através do e-mail: <EMAIL>"
   #ENDIF
#ENDIF