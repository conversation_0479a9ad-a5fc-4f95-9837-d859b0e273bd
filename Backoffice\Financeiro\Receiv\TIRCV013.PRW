#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} TIRCV013
Funcao responsavel pela callback do interceptor referente ao status do Serasa
@type  Function
<AUTHOR>
@since 12/09/2021
@version 1.0
/*/
User Function TIRCV013(nRe)
	Local aRequest       := {}
	Local oJson          := JsonObject():new()
	Local oBody
	Local aTitulos as Array
	Local cRemessa       := ""
	Local cGeracao       := ""
	Local cRetorno       := ""
	Local cArqTxt        := ""
	Local nI             := 0
	Local cFil           := ""
	Local cNum           := ""
	Local cParc          := ""
	Local cTipo          := ""
	Local cPref          := ""
	Local cCli           := ""
	Local cLoja          := ""
	Local cTipoArq       := ""
	Local cLoteProc      := ""
	Local cStatus        := ""
	Local lErro          := .F.
	Local lErrCabec      := .F.
	Local cChave         := ""
	Local cFilArquivo    := ""
	Local dVcto          := ctod("")
	Local dVctoReal      := ctod("")
	Local nSaldo         := 0
	Local cSituac        := ""
	Local cFilBkp		 := cFilAnt
	Local cEmpBkp		 := cEmpAnt
	Local nRecBkp 		 := 0
	Local cEmpCur		 := ""
	Local nOpen			 := 0
	Private nQtErros     := 0
	Private cDetAlias    := GetNextAlias()
	Private oResponse    := JsonObject():New()
	Default nRe			 := 0
	oResponse["status" ] := 200
	oResponse['titulos'] := {}
	if nRe > 0
		DBSELECTAREA( "P37" )
		P37->(DBSETORDER(1))
		P37->(DBGOTO(nRe))
		oJson          := JsonObject():new()
		oBody:= oJson:FromJson(P37->P37_BODY)
	Else//  00001001200-->70312    60001000100-->62681
		aRequest       := PARAMIXB //Parametros recebidos do Interceptor  [1]= Codigo da Requisicao P37_COD [2]= Body Recebido da requisicao  P37_BODY
		oJson          := JsonObject():new()
		oBody          := oJson:FromJson(aRequest[2])
	EndIF
	nRecBkp 		 := P37->(Recno())
	If VALTYPE(oBody) == 'U'
		aTitulos  := oJson:GetJsonObject("titulos")
		cRemessa  := oJson:GetJsonObject("remessa")
		cArqTxt   := oJson:GetJsonObject("txt_arq")
		cTipoArq  := oJson:GetJsonObject("tipo_arq")//1 envio 2 retirada
		cGeracao  := oJson:GetJsonObject("geracao_arquivo")
		cRetorno  := oJson:GetJsonObject("processamento_arquivo")

		If ValType(aTitulos) == "A"
			//Se a Filial logada nao for a mesma do Dado troca para a mesma do dado
			cFil := aTitulos[1]:GetJsonObject("filial")
			cEmpCur	:= U_TIRCVEMP(cFil)
			if  !( cEmpCur $ cEmpAnt)
				cEmpAnt := cEmpCur
				cFilAnt := aTitulos[1]:GetJsonObject("filial")
				nOpen   := 1
			EndIF

			If U_RCVENV(cEmpAnt,cFilAnt,nOpen)
				Begin Transaction
					If Len(aTitulos) > 0
						cFilAnt := aTitulos[1]:GetJsonObject("filial")

						//premissa projeto: Receiv enviar sempre todos os títulos de uma mesma filial
						cFilArquivo := padr(substr(aTitulos[1]:GetJsonObject("filial"), 1, 3), TamSx3("FW8_FILIAL")[1]," " )
						cLoteProc := Cabecalhos(cFilArquivo, cTipoArq, cRetorno, cRemessa, aTitulos[1]:GetJsonObject("filial"), cGeracao, cArqTxt )
					EndIf

					If !empty(cLoteProc)
						For nI:= 1 To Len(aTitulos)
							cFil       :=aTitulos[nI]:GetJsonObject("filial")
							cNum       :=aTitulos[nI]:GetJsonObject("titulo")
							cParc      :=aTitulos[nI]:GetJsonObject("parcela")
							cTipo      :=aTitulos[nI]:GetJsonObject("tipo")
							cPref      :=aTitulos[nI]:GetJsonObject("prefixo")
							cCli       :=aTitulos[nI]:GetJsonObject("codigo")
							cLoja      :=aTitulos[nI]:GetJsonObject("loja")
							dVcto      :=stod(aTitulos[nI]:GetJsonObject("vcto"))
							dVctoReal  :=stod(aTitulos[nI]:GetJsonObject("vcto_real"))
							nSaldo     :=aTitulos[nI]:GetJsonObject("saldo")
							cSituac    :=aTitulos[nI]:GetJsonObject("situac")
							cStatus    :=aTitulos[nI]:GetJsonObject("status")
							cChave     := FINGRVFK7("SE1", substr(cFil,1,3) + "|" + cPref + "|" + cNum + "|" + cParc + "|" + cTipo + "|" + cCli  + "|" + cLoja )

							If cFilArquivo != padr(substr(cFil, 1, 3), TamSx3("FW8_FILIAL")[1]," " )
								lErro := .T.
								TrataErros(cFil, cNum, cParc, cTipo, cPref, cCli, cLoja, cLoteProc,"Todas as filiais do Lote devem pertencer a mesma empresa!")
							Else
								SE1->(DbSetOrder(1)) //E1_FILIAL+E1_PREFIXO+E1_NUM+E1_PARCELA+E1_TIPO
								SE1->(DbSeek(cFil+cPref+cNum+cParc+cTipo))

								If SE1->(Eof())
									lErro := .T.
									TrataErros(cFil, cNum, cParc, cTipo, cPref, cCli, cLoja, cLoteProc,"Este titulo nao existe no Protheus!")
								Else
									If cTipoArq == "1"//inserir
										If !empty(cRetorno) //retorno de arquivo já enviado ao Serasa
											Detalhes(.F.,cFilArquivo, cFil, cNum, cParc, cTipo, cPref, cCli, cLoja, dVcto, dVctoReal, nSaldo, cSituac, cLoteProc, cChave, cArqTxt, cStatus)
										Else //remessa recem gerada pela Receiv
											Detalhes(.T.,cFilArquivo, cFil, cNum, cParc, cTipo, cPref, cCli, cLoja, dVcto, dVctoReal, nSaldo, cSituac, cLoteProc, cChave, cArqTxt, cStatus)
										EndIf
									Else//retirar do Serasa
										If !empty(cRetorno)  //retorno de arquivo já enviado ao Serasa
											Detalhes(.F.,cFilArquivo, cFil, cNum, cParc, cTipo, cPref, cCli, cLoja, dVcto, dVctoReal, nSaldo, cSituac, cLoteProc, cChave, cArqTxt, cStatus)
										Else //remessa recem gerada pela Receiv
											Detalhes(.T.,cFilArquivo, cFil, cNum, cParc, cTipo, cPref, cCli, cLoja, dVcto, dVctoReal, nSaldo, cSituac, cLoteProc, cChave, cArqTxt, cStatus)
										EndIf
									EndIf
								EndIf
							EndIf
						Next nI
					EndIF
					If lErro
						DisarmTransaction()
					EndIf
				End Transaction
			Else
				If Len(aTitulos) > 0
					cFil       :=aTitulos[nI]:GetJsonObject("filial")
					cNum       :=aTitulos[nI]:GetJsonObject("titulo")
					cParc      :=aTitulos[nI]:GetJsonObject("parcela")
					cTipo      :=aTitulos[nI]:GetJsonObject("tipo")
					cPref      :=aTitulos[nI]:GetJsonObject("prefixo")
					cCli       :=aTitulos[nI]:GetJsonObject("codigo")
					cLoja      :=aTitulos[nI]:GetJsonObject("loja")
					lErrCabec      := .T.
					TrataErros(cFil, cNum, cParc, cTipo, cPref, cCli, cLoja, cLoteProc, "Lote nao localizado no sistema!")
					lErro := .T.
				Else
					lErrCabec      := .T.
					TrataErros("", "", "", "", "", "", "", "", "Lote nao localizado no sistema! (nenhum titulo relacionado no json)!")
					lErro := .T.
				EndIf

			EndIf

			//Caso a Empresa seja Diferente volta o recno posicionado apos posicionar na SM0
			If U_RCVENV(cEmpBkp,cFilBkp,nOpen)
				cEmpAnt := cEmpBkp
				cFilAnt := cFilBkp
				SM0->(DBSEEK(cEmpAnt+cFilAnt))
				DbSelectArea("P37")
				P37->(DBGOTO(nRecBkp))
				P37->(RecLock("P37",.F.))
				If nQtErros > 0
					If nQtErros == len(aTitulos) .or. lErrCabec
						P37->P37_STATUS := "3"
					Else
						P37->P37_STATUS := "5"
					EndIf

					oResponse["status" ] := 500
					P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
				Else
					P37->P37_STATUS := "2"
					P37->P37_BODYRP := ""
				EndIf

				P37->(MsUnLock())
			EndIF
			oResponse := Nil
			FreeObj(oResponse)
		EndIF
	EndIF
Return


 /*/{Protheus.doc} PegaLote
	Obtem codigo do lote enviado

	@type  Function
	<AUTHOR>
	@since 25/08/2021
	@version 1.0
/*/
static function PegaLote(cFw8Filial, cArqSer, lIncluir)
	Local cRet      := ""

	If lIncluir
		dbselectarea('FW8')
		cRet := GETSXENUM( "FW8", "FW8_LOTE")
		FW8->(ConfirmSx8())

		FW8->(DBSetOrder(1))
		While FW8->(DBSeek(xFilial("FW8")+cRet))
			cRet := GETSXENUM( "FW8", "FW8_LOTE")
			FW8->(ConfirmSx8())
		EndDo
	Else
		DbSelectArea('FW8')
		FW8->(DbSetOrder(2))
		FW8->(DbSeek(cFw8Filial + cArqSer))

		If (FW8->(!Eof()) .And. Alltrim(FW8->FW8_FILIAL) == AllTrim(cFw8Filial)  .And. Alltrim(FW8->FW8_ARQSER) == cArqSer )
			cRet := FW8->FW8_LOTE
		EndIf
	EndIf

return cRet

 /*/{Protheus.doc} Cabecalhos
	Trata tabelas de Cabecalho do produto padrao SERASA FWA e FWR

	@type  Function
	<AUTHOR>
	@since 10/09/2021
	@version 1.0
/*/
static function Cabecalhos(cFilArquivo, cTipoArq, cRetorno, cRemessa, cFilOri, cGeracao, cArqTxt )
	Local cCgc := ""
	Local cRet := ''

	dbSelectArea( "SM0" )
	If SM0->( dbSeek( cEmpAnt + cFilOri ) )
		cCgc := SM0->M0_CGC
	Endif

	If cTipoArq == "1"//inserir
		If !empty(cRetorno) //retorno de arquivo já enviado ao Serasa
			cRet := PegaLote(cFilArquivo, cRemessa, .F. )
			If Empty(FW8->FW8_DTPROC)
				FW8->(reclock('FW8',.F.))
				FW8->FW8_DTPROC := stod(cRetorno)
				FW8->(Msunlock())
			EndIf
		Else //remessa recem gerada pela Receiv
			cRet := PegaLote(cFilArquivo, cRemessa, .T. )
			FW8->(reclock('FW8',.T.))
			FW8->FW8_FILIAL := cFilArquivo
			FW8->FW8_LOTE   := cRet
			FW8->FW8_DTLOTE := stod(cGeracao)
			FW8->FW8_DTARQ  := stod(cGeracao)
			FW8->FW8_TIPO   := cTipoArq
			FW8->FW8_ARQSER := cRemessa
			FW8->FW8_XRODA  := "Portal RECEIV, " + P37->P37_COD
			FW8->FW8_XRESV  := cArqTxt
			FW8->(Msunlock())

			FWR->(reclock('FWR',.T.))
			FWR->FWR_FILIAL := FW8->FW8_FILIAL
			FWR->FWR_LOTE   := FW8->FW8_LOTE
			FWR->FWR_ARQSER := FW8->FW8_ARQSER
			FWR->FWR_CNPJRZ := cCgc
			FWR->FWR_FILORI := cFilOri
			FWR->(Msunlock())

		EndIf
	Else//retirar do Serasa

		If !empty(cRetorno)  //retorno de arquivo já enviado ao Serasa
			cRet := PegaLote(cFilArquivo, cRemessa, .F. )
			If Empty(FW8->FW8_DTPROC)
				FW8->(reclock('FW8',.F.))
				FW8->FW8_DTPROC := stod(cRetorno)
				FW8->(Msunlock())
			EndIf
		Else //remessa recem gerada pela Receiv
			cRet := PegaLote(cFilArquivo, cRemessa, .T. )
			FW8->(reclock('FW8',.T.))
			FW8->FW8_FILIAL := cFilArquivo
			FW8->FW8_LOTE   := cRet
			FW8->FW8_DTLOTE := stod(cGeracao)
			FW8->FW8_DTARQ  := stod(cGeracao)
			//FW8->FW8_DTPROC
			FW8->FW8_TIPO   := cTipoArq
			FW8->FW8_ARQSER := cRemessa
			FW8->FW8_XRODA  := "Portal RECEIV, " + P37->P37_COD
			FW8->FW8_XRESV  := cArqTxt
			FW8->(Msunlock())

			FWR->(reclock('FWR',.T.))
			FWR_FILIAL := FW8->FW8_FILIAL
			FWR_LOTE   := FW8->FW8_LOTE
			FWR_ARQSER := FW8->FW8_ARQSER
			FWR_CNPJRZ := cCgc
			FWR_FILORI := cFilOri
			FWR->(Msunlock())
		EndIf
	EndIf

return cRet

 /*/{Protheus.doc} Detalhes
	Trata tabelas de Detalhes do produto padrao SERASA FW9 FWB

	@type  Function
	<AUTHOR>
	@since 10/09/2021
	@version 1.0
/*/

static function Detalhes (lCriaFwaFw9,cFilArquivo, cFil, cNum, cParc, cTipo, cPref, cCli, cLoja, dVcto, dVctoReal, nSaldo, cSituac, cLote, cChave, cArqTxt, cStatus)
	Local cUlSeq := TamSx3("FWB_SEQ")[1]

	cUltSeq := u_F770NumSeq(cChave, 1)

	If lCriaFwaFw9
		BeginSql Alias cDetAlias
		SELECT FWA.R_E_C_N_O_ AS RECFWA 
		FROM %TABLE:FWA% FWA 
		WHERE FWA.FWA_FILIAL = %Exp:cFilArquivo% AND FWA.FWA_LOTE = %Exp:cLote% 
		AND FWA.%NOTDEL% AND FWA.FWA_IDDOC   = %Exp:cChave% 
		EndSQL

		If (cDetAlias)->(Eof())
			FWA->(reclock('FWA',.T.))
			FWA->FWA_FILIAL := cFilArquivo
			FWA->FWA_IDDOC  := cChave
			FWA->FWA_PREFIX := cPref
			FWA->FWA_NUM    := cNum
			FWA->FWA_PARCEL := cParc
			FWA->FWA_TIPO   := cTipo
			FWA->FWA_CLIENT := cCli
			FWA->FWA_LOJA   := cLoja
			FWA->FWA_SEQ    := cUltSeq
			FWA->FWA_STATUS := cStatus
			FWA->FWA_FILORI := cFil
			FWA->FWA_LOTE   := cLote
			FWA->(Msunlock())
		EndIf

		(cDetAlias)->(DbCloseArea())

		BeginSql Alias cDetAlias
		SELECT FW9.R_E_C_N_O_ AS RECFW9 
		FROM %TABLE:FW9% FW9 
		WHERE FW9.FW9_FILIAL = %Exp:cFilArquivo% 
		AND FW9.FW9_LOTE = %Exp:cLote% 
		AND FW9.%NOTDEL% 
		AND FW9.FW9_IDDOC   = %Exp:cChave% 
		EndSQL

		If (cDetAlias)->(Eof())
			FW9->(reclock('FW9',.T.))
			FW9->FW9_FILIAL := cFilArquivo
			FW9->FW9_LOTE   := cLote
			FW9->FW9_IDDOC  := cChave
			FW9->FW9_PREFIX := cPref
			FW9->FW9_NUM    := cNum
			FW9->FW9_PARCEL := cParc
			FW9->FW9_TIPO   := cTipo
			FW9->FW9_CLIENT := cCli
			FW9->FW9_LOJA   := cLoja
			FW9->FW9_FILORI := cFil
			FW9->FW9_VALOR  := nSaldo
			FW9->(MsunLock())
		EndIf

		(cDetAlias)->(DbCloseArea())
	EndIf

	FWB->(reclock("FWB", .T.))
	FWB->FWB_FILIAL := cFilArquivo
	FWB->FWB_IDDOC  := cChave
	FWB->FWB_SEQ    := cUltSeq
	FWB->FWB_LOTE   := cLote
	FWB->FWB_OCORR  := cStatus //de/para Receiv
	FWB->FWB_DESCR  := ""
	FWB->FWB_DTOCOR := Date()
	FWB->FWB_CODERR := ""
	FWB->FWB_DTSERA := Date()
	FWB->FWB_VALOR  := nSaldo
	FWB->(MsunLock())

return

 /*/{Protheus.doc} TrataErros
	Registra inconsistencias detectadas nos titulos

	@type  Function
	<AUTHOR>
	@since 10/09/2021
	@version 1.0
/*/
static function TrataErros(cFil, cNum, cParc, cTipo, cPref, cCli, cLoja, cLoteProc, cMsg)
	oJsonErro := JsonObject():New()
	oJsonErro['filial']         := cFil
	oJsonErro['titulo']         := cNum
	oJsonErro['parcela']        := cParc
	oJsonErro['tipo']           := cTipo
	oJsonErro['prefixo']        := cPref
	oJsonErro['codigo']         := cCli
	oJsonErro['loja']           := cLoja
	oJsonErro['erro']           := cMsg
	aadd( oResponse['titulos'], oJsonErro)
	nQtErros  += 1
	oJsonErro := Nil
	FreeObj(oJsonErro)

return
