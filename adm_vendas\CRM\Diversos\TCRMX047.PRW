#INCLUDE "protheus.ch"
//-----------------------------------------------------------------------------
/*/ {Protheus.doc} Function
Analise de Credito e Risco
<AUTHOR>
@version P12
@since 16/09/2015
/*/
//-----------------------------------------------------------------------------
User Function TCRMX047()

Local nResult := 0
Local dDataIn := StoD("")
Local aArea   := GetArea()

BeginSQL Alias "SF2QRY"
	COLUMN DT_INICIO AS DATE
	SELECT MIN(F2_EMISSAO) AS DT_INICIO 
	  FROM %table:SF2%
	 WHERE F2_FILIAL  = %Exp:xFilial("SF2")% 
	   AND F2_CLIENTE = %Exp:SA1->A1_COD%
	   AND F2_LOJA    = %Exp:SA1->A1_LOJA%
	   AND %notDel% 
EndSQL	
If Empty(SF2QRY->DT_INICIO)
	dDataIn := dDataBase
Else
	dDataIn := SF2QRY->DT_INICIO
Endif
SF2QRY->(DbCloseArea())
		
nResult := (Val(Left(DtoS(dDatabase),4)) - Val(Left(DtoS(dDataIn),4))) * 12
nResult += (Val(SubStr(DtoS(dDatabase),5,2)) - Val(SubStr(DtoS(dDataIn),5,2)))
nResult ++

RestArea(aArea)

Return nResult
