# Coletando as variaveis de ambiente/parametro ==================================================
param($title, $subtitle, $titleMessage, $typeMessage, $AnimeMessage, $uri, $imageMessage, $text, $pathText, $titleImage, $pathImage, $titleButon1, $titleButon2, $urlButon1, $urlButon2 )

if ([string]::IsNullOrEmpty($title))        {$title = $ENV:MSGGOOGLECHAT_TITLE }
if ([string]::IsNullOrEmpty($subtitle))     {$subtitle = $ENV:MSGGOOGLECHAT_SUBTITLE }
if ([string]::IsNullOrEmpty($titleMessage)) {$titleMessage = $ENV:MSGGOOGLECHAT_TITLEMSG }
if ([string]::IsNullOrEmpty($typeMessage))  {$typeMessage = $ENV:MSGGOOGLECHAT_TYPEMSG }
if ([string]::IsNullOrEmpty($AnimeMessage)) {$AnimeMessage = $ENV:MSGGOOGLECHAT_ANIMEMSG}
if ([string]::IsNullOrEmpty($uri))          {$uri = $ENV:MSGGOOGLECHAT_URI}
if ([string]::IsNullOrEmpty($text       ))  {$text = $ENV:MSGGOOGLECHAT_TEXT}
if ([string]::IsNullOrEmpty($pathText   ))  {$pathText = $ENV:MSGGOOGLECHAT_PATHTEXT}
if ([string]::IsNullOrEmpty($titleImage ))  {$titleImage = $ENV:MSGGOOGLECHAT_TITLEIMG}
if ([string]::IsNullOrEmpty($pathImage  ))  {$pathImage = $ENV:MSGGOOGLECHAT_PATCHIMG}
if ([string]::IsNullOrEmpty($titleButon1))  {$titleButon1 = $ENV:MSGGOOGLECHAT_BUT1TIILE}
if ([string]::IsNullOrEmpty($titleButon2))  {$titleButon2 = $ENV:MSGGOOGLECHAT_BUT2TITLE}
if ([string]::IsNullOrEmpty($urlButon1  ))  {$urlButon1 = $ENV:MSGGOOGLECHAT_BUT1URL}
if ([string]::IsNullOrEmpty($urlButon2  ))  {$urlButon2 = $ENV:MSGGOOGLECHAT_BUT2URL}

Write-Host "title       : $title"
Write-Host "subtitle    : $subtitle"
Write-Host "titleMessage: $titleMessage"
Write-Host "typeMessage : $typeMessage"
Write-Host "AnimeMessage: $AnimeMessage"
Write-Host "uri         : $uri"
Write-Host "text        : $text"
Write-Host "pathText    : $pathText"
Write-Host "titleImage  : $titleImage"
Write-Host "pathImage   : $pathImage"
Write-Host "titleButon1 : $titleButon1"
Write-Host "titleButon2 : $titleButon2"
Write-Host "urlButon1   : $urlButon1"
Write-Host "urlButon2   : $urlButon2"

# Variveis auxilizares ==========================================================================
$textMessage  = $null
$image        = $null
$imageMessage = ""

try {

    #tratamento das variáveis de ambiente ========================================================
    #Obrigatórias --------------------------------------------------------------------------------
    if ( ([string]::IsNullOrEmpty($title)) -or 
         ([string]::IsNullOrEmpty($Subtitle)) -or
         ([string]::IsNullOrEmpty($uri)) 
       ) 
    { 
        throw "Não foi informado alguma(s) variavel(is) obrigatória(s): MSGGOOGLECHAT_TITLE | MSGGOOGLECHAT_SUBTITLE | MSGGOOGLECHAT_URI" 
    }

    #Não obrigatórios, valores default -----------------------------------------------------------
    if ([string]::IsNullOrEmpty($titleMessage))
    {
        $titleMessage = "Mensagem"
    }
    if ([string]::IsNullOrEmpty($titleImage))
    {
        $titleImage= "Imagem"
    }    
    if ([string]::IsNullOrEmpty($AnimeMessage))
    {
        $AnimeMessage = "false"
    }      
    
    #Validação de variveis com opções válidas ----------------------------------------------------
    #typeMessage: opções possíveis: W=warning | E=error | I=Info
    if (![string]::IsNullOrEmpty($typeMessage))
    {
        if( $typeMessage -in "W", "E", "I", "S" ) 
        { 
            if ( $typeMessage -eq "W" )
            {
                if ($AnimeMessage -eq "true") 
                    {$imageMessage = "https://i.ibb.co/tbvWvgj/warning-geral-move.gif"}
                else{$imageMessage = "https://i.ibb.co/zVSDHtL/warning-geral.png"}
            }
            elseif( $typeMessage -eq "E" )
            {
                if ($AnimeMessage -eq "true") 
                    {$imageMessage = "https://i.ibb.co/K2ZbwfP/error-geral-move.gif"} 
                else{$imageMessage = "https://i.ibb.co/B2WZnHV/error-geral.png"}                
            }
            elseif( $typeMessage -eq "S" )
            {
                if ($AnimeMessage -eq "true") 
                    {$imageMessage = "https://i.ibb.co/SN2LSWN/success-geral-move.gif"}
                else{$imageMessage = "https://i.ibb.co/d7mWqdt/success-geral.png"}    
            }
            else {
                if ($AnimeMessage -eq "true") 
                    {$imageMessage = "https://i.ibb.co/JQ7kTkS/info-geral-move.gif"}
                else{$imageMessage = "https://i.ibb.co/D7kmQK2/info-geral.png"}                  
            }
            
        }
        else { throw "typeMessage com valor inválido, opções: W=warning | E=error | I=info | S=success" }
    }
    else { throw "typeMessage não informado, opções: W=warning | E=error | I=info | S=success" }

    #Validando texto a ser enviado ---------------------------------------------------------------
    if (![string]::IsNullOrEmpty($pathText))
    {
        $textMessage = Get-Content -Path $pathText -Encoding "UTF8" -Force -Raw
    }
    elseif(![string]::IsNullOrEmpty($text))
    {
        $textMessage = $text
    }
    else { throw "pathfilelog e Text não foram informados" }

    #Validando/Preparando imagem ----------------------------------------------------------------
    if (![string]::IsNullOrEmpty($pathImage))
    {
        $nameImage    = Get-ChildItem -Path $pathImage -Name -Include *.PNG
        $pathImaFinal = $pathImage + '\' + $nameImage    
        [String]$base64IMG = [convert]::ToBase64String((Get-Content $pathImaFinal -Encoding byte))
        [String]$UriIMG    = "https://api.imgbb.com/1/upload?expiration=600&key=56cf8a1a1ebc3820d15ae1904b9ccfd4&image=&name=$nameImage&expiration=83400"
        [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
        $Result = Invoke-RestMethod -Uri "$UriIMG" -Method Post -Body @{ 'image' = "$base64IMG"} -ErrorAction Stop
        if ($Result.success) 
        {
            $image = $Result.data.image.url
        }
    }
    #Montando a mensagem para o google chat =================================================
    if (
            (![string]::IsNullOrEmpty($title)) -and
            (![string]::IsNullOrEmpty($Subtitle)) -and
            (![string]::IsNullOrEmpty($typeMessage))
       )
    {
        $title = [System.Net.WebUtility]::HtmlEncode($title)
        $Subtitle = [System.Net.WebUtility]::HtmlEncode($Subtitle)
        Get-Module -name GHCWebhook
        $message = New-GHCCardMessage -Content {    
                        #Header
                        New-GHCCardHeader -Title "$title" -Subtitle "$Subtitle" -ImageURL "$imageMessage" -ImageStyle avatar
                        New-GHCCardSection -Content {
                            #Text
                            if ( ![string]::IsNullOrEmpty($textMessage) )
                            {
                                New-GHCCardWidget -Header "${titleMessage}:" -Content { 
                                    New-GHCWidgetTextParagraph -Text $textMessage 
                                }
                            }

                            #Image
                            if ( (![string]::IsNullOrEmpty($image)) )
                            {
                                New-GHCCardWidget -Header "${titleImage}:" -Content { 
                                    New-GHCWidgetImage -URL "$image"
                                }
                            }

                            #Button
                            if ( 
                                 ( ((![string]::IsNullOrEmpty($titleButon1)) -and (![string]::IsNullOrEmpty($urlButon1))) -or
                                   ((![string]::IsNullOrEmpty($titleButon2)) -and (![string]::IsNullOrEmpty($urlButon2)))
                                 ) 
                               )
                            {
                                New-GHCCardWidget -Content { 
                                    if ( (![string]::IsNullOrEmpty($titleButon1)) )
                                    {
                                        New-GHCWidgetTextButton -text "$titleButon1" -onclickurl "$urlButon1"
                                    }
                                    else {Write-Host "Erro na geração do botão 1"}
                                    if ( (![string]::IsNullOrEmpty($titleButon2)) )
                                    {
                                        New-GHCWidgetTextButton -text "$titleButon2" -onclickurl "$urlButon2"
                                    }
                                }
                            }
                            else {Write-Host "Erro na validação dos botões"}
                        }
                    }
                    Send-GHCWebhookMessage -URI $uri -Message $message
    }
    else
    {
        throw "Não foi informado alguma(s) variavel(is) obrigatória(s): title | Subtitle | typeMessage" 
    }
}
catch {
    Write-Host "Erro(s) durante a montagem da mensagem para o google chat:"
    $_ | Out-String
}
