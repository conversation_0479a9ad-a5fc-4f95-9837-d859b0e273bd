#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'FWMVCDEF.CH'
 
//-------------------------------------------------------------------
/*/{Protheus.doc} f151bor
PE para posicionar bordero ao gerar instrucao de cobranca

<AUTHOR>
@since 29/09/2016  
@version P12 
/*/ 
//-------------------------------------------------------------------
User Function F151BOR() 
Local lRet := .F. 

dbSelectarea("SEA")
DbSetOrder(4)
SEA->( DbSeek(FI2->FI2_FILIAL + FI2->FI2_NUMBOR + FI2->FI2_PREFIX + FI2->FI2_TITULO + FI2->FI2_PARCEL + FI2->FI2_TIPO)  )

If SEA->(!Eof()) 
	lRet := .T.
EndIf
 
return lRet 

 
