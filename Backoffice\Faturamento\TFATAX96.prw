#include 'protheus.ch'
#include 'parmtype.ch'

/*/{Protheus.doc} TFATAX96
//TODO Preenche o campo PZG_ULTPED no browse.
<AUTHOR>
@since 01/10/2019
@version 1.0
@return Numero do pedido

@type function
/*/
user function TFATAX96()
	Local cRet := ""

	 DbSelectArea("PZH")
	 PZH->(DbSetOrder(1))

	 PZH->(DbGotop())
	If PZH->(DbSeek(xFilial("PZG")+STR(PZG->PZG_ID,10,0))) 
		cRet:= PZH->PZH_PEDIDO
		While STR(PZG->PZG_ID) == STR(PZH->PZH_IDPLN)

			If cRet < PZH->PZH_PEDIDO
				cRet:= PZH->PZH_PEDIDO
			EndIf

			PZH->(DbSkip())
		EndDO
	EndIf
	
return cRet