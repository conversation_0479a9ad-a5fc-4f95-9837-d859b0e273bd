#include "tlpp-core.th"


Class   CreateRegisterToControlCheckout
    Private Data    oTableControl                   As  Object
    Public  Method  new()                           As  Object      
    Public  Method  setObject(oVal  As  Object)     As  Object
    Public  Method  execute()                       As  Character      
EndClass


Method  new()   As  Object  Class CreateRegisterToControlCheckout
    ::oTableControl   :=  Nil
Return Self


Method  setObject(oVal  As  Object) As  Object  Class CreateRegisterToControlCheckout
    ::oTableControl   :=  oVal
Return Self


Method  execute()   As  Character  Class CreateRegisterToControlCheckout
    Local   dDate   :=  Date()      As  Date
    Local   cTime   :=  Time()      As  Character
    Local   nX      :=  1           As  Numeric 
    Local   cUUID   :=  ''          As  Character



    For nX  :=  1   to  Len(::oTableControl:listaProdutos)
        cUUID:=  SHA1(::oTableControl:contrato+::oTableControl:numero+DtoC(dDate) + cTime)
        PXG->(RecLock("PXG",.T.))
            PXG->PXG_FILIAL     :=  xFilial('PXG')
            PXG->PXG_CODIGO     :=  GetSxeNum("PXG","PXG_CODIGO")
            PXG->PXG_DATINC     :=  dDate 
            PXG->PXG_HORINC     :=  cTime       
            PXG->PXG_CLIENT     :=  ::oTableControl:cliente
            PXG->PXG_LOJA       :=  ::oTableControl:loja
            PXG->PXG_CONTRA     :=  ::oTableControl:contrato
            PXG->PXG_NUMERO     :=  ::oTableControl:numero
            PXG->PXG_ITEM       :=  ::oTableControl:listaProdutos[nX]['item']
            PXG->PXG_PRODUT     :=  ::oTableControl:listaProdutos[nX]['produto']
            PXG->PXG_DESCRI     :=  ::oTableControl:listaProdutos[nX]['descricao']
            PXG->PXG_PROPOS     :=  ::oTableControl:proposta
            PXG->PXG_EMAIL      :=  ::oTableControl:email
            PXG->PXG_VALOR      :=  ::oTableControl:nVlrTitulo
            PXG->PXG_STOKEN     :=  '0'
            PXG->PXG_XTPPG      :=  ::oTableControl:tipoPagamento
            PXG->PXG_UUID       :=  cUUID
            If PXG->(FieldPos("PXG_DTVLD")) > 0 
                PXG->PXG_DTVLD      :=  ::oTableControl:dDtVld
            EndIf
            PXG->(ConfirmSx8())
        PXG->(MsUnLock())
    Next Nx
Return cUUID




