#include    "tlpp-core.th"

Class   LinkPBoletoNfPosPDynamicDataDTO

    Public  Data    RazaoSocial             As  Character
    Public  Data    DatAnoNF                As  Character
    Public  Data    EmpresaResponsavel      As  Character
    Public  Data    NumNF                   As  Character
    Public  Data    DataNF                  As  Character
    Public  Data    CodigoNF                As  Character
    Public  Data    NomeDaEmpresa           As  Character
    Public  Data    CNPJEmpresa             As  Character
    Public  Data    VencimentoBoleto        As  Character
    Public  Data    TotalPagar              As  Character

    Public  Data    LinkBoleto              As  Character
    Public  Data    LinkNota                As  Character
    Public  Data    LinkCentral             As  Character
    Public  Data    LinkRps                 As  Character
    Public  Data    LinkGuiaAtendimento     As  Character
    Public  Data    LinkSuporte             As  Character

    //necessárias por conta de validação da  AWS remover depois

    Public  Data    proc_link_NFE	        As  Character
    Public  Data    proc_link_RPS	        As  Character
    Public  Data    proc_link_GUIA 	        As  Character
    Public  Data    TxtMsg			        As  Character
    Public  Data    NomeCliente		        As  Character
    Public  Data    MsgPix 			        As  Character
    Public  Data    cNw1Lnk			        As  Character
    Public  Data    cNw2Lnk			        As  Character
    Public  Data    cNw3Lnk			        As  Character
    Public  Data    numeroRPS		        As  Character
    Public  Data    numeroNFE		        As  Character
    Public  Data    emissaoNFE		        As  Character
    Public  Data    codigoNFE		        As  Character
    Public  Data    cLinkContato	        As  Character
    Public  Data    cObs1			        As  Character
    Public  Data    cObs2			        As  Character
    Public  Data    cObs3			        As  Character
    Public  Data    cObs4			        As  Character
    Public  Data    cObs5			        As  Character
    Public  Data    cObs6			        As  Character
    Public  Data    cObs7			        As  Character
    Public  Data    cObs8			        As  Character
    Public  Data    cObs9			        As  Character
    Public  Data    cObs10			        As  Character
    Public  Data    cObs11			        As  Character
    Public  Data    EmpAnt			        As  Character
    Public  Data    FilAnt			        As  Character
    Public  Data    LogicalPix		        As  Logical
    Public  Data    parcelas		        As  Array

    Public  Method  new()                   As  Object
    Public  Method  transformToJson()       As  Json
EndClass


Method  new()   As  Object  Class   LinkPBoletoNfPosPDynamicDataDTO
    //Campos personalizados
    ::RazaoSocial               :=  ''
    ::DatAnoNF                  :=  ''
    ::EmpresaResponsavel        :=  ''
    ::NumNF                     :=  ''
    ::DataNF                    :=  ''
    ::CodigoNF                  :=  ''
    ::DataNF                    :=  ''
    ::NomeDaEmpresa             :=  ''
    ::CNPJEmpresa               :=  ''
    ::VencimentoBoleto          :=  ''
    ::TotalPagar                :=  ''
    ::LinkBoleto                :=  ''
    ::LinkNota                  :=  ''
    ::LinkCentral               :=  ''
    ::LinkRps                   :=  ''
    ::LinkGuiaAtendimento       :=  ''
    ::LinkSuporte               :=  ''
    //Campos que precisam ser mantidos até mudaram na AWS para evitar validação deles
    ::proc_link_NFE		        :=  '-'
    ::proc_link_RPS		        :=  '-'
    ::proc_link_GUIA 		    :=  '-'
    ::TxtMsg				    :=  ''
    ::NomeCliente			    :=  ''
    ::MsgPix 				    :=  ''
    ::cNw1Lnk				    :=  ''
    ::cNw2Lnk				    :=  ''
    ::cNw3Lnk				    :=  ''
    ::numeroRPS			        :=  ''
    ::numeroNFE			        :=  ''
    ::emissaoNFE			    :=  ''
    ::codigoNFE			        :=  ''
    ::cLinkContato		        :=  ''
    ::cObs1				        :=  ''
    ::cObs2				        :=  ''
    ::cObs3				        :=  ''
    ::cObs4				        :=  ''
    ::cObs5				        :=  ''
    ::cObs6				        :=  ''
    ::cObs7				        :=  ''
    ::cObs8				        :=  ''
    ::cObs9				        :=  ''
    ::cObs10				    :=  ''
    ::cObs11				    :=  ''
    ::EmpAnt				    :=  ''
    ::FilAnt				    :=  ''
    ::LogicalPix			    :=  .F.
    ::parcelas			        :=  {}

Return  Self


Method  transformToJson()       As  Json     Class   LinkPBoletoNfPosPDynamicDataDTO
    Local oJsonToReturn :=  JsonObject():new()
    //Campos personalizados
    oJsonToReturn['RazaoSocial']            :=  ::RazaoSocial        
    oJsonToReturn['DatAnoNF']               :=  ::DatAnoNF           
    oJsonToReturn['EmpresaResponsavel']     :=  ::EmpresaResponsavel 
    oJsonToReturn['NumNF']                  :=  ::NumNF              
    oJsonToReturn['DataNF']                 :=  ::DataNF             
    oJsonToReturn['CodigoNF']               :=  ::CodigoNF           
    oJsonToReturn['DataNF']                 :=  ::DataNF             
    oJsonToReturn['NomeDaEmpresa']          :=  ::NomeDaEmpresa      
    oJsonToReturn['CNPJEmpresa']            :=  ::CNPJEmpresa        
    oJsonToReturn['VencimentoBoleto']       :=  ::VencimentoBoleto   
    oJsonToReturn['TotalPagar']             :=  ::TotalPagar         
    oJsonToReturn['LinkBoleto']             :=  ::LinkBoleto         
    oJsonToReturn['LinkNota']               :=  ::LinkNota           
    oJsonToReturn['LinkCentral']            :=  ::LinkCentral        
    oJsonToReturn['LinkRps']                :=  ::LinkRps            
    oJsonToReturn['LinkGuiaAtendimento']    :=  ::LinkGuiaAtendimento
    oJsonToReturn['LinkSuporte']            :=  ::LinkSuporte        
    //Campos que precisam ser mantidos até mudaram na AWS para evitar validação deles
    oJsonToReturn['proc_link_NFE']          :=  ::proc_link_NFE
    oJsonToReturn['proc_link_RPS']          :=  ::proc_link_RPS  
    oJsonToReturn['proc_link_GUIA']         := 	::proc_link_GUIA   
    oJsonToReturn['TxtMsg']                 :=	::TxtMsg		   
    oJsonToReturn['NomeCliente']            :=	::NomeCliente	   
    oJsonToReturn['MsgPix']                 := 	::MsgPix		   
    oJsonToReturn['cNw1Lnk']                :=  ::cNw1Lnk	   
    oJsonToReturn['cNw2Lnk']                :=	::cNw2Lnk		   
    oJsonToReturn['cNw3Lnk']                :=	::cNw3Lnk		   
    oJsonToReturn['numeroRPS']              :=	::numeroRPS	   
    oJsonToReturn['numeroNFE']              :=	::numeroNFE	   
    oJsonToReturn['emissaoNFE']	            :=	::emissaoNFE   
    oJsonToReturn['codigoNFE']	            :=	::codigoNFE   
    oJsonToReturn['cLinkContato']           :=	::cLinkContato   
    oJsonToReturn['cObs1']                  :=	::cObs1		   
    oJsonToReturn['cObs2']                  :=	::cObs2	   
    oJsonToReturn['cObs3']                  :=	::cObs3	   
    oJsonToReturn['cObs4']                  :=	::cObs4	   
    oJsonToReturn['cObs5']                  :=	::cObs5	   
    oJsonToReturn['cObs6']                  :=	::cObs6	   
    oJsonToReturn['cObs7']                  :=	::cObs7	   
    oJsonToReturn['cObs8']                  :=	::cObs8	   
    oJsonToReturn['cObs9']                  :=	::cObs9	   
    oJsonToReturn['cObs10']                 :=	::cObs10		   
    oJsonToReturn['cObs11']                 :=	::cObs11	   
    oJsonToReturn['EmpAnt']                 :=	::EmpAnt	   
    oJsonToReturn['FilAnt']                 :=	::FilAnt	   
    oJsonToReturn['LogicalPix']             :=	::LogicalPix   
    oJsonToReturn['parcelas']               :=	::parcelas   
Return  oJsonToReturn
