#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'FILEIO.CH'  

User Function ESPX010C(cTokenExt)

    Local cAliasAce := ''
    Local cFilelist := ''
    
    Local aArquivos := {}
    

    cAliasAce := GetNextAlias()

    BeginSQL Alias cAliasAce
        SELECT PVM.PVM_TIPO,
               PVM.PVM_ORIGEM,
               PVM.PVM_DESTIN
          FROM %Table:PV7% PV7
         INNER JOIN %Table:PVD% PVD
                 ON PVD_PAIS   = PV7.PV7_PAIS
                AND PVD_CODPRJ = PV7.PV7_CODPRJ
                AND PVD_VALIDA = '1'
                AND PVD.%NotDel%
         INNER JOIN %Table:PVQ% PVQ
                 ON PVQ.PVQ_PAIS   = PV7.PV7_PAIS
                AND PVQ.PVQ_CODPER = PVD.PVD_CODPRG
                AND CAST(PVQ.PVQ_COMBO AS VARCHAR) = (SELECT DISTINCT   CASE PVA.PVA_TIPRES   
                                                                        WHEN '3' THEN '1'
                                                                        WHEN '4' THEN '1'
                                                                        ELSE PVD.PVD_RESPUE END
							FROM %Table:PVA% PVA
							WHERE PVA.PVA_CODPRG = PVD.PVD_CODPRG
								AND PVA.PVA_PAIS = PV7.PV7_PAIS
								AND PVA.%NotDel%)
                AND PVQ.PVQ_TIPO   = '3'
                AND PVQ.%NotDel%
         INNER JOIN %Table:PVM% PVM
                 ON PVM.PVM_PAIS   = PV7.PV7_PAIS
                AND PVM.PVM_CODACE = PVQ.PVQ_CODACE
                AND PVM.%NotDel%
          WHERE PV7.PV7_TOKEN  = %Exp:cTokenExt%
            AND PV7.%NotDel%
        UNION
        SELECT PVM.PVM_TIPO,
               PVM.PVM_ORIGEM,
               PVM.PVM_DESTIN
          FROM %Table:PV7% PV7
         INNER JOIN %Table:PVD% PVD
                 ON PVD_PAIS   = PV7.PV7_PAIS
                AND PVD_CODPRJ = PV7.PV7_CODPRJ
                AND PVD_VALIDA = '1'
                AND PVD.%NotDel%
         INNER JOIN %Table:PVP% PVP
                 ON PVP.PVP_PAIS   = PV7.PV7_PAIS
                AND PVP.PVP_CODPER = PVD.PVD_CODPRG
                AND CAST(PVP.PVP_COMBO AS VARCHAR) = (SELECT DISTINCT CASE PVA.PVA_TIPRES   
                                                                        WHEN '3' THEN '1'
                                                                        WHEN '4' THEN '1'
                                                                        ELSE PVD.PVD_RESPUE END
							FROM %Table:PVA% PVA
							WHERE PVA.PVA_CODPRG = PVD.PVD_CODPRG
								AND PVA.PVA_PAIS = PV7.PV7_PAIS
								AND PVA.%NotDel%)
                AND PVP.%NotDel%
         INNER JOIN %Table:PVO% PVO
                 ON PVO.PVO_PAIS   = PV7.PV7_PAIS
                AND PVO.PVO_CODGRP = PVP.PVP_CODGRP
                AND PVO.PVO_TIPO   = '3'
                AND PVO.%NotDel%
         INNER JOIN %Table:PVM% PVM
                 ON PVM.PVM_PAIS   = PV7.PV7_PAIS
                AND PVM.PVM_CODACE = PVO.PVO_CODACE
                AND PVM.%NotDel%
          WHERE PV7.PV7_TOKEN  = %Exp:cTokenExt%
            AND PV7.%NotDel%
    EndSQL

    While (cAliasAce)->(!EoF())
        AAdd(aArquivos, {AllTrim((cAliasAce)->PVM_TIPO), AllTrim((cAliasAce)->PVM_ORIGEM), AllTrim((cAliasAce)->PVM_DESTIN)})
        (cAliasAce)->(DbSkip())
    End

    cFilelist := FwJsonSerialize(aArquivos)

Return cFilelist
