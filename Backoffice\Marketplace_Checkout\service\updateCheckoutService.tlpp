#include "tlpp-core.th"


Class   UpdateCheckoutService
    Private Data    oCreatedObject                              As      Object
    Private Data    cResponseIpaas                              As      Character
    Private Data    cUuid                                       As      Character
    Private Data    cCheckoutCode                               As      Character

    Public  Method  new(oVar    As  Object)                     As      Object
    Public  Method  execute()                                   As      Object
    Public  Method  setUuid(cValue  As  Character)              As      Object
    Private Method  sendToIpaas(cJson   As  Character)          As      Object
    Private Method  writeOnDb()                                 As      Object
    Private Method  setCheckoutCode()                           As      Object
EndClass


Method  new(oVar    As  Object) As  Object  Class   UpdateCheckoutService
    ::oCreatedObject        :=      oVar
    ::cResponseIpaas        :=      ''
    ::cUuid                 :=      ''
    ::cCheckoutCode         :=      ''
Return  Self


Method  execute()   As  Object  Class   UpdateCheckoutService
    ::setCheckoutCode()
    ::sendToIpaas(::oCreatedObject:ToString())
    ::writeOnDb()
Return  Self


Method  setUuid(cValue  As  Character)   As  Object  Class   UpdateCheckoutService
    ::cUuid :=  cValue
Return  Self


Method  setCheckoutCode()   As      Object  Class   UpdateCheckoutService
    dbSelectArea('PXG')
    dbSetOrder(1)
    If dbSeek(::cUuid)
        ::cCheckoutCode         :=      PXG->PXG_CKCODE
    EndIf
    
Return  Self

Method  writeOnDb() As  Object  Class   UpdateCheckoutService

Return Self


Method  sendToIpaas(cJson   As  Character)   As  Object  Class   UpdateCheckoutService
    //Local   cUrl          := GetMV("TI_TIPDXX5",,"https://api-ipaas.totvs.app/ipaas/api/v1/integrations/bedf3666-b94d-4893-b51c-e3ddb8b20439/api-key/34084cc2-4eb4-4e09-8774-722909561c37")
    Local   cUrl    :=  'https://api-ipaas.totvs.app/sync-hook/api/v1/integrations/b3e3eb7b-d5a8-4c60-b379-fdbd4d9b2093/api-key/65b77cac-3e2c-4b9b-9693-26580a48cd7a'
    Local   oIpaas  :=  Nil

    oIpaas := TIiPaaS():New()
	oIpaas:SetWebHook(cUrl)
	oIpaas:SetBodyWebHook(cJson)
    oIpaas:addHeaderParam('CheckoutCode: ' + ::cCheckoutCode)
	oIpaas:SendToiPaaS()

    ::cResponseIpaas  :=  oIpaas:getIpaasCharacterResponse()

    FreeObj(oIpaas)

Return  Self
