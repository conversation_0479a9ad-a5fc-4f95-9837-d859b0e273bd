#Include 'Protheus.ch'
#Include 'TopConn.ch'

User Function ATUGCT001()

Local _cQry		:= ""
Local _cAlias	:= ""

RpcClearEnv()
RpcSetType(3)
RpcSetEnv("00", "00001000100")

_cQry := "	SELECT			" + CRLF
_cQry += "		PH6_FILIAL	" + CRLF
_cQry += "		,PH6_CONTRA	" + CRLF
_cQry += "		,PH6_REVISA	" + CRLF
_cQry += "		,PH6_NUMERO	" + CRLF
_cQry += "		,PH6_COMPET	" + CRLF
_cQry += "		,PH6_CONDIC	" + CRLF
_cQry += "		,PH6_CLIENT	" + CRLF
_cQry += "		,PH6_LOJA	" + CRLF
_cQry += "		,PH6_CONDPG	" + CRLF
_cQry += "		,PH6_NOTASE	" + CRLF
_cQry += "		,PH6_MOEDA	" + CRLF
_cQry += "		,PH6_MASCCC	" + CRLF
_cQry += "		,PH6_GU		" + CRLF
_cQry += "		,PH6_SEQ	" + CRLF
_cQry += "		,PH6_MANCOR	" + CRLF
_cQry += "		,PH6.R_E_C_N_O_ RECNUMPH6	" + CRLF
_cQry += "		,PH5_CONTRA	" + CRLF
_cQry += "		,PH5_REVISA	" + CRLF
_cQry += "		,PH5_NUMERO	" + CRLF
_cQry += "		,PH5_COMPET	" + CRLF
_cQry += "		,PH5_CONDIC	" + CRLF
_cQry += "		,PH5_CLIENT	" + CRLF
_cQry += "		,PH5_LOJA	" + CRLF
_cQry += "		,PH5_CONDPG	" + CRLF
_cQry += "		,PH5_NOTASE	" + CRLF
_cQry += "		,PH5_MOEDA	" + CRLF
_cQry += "		,PH5_MASCCC	" + CRLF
_cQry += "		,PH5_GU		" + CRLF
_cQry += "		,PH5_SEQ	" + CRLF
_cQry += "		,PH5.R_E_C_N_O_ RECNUMPH5	" + CRLF
_cQry += "	FROM			" + CRLF
_cQry += "		PH6000 PH6	" + CRLF
_cQry += "			INNER JOIN		" + CRLF
_cQry += "				PH5000 PH5	" + CRLF
_cQry += "			ON				" + CRLF
_cQry += "				PH5_FILIAL = ' '		" + CRLF
_cQry += "				AND						" + CRLF
_cQry += "				PH5_CONTRA = PH6_CONTRA	" + CRLF
_cQry += "				AND						" + CRLF
_cQry += "				PH5_REVISA = PH6_REVISA	" + CRLF
_cQry += "				AND						" + CRLF
_cQry += "				PH5_NUMERO = PH6_NUMERO	" + CRLF
_cQry += "				AND						" + CRLF
_cQry += "				PH5_COMPET = PH6_COMPET	" + CRLF
_cQry += "				AND						" + CRLF
_cQry += "				PH5_CONDIC = PH6_CONDIC	" + CRLF
_cQry += "				AND						" + CRLF
_cQry += "				PH5_CLIENT = PH6_CLIENT	" + CRLF
_cQry += "				AND					" + CRLF
_cQry += "				PH5_LOJA = PH6_LOJA	" + CRLF
_cQry += "				AND						" + CRLF
_cQry += "				PH5_CONDPG = PH6_CONDPG	" + CRLF
_cQry += "				AND						" + CRLF
_cQry += "				PH5_NOTASE = PH6_NOTASE	" + CRLF
_cQry += "				AND						" + CRLF
_cQry += "				PH5_MOEDA = PH6_MOEDA	" + CRLF
_cQry += "				AND						" + CRLF
_cQry += "				PH5_MASCCC = PH6_MASCCC	" + CRLF
_cQry += "				AND				" + CRLF
_cQry += "				PH5_GU = PH6_GU	" + CRLF
_cQry += "				AND					" + CRLF
_cQry += "				PH5_SEQ = PH6_SEQ	" + CRLF
_cQry += "				AND						" + CRLF
_cQry += "				PH5.D_E_L_E_T_ = ' '	" + CRLF
_cQry += "	WHERE					" + CRLF
_cQry += "		PH6_FILIAL = ' '	" + CRLF
_cQry += "		AND					" + CRLF
_cQry += "		PH6_CONTRA <> ' '	" + CRLF
_cQry += "		AND					" + CRLF
_cQry += "		PH6_MANCOR <> ' '	" + CRLF
_cQry += "		AND					" + CRLF
_cQry += "		PH6.D_E_L_E_T_ = ' '" + CRLF

_cAlias := GetNextAlias()

If Select( _cAlias ) > 0
	dbSelectArea( _cAlias )
	( _cAlias )->( dbCloseArea() )
EndIf

TCQUERY _cQry NEW ALIAS ( _cAlias )

While ( _cAlias )->( !EoF() )
	
	dbSelectArea("PH5")
	PH5->(dbGoTo( ( _cAlias )->RECNUMPH5 ))
	
	RecLock("PH5", .F.)
	PH5->PH5_CMPCTB	:= ( _cAlias )->PH6_MANCOR
	PH5->(MsUnLock())
	
	( _cAlias )->( dbSkip() )
EndDo

Return()