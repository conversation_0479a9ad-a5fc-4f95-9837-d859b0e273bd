//----------------------------------------------------------------
/*/{Protheus.doc} 
<AUTHOR>
@since 10/07/2021
@version 1.0
/*/
//----------------------------------------------------------------
User Function AJVIAGEM()
Local aSays 	 := {}
Local aButtons	 := {}
Local nOpca		 := 0
Local lRet       := .T. 
Local aParam1 	 := {}
Local aRetP1	 := {}
Local cObs       := ''
Local nI		 := 0
Local nPontos    := 0 
Local oDlg       := Nil    
Local nOpc       := 2 
Local cNomeArq   := '' 
Local cDiretorio 
Private cErrArqRet := '' 
Private cLogArqRet := '' 
Private cPH4Alias  := GetNextAlias() 
Private cMaxPH4Alias  := GetNextAlias() 
Private cLogArqRet 

AADD(aSays, OemToAnsi("Inserir Dados de Viagens - Tabela ZX5"))
AADD(aSays, OemToAnsi(""))
AADD(aButtons, {1,.T.,{|| nOpca := 1, FECHABATCH()}})
AADD(aButtons, {2,.T.,{|| nOpca := 0, FECHABATCH()}})

FormBatch("Insere CSV na Tabela ZX5", aSays, aButtons)

If nOpca == 1
	cObs := cGetFile("Arquivos CSV|*.CSV|","Processar arquivo ",0,,.T.,GETF_ONLYSERVER+GETF_LOCALFLOPPY+GETF_LOCALHARD  )
		
	If !Empty(cObs)
		nOpc := 1
		cLogArqRet := strtran(cObs,".","_resultado.")
	EndIf
		
	If nOpc == 1 
		cObs := Alltrim(cObs)	
		If Empty( cObs ) 
			lRet := .F.
		Endif
				
		If !File(  (cObs)  )
			lRet := .F.
		EndIf
			
		If lRet 
			For ni := 1 to Len(cObs) 
				If substr(cObs,ni,1) == "\"
					nPontos := ni + 1
				EndIf 
			Next
			If nPontos > 0 
				cNomeArq := substr(cObs, nPontos, Len(cObs))
			EndIf

			FwMsgRun(, { || Reprocessa(cObs) }, "Processando ","Aguarde")
			
		EndIf
	EndIf

EndIf

Return

//-------------------------------------------------------------------
/*{Protheus.doc} Reprocessa
Varre CSV 

<AUTHOR> Leite
@since 15/07/2015
@version Protheus12
*/
//-------------------------------------------------------------------
Static function Reprocessa(cArqRet)
Local cBuffer := ""
Local nCpos
Local cString := ""
Local aCampos := {}
Local nHandler
Local nLinha := 0 

Local nTamTabela   := TamSx3("ZX5_TABELA")[1]
Local nTamChv   := TamSx3("ZX5_CHAVE")[1]
Local nTamChv2   := TamSx3("ZX5_CHAVE2")[1]  

Procregua(0)
cErrArqRet := strtran(cArqRet, ".", "_erro.")
cLogArqRet := strtran(cArqRet, ".", "_log.")
nHandler := fOpen(cArqRet,68)
	
FT_FUSE(cArqRet)
FT_FGOTOP()

While !FT_FEOF()

	nLinha += 1
	cBuffer := FT_FReadLn()
	aCampos := {}
	cString := "" 

	For nCpos := 1 to Len(cBuffer)
		cString += IIf( Substr(cBuffer,nCpos,1) != ";", Substr(cBuffer,nCpos,1), "")

		If Substr(cBuffer,nCpos,1) == ";"
	
			aAdd(aCampos, Upper(cString))
			cString := ""
		Endif
	Next
	
	If !Empty(cString)
		aAdd(aCampos, Upper(cString))
	EndIf
	
	If Len(aCampos) > 0 
        bBlock	:= ErrorBlock({|e|  ThrdChkErr( e, cBuffer)  })

		BEGIN SEQUENCE

            If nLinha > 0 
                aCampos[1] := padr(aCampos[1],nTamTabela)
                aCampos[2] := padr(aCampos[2],nTamChv)
                aCampos[3] := padr(aCampos[3],nTamChv2)
                DbSelectArea("ZX5")
                DbSetOrder(2)//ZX5_FILIAL+ZX5_TABELA+ZX5_CHAVE2+ZX5_CHAVE
                ZX5->( DbSeek(Fwxfilial('ZX5')+aCampos[1]+aCampos[3]+aCampos[2] ) )

                If ZX5->(Eof())
				    ZX5->(reclock('ZX5',.T.))
                    ZX5->ZX5_FILIAL := Fwxfilial('ZX5')
                    ZX5->ZX5_TABELA := aCampos[1]
                    ZX5->ZX5_CHAVE  := aCampos[2]
                    ZX5->ZX5_CHAVE2 := aCampos[3]
                    ZX5->ZX5_DESCRI := aCampos[4]
                    ZX5->(Msunlock())
                    u_xacalog(cLogArqRet, cBuffer + ";OK")
                Else
                    u_xacalog(cLogArqRet, cBuffer + ";REG_JA_EXISTE")
                Endif
            EndIf

		END SEQUENCE
		ErrorBlock(bBlock)
	EndIf
		
	FT_FSKIP()
EndDo

FT_FUSE()
FCLOSE(nHandler)

MsgInfo('Processo Finalizado!')
return

//----------------------------------------------------------------
/*/{Protheus.doc} ThrdChkErr
Captura errlog 

<AUTHOR>
@since 10/07/2020
@version 1.0
/*/
//----------------------------------------------------------------
Static Function ThrdChkErr(oErroArq, cBuffer) 
Local n:= 2
Local cLogError 
Local cDir :=  ""
Local cbErrInThrd := ''
Local cRet := oErroArq:Description + oErroArq:ErrorStack

U_xacalog(cErrArqRet , (cBuffer + ' <----- Linha problematica'  )   )
U_xacalog(cErrArqRet , cRet)

If oErroArq:GenCode > 0
    cbErrInThrd += Alltrim( Str( oErroArq:GenCode ) )
    cbErrInThrd += chr(13)+chr(10)
    cbErrInThrd += AllTrim( oErroArq:Description )
    cbErrInThrd += chr(13)+chr(10)
EndIf 

While ( !Empty(ProcName(n)) )
    cbErrInThrd += Trim(ProcName(n)) + " (" + Alltrim(Str(ProcLine(n))) + ") " + chr(13)+chr(10)
    n++
End   

cbErrInThrd += chr(13)+chr(10)

U_xacalog(cErrArqRet , cbErrInThrd )          

Break
Return cRet 

