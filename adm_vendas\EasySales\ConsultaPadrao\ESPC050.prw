#INCLUDE 'TOTVS.CH'
#INCLUDE 'ESPC050.CH'

//--------------------------------------------------------------------
/*/{Protheus.doc} ESPC050
Consulta Especifica customizada aceleradores de Par�metros, EasySales.
@return Nil
/*/
//--------------------------------------------------------------------
User Function ESPC050()
	
	Local cDir     := GetMV("FS_UPDSYS")
	
	Local lRet     := .F.
	
	Local oDlg 	   := Nil
	Local oBrw     := Nil
	Local oCol	   := Nil
	
	Local bOk      := {|| lRet := .T., oDlg:End()}
	Local bCancel  := {|| oDlg:End()}
	
	Local aColunas := {}
	
	//Local xDriver	:= alltrim(lower(GetSrvProfString('LocalFiles','indefinido')))
	
	If Select("SX6AP") == 0
		
		//dbUseArea(.T.,'CTREECDX',cDir+'sx6.dtc','SX6AP',.T.,.F.)
		USE (cDir +'sx6.dtc') ALIAS ("SX6AP") SHARED NEW VIA ("CTREECDX") 
		dbSetIndex(cDir+'sx6.cdx')
		dbSetOrder(1)
		
	EndIf	
	
	DEFINE DIALOG oDlg TITLE STR0001 FROM 0, 0 TO 320, 600 PIXEL STYLE DS_MODALFRAME
	
	oBrw := FWFormBrowse():New(oDlg)
	oBrw:DisableConfig()
	oBrw:DisableDetails()
	oBrw:DisableReport()
	oBrw:SetDataTable(.T.)
	oBrw:SetAlias("SX6AP")
	oBrw:SetDescription(STR0002)
	oBrw:Setdoubleclick(bOk)
	oBrw:AddButton("Cancel", bCancel)
	oBrw:AddButton("OK", bOk)	
	oBrw:SetDBFFilter()
	oBrw:SetUseFilter() 
	oBrw:SetSeek()
		
	oCol := FWBrwColumn():New()
	oCol:SetTitle(STR0003)
	oCol:SetData({||SX6AP->&("X6_VAR")})
	oCol:SetSize(Len(SX6AP->&("X6_VAR")))
	aAdd(aColunas, oCol)
	
	oCol := FWBrwColumn():New()
	oCol:SetTitle(STR0004)
	oCol:SetData({||SX6AP->&("X6_CONTEUD")})
	aAdd(aColunas, oCol)
	
	oBrw:SetColumns(aColunas)
	oBrw:Activate(oDlg)
	
	ACTIVATE MSDIALOG oDlg CENTERED
	
Return lRet
