#include "tlpp-core.th"

/*===================================================
Exemplo de Payload:
{
    "status": 1,
    "return_page": "www.totvs.com.br",
    "description": "string 1 ",
    "price": "10",
    "customer": {
        "name": "will update",
        "document": "87.133.195/0001-26",
        "email": "<EMAIL>"
    },
    "allowed_payment_forms": [
        {
            "method": "credit_card",
            "installments": 2
        }
    ],
    "valid": "2025-05-08T16:18:15.879Z"
}
===================================================*/

Class   UpdateCheckoutDTO
    Public  Data    status                  As  Numeric
    Public  Data    return_page             As  Character 
    Public  Data    description             As  Character
    Public  Data    price                   As  Character
    Public  Data    customer                As  Object
    Public  Data    allowed_payment_forms   As  Array
    Public  Data    valid                   As  Character

    Public  Method  new()                   As  Object
    Public  Method  toString()              As  Character
EndClass


Method  new()   As  Object  Class   UpdateCheckoutDTO
    Local oArrayForms           As  Json
    ::status                    :=  0
    ::return_page               :=  ''
    ::description               :=  ''
    ::price                     :=  ''
    
    ::customer                  :=  JsonObject():new()
    ::customer['name']          :=  ''
    ::customer['document']      :=  ''
    ::customer['email']         :=  ''
    


    ::allowed_payment_forms     :=  {}
    oArrayForms                 :=  JsonObject():new()
    oArrayForms['method']       :=  ''
    oArrayForms['installments'] :=  ''
    Aadd(::allowed_payment_forms, oArrayForms)

    ::valid                     :=  ''
Return Self


Method  toString()  As  Character  Class   UpdateCheckoutDTO
    Local   oJson   :=  JsonObject():new()
    
    oJson['status']                     :=  ::status
    oJson['return_page']                :=  ::return_page
    oJson['description']                :=  ::description
    oJson['price']                      :=  ::price
    oJson['customer']                   :=  ::customer
    oJson['allowed_payment_forms']      :=  ::allowed_payment_forms
    oJson['valid']                      :=  ::valid

Return oJson:toJson()
