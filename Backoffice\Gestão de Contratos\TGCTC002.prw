/*/
{PROJETO} - TGCTC001.PRW
@desc:		Fonte para consulta de filiais.
@author:	<PERSON><PERSON>
@version: 	1.00
@since: 	24/03/2015
/*/

#include "protheus.ch"

User Function TGCTC002()

Local	aFiliais		:= {}
Local	nX
Static cFiliais

	cFiliais := " "

	aFiliais := AdmGetFil()
	
	For nX := 1 to len(aFiliais)
	
		cFiliais += aFiliais[nX] + "; "
		
	Next nX
	
Return .T.

User function RetItem4()

Return cFiliais   