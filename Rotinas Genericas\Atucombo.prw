#INCLUDE "TOTVS.CH"

//-------------------------------------------------------------------
/*/{Protheus.doc} AtuComBo
Atualizar campo EA_CPVPGT

PROJETO TDI UPGRADE 12.1.25
<AUTHOR>
@since 24/01/2020
@version P12.1.25
/*/
//-------------------------------------------------------------------

User Function AtuComBo()

    Local cQry      := ""
    Local cComp     := ""

    cQry += " select PSC_FILIAL,PSC_NUMBOR,PSC_PREFIX,PSC_NUM,PSC_PARCEL,PSC_TIPO,PSC_FORNEC,PSC_LOJA, "
    cQry += " PSC_VERSAO, EA_CPVPGT "
    cQry += " FROM "+RetSqlName("PSC")
    cQry += " inner join "+RetSqlName("SEA")+" on ea_filial=psc_filial and ea_numbor=psc_numbor and "
    cQry += " ea_versao=psc_versao and ea_prefixo=PSC_PREFIX and ea_num=PSC_NUM and ea_parcela=PSC_PARCEL "
    cQry += " and ea_tipo=PSC_TIPO and ea_fornece=PSC_FORNEC and ea_loja=PSC_LOJA "
    cQry += " wHERE ea_databor>='20200401' and ea_cpvpgt<>psc_cpvpgt and "+RetSqlName("PSC")+".d_e_l_e_T_=' ' "
    cQry += " and "+RetSqlName("SEA")+".d_e_l_e_T_=' ' "
	
    dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),"TRB",.T.,.T.)
/*
    TRB->(dbGotop())

    While TRB->(!EOF())

        cComp  := Iif(Empty(Alltrim(TRB->ED_CPVPGT)),"2",TRB->ED_CPVPGT)
        SEA->(dbSetOrder(1))
        If SEA->(dbSeek(TRB->(EA_FILIAL+EA_NUMBOR+EA_PREFIXO+EA_NUM+EA_PARCELA+EA_TIPO+EA_FORNECE+EA_LOJA+EA_VERSAO)))
            RecLock("SEA",.F.)
            SEA->EA_CPVPGT := cComp
            MsUnlock()
        EndIf
        TRB->(dbSkip())
    End
*/   
    TRB->(dbGotop())

    While TRB->(!EOF())

        cComp  := Iif(Empty(Alltrim(TRB->EA_CPVPGT)),"2",TRB->EA_CPVPGT)
        PSC->(dbSetOrder(1))
        If PSC->(dbSeek(TRB->(PSC_FILIAL+PSC_NUMBOR+PSC_PREFIXO+PSC_NUM+PSC_PARCELA+PSC_TIPO+PSC_FORNECE+PSC_LOJA+PSC_VERSAO)))
            RecLock("PSC",.F.)
            PSC->PSC_CPVPGT := cComp
            MsUnlock()
        EndIf
        TRB->(dbSkip())
    End
    TRB->(dbCloseArea())
Return()

