/*/
{PROJETO} - CTBPFO7.PRW
@desc:		Posicionamento da FO7 no momento do offline do financeiro   
@author:	<PERSON><PERSON>
@version: 	1.00
@since: 	17/11/2016
/*/


#Include 'Protheus.ch'

User Function CONTVGM(nCod)

Local cRet := ""
Local aAreaFO7 := FO7->(GetArea())

	If nCod == 1
	
		DbSelectArea("FO7")
		FO7->(DbSetOrder(2))
	
		If FO7->(DbSeek(FLF->(FLF_FILIAL+FLF_TIPO+FLF_PRESTA)))
			While FO7->FO7_FILIAL == FLF->FLF_FILIAL .And. FO7->FO7_PRESTA == FLF->FLF_PRESTA .And. FO7->FO7_PREFIX == "VGM"
				FO7->(DbSkip())
			End
	
			IF FO7->FO7_RECPAG == "R" .AND. FLF->FLF_NACION== "1"
				cRet := cValToChar(FLF->(FLF_TADIA1-FLF_TVLRE1))
			EndIf
			
		EndIf
		
		If Empty(cRet)
			cValToChar(cRet)
		EndIf
		
	EndIf
	
	If nCod == 2
	
		DbSelectArea("FO7")
		FO7->(DbSetOrder(2))
	
		If FO7->(DbSeek(FLF->(FLF_FILIAL+FLF_TIPO+FLF_PRESTA)))
			While FO7->FO7_FILIAL == FLF->FLF_FILIAL .And. FO7->FO7_PRESTA == FLF->FLF_PRESTA .And. FO7->FO7_PREFIX == "VGM"
				FO7->(DbSkip())
			End
			
			cRet := "APROP DP  "+FO7->FO7_PREFIX+" "+FO7->FO7_TITULO+" "+FLD->FLD_PREFIX+" "+FLD->FLD_TITULO
		EndIf
		
	EndIf                                                                                                                

FO7->(RestArea(aAreaFO7))
aSize(aAreaFO7,0)

Return(cRet)