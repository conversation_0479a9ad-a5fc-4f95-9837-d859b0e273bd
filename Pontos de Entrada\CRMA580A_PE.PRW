#include 'totvs.ch'

/*/{Protheus.doc} CRMA580
Ponto de Entrada - Cadastro de Agrupadores
<AUTHOR>
@since 10/12/2021
@type function
/*/
user function CRMA580A

Local lRet      := .t.
Local cAlias    := alias()
Local cTabela   := getmv("TI_TABTABF",,"CRM100")

If ParamIxb <> NIL .and. ParamIXB[1]:GetModel():GetID()=="CRMA580A"

    If Paramixb[2] == "MODELVLDACTIVE"

        dbSelectArea("ZX5")
        dbSetOrder(1)
        dbSeek(xFilial("ZX5")+cTabela)
        Do While !(ZX5->(EoF())) .and. ZX5->(ZX5_FILIAL+ZX5_TABELA)==xFilial("ZX5")+cTabela
            If !Empty(ZX5->ZX5_CHAVE) .and. AliasInDic(Alltrim(ZX5->ZX5_CHAVE))
                dbSelectArea(Alltrim(ZX5->ZX5_CHAVE))
            EndIf
            ZX5->(dbSkip())
        EndDo
        dbSelectArea(cAlias)
    EndIf

EndIF

Return lRet
