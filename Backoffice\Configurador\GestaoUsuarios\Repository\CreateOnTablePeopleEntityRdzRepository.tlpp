#include  "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Repository.CreateOnTablePeopleEntityRdzRepository
/*/{Protheus.doc} CreateOnTablePeopleEntityRdz
This class has the purpose of verify if exist a register on Table
Rdz if do not exist, so a new register is created. 
@type class
@version  1.0
<AUTHOR>
@since 10/25/2024
/*/
Class   CreateOnTablePeopleEntityRdz
	Static  Method  execUsingActualParticipantPositioned(cCodeOnRd0 As  Character)
EndClass

/*/{Protheus.doc} CreateOnTablePeopleEntityRdz::execUsingActualParticipantPositioned
This method create a new register on Table People x Entity when do not exist a valid register yet
On this table the order 1 is: RDZ_FILIAL+RDZ_EMPENT+RDZ_FILENT+RDZ_ENTIDA+RDZ_CODENT+RDZ_CODRD0
@type method
@version  1.0  
<AUTHOR>
@since 10/25/2024
@param cCodeOnRd0, character, This parameter is the Code of participant on Table Rd0
@return Variant, Return Nil
/*/
Method  execUsingActualParticipantPositioned(cCodeOnRd0 As  Character)    Class   CreateOnTablePeopleEntityRdz
	RDZ->( dbSetOrder(1) )
	If RDZ->( ! dbSeek( xFilial("RDZ") + RD0->RD0_EMPATU + Pad( RD0->RD0_FILATU, Len(RDZ->RDZ_FILENT ) ) + "RD0" + Space( Len (RDZ->RDZ_CODENT ) ) + Pad( cCodeOnRd0, Len( RDZ->RDZ_CODRD0 ) ) ) )
		RecLock( "RDZ", .T. )
		RDZ->RDZ_FILIAL := xFilial("RDZ")
		RDZ->RDZ_CODRD0	:= cCodeOnRd0
		RDZ->RDZ_EMPENT	:= RD0->RD0_EMPATU
		RDZ->RDZ_FILENT	:= RD0->RD0_FILATU
		RDZ->RDZ_ENTIDA	:= "RD0"
		RDZ->( MsUnlock() )
	EndIf

Return Nil

