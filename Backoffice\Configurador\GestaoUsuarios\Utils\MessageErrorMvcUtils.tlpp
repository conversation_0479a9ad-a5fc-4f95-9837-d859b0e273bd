#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Utils.MessageErrorMvcUtils

/*/{Protheus.doc} MessageErrorMvc
This class aims to extract the error from the income object
@type class
@version  1.0
<AUTHOR>
@since 10/31/2024
/*/
Class MessageErrorMvc
    static method getError(oObjeto as Object, cError as character) as Character
EndClass

/*/{Protheus.doc} MessageErrorMvc::getError
This static method aims to extract the value of the error from the income object
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@param oObjeto, object, The object of interest 
@param cError, character, The variable of error
@return character, The variable of error with the error actualized
/*/
Method getError(oObjeto as Object, cError as character) as Character class MessageErrorMvc
    Local aError  := oObjeto:GetErrorMessage() as Array
    Local CRLF      :=  Chr(13)+Chr(10) As Character

    If ! Empty( cError )
        cError += CRLF 
    Endif

    cError += AllTrim( StrTran( aError[6], Chr( 13 ) + Chr( 10 ), "" ) )

    If ! Empty( AllTrim( StrTran( aError[7], Chr( 13 ) + Chr( 10 ), "" ) ) )
        cError += " - " + AllTrim( StrTran( aError[7], Chr( 13 ) + Chr( 10 ), "" ) )
    Endif    
Return cError
