﻿
/*
ÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœ
Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±
Â±Â±Ã‰Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‘Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‹Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‘Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‹Ã�Ã�Ã�Ã�Ã�Ã�Ã‘Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â»Â±Â±
Â±Â±ÂºPrograma  Â³F240FIL   ÂºAutor  Â³TI1971-<PERSON>ga Âº Data Â³  11/04/05   ÂºÂ±Â±
Â±Â±ÃŒÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã˜Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�ÃŠÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�ÃŠÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â¹Â±Â±
Â±Â±ÂºDesc.     Â³                                                            ÂºÂ±Â±
Â±Â±Âº          Â³                                                            ÂºÂ±Â±
Â±Â±ÃŒÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã˜Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â¹Â±Â±
Â±Â±ÂºUso       Â³ AP                                                        ÂºÂ±Â±
Â±Â±ÃˆÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â¼Â±Â±
Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±
ÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸ
*/

#Include "Protheus.ch"

User Function F240Fil()
	Local cPerg	:="BORDER"

	AjustaSX1()
	Pergunte(cPerg,.t.)

	cFil:="E2_FORBCO  >= '"+ MV_PAR01+ "' .and. E2_FORBCO <= '"  + MV_PAR02+"' .And. "
	cFil+="E2_NATUREZ >= '"+ MV_PAR03+ "' .and. E2_NATUREZ <= '" + MV_PAR04+"' .And. "
	cFil+="E2_PREFIXO >= '"+ MV_PAR05+ "' .and. E2_PREFIXO <= '" + MV_PAR06+"' .And. "
	cFil+="E2_NUM >= '"    + MV_PAR07+ "' .and. E2_NUM <= '"     + MV_PAR08+"' .And. "
	cFil+="E2_MSBLQL <> '1' .And. "
//  28.03.2018
// InclusÃ£o do filtro por forma de pagamento 
	If cModPgto = "99"
		cFil+=" ( E2_FORMPAG = '  ' .or. E2_FORMPAG = '99' )
	Else
		cFil+="E2_FORMPAG = '" + cModPgto + "'"
	EndIf

	pergunte("F240BR",.F.)


Return(cFil)

/*
ÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœÃœ
Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±
Â±Â±Ã‰Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‘Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‹Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‘Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã‹Ã�Ã�Ã�Ã�Ã�Ã�Ã‘Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â»Â±Â±
Â±Â±ÂºPrograma  Â³F240FIL   ÂºAutor  Â³Microsiga           Âº Data Â³  11/10/05   ÂºÂ±Â±
Â±Â±ÃŒÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã˜Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�ÃŠÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�ÃŠÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â¹Â±Â±
Â±Â±ÂºDesc.     Â³                                                            ÂºÂ±Â±
Â±Â±Âº          Â³                                                            ÂºÂ±Â±
Â±Â±ÃŒÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã˜Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â¹Â±Â±
Â±Â±ÂºUso       Â³ AP                                                        ÂºÂ±Â±
Â±Â±ÃˆÃ�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Ã�Â¼Â±Â±
Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±Â±
ÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸÃŸ
*/

Static Function AjustaSX1()

	PutSx1(	"BORDER", "01", "Bco Fornecedor de ?","","","mv_ch1","C",3,0,0,"G","","","","","MV_PAR01","","","","","","","","","","","","","","","","","","","","","","","","","","","","",,,)
	PutSx1(	"BORDER", "02", "Bco Fornecedor ate   ?","","","mv_ch2","C",3,0,0,"G","","","","","MV_PAR02","","","","","","","","","","","","","","","","","","","","","","","","","","","","",,,)
	PutSx1(	"BORDER", "03", "Natureza de ?","","","mv_ch3","C",10,0,0,"G","","","","","MV_PAR03","","","","","","","","","","","","","","","","","","","","","","","","","","","","",,,)
	PutSx1(	"BORDER", "04", "Natureza ate ?","","","mv_ch4","C",10,0,0,"G","","","","","MV_PAR04","","","","","","","","","","","","","","","","","","","","","","","","","","","","",,,)
	PutSx1(	"BORDER", "05", "Prefixo de ?","","","mv_ch5","C",3,0,0,"G","","","","","MV_PAR05","","","","","","","","","","","","","","","","","","","","","","","","","","","","",,,)
	PutSx1(	"BORDER", "06", "Prefixo ate ?","","","mv_ch6","C",3,0,0,"G","","","","","MV_PAR06","","","","","","","","","","","","","","","","","","","","","","","","","","","","",,,)
	PutSx1(	"BORDER", "07", "Titulo de ?","","","mv_ch7","C",9,0,0,"G","","","","","MV_PAR07","","","","","","","","","","","","","","","","","","","","","","","","","","","","",,,)
	PutSx1(	"BORDER", "08", "Titulo ate ?","","","mv_ch8","C",9,0,0,"G","","","","","MV_PAR08","","","","","","","","","","","","","","","","","","","","","","","","","","","","",,,)

Return
