#INCLUDE 'TOTVS.CH'

/*/{Protheus.doc} TCRMV066
    (long_description)
    @type  Function
    <AUTHOR>
    @since 11/08/2022
    @version 1.0
    @param cUnidSrv, string, Unidade de serviços
    @param cMotSrv, string, Unidade de serviços
    @return lRet, boolean, .T. para calcular juros, .F. para não calcular
    @example
    (examples)
    @see (links_or_references)
    /*/
User Function TCRMV066(cUnidSrv, cMotSrv, cADYTabela, cCodAgrVld, cModalVld)
    Local lRet          := .T.
	Local aArea 		:= GetArea()
	Local aAreaPE1  	:= PE1->(GetArea())
    Local cMotJuros     := ''
    Local cZX5TabMot    := "SRV041"    //TABELA DA ZX5 COM OS MOTIVOS QUE DEVEM TER JUROS
    Local lEhServico    := U_TCRMTabP(cADYTabela, cCodAgrVld, GetMV("TI_NIVSERV",,"0002;0147;0091"), cModalVld)

    If lEhServico .AND. !Empty(cUnidSrv) .AND. !Empty(cMotSrv)
        //SE A UNIDADE DE SERVIÇO Eh DE FRANQUIA NAO CALCULA
        DbSelectArea("PE1")
        PE1->(DbSetOrder(1))
        If PE1->(DbSeek(xFilial("PE1")+cUnidSrv)) .AND. PE1->PE1_PROPRI == '2'
            lRet := .F.
        EndIf

        //VALIDA SE O MOTIVO PERMITE JUROS
        If lRet
            cMotJuros := ''
                
            DbSelectArea("ZX5")
            ZX5->(DbSetOrder(1))
            If ZX5->(DbSeek(xFilial("ZX5")+cZX5TabMot))
                While ZX5->( !Eof() ) .AND. ZX5->(ZX5_FILIAL + ZX5_TABELA) == xFilial("ZX5")+cZX5TabMot
                    cMotJuros += AllTrim(ZX5->ZX5_CHAVE) + ";"

                    ZX5->(DbSkip())
                EndDo
            EndIf

            If !(cMotSrv $ cMotJuros)
                lRet := .F.
            EndIf
        EndIf
    EndIf
    
    RestArea(aArea)
	RestArea(aAreaPE1)
Return lRet
