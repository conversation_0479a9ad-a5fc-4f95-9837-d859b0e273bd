/*/
{PROJETO} - MT103QPC.PRW
@desc:		Ponto de entrada para manipular query que busca os pedidos de compra.   
@author:	<PERSON><PERSON>
@version: 	1.00
@since: 	14/12/2016
/*/

#include "protheus.ch"

User Function MT103QPC()

Local cQryRet := ""

	cQryRet := " SELECT R_E_C_N_O_ RECSC7 FROM " + RetSqlName("SC7") + " SC7 " 
	cQryRet += " WHERE  C7_FILENT = '" + xFilial("SC7") + "' AND C7_FORNECE = '" + CA100FOR + "' AND (C7_QUANT-C7_QUJE-C7_QTDACLA) > 0 "
	cQryRet += " AND C7_RESIDUO = ' ' AND C7_TPOP <> 'P' AND (C7_CONAPRO = 'L' OR C7_CONAPRO = ' ') AND C7_LOJA = '" + CLOJA + "' AND SC7.D_E_L_E_T_ = ' ' "
	cQryRet += " ORDER BY  C7_FILENT,C7_FORNECE,C7_LOJA,C7_NUM "

Return(cQryRet)