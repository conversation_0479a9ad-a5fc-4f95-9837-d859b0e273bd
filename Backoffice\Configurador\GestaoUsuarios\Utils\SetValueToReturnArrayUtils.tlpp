#include    "tlpp-core.th"

namespace   Backoffice.Configurador.Gestao_Usuarios.Utils.SetValueToReturnArrayUtils

/*/{Protheus.doc} SetValueToReturnArray
This method aims to set the value of the array on a object
@type class
@version  1.0
<AUTHOR>
@since 11/8/2024
/*/
Class   SetValueToReturnArray
    Static  Method  execute(oValues As  Object, lLogicalResult   As  Logical, cMessage   As  Variant)  As  Variant
EndClass

/*/{Protheus.doc} SetValueToReturnArray::execute
This method aims to execute the process and set the value of the array
@type method
@version  1.0
<AUTHOR>
@since 11/8/2024
@param oValues, object, The income object with all data
@param lLogicalResult, logical, The logical state of the response
@param cMessage, variant, The message of the response
@return object, The return will be a Nil Value
/*/
Method  execute(oValues As  Object, lLogicalResult   As  Logical, cMessage   As  Variant)  As  Variant  Class   SetValueToReturnArray
    oValues:aRetProc   := {lLogicalResult, cMessage}
Return  Nil




