#include 'protheus.ch'
#include 'parmtype.ch'

User Function TCRMX143(cProduto)
    Local llRet := .F.

    dbSelectArea("PIR")
    PIR->(dbSetOrder(1))
    If PIR->(dbSeek(xFilial("PIR")+PADR(c<PERSON>rod<PERSON>,TAMSX3("PIR_PRODUT")[1]) ))
        If PIR->PIR_MODNEG == '4' // Intera 2018
            llRet := .T.
        EndIF
    EndIF
    PIR->(dbCloseArea())
Return(llRet)

User Function CRMX143A(cCliente, cLoja)
    Local nlRet     := 0
    Local clAlias	:= GetNextAlias()
	Local clSelect	:= ""
	Local clInner	:= ""
	Local clWhere	:= ""
	Local clGroup	:= ""
	Local clOrder	:= ""

    clSelect := "SUM(CNB_VLTOT) TOTAL"

    clInner += " INNER JOIN " + RetSqlName("PIR") + " PIR "
	clInner += " ON PIR.PIR_FILIAL = '" + xFilial("PIR") + "' "
	clInner += " AND PIR.PIR_PRODUT = CNB.CNB_PRODUT "
	clInner += " AND PIR.PIR_MODNEG = '4' " // identifica os produtos Cloud Intera 2018
	clInner += " AND PIR.D_E_L_E_T_ = ' ' "

    U_CRCLD103(@clAlias,cCliente,cLoja,clSelect,clInner,clWhere,clGroup,clOrder)
	
	If (clAlias)->(!EOF()) .and. (clAlias)->TOTAL > 0 
	
        nlRet := (clAlias)->TOTAL
	
	EndIF
	(clAlias)->(dbCloseArea())
Return(nlRet)