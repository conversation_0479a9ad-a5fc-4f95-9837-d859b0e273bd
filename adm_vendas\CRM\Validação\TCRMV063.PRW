#include 'protheus.ch'
#include 'fwmvcdef.ch'
#include 'tcrmv063.ch'

/*/{Protheus.doc} TCRMV063
Valida Tp.Ap.Fatura / Tipo de <PERSON>ura
<AUTHOR>
@since 01/06/2022
@version undefined

@type function
/*/
User Function TCRMV063(cCampo)
Local lRet		:= .t.
Local cXTPSRV 	:= FwFldGet("ADY_XTPSRV")
Local cMICRSRV 			:= GetMV("TI_MICRSRV",,"000007")

if cCampo =="ADY_XAPFTP"
    if cXTPSRV ==cMICRSRV .and. FwFldGet("ADY_XAPFTP") <> "1"
        help(,,"TCRMV063",,STR0001,1,0) 
        lRet	:= .f.
    Endif
ENDIF

if cCampo =="ADY_XTPFAT"
    if cXTPSRV ==cMICRSRV .and. FwFldGet("ADY_XTPFAT") <> "2"
        help(,,"TCRMV063",,STR0002,1,0) 
        lRet	:= .f.
    Endif
ENDIF



Return(lRet)
