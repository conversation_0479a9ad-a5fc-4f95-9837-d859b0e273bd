#INCLUDE 'PROTHEUS.CH'

/*/{Protheus.doc} User Function nomeFunction
    (long_description)
    @type  Function
    <AUTHOR>
    @since 14/08/2023
    @version version
    @param param_name, param_type, param_descr
    @return return_var, return_type, return_description
    @example
    (examples)
    @see (links_or_references)
    /*/
User Function TRGA070(cFilWS)

Local lRet      := .T.
Local aEmpresas := {}
Local nPos      := 0

aEmpresas := FWLoadSM0( .f. , .f. )

nPos := Ascan(aEmpresas,{|x| Alltrim(x[2]) == Alltrim(cFilWS)})

If nPos == 0
    lRet := .F.
EndIf 

Return(lRet)
