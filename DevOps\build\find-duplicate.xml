<?xml version="1.0" encoding="UTF-8"?>
<project name="find-dup" basedir=".">

    <!-- <PERSON><PERSON><PERSON> as variaveis de ambiente -->
	<property environment="env" />

    <property name="src.dir" value="src"/>
    <property name="classes.dir" value="classes"/>

    <target name="find-dup" description="Find duplicate files" >
        <taskdef name="finddupfiles" classname="com.totvs.task.FindDuplicatedFiles" />
        <finddupfiles>
            <p ath="${env.BUILD_SOURCESDIRECTORY}" filterSuffixes="ch,prw" />
        </finddupfiles>
    </target>
</project>