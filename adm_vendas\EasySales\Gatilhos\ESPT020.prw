#INCLUDE 'TOTVS.CH'

/*/{Protheus.doc} ESPT020
Função para gatilhar dados do parâmetro
@obs    Função chamado no gatilho do campo PVL_VAR seq. 001

@return Nil
/*/
User Function ESPT020(cCampo)
    Local oModel     := FWModelActive()
    Local cParam     := oModel:GetValue("PVLMASTER", "PVL_VAR")
    Local cDir       := GetMV("FS_UPDSYS")
    Local cRet       := ""
    
    Default cCampo := ""

    If Empty(cCampo)
        Return cRet
    EndIf
    
    If Select("SX6PAR") > 0
        SX6PAR->(DbCloseArea())
    EndIf
    
    //DbUseArea(.T., "CTREECDX", cDir + "sx6.dtc", "SX6PAR", .T., .F.)
    USE (cDir + "sx6.dtc") ALIAS ("SX6PAR") SHARED NEW VIA ("CTREECDX")
    DbSetIndex(cDir + "sx6.cdx")
    DbSetOrder(1)
    
    If SX6PAR->(DbSeek(xFilial("SX6") + cParam))
        Do Case 
            Case cCampo == "PVL_CONTEU"
                cRet := SX6PAR->&("X6_CONTEUD")
            Case cCampo == "PVL_CONSPA"
                cRet := SX6PAR->&("X6_CONTSPA")
            Case cCampo == "PVL_CONENG"
                cRet := SX6PAR->&("X6_CONTENG")
            Case cCampo == "PVL_DESVAR"
                cRet := SX6PAR->&("X6_DESCRIC")
            Case cCampo == "PVL_DESC1"
                cRet := SX6PAR->&("X6_DESC1")
            Case cCampo == "PVL_DESC2"
                cRet := SX6PAR->&("X6_DESC2")
            Case cCampo == "PVL_DSCSPA"
                cRet := SX6PAR->&("X6_DSCSPA")
            Case cCampo == "PVL_DCSPA1"
                cRet := SX6PAR->&("X6_DSCSPA1")
            Case cCampo == "PVL_DCSPA2"
                cRet := SX6PAR->&("X6_DSCSPA2")
            Case cCampo == "PVL_DSCENG"
                cRet := SX6PAR->&("X6_DSCENG")
            Case cCampo == "PVL_DCENG1"
                cRet := SX6PAR->&("X6_DSCENG1")
            Case cCampo == "PVL_DCENG2"
                cRet := SX6PAR->&("X6_DSCENG2")
        End Case
    EndIf
    
Return cRet

