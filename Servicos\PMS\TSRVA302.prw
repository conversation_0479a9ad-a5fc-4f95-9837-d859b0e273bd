#include "protheus.ch" 

/*/{Protheus.doc} TSRVA302 
Retorna a quantidade da coluna customizada Qtd. Orcada na WBS do PMS
<AUTHOR> 
@since 02/12/2015
@version 1.0 
@return numeric, Qtd orcada na frente. 
/*/ 
User Function TSRVA302()
Local nQtdRet		:= 0

If !Empty(AFC->AFC_PROJET) 
	If Empty(AFC->AFC_EDTPAI)
		PE5->(DbSetOrder(1))
		If PE5->(MsSeek(FwxFilial("PE5")+AFC->AFC_PROJET))
			nQtdRet := PE5->PE5_QTDORC
		EndIf
	ElseIf Alltrim(AFC->AFC_EDTPAI) == AF8->AF8_PROJET
		PE8->( DbSetOrder(5) )
		If PE8->( MsSeek(FwxFilial("PE8")+AFC->(AFC_PROJET+AFC_EDT)) )
			nQtdRet := PE8->PE8_QTDHRS
		EndIf
	EndIf
EndIf

Return nQtdRet