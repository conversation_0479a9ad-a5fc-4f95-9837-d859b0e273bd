const fs = require("fs");

fs.readFile(
  `devops/gradle/sonar_remover/data/issuesGoogleChat.log`,
  (err, data) => {
    if (err) throw err;
    let files = JSON.parse(data);

    if (JSON.stringify(files).toUpperCase().includes(".PRW")) {
      console.log(
        `Arquivos que possuem BUG novo:\n ${JSON.stringify(files).replaceAll(
          "},",
          "},\n"
        )}`
      );
      throw new Error();
    } else {
      // console.log( typeof files)
      console.log(`Log: ${JSON.stringify(files).replaceAll("},", "},\n")}`);
    }
  }
);
