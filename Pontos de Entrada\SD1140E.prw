#Include 'Protheus.ch'


User Function SD1140E()
Local aArea    := GetArea()
Local aAreaPFZ := PFZ->( GetArea() )

DbSelectArea("PFZ")
PFZ->( DbSetOrder(2) )	//PFZ_FILIAL+PFZ_SERNCC+PFZ_NUMNCC+PFZ_ITNCC+PFZ_SERORI+PFZ_NFORI+PFZ_ITEMOR

If PFZ->( DbSeek(xFilial("PFZ")+SD1->(D1_SERIE+D1_DOC+D1_ITEM+D1_SERIORI+D1_NFORI+D1_ITEMORI)) )
	RecLock("PFZ", .F.)
	PFZ->(DbDelete())	
	PFZ->( MsUnlock() )
EndIf

RestArea( aAreaPFZ )
RestArea( aArea )

Return

