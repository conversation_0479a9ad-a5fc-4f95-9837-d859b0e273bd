#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRCV000     
	Callback de autenticacao receiv customizado
	@type  Function
	<AUTHOR> Menabue Lima
	@since 26/07/2021
	@version 1.0
	/*/
User Function TIVDU000(cRespBody)
	Local oReceptor:=TIINTERCEPTOR():New()
	Local cToken   :=""
	cToken:= oReceptor:SearchJsonKey(cRespBody,"token")
	Reclock("P36",.F.)
	P36->P36_HEADRE := 'Authorization: Bearer ' + cToken
	P36->(MsUnlock())
Return
