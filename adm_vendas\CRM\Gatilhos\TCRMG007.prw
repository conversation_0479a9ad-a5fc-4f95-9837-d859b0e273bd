#include 'protheus.ch'
#include 'parmtype.ch'

/*/{Protheus.doc} TCRMG007
Gatilho para preenchimento de campos de acordo com a conta - BlackList
<AUTHOR>
@since 12/05/2016
@version undefined
@param nTipo, numeric, descricao
@param cAlias, characters, descricao
@param cChave, characters, descricao
@type function
/*/
user function TCRMG007(nTipo,cAlias,cChave)
Local aArea		:= GetArea()
Local aAreaCTA	:= NIL
Local cRet		:= ""
Local cField	:= ""

If nTipo==1
	cField	:= Iif(cAlias=="SA1","A1_NOME","US_NOME") 
ElseIf nTipo==2
	cField	:= Iif(cAlias=="SA1","A1_CGC","US_CGC") 
ElseIf nTipo==3
	cField	:= Iif(cAlias=="SA1","A1_LOJA","US_LOJA") 
EndIf

dbSelectArea(cAlias)
aAreaCTA := GetArea()
dbSetOrder(1)
If dbSeek(xFilial(cAlias)+cChave)
	
	cRet := FieldGet(FieldPos(cField))
	
EndIf

RestArea(aAreaCTA)
RestArea(aArea)
return(cRet)