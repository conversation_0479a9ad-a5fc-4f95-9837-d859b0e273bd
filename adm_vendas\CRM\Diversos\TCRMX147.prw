#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'FWMVCDEF.CH'
#Include 'TopConn.ch'

/******************************************************************************
* {Protheus.doc} TCRMX147()													  *
* Rotina que retorna os dados do Site de Faturamento 						  *
* --------------------------------------------------------------------------- *
* <AUTHOR> MICK WILLIAM DA SILVA										  *
* @Since		: 12/04/2018												  *
* @Version    	: P12														  *
* @Return    	:											  				  *
******************************************************************************/

User Function TCRMX147(cCodEmp,cCodFil,cAgrupa,cCodNiv)

	Local aArea 	:= GetArea()
	Local aDados	:= {}
	Local cQuery	:= ""
	Local cAlsQry	:= GetNextAlias()

	
	cQuery:= "SELECT POP_CODEMP,POP_CODFIL,POP_AGRUPA,POP_CODNIV,POP_NOMVAR,CAST(POP_TEXTO AS VARCHAR(2000)) POP_TEXTO,'1' POPTIPO "
	cQuery+= " FROM "+ RetSqlName("POP") +" POP "
	cQuery+= " WHERE POP_CODEMP='"+cCodEmp+"' and POP_CODFIL='"+cCodFil+"' and POP_AGRUPA='"+cAgrupa+"' and POP_CODNIV='"+cCodNiv+"' AND POP.D_E_L_E_T_=' ' "
	cQuery+= " UNION ALL "
	cQuery+= " SELECT POP_CODEMP,POP_CODFIL,POP_AGRUPA,POP_CODNIV,POP_NOMVAR,CAST(POP_TEXTO AS VARCHAR(2000)) POP_TEXTO,'2' POPTIPO "
	cQuery+= " FROM "+ RetSqlName("POP") +" POP "
	cQuery+= " WHERE POP_CODEMP='"+cCodEmp+"' and POP_CODFIL='"+cCodFil+"' and POP_AGRUPA=' ' and POP_CODNIV=' ' AND POP.D_E_L_E_T_=' ' "
	cQuery+= " UNION ALL "
	cQuery+= " SELECT POP_CODEMP,POP_CODFIL,POP_AGRUPA,POP_CODNIV,POP_NOMVAR,CAST(POP_TEXTO AS VARCHAR(2000)) POP_TEXTO, '3' POPTIPO "
	cQuery+= " FROM "+ RetSqlName("POP") +" POP "
	cQuery+= " WHERE POP_CODEMP=' ' and POP_CODFIL=' ' and POP_AGRUPA='"+cAgrupa+"' and POP_CODNIV='"+cCodNiv+"' AND POP.D_E_L_E_T_=' ' "
	cQuery+= " UNION ALL "
	cQuery+= " SELECT POP_CODEMP,POP_CODFIL,POP_AGRUPA,POP_CODNIV,POP_NOMVAR,CAST(POP_TEXTO AS VARCHAR(2000)) POP_TEXTO, '4' POPTIPO "
	cQuery+= " FROM "+ RetSqlName("POP") +" POP "
	cQuery+= " WHERE POP_CODEMP=' ' and POP_CODFIL=' ' and POP_AGRUPA=' ' and POP_CODNIV=' ' AND POP.D_E_L_E_T_=' ' "

	TCQUERY cQuery NEW ALIAS (cAlsQry)
	
	While !(cAlsQry)->(EOF())
		
		IF  aScan(aDados, {|x| Alltrim(x[1]) == Alltrim((cAlsQry)->POP_NOMVAR)	} ) <= 0
			AADD(aDados, { Alltrim((cAlsQry)->POP_NOMVAR), (cAlsQry)->POP_TEXTO })
	 	EndIf
	 	
	 (cAlsQry)->(dbSkip())
	EndDo
	
		(cAlsQry)->(DbCloseArea())
	RestArea(aArea)

Return(aDados)