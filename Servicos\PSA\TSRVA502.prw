#Include 'Protheus.ch'


/*/{Protheus.doc} nomeFunction
    (long_description)
    @type  Function
    <AUTHOR>
    @since date
    @version version
    @param param, param_type, param_descr
    @return returno,return_type, return_description
    @example
    (examples)
    @see (links_or_references)
/*/
User Function TSRVA502()
Local lRet      := .T.
Local aArea     := GetArea()

Private cCadastro := 'Cadastro DE/PARA de Centro de Custo X Cargos'

dbSelectArea( 'PKU' )
PKU->( dbSetOrder( 1 ) )


AxCadastro( 'PKU', cCadastro )


RestArea( aArea )
Return lRet