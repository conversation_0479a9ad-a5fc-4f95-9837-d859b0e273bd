<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project name="TINDIN" basedir="." default="main">

<!--
	Opção 2
	Etapas:
	1 - Ler o arquivo D:\devops\testes\bin\appserver_7031.ini >> OK
	   a) Fazer a busca por "SourcePath=D:\TOTVS\APO\apo_master_crm\TESTE\NN" >> ok
	   b) ao encontrar expressão, criar diretório D:\TOTVS\APO\apo_master_crm\TESTE\OO (onde OO é NN mais um) >> ok
	2 - Copiar rpo para D:\TOTVS\APO\apo_master_crm\TESTE\OO >> ok
	3 - Substituir nos arquivos .ini a expressão SourcePath=D:\TOTVS\APO\apo_master_crm\TESTE\NN, localizada por D:\TOTVS\APO\apo_master_crm\TESTE\OO 
	4 - Reiniciar os serviços de rest
-->
    <!--	Propriedades 	-->
    <property environment="env" />

	<!--	Dependencias 	-->
	<taskdef resource="net/sf/antcontrib/antcontrib.properties">
		<classpath>
			<pathelement location="${env.ANT_HOME}/lib/ant-contrib-0.3.jar"/>
		</classpath>
	</taskdef>
	<taskdef resource="net/sf/antcontrib/antlib.xml" >
		<classpath>
			<pathelement location="${env.ANT_HOME}\lib\ant-contrib-1.0b3.jar"/>
		</classpath>
	</taskdef>	    

    <target name="main" description="target principal">
        
        <property name="dirApos"	value="${env.PROTHEUS_RPOS_PATH}" />
        <property name="dirApowa"	value="${env.PROTHEUS_WORKING_PATH}" />
        <property name="dirBin"	value="${env.APPSERVER_PATH}" />
        <property name="fappserver"	value="${env.APPSERVER_APP}" />
        <property name="frpoPadrao"	value="${env.PROTHEUS_FILIE_RPO_PADRAO}" />
        <property name="frpoCustom"	value="${env.PROTHEUS_FILIE_RPO_CUSTOM}" />
        <property name="dirAposQtde"	value="${env.PROTHEUS_FILIE_RPO_QTDE}" />
        <property name="charBarr"	value="\" />
        <property name="dirFontes"	value="${env.PROTHEUS_SOURCE_FONTES}" />
        <property name="dirApoPrincipal"	value="${env.PROTHEUS_RPOS_PRINCIPAL}\${dirFontes}" />
        <property name="dirBin_todos"	value="${env.APPSERVER_PATH_ALL}" />

        <!-- Lê o .ini para identificar o diretório atual com o rpo e definir o novo diretorio -->
        <antcallback target="LerArquivos" return="dirApoE, dirApoA" /> 
        
        <!-- Copia o rpo da area de trabalho para o novo diretorio que sera usado no ini -->
        <antcall target="copiaRpoEleito"> 
            <param name="fileP" value="${frpoPadrao}"/> 
            <param name="fileC" value="${frpoCustom}"/>
            <param name="dirOrigem" value="${dirApowa}\"/> 
            <param name="dirDestino" value="${dirApoPrincipal}\${dirApoE}\"/> 
        </antcall>        
        
        <!-- Atualiza a chave SourcePath para o novo diretorio -->
        <antcall target="AtualizaINIs"> 
            <param name="srcbin" value="${dirBin_todos}"/>
            <param name="srcapoAtual" value="${dirApoPrincipal}\${dirApoA}"/>  
            <param name="srcapoNovo" value="${dirApoPrincipal}\${dirApoE}"/>  
        </antcall>

    </target>

    <target name="AtualizaINIs" description="Atualiza todos os .ini que possuam a expressão informada">
		<patternset id="sources.Patchs">
			<include name="**/*.INI" />
		</patternset>

		<if>
			<available file="${srcbin}" type="dir"/>
			<then>
				<sequential>
				<for param="files" >
					<path>
						<fileset dir="${srcbin}\" casesensitive="false">
							<patternset refid="sources.Patchs" />
						</fileset>				
					</path>
					<sequential>
						<var name="patches" unset="true" />
						<basename property="patches" file="@{files}"/>
                        <echo message="- Arquivo: @{files}" level="info" />
                        <echo message="- Atual..: ${srcapoAtual}" level="info" />
                        <echo message="- Novo...: ${srcapoNovo}" level="info" />
						<replace file="@{files}"
								 value="${srcapoNovo}">
						  <replacefilter token="${srcapoAtual}"/>
						</replace>
					</sequential>
				</for>
				</sequential>
			</then>
			<else>
				<echo message="- Nao existe patchs para aplicar." level="info" />
			</else>
		</if>

    </target>

    <target name="copiaRpoEleito" description="copia o rpo para o diretorio eleito">
        <echo message="Origem.: ${dirOrigem}*.rpo" level="info"/>
        <echo message="Destino: ${dirDestino}*.rpo" level="info"/>
        <copy file="${dirOrigem}${fileP}" tofile="${dirDestino}${fileP}" overwrite="true" force="true" /> 
        <copy file="${dirOrigem}${fileC}" tofile="${dirDestino}${fileC}" overwrite="true" force="true" /> 
    </target>

    <target name="LerArquivos" description="target principal">
        <property name="dirApoA"	value="00" />
        <property name="dirApoE"	value="99" />

        <script language="javascript"> 
            var BufferedReader = Java.type('java.io.BufferedReader');
            var FileReader = Java.type('java.io.FileReader');
         
            var rpo = project.getProperty("frpo");      
            var dirAN = 0; // Diretorio Atual - numerico
            var dirAS = ''; // Diretorio Atual - string  
            var dirEN = ''; // Diretorio Elegido - numerico
            var dirES = ''; // Diretorio Elegido - string            
            var achou = false;
            var barra = project.getProperty("charBarr")
            var freader = project.getProperty("dirBin") + barra + project.getProperty("fappserver") + ".ini";
            var buscar = "SourcePath=" + project.getProperty("dirApoPrincipal") + barra;

            var buscarf = '';
            var dirRPOQtde = project.getProperty("dirAposQtde");

            <![CDATA[
                echo = TINDIN.createTask("echo");
                var strCurrentLine;

                echo.setMessage("Appserver.ini analisado: " + freader);
                echo.execute();
                echo.setMessage("Prefixo da pesquisa: " + buscar);
                echo.execute();

                objReader = new BufferedReader(new FileReader(freader));

                // Le o arquivo .ini buscando qual diretório esta sendo usando para o sourcepath
                //echo.setMessage(dirRPOQtde);
                //echo.execute();                
                while ((strCurrentLine = objReader.readLine()) != null) {
                    //echo.setMessage(strCurrentLine);
                    //echo.execute();

                    // Compara linha atual com o texto desejado
                    for ( i=dirRPOQtde; i>=1; i-- ) {
                        if (i < 10) {
                            dirAN = '0' + i.toString(); 
                        } else {
                            dirAN = i.toString(); 
                        }
                        
                        buscarf = buscar + dirAN
                        //echo.setMessage("Pesquisando...: " + buscarf);
                        //echo.execute();
                        if (strCurrentLine == buscarf) {

                            //echo.setMessage("Linha atual...: " + strCurrentLine);
                            //echo.execute();
                            //echo.setMessage("Pesquisando...: " + buscarf);
                            //echo.execute();

                            if ( (i + 1) > dirRPOQtde ) {
                                dirEN = 1;
                            } else {
                                dirEN = i + 1;
                            }
                            if (dirEN < 10) {
                                dirES = '0'+dirEN.toString();
                            } else {
                                dirES = dirEN.toString();
                            }
                            achou = true;
                            break;
                        }
                    }
                    if (achou) {
                            //echo.setMessage("DirEleito...: " + dirES);
                            //echo.execute();
                        break;
                    }
                }
            ]]>
            project.setProperty("dirApoE", dirES); 
            project.setProperty("dirApoA", dirAN); 
        </script> 
        <echo message="DirEleito: ${dirApoE}" level="info"/>  
        <echo message="DirAtual.: ${dirApoA}" level="info"/> 
    </target>
     

</project>