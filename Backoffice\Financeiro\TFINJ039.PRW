#INCLUDE 'TOTVS.CH'
#INCLUDE "TRYEXCEPTION.CH"

/*/{Protheus.doc} TFINJ039
JOB de copia e renomeia os arquivos do CNAB PIX 

<AUTHOR> Carneiro
@since 05/11/2021
@return nil
/*/

User Function TFINJ039(aParam)
Local aArea     := GetArea()
Local lRet      := .T.
Local cEmpPDD   := ""
Local cFilPDD   := ""
Local oError

If VALTYPE(aParam)!="A" .OR. len(aParam)==0   
    cEmpPDD := "00"
    cFilPDD := "00001000100"
Else
    cEmpPDD := aParam[1]
    cFilPDD := aParam[2]
EndIf    

RpcClearEnv()
RpcSetType(3)
If !RpcSetEnv(cEmpPDD,cFilPDD)
	lRet := .F.  
EndIf

If lRet
	TRYEXCEPTION
		FWMonitorMsg('Inicio: copia CNAB pix\of para \pix\qu - U_FJ39JOB')
		FJ39JOB()
		FWMonitorMsg( 'Termino: copia CNAB pix\of para \pix\qu - U_FJ39JOB')

	CATCHEXCEPTION USING oError
		FWMonitorMsg( 'Erro: U_TFINJ039 | ERRO'+oError:Description)

	ENDEXCEPTION
Else
	FWMonitorMsg( 'Erro: U_TFINJ039 | Erro ao abrir ambiente')
EndIf

RestArea(aArea)
Return

/*/{Protheus.doc} CopyArq
Copia os arquivos do CNAB PIX 

<AUTHOR> Carneiro
@since 05/11/2021
@return nil
/*/

Static Function FJ39JOB()
LOCAL aDir        := {}     
Local aDirSub     := {} 
Local nTam        := 0 
Local nTamSub     := 0 
Local ni          := 0  
Local nj          := 0  
Local lGerAuxDir  := GetMV("TI_GCNDIR",,.T.)
Local cGerAuxDir  := GetMV("TI_GCNITA",,"\EDI\ITAU\COBRANCA")
Local cDestino    := '' 
Local cOrigem     := '' 
Local cOfflnPix   := Alltrim(GetMV("TI_GCPIXOF",,'\pix\of\'))
Local cqueuePix   := Alltrim(GetMV("TI_GCPIXQU",,'\pix\qu\'))
Local cRecpPix    := Alltrim(GetMV("TI_GCPIXRE",,'\RECEPCAO\PIX\'))
Local cRbkpPix    := Alltrim(GetMV("TI_GCPIXBP",,'\RECEPCAO\PIX\BKP\'))
Local cFilename   := ""
Local nPos 		  := 0
Local nPosA 	  := 0
Local nPosD 	  := 0
Local cExt 		  := ""
Local cTrocaNo 	  := ""
Local cBkpNo 	  := ""
Local cNewFile	  := ""

If lGerAuxDir
	aDir := DIRECTORY(cGerAuxDir  + "\*.*" , "D")  
	nTam := Len(aDir) 
	For ni := 1  to nTam
		If !Empty(aDir[ni][1]) 
			
			cOrigem  := cGerAuxDir + "\" + Alltrim(aDir[ni][1]) + cOfflnPix 
			cDestino := cGerAuxDir + "\" + Alltrim(aDir[ni][1]) + cqueuePix

			aDirSub := DIRECTORY( cOrigem + "*.*" , "") 
			nTamSub := Len(aDirSub) 
			For nj := 1  to nTamSub
				If __CopyFile(cOrigem + aDirSub[nj][1] ,cDestino + aDirSub[nj][1], , ,.F.)
					fErase(cOrigem + aDirSub[nj][1])
				EndIf 
			Next

			//Troca extensao do nome de retorno no aquivo ex - 01 para 00
			cTrocaNo := cGerAuxDir + "\" + Alltrim(aDir[ni][1]) + cRecpPix 
			cBkpNo := cGerAuxDir + "\" + Alltrim(aDir[ni][1]) + cRbkpPix 

			aDirSub := DIRECTORY( cTrocaNo + "*.*" , "") 
			nTamSub := Len(aDirSub) 
			For nj := 1  to nTamSub
				cFilename := aDirSub[nj][1]
				nPos := AT(".",cFilename) 
				nPosA := nPos+1 
				nPosD := nPos-1 
				cExt := Subs(cFilename,nPosA)
				If cExt > "00"			
					__CopyFile(cTrocaNo + cFilename ,cBkpNo + cFilename , , ,.F.)	//bkp					
					cNewFile := Subs(cFilename,1,nPosD)+"_"+cExt +".00"
					If __CopyFile(cTrocaNo + cFilename , cTrocaNo + cNewFile , , ,.F.) //Rename					 
						fErase(cTrocaNo + cFilename)
					EndIf					
				EndIf
			Next
		Endif
	Next
Endif

RETURN
