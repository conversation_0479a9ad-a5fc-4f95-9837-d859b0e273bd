#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRCV000     
	Callback de autenticacao receiv customizado
	@type  Function
	<AUTHOR> Menabue Lima
	@since 26/07/2021
	@version 1.0
	/*/
User Function TIRCV000(cRespBody)
	Local oReceptor:=TIINTERCEPTOR():New()
	Local cToken   :=""
	Local cExpire  := ""	
	cToken:= oReceptor:SearchJsonKey(cRespBody,"access_token")
	cExpire:= oReceptor:SearchJsonKey(cRespBody,"expires_in")
	Reclock("P36",.F.)
	P36->P36_HEADRE := 'Authorization: Bearer ' + cToken+ '|Content-Type: multipart/form-data; charset=utf-8'
	P36->P36_EXPIRE := strtran(strtran(cExpire,":")," ") //Grava a data+time de validade do token
	P36->(MsUnlock())
Return
