#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"
#INCLUDE "TRYEXCEPTION.CH"
#define CRLF Chr(13) + Chr(10)
/*/{Protheus.doc} User Function TIRVF003
	Funcao Callback de cadastro de elegiveis
	@type  Function
	<AUTHOR> Menabue Lima
	@since 17/08/2023
	@version 12.1.33
 
	/*/
User Function TIRVF003(nPar)
	Local oJson         := JsonObject():new()
	Local oBody
	Local lFalhou       := .F.
	Local cFalha        := ""
	Local aLancam		:= {}
	Local oResponse 	:= JsonObject():new()
	Default nPar        := 0
	Private cCodReq     := ""

	if(nPar==0)
		oBody		:= oJson:FromJson(PARAMIXB[2])
		cCodReq		:= PARAMIXB[1]
	Else
		DBSELECTAREA( "P37" )
		P37->(DBSETORDER(1))
		P37->(DBGOTO(nPar))//14189199
		cJson:=getJsonTst()
		//oBody:= oJson:FromJson(P37->P37_BODY)
		oBody:= oJson:FromJson(cJson)
	EndIF
	oResponse["status" ] := 200
	//Desmarca o registro para evitar sujeira
	P37->(RecLock("P37",.F.))
	P37->P37_STATUS := "F"
	P37->P37_TIMERE := TIME()
	P37->P37_BODYRP := '{ "mensagem":"Preparando pra processamento"}'
	P37->(MsUnLock())
	__cUserID := "000000"
	If VALTYPE(oBody) == 'U'
		aLancam := oJson:GetJsonObject("items")
		If ValType(aLancam) == "A"
			If Len(aLancam) > 0
				cFalha :=SaveEle(aLancam)
				lFalhou:=!Empty(Alltrim(cFalha))
				P37->(RecLock("P37",.F.))
				If lFalhou
					P37->P37_STATUS := "5"
					oResponse["status" ]  := 500
					oResponse["mensagem" ]:=cFalha
					P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
					P37->P37_TIMERP:= TIME()
				Else
					P37->P37_STATUS := "2"
					P37->P37_BODYRP := '{"status":200, "mensagem":"Registros incluidos com sucesso"}'
					P37->P37_TIMERP:= TIME()
				EndIf
				P37->(MsUnLock())
				oResponse := Nil
				FreeObj(oResponse)
			Else
				P37->(RecLock("P37",.F.))
				oResponse["status" ]   := 500
				oResponse["mensagem" ] := "no items"
				P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
				P37->P37_STATUS := "3"
				P37->P37_TIMERP := TIME()
				P37->(MsUnLock())
			EndIF
		Else
			P37->(RecLock("P37",.F.))
			oResponse["status" ] := 500
			oResponse["mensagem" ] := "items is not Array"
			P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
			P37->P37_STATUS := "3"
			P37->P37_TIMERP:= TIME()
			P37->(MsUnLock())
		EndIF
	Else
		P37->(RecLock("P37",.F.))
		oResponse["status" ] := 500
		oResponse["mensagem" ] := "Body is Empty"
		P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
		P37->P37_STATUS := "3"
		P37->P37_TIMERP:= TIME()
		P37->(MsUnLock())
	EndIF
Return

/*/{Protheus.doc} SaveEle
	Salva os lancamentos Contabeis ""
	@type   Function
	<AUTHOR> Menabue Lima
	@since 29/08/2022
	@version 12.1.33
 
/*/
Static Function SaveEle(aDados)
	Local nI
	Local aArea         := GetArea()
	Local aItens        := {}
	Local oError
	Local cError		:= ""
	Local cMatricul 	:= ""
	Local cCodArea 		:= ""
	Local cDescAre 		:= ""
	Local cCodMArea 	:= ""
	Local cDescMAre 	:= ""
	Local cCodSbAr 		:= ""
	Local cDesSbAr 		:= ""
	Local cCodCarg 		:= ""
	Local cDesCarg 		:= ""
	Local cCodFunc 		:= ""
	Local cDesFunc 		:= ""
	Local cCpf 			:= ""
	Local cEmail 		:= ""
	Local cVend 		:= ""
	Local dIni 			:= ""
	Local dFim 			:= ""
	Local cBlql			:= ""
	Local oActio 		:= TIRVACTIO():New()
	Local lValid		:= .F.
	Local nJ			:= 0
	Default cEmp 		:= cEmpAnt
	Default cFil 		:= cFilAnt
	Private lMsErroAuto := .F.
	Private lMsHelpAuto := .T.
	TRYEXCEPTION

	nTot:=Len(aDados)
	For nI:= 1 to nTot
		lValid		:= .F.
		lMsErroAuto := .F.
		lMsHelpAuto := .T.
		aItens :={}
		P37->(RecLock("P37",.F.))
		P37->P37_BODYRP := '{ "mensagem":"Gravando Linhas '+cValToChar(nI)+' de '+cValToChar(nTot)+'  "}'
		P37->(MsUnLock())
		cMatricul   := aDados[nI]["matricula"]
		cCodArea 	:= aDados[nI]["codigoArea"]
		cDescAre 	:= aDados[nI]["descriArea"]
		cCodMArea 	:= aDados[nI]["codigoMacro"]
		cDescMAre 	:= aDados[nI]["descriMacro"]
		cCodSbAr 	:= aDados[nI]["codigoDivisao"]
		cDesSbAr 	:= aDados[nI]["descriDivisao"]
		cCodCarg 	:= aDados[nI]["codigoCargo"]
		cDesCarg 	:= aDados[nI]["descriCargo"]
		cCodFunc 	:= aDados[nI]["codigoFuncao"]
		cDesFunc 	:= aDados[nI]["descriFuncao"]
		cCpf 		:= aDados[nI]["cpf"]
		cEmail 		:= aDados[nI]["email"]
		cVend 		:= getVend(cMatricul,cCpf,cEmail)
		dIni 		:= StoD(aDados[nI]["inicio"])
		dFim 		:= aDados[nI]["fim"]
		cBlql		:= "2"

		aAdd(aItens,  { ;
			{'P64_FILIAL' 	,xFilial("P64")			, NIL},;
			{'P64_MATRIC' 	,cMatricul				, NIL},;
			{'P64_CAREA'	,cCodArea				, NIL},;
			{'P64_AREA' 	,cDescAre				, NIL},;
			{'P64_CMAREA'	,cCodMArea				, NIL},;
			{'P64_MAREA' 	,cDescMAre				, NIL},;
			{'P64_CSAREA' 	,cCodSbAr				, NIL},;
			{'P64_SAREA'	,cDesSbAr				, NIL},;
			{'P64_CARGO' 	,cCodCarg				, NIL},;
			{'P64_DCARGO' 	,cDesCarg				, NIL},;
			{'P64_CFUNCA'	,cCodFunc				, NIL},;
			{'P64_FUNCAO' 	,cDesFunc				, NIL},;
			{'P64_CPF' 		,cCpf					, NIL},;
			{'P64_EMAIL'	,cEmail					, NIL},;
			{'P64_VEND'		,cVend					, NIL},;
			{'P64_DTINI'	,dIni					, NIL}})
		if !Empty(dFim)
			aAdd(aItens[Len(aItens)],{'P64_DTFIM'	,StoD(dFim)	, NIL})
			aAdd(aItens[Len(aItens)],{'P64_MSBLQL'	,"1"		, NIL})
		Else
			aAdd(aItens[Len(aItens)],{'P64_MSBLQL'	,cBlql		, NIL})
		EndIF
		DbSelectArea("P64")
		DbSetOrder(1)//Filial + Matricula
		nOpcR:=3
		If(P64->(Dbseek(xFilial("P64")+cMatricul)))
			nOpcR:=4
			//Verifica se ao menos um campo na alteracao esta diferente para evitar erro de salvar formulario nao alterado
			aItem:=aItens[1]
			For nJ:=1 to len(aItem)
				if(Alltrim(aItem[nJ][2]) <> Alltrim(P64->&(aItem[nJ][1])))
					lValid:=.T.
					Exit
				EndIF
			Next nJ
			if lValid
				//Verifica se o funcionario mudou de Macro Area, Divisao ou Cargo
				if(P64->P64_CMAREA != cCodMArea .Or. P64->P64_CSAREA != cCodSbAr .Or. P64->P64_CFUNCA !=cCodFunc  )
					oActio:changeIndElegivel(cMatricul)
				EndIF
			EndIF
		Else
			lValid:=.T.
		EndIF

		if lValid
			MSExecAuto({|x, y| U_TIRVA003(x,y)}, aItens, nOpcR)
			If !lMsErroAuto

			Else // EM ESTADO DE JOB
				cTime:= dTos(Date()) + strtran(IncTime(time() ,3,0,0 ),":")
				cError := MostraErro("/system/RVRM/", "TIRVF003_"+cTime+".log")
			EndIf
		EndIF
	Next nI
	CATCHEXCEPTION USING oError
	cMsgFault:=""
	IF ( ValType( oError ) == "O" )
		cMsgFault := IF( !Empty( oError:Description )	, oError:Description	, "" )
		cMsgFault += IF( !Empty( oError:ErrorStack )	, oError:ErrorStack 	, "" )
	EndIF
	cError:='{ "mensagem":"Error '+cMsgFault+'"}'
	IF InTransact()
		DisarmTransaction()
	EndIF
	ENDEXCEPTION
	RestArea(aArea)
	FreeObj(oActio)
Return cError
/*/{Protheus.doc} getVend
	Busca o codigo do vendedor
	@type    Function
	<AUTHOR> Menabue Lima
	@since 18/08/2023
	@version 12.1.33

/*/
Static Function getVend(cMatri,cCpf,cEmail)
	Local oAction := TIRVACTIO():New()
	Local cVende  :=oAction:getVendElegivel(cMatri,cCpf,cEmail)

	FreeObj(oAction)
Return  cVende
/*/{Protheus.doc} User Function getJsonTst
Funcao para teste de inclusao e alteracao de dados
	@type  Function
	<AUTHOR> Menabue Lima
	@since 21/08/2023
	@version 12.1.33
/*/

Static Function getJsonTst()
	Local nI:= 1
	Local cJson		:= '{ "items" :['
	Local cQuery 	:= "SELECT A3_COD,A3_EMAIL,A3_CGC,A3_XMATRH,A.* FROM" + RetSQLName("SA3") +" A WHERE A3_COD IN (SELECT DISTINCT  P66_VENDED FROM" + RetSQLName("P66") +" )    "
	Local cAlsSA3   := GetNextAlias()


	cQuery := ChangeQuery(cQuery)

	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsSA3,.T.,.T.)

	While (cAlsSA3)->(!Eof())
		cCgc:=Alltrim((cAlsSA3)->A3_CGC)
		cJson+=	'				{'
		cJson+=	'					"matricula" 	: "'+cCgc+StrZero(nI,16-Len(cCgc))			 +'" ,  '
		cJson+=	'					"codigoArea"  	: "AR'+StrZero(nI,33)     					 +'" ,  '
		cJson+=	'					"descriArea" 	: "BEMATEC"										 ,  '
		cJson+=	'					"codigoMacro"  	: "MAC.00015.0012.1200.222.RV00'+StrZero(1,2)+'", 	'
		cJson+=	'					"descriMacro" 	: "AREA'+StrZero(nI,2)						 +'" , 	'
		cJson+=	'					"codigoDivisao" : "DIV.00015.0012.1200.222.RV00'+StrZero(1,2)+'", 	'
		cJson+=	'					"descriDivisao" : "DIVISAO'+StrZero(nI,2)					 +'" ,	'
		cJson+=	'					"codigoFuncao" 	: "RV'+StrZero(nI+1,8)					 +'", 	'
		cJson+=	'					"descriFuncao"	: "FUNCAO'+StrZero(nI,2)					 +'" ,  '
		cJson+=	'					"codigoCargo" 	: "RV00'+StrZero(nI+1,2)	   					 +'" ,	'
		cJson+=	'					"descriCargo"   : "GERENTE"										 ,  '
		cJson+=	'					"inicio"   		: "202308'+StrZero(nI,2)    				 +'",   '
		/*If nI % 2 == 0
			cJson+=	'					"fim"   		: "202309'+StrZero(nI,2)				 +'",   '
		Else
			cJson+=	'					"fim"   		: ""	,   '
		EndIF*/
			cJson+=	'					"fim"   		: ""	,   '
		/*if nI ==1
			cJson+=	'					"cpf"     		: "37977757842"								,   '
			cJson+=	'					"email"    		: "<EMAIL>"    '
		Else
			cJson+=	'					"cpf"     		: "346977854'+StrZero(nI,2) 			 +'",   '
			cJson+=	'					"email"    		: "<EMAIL>"    					'
		EndIF*/
			cJson+=	'					"cpf"     		: "'+SubStr(cCgc,1,11)		 +'",   '
			cJson+=	'					"email"    		: "'+StrZero(nI,2)+Alltrim((cAlsSA3)->A3_EMAIL)			 +'"	'
		cJson+=	'				},'
		nI++
		(cAlsSA3)->(DbSkip())
	EndDo

	If Select(cAlsSA3) > 0
		(cAlsSA3)->(dbCloseArea())
	EndIf
		
  
	cJson:=SUBSTR( cJson, 1, len(cJson)-1)
	cJson+= "] }"

Return cJson
