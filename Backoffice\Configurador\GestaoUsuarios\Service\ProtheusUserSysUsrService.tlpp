#include "tlpp-core.th"


namespace Backoffice.Configurador.Gestao_Usuarios.Service.ProtheusUserSysUsrService

using namespace Backoffice.Configurador.Gestao_Usuarios.Service.ProtheusUserRegisterService
using namespace Backoffice.Configurador.Gestao_Usuarios.Service.StructureArrayToUsrService
using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.ExtractValueArrayUtils

/*/{Protheus.doc} ProtheusUserSysUsr
This method aims to create the user on table RD0 using 
other class that manipulates the Model of Data on RD0.
In other other this is a class derivate from the father class 
@type class
@version  1.0
<AUTHOR>
@since 10/31/2024
/*/
Class   ProtheusUserSysUsr  From    ProtheusUserRegister
    Public  Data    aRetProc                                    As  Array
    Public  Data    aDados                                      As  Array
    Public  Data    cJson                                       As  Character
    Private Data    cCodUserOnRd0                               As  Character
    Private Data    cLoginOnRd0                                 As  Character

    Public Method   new() Constructor
    Public Method   createUserUsr()                             As  Array
    Public Method   setJson(cJsonText as Character)
    Public Method   setValues()
    Public Method   setArrayFromJson()                          As  Array
    Public Method   setUserCod()
    Public Method   getUserCod()                                As  Character
    Public Method   setLoginUser()                              As  Character
    Public Method   getLoginUser()                              As  Character

EndClass

/*/{Protheus.doc} ProtheusUserSysUsr::new
This method creates a new instance of the class and instancies
the father class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Returns the instantiated object itself
/*/
Method new() class ProtheusUserSysUsr
    ::aRetProc 				:=  { .T., "" }
    ::cCodUserOnRd0 	:= 	''
    ::cLoginOnRd0			:=	''
    ::cJson 					:= 	''
    ::aDados 					:= 	{}
    _Super:new()
Return Self

/*/{Protheus.doc} ProtheusUserSysUsr::setJson
This method aims to set the Json of income system
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@param cJsonText, character, this parameter is the json with the informations
@return Object, Return the instantiated object itself
/*/
Method setJson(cJsonText as Character) class ProtheusUserSysUsr
	::cJson := cJsonText
Return  Self

/*/{Protheus.doc} ProtheusUserSysUsr::setValues
This method aims to set the values on Model
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method setValues() class ProtheusUserSysUsr
	Local vCampo := '' as Variant
	//---herdado
	::setValuePersonal("DATAUSER", "USR_CODIGO"	, ExtractValueArray():extractValue('USR_CODIGO',::aDados) )
	::setValuePersonal("DATAUSER", "USR_NOME"	    , ExtractValueArray():extractValue('USR_NOME',::aDados) )
	::setValuePersonal("DATAUSER", "USR_EMAIL"	    , ExtractValueArray():extractValue('USR_EMAIL',::aDados) )
	::setValuePersonal("DATAUSER", "USR_PSW"	    , ExtractValueArray():extractValue('USR_PSW',::aDados) )
	::setValuePersonal("DATAUSER", "USR_PSWCMP"	, ExtractValueArray():extractValue('USR_PSW',::aDados) )
	::setValuePersonal("DATAUSER", "USR_CARGO"	    , ExtractValueArray():extractValue('USR_CARGO',::aDados) )
	::setValuePersonal("DATAUSER", "USR_MSBLQL"	    , "2" )
	::setValuePersonal("DATAUSER", "USR_CHGPSW"	    , .T. )
	::setValuePersonal("DATAUSER", "USR_GRPRULE"	    , "1" )
	::setValuePersonal("RESTRICTION", "USR_QTDACESSOS"	    , 5)
	::setValuePersonalGroup("USR_GRUPO", ExtractValueArray():extractValue('USR_GRUPO',::aDados))
	::setValuePersonalGroup("USR_PRIORIZA", "2")

	vCampo := ExtractValueArray():extractValue('USR_SO_DOMINIO',::aDados)
	If vCampo <> Nil
		::setValuePersonal("DATASSIGNON", "USR_SO_DOMINIO"	    , vCampo )
	EndIf

	vCampo := ExtractValueArray():extractValue('USR_SO_USERLOGIN',::aDados)
	If vCampo <> Nil
		::setValuePersonal("DATASSIGNON", "USR_SO_USERLOGIN"	    , vCampo )
	EndIf
Return Self

/*/{Protheus.doc} ProtheusUserSysUsr::setArrayFromJson
This method set the array using the income Character Json
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  setArrayFromJson()  As  Array   Class   ProtheusUserSysUsr
	::aDados  := StructureArrayToUsr():getArrayFromJson(::cJson)
Return Self

/*/{Protheus.doc} ProtheusUserSysUsr::setUserCod
This method aims to set the code user
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  setUserCod()    Class   ProtheusUserSysUsr
	PSWOrder( 2 )
	If PSWSeek( Upper( ExtractValueArray():extractValue('USR_CODIGO',::aDados) ) )
		::cCodUserOnRd0 := PswID()
	EndIf
Return Self

/*/{Protheus.doc} ProtheusUserSysUsr::getUserCod
This method aims to get the code user
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return character, return the code of user on table RD0
/*/
Method  getUserCod()    Class   ProtheusUserSysUsr
Return ::cCodUserOnRd0 

/*/{Protheus.doc} ProtheusUserSysUsr::setLoginUser
This method aims to set the USR_CODE on the property cLoginOnRd0 of the class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instanteated object itself
/*/
Method  setLoginUser()  Class   ProtheusUserSysUsr
	::cLoginOnRd0	:=	ExtractValueArray():extractValue('USR_CODIGO',::aDados)	//nome.sobrenome
Return Self

/*/{Protheus.doc} ProtheusUserSysUsr::getLoginUser
This method aims to get the property cLoginOnRdo
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return character, Return the property cLoginOnRd0 of the class
/*/
Method  getLoginUser()  Class   ProtheusUserSysUsr
Return  ::cLoginOnRd0

/*/{Protheus.doc} ProtheusUserSysUsr::createUserUsr
This method aims to coordinate the process of create any user on SYS_USR Table
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantead object itself
/*/
Method  createUserUsr() As  Array   Class   ProtheusUserSysUsr
	::chooseOperation(3) //herdado
	::activeModel() //herdado
	::setArrayFromJson()
	::setValues()
	::difuseVoid()//herdado
	If ::validadeData() //herdado
		::commitDataPersonal() //herdado
		::aRetProc := { .T., "" }
		::setUserCod()
		::setLoginUser()
	Else
		::aRetProc := ::getArrayError()//herdado
	EndIf
Return Self

