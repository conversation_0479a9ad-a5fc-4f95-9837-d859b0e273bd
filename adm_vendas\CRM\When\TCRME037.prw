#include 'totvs.ch'

// -----------------------------------------------------------------
/*{Protheus.doc} TCRME037
When do campo ADZ_XTPPG
<AUTHOR>
@since 03/03/2025
@version 1.0
*/
// -----------------------------------------------------------------
User Function TCRME037()

Local aArea			:= GetArea()
Local lRet      := .F.
Local oModelADZ := U_TCRMXADZ()
Local cTpPagCad := ReadValue('SB5', 1, xFilial("SB5")+Alltrim(oModelADZ:GetValue("ADZ_PRODUT")), 'B5_XMDPGTO')

If IsInCallStack("FT600LOADGRID") .Or. IsInCallStack("U_PUTPROPOSTACRM") .Or.;
    cTpPagCad == '3' // 3=Ambos (pre e pos pago)

  lRet := .T.
EndIf

RestArea(aArea)
aArea := Nil
oModelADZ := Nil
FwFreeArray(aArea)
FwFreeArray(oModelADZ)
Return lRet
