#INCLUDE "Totvs.ch"

Static lBloqRot := .F.

/*/{Protheus.doc} CT010BLF()

	Ponto de Entrada para desbloquear calendario na mesma thread
	Projeto Migra��o P12

	<AUTHOR>
	@since   18-02-2020
/*/
User Function CT010BLF()

Local lRet := .T.
    
    //--------------------------------------------------------
    // Desbloqueia a rotina CTBA010 se ela tiver sido colocada em lock pelo PE CT010BLI
    //--------------------------------------------------------
    If lB<PERSON>qRot
        UnLockByName("CTBA010", .T., .T.)
    EndIf


Return lRet