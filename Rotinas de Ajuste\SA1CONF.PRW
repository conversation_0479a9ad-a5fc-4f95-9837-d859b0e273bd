#Include 'Protheus.ch'
#INCLUDE "GCTXDEF.CH"

//-------------------------------------------------------------------
/*{Protheus.doc} 
Verifica se dados de monitorado e hd da SA1 expiraram
<AUTHOR>
@since 27/03/2020
@version Protheus12
*/
//-------------------------------------------------------------------
User Function SA1CONF()
Local nOpca 	:= 0
Local aSays 	:= {}
Local aButtons	:= {}
Private cArqSa1Log  := ''
Private cErrArqRet  := ''

If cEmpAnt == "00"
	If 	IsInCallStack ("U_TFINJ014") 
		cArqSa1Log := Alltrim(GetMv("TI_MNRGZQE", , "\REGUA_ZQE"))+"\SA1CONF.TXT"
		cErrArqRet  := Alltrim(GetMv("TI_MNRGZQE", , "\REGUA_ZQE"))+"\SA1CONF_ERRO.TXT"
		FErase(cArqSa1Log)
		FErase(cErrArqRet)

		U_xAcaLog(cArqSa1Log,"Inicio: " + DtoS(dDataBase) + " " + StrTran(TIME(),":","")  )
		Reprocessa()
		U_xAcaLog(cArqSa1Log,"Fim: " + DtoS(dDataBase) + " " + StrTran(TIME(),":","")  )
		
		FRename(cArqSa1Log, STRTRAN(cArqSa1Log, ".TXT", ".FIM") , , .F.)
	Else
		//rotina pronta para utilizar em jump caso solicitem futuramente
		AADD(aSays, OemToAnsi("Esta rotina verifica todos os cadastros de clientes cujo o campo (Dt.Monit) ou o campo "))
		AADD(aSays, OemToAnsi("(Sempre Hd) tenham expirado no dia anterior a " + dtoc(dDataBase) + ". Em seguida, a rotina"))
		AADD(aSays, OemToAnsi("reprocessa o ZQE destes clientes." ))
		AADD(aSays, OemToAnsi(""))
			
		AADD(aButtons, {1,.T.,{|| nOpca := 1, FECHABATCH()}})
		AADD(aButtons, {2,.T.,{|| nOpca := 0, FECHABATCH()}})

		FormBatch("Campos Dt.Monit e Sempre Hd do cadastro do Cliente", aSays, aButtons)

		If nOpca == 1 
			FwMsgRun(, { || Reprocessa(.T.) }, "Verificando contratos dos clientes","Aguarde")		
		EndIf
	EndIf
EndIf

Return()

//-------------------------------------------------------------------
/*{Protheus.doc} 
Reprocessa clientes
<AUTHOR>
@since 27/03/2020
@version Protheus12
*/
//-------------------------------------------------------------------
static function reprocessa (lTela) 
Local cQrySA1 := GetNextAlias()
Local ni      := 0 
Local cCntr
Local cRevs 
default lTela := .F.
Private aLista := {} 

If lTela 
	Procregua(0)
EndIf

BeginSql Alias cQrySA1
	SELECT  A1_COD, A1_LOJA FROM  %table:SA1%  SA1  
	WHERE 	SA1.%notDel%
	AND  ( (SA1.A1_XDTESTR BETWEEN %Exp:(dDataBase-2)% AND  %Exp:(dDataBase-1)% ) OR 
	       (SA1.A1_XHDSEMP BETWEEN %Exp:(dDataBase-2)% AND  %Exp:(dDataBase-1)% ) )
		  
	ORDER BY A1_COD, A1_LOJA
EndSql

If !lTela
	u_xacalog(cArqSa1Log, GetLastQuery()[2] )
EndIf

while (cQrySA1)->(!Eof())
	LstContratos(  (cQrySA1)->(A1_COD), (cQrySA1)->(A1_LOJA)  ) 
	(cQrySA1)->(DbSkip())
End

(cQrySA1)->(DbCloseArea())

For ni := 1 to Len(aLista)
	If lTela 
		IncProc ( "Cliente: " + aLista[ni][1] + " - " +  aLista[ni][2] )
	EndIf
	cCntr := aLista[ni][1] 
	crevs := aLista[ni][2]  
	
	If ClienteBrasil( aLista[ni][1],aLista[ni][2]  )
		bBlock	:= ErrorBlock({|e|  ThrdChkErr( e, cCntr, cRevs )  })
		BEGIN SEQUENCE
		U_TGCVA018 ( aLista[ni][1], aLista[ni][2])
		END SEQUENCE
		ErrorBlock(bBlock)
	EndIf
End

return 
//----------------------------------------------------------------
/*/{Protheus.doc} ThrdChkErr
Captura errlog 

<AUTHOR>
@since 10/07/2020
@version 1.0
/*/
//----------------------------------------------------------------
Static Function ThrdChkErr(oErroArq, cContra, cRev) 
Local n:= 2
Local cLogError 
Local cDir :=  ""
Local cbErrInThrd := ''
Local cRet := oErroArq:Description + oErroArq:ErrorStack
U_xacalog(cErrArqRet , (cContra + '-' + cRev + ' <- contrato')   )
U_xacalog(cErrArqRet , cRet)

If oErroArq:GenCode > 0
    cbErrInThrd += Alltrim( Str( oErroArq:GenCode ) )
    cbErrInThrd += chr(13)+chr(10)
    cbErrInThrd += AllTrim( oErroArq:Description )
    cbErrInThrd += chr(13)+chr(10)
EndIf 

While ( !Empty(ProcName(n)) )
    cbErrInThrd += Trim(ProcName(n)) + " (" + Alltrim(Str(ProcLine(n))) + ") " + chr(13)+chr(10)
    n++
End   

cbErrInThrd += chr(13)+chr(10)
U_xacalog(cErrArqRet , cbErrInThrd )          
Break
Return cRet 


//-------------------------------------------------------------------
/*{Protheus.doc} 
Validacao Clientes MI 
<AUTHOR>
@since 27/03/2019
@version Protheus12
*/
//-------------------------------------------------------------------
static function ClienteBrasil(cCli,cLoja)
Local cQry      := ""
Local aDados   := {}
LOCAL nI        := 0
Local lRet := .T.   
Local cAliasMiQry := GetNextAlias() 
cQry += "SELECT DISTINCT ZQE.ZQE_CODCLI AS CODCLI, ZQE.ZQE_GRPEMP AS CODGRP"+ CRLF
cQry += "FROM " +RetSqlName("ZQE")+ " ZQE " + CRLF
cQry += "WHERE "
cQry += "ZQE.ZQE_FILIAL ='" + xFilial("ZQE") + "' "+ CRLF
cQry += "AND ZQE.ZQE_CODCLI = '"+cCli+"'"	+ CRLF
cQry += "AND ZQE.ZQE_LJCLI = '"+cLoja+"'"	+ CRLF
cQry += "AND D_E_L_E_T_ = ' ' "+ CRLF

DbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAliasMiQry,.T.,.F.)

While (cAliasMiQry)->(! Eof())
	If (cAliasMiQry)->CODGRP !=  "00" .AND. cEmpAnt == "00" 
		lRet := .F.
		exit
	else 
		lRet := .T.
	Endif
	
	(cAliasMiQry)->(DbSkip())
EndDo

DbCloseArea(cAliasMiQry)
				
Return (lRet)

//-------------------------------------------------------------------
/*{Protheus.doc} 
Verifica contratos do Clientes 
<AUTHOR>
@since 27/03/2019
@version Protheus12
*/
//-------------------------------------------------------------------
static function LstContratos (cCli,cLoja) 
Local cQuery  := ""
Local cCntQry := GetNextAlias()

cQuery += "SELECT CN9.CN9_NUMERO, CN9.CN9_REVISA FROM " + RetSqlName("CN9") + " CN9 " 
cQuery += "INNER JOIN " + RetSqlName("CNC") + " CNC " 
cQuery += "	ON  CNC.CNC_FILIAL = '" + xFilial("CNC") + "' " 
cQuery += "	AND CN9.CN9_NUMERO = CNC.CNC_NUMERO "
cQuery += "	AND CN9.CN9_REVISA = CNC.CNC_REVISA "
cQuery += "	AND CNC.D_E_L_E_T_ = ' ' " 
cQuery += "	AND CNC.CNC_CLIENT = '" + cCli + "'  "
cQuery += "	AND CNC.CNC_LOJACL = '" + cLoja +"' "  
cQuery += " WHERE	CN9.CN9_FILIAL = '" + xFilial("CN9") + "' " 
cQuery += " AND 	CN9.CN9_SITUAC = '05'" 
cQuery += " AND     CN9.CN9_NUMERO LIKE	'CON%'"
cQuery += " AND		CN9.D_E_L_E_T_ = ' ' " 
cQuery += " GROUP BY 	CN9.CN9_NUMERO, CN9.CN9_REVISA " 
cQuery += " ORDER BY 	CN9.CN9_NUMERO, CN9.CN9_REVISA " 


DbUseArea(.T., "TOPCONN", TcGenQry(NIL, NIL, cQuery), cCntQry, .T., .T.)
While (cCntQry)->(!Eof())
	Aadd( aLista, { (cCntQry)->(CN9_NUMERO), (cCntQry)->(CN9_REVISA)  } )
	(cCntQry)->(DbSkip())
End

(cCntQry)->(DbCloseArea())

return