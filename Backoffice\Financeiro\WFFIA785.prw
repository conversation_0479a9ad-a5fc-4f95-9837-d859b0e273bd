#Include 'Protheus.ch'
#Include 'FWMVCDef.ch'
#Include 'WFFIA785.ch'

//-------------------------------------------------------------------
/*/{Protheus.doc} WFFINA785
Modelo de dados da Solicita��o de Aprova��o de Border� de Pagamento

<AUTHOR>
@since 05/08/2015
@version 12.1.7
@param cUserSol C�digo Protheus do Usu�rio respons�vel pela solicita��o de aprova��o
@param cCodigoSol C�digo de Identifica��o da solicita��o de aprova��o
@param nOperation N�mero do Modo de Edi��o do modelo de dados da solicita��o (3-Insert;4-Update;5-Delete)
@param aUser Array com o c�digo Fluig dos usu�rios que ser�o os aprovadores da solicita��o de aprova��o
@param lCancela Indica se � cancelamento de solicita��o de aprova��o de border�
/*/
//-------------------------------------------------------------------

User Function WFFIA785( cUserSol, cCodigoSol, nOperation, aUser, lCancela )
Local	aRetWF		:= {}
Local	cWFID		:= ""
Local	cUserFluig	:= ""
Local	aNextTask	:= {}

DEFAULT aUser := {}

dbSelectArea( "PSB" )	// Solicita��o de Aprova��o de Border�
PSB->(DbSetOrder(1)) // Filial + Border� + Vers�o + Carteira

dbSelectArea( "PRO" )	// Solicita��o de Aprova��o de Border�
PRO->( dbSetOrder(1) )	// Filial + C�digo da Solicita��o
If PRO->( dbSeek( FWxFilial( "PRO" ) + cCodigoSol ) )
	//WSDLDbgLevel(3)
	
	If nOperation == MODEL_OPERATION_INSERT
	
	//	FWExecView('Solicita��o de Aprova��o de Border�','WFFINA785', 4, , { || .T. }, , , )
		
		cUserFluig := FWWFColleagueId(cUserSol)
		//WSDLSaveXML(.T.) // Aramazena os XMLs de Envio e Recebimento na pasta System/
		aNextTask := {2,cUserFluig,aUser}
		aRetWF	:= StartProcess( 'SOLAPR', cUserFluig, aUser, /*nTaks*/,/*lMessage*/,/*lComplete*/,/*aAttach*/,aNextTask) // StartProcess(Codigo do Processo, Usu�rio respons�vel)
		cWFID	:= AllTrim( Str( aRetWF[1] ) )
		
		If cWFID != "0" .AND. cWFID != "" .AND. cWFID != Nil
			MoveProcess("SOLAPR",aRetWF[1],cUserFluig,aUser,2)
			BEGIN TRANSACTION
				RecLock( "PRO", .F. )
				PRO->PRO_WFID		:= cWFID
				PRO->PRO_WFNCAR	:= CVALTOCHAR(aRetWF[2])
				PRO->( MsUnLock() )
			END TRANSACTION
		EndIf
	ElseIf !EMPTY(PRO->PRO_WFID) .AND. nOperation == MODEL_OPERATION_UPDATE .AND. lCancela
		cUserFluig := FWWFUserId(VAL(PRO->PRO_WFID))
		CancelProcess(VAL(PRO->PRO_WFID),cUserFluig,STR0001)//"Excluido pelo sistema"
	EndIf
EndIf

Return Nil

//-------------------------------------------------------------------
/*/{Protheus.doc} ModelDef
Modelo de dados da Solicita��o de Aprova��o de Border� de Pagamento

<AUTHOR> Ara�jo Silva
@since 05/08/2015
@version 12.1.7
/*/
//-------------------------------------------------------------------

Static Function ModelDef()
Local oModel		:= MPFormModel():New('MWFFIA785' ,/*PreValidacao*/, {|oModel| U_PosLF785(oModel) }, {|oModel| U_WFF785Gr(oModel) },/*bCancel*/ )
Local oStrMaster	:= FWFormModelStruct():New()
Local oStruPSP	:= FWFormStruct(1,'PSP')

oStrMaster:AddField(	  ;
STR0005					, ;	// [01] Titulo do campo		//"Processo"	
STR0005					, ; // [02] ToolTip do campo	//"Processo"
"PRO_CODIGO"				, ;	// [03] Id do Field
"C"						, ;	// [04] Tipo do campo
TamSX3("PRO_CODIGO")[1]	, ;	// [05] Tamanho do campo
0						, ;	// [06] Decimal do campo
{ || .T. }				, ;	// [07] Code-block de valida��o do campo
						, ;	// [08] Code-block de valida��o When do campo
						, ;	// [09] Lista de valores permitido do campo
.F.						, ; // [10] Indica se o campo tem preenchimento obrigat�rio
{ || ''}				)	// [11] Inicializador Padr�o do Campo

oModel:SetDescription(STR0006) //"Solicita��o de Aprova��o de Border�"

oModel:Addfields("MASTER",/*cOwner*/,oStrMaster/*oStruct*/,/*bPre*/,/*bPost*/,/*bLoad*/ { |oModel| LoadMF785(oModel) } )
oModel:GetModel("MASTER"):SetDescription(STR0007) // "Aprova��o de Border� de Pagamento"
 
oModel:AddGrid("APROVA","MASTER",oStruPSP)
oModel:GetModel("APROVA"):SetDescription(STR0007) // "Aprova��o de Border� de Pagamento"

oModel:SetPrimaryKey({"PRO_CODIGO"})
oModel:SetOnlyQuery('MASTER',.T.)

oModel:SetActivate( {|oModel| U_FIN786Lo(oModel) } )

oModel:GetModel("APROVA"):SetNoInsertLine(.T.)
oModel:GetModel("APROVA"):SetNoDeleteLine(.T.)

Return oModel


//-------------------------------------------------------------------
/*/{Protheus.doc} ViewDef
View da Interface do Formul�rio do WorkFlow de Solicita��o de Aprova��o de Border� de Pagamento

<AUTHOR> Ara�jo Silva
@since 05/08/2015
@version 12.1.7
/*/
//-------------------------------------------------------------------

Static Function ViewDef()
Local oModel		:= FWLoadModel('MWFFIA785')
Local oStrMaster	:= FWFormViewStruct():New()
Local oStruPSP	:= FWFormStruct(2, 'PSP')
Local oView		:= Nil
Local nY			:= 0

For nY := 1 To Len(oStruPSP:AFIELDS) 
	If !oStruPSP:AFIELDS[nY][1] $ "PSP_MOVIME|PSP_HISTOR"
		oStruPSP:SetProperty( oStruPSP:AFIELDS[nY][1], MVC_VIEW_CANCHANGE, .F.)
	EndIf
Next nY

oStrMaster:AddField(;
"PRO_CODIGO"			, ;		// [01] Id do Field
"05"					, ;		// [02] Ordem
STR0005				, ;		// [03] Titulo do campo	//"Processo"
STR0005				, ;		// [04] ToolTip do campo	//"Processo"
					, ;		// [05] Help
"C"					, ;		// [06] Tipo do campo
"@!"					, ;		// [07] Picture
					, ;		// [08] PictVar
					, ;		// [09] F3
.T.					, ;		// [10] Cmpo Evit�vel ?
					, ;		// [11] Folder
					, ;		// [12] Grupo
					, ;		// [13] aComboValues (Op��es)
					, ;		// [14] nMaxLenCombo
	          	   	  )		// [15] cIniBrow

oStrMaster:SetProperty( "PRO_CODIGO", MVC_VIEW_CANCHANGE, .F.)

oView := FWFormView():New()
oView:SetModel(oModel)
oView:AddField('VIEWMASTER',	oStrMaster	,'MASTER')
oView:AddGrid('VIEWPSP',		oStruPSP	,'APROVA')
oView:CreateHorizontalBox( 'CAB', 	20 )
oView:CreateHorizontalBox( 'GRID',	80 )
oView:SetOwnerView('VIEWMASTER','CAB')
oView:SetOwnerView('VIEWPSP','GRID')

/* 
@param cLabel�Indica a descricao que sera exibida no combo das acoes relacionadas
@param nModule�Indica o modulo do processo
@param cAction�Indica a acao que sera executada
@param nOperation�Indica o numero da operacao que sera executada
@param aFieldsFilter�Indica os campos do formularios que serao retornardos (se nao informado, retornara somente a pk do modelo)
*/
oView:AddFluigRelatedAction("Consulta", 06, "WFCON170", 1, {"PSP_BORDER","PSP_VERSAO"})
oView:SetFldHidden('APROVA', "PSP_USER")
oView:SetFldHidden('APROVA', "PSP_PAPEL")
oView:SetFldHidden('APROVA', "PSP_PROAPR")

Return oView

//-------------------------------------------------------------------
/*/{Protheus.doc} WFF785Gr
Fun��o para grava��o dos dados modelo de dados da solicita��o de aprova��o de border� de pagamento.

@return lRet Retorno se a grava��o do modelo de dados foi bem sucedida.
<AUTHOR> Ara�jo Silva
@since 18/06/2015
@version 12.1.6
@param oModel Objeto do modelo de dados da rotina de solicita��o de aprova��o de border� de pagamento
/*/
//-------------------------------------------------------------------

User Function WFF785Gr(oMdlFluig)
Local lRet		:= .T.
Local nX			:= 0
Local cFluigEtap	:= AllTrim(oMdlFluig:GetWKCurrentState())
Local oModel786	:= Nil
Local nTamNom		:= TamSX3("PSP_NOME")[1]
Local cNomApr		:= ""
Local cHora		:= SUBSTR(TIME(), 1, 5)
Local aUser		:= {}
Local cFRWFilial	:= FWxFilial("FRW")
Local cProcesso	:= ""

If cFluigEtap == "2" // Etapa de Aprova��o
	aUser := FWSFLoadUser(oMdlFluig:GetWKUser())
	
	If Len(aUser) == 0
		aUser := FWSFLoadUser(oMdlFluig:GetWKUserEmail())
	EndIf
	
	__cUserID	:= aUser[2]
	cUsername	:= aUser[3]
	cNomApr		:= aUser[4]
	
	DbSelectArea("FRW") // Cadastro de Pap�is
	FRW->(DbSetOrder(1)) // Filial + C�digo do Papel
	
	oModel786	:= FWLoadModel("TFINA786")
	oModel786:SetActivate({||.T.})	
	oModel786:SetOperation(MODEL_OPERATION_INSERT)
	oModel786:Activate()
	oModel786:LoadValue("MASTER","FAKE","0")
	oModel786:GetModel("APROVA"):SetNoInsertLine(.F.)
	
	For nX := 1 To oMdlFluig:GetModel("APROVA"):Length()
		oMdlFluig:GetModel("APROVA"):GoLine(nX)
		If !EMPTY(oMdlFluig:GetModel("APROVA"):GetValue("PSP_MOVIME"))
			If oModel786:GetModel("APROVA"):IsEmpty() .OR. oModel786:GetModel("APROVA"):Length() > 0
				oModel786:GetModel("APROVA"):AddLine()
			EndIf
			
			oModel786:GetModel("APROVA"):SetValue("PSP_BORDER"	,oMdlFluig:GetModel("APROVA"):GetValue("PSP_BORDER"))
			oModel786:GetModel("APROVA"):SetValue("PSP_VERSAO"	,oMdlFluig:GetModel("APROVA"):GetValue("PSP_VERSAO"))
			oModel786:GetModel("APROVA"):SetValue("PSP_PROAPR"	,oMdlFluig:GetModel("APROVA"):GetValue("PSP_PROAPR"))
			oModel786:GetModel("APROVA"):SetValue("PSP_PAPEL"		,oMdlFluig:GetModel("APROVA"):GetValue("PSP_PAPEL"))
			oModel786:GetModel("APROVA"):SetValue("PSP_DSCPAP"	,oMdlFluig:GetModel("APROVA"):GetValue("PSP_DSCPAP"))
			oModel786:GetModel("APROVA"):SetValue("PSP_DATA"		,DDATABASE)
			oModel786:GetModel("APROVA"):SetValue("PSP_USER"		,__cUserID)
			oModel786:GetModel("APROVA"):SetValue("PSP_NOME"		,cNomApr)
			oModel786:GetModel("APROVA"):SetValue("PSP_MOVIME"	,oMdlFluig:GetModel("APROVA"):GetValue("PSP_MOVIME"))
			oModel786:GetModel("APROVA"):SetValue("PSP_HISTOR"	,oMdlFluig:GetModel("APROVA"):GetValue("PSP_HISTOR"))
			oModel786:GetModel("APROVA"):SetValue("PSP_HORA"		,cHora)
		EndIf
	Next nX
	
	If (lRet := oModel786:VldData())
		lRet := oModel786:CommitData()
	Else
		oMdlFluig:SetErrorMessage("WFFINA785",;
								oModel786:GetErrorMessage()[MODEL_MSGERR_IDFIELDERR],;
								"WFF785GRAVA",;
								oModel786:GetErrorMessage()[MODEL_MSGERR_IDFIELDERR],;
								oModel786:GetErrorMessage()[MODEL_MSGERR_ID],;
								oModel786:GetErrorMessage()[MODEL_MSGERR_MESSAGE])
		varinfo("Erro de graa��o: ",oModel786:GetErrorMessage())
		lRet := .F.																
	EndIf
	oModel786:DeActivate()
	oModel786:Destroy()
	oModel786 := Nil
EndIf

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} WFCON170
Fun��o para chamada de consulta de border� de pagamento atr�ves do formul�rio de aprova��o no WF Fluig.

<AUTHOR> Ara�jo Silva
@since 21/10/2015
@version 12.1.8
/*/
//-------------------------------------------------------------------
Static Function WFCON170()
Local oRow	:= FWFluigRA()
Local aGrid	:= {}
Local nAt	:= 0

If oRow <> Nil
	aGrid := oRow:VIEWPSP:rows
	nAt := oRow:VIEWPSP:index
	If nAt > 0
		PSB->(DbSetOrder(1)) // Filial + Border� + Vers�o + Carteira
		If PSB->(DbSeek(FWxfilial("PSB") + AllTrim(aGrid[nAt]:PSP_BORDER) + "P" + AllTrim(aGrid[nAt]:PSP_VERSAO)))
			FWExecView(STR0008,; // "Consulta Border�"
								'FINC170',;
								MODEL_OPERATION_VIEW,;
								/*oDlg*/,;
								{ || .T. },;
								{ || .T. }/*bOk*/,;
								/*nPercReducao*/,;
								/*aButtons*/,;
								{ || .T. }/*bCancel*/,;
								/*cOperatId*/,;
								/*cToolBar*/,;
								/*oModel*/)
		Else
			Help(" ",1,"WFCON170",,STR0009,3,1)//"Nenhum border� encontrado para consulta."
		EndIf
	Else
		Help(" ",1,"WFCON170",,STR0010,3,1)//"Nenhum border� posicionado para consulta."
	EndIf
Else
	Help(" ",1,"WFCON170",,STR0010,3,1)//"Nenhum border� posicionado para consulta."
EndIf

Return Nil

//-------------------------------------------------------------------
/*/{Protheus.doc} LoadMF785
Fun��o carga de dados do Field escondido do formul�rio de aprova��o do Fluig de Border� de Pagamento

@return aRet Retorno de dados de carga do Field do Modelo de Dados.
<AUTHOR> Ara�jo Silva
@since 29/10/2015
@version 12.1.8
@param oModel Objeto do modelo de dados da rotina de aprova��o de border� de pagamento
/*/
//----------------------------------------------------------------
Static Function LoadMF785(oModel)
Local aRet := {{PRO->PRO_CODIGO},0} 
Return aRet

//-------------------------------------------------------------------
/*/{Protheus.doc} PosLF785
Fun��o carga de dados do Field escondido do formul�rio de aprova��o do Fluig de Border� de Pagamento

@return lRet Retorno da valida��o de dados de carga do Field do Modelo de Dados.
<AUTHOR> Ara�jo Silva
@since 06/11/2015
@version 12.1.8
@param oModel Objeto do modelo de dados da rotina de aprova��o de border� de pagamento
/*/
//----------------------------------------------------------------
User Function PosLF785(oMdlFluig)
Local lRet	:= .T.
Local aUser	:= FWSFLoadUser(oMdlFluig:GetWKUser())
Local nX		:= 0
	
If Len(aUser) == 0
	aUser := FWSFLoadUser(oMdlFluig:GetWKUserEmail())
EndIf
	
__cUserID	:= aUser[2]
cUsername	:= aUser[3]
	
For nX := 1 To oMdlFluig:GetModel("APROVA"):Length()
	oMdlFluig:GetModel("APROVA"):GoLine(nX)
	If !EMPTY(oMdlFluig:GetModel("APROVA"):GetValue("PSP_MOVIME")) .OR. !EMPTY(oMdlFluig:GetModel("APROVA"):GetValue("PSP_HISTOR"))
		If oMdlFluig:GetModel("APROVA"):GetValue("PSP_USER") <> __cUserID
			oMdlFluig:SetErrorMessage("WFFINA785",;
								"PSP_MOVIME",;
								"WFF785GRAVA",;
								"PSP_MOVIME",;
								"WFFINA785VLD",;
								STR0011) // "Dados de aprova��o informados para respons�vel incorreto."
			lRet := .F.
		EndIf
	ElseIf EMPTY(oMdlFluig:GetModel("APROVA"):GetValue("PSP_MOVIME")) .OR. EMPTY(oMdlFluig:GetModel("APROVA"):GetValue("PSP_HISTOR"))
		If oMdlFluig:GetModel("APROVA"):GetValue("PSP_USER") == __cUserID
			oMdlFluig:SetErrorMessage("WFFINA785",;
								"APROVA",;
								"WFF785GRAVA",;
								"APROVA",;
								"WFFINA785VLD",;
								STR0012) //"Dados insuficientes de aprova��o para o respons�vel logado."
			lRet := .F.
		EndIf
	EndIf
Next nX

Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} WFPuDMdl
Fun��o de montagem do modelo de dados que ser� enviado para o Fluig para atualiza��o do dados do formul�rio da solicita��o que est� sendo
atualizada.
Projeto Migra��o P12 - antiga WFPutDataMdl
<AUTHOR> Ara�jo Silva
@since 16/11/2015
@version 12.1.8
@param oWFModel Objeto do modelo de dados recebido do Fluig para inclus�o dos dados atualizados
@param aDados Dados atualizados para inclus�o no modelo de dados que ser� enviado ao Fluig para atualiza��o.
@param nQtdTotAp Quantidade total de aprova��es que � a quantidade total de linhas no grid de aprova��o de border�s.
@param oFWLMdl Objeto do modelo de dados de aprova��es feitas atrav�s do Protheus que ser� enviado para atualiza��o da solicita��o no Fluig.

/*/
//----------------------------------------------------------------
User Function WFPuDMdl(oWFModel, aDados, nQtdTotAp, oPSPMdl)
Local nX			:= 0
Local nLinha		:= 0
Local nLinAtual	:= 0
Local nQtdCar		:= 0
Local cCpo		:= ""
Local nPSP		:= 0
Local cLineWF		:= ""
Local nTotApr		:= 0

oWFModel:GetModel("APROVA"):SetNoInsertLine(.F.)

For nX := 1 To Len(aDados)
	nQtdCar := Len(aDados[nX][1])
	cCpo := SUBSTR(aDados[nX][1],1,nQtdCar - 4)
	nLinha := VAL(SUBSTR(aDados[nX][1],nQtdCar))
	
	If nLinha <> nLinAtual
		nLinAtual	:= nLinha
		nTotApr		:= oWFModel:GetModel("APROVA"):Length()
		If nTotApr < nQtdTotAp .AND. nLinha < nTotApr 
			oWFModel:GetModel("APROVA"):AddLine()
		EndIf
	EndIf
	
	If "___" $ aDados[nX][1]
		oWFModel:GetModel("APROVA"):GoLine(nLinha)
		oWFModel:GetModel("APROVA"):SetValue(cCpo,aDados[nX][2])
	EndIf	
Next nX

For nPSP := 1 To oPSPMdl:Length()
	oPSPMdl:GoLine(nPSP)
	
	If oWFModel:GetModel("APROVA"):SeekLine({{"PSP_BORDER",oPSPMdl:GetValue("PSP_BORDER")},{"PSP_VERSAO",oPSPMdl:GetValue("PSP_VERSAO")},{"PSP_USER",oPSPMdl:GetValue("PSP_USER")}})
		If !EMPTY(oPSPMdl:GetValue("PSP_MOVIME"))
			cLineWF := CVALTOCHAR(oWFModel:GetModel("APROVA"):GetLine())
			oWFModel:GetModel("APROVA"):SetValue("PSP_DATA"	,oPSPMdl:GetValue("PSP_DATA"))
			oWFModel:GetModel("APROVA"):SetValue("PSP_MOVIME"	,oPSPMdl:GetValue("PSP_MOVIME"))
			oWFModel:GetModel("APROVA"):SetValue("PSP_HISTOR"	,oPSPMdl:GetValue("PSP_HISTOR"))
			If (nPos := aScan(aDados, { |cCpo| cCpo[1] == "PSP_DATA___" + cLineWF } )) > 0
				aDados[nPos][2] := oPSPMdl:GetValue("PSP_DATA")
			EndIf
			
			If (nPos := aScan(aDados, { |cCpo| cCpo[1] == "PSP_MOVIME___" + cLineWF } )) > 0
				aDados[nPos][2] := oPSPMdl:GetValue("PSP_MOVIME")
			EndIf
			
			If (nPos := aScan(aDados, { |cCpo| cCpo[1] == "PSP_HISTOR___" + cLineWF } )) > 0
				aDados[nPos][2] := oPSPMdl:GetValue("PSP_HISTOR")
			EndIf
		EndIf
	EndIf
Next nPSP
oWFModel:GetModel("APROVA"):SetNoInsertLine(.T.)

Return Nil