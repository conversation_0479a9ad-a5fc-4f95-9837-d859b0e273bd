#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'FWMVCDEF.CH'

//-------------------------------------------------------------------
/*/{Protheus.doc} TFATA006
Cadastro de Linha de Receita


<AUTHOR>
@since 21/09/2015
@version P12
/*/
//-------------------------------------------------------------------

User Function TFATA006()

Local oBrowse	:= NIL
Private cTit	:= "Linha de Receita"

oBrowse := FWMBrowse():New()
oBrowse:SetAlias("PKH")
oBrowse:SetDescription(cTit)
oBrowse:Activate()
	
Return NIL

//-------------------------------------------------------------------
/*/{Protheus.doc} MODELDEF
MVC de cadastro dos grupos que farao parte da regra de rec. aglutinado.


<AUTHOR> Buttner
@since 21/09/2015
@version P12
/*/
//-------------------------------------------------------------------
Static Function ModelDef()

Local oModel		:= NIL
Local oStruPKH	:= FWFormStruct(1,"PKH")

// Modelo de Dados
oModel := MPFormModel():New("CADPKH",/*_bPreValid*/,/*bPosValid*/,/*_bCommit*/,/*bCancel*/)

// Adiciona ao modelo uma estrutura de formulario de edicao por campo
oModel:AddFields("PKHMASTER",/*cOwner*/,oStruPKH)

// Adiciona a descricao do Modelo de Dados
oModel:SetDescription(cTit)

// Adiciona a descricao do Componente do Modelo de Dados
oModel:GetModel( "PKHMASTER" ):SetDescription("LOG")

// Criar uma chave primaria
oModel:SetPrimaryKey( {} )

Return oModel

//-------------------------------------------------------------------
/*/{Protheus.doc} VIEWDEF
MVC de cadastro dos grupos que farao parte da regra de rec. aglutinado.


<AUTHOR> Buttner
@since 02/09/2015
@version P12
/*/
//-------------------------------------------------------------------
Static Function ViewDef()

Local oStruPKH	:= FWFormStruct( 2, "PKH" )
Local oModel		:= FWLoadModel( "TFATA006" )
Local oView		:= NIL

// Cria objeto de VIEW
oView := FWFormView():New()
oView:SetModel(oModel)

// Adiciona controle do tipo enchoice (antiga)
oView:AddField("VIEWPKH", oStruPKH, "PKHMASTER")
oView:CreateHorizontalBox("TELA",100)
oView:SetOwnerView("VIEWPKH","TELA")
oView:EnableTitleView("VIEWPKH")

Return oView