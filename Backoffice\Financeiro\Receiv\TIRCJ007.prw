#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWADAPTEREAI.CH"

#DEFINE COL_RATTIT      02
#DEFINE COL_RATACORDO   02
#DEFINE COL_TITJURO     03
#DEFINE COL_TITMULT     04

/*/{Protheus.doc} TIRCJ007
	Job Responsavel por distribuir os valores dos honorarios nas demais parcelas do acordo
	@type  Function
	<AUTHOR> Menabue Lima
	@since 28/10/2021
	@version 1.0
 
/*/

User Function TIRCJ007(lJob)
	Local cQry     := ""
	Local cAlsPro := GetNextAlias()
	Local cAlsP38:= GetNextAlias()
	Local nVlHono:=0
	Default lJob := .T.
	If U_RCVENV(cEmpAnt,cFilAnt)

		cQry := " SELECT Distinct P38.P38_NUM ACORDO, sUM(P38.P38_HONORA) HONORA,  "
		cQry += "( SELECT  Max(P38_PARCEL) "
		cQry += " FROM "+ RetSqlName("P38") +" P38B "
		cQry += " WHERE   P38B.D_E_L_E_T_ = ' ' AND P38B.P38_TIPO ='A' AND P38B.P38_QUEBRA <> '1' AND P38B.P38_NUM=P38.P38_NUM ) MAXPARC"
		cQry += " FROM "+ RetSqlName("P38") +" P38 "
		cQry += " WHERE   P38.D_E_L_E_T_ = ' '   AND P38_TIPO ='A' "
		cQry += " AND P38_HONORA > 0 AND P38_QUEBRA <>'1' "
		cQry += " AND EXISTS ( SELECT P38.R_E_C_N_O_ RECP38 "
		cQry += " FROM "+ RetSqlName("P38") +" P38A "
		cQry += " WHERE   P38A.D_E_L_E_T_ = ' '   AND P38A.P38_TIPO ='A' AND P38A.P38_NUM=P38.P38_NUM "
		cQry += " AND P38A.P38_HONORA = 0  AND P38A.P38_PARCEL <> '001')
		cQry += " GROUP BY P38_NUM


		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAlsPro,.T.,.T.)


		while !(cAlsPro)->(EOF())

			cQry := " SELECT P38.R_E_C_N_O_ RECP38 "
			cQry += " FROM "+ RetSqlName("P38") +" P38 "
			cQry += " WHERE   P38.D_E_L_E_T_ = ' '   AND P38.P38_TIPO ='A'  AND P38.P38_QUEBRA <>'1' AND P38.P38_NUM='"+(cAlsPro)->ACORDO+"'"
			dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAlsP38,.T.,.T.)

			BeginTran()
			nSldHono:=0
			nQtdPar:=0
			while !(cAlsP38)->(EOF())
				nQtdPar++
				P38->(DbGoTo((cAlsP38)->RECP38))
				aRatAcordo:= fRatAcordo(P38->P38_NUM)
				If ( nLinAcor := aScan(aRatAcordo, {|x| x[1] = P38->(Recno())}) ) > 0
					nPerRatAco   := aRatAcordo[nLinAcor][COL_RATACORDO]
					if(nQtdPar == vAL((cAlsPro)->MAXPARC))//Joga a Dif na ultima parcela
						nVlHono:= (cAlsPro)->HONORA-nSldHono
					Else
						nVlHono:= NoRound((nPerRatAco*(cAlsPro)->HONORA),2) // Nao arredondar em mais de 2 casas deixando a diferenca sempre pra ultima parcela

					EndIF
					RecLock("P38",.F.)
					P38->P38_HONORA := nVlHono
					nSldHono+=P38->P38_HONORA
					//P38->P38_PERCEN := nPerRatAco
					P38->(MsUnLock())
				EndIf
				(cAlsP38)->(Dbskip())
			EndDo
			If Select(cAlsP38) > 0
				(cAlsP38)->(DbCloseArea())
			EndIf
			EndTran()
			(cAlsPro)->(Dbskip())
		EndDo
		If Select(cAlsPro) > 0
			(cAlsPro)->(DbCloseArea())
		EndIf
	EndIF
Return

/*/{Protheus.doc} fRatAcordo

    Retorna o percentual do rateio do acordo.
    
	<AUTHOR>	
	
    @since 25/09/2021

/*/
Static Function fRatAcordo(cIdAcordo)

	Local aRatAcordo    := {}
	Local cAQ           := GetNextAlias()

	If Select(cAQ) > 0
		(cAQ)->(DbCloseArea())
	Endif

	BeginSql Alias cAQ

        SELECT P38.R_E_C_N_O_ RECP38, P38_VALOR/
                (SELECT 
                    SUM(P38_VALOR) 
                FROM 
                    %Table:P38% P38B 
                WHERE 
                    P38B.P38_NUM = %Exp:cIdAcordo%
                    AND P38B.%NotDel%)RATACORDO
        FROM 
            %Table:P38% P38
        WHERE 
            P38.P38_NUM = %Exp:cIdAcordo%
            AND P38.%NotDel%
        ORDER BY 
            P38_PARCEL

	EndSql

	While (cAQ)->(!Eof())

		aAdd(aRatAcordo, {(cAQ)->RECP38, (cAQ)->RATACORDO})

		(cAQ)->(DbSkip())

	EndDo

	If Select(cAQ) > 0
		(cAQ)->(DbCloseArea())
	Endif

Return aRatAcordo
