#Include "Totvs.ch"




/*/{Protheus.doc} TSRVR016
Impressao do Demontrativo de resultado de projetos - DRE
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
User Function TSRVR016()


//���������������������������������������������������������������������Ŀ
//� Declaracao de Variaveis                                             �
//�����������������������������������������������������������������������

Local cDesc1        	:= "Este programa tem como objetivo imprimir relatorio "
Local cDesc2        	:= "de acordo com os parametros informados pelo usuario."
Local cDesc3        	:= "Analise Financeira Projetos - DRE"
Local titulo       	:= "Analise Financeira Projetos - DRE"
Local nLin         	:= 80
Local aHelpPor		:= {}                   	
Local imprime      	:= .T.
Local aOrd 			:= {}
Local aSumarioProjeto			:= {}
Private Cabec1       	:= ""
Private Cabec2       	:= ""
Private lEnd        	:= .F.
Private lAbortPrint 	:= .F.
Private limite      	:= 220
Private tamanho     	:= "G"
Private nomeprog    	:= "TSRVR016" 
Private nTipo       	:= 18
Private aReturn     	:= { "Zebrado", 1, "Administracao", 2, 2, 1, "", 1}
Private nLastKey    	:= 0
Private cPerg       	:= "TSRVR016"
Private cbtxt      	:= Space(10)
Private cbcont     	:= 00
Private CONTFL     	:= 01
Private m_pag      	:= 01
Private wnrel      	:= "TSRVR016"
Private cString 		:= "PE5"

Private nMoedaRel			:= 0

Private lGeraArq := .F.
Private lDREAnalitico := .F.
Private nTpRefRep := 1
Private aProjetoPai := {}
Private lTemFilhos := .F.
Private lOnlyFile := .F.
Private lGeraMov := .F.
Private dDtMovDe        := CtoD("")
Private dDtMovAte       := dDataBase
Private lBuBaMov        := .F.  // gera buffer e backlog por movimento
Private lCalcRV         := .F.  // se veio da rotina de calculo de RV 
Private lConvMoeda	 := .F.
Private lOnlyArray		:= .F.  // gera somente array

dbSelectArea("PE5")
dbSetOrder(1)

//���������������������������������������������������������������������Ŀ
//� Definicao das perguntas                                             �
//�����������������������������������������������������������������������
U_FTTVSX001(cPerg,"01","De Cliente          "	,"De Cliente          "	,"De Cliente          "	,"mv_ch1","C",TamSX3("PE5_CLIENT")[1]	,TamSX3("PE5_CLIENT")[2]	,0,"G","","SA1"                   ,"","","MV_PAR01","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"02","Ate Cliente         "	,"Ate Cliente         "	,"Ate Cliente         "	,"mv_ch2","C",TamSX3("PE5_CLIENT")[1]	,TamSX3("PE5_CLIENT")[2]	,0,"G","","SA1"                   ,"","","MV_PAR02","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"03","Do Reponsavel       "	,"Do Reponsavel       "	,"Do Reponsavel       "	,"mv_ch3","C",TamSX3("PE7_CODREC")[1]	,TamSX3("PE7_CODREC")[2]	,0,"G","","RD0"                   ,"","","MV_PAR03","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"04","Ate Responsavel     "	,"Ate Responsavel     "	,"Ate Responsavel     "	,"mv_ch4","C",TamSX3("PE7_CODREC")[1]	,TamSX3("PE7_CODREC")[2]	,0,"G","","RD0"                   ,"","","MV_PAR04","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"05","Do Projeto          "	,"Do Projeto          "	,"Do Projeto          "	,"mv_ch5","C",TamSX3("PE5_PROJET")[1]	,TamSX3("PE5_PROJET")[2]	,0,"G","","PE5DRE"                ,"","","MV_PAR05","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"06","Ate Projeto         "	,"Ate Projeto         "	,"Ate Projeto         "	,"mv_ch6","C",TamSX3("PE5_PROJET")[1]	,TamSX3("PE5_PROJET")[2]	,0,"G","","PE5DRE"                ,"","","MV_PAR06","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"07","Status              "	,"Status              "	,"Status              "	,"mv_ch7","N",1							,0							,0,"C","",""                      ,"","","MV_PAR07","Ativo","Ativo","Ativo","","Encerrado","Encerrado","Encerrado","Suspenso","Suspenso","Suspenso","Bloqueado","Bloqueado","Bloqueado","Todos","Todos","Todos",aHelpPor,{},{})
U_FTTVSX001(cPerg,"08","Incluidos de        "	,"Incluidos de        "	,"Incluidos de        "	,"mv_ch8","D",TamSX3("PE5_EMISSA")[1]	,TamSX3("PE5_EMISSA")[2]	,0,"G","",""                      ,"","","MV_PAR08","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"09","Incluidos Ate       "	,"Incluidos Ate       "	,"Incluidos Ate       "	,"mv_ch9","D",TamSX3("PE5_EMISSA")[1]	,TamSX3("PE5_EMISSA")[2]	,0,"G","",""                      ,"","","MV_PAR09","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"10","Encerrados de       "	,"Encerrados de       "	,"Encerrados de       "	,"mv_ch0","D",TamSX3("PE5_DTENCR")[1]	,TamSX3("PE5_DTENCR")[2]	,0,"G","",""                      ,"","","MV_PAR10","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"11","Encerrados Ate      "	,"Encerrados Ate      "	,"Encerrados Ate      "	,"mv_cha","D",TamSX3("PE5_DTENCR")[1]	,TamSX3("PE5_DTENCR")[2]	,0,"G","",""                      ,"","","MV_PAR11","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"12","Unidade de Servico  "	,"Unidade de Servico  "	,"Unidade de Servico  "	,"mv_chb","C",99						,0							,0,"G","U_SV011C01('MV_PAR12')","","","","MV_PAR12","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"13","Moeda               "	,"Moeda               "	,"Moeda               "	,"mv_chc","N",2							,0							,0,"G","",""                      ,"","","MV_PAR13","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"14","Outras Moedas       "	,"Outras Moedas       "	,"Outras Moedas       "	,"mv_chd","N",1							,0							,0,"C","",""                      ,"","","MV_PAR14","Converter","Convertir","Convert","","Nao Imprimir","No Imprimir","No Imprimir","Do Not Print","Do Not Print","Do Not Print","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"15","De Segmento         "	,"De Segmento         "	,"De Segmento         "	,"mv_che","C",TamSX3("PE5_CODSEG")[1]	,TamSX3("PE5_CODSEG")[2]	,0,"G","","AOV"                   ,"","","MV_PAR15","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"16","Ate Segmento        "	,"Ate Segmento        "	,"Ate Segmento        "	,"mv_chf","C",TamSX3("PE5_CODSEG")[1]	,TamSX3("PE5_CODSEG")[2]	,0,"G","","AOV"                   ,"","","MV_PAR16","","","","","","","","","","","","","","","","",aHelpPor,{},{})
U_FTTVSX001(cPerg,"17","Imprimir            "	,"Imprimir            "	,"Imprimir            "	,"mv_chg","N",1							,0							,0,"C","",""                      ,"","","MV_PAR17","DRE","DRE","DRE","","DRE + Analitico","DRE + Analitico","DRE + Analitico","Arquivo Resumo","Arquivo Resumo","Arquivo Resumo","","","","","","",aHelpPor,{},{})

pergunte(cPerg,.F.)
//���������������������������������������������������������������������Ŀ
//� Monta a interface padrao com o usuario...                           �
//�����������������������������������������������������������������������
wnrel := SetPrint(cString,NomeProg,cPerg,@titulo,cDesc1,cDesc2,cDesc3,.F.,aOrd,.T.,Tamanho,,.F.)

If nLastKey == 27
	Return
Endif

SetDefault(aReturn,cString)

If nLastKey == 27
   Return
Endif

nTipo := If(aReturn[4]==1,15,18)

If MV_PAR17 == 2
	lDREAnalitico := .T.
ElseIf MV_PAR17 == 3
	lGeraArq := .T.
Endif	

//���������������������������������������������������������������������Ŀ
//� Levantamento dos dados                                              �
//�����������������������������������������������������������������������
Processa( {|| aSumarioProjeto := TSR016Dados() } , "Analise Financeira de Projetos - DRE" , "Levantamento de dados...")

//���������������������������������������������������������������������Ŀ
//� Processamento. RPTSTATUS monta janela com a regua de processamento. �
//�����������������������������������������������������������������������

RptStatus({|| RunReport(Cabec1,Cabec2,Titulo,nLin,aSumarioProjeto) },Titulo)

Return



/*/
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Fun��o    �RUNREPORT � Autor � AP6 IDE            � Data �  07/03/13   ���
�������������������������������������������������������������������������͹��
���Descri��o � Funcao auxiliar chamada pela RPTSTATUS. A funcao RPTSTATUS ���
���          � monta a janela com a regua de processamento.               ���
�������������������������������������������������������������������������͹��
���Uso       � Programa principal                                         ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
/*/

/*/{Protheus.doc} RunReport
RunReport
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
Static Function RunReport(Cabec1,Cabec2,Titulo,nLin,aSumarioProjeto)

Local nW	:= 0
nTpRefRep := 1
nStatPE5 := MV_PAR07

For nW:=1 To Len(aSumarioProjeto)
	U_CA50Impr(nW, .F., lGeraArq, lDREAnalitico, aSumarioProjeto, aProjetoPai, lTemFilhos, lOnlyFile, lGeraMov, dDtMovDe, dDtMovAte, lBuBaMov, lCalcRV, cEmpAnt+cFilAnt, nTpRefRep, nStatPE5)
Next nW
//���������������������������������������������������������������������Ŀ
//� Finaliza a execucao do relatorio...                                 �
//�����������������������������������������������������������������������

SET DEVICE TO SCREEN

//���������������������������������������������������������������������Ŀ
//� Se impressao em disco, chama o gerenciador de impressao...          �
//�����������������������������������������������������������������������
If aReturn[5]==1
   dbCommitAll()
   SET PRINTER TO
   OurSpool(wnrel)
Endif

MS_FLUSH()
Return


/*/{Protheus.doc} CA50Impr
CA50Impr
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
User Function CA50Impr(nPos, lResumo, lGeraArq, lDREAnalitico, aSumarioProjeto, aProjetoPai, lTemFilhos, lOnlyFile, lGeraMov, dDtMovDe, dDtMovAte, lBuBaMov, lCalcRV, cEmpFilOri, nTpRefRep, nStatPrj)

Local x, y, z,_n1, _n2, _s1, lOk
Local nQuebra, nResultado
Local nMenu             := 1
Local _aDREPrj          := {}
Local _aDREFilho        := {}
Local _aSumario         := {}
Local _aItemFaturado    := {}
Local _aTitulos         := {}
Local _aComissoes       := {}
Local _aDespesasProjeto := {}
Local _aDespAvulsa      := {}
Local _aDespViagem     	:= {}
Local _aOrdemServico    := {}
Local _aResumoMotivos   := {}
Local _aFatDespesa      := {}
Local cQuebra           :=  ""
Local nResultReceita    := 0
Local nResultCustos     := 0
Local nResultComissao   := 0

Local nNaoAbon  		:= 0   
Local nAbonoAR  		:= 0   
Local nAbonoCR  		:= 0   
Local nAbonoIP  		:= 0
Local cCompet           := ""

Local nFatHoras         := 0
Local nFatDespD  		:= 0   
Local nFatDespA  		:= 0   
Local nFatDespV  		:= 0   

Local lCabe             := .T.

Local cSimbMoeda        := GetMv("MV_SIMB"+Str(nMoedaRel,1))
Local cDescTpProj       := ""

Local lOkIncRec         := .F.

Local lxHistExc         := .F.
Local lxTemDe           := .F.
Local lImpTudo          := (! lGeraMov) .Or. (lGeraMov .And. ! lBubaMov)

Local nDespA            := 0
Local nDespD            := 0
Local nDespV            := 0
Local nDespT            := 0
Local cTipoD            := ""
Local lQuebra           := .F.
Local nValor            := 0
Local cEmpFilAtu        := cEmpAnt+cFilAnt
Local cArqZCX           := ""
Local cFilZCX           := ""
Local cFilZCT           := ""
Local cCCusto           := ""

Private nLin 			:= 5
Private _cChaveProjeto  := ""

Default lResumo         := .F.
Default lGeraArq        := .F.
Default lOnlyFile       := .F.
Default nTpRefRep       := 1

If lOnlyFile .And. !lGeraArq
	lGeraArq := .T.
Endif

titulo := "Analise Financeira Projetos - DRE"
If lGeraMov   
	If lBuBaMov
		titulo := Alltrim(titulo) + " - Periodo de "+DtoC(dDtMovDe)+" ate "+DtoC(dDtMovAte)
	Else
		If lCalcRV
			titulo := Alltrim(titulo) + " - Data Corte RV "+DtoC(dDtMovAte)
		Endif
	Endif
Endif                   

If nTpRefRep == 1
	titulo := Alltrim(titulo) + " - Ref. TOTVS"
ElseIf nTpRefRep == 2
	titulo := Alltrim(titulo) + " - Ref. Dono Projeto"
ElseIf nTpRefRep == 3
	titulo := Alltrim(titulo) + " - Ref. Contratado"
Endif

//-- busca dados do projeto
If ! lResumo  // projetos pai e filho
	
	_cChaveProjeto    := aSumarioProjeto[nPos,1]
	_aItemFaturado    := aClone( aSumarioProjeto[nPos,2] )
	_aTitulos         := aClone( aSumarioProjeto[nPos,3] )
	_aComissoes       := aClone( aSumarioProjeto[nPos,4] )
	_aDREPrj          := aClone( aSumarioProjeto[nPos,5] )
	_aSumario         := aClone( aSumarioProjeto[nPos,6] )
	_aDespesasProjeto := aClone( aSumarioProjeto[nPos,7] )
	_aDespAvulsa      := aClone( aSumarioProjeto[nPos,8] )
	_aDespViagem      := aClone( aSumarioProjeto[nPos,9] )
	_aOrdemServico    := aClone( aSumarioProjeto[nPos,12] )
	_aResumoMotivos   := aClone( aSumarioProjeto[nPos,13] )
	_aFatDespesa      := aClone( aSumarioProjeto[nPos,14] )
	
Else // projeto resumo
	
	_cChaveProjeto    := aSumarioProjeto[nPos,1]
	
	nPos := AScan(aProjetoPai,{|x| x[1] == _cChaveProjeto })
	
	If nPos <> 0
		
		_cChaveProjeto    := aProjetoPai[nPos,1]
		_aItemFaturado    := aClone( aProjetoPai[nPos,2] )
		_aTitulos         := aClone( aProjetoPai[nPos,3] )
		_aComissoes       := aClone( aProjetoPai[nPos,4] )
		_aDREPrj          := aClone( aProjetoPai[nPos,5] )
		_aSumario         := aClone( aProjetoPai[nPos,6] )
		_aDespesasProjeto := aClone( aProjetoPai[nPos,7] )
		_aDespAvulsa      := aClone( aProjetoPai[nPos,8] )
		_aDespViagem      := aClone( aProjetoPai[nPos,9] )
		_aOrdemServico    := aClone( aProjetoPai[nPos,11] )
		_aResumoMotivos   := aClone( aProjetoPai[nPos,12] )
		_aFatDespesa      := aClone( aProjetoPai[nPos,13] )

	Endif
	
Endif

	
dbSelectArea("PE5")
dbSetOrder(1)
dbSeek(FWxFilial("PE5")+_cChaveProjeto)
If Eof()
	Return
Endif

//-- grava como competencia para uso no BI
//-- se for por periodo grava o mes/ano do intervalo solicitado
If lGeraMov
	cCompet	:= StrZero(Year(dDtMovAte),4) + StrZero(Month(dDtMovAte),2)
//-- se estiver rodando os encerrados grava o mes/ano do intervalo solicitado 
ElseIf nStatPrj == 3
	cCompet	:= StrZero(Year(MV_PAR25),4) + StrZero(Month(MV_PAR25),2)
Else
	cCompet	:= StrZero(Year(PE5->PE5_EMISSA),4) + StrZero(Month(PE5->PE5_EMISSA),2)	
Endif	

_cChavePrjFat 	  := _aDREPrj[1]				// [1] projeto pai
_nQtdFat	  	  := _aDREPrj[2] 				// [2] horas faturadas
_nVlrFat		  := _aDREPrj[3] 				// [3] valor faturado
_nQtdDev		  := _aDREPrj[4] 				// [4] horas cancelas
_nVlrDev	  	  := _aDREPrj[5] 				// [5] valor cancelado
_nQtdFatDev		  := _aDREPrj[6]  			    // [6] horas faturadas-canceladas
_nVlrFatDev		  := _aDREPrj[7] 				// [7] valor faturado-cancelado
_nQtdRea		  := _aDREPrj[8] 				// [8] horas realizadas
_nVlrRea		  := _aDREPrj[9]			 	// [9] valor realizado
_nQtdAbo		  := _aDREPrj[10] 				// [10] horas abonadas
_nVlrAbo		  := _aDREPrj[11]			 	// [11] valor abonado
_nQtdReaAbo		  := _aDREPrj[12] 				// [12] horas realizadas-abonadas
_nVlrReaAbo		  := _aDREPrj[13]				// [13] valor relizado-abonado
_nOrcamento		  := _aDREPrj[14]				// [14] horas orcadas
_nVlrOrcamento	  := _aDREPrj[15]				// [15] valor orcado
_nHrBuff		  := _aDREPrj[16]				// [16] horas buffer
_nVlBuff		  := _aDREPrj[17] 				// [17] valor buffer
_nHrBack		  := _aDREPrj[18] 				// [18] horas backlog
_nVlBack		  := _aDREPrj[19] 				// [19] valor backlog
_nTrasCobrado	  := _aDREPrj[20]				// [20] traslado cobrado
_nTrasIncorrido	  := _aDREPrj[21]				// [21] traslado incorrido
_nDespCobrada	  := _aDREPrj[22]				// [22] despesa cobrada
_nDespIncorrida	  := _aDREPrj[23]				// [23] despesa incorrida
_nValLiq		  := _aDREPrj[24]				// [24] valor liquido cobrado x incorrido
_nValorHMedio	  := _aDREPrj[25]				// [25] valor hora medio
_nParticipacao	  := _aDREPrj[26]				// [26] participacao
_cStatus		  := _aDREPrj[27]				// [27] status
_dPriOS		      := _aDREPrj[28]				// [28] primeira OS
_dUltOS		      := _aDREPrj[29]				// [29] ultima OS
_nFatorComiss     := _aDREPrj[30]				// [30] fator de proporcao da comissao mediante o faturamento
_cExcQFat         := _aDREPrj[31]				// [31] flag se for excecao gerencial da quantidade faturada
_cExcBuffBack     := _aDREPrj[32]				// [32] flag se for excecao gerencial da quantidade do buffer/backlog
_cExcVMed		  := _aDREPrj[33]				// [33] flag se for excecao gerencial do valor medio

//-- dados dos indicadores do projeto
_nPF              := _aDREPrj[34]  				// [34] PROGRESSO FISICO - PERCENTUAL
_nETCI            := _aDREPrj[35]				// [35] ETC INFORMADO - HORAS
_nODEI            := _aDREPrj[36]				// [36] ODE INFORMADO - HORAS
_nCPI             := _aDREPrj[37]				// [37] CPI (INDICE DE PERFORMANCE DO CUSTO) - COEFICIENTE
_nSPI             := _aDREPrj[38]				// [38] SPI (INDICE DE PERFORMANCE DO PRAZO) - COEFICIENTE
_nETCL            := _aDREPrj[39]				// [39] ETC Linear (ESTIMATIVA PARA A CONCLUSAO SEM VARIACAO) - HORAS
_nETCT            := _aDREPrj[40]				// [40] ETC TENDENCIA (ESTIMATIVA PARA A CONCLUSAO COM VARIACAO PREVISTA) - HORAS
_nEACPL           := _aDREPrj[41]				// [41] EAC LINEAR PROJETADO - HORAS
//-- nao usado
_nEACPT           := _aDREPrj[42]				// [42] EAC TENDENCIA PROJETADO - HORAS (*** NAO USADO ***)
_nEACA            := _aDREPrj[43]				// [43] EAC ATUAL - HORAS  (*** NAO USADO ***)             
//-- nao usado
_cWBS             := _aDREPrj[44]				// [44] WBS DO PROJETO
_cCOMPLEX         := _aDREPrj[45]				// [45] COMPLEXIDADE
_nIMIT            := _aDREPrj[46]				// [46] IMIT - PERCENTUAL
_nEACT            := _aDREPrj[47]				// [47] EAC Tendencia (hrs)			
_nEACLC           := _aDREPrj[48]				// [48]	EAC Linear Custo			
_nEACTC           := _aDREPrj[49]				// [49] EAC Tendencia Custo			
_nMCLV            := _aDREPrj[50]				// [50]	Margem Contribuicao Linear ($)		
_nMCLP            := _aDREPrj[51]				// [51]	Margem Contribuicao Linear (%)		
_nMCTV            := _aDREPrj[52]				// [52]	Margem Contribuicao Tendencia ($)	
_nMCTP            := _aDREPrj[53]				// [53]	Margem Contribuicao Tendencia (%)	

_nEACAG			  := _aDREPrj[54] 				// [54] EAC Agenda (hrs)
_nEACAGC		  := _aDREPrj[55]				// [55] EAC Agenda Custo
_nMCAGV			  := _aDREPrj[56]				// [56] Margem Contribuicao Agenda ($)
_nMCAGP			  := _aDREPrj[57]				// [57] Margem Contribuicao Agenda (%)

_nHrBuffAnt       := _aDREPrj[58] 				// [58] horas de buffer mes anterior (DRE por movimento)
_nHrBackAnt       := _aDREPrj[59]  			    // [59] horas de backlog mes anterior (DRE por movimento)
_nVlBuffAnt       := _aDREPrj[60]  			    // [60] valor de buffer mes anterior (DRE por movimento)
_nVlBackAnt       := _aDREPrj[61]  			    // [61] valor de backlog mes anterior (DRE por movimento)
_nHrBuffAtu       := _aDREPrj[62]  			    // [62] horas de buffer mes atual (DRE por movimento)	
_nHrBackAtu       := _aDREPrj[63]  			    // [63] horas de backlog mes atual (DRE por movimento)	
_nVlBuffAtu       := _aDREPrj[64]  			    // [64] valor de buffer mes atual (DRE por movimento)	
_nVlBackAtu       := _aDREPrj[65]  			    // [65] valor de backlog mes atual (DRE por movimento)	
_nHrMedBuff       := _aDREPrj[66]  				// [66] valor hora medio de buffer (DRE por movimento)	
_nHrMedBack       := _aDREPrj[67] 				// [67] valor hora medio de backlog (DRE por movimento)	
_nMCPS            := _aDREPrj[68]				// [68] MARGEM DE CONTRIBUICAO SIMULADA
_nRecSim          := _aDREPrj[69]				// [69] VALOR RECEITA SIMULADA
_nVlrRepasse      := _aDREPrj[70]				// [70] Valor repasse utilizado para calcular a receita simulada
_nLimRNP          := _aDREPrj[71]				// [71] Limite RNP
_nHrSuspensa      := _aDREPrj[72]				// [72] Qtd de Horas Suspensas

//-- busca dados do projeto pai para o filho
If ! Empty(_cChavePrjFat)

	nPos := aScan( aSumarioProjeto , {|x| x[1] == _cChavePrjFat })

	If	nPos > 0

		_aDREFilho := aClone( aSumarioProjeto[nPos,5] )
		_aTitulos  := aClone( aSumarioProjeto[nPos,3] )
		
		//-- tem movimento de notas no filho, entao junta
		If Len(_aItemFaturado) > 0                            
			For x:=1 To Len(aSumarioProjeto[nPos,2])
				AAdd(_aItemFaturado,aSumarioProjeto[nPos,2,x])
			Next x
		//-- senao, assume o do pai	
		Else 
			_aItemFaturado := aClone( aSumarioProjeto[nPos,2] )
		Endif
		
	EndIf

Endif

//If ! lGeraMov                                                   
//    //-- quando roda por projeto, o projeto tem buffer ou backlog
//	_aSumario[2][3] := IIf(_nVlBuff == 0 ,_nVlBack ,_nVlBuff)
//Else                             
//    //-- quando roda por movimento eh sempre a soma das variacoes de buffer e backlog
//	_aSumario[2][3] := _nVlBuff + _nVlBack
//Endif	

If lGeraMov .And. lBubaMov                                                   
    //-- quando roda por movimento eh sempre a soma das variacoes de buffer e backlog
	_aSumario[2][3] := _nVlBuff + _nVlBack
Else
    //-- quando roda por projeto, o projeto tem buffer ou backlog
	_aSumario[2][3] := IIf(_nVlBuff == 0 ,_nVlBack ,_nVlBuff)
Endif	

//-- calcula custo das horas, separando por tipo de abono

nNaoAbon  := 0   
nAbonoAR  := 0   
nAbonoCR  := 0   
nAbonoIP  := 0

For z:=1 To Len(_aResumoMotivos)
	If _aResumoMotivos[z,5] == "1" 		// nao abonados
		nNaoAbon += _aResumoMotivos[z,3]
	ElseIf _aResumoMotivos[z,5] == "2"	// abono AR 
		nAbonoAR += _aResumoMotivos[z,3]
	ElseIf _aResumoMotivos[z,5] == "3"	// abono CR
		nAbonoCR += _aResumoMotivos[z,3]
	ElseIf _aResumoMotivos[z,5] == "4"	// acompanhamento
		nAbonoCR += _aResumoMotivos[z,3]
	ElseIf _aResumoMotivos[z,5] == "5"	// erro de produto
		nAbonoIP += _aResumoMotivos[z,3]
	ElseIf _aResumoMotivos[z,5] == "6"	// investimento
		nAbonoCR += _aResumoMotivos[z,3]
	EndIf
Next z

//-- calcula faturamento de despesas (despesas de atendimento, avulsas, viagens)
                     
nFatHoras := 0
nFatDespD := 0   
nFatDespA := 0   
nFatDespV := 0   

For z:=1 To Len(_aFatDespesa)
	If Substr(_aFatDespesa[z,1],1,1) == "D"  // despesa de atendimento
		nFatDespD += _aFatDespesa[z,5]
	ElseIf Substr(_aFatDespesa[z,1],1,1) == "A"  // avulsa
		nFatDespA += _aFatDespesa[z,5]
	ElseIf Substr(_aFatDespesa[z,1],1,1) == "V"  // viagem
		nFatDespV += _aFatDespesa[z,5]
	Endif	
Next z

For x := 1 To Len( _aSumario )
	If Subs(_aSumario[x,2],1,2) == "A1"  // FATURAMENTOS
		nFatHoras := _aSumario[x,3]
		Exit
	Endif	
Next x	

nFatHoras := nFatHoras - nFatDespD - nFatDespA - nFatDespV

lOkIncRec := .F.

//-- grava arquivo de trabalho
If lGeraArq 

	//-- 08/10/2010 - quando esta gerando arquivo, desconsiderar o projeto pai, pois seu 
	//-- faturamento esta distribuido nos filhos, para nao duplicar os valores de faturamento
	//-- para ficar igual a planilha Excel (Victor)
	lOk := .T.
	If lOnlyFile .And. lTemFilhos 
		lOk := .F.
	Endif                        
	
	If lOk
	
		dbSelectArea("PE2")
		dbSetOrder(2)
		dbSeek(xFilial("PE2")+cEmpAnt+cFilAnt)
	
		cCodDrg := PE2_UNSRV

		dbSelectArea("PE1")
		dbSetOrder(1)
		dbSeek(xFilial("PE1")+PE2->_PE2_UNSRV)
		
		cDesDrg := PE1->PE1_DESCRI
		cClVl   := PE1->PE1_CLVL 
		cCCusto := PE1->PE1_CCUSTO

		dbSelectArea("DRE")
		RecLock("DRE",.T.)

		lOkIncRec := .T.
		
		A50Cabecalho(.T., lResumo, lGeraArq, nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		
		//-- buffer/backlog
		DRE->PRJFAT		:= Substr(_cChavePrjFat,7,Len(_cChavePrjFat)-6)
		DRE->PARTICIPA	:= _nParticipacao*100
		DRE->VLHRMED	:= _nValorHMedio
		DRE->HRFAT		:= _nQtdFat
		DRE->VLFAT		:= _nVlrFat
		DRE->HRCAN		:= _nQtdDev
		DRE->VLCAN		:= _nVlrDev
		DRE->HRFATCANC	:= _nQtdFatDev
		DRE->VLFATCANC	:= _nVlrFatDev
		DRE->HRREA		:= _nQtdRea
		DRE->VLREA		:= _nVlrRea
		DRE->HRABON		:= _nQtdAbo
		DRE->VLABON		:= _nVlrAbo
		DRE->HRAREABON	:= _nQtdReaAbo
		DRE->VLREAABON	:= _nVlrReaAbo
		DRE->HRORC		:= _nOrcamento
		DRE->VLORC		:= _nVlrOrcamento
		DRE->LIMRNP     := _nLimRNP
		DRE->HRSUSP     := _nHrSuspensa
		DRE->HRBUFFER	:= _nHrBuff
		DRE->VLBUFFER	:= _nVlBuff
		DRE->HRBACKLOG	:= _nHrBack
		DRE->VLBACKLOG	:= _nVlBack
		
		//-- despesas
		DRE->VLTRASCOBR	:= _nTrasCobrado
		DRE->VLTRASAPON	:= _nTrasIncorrido
		DRE->VLDESPCOBR	:= _nDespCobrada
		DRE->VLDESPAPON	:= _nDespIncorrida
		DRE->VLDESPTOT	:= _nValLiq
		
		DRE->TEMFILHO   := If(lTemFilhos,"*","")
	
	    //-- indicadores do projeto
		DRE->PF         := _nPF
		DRE->ETCI       := _nETCI
		DRE->ODEI       := _nODEI
		DRE->CPI        := _nCPI
		DRE->SPI        := _nSPI
		DRE->ETCL       := _nETCL
		DRE->ETCT       := _nETCT
		DRE->EACPL      := _nEACPL
		//-- nao usado
		DRE->EACPT      := _nEACPT
		DRE->EACA       := _nEACA
		//-- nao usado
		DRE->WBS        := _cWBS
		DRE->DESCRWBS   := Posicione("ZDT",1,xFilial("ZDT")+_cWBS,"ZDT_DESCR")
		DRE->COMPLEX    := _cCOMPLEX
		DRE->IMIT       := _nIMIT
		DRE->EACT       := _nEACT  		
		DRE->EACLC      := _nEACLC
		DRE->EACTC      := _nEACTC
		DRE->MCLV       := _nMCLV
		DRE->MCLP       := _nMCLP 
		DRE->MCTV       := _nMCTV
		DRE->MCTP       := _nMCTP 
		DRE->EACAG 		:= _nEACAG
		DRE->EACAGC 	:= _nEACAGC
		DRE->MCAGV 		:= _nMCAGV
		DRE->MCAGP 		:= _nMCAGP
	
	    //-- dados da empresa
		DRE->EMPRESA    := cEmpAnt
		DRE->FILIAL     := cFilAnt
		DRE->DRG        := cCodDrg 
		DRE->DESCDRG    := cDesDrg
		DRE->COMPET     := cCompet
		If lNewCC
			DRE->CCUSTO     := cCCusto
		EndIf
		DRE->CLVL       := cClVl

		//-- dados do buffer e backlog para relatorio por movimento
		DRE->HRBUFFD    := _nHrBuffAnt
		DRE->HRBACKD    := _nHrBackAnt
		DRE->VLBUFFD    := _nVlBuffAnt
		DRE->VLBACKD    := _nVlBackAnt
		DRE->HRBUFFA    := _nHrBuffAtu
		DRE->HRBACKA    := _nHrBackAtu
		DRE->VLBUFFA    := _nVlBuffAtu
		DRE->VLBACKA    := _nVlBackAtu
		DRE->HMBUFF     := _nHrMedBuff 
		DRE->HMBACK     := _nHrMedBack
		DRE->MCPS       := _nMCPS
		DRE->RECSIM     := _nRecSim
		DRE->VLREPASSE	:= _nVlrRepasse	
		
		DRE->(MsUnlock())

	Endif
		
Else
	
	Cabec(Titulo,Cabec1,Cabec2,NomeProg,Tamanho,nTipo)
	
	If 	lAbortPrint
		@ nLin,00 PSAY "*** CANCELADO PELO OPERADOR ***"
		Return
	EndIf
	
	//-- CABECALHO
	A50Cabecalho(.T., lResumo, lGeraArq, nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

    _nHrBB := 0
    _nVlBB := 0
	If _nHrBuff <> 0
	   _nHrBB := _nHrBuff
	   _nVlBB := _nVlBuff
	Else
	   _nHrBB := _nHrBack
	   _nVlBB := _nVlBack
	Endif
	           
	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin   , 00 PSAY "=> BUFFER E BACKLOG - VALORIZADOS"
	@ nLin   , 85 PSAY "=> DESPESAS DO PROJETO"	
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin   ,155 PSAY "=> INDICADORES PROJETADOS"	
	Endif	
//	@ nLin+2 , 00 PSAY "Cod.Projeto Pai       : "    + PADR(Substr(_cChavePrjFat,7,Len(_cChavePrjFat)-6),14)
	@ nLin+2 , 85 PSAY "(+) Traslado Cobrado  : "+cSimbMoeda+" " + Transform(_nTrasCobrado,"@E 999,999,999,999.99")
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+2 ,155 PSAY "ETC Agenda            : "    + Transform(_nETCI,"@E 9,999,999.99") + " hrs"
	Endif
//	@ nLin+3 , 00 PSAY "% Participacao        : "    + Transform(_nParticipacao*100,"@E 9,999,999.99%")
	@ nLin+3 , 85 PSAY "(-) Traslado Apontado : "+cSimbMoeda+" " + Transform(_nTrasIncorrido,"@E 999,999,999,999.99")
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+3 ,155 PSAY "ETC Linear            : "    + Transform(_nETCL,"@E 9,999,999.99") + " hrs"
	Endif	
    @ nLin+4 , 00 PSAY "Valor Hora Media      : "+cSimbMoeda+" " + Transform(_nValorHMedio,"@E 99,999.99") + "  " + If(Empty(_cExcVMed),"",Space(28)+"(1)")
	@ nLin+4 , 85 PSAY "(+) Despesas Cobradas : "+cSimbMoeda+" " + Transform(_nDespCobrada,"@E 999,999,999,999.99")	
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+4 ,155 PSAY "ETC Tendencia         : "    + Transform(_nETCT,"@E 9,999,999.99") + " hrs"
		@ nLin+5 , 00 PSAY "Orcada                : "    + Transform(_nOrcamento,"@E 9,999,999.99")+" hrs " + "  "+cSimbMoeda+" " + Transform(_nVlrOrcamento,"@E 999,999,999,999.99")
	Endif	
	@ nLin+5 , 85 PSAY "(-) Despesas Apontadas: "+cSimbMoeda+" " + Transform(_nDespIncorrida,"@E 999,999,999,999.99")		
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+5 ,155 PSAY "EAC Agenda            : "    + Transform(_nEACAG,"@E 9,999,999.99") + " hrs"
	Endif	
	@ nLin+6 , 00 PSAY "Nota Fiscal Faturada  : "    + Transform(_nQtdFat   ,"@E 9,999,999.99")+" hrs " + "  "+cSimbMoeda+" " + Transform(_nVlrFat,"@E 999,999,999,999.99")
	@ nLin+6 , 85 PSAY "(=) Total Despesas    : "+cSimbMoeda+" " + Transform(_nValLiq   ,"@E 999,999,999,999.99")		
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+6 ,155 PSAY "EAC Linear            : "    + Transform(_nEACPL,"@E 9,999,999.99") + " hrs"
	Endif	
    @ nLin+7 , 00 PSAY "Nota Fiscal Cancelada : "    + Transform(_nQtdDev   ,"@E 9,999,999.99")+" hrs " + "  "+cSimbMoeda+" " + Transform(_nVlrDev,"@E 999,999,999,999.99")
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+7 ,155 PSAY "EAC Tendencia         : "    + Transform(_nEACT,"@E 9,999,999.99") + " hrs"
	Endif	
    @ nLin+8 , 00 PSAY "NF Faturada-Cancelada : "    + Transform(_nQtdFatDev,"@E 9,999,999.99")+" hrs " + "  "+cSimbMoeda+" " + Transform(_nVlrFatDev,"@E 999,999,999,999.99") + "  " + If(Empty(_cExcQFat),"","(2)")
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+8 , 85 PSAY "=> INDICADORES DO PROJETO"	
		@ nLin+8 ,155 PSAY "EAC Agenda Custo      : "+cSimbMoeda+" " + Transform(_nEACAGC    ,"@E 999,999,999,999.99")		
	Endif	
	@ nLin+9 , 00 PSAY "Realizada             : "    + Transform(_nQtdRea   ,"@E 9,999,999.99")+" hrs " + "  "+cSimbMoeda+" " + Transform(_nVlrRea,"@E 999,999,999,999.99")
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+9 ,155 PSAY "EAC Linear Custo      : "+cSimbMoeda+" " + Transform(_nEACLC    ,"@E 999,999,999,999.99")		
	Endif	
    @ nLin+10, 00 PSAY "(+)Buffer (-)Backlog  : "    + Transform(_nHrBB     ,"@E 9,999,999.99")+" hrs " + "  "+cSimbMoeda+" " + Transform(_nVlBB  ,"@E 999,999,999,999.99") + "  " + If(Empty(_cExcBuffBack),"","(3)")
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+10, 85 PSAY "IMIT                  : "    + Transform(_nIMIT,"@E 9,999,999.99%")
		@ nLin+10,155 PSAY "EAC Tendencia Custo   : "+cSimbMoeda+" " + Transform(_nEACTC    ,"@E 999,999,999,999.99")		
	Endif
    @ nLin+11, 00 PSAY "Horas Abonadas        : "    + Transform(_nQtdAbo   ,"@E 9,999,999.99")+" hrs " + "  "+cSimbMoeda+" " + Transform(_nVlrAbo,"@E 999,999,999,999.99")
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+11, 85 PSAY "CPI                   : "    + Transform(_nCPI,"@E 9,999,999.99")
		@ nLin+11,155 PSAY "Margem Contrib. Agenda: "+cSimbMoeda+" " + Transform(_nMCAGV     ,"@E 999,999,999,999.99")		
		@ nLin+11,PCOL()+2 PSAY Transform(_nMCAGP,"@E 9,999,999.99%")
	Endif
    @ nLin+12, 00 PSAY "Realizada + Abonada   : "    + Transform(_nQtdReaAbo,"@E 9,999,999.99")+" hrs " + "  "+cSimbMoeda+" " + Transform(_nVlrReaAbo,"@E 999,999,999,999.99")
	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+12, 85 PSAY "SPI                   : "    + Transform(_nSPI,"@E 9,999,999.99")
		@ nLin+12,155 PSAY "Margem Contrib. Linear: "+cSimbMoeda+" " + Transform(_nMCLV     ,"@E 999,999,999,999.99")		
		@ nLin+12,PCOL()+2 PSAY Transform(_nMCLP,"@E 9,999,999.99%")
	Endif	

    @ nLin+13, 00 PSAY "Receita Apurada       : "    +Space(12)+"     " + "  "+cSimbMoeda+" " + Transform(_nRecSim,"@E 999,999,999,999.99")
	@ nLin+13, 85 PSAY "Marg. Contr. Estimatda: "    + Transform(_nMCPS,"@E 9,999,999.99%")

	//-- Por movimento - nao imprimir
	If lImpTudo
		@ nLin+13,155 PSAY "Margem Contr. Tendenc.: "+cSimbMoeda+" " + Transform(_nMCTV     ,"@E 999,999,999,999.99")		
		@ nLin+13,PCOL()+2 PSAY Transform(_nMCTP,"@E 9,999,999.99%")
	Endif	

    nLin+=14

	//-- RESULTADO DO PROJETO
	@ nLin  ,00 PSAY REPL("_",220)
	nLin+=1
	@ nLin  ,00 PSAY "=> DEMONSTRATIVO RESULTADO DO PROJETO"
	nLin++
	
Endif

aSort(_aSumario,,,{|x,y|  Subs(x[2],1,2) < Subs(y[2],1,2) })

cQuebra         :=  ""
nResultReceita  := 0
nResultCustos   := 0
nResultComissao := _aSumario[11][3] * -1

If ! lGeraArq
	nLin ++
Endif

For x := 1 To Len( _aSumario )
	
	If cQuebra <> Subs(_aSumario[x,2],1,1)
		cQuebra := Subs(_aSumario[x,2],1,1)
		If cQuebra == "A"
			cXCab := "A - RECEITA"
		ElseIf cQuebra == "B"
			cXCab := "B - CUSTOS"
		ElseIf cQuebra == "C"
			cXCab := "C - COMISSOES
		Endif
		If ! lGeraArq
			@ nLin, 03 PSAY cXCab
			nLin ++
		Endif
	EndIf
	
	If Subs(_aSumario[x,2],1,1) == "A"
		If ! lGeraArq
			@ nLin, 05			PSAY PADR(_aSumario[x,2],50)
			@ nLin, PCOL()+1	PSAY _aSumario[x,3] PICTURE "@E 999,999,999,999.99"
			If Subs(_aSumario[x,2],1,2) == "A1"  // FATURAMENTOS
				nLin ++
				@ nLin, 05 PSAY Space(13)+PADR("Fat. Horas",15)+Space(3)+Transform(nFatHoras,"@E 999,999,999,999.99")
				nLin ++
				@ nLin, 05 PSAY Space(13)+PADR("Fat. Despesas",15)+Space(3)+Transform(nFatDespD,"@E 999,999,999,999.99")
				nLin ++                                                    
				@ nLin, 05 PSAY Space(13)+PADR("Fat. Viagens",15)+Space(3)+Transform(nFatDespV,"@E 999,999,999,999.99")
				nLin ++
				@ nLin, 05 PSAY Space(13)+PADR("Fat. Avulsas",15)+Space(3)+Transform(nFatDespA,"@E 999,999,999,999.99")
			Endif
		Endif
		nResultReceita  += _aSumario[x,3]
	ElseIf Subs(_aSumario[x,2],1,1) == "B"
		If ! lGeraArq
			@ nLin, 05			PSAY PADR(_aSumario[x,2],50)
			@ nLin, PCOL()+1	PSAY _aSumario[x,3] PICTURE "@E 999,999,999,999.99"
			If Subs(_aSumario[x,2],1,2) == "B1"  // HORAS
				nLin ++
				@ nLin, 05 PSAY Space(13)+PADR("Horas Faturadas",15)+Space(3)+Transform(nNaoAbon,"@E 999,999,999,999.99")
				nLin ++
				@ nLin, 05 PSAY Space(13)+PADR("Horas Abono AR",15)+Space(3)+Transform(nAbonoAR,"@E 999,999,999,999.99")
				nLin ++                                                    
				@ nLin, 05 PSAY Space(13)+PADR("Horas Abono CR",15)+Space(3)+Transform(nAbonoCR,"@E 999,999,999,999.99")
				nLin ++
				@ nLin, 05 PSAY Space(13)+PADR("Horas Abono IP",15)+Space(3)+Transform(nAbonoIP,"@E 999,999,999,999.99")
			Endif
		Endif
		nResultCustos   += _aSumario[x,3]
	ElseIf Subs(_aSumario[x,2],1,1) == "C"
		If ! lGeraArq
			@ nLin, 05 			PSAY PADR(_aSumario[x,2],50)
			@ nLin, PCOL()+1	PSAY nResultComissao PICTURE "@E 999,999,999,999.99"
		Endif
	EndIf
	
	If ! lGeraArq
		nLin ++
	Endif
	
	y := x + 1
	If y > Len(_aSumario)
		cProx := "*"
	Else
		cProx := Subs(_aSumario[y,2],1,1)
	Endif
	
	If ! lGeraArq
		If cQuebra <> cProx
			If cQuebra == "A"
				@ nLin, 05			PSAY PADR("A  (=) TOTAL",50)
				@ nLin, PCOL()+1	PSAY nResultReceita  PICTURE "@E 999,999,999,999.99"
			ElseIf cQuebra == "B"
				@ nLin, 05			PSAY PADR("B  (=) TOTAL",50)
				@ nLin, PCOL()+1	PSAY nResultCustos   PICTURE "@E 999,999,999,999.99"
			ElseIf cQuebra == "C"
				@ nLin, 05			PSAY PADR("C  (=) TOTAL",50)
				@ nLin, PCOL()+1	PSAY nResultComissao PICTURE "@E 999,999,999,999.99"
			Endif
			nLin ++
		EndIf
	EndIf
	
Next x

nPerc1  := 0
nPerc2  := 0
nPerc3  := 0

nResul1 := nResultReceita + nResultCustos + nResultComissao
nResul2 := nResultReceita + nResultCustos
nResul3 := nResultReceita + nResultComissao

If nResultReceita <> 0
	nPerc1  := (Abs(nResul1) / Abs(nResultReceita)) * 100
	nPerc2  := (Abs(nResul2) / Abs(nResultReceita)) * 100
	nPerc3  := (Abs(nResul3) / Abs(nResultReceita)) * 100
Endif            

//-- ajusta sinal
If nResul1 < 0
	nPerc1 := nPerc1 * -1
Endif

If nResul2 < 0
	nPerc2 := nPerc2 * -1
Endif

If nResul3 < 0
	nPerc3 := nPerc3 * -1
Endif

//-- ajusta para nao estourar campo
If nPerc1 < -999999.99
	nPerc1 := -99.99  /// -999999.99
Endif	
If nPerc1 > 9999999.99
	nPerc1 := 99.99  /// 9999999.99
Endif	

If nPerc2 < -999999.99
	nPerc2 := -99.99  /// -999999.99
Endif	
If nPerc2 > 9999999.99
	nPerc2 := 99.99  /// 9999999.99
Endif	

If nPerc3 < -999999.99
	nPerc3 := -99.99  /// -999999.99
Endif	
If nPerc3 > 9999999.99
	nPerc3 := 99.99
Endif	

If lGeraArq .And. lOkIncRec
               
    //-- atualiza o registro atual
	dbSelectArea("DRE")
	RecLock("DRE",.F.)

	//-- DRE - RECEITA
	DRE->VLRECFAT	 := _aSumario[1,3]
	DRE->VLRECBUFBA	 := _aSumario[2,3]
	DRE->VLRECDESC	 := _aSumario[3,3]
	DRE->VLRECCUSFI	 := _aSumario[4,3]
	DRE->VLRECINADI	 := _aSumario[5,3]
	DRE->VLRECTOTAL	 := nResultReceita
	
	//-- DRE - FATURAMENTO DESPESAS	
	DRE->VLFATDH     := nFatHoras
	DRE->VLFATDD     := nFatDespD
	DRE->VLFATDA     := nFatDespA
	DRE->VLFATDV     := nFatDespV
	
	//-- DRE - CUSTOS
	DRE->VLCUSHORAS	 := _aSumario[6,3]
	DRE->VLCUSHRFAT  := nNaoAbon
	DRE->VLCUSHRAR   := nAbonoAR
	DRE->VLCUSHRCR   := nAbonoCR
	DRE->VLCUSHRIP   := nAbonoIP
	DRE->VLCUSKM	 := _aSumario[7,3]
	DRE->VLCUSMANUA	 := _aSumario[8,3]
	DRE->VLCUSAVULS	 := _aSumario[9,3]
	DRE->VLCUSVIAGE	 := _aSumario[10,3]
	DRE->VLCUSTOTAL	 := nResultCustos
	
	//-- DRE - COMISSOES
	DRE->VLCOMISSAO  := nResultComissao
	DRE->VLCOMTOTAL	 := nResultComissao
	
	//-- DRE - RESULTADOS
	DRE->VLRRES1     := nResul1
	DRE->PERCRES1    := nPerc1
	DRE->VLRRES2     := nResul2
	DRE->PERCRES2    := nPerc2
	DRE->VLRRES3     := nResul3
	DRE->PERCRES3    := nPerc3

	DRE->EXCBUFBAC   := _cExcBuffBack
	DRE->EXCFAT      := _cExcQFat

	DRE->(MsUnlock())
	
Else
	
	nLin ++
	@ nLin, 03			PSAY PADR("RESULTADO -> (A-B-C) RECEITA-CUSTOS-COMISSOES",50)
	@ nLin, PCOL()+3	PSAY nResul1 PICTURE "@E 999,999,999,999.99"
	@ nLin, PCOL()+3	PSAY nPerc1 PICTURE "@E 99,999.99%"
	
	nLin ++
	@ nLin, 03			PSAY PADR("RESULTADO -> (A-B)   RECEITA-CUSTOS",50)
	@ nLin, PCOL()+3	PSAY nResul2 PICTURE "@E 999,999,999,999.99"
	@ nLin, PCOL()+3	PSAY nPerc2 PICTURE "@E 99,999.99%"
	
	nLin ++
	@ nLin, 03			PSAY PADR("RESULTADO -> (A-C)   RECEITA-COMISSOES",50)
	@ nLin, PCOL()+3	PSAY nResul3 PICTURE "@E 999,999,999,999.99"
	@ nLin, PCOL()+3	PSAY nPerc3 PICTURE "@E 99,999.99%"

	cMsg1 := ""
	cMsg2 := ""
	cMsg3 := ""
	
	
Endif
     
//�����������������������������������������������������������������������������������������������������Ŀ
//�	Impressao dos dados analiticos                                                                      �
//�������������������������������������������������������������������������������������������������������

If lDREAnalitico .And. ! lResumo .And. ! lGeraArq
	
	Cabec(Titulo,Cabec1,Cabec2,NomeProg,Tamanho,nTipo)
	nLin := 5
	A50Cabecalho( .F. , .F. , .F. , nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT )
	nLin += 2


	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin   , 00 PSAY "=> TIME DE PROJETO"
	nLin+=1
	PE7->(DbSetOrder(1)) //PE7_FILIAL+PE7_PROJET+PE7_CODREC+PE7_PAPEL
	PE7->(MsSeek(FWxFilial("PE7")+PE5->PE5_PROJET))
	While !PE7->(EOF()) .AND. FWxFilial("PE7")+PE5->PE5_PROJET == PE7->(PE7_FILIAL+PE7_PROJET)
		A50Quebra(99, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

		@ nLin,010 PSAY "Recurso: "+PE7->PE7_CODREC+"-"+POSICIONE('RD0',1,XFILIAL('RD0')+PE7->PE7_CODREC,"RD0_NOME")+"     Papel: "+PE7->PE7_PAPEL+" - "+POSICIONE('ZX5',1,XFILIAL('ZX5')+"SRV001"+PE7->PE7_PAPEL,"ZX5_DESCRI")
		nLin++
		PE7->(DbSkip())
	EndDo

	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin   , 00 PSAY "=> PROPOSTA(S)"
	nLin+=1
	PE6->(DbSetOrder(1)) //PE6_FILIAL+PE6_PROJET+PE6_PROPOS+PE6_REVISA
	PE6->(MsSeek(FWxFilial("PE6")+PE5->PE5_PROJET))
	While !PE6->(EOF()) .AND. FWxFilial("PE6")+PE5->PE5_PROJET == PE6->(PE6_FILIAL+PE6_PROJET)
		A50Quebra(99, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

		@ nLin,010 PSAY "Proposta: "+PE6->PE6_PROPOS+"/"+PE6->PE6_REVISA+" - "+PE6->PE6_DESCR +"       Hrs.Or�adas: "+Transform(PE6->PE6_QTDORC,PesqPict("PE6","PE6_QTDORC"))+"      Vlr.Hrs.Orc.: "+Transform(PE6->PE6_VLRORC,PesqPict("PE6","PE6_VLRORC"))
		nLin++
		PE6->(DbSkip())
	EndDo

	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin   , 00 PSAY "=> TIME DE VENDAS"
	nLin+=1
	PE9->(DbSetOrder(1)) //PE9_FILIAL+PE9_PROJET+PE9_VEND+PE9_CODPAP+PE9_PROPOS+PE9_REVISA
	PE9->(MsSeek(FWxFilial("PE9")+PE5->PE5_PROJET))
	While !PE9->(EOF()) .AND. FWxFilial("PE9")+PE5->PE5_PROJET == PE9->(PE9_FILIAL+PE9_PROJET)
		A50Quebra(99, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

		@ nLin,010 PSAY "Vendedor: "+PE9->PE9_VEND+"-"+POSICIONE("SA3",1,XFILIAL("SA3")+PE9->PE9_VEND,"A3_NOME")+"     Papel: "+PE9->PE9_CODPAP+"-"+POSICIONE("SUM",1,XFILIAL("SUM")+PE9->PE9_CODPAP,"UM_DESC")+"    % Particip.: "+Transform(PE9->PE9_PERC,PesqPict("PE9","PE9_PERC"))+"     Proposta: "+PE9->PE9_PROPOS+"/"+PE9->PE9_REVISA
		nLin++
		PE9->(DbSkip())
	EndDo

	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin   , 00 PSAY "=> FRENTES DE ENTREGA"
	nLin+=1
	PE8->(DbSetOrder(1)) //PE8_FILIAL+PE8_PROJET+PE8_CODIGO+PE8_PRJPMS+PE8_EDT
	PE8->(MsSeek(FWxFilial("PE8")+PE5->PE5_PROJET))
	While !PE8->(EOF()) .AND. FWxFilial("PE8")+PE5->PE5_PROJET == PE8->(PE8_FILIAL+PE8_PROJET)
		A50Quebra(99, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

		@ nLin,010 PSAY "Frente: "+PE8->PE8_CODIGO+"  Descricao: "+PE8->PE8_DESCR+"   Qtde Hrs:"+Transform(PE8->PE8_QTDHRS,PesqPict("PE8","PE8_QTDHRS"))+"  Tipo:"+RetX3Combo( "PE8_TPFRT" , PE8->PE8_TPFRT )+"  Motivo:"+PE8->PE8_MOTIVO+"-"+Substr(Posicione("PE0",1,xFilial("PE0")+PE8->PE8_MOTIVO,"PE0_DESC"),1,35)+"   Resp: "+PE8->PE8_COORD+"-"+POSICIONE('RD0',1,XFILIAL('RD0')+PE8->PE8_COORD,"RD0_NOME")
		nLin++
		PE8->(DbSkip())
	EndDo

	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin   , 00 PSAY "=> PARCELAS DO PROJETO"
	nLin+=1
	PEA->(DbSetOrder(1)) //PEA_FILIAL+PEA_PROJET+PEA_PARCEL
	PEA->(MsSeek(FWxFilial("PEA")+PE5->PE5_PROJET))
	While !PEA->(EOF()) .AND. FWxFilial("PEA")+PE5->PE5_PROJET == PEA->(PEA_FILIAL+PEA_PROJET)
		A50Quebra(99, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

		@ nLin,000 PSAY "Parc.:"+PEA->PEA_PARCEL+" Status:"+PadR(RetX3Combo( "PEA_STATUS" , PEA->PEA_STATUS ),16)+"  Prod.:"+PEA->PEA_PRODUT+"  Qtde:"+Transform(PEA->PEA_QTDPAR,PesqPict("PEA","PEA_QTDPAR"))+" Cond.Pg:"+PEA->PEA_CODPAG+" Pedido:"+PEA->PEA_PEDIDO+" Doc:"+PEA->PEA_NUMFAT+"/"+PEA->PEA_SERFAT+"  Qt.Fat:"+Transform(PEA->PEA_QTDFAT,PesqPict("PEA","PEA_QTDFAT"))+"  Qt.Canc.:"+Transform(PEA->PEA_QTDCAN,PesqPict("PEA","PEA_QTDCAN"))
		nLin++
		PEA->(DbSkip())
	EndDo

	
//	@ nLin   , 00 PSAY REPL("_",220)
//	nLin++
//	@ nLin, 00			PSAY "=> DADOS ANALITICOS"
	
	// **********************************   A1 -> FATURAMENTO
	aSort(_aItemFaturado,,,{|x,y| x[1]+dtos(x[6]) < y[1]+dtos(y[6]) })
	
	nMenu  := 1
	
	nLin+=1
	lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin, 00			PSAY "=> FATURAMENTO"
	If	nLin > 64
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	Else
		lCabe := .F.
	EndIf
	
	// ********** Impressao
	If Len( _aItemFaturado ) <> 0
		
		nPedQuanti := 0
		nPedPrcVen := 0
		nPedValor  := 0
		nSD2Quanti := 0
		nSD2PrcVen := 0
		nSD2Valor  := 0
		nSD1Quanti := 0
		nSD1PrcVen := 0
		nSD1Valor  := 0
		
		If !lCabe
			A50Menu(nMenu)
		EndIf
		
		For x:= 1 To Len( _aItemFaturado )
			
			_aItemFaturado[x][7]  := Round(_aItemFaturado[x][7] * _nParticipacao,2)
			_aItemFaturado[x][9]  := Round(_aItemFaturado[x][9] * _nParticipacao,2)
			
			_aItemFaturado[x][13] := Round(_aItemFaturado[x][13] * _nParticipacao,2)
			_aItemFaturado[x][15] := Round(_aItemFaturado[x][15] * _nParticipacao,2)
			
			_aItemFaturado[x][19] := Round(_aItemFaturado[x][19] * _nParticipacao,2)
			_aItemFaturado[x][21] := Round(_aItemFaturado[x][21] * _nParticipacao,2)
			
			// ********** Impressao
			nLin ++
			@ nLin, 00     	PSAY _aItemFaturado[x,22]  
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,2]
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,3]
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,4]
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,5]
			@ nLin, PCOL()+1	PSAY Dtoc(_aItemFaturado[x,6])
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,7]  PICTURE "@E 99,999.99"
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,8]  PICTURE "@E 999,999.99"
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,9]  PICTURE "@E 9,999,999.99"
			@ nLin, PCOL()+2	PSAY _aItemFaturado[x,10]
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,11]
			@ nLin, PCOL()+1	PSAY Dtoc(_aItemFaturado[x,12])
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,13]  PICTURE "@E 999,999.99"
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,14]  PICTURE "@E 99,999,999.99"
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,15]  PICTURE "@E 999,999,999.99"
			@ nLin, PCOL()+2	PSAY _aItemFaturado[x,16]
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,17]
			@ nLin, PCOL()+1	PSAY Dtoc(_aItemFaturado[x,18])
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,19]  PICTURE "@E 999,999.99"
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,20]  PICTURE "@E 99,999,999.99"
			@ nLin, PCOL()+1	PSAY _aItemFaturado[x,21]  PICTURE "@E 999,999,999.99"
			
			nPedQuanti += _aItemFaturado[x,7]
			nPedPrcVen += _aItemFaturado[x,8]
			nPedValor  += _aItemFaturado[x,9]
			
			nSD2Quanti += _aItemFaturado[x,13]
			nSD2PrcVen += _aItemFaturado[x,14]
			nSD2Valor  += _aItemFaturado[x,15]
			
			nSD1Quanti += _aItemFaturado[x,19]
			nSD1PrcVen += _aItemFaturado[x,20]
			nSD1Valor  += _aItemFaturado[x,21]
			
			lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		Next x
		
		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@ nLin, 16			PSAY Subs("TOTAL",1,8)
		@ nLin, PCOL()+36	PSAY nPedQuanti  PICTURE "@E 99,999.99"
		@ nLin, PCOL()+1	PSAY nPedPrcVen  PICTURE "@E 999,999.99"
		@ nLin, PCOL()+1	PSAY nPedValor   PICTURE "@E 9,999,999.99"
		
		@ nLin, PCOL()+27	PSAY nSD2Quanti  PICTURE "@E 999,999.99"
		@ nLin, PCOL()+1	PSAY nSD2PrcVen  PICTURE "@E 99,999,999.99"
		@ nLin, PCOL()+1	PSAY nSD2Valor   PICTURE "@E 999,999,999.99"
		
		@ nLin, PCOL()+27	PSAY nSD1Quanti  PICTURE "@E 999,999.99"
		@ nLin, PCOL()+1	PSAY nSD1PrcVen  PICTURE "@E 99,999,999.99"
		@ nLin, PCOL()+1	PSAY nSD1Valor   PICTURE "@E 999,999,999.99"
	EndIf

	
	// **********************************   FATURAMENTO DE DESPESAS 
	
	aSort(_aFatDespesa,,,{|x,y| x[1]+(x[4]) < y[1]+(y[4]) })
	
	nMenu  := 10
	
	nLin+=1
	lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin, 00			PSAY "=> FATURAMENTO DE DESPESAS"
	If Len( _aFatDespesa ) <> 0
		nLin++
	Endif	
	If	nLin > 64
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	Else
		lCabe := .F.
	EndIf
	
	// ********** Impressao
	If Len( _aFatDespesa ) <> 0
		
		nDespA := 0
		nDespD := 0
		nDespV := 0
		nDespT := 0
		
		If !lCabe
			A50Menu(nMenu)
		EndIf
		
		For x:= 1 To Len( _aFatDespesa )
		
			cTipoD := _aFatDespesa[x,1]

			// ********** Impressao
			nLin ++
			@ nLin, 05			PSAY PADR(cTipoD,7)
			@ nLin, PCOL()+1	PSAY PADR(_aFatDespesa[x,2],6)
			@ nLin, PCOL()+1	PSAY PADR(_aFatDespesa[x,3],13)
			@ nLin, PCOL()+1	PSAY StoD(_aFatDespesa[x,4])
			@ nLin, PCOL()+1	PSAY _aFatDespesa[x,5]  PICTURE "@E 999,999,999.99"
			@ nLin, PCOL()+1	PSAY _aFatDespesa[x,6]  PICTURE "@E 999.99%"

			If Substr(cTipoD,1,1) == "A"			
				nDespA += _aFatDespesa[x,5]
			ElseIf Substr(cTipoD,1,1) == "D"			
				nDespD += _aFatDespesa[x,5]
			ElseIf Substr(cTipoD,1,1) == "V"			
				nDespV += _aFatDespesa[x,5]
			Endif	
			
			nDespT += _aFatDespesa[x,5]
			
			lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
			
			z:= x+1                   
			lQuebra := .F.
			If z > Len( _aFatDespesa )
				lQuebra := .T.
			Else	
				If _aFatDespesa[z,1] <> cTipoD
					lQuebra := .T.
				Endif
			Endif		
			
			If lQuebra
				nValor := 0
				If Substr(cTipoD,1,1) == "A"			
					nValor := nDespA
				ElseIf Substr(cTipoD,1,1) == "D"			
					nValor := nDespD
				ElseIf Substr(cTipoD,1,1) == "V"			
					nValor := nDespV
				Endif	
				nLin++
				@ nLin, 05			PSAY PADL("SUB-TOTAL",9)
				@ nLin, PCOL()+26	PSAY nValor PICTURE "@E 999,999,999.99"
				
			Endif
			
		Next x
		
		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@ nLin, 05			PSAY PADL("TOTAL",9)
		@ nLin, PCOL()+26	PSAY nDespT PICTURE "@E 999,999,999.99"

	EndIf
	
	// **********************************   A4 -> CUSTO FINANCEIRO
	nLin+=1
	lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin, 00			PSAY "=> CUSTO FINANCEIRO"
	
	// **********************************   B1 (-) ORDEM SERVICO
	aSort(_aOrdemServico,,,{|x,y| x[1]+Subs(Dtos(x[4]),1,6)+x[15]+Dtoc(x[4]) < y[1]+Subs(Dtos(y[4]),1,6)+y[15]+Dtoc(y[4]) })
	
	cQuebra:= ""
	nMenu  := 3
	
	nLin+=1
	lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin, 00 PSAY "=> ORDEM SERVICO"
	If	nLin > 64
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	Else
		lCabe := .F.
	EndIf
	
	If Len(_aOrdemServico) <> 0
		nSubHrTraslado:= 0
		nSubHrAbono   := 0
		nSubHrTotal   := 0
		nSubVlCustTras:= 0
		nSubVlTotal   := 0
		nSubVlReceita := 0
		nSubVlTraslado:= 0
		
		nTotHrTraslado:= 0
		nTotHrAbono   := 0
		nTotHrTotal   := 0
		nTotVlCustTras:= 0
		nTotVlTotal   := 0
		nTotVlReceita := 0
		nTotVlTraslado:= 0
		
		cQuebra       := Subs(Dtos(_aOrdemServico[1,4]),1,6)
		
		If !lCabe
			A50Menu(nMenu)
		EndIf
		
		For x := 1 to Len(_aOrdemServico)
			
			// ********** Impressao
			If cQuebra <> Subs(Dtos(_aOrdemServico[x,4]),1,6)
				nLin++
				lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)                  
				nLin++
				@ nLin, 44		    PSAY "SUB-TOTAL"
				@ nLin, PCOL()+6	PSAY RetHora(nSubHrTraslado,8)
				@ nLin, PCOL()+2	PSAY RetHora(nSubHrAbono,8)
				@ nLin, PCOL()+2	PSAY RetHora(nSubHrTotal,8)
				@ nLin, PCOL()+4	PSAY Transform(nSubVlCustTras,"@E 999,999,999.99")
				@ nLin, PCOL()+1	PSAY Transform(nSubVlTotal   ,"@E 9,999,999.99")
				@ nLin, PCOL()+1	PSAY Transform(nSubVlReceita ,"@E 9,999,999.99")
				@ nLin, PCOL()+1	PSAY Transform(nSubVlTraslado,"@E 9,999,999.99")
				nLin++
				
				nSubHrTraslado:= 0
				nSubHrAbono   := 0
				nSubHrTotal   := 0
				nSubVlCustTras:= 0
				nSubVlTotal   := 0
				nSubVlReceita := 0
				nSubVlTraslado:= 0
				cQuebra := Subs(Dtos(_aOrdemServico[x,4]),1,6)
			EndIf
			
			nLin ++
			@ nLin, 00			    PSAY _aOrdemServico[x,2]
			@ nLin, PCOL()+1		PSAY _aOrdemServico[x,3]
			@ nLin, PCOL()+1		PSAY Dtoc(_aOrdemServico[x,4])
			@ nLin, PCOL()+2		PSAY RetHora(HoratoInt(_aOrdemServico[x,5]),8)
			@ nLin, PCOL()+2		PSAY RetHora(HoratoInt(_aOrdemServico[x,6])+HoratoInt(_aOrdemServico[x,8]),8)
			@ nLin, PCOL()+2		PSAY RetHora(HoratoInt(_aOrdemServico[x,7]),8)
			@ nLin, PCOL()+2		PSAY RetHora(HoratoInt(_aOrdemServico[x,8]),8)
			@ nLin, PCOL()+2		PSAY RetHora(HoratoInt(_aOrdemServico[x,9]),8)
			@ nLin, PCOL()+1		PSAY Transform(_aOrdemServico[x,10],"@E 9,999.99")
			@ nLin, PCOL()+1		PSAY Transform(_aOrdemServico[x,11],"@E 9,999.99")
			@ nLin, PCOL()+1		PSAY Transform(_aOrdemServico[x,12],"@E 9,999,999.99")
			@ nLin, PCOL()			PSAY _aOrdemServico[x,22]
			@ nLin, PCOL()			PSAY Transform(IIf(_aOrdemServico[x,17] == "SIM", _aOrdemServico[x,13], 0),"@E 9,999,999.99")
			@ nLin, PCOL()+1		PSAY Transform(IIf(_aOrdemServico[x,17] == "SIM", _aOrdemServico[x,14], 0),"@E 9,999,999.99")
			@ nLin, PCOL()+2		PSAY _aOrdemServico[x,15]+" - "+Subs(Posicione("RD0",1,xFilial("RD0")+_aOrdemServico[x,15],"RD0_NOME"),1,23)
			@ nLin, PCOL()+2		PSAY _aOrdemServico[x,16]
			@ nLin, PCOL()+5		PSAY _aOrdemServico[x,17]
			@ nLin, PCOL()+3		PSAY _aOrdemServico[x,18]
			@ nLin, PCOL()+2		PSAY _aOrdemServico[x,19]
			@ nLin, PCOL()+1		PSAY Dtoc(_aOrdemServico[x,20])
			@ nLin, PCOL()+1		PSAY _aOrdemServico[x,21]
			
			nSubHrTraslado+= HoratoInt(_aOrdemServico[x,7])
			nSubHrAbono   += HoratoInt(_aOrdemServico[x,8])
			nSubHrTotal   += HoratoInt(_aOrdemServico[x,9])
			nSubVlCustTras+= _aOrdemServico[x,11]
			nSubVlTotal   += _aOrdemServico[x,12]
			If _aOrdemServico[x,17] == "SIM"
				nSubVlReceita += _aOrdemServico[x,13]
				nSubVlTraslado+= _aOrdemServico[x,14]
			EndIf
			nTotHrTraslado+= HoratoInt(_aOrdemServico[x,7])
			nTotHrAbono   += HoratoInt(_aOrdemServico[x,8])
			nTotHrTotal   += HoratoInt(_aOrdemServico[x,9])
			nTotVlCustTras+= _aOrdemServico[x,11]
			nTotVlTotal   += _aOrdemServico[x,12]
			If _aOrdemServico[x,17] == "SIM"
				nTotVlReceita += _aOrdemServico[x,13]
				nTotVlTraslado+= _aOrdemServico[x,14]
			EndIf
			                                                                                                      
			lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		Next
		
		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@ nLin, 44		    PSAY "SUB-TOTAL"
		@ nLin, PCOL()+6	PSAY RetHora(nSubHrTraslado,8)
		@ nLin, PCOL()+2	PSAY RetHora(nSubHrAbono,8)
		@ nLin, PCOL()+2	PSAY RetHora(nSubHrTotal,8)
		@ nLin, PCOL()+4	PSAY Transform(nSubVlCustTras,"@E 999,999,999.99")
		@ nLin, PCOL()+1	PSAY Transform(nSubVlTotal   ,"@E 9,999,999.99")
		@ nLin, PCOL()+1	PSAY Transform(nSubVlReceita ,"@E 9,999,999.99")
		@ nLin, PCOL()+1	PSAY Transform(nSubVlTraslado,"@E 9,999,999.99")
		
		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@ nLin, 44		    PSAY "TOTAL    "
		@ nLin, PCOL()+6	PSAY RetHora(nTotHrTraslado,8)
		@ nLin, PCOL()+2	PSAY RetHora(nTotHrAbono,8)
		@ nLin, PCOL()+2	PSAY RetHora(nTotHrTotal,8)
		@ nLin, PCOL()+4	PSAY Transform(nTotVlCustTras,"@E 999,999,999.99")
		@ nLin, PCOL()+1	PSAY Transform(nTotVlTotal   ,"@E 9,999,999.99")
		@ nLin, PCOL()+1	PSAY Transform(nTotVlReceita ,"@E 9,999,999.99")
		@ nLin, PCOL()+1	PSAY Transform(nTotVlTraslado,"@E 9,999,999.99")
	EndIf

	
	// **********************************   ORDEM SERVICO -> RESUMO POR MOTIVOS ORDEM SERVICO
	aSort(_aResumoMotivos,,,{|x,y| x[1] < y[1]})
	
	nMenu  := 8
	
	nLin+=1
	lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin, 00 PSAY "=> ORDEM SERVICO - RESUMO POR MOTIVO"
	If	nLin > 64
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	Else
		lCabe := .F.
	EndIf
	
	If Len(_aResumoMotivos) <> 0
		nTotHrTotal   := 0
		nTotCusTotal  := 0
		nTotRecTotal  := 0
		
		If !lCabe
			A50Menu(nMenu)
		EndIf
		
		For x := 1 to Len(_aResumoMotivos)
			
			// ********** Impressao
			nLin ++
			@ nLin, 05			    PSAY _aResumoMotivos[x,1]
			@ nLin, PCOL()+1		PSAY Posicione("PE0",1,xFilial("PE0")+_aResumoMotivos[x,1],"PE0_DESC") //Posicione("ZC6",1,xFilial("ZC6")+_aResumoMotivos[x,1],"ZC6_DESC")
			@ nLin, PCOL()+1		PSAY RetHora(_aResumoMotivos[x,2],8)
			@ nLin, PCOL()+1		PSAY Transform(_aResumoMotivos[x,3],"@E 99,999,999.99")
			@ nLin, PCOL()+1		PSAY Transform(_aResumoMotivos[x,4],"@E 99,999,999.99")
			
			nTotHrTotal   += _aResumoMotivos[x,2]
			nTotCusTotal  += _aResumoMotivos[x,3]
			nTotRecTotal  += _aResumoMotivos[x,4]
			
			lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

		Next
		
		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@ nLin, 05		    PSAY "TOTAL"
		@ nLin, PCOL()+54	PSAY RetHora(nTotHrTotal,8)
		@ nLin, PCOL()+1	PSAY Transform(nTotCusTotal,"@E 99,999,999.99")
		@ nLin, PCOL()+1	PSAY Transform(nTotRecTotal,"@E 99,999,999.99")

	EndIf


	// **********************************   DESPESAS DO PROJETO
	aSort(_aDespesasProjeto,,,{|x,y| x[1]+Subs(Dtos(x[3]),1,6)+x[4]+Dtoc(x[3]) < y[1]+Subs(Dtos(y[3]),1,6)+y[4]+Dtoc(y[3]) })
	
	cQuebra:= ""
	nMenu  := 2
	
	nLin+=1
	lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@ nLin, 00			PSAY "=> DESPESAS"
	If	nLin > 64
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	Else
		lCabe := .F.
	EndIf
	
	If Len(_aDespesasProjeto) <> 0
		
		nSubKm       := 0
		nSubKmValor  := 0
		nSubEstacion := 0
		nSubPedagio  := 0
		nSubZonaazul := 0
		nSubColetivo := 0
		nSubTaxi     := 0
		nSubRefeicoes:= 0
		nSubCelular  := 0
		nSubOutros   := 0
		nSubTotal    := 0
		
		nVlrKm       := 0
		nVlrKmValor  := 0
		nVlrEstacion := 0
		nVlrPedagio  := 0
		nVlrZonaazul := 0
		nVlrColetivo := 0
		nVlrTaxi     := 0
		nVlrRefeicoes:= 0
		nVlrCelular  := 0
		nVlrOutros   := 0
		nVlrTotal    := 0
		
		cQuebra      := Subs(Dtos(_aDespesasProjeto[1,3]),1,6)
		
		If !lCabe
			A50Menu(nMenu)
		EndIf
		
		For x := 1 to Len(_aDespesasProjeto)
			// ********** Impressao
			If cQuebra <> Subs(Dtos(_aDespesasProjeto[x,3]),1,6)
				nLin++
				lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
				nLin++
				@ nLin, 5			PSAY PADR("SUB-TOTAL",9)
				@ nLin, PCOL()+51	PSAY nSubKm        PICTURE "@E 999,999.9999"
				@ nLin, PCOL()+1	PSAY nSubKmValot   PICTURE "@E 9,999,999.99"
				@ nLin, PCOL()+5	PSAY nSubEstacion  PICTURE "@E 9,999,999.99"
				@ nLin, PCOL()+2	PSAY nSubPedagio   PICTURE "@E 9,999,999.99"
				@ nLin, PCOL()+2	PSAY nSubZonaazul  PICTURE "@E 9,999,999.99"
				@ nLin, PCOL()+2	PSAY nSubColetivo  PICTURE "@E 9,999,999.99"
				@ nLin, PCOL()+2	PSAY nSubTaxi      PICTURE "@E 9,999,999.99"
				@ nLin, PCOL()+2	PSAY nSubRefeicoes PICTURE "@E 9,999,999.99"
				@ nLin, PCOL()+2	PSAY nSubCelular   PICTURE "@E 9,999,999.99"
				@ nLin, PCOL()+2	PSAY nSubOutros    PICTURE "@E 9,999,999.99"
				@ nLin, PCOL()+2	PSAY nSubTotal     PICTURE "@E 9,999,999.99"
				nLin++
				
				nSubKm       := 0
				nSubKmValor  := 0
				nSubEstacion := 0
				nSubPedagio  := 0
				nSubZonaazul := 0
				nSubColetivo := 0
				nSubTaxi     := 0
				nSubRefeicoes:= 0
				nSubCelular  := 0
				nSubOutros   := 0
				nSubTotal    := 0
				
				cQuebra := Subs(Dtos(_aDespesasProjeto[x,3]),1,6)
			EndIf
			
			nLin ++
			@ nLin, 05			PSAY _aDespesasProjeto[x,2]
			@ nLin, PCOL()+1	PSAY Dtoc(_aDespesasProjeto[x,3])
			@ nLin, PCOL()+1	PSAY _aDespesasProjeto[x,4]+" - "+Subs(Posicione("RD0",1,xFilial("RD0")+_aDespesasProjeto[x,4],"RD0_NOME"),1,28)
			@ nLin, PCOL()+1	PSAY _aDespesasProjeto[x,5]  PICTURE "@E 999,999.9999"
			@ nLin, PCOL()+1	PSAY _aDespesasProjeto[x,6]  PICTURE "@E 9,999,999.99"
			@ nLin, PCOL()+5	PSAY _aDespesasProjeto[x,7]  PICTURE "@E 9,999,999.99"
			@ nLin, PCOL()+2	PSAY _aDespesasProjeto[x,8]  PICTURE "@E 9,999,999.99"
			@ nLin, PCOL()+2	PSAY _aDespesasProjeto[x,9]  PICTURE "@E 9,999,999.99"
			@ nLin, PCOL()+2	PSAY _aDespesasProjeto[x,10] PICTURE "@E 9,999,999.99"
			@ nLin, PCOL()+2	PSAY _aDespesasProjeto[x,11] PICTURE "@E 9,999,999.99"
			@ nLin, PCOL()+2	PSAY _aDespesasProjeto[x,12] PICTURE "@E 9,999,999.99"
			@ nLin, PCOL()+2	PSAY _aDespesasProjeto[x,13] PICTURE "@E 9,999,999.99"
			@ nLin, PCOL()+2	PSAY _aDespesasProjeto[x,14] PICTURE "@E 9,999,999.99"
			@ nLin, PCOL()+2	PSAY _aDespesasProjeto[x,6]+_aDespesasProjeto[x,7]+_aDespesasProjeto[x,8]+_aDespesasProjeto[x,9]+_aDespesasProjeto[x,10]+_aDespesasProjeto[x,11]+_aDespesasProjeto[x,12]+_aDespesasProjeto[x,13]+_aDespesasProjeto[x,14] PICTURE "@E 9,999,999.99"
			
			nSubKm       += _aDespesasProjeto[x,5]
			nSubKmValor  += _aDespesasProjeto[x,6]
			nSubEstacion += _aDespesasProjeto[x,7]
			nSubPedagio  += _aDespesasProjeto[x,8]
			nSubZonaazul += _aDespesasProjeto[x,9]
			nSubColetivo += _aDespesasProjeto[x,10]
			nSubTaxi     += _aDespesasProjeto[x,11]
			nSubRefeicoes+= _aDespesasProjeto[x,12]
			nSubCelular  += _aDespesasProjeto[x,13]
			nSubOutros   += _aDespesasProjeto[x,14]
			nSubTotal    += _aDespesasProjeto[x,6]+_aDespesasProjeto[x,7]+_aDespesasProjeto[x,8]+_aDespesasProjeto[x,9]+_aDespesasProjeto[x,10]+_aDespesasProjeto[x,11]+_aDespesasProjeto[x,12]+_aDespesasProjeto[x,13]+_aDespesasProjeto[x,14]
			
			nVlrKm       += _aDespesasProjeto[x,5]
			nVlrKmValor  += _aDespesasProjeto[x,6]
			nVlrEstacion += _aDespesasProjeto[x,7]
			nVlrPedagio  += _aDespesasProjeto[x,8]
			nVlrZonaazul += _aDespesasProjeto[x,9]
			nVlrColetivo += _aDespesasProjeto[x,10]
			nVlrTaxi     += _aDespesasProjeto[x,11]
			nVlrRefeicoes+= _aDespesasProjeto[x,12]
			nVlrCelular  += _aDespesasProjeto[x,13]
			nVlrOutros   += _aDespesasProjeto[x,14]
			nVlrTotal    += _aDespesasProjeto[x,6]+_aDespesasProjeto[x,7]+_aDespesasProjeto[x,8]+_aDespesasProjeto[x,9]+_aDespesasProjeto[x,10]+_aDespesasProjeto[x,11]+_aDespesasProjeto[x,12]+_aDespesasProjeto[x,13]+_aDespesasProjeto[x,14]
			
			lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		Next
		
		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@ nLin, 5			PSAY PADR("SUB-TOTAL",9)
		@ nLin, PCOL()+51	PSAY nSubKm        PICTURE "@E 999,999.9999"
		@ nLin, PCOL()+1	PSAY nSubKmValot   PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+5	PSAY nSubEstacion  PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nSubPedagio   PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nSubZonaazul  PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nSubColetivo  PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nSubTaxi      PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nSubRefeicoes PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nSubCelular   PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nSubOutros    PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nSubTotal     PICTURE "@E 9,999,999.99"
		
		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@ nLin, 05			PSAY PADR("TOTAL",9)
		@ nLin, PCOL()+51	PSAY nVlrKm        PICTURE "@E 999,999.9999"
		@ nLin, PCOL()+1	PSAY nVlrKmValot   PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+5	PSAY nVlrEstacion  PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nVlrPedagio   PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nVlrZonaazul  PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nVlrColetivo  PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nVlrTaxi      PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nVlrRefeicoes PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nVlrCelular   PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nVlrOutros    PICTURE "@E 9,999,999.99"
		@ nLin, PCOL()+2	PSAY nVlrTotal     PICTURE "@E 9,999,999.99"
	EndIf
	
	// **********************************   B4 (-) DESPESAS AVULSA
	aSort(_aDespAvulsa,,,{|x,y| x[7]+x[8]+Dtos(x[2])+x[3] < y[7]+y[8]+Dtos(y[2])+y[3] })
	
	nMenu       := 4
	nlin+=1
	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@NLIN, 00			PSAY "=> DESPESAS AVULSA"
	nLin++
	
	If	nLin > 64
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	Else
		lCabe := .F.
	EndIf
	
	If Len(_aDespAvulsa) <> 0

	    cQuebra     := ""
	    nQQuebraA   := 0
	    nVQuebraA   := 0
		nQtdeAvulsa := 0
		nVlrRAvulsa := 0
		nMenu       := 4
		
		If !lCabe
			A50Menu(nMenu)
		EndIf
		
		dbSelectArea("RD0")
		RD0->( dbSetOrder(1) )
		
		For x := 1 to Len(_aDespAvulsa)
			If x == 1
				cQuebra := _aDespAvulsa[x,7]+_aDespAvulsa[x,8]
			Endif	
			If _aDespAvulsa[x,7]+_aDespAvulsa[x,8] <> cQuebra
				nLin++
				lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
				nLin++
				@NLIN, 05			PSAY PADR("SUB-TOTAL",10)  
				@NLIN, PCOL()+159	PSAY nQQuebraA PICTURE "@E 999,999.9999"
				@NLIN, PCOL()+3		PSAY nVQuebraA PICTURE "@E 999,999,999.99"
				nLin ++
			    nQQuebraA := 0
	    		nVQuebraA := 0
	    		cQuebra   := _aDespAvulsa[x,7]+_aDespAvulsa[x,8]
			Endif
		
			// ********** Impressao
			nLin ++         
			@NLIN, 05			PSAY _aDespAvulsa[x,7]+" - "+Substr(Posicione("RD0",1,xFilial("RD0")+_aDespAvulsa[x,7],"RD0_NOME"),1,60)
			@NLIN, PCOL()+2		PSAY _aDespAvulsa[x,2]
			@NLIN, PCOL()+2		PSAY _aDespAvulsa[x,3]
			@NLIN, PCOL()+2		PSAY _aDespAvulsa[x,4]
			@NLIN, PCOL()+3		PSAY _aDespAvulsa[x,5] PICTURE "@E 999,999.9999"
			@NLIN, PCOL()+3		PSAY _aDespAvulsa[x,6] PICTURE "@E 999,999,999.99"
			lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
			
		    nQQuebraA   += _aDespAvulsa[x,5]
		    nVQuebraA   += _aDespAvulsa[x,6]

			nQtdeAvulsa += _aDespAvulsa[x,5]
			nVlrRAvulsa += _aDespAvulsa[x,6]

		Next
		
		nLin++
		@NLIN, 05			PSAY PADR("SUB-TOTAL",10)  
		@NLIN, PCOL()+159	PSAY nQQuebraA PICTURE "@E 999,999.9999"
		@NLIN, PCOL()+3		PSAY nVQuebraA PICTURE "@E 999,999,999.99"

		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@NLIN, 05			PSAY PADR("TOTAL",10)
		@NLIN, PCOL()+159	PSAY nQtdeAvulsa PICTURE "@E 999,999.9999"
		@NLIN, PCOL()+3		PSAY nVlrRAvulsa PICTURE "@E 999,999,999.99"

	EndIf
	
	// **********************************   B5 (-) DESPESAS VIAGEM
	aSort(_aDespViagem,,,{|x,y| x[7]+x[8]+Dtos(x[2])+x[3] < y[7]+y[8]+Dtos(y[2])+y[3] })
	
	nMenu       := 4
	nlin+=1
	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@NLIN, 00			PSAY "=> DESPESAS VIAGEM"
	nLin++
	
	If	nLin > 64
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	Else
		lCabe := .F.
	EndIf
	
	If Len(_aDespViagem) <> 0
		cQuebra     := ""
		nQQuebraV   := 0
		nVQuebraV   := 0
		nQtdeViagem := 0
		nVlrRViagem := 0
		nMenu       := 4
		
		If !lCabe
			A50Menu(nMenu)
		EndIf
		
		For x := 1 to Len(_aDespViagem)
			If x == 1
				cQuebra := _aDespViagem[x,7]+_aDespViagem[x,8]
			Endif	
			If _aDespViagem[x,7]+_aDespViagem[x,8] <> cQuebra
				nLin++
				lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
				nLin++
				@NLIN, 05			PSAY PADR("SUB-TOTAL",10)  
				@NLIN, PCOL()+159	PSAY nQQuebraV PICTURE "@E 999,999.9999"
				@NLIN, PCOL()+3		PSAY nVQuebraV PICTURE "@E 999,999,999.99"
				nLin ++
			    nQQuebraV := 0
	    		nVQuebraV := 0
	    		cQuebra   := _aDespViagem[x,7]+_aDespViagem[x,8]
			Endif

			// ********** Impressao
			nLin ++

			@NLIN, 05			PSAY _aDespViagem[x,7]+" - "+Substr(Posicione("RD0",1,xFilial("RD0")+_aDespViagem[x,7],"RD0_NOME"),1,60)
			@NLIN, PCOL()+2		PSAY _aDespViagem[x,2]
			@NLIN, PCOL()+2		PSAY _aDespViagem[x,3]
			@NLIN, PCOL()+2		PSAY _aDespViagem[x,4]
			@NLIN, PCOL()+3 	PSAY _aDespViagem[x,5] PICTURE "@E 999,999.9999"
			@NLIN, PCOL()+3		PSAY _aDespViagem[x,6] PICTURE "@E 999,999,999.99"
			lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
			
		    nQQuebraV   += _aDespViagem[x,5]
		    nVQuebraV   += _aDespViagem[x,6]

			nQtdeViagem += _aDespViagem[x,5]
			nVlrRViagem += _aDespViagem[x,6]
			
		Next
		
		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@NLIN, 05			PSAY PADR("SUB-TOTAL",10)  
		@NLIN, PCOL()+159	PSAY nQQuebraV PICTURE "@E 999,999.9999"
		@NLIN, PCOL()+3		PSAY nVQuebraV PICTURE "@E 999,999,999.99"

		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@NLIN, 05			PSAY PADR("TOTAL",10)
		@NLIN, PCOL()+159	PSAY nQtdeViagem PICTURE "@E 999,999.9999"
		@NLIN, PCOL()+3		PSAY nVlrRViagem PICTURE "@E 999,999,999.99"
		
	EndIf
	
	// **********************************   A5 -> PERDA POR INADIMPLENCIA  ***  TITULOS FINANCEIRO
	aSort(_aTitulos,,,{|x,y| x[1]+Dtos(x[2])+x[3] < y[1]+Dtos(y[2])+y[3]})
	
	nMenu     := 5
	nlin+=1
	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@NLIN, 00			PSAY "=> TITULOS FINANCEIRO"
	
	If	nLin > 64
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	Else
		lCabe := .F.
	EndIf
	
	If Len(_aTitulos) <> 0
		nVlrTotal := 0
		nVlrSaldo := 0
		nVlrPDD   := 0
		nMenu     := 5
		
		If !lCabe
			A50Menu(nMenu)
		EndIf
		
		For x := 1 to Len(_aTitulos)
			// ********** Impressao
			nLin ++
			
			_aTitulos[x,6] := Round(_aTitulos[x,6] * _nParticipacao,2)
			_aTitulos[x,7] := Round(_aTitulos[x,7] * _nParticipacao,2)
			_aTitulos[x,8] := Round(_aTitulos[x,8] * _nParticipacao,2)
			
			@NLIN, 05			PSAY Dtoc(_aTitulos[x,2])
			@NLIN, PCOL()+2		PSAY _aTitulos[x,3]
			@NLIN, PCOL()+5		PSAY _aTitulos[x,4]
			@NLIN, PCOL()+4		PSAY Dtoc(_aTitulos[x,5])
			@NLIN, PCOL()+7		PSAY _aTitulos[x,6] PICTURE "@E 999,999,999.99"
			@NLIN, PCOL()+3		PSAY _aTitulos[x,7] PICTURE "@E 999,999,999.99"
			@NLIN, PCOL()+3		PSAY _aTitulos[x,8] PICTURE "@E 999,999,999.99"
			lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
			                  
			nVlrTotal += _aTitulos[x,6]
			nVlrSaldo += _aTitulos[x,7]
			nVlrPDD   += _aTitulos[x,8]

		Next
		
		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@NLIN, 05			PSAY PADR("TOTAL",8)
		@NLIN, PCOL()+50	PSAY nVlrTotal  PICTURE "@E 999,999,999.99"
		@NLIN, PCOL()+3		PSAY nVlrSaldo  PICTURE "@E 999,999,999.99"
		@NLIN, PCOL()+3		PSAY nVlrPDD    PICTURE "@E 999,999,999.99"
	EndIf
	
	// **********************************   "C1 (-) COMISSOES DIRETAS "
	nlin+=1
	@ nLin   , 00 PSAY REPL("_",220)
	nLin+=1
	@NLIN, 00			PSAY "=> COMISSOES DIRETAS"
    nLin++
	
	If	nLin > 64
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
	Else
		lCabe := .F.
	EndIf
	
	If Len(_aComissoes) <> 0
		nValComissao := 0
		nValBaseComi := 0
		nPercentComi := 0
		
		// ********** Impressao
		nLin ++
		@NLIN, 10			PSAY "COMISSAO"
		@NLIN, PCOL()+2		PSAY _aComissoes[1] PICTURE "@E 999,999,999.99"
		@NLIN, PCOL()+10	PSAY "FATURADO"
		@NLIN, PCOL()+2		PSAY _aComissoes[2] PICTURE "@E 999,999,999.99"
		@NLIN, PCOL()+10	PSAY "% COMISSAO"
		@NLIN, PCOL()+2		PSAY _aComissoes[3] PICTURE "@E 999.9999"
		
		// **********  COMISSAO POR VENDEDOR/PRODUTO
		aSort(_aComissoes[5],,,{|x,y| x[1]+x[2] < y[1]+y[2] })
		
		nMenu := 7
		nLin+=1
		@ nLin   , 00 PSAY REPL("_",220)
		nLin+=1
		@NLIN, 05 		PSAY "COMISSAO POR VENDEDOR/PRODUTO"
		
		If	nLin > 64
			lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		Else
			nLin++
			lCabe := .F.
		EndIf
		
		If !lCabe
			nMenu := 7
			A50Menu(nMenu)
		EndIf
		
		For _n2 := 1 to Len(_aComissoes[5])
			nLin ++
			@NLIN, 10		    PSAY _aComissoes[5,_n2,1]
			@NLIN, PCOL()+3		PSAY _aComissoes[5,_n2,2]
			@NLIN, PCOL()+3		PSAY _aComissoes[5,_n2,5] PICTURE "@E 999,999,999.99"
			@NLIN, PCOL()+3		PSAY _aComissoes[5,_n2,4] PICTURE "@E 999,999,999.99"
			@NLIN, PCOL()+3		PSAY _aComissoes[5,_n2,3] PICTURE "@E 999,999,999.99"
			@NLIN, PCOL()+10	PSAY _aComissoes[5,_n2,6]
			lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
			
			nValComissao += _aComissoes[5,_n2,5]
			nValBaseComi += _aComissoes[5,_n2,4]
			nPercentComi += _aComissoes[5,_n2,3]
		Next
		
		nLin++
		lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)
		nLin++
		@NLIN, 10		    PSAY "TOTAL"
		@NLIN, PCOL()+22	PSAY nValComissao PICTURE "@E 999,999,999.99"
		@NLIN, PCOL()+3		PSAY nValBaseComi PICTURE "@E 999,999,999.99"
		@NLIN, PCOL()+3		PSAY nPercentComi PICTURE "@E 999,999,999.99"
	EndIf
	
EndIf


Return



/*/{Protheus.doc} TSR016Dados
Levantamento de Dados do DRE
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
Static Function TSR016Dados()
Local aArea			:= GetArea()
Local aImp			:= {}
Local aModeloSumario 	:= {}
Local aSumarioProjeto	:= {}

Local cQuery			:= ""
Local cStatPE5		:= ""
Local cChar 			:= ""
Local cUnSrv  		:= ""
Local cCpoVl			:= ""
Local cCdEstac		:= GetMV("TI_TSR0161",,"000002")  //Codigo de despesa de estacionamento
Local cCdPedagio		:= GetMV("TI_TSR0162",,"000002")  //Codigo de despesa de  Pedagio
Local cCdZonaAzul		:= GetMV("TI_TSR0163",,"000003")  //Codigo de despesa de  Zona Azul
Local cCdOnibus		:= GetMV("TI_TSR0164",,"000004")  //Codigo de despesa de  Transporte coletivo
Local cCdTaxi		:= GetMV("TI_TSR0165",,"000005")  //Codigo de despesa de  Taxi
Local cCdAlimentacao	:= GetMV("TI_TSR0166",,"000006")  //Codigo de despesa de  Refeicoes
Local cCdCelular		:= GetMV("TI_TSR0167",,"000007")  //Codigo de despesa de  Celular
Local cTpSum			:= ""
Local cMoeda			:= ""
Local cUnSrvTec		:= ""
Local cUnSrvPrj		:= ""
Local cDBMS          := Upper(TCGETDB())
Local cNmDesp		:= ""

Local nTot			:= 0
Local nX				:= 0
Local nPos			:= 0
Local nValorR		:= 0
Local nQtdPrj        := 0
Local nHrBuffAnt 		:= 0
Local nHrBackAnt 		:= 0 
Local nVlBuffAnt 		:= 0
Local nVlBackAnt 		:= 0                                                    
Local nHrBuffAtu 		:= 0
Local nHrBackAtu 		:= 0 
Local nVlBuffAtu 		:= 0
Local nVlBackAtu 		:= 0
Local nHrMedBuff     := 0
Local nHrMedBack     := 0
Local nW				:= 0
Local lDW50ESI        := ExistBlock("DW50ESI")
Local lDW50COMIS	  := ExistBlock("lDW50COMIS")

Local lPropComis     := GetMv("TI_#PCOM50",,.T.)
Local lCalcRV		:= .F. 
Local lFranquia 		:= U_Franquia(cEmpAnt+cFilAnt)

//���������������������������������������������������������������������Ŀ
//� Tratamento de parametros                                            �
//�����������������������������������������������������������������������
If MV_PAR07 == 1 //"Ativo"
	cStatPE5 := "1"
ElseIf MV_PAR07 == 2 // "Encerrado",
	cStatPE5 := "2"
ElseIf MV_PAR07 == 3 // "Suspenso"
	cStatPE5 := "3"
ElseIf MV_PAR07 == 4 // "Bloqueado"
	cStatPE5 := "5"
EndIf

cChar := ""
For nX:=1 To Len(MV_PAR12)
	If ! ( Substr(MV_PAR12,nX,1) $ "0123456789ABCDEFGHIJKLMNOPQRSTUVWYXZ" )
		cChar := Substr(MV_PAR12,nX,1)
		Exit
	Endif
Next nX
cUnSrv  := IIF(Empty(MV_PAR12), "", FormatIn(AllTrim(MV_PAR12),cChar))

nMoedaRel		:= MV_PAR13
lConvMoeda	 := (MV_PAR14 == 1)
nTpRefRep := 1



//���������������������������������������������������������������������Ŀ
//� Iniciando vetor aSumario                                            �
//�����������������������������������������������������������������������
AADD( aModeloSumario , { "A - RECEITA" , "A1 (+) Faturamentos"                                , 0 } )
AADD( aModeloSumario , { "A - RECEITA" , "A2 (+/-) Buffer/Backlog"                            , 0 } )
AADD( aModeloSumario , { "A - RECEITA" , "A3 (-) Desconto/Cancelamento "                      , 0 } )
AADD( aModeloSumario , { "A - RECEITA" , "A4 (-) Custo Financeiro "                           , 0 } )
AADD( aModeloSumario , { "A - RECEITA" , "A5 (-) Perda por Inadimplencia "                    , 0 } )

AADD( aModeloSumario , { "B - CUSTOS"  , "B1 (-) Horas "                                      , 0 } )
AADD( aModeloSumario , { "B - CUSTOS"  , "B2 (-) Despesas Atendimento - km padrao "           , 0 } )
AADD( aModeloSumario , { "B - CUSTOS"  , "B3 (-) Despesas Atendimento - digitacao manual "    , 0 } )
AADD( aModeloSumario , { "B - CUSTOS"  , "B4 (-) Despesas Avulsas"                            , 0 } )
AADD( aModeloSumario , { "B - CUSTOS"  , "B5 (-) Despesas Viagens"                            , 0 } )

AADD( aModeloSumario , { "C - COMISSOES", "C1 (-) Comissoes Diretas "                         , 0 } )

//���������������������������������������������������������������������Ŀ
//� Pesquisa projetos                                                   �
//�����������������������������������������������������������������������
cQuery := " SELECT PE5.R_E_C_N_O_ AS RECPE5 "
cQuery += " FROM " + RetSQLName("PE5") + " PE5 "
cQuery += " WHERE PE5.D_E_L_E_T_ = '' "
cQuery += " 	AND PE5_FILIAL = '" + FWxFilial("PE5") + "' "
cQuery += " 	AND PE5_CLIENT BETWEEN '" + MV_PAR01 + "'  AND '" + MV_PAR02 + "' "
cQuery += " 	AND PE5_PROJET BETWEEN '" + MV_PAR05 + "'  AND '" + MV_PAR06 + "' "
cQuery += " 	AND EXISTS (SELECT PE8.R_E_C_N_O_ FROM " + RetSQLName("PE8") + " PE8 WHERE PE8.D_E_L_E_T_ = '' AND PE8_FILIAL = '" + FWxFilial("PE8") + "' AND PE8_PROJET = PE5_PROJET AND PE8_COORD BETWEEN '" + MV_PAR03 + "' AND '" + MV_PAR04 + "')
If !Empty(cStatPE5)
	cQuery += " 	AND PE5_STATUS = '" + cStatPE5 + "' "
EndIf
cQuery += " 	AND PE5_EMISSA BETWEEN '" + DtoS(MV_PAR08) + "'  AND '" + DtoS(MV_PAR09) + "' "
cQuery += " 	AND PE5_DTENCR BETWEEN '" + DtoS(MV_PAR10) + "'  AND '" + DtoS(MV_PAR11) + "' "
If !Empty(cUnSrv) .OR. U_Franquia(cEmpAnt+cFilAnt)
	cQuery += " AND PE5_UNSRV IN " + cUnSrv + " "
EndIf
cQuery += " 	AND PE5_MOEDA = '" + Alltrim(Str(nMoedaRel)) + "' "
cQuery += " 	AND PE5_CODSEG BETWEEN '" + MV_PAR15 + "'  AND '" + MV_PAR16 + "' "
cQuery += " ORDER BY PE5_CLIENT, PE5_LOJA, PE5_PROJET "

cQuery := ChangeQuery(cQuery)
MPSysOpenQuery(cQuery,"TSR016DRE")

Count to nTot
ProcRegua(nTot)
TSR016DRE->(DbGoTop())
While !TSR016DRE->(EOF())
	PE5->(DbGoTo(TSR016DRE->RECPE5))
	IncProc("Analisando projeto : "+PE5->PE5_CLIENT+"/"+PE5->PE5_PROJET)

	AAdd(aImp, PE5->(recno()) )
	cChaveProjeto		:= PE5->PE5_PROJET
	nQtdFat 			:= 0
	nQtdRea			:= 0
	nQtdAbo			:= 0
	nVlrRepasse		:= 0
	nParcelaValor		:= 0
	nOrcamento		:= 0
	nValorHMedio		:= 0
	aFatDespesa		:= {}
	nDespCobrada		:= 0
	aSumario     	:= AClone( aModeloSumario )  //  Iniciando vetor aSumario
	aDespesasProjeto	:= {}
	cUnSrvPrj			:= PE5->PE5_UNSRV
	nParticipacao		:= 1
	cStatus			:= RetX3Combo( "PE5_STATUS" , PE5->PE5_STATUS )
	aItemFaturado  	:= {}
	aCabecDRE		    := {}
	aOrdemServico  	:= {}
	aResumoMotivos	:= {}
	aDespAvulsa    	:= {}
	aDespViagem      	:= {}
	aComissoes 		:= {}
	aComissPai       := {}
	aTitulos			:= {}
	cChavePrjFat		:= ""
	cExcVMed         := ""
	cExcQFat       	:= ""
	cExcBuffBack     	:= ""
	cExceto          := ""
	nHrsExc          := 0
	nValExc          := 0
	nBuffExc         := 0
	nBackExc         := 0
	nQtdPrj ++
	nTrasCobrado      := 0
	nTrasIncorrido    := 0
	nDespIncorrida    := 0
	nFatorComiss	    := 0
	nVlrFat 			:= 0
	nQtdDev 			:= 0
	nVlrDev 			:= 0
	nQtdFatLiq       	:= 0
	nVlrFatLiq       := 0
	_dDataIni        := CtoD("")
  	_dDataFim       	:= CtoD("")
	nLimRNP      	:= PE5->PE5_LIMRNP
	nHrSuspensas 		:= PE5->PE5_HRSUSP
	
	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Dados das parcelas                                                                                  �
	//�������������������������������������������������������������������������������������������������������
	PEA->(DbsetOrder(1)) //PEA_FILIAL+PEA_PROJET+PEA_PARCEL
	PEA->(MsSeek(FWxFilial("PEA")+PE5->PE5_PROJET))

	//-- ponto de entrada para definir regra para determiniar se o recurso eh terceiro ou nao
		//-- utilizado no DRE
	lEhTerceiro := .F.             
	If lDW50ESI
		lEhTerceiro := ExecBlock("DW50ESI",.F.,.F.,{PF9->PF9_CODTEC})
	Else
		lEhTerceiro := IsTerceiro(PF9->PF9_CODTEC)
	Endif	

	While !PEA->(EOF()) .AND. FWxFilial("PEA")+PE5->PE5_PROJET == PEA->(PEA_FILIAL+PEA_PROJET) 

		cMoeda 	:= Alltrim(PEA->PEA_MOEFAT)
		If cMoeda $ "1,2,3,4,5,6,7"
			cCpoVl := "PEA_VFMOE"+cMoeda
		Else	
			cCpoVl := "PEA_VFMOE1"
		Endif	

		nOrcamento  	+= PEA->PEA_QTDPAR
		nParcelaValor	+= &("PEA->"+cCpoVl)
		
		PEA->(DbSkip())
	EndDo
	nValorHMedio := nParcelaValor / nOrcamento

	//-- mudanca de conceito -> horas orcadas sempre sao digitadas no projeto CFP
	If PE5->PE5_QTDORC <> 0
		nOrcamento := PE5->PE5_QTDORC
	Endif

	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Referencia de repasse                                                                               �
	//�������������������������������������������������������������������������������������������������������
	If nTpRefRep == 1 //TOTVS
		
		nVlrRepasse := 0
		
	ElseIf nTpRefRep == 2 //-- Referencia Dono do projeto
		
		nVlrRepasse := 0
		
	ElseIf nTpRefRep == 3 		//-- Referencia Contratado
		//-- busca valor negociado para o repasse
		nVlrRepasse := nValorHMedio
		//-- o valor hora medio passa a ser o valor de repasse para valorizar o DRE
		nValorHMedio := nVlrRepasse
	Endif

	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Dados de OS                                                                                         �
	//�������������������������������������������������������������������������������������������������������
	cQuery := " SELECT PF9.R_E_C_N_O_ AS RECPF9, PE0.R_E_C_N_O_ AS RECPE0, RD0.R_E_C_N_O_ AS RECRD0 "
	cQuery += " FROM " + RetSQLName("PF9") + " PF9 "
	cQuery += " INNER JOIN " + RetSQLName("RD0") + " RD0 ON RD0.D_E_L_E_T_ = '' AND RD0_FILIAL = '" + FWxFilial("RD0") + "' AND RD0_CODIGO = PF9_CODTEC "
	cQuery += " LEFT JOIN " + RetSQLName("PE0") + " PE0 ON PE0.D_E_L_E_T_ = '' AND PE0_FILIAL = '" + FWxFilial("PE0") + "' AND PE0_COD = PF9_CODMOT "
	cQuery += " WHERE PF9.D_E_L_E_T_ = '' "
	cQuery += " AND PF9_FILIAL = '" + FWxFilial("PE5") + "' "
	cQuery += " AND PF9_PROJET = '" + PE5->PE5_PROJET + "' "
	cQuery += " AND PF9_STATUS = '3' "
	cQuery := ChangeQuery(cQuery)

	MPSysOpenQuery(cQuery,"TSR016OS")

	
	While !TSR016OS->(EOF())
		PF9->(DbGoTo(TSR016OS->RECPF9))
		PE0->(DbGoTo(TSR016OS->RECPE0))
		RD0->(DbGoTo(TSR016OS->RECRD0))

		cUnSrvTec := Alltrim(RD0->RD0_XUNSRV)


		If Empty(_dDataIni)
			_dDataIni := PF9->PF9_DATA
		Endif
		If Empty(_dDataFim)
			_dDataFim := PF9->PF9_DATA
		Endif
		If PF9->PF9_DATA < _dDataIni
			_dDataIni := PF9->PF9_DATA
		Endif
		If PF9->PF9_DATA > _dDataFim
			_dDataFim := PF9->PF9_DATA
		Endif


		lTemAbono := IIf(PE0->PE0_TPABON <> "1", .T., .F.)
		cFatura   := IIf(PE0->PE0_DEBPRJ == "S","SIM", "NAO")
		cPremio   := IIf(PE0->PE0_CLASOS $ "1,5","SIM","NAO")
		cEhVlrRepas := " "
		
		nQtdRea += PF9->PF9_TOTAL
		If lTemAbono
			nQtdAbo += PF9->PF9_TOTAL
		EndIf

		

		//-- Referencia TOTVS
		//--    custo = apurado no DRE
		If nTpRefRep == 1    
		                     
			cEhVlrRepas := " "   
			nValorR     := 0
	
		//-- Referencia Dono do projeto
		//--    custo = apurada no DRE, mas quando tiver contratacao pega valor de repasse
		ElseIf nTpRefRep == 2    
	
			//-- unidade de servico diferente da unidade de servico do projeto
			If cUnSrvTec <> cUnSrvPrj              
				//-- busca valor negociado para o repasse quando o tecnico for um SI deve ser informado o valor de contrato.
				//-- Egidio JR - 15/09/14
//				If nZCTVRepas > 0 .And. Empty(Alltrim(cCodTerc))
//					cEhVlrRepas := "*"   
//					nValorR     := nZCTVRepas
//				Else 	        
					cEhVlrRepas := " "   
					nValorR     := 0
//				Endif	
			Else 	            
				cEhVlrRepas := " "   
				nValorR     := 0
			Endif			
		//-- Referencia Contratado
		//--    custo = apurada no DRE, mas quando tiver contratacao pega valor de repasse
		ElseIf nTpRefRep == 3    
	
			//-- unidade de servico for diferente da unidade de servico responsavel
			If cUnSrvTec <> cUnSrvPrj //cUnSerResp
				cEhVlrRepas := "*"   
				nValorR     := nZCTVRepaQ
			Else 	        
				cEhVlrRepas := " "   
				nValorR     := 0	
			Endif	
		
		Endif


		nCustoHora := U_RetCusHr(PF9->PF9_DATA, PF9->PF9_CODTEC, PF9->PF9_CUSTO, PF9->PF9_3VLHOR, PF9->PF9_TOTAL, PF9->PF9_CODMOT, lCalcRV, nValorR, lEhTerceiro)

		AAdd( aOrdemServico, { cChaveProjeto ,;
								PF9->PF9_IDOS ,;
								PF9->PF9_PRODUT ,;
								PF9->PF9_DATA ,;
								PF9->PF9_HRINI ,;
								PF9->PF9_HRFIM ,;
								PF9->PF9_HRTRA ,;
								PF9->PF9_HRABN ,;
								PF9->PF9_HRTOT  ,;
								(nCustoHora*-1) ,;
								Round((HoratoInt(PF9->PF9_HRTRA)*nCustoHora)*-1,2) ,;
								Round((PF9->PF9_TOTAL*nCustoHora)*-1,2) ,;
								Round(PF9->PF9_TOTAL*nValorHMedio,2) ,;
								Round(HoratoInt(PF9->PF9_HRTRA)*nValorHMedio,2) ,;
								PF9->PF9_CODTEC ,;
								PF9->PF9_CODMOT ,;
								cFatura ,;
								cPremio ,;
								Space(6) ,; //PF9->PF9_PEDIDO ,;
								CtoD("") ,; //PF9->PF9_DTDIGIT ,;
								PF9->PF9_CARGO  ,;
								cEhVlrRepas })


		//********** Sumarizando -> ORDEM SERVICO
		nPos := Ascan( aSumario , {|x| Substr(x[2],1,2) ==  "B1" })
		If	nPos <> 0
			aSumario[nPos][3] += Round(( PF9->PF9_TOTAL * nCustoHora )*-1,2)
		EndIf

		y := AScan(aResumoMotivos,{|z| z[1]==PF9->PF9_CODMOT})
		
		If y == 0
		
			AAdd(aResumoMotivos, { PF9->PF9_CODMOT ,; 
			                        PF9->PF9_TOTAL ,;
			                        Round((PF9->PF9_TOTAL*nCustoHora)*-1,2) ,;
			                        IIf(cFatura == "SIM", Round(PF9->PF9_TOTAL*nValorHMedio,2), 0) ,;
			                        PE0->PE0_TPABON })

		Else                    
		
			aResumoMotivos[y,2] += PF9->PF9_TOTAL
			aResumoMotivos[y,3] += Round((PF9->PF9_TOTAL*nCustoHora)*-1,2)
			aResumoMotivos[y,4] += IIf(cFatura == "SIM", Round(PF9->PF9_TOTAL*nValorHMedio,2), 0)
		
		Endif	
											
		nTrasCobrado   += Iif(cFatura == "SIM",Round(HoraToInt(PF9->PF9_HRTRA)*nValorHMedio,2),0)
		nTrasIncorrido += Round((HoraToInt(PF9->PF9_HRTRA)*nCustoHora)*-1,2)

		//�����������������������������������������������������������������������������������������������������Ŀ
		//�	Despesas de OS                                                                                      �
		//�������������������������������������������������������������������������������������������������������
		cQuery := " SELECT PFM.R_E_C_N_O_ AS RECPFM, PFG.R_E_C_N_O_ AS RECPFG " 
		cQuery += " FROM " + RetSQLName("PFM") + " PFM " 
		cQuery += " INNER JOIN " + RetSQLName("PFG") + " PFG ON PFG.D_E_L_E_T_ = '' AND PFG_FILIAL = '" + FWxFilial("PFG") + "' AND PFG_CODIGO = PFM_CODDES "
		cQuery += " WHERE PFM.D_E_L_E_T_ = '' "
		cQuery += " AND PFM_FILIAL = '" + FWxFilial("PFM") + "' "
		cQuery += " AND PFM_IDOS = '"+PF9->PF9_IDOS+"' "
		cQuery := ChangeQuery(cQuery)

		MPSysOpenQuery(cQuery,"TSR016PFM")
		
	
		While !TSR016PFM->(EOF())
			PFM->(DbGoTo(TSR016PFM->RECPFM))
			PFG->(DbGoTo(TSR016PFM->RECPFG))
			
			If PFG->PFG_TIPO = '1'
				nVlKM := PFM->PFM_VLUNIT
			Else
				nVlKM := 0
			EndIf

			nPos := Ascan( aDespesasProjeto , {|x| x[1]+Dtoc(x[3])+x[2]+x[4] ==  cChaveProjeto+Dtoc(PFM->PFM_DATA)+PFM->PFM_IDOS+PFM->PFM_TECNIC })
			If	nPos == 0
				AAdd( aDespesasProjeto, { cChaveProjeto ,;
											PFM->PFM_IDOS ,;
											PFM->PFM_DATA ,;
											PFM->PFM_TECNIC ,;
											0 ,;
											0 ,;
											0 ,;
											0 ,;
											0 ,;
											0 ,;
											0 ,;
											0 ,;
											0 ,;
											0 })
				nPos := Len(aDespesasProjeto)
			EndIf

			nDespIncorrida +=(PFM->PFM_VLDESP*-1)
			If PFG->PFG_TIPO = '1'
				aDespesasProjeto[nPos][5] += PFM->PFM_QUANT
				aDespesasProjeto[nPos][6] += (PFM->PFM_VLDESP*-1)
				cTpSum	:= "B2"
			Else
				If PFM->PFM_CODDES == cCdEstac  // Estacionamento
					aDespesasProjeto[nPos][7]  += (PFM->PFM_VLDESP*-1)
				ElseIf PFM->PFM_CODDES == cCdPedagio // Pedagio
					aDespesasProjeto[nPos][8]  += (PFM->PFM_VLDESP*-1)
				ElseIf PFM->PFM_CODDES == cCdZonaAzul  // Zona Azul
					aDespesasProjeto[nPos][9]  += (PFM->PFM_VLDESP*-1)
				ElseIf PFM->PFM_CODDES == cCdOnibus  // Transporte coletivo
					aDespesasProjeto[nPos][10]  += (PFM->PFM_VLDESP*-1)
				ElseIf PFM->PFM_CODDES == cCdTaxi  // Taxi
					aDespesasProjeto[nPos][11]  += (PFM->PFM_VLDESP*-1)
				ElseIf PFM->PFM_CODDES == cCdAlimentacao  // Refeicoes
					aDespesasProjeto[nPos][12]  += (PFM->PFM_VLDESP*-1)
				ElseIf PFM->PFM_CODDES == cCdCelular  // Celular
					aDespesasProjeto[nPos][13]  += (PFM->PFM_VLDESP*-1)
				Else // Outros
					aDespesasProjeto[nPos][14]  += (PFM->PFM_VLDESP*-1)
				EndIf
				cTpSum	:= "B3"
			EndIf		

			//********** Sumarizando -> DESPESAS ATENDIMENTO - DIGITACAO MANUAL
			nPos := Ascan( aSumario , {|x| Substr(x[2],1,2) ==  cTpSum })
			If	nPos <> 0
				aSumario[nPos][3] += (PFM->PFM_VLDESP*-1)
			EndIf
		

			TSR016PFM->(DbSkip())
		EndDo
		TSR016PFM->(DbCloseArea())		
		
		TSR016OS->(DbSkip())
	EndDo
	TSR016OS->(DbCloseArea())

	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Despesas de viagens                                                                                 �
	//�������������������������������������������������������������������������������������������������������

	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Comissoes                                                                                           �
	//�������������������������������������������������������������������������������������������������������
		If ! lFranquia // se for franquia nao chama a rotina padr�o      
			aComissoes := RDW50Comiss(lDREAnalitico,PE5->PE5_CLIENT,PE5->PE5_LOJA,PE5->PE5_PROJET,nMoedaRel,lConvMoeda,lGeraMov)
		Endif 
		
		If lDW50COMIS
			aComissoes := ExecBlock("DW50COMIS",.F.,.F.,{PE5->PE5_CLIENT,PE5->PE5_LOJA,PE5->PE5_PROJET,nMoedaRel,lConvMoeda})
			If ValType(aComissoes) <> "A"
				aComissoes := {}
			Endif
		Endif	

	


	//�����������������������������������������������������������������������������������������������������Ŀ
	//� Despesas de viagem/avulsas                                                                          �
	//�������������������������������������������������������������������������������������������������������
	//FLF_TIPO ? 1=Viagem;2=Avulsa  	
	cQuery := " SELECT FLF.R_E_C_N_O_ AS RECFLF, FLE.R_E_C_N_O_ AS RECFLE "
	cQuery += " FROM " + RetSQLName("FLF") + " FLF "
	cQuery += " INNER JOIN " + RetSQLName("FLE") + " FLE ON FLE_FILIAL = FLF_FILIAL AND FLE_TIPO = FLF_TIPO AND FLE_PRESTA = FLF_PRESTA AND FLE_PARTIC = FLF_PARTIC AND FLE.D_E_L_E_T_ = '' "
	cQuery += " WHERE FLF.D_E_L_E_T_ = '' "
	cQuery += " AND FLF_XCFP = '" + PE5->PE5_PROJET + "' "
	cQuery += " AND FLF_STATUS = '9' " // Sera que eh somente status 9 mesmo ??
	cQuery += " ORDER BY FLF_TIPO, FLF_PRESTA "
	cQuery := ChangeQuery(cQuery)
	
	MPSysOpenQuery(cQuery,"TSR016FLF")
	
	While !TSR016FLF->(EOF())
		FLF->(DbGoTo(TSR016FLF->RECFLF))

		nFLEVALREE 	:= FLE->FLE_VALREE
		cNmDesp		:= GETADVFVAL("FLG","FLG_DESCRI",XFILIAL("FLG")+FLE->FLE_DESPES,1,"")
			
		If	FLF->FLF_TIPO == '2' //Avulso
				
			AAdd( aDespAvulsa, { cChaveProjeto ,;
									FLF->FLF_EMISSA ,;
									"ACERTO/ITEM: " + FLF->FLF_PRESTA +" "+ FLE->FLE_ITEM ,;
									cNmDesp ,;
									FLE->FLE_QUANT  ,;
									(nFLEVALREE*-1) ,;
									FLF->FLF_PARTIC ,;
									FLF->FLF_PRESTA })
										
			//********** Sumarizando -> DESPESAS AVULSA
			nPos := Ascan( aSumario , {|x| Substr(x[2],1,2) ==  "B4" })
			If	nPos <> 0
				aSumario[nPos][3] += (nFLEVALREE*-1)
			EndIf
			
		Else
				
			Aadd( aDespViagem, { cChaveProjeto ,;
									FLF->FLF_EMISSA ,;
									"ACERTO/ITEM: " + FLF->FLF_PRESTA +" "+ FLE->FLE_ITEM ,;
									cNmDesp ,;
									FLE->FLE_QUANT  ,;
									(nFLEVALREE*-1) ,;
									FLF->FLF_PARTIC ,;
									FLF->FLF_PRESTA })
									
			//********** Sumarizando -> DESPESAS VIAGEM
			nPos := Ascan( aSumario , {|x| Substr(x[2],1,2) ==  "B5" })
			If	nPos <> 0
				aSumario[nPos][3] += (nFLEVALREE*-1)
			EndIf
			
		EndIf
		TSR016FLF->(DbSkip())
	EndDo
	TSR016FLF->(DbCloseArea())

	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Selecionando Faturamentos de despesas, viagens e avulsos                                            �
	//�������������������������������������������������������������������������������������������������������
	CA50FatDesp( PE5->PE5_CLIENT, PE5->PE5_PROJET )

	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Selecionando Faturamentos baseado nos pedidos de venda                                              �
	//�������������������������������������������������������������������������������������������������������
	CA50Faturamento( PE5->PE5_CLIENT, PE5->PE5_PROJET, lDREAnalitico, lOnlyArray, lOnlyFile, lGeraMov, dDtMovDe, dDtMovAte )  // A1 (+) FATURAMENTOS


	nQtdFatLiq := nQtdFat-nQtdDev
	nVlrFatLiq := nVlrFat-nVlrDev



	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	calcula o buffer e o backlog baseado nos motivos que compoe cada um deles                           �
	//�������������������������������������������������������������������������������������������������������

	nBuffer  := 0
	nBackLog := 0
	lBuffer  := .F.
	lBackLog := .F.

	If Posicione("PE0",1,FWxFilial("PE0")+PE5->PE5_CODMOT,"PE0_TPMOT") == "1"
		lBuffer := .T.
		lBackLog := .T.
	EndIf

	If lBuffer .And. lBackLog
		nBuffer := (nQtdRea - nQtdAbo) - nQtdFatLiq
		If nBuffer < 0
			nBackLog := Abs(nBuffer)
			nBuffer  := 0
		EndIf
	ElseIf lBuffer
		nBuffer := (nQtdRea - nQtdAbo) - nQtdFatLiq
		If nBuffer < 0
			nBackLog := Abs(nBuffer)
			nBuffer  := 0
		EndIf
	ElseIf lBackLog
		nBackLog := nQtdFatLiq - (nQtdRea - nQtdAbo)
		If nBackLog < 0
			nBuffer  := Abs(nBackLog)
			nBackLog := 0
		EndIf
	EndIf

	nVlReaAbo  := (nQtdRea-nQtdAbo) * nValorHMedio
	
	nHBuffBack := 0

	If nBuffer > 0
		nHBuffBack := nBuffer
	Endif
	
	If nBackLog > 0
		nHBuffBack := nBackLog * -1
	Endif
	
	nVBuffBack := nHBuffBack * nValorHMedio
	
	nHrBuff    := IIf( nHBuffBack >= 0, nHBuffBack, 0 )
	nHrBack    := IIf( nHBuffBack <  0, nHBuffBack, 0 )
	
	nVlBuff    := IIf( nVBuffBack >= 0, nVBuffBack, 0 )
	nVlBack    := IIf( nVBuffBack <  0, nVBuffBack, 0 )
	
	//-- Quando projeto estiver encerrado inverte valores do buffer e backlog (Victor - 11/12/09)
	//-- porque se foi buffer, entregou e nao recebeu, virou perda
	//-- porque se foi backlog, recebeu e nao entregou, virou lucro
////////	If ! lGeraMov //-- se nao estiver rodando por movimento nao inverte valores - 06/12/10 - Victor
    If lGeraMov .And. lBubaMov
		nW := 0    	
    Else //-- se nao estiver rodando por movimento nao inverte valores - 06/12/10 - Victor
		If PE5->PE5_STATUS == "2"
	
			nBuff  := nHrBuff
			nBack  := nHrBack
	
			nHrBuff := nBack * -1
			nHrBack := nBuff * -1 
	
			nBuff  := nVlBuff
			nBack  := nvlBack
	
			nVlBuff := nBack * -1
			nVlBack := nBuff * -1 
			
		Endif

	Endif	
		
	nValLiq    := nTrasCobrado+nTrasIncorrido+nDespIncorrida+nDespCobrada
	
	If lPropComis // se faz a proporcao da comissao (para tirar para as franquias)
		nFatorComiss := (nQtdRea - nQtdAbo) / nQtdFatLiq
	Else
		nFatorComiss := 1
	Endif	
	
	If nFatorComiss <= 0
		nFatorComiss := 1
	Endif


	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Grava array com os dados do cabecalho do projeto                                                    �
	//�������������������������������������������������������������������������������������������������������

	AAdd(aCabecDRE,PE5->PE5_PROJET)			// [1] projeto pai
	AAdd(aCabecDRE,nQtdFat) 				// [2] horas faturadas
	AAdd(aCabecDRE,nVlrFat) 				// [3] valor faturado
	AAdd(aCabecDRE,nQtdDev) 				// [4] horas cancelas
	AAdd(aCabecDRE,nVlrDev) 				// [5] valor cancelado
	AAdd(aCabecDRE,nQtdFatLiq) 				// [6] horas faturadas-canceladas
	AAdd(aCabecDRE,nVlrFatLiq) 				// [7] valor faturado-cancelado
	
	AAdd(aCabecDRE,nQtdRea-nQtdAbo)			// [8] horas realizadas
	AAdd(aCabecDRE,nVlReaAbo)			 	// [9] valor realizado
	AAdd(aCabecDRE,nQtdAbo) 				// [10] horas abonadas
	AAdd(aCabecDRE,nQtdAbo*nValorHMedio) 	// [11] valor abonado
	AAdd(aCabecDRE,nQtdRea) 				// [12] horas realizadas+abonadas
	AAdd(aCabecDRE,nQtdRea*nValorHMedio)	// [13] valor relizado+abonado
	
	AAdd(aCabecDRE,PE5->PE5_QTDORC) 			// [14] horas orcadas
	AAdd(aCabecDRE,nOrcamento*nValorHMedio)	// [15] valor orcado
	AAdd(aCabecDRE,nHrBuff) 				// [16] horas buffer
	AAdd(aCabecDRE,nVlBuff) 				// [17] valor buffer
	AAdd(aCabecDRE,nHrBack) 				// [18] horas backlog
	AAdd(aCabecDRE,nVlBack) 				// [19] valor backlog
	AAdd(aCabecDRE,nTrasCobrado)			// [20] traslado cobrado
	AAdd(aCabecDRE,nTrasIncorrido)			// [21] traslado incorrido
	AAdd(aCabecDRE,nDespCobrada)			// [22] despesa cobrada
	AAdd(aCabecDRE,nDespIncorrida)			// [23] despesa incorrida
	AAdd(aCabecDRE,nValLiq)					// [24] valor liquido cobrado x incorrido
	AAdd(aCabecDRE,nValorHMedio)			// [25] valor hora medio
	AAdd(aCabecDRE,nParticipacao)			// [26] participacao
	AAdd(aCabecDRE,cStatus)					// [27] status
	AAdd(aCabecDRE,_dDataIni)				// [28] primeira OS
	AAdd(aCabecDRE,_dDataFim)				// [29] ultima OS
	AAdd(aCabecDRE,nFatorComiss)			// [30] fator de proporcao da comissao mediante o faturamento
	AAdd(aCabecDRE,cExcQFat)				// [31] flag se for excecao gerencial da quantidade faturada
	AAdd(aCabecDRE,cExcBuffBack)			// [32] flag se for excecao gerencial da quantidade do buffer/backlog
	AAdd(aCabecDRE,cExcVMed)               	// [33] flag se for excecao gerencial do valor medio                 
	
    //-- atualiza acumulador da comissao
	nResultComissao := 0
	
	//-- aplica percentual na comissao
	If Len(aComissoes) <> 0
		aComissoes[1] := Round(aComissoes[1] * nParticipacao * nFatorComiss ,2)
		aComissoes[2] := Round(aComissoes[2] * nParticipacao * nFatorComiss ,2)
		For nW:=1 To Len(aComissoes[5])
			aComissoes[5,nW,4] := Round(aComissoes[5,nW,4] * nParticipacao * nFatorComiss ,2)
			aComissoes[5,nW,5] := Round(aComissoes[5,nW,5] * nParticipacao * nFatorComiss ,2)
			nResultComissao += aComissoes[5][nW][5]
		Next nW
	Endif
	
	aSumario[11][3] := nResultComissao
	    
    //-- para ordenar a impressao por pai e filho
    If Empty(cChavePrjFat)
		cIndice := cChaveProjeto+"0"+cChaveProjeto  // pai
	Else	
		cIndice := cChavePrjFat+"1"+cChaveProjeto   // filho
	Endif	

	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Calcula indicadores                                                                                 �
	//�������������������������������������������������������������������������������������������������������

	nPF      := 0    	// PROGRESSO FISICO - PERCENTUAL
	nODEI    := 0 		// ODE INFORMADO - HORAS
	cWBS     := "" //ZCTQRY->ZCT_TPWBS 		// WBS DO PROJETO
	cCOMPLEX := "" //ZCTQRY->ZCT_CLASSI		// COMPLEXIDADE
	nIMIT    := 0		// IMIT - PERCENTUAL
	nCPI     := 0		// CPI (INDICE DE PERFORMANCE DO CUSTO) - COEFICIENTE
	nSPI     := 0 		// SPI (INDICE DE PERFORMANCE DO PRAZO) - COEFICIENTE
	nETCI    := 0 		// ETC INFORMADO (AGENDA) - HORAS
	nETCL    := 0		// ETC Linear (ESTIMATIVA PARA A CONCLUSAO SEM VARIACAO) - HORAS
	nETCT    := 0		// ETC TENDENCIA (ESTIMATIVA PARA A CONCLUSAO COM VARIACAO PREVISTA) - HORAS
	nEACPL   := 0		// EAC LINEAR PROJETADO - HORAS
	//-- nao usado
	nEACPT   := 0		// EAC TENDENCIA PROJETADO - HORAS (*** NAO USADO ***)
	nEACA    := 0 		// EAC ATUAL - HORAS (*** NAO USADO ***)
	//-- nao usado
	nEACT    := 0 		// EAC Tend�ncia (hrs)			
	nEACLC   := 0 		// EAC Linear Custo			
	nEACTC   := 0 		// EAC Tend�ncia Custo			
	nMCLV    := 0 		// Margem Contribuicao Linear ($)		
	nMCLP    := 0 		// Margem Contribuicao Linear (%)		
	nMCTV    := 0 		// Margem Contribuicao Tendencia ($)	
	nMCTP    := 0 		// Margem Contribuicao Tendencia (%)	
	nEACAG   := 0 		// EAC Agenda (hrs)
	nEACAGC  := 0 		// EAC Agenda Custo
	nMCAGV   := 0 		// Margem Contribuicao Agenda ($)
	nMCAGP   := 0 		// Margem Contribuicao Agenda (%)
	
	//Nao existe WBS no PE5, busca o primeiro documento existente
	PF4->(DbSetORder(1)) //PF4_FILIAL+PF4_CODPRJ+PF4_EDT+PF4_WBS+PF4_CODIGO
	If PF4->(MsSeek(FWxFilial("PF4")+PE5->PE5_PROJET))
		cWBS := PF4->PF4_WBS
		//cCOMPLEX  SE NAO TEM NA PE5 , PEGA DE ONDE??
	EndIf
	
	//����������������������Ŀ
	//� Progresso Fisico     �
	//������������������������
	//-- busca os projetos no AF8 que estejam ligados no CFP, se houver mais de um tira a media
	//-- busca o ultimo apontamento (maior data) no AFQ

	cQuery := " SELECT AF8_CLIENT, AF8_PROJET, AVG((AFQ_QUANT*100)) AF8_PERC "
	cQuery += "   FROM "+RetSQLName("AFQ")+" AFQ "

    //-- busca o projeto CFP informado no PMS
	cQuery += "  INNER JOIN "+RetSQLName("AF8")+" AF8 "
	cQuery += "          ON AF8.D_E_L_E_T_ = ' ' " 
	cQuery += "         AND AF8_FILIAL = '"+xFilial("AF8")+"' "
	cQuery += "         AND AF8_PROJET = AFQ_PROJET "
	cQuery += "         AND AF8_REVISA = AFQ_REVISA "
	cQuery += "         AND AF8_CLIENT = '"+PE5->PE5_CLIENT+"' "
	cQuery += "         AND AF8_LOJA = '"+PE5->PE5_LOJA+"' "
	cQuery += " AND AF8_PROJET = '"+PE5->PE5_PROJET+"' "

    //-- busca ultimo lancamento de progresso fisico
	cQuery += "  INNER JOIN ( SELECT AFQ_PROJET AFQ1_PROJET, AFQ_REVISA AFQ1_REVISA, MAX(AFQ_DATA) AFQ1_DATA "
	cQuery += "                 FROM "+RetSQLName("AFQ")+" AFQ1 "
	cQuery += "                WHERE AFQ1.D_E_L_E_T_ = ' ' "
	cQuery += "                  AND AFQ1.AFQ_FILIAL = '"+xFilial("AFQ")+"' "
	cQuery += "                  AND AFQ1.AFQ_PROJET = AFQ1.AFQ_EDT "            
	cQuery += "                GROUP BY AFQ1.AFQ_PROJET, AFQ1.AFQ_REVISA ) AFQ1 "
	cQuery += "          ON AFQ_PROJET = AFQ1_PROJET "
	cQuery += "         AND AFQ_REVISA = AFQ1_REVISA "
	cQuery += "         AND AFQ_DATA   = AFQ1_DATA "
                                                    
    //-- filtra somente a EDT principal onde esta o total do progresso fisico do projeto
	cQuery += "  WHERE AFQ.D_E_L_E_T_ = ' ' "
	cQuery += "    AND AFQ_FILIAL = '"+xFilial("AFQ")+"' "
	cQuery += "    AND AFQ_PROJET = AFQ_EDT "

	cQuery += "  GROUP BY AF8_CLIENT, AF8_PROJET "
	
	If cDBMS == "DB2"
		cQuery += " FOR READ ONLY"
    Endif
    
	MPSysOpenQuery(cQuery,"AF8QRY")
	
	nPF := AF8QRY->AF8_PERC			
	            
	//-- 10/10/11 - Victor -> quando estiver zerado, coloca 0,5% de progresso fisico para a tendencia
	//--                      refletir a variacao, senao a tendencia fica errada
	If nPF == 0
		nPF := 0.5
	Endif
	
	AF8QRY->(dbCloseArea())

	//����������������������Ŀ
	//�	ETC                  �
	//������������������������
	//-- se existir os campos iguala a eles 

//	If ZCT->(FieldPos("ZCT_ETC")) > 0     
//		nETCI := ZCTQRY->ZCT_ETC
//	Endif

	//����������������������Ŀ
	//� ODE                  �
	//������������������������

	//-- se existir os campos iguala a eles 
//	If ZCT->(FieldPos("ZCT_ODE")) > 0     
//		nODEI := ZCTQRY->ZCT_ODE
//	Endif

	//����������������������Ŀ
	//� CPI                  �
	//������������������������

	If (nOrcamento*nValorHMedio) > 0                                                                                                     
		//-- % custo = (total custo+comissoes)/valor orcado
		nCPI := (Abs(aSumario[6,3])+Abs(aSumario[7,3])+Abs(aSumario[8,3])+Abs(aSumario[9,3])+Abs(aSumario[10,3])+Abs(aSumario[11,3])) / (nOrcamento*nValorHMedio) * 100
		If nCPI > 0
			//-- CPI = % progresso fisico / % custo
			nCPI := nPF / nCPI
		Endif	
	Endif	
	
	nCPI := Round(nCPI,2)

	//����������������������Ŀ
	//� SPI                  �
	//������������������������
	                                
	If (nOrcamento*nValorHMedio) > 0
		//-- % apontamento = valor apontado/valor orcado
		nSPI := (nQtdRea*nValorHMedio) / (nOrcamento*nValorHMedio) * 100
		If nSPI > 0
 			//-- SPI = % progresso fisico / % apontamento
			nSPI := nPF / nSPI
		Endif	
	Endif 

	nSPI := Round(nSPI,2)
	
	//����������������������Ŀ
	//� ETC LINEAR           �
	//������������������������

	//-- Progresso fisico pendente = 100 % - % progresso fisico
	nETCL := 100 - nPF
	//-- ETC = % Progresso fisico pendente * qtde orcada 
	nETCL := nETCL * nOrcamento / 100

	nETCL := Round(nETCL,2)

	//����������������������Ŀ
	//� EAC LINEAR PROJETADO �
	//������������������������
	                         
	//-- EAC = horas apontadas + horas pendentes (ETC)
	nEACPL := nQtdRea + nETCL

	//����������������������Ŀ
	//� ETC TENDENCIA        �
	//������������������������
         
    If nCPI > 0
		//-- ETC TENDENCIA = EAC LINEAR / CPI
		nETCT := nETCL / nCPI
	Endif	

	nETCT := Round(nETCT,2)

	//�������������������������Ŀ
	//� EAC TENDENCIA PROJETADO �
	//���������������������������
	                         
	//nEACPT := nEACPL

	//����������������������Ŀ
	//� EAC ATUAL            �
	//������������������������
	                         
	nC := 0
 	//nA := 0
	If (nOrcamento*nValorHMedio) > 0
		//-- % custo = (total custo+comissoes)/valor orcado
		nC := (Abs(aSumario[6,3])+Abs(aSumario[7,3])+Abs(aSumario[8,3])+Abs(aSumario[9,3])+Abs(aSumario[10,3])+Abs(aSumario[11,3])) / (nOrcamento*nValorHMedio) * 100
		//-- % apontamento = valor apontado/valor orcado
		//nA := (nQtdRea*nValorHMedio) / (nOrcamento*nValorHMedio) * 100
	Endif 		
	//-- EAC = % custo + (100% - % progresso fisico)
	///nEACA := nC + ( 100 - nPF )

	//����������������������Ŀ
	//� IMIT                 �
	//������������������������

    //-- busca os documentos relacionados ao projeto
    //-- conta o total de documentos e os que ja foram entregues para calcular o percentual 
    cQuery := " SELECT SUM(1) AS TOTAL, SUM(CASE WHEN PF4_DTENTR<>' ' THEN 1 ELSE 0 END) ENTREGUE " 
	cQuery += " FROM " + RetSQLName("PF4") + " PF4 "
	cQuery += " WHERE PF4.D_E_L_E_T_ = '' "
	cQuery += " AND PF4_FILIAL = '" + FWxFilial("PE5") + "' "
	cQuery += " AND PF4_CODPRJ = '" + PE5->PE5_PROJET + "' "
	cQuery += " AND PF4_ACAO = '1' "
    
	MPSysOpenQuery(cQuery,"PF4QRY")
                             
    If PF4QRY->TOTAL > 0
		nIMIT := PF4QRY->ENTREGUE / PF4QRY->TOTAL * 100
	Endif

	nIMIT := Round(nIMIT,2)

	PF4QRY->(dbCloseArea())

	//����������������������Ŀ
	//� EAC Tendencia (hrs)  �
	//������������������������
	
	//-- nEACT = Horas apontadas + ETC Tendencia (hrs)
	nEACT := nQtdRea + nETCT

	//����������������������Ŀ
	//� EAC Agenda (hrs)     �
	//������������������������
	
	//-- nEACT = Horas apontadas + ETC Agenda (hrs)
	nEACAG := nQtdRea + nETCI

	//����������������������Ŀ
	//� EAC Linear Custo	 �
	//������������������������

    //-- total de custo
	nC  := Abs(aSumario[6,3])+Abs(aSumario[7,3])+Abs(aSumario[8,3])+Abs(aSumario[9,3])+Abs(aSumario[10,3])
	//-- comissao
	nC1 := Abs(aSumario[11,3])

	//-- nEACLC = (EAC linear hrs x (Total Custos / Horas Apontadas)) + Total Comissao
	If nQtdRea > 0
		nEACLC := (nEACPL * ((nC * -1) / nQtdRea)) + nC1
	Endif	
        
	//����������������������Ŀ
	//� EAC Tendencia Custo  �
	//������������������������

	//-- nEACTC = (EAC tendencia hrs x (Total Custos / Horas Apontadas)) + Total Comissao	
	If nQtdRea > 0
		nEACTC := (nEACT * ((nC * -1) / nQtdRea)) + nC1
    Endif

	//����������������������Ŀ
	//� EAC Agenda Custo     �
	//������������������������

	//-- nEACAGC = (EAC agenda hrs x (Total Custos / Horas Apontadas)) + Total Comissao	
	If nQtdRea > 0
		nEACAGC := (nEACAG * ((nC * -1) / nQtdRea)) + nC1
    Endif
    
	//��������������������������������Ŀ
	//� Margem Contribuicao Linear ($) �
	//����������������������������������

	//-- nMCLV = Orcadas ($) + EAC Linear Custo 	
	nMCLV := (nOrcamento*nValorHMedio) + nEACLC

	//��������������������������������Ŀ
	//� Margem Contribuicao Linear (%) �
	//����������������������������������

	//-- nMCLP = Mrg de Contrib Linear $ / Orcado $	
	If (nOrcamento*nValorHMedio) > 0
		nMCLP := nMCLV / (nOrcamento*nValorHMedio) * 100
	Endif        
	
	nMCLP := Round(nMCLP,2) 

	//�����������������������������������Ŀ
	//� Margem Contribuicao Tendencia ($) �
	//�������������������������������������

	//-- nMCTV = Orcadas ($) + EAC Tendencia Custo 	
	nMCTV := (nOrcamento*nValorHMedio) + nEACTC

	//�����������������������������������Ŀ
	//� Margem Contribuicao Tendencia (%) �
	//�������������������������������������

	//-- nMCTP = Mrg de Contrib Tendencia $ / Orcado $	
	If (nOrcamento*nValorHMedio) > 0
		nMCTP := nMCTV / (nOrcamento*nValorHMedio) * 100
	Endif

	nMCTP := Round(nMCTP,2)

	//�����������������������������������Ŀ
	//� Margem Contribuicao Agenda ($)    �
	//�������������������������������������

	//-- nMCAGV = Orcadas ($) + EAC Agenda Custo 	
	nMCAGV := (nOrcamento*nValorHMedio) + nEACAGC

	//�����������������������������������Ŀ
	//� Margem Contribuicao Agenda (%)    �
	//�������������������������������������

	//-- nMCAGP = Mrg de Contrib Agenda $ / Orcado $	
	If (nOrcamento*nValorHMedio) > 0
		nMCAGP := nMCAGV / (nOrcamento*nValorHMedio) * 100
	Endif

	nMCAGP := Round(nMCAGP,2)

	//�����������������������������������Ŀ
	//� Ajuste para nao estourar campo    �
	//�������������������������������������

	If 	nMCLP < -999999.99
		nMCLP := -99.99   /// -999999.99
	Endif	
	If nMCLP > 9999999.99
		nMCLP := 99.99   /// 9999999.99
	Endif	
	
	If 	nMCTP < -999999.99
		nMCTP := -99.99  /// -999999.99
	Endif	
	If nMCTP > 9999999.99
		nMCTP := 99.99  /// 9999999.99
	Endif	

	If 	nMCAGP < -999999.99
		nMCAGP := -99.99  /// -999999.99
	Endif	
	If nMCAGP > 9999999.99
		nMCAGP := 99.99   //// 9999999.99
	Endif	
                                                                                                       
	//�����������������������������������Ŀ
	//� calculo da margem para uso na     �
	//� RV por rentabilidade              �
	//�������������������������������������

	nFatDesp := 0
	nCustos  := 0
	nRecSim  := 0 
	nMCSim   := 0 
	nVHM     := nValorHMedio
	
	//-- Considera para calculo da receita da RV o valor de repasse
	If nVlrRepasse > 0
		nVHM := nVlrRepasse
	Endif	
	
    //-- soma faturamento de despesas
	For nW:=1 To Len(aFatDespesa)
		nFatDesp += aFatDespesa[nW,5]
	Next nW
	                                 
	//-- soma custos
	For nW := 1 To Len( aSumario )
		If Subs(aSumario[nW,2],1,1) == "B"  // CUSTOS
			nCustos += Abs(aSumario[nW,3])
		Endif	
	Next nW
          
    //-- receita apurada = (horas realizadas * valor hora medio + faturamento de despesas)
    nRecSim := ( (nQtdRea-nQtdAbo) * Round(nVHM,2) ) + nFatDesp
    
    //-- se o projeto estiver encerrado e com progresso fisico 100%, considera o valor total do projeto 
    If PE5->PE5_STATUS == "2" .And. nPF >= 100
	    nRecSim := ( nOrcamento * Round(nVHM,2) ) + nFatDesp
    Endif

    //-- calcula a margem de contribuicao apurada
    //-- receita apurada - os custos do projeto
    nMCSim := PE5->PE5_MLUCRO //Round((((nRecSim-nCustos)/nRecSim)*100),2)

	If 	nMCSim < -999999.99
		nMCSim := 0
	Endif	

	If nMCSim > 9999999.99
		nMCSim := 0
	Endif	

	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Grava array com os dados do cabecalho do projeto - indicadores                                      �
	//�������������������������������������������������������������������������������������������������������

	AAdd(aCabecDRE,nPF)	      				// [34] PROGRESSO FISICO - PERCENTUAL
	AAdd(aCabecDRE,nETCI)					// [35] ETC INFORMADO - HORAS
	AAdd(aCabecDRE,nODEI)					// [36] ODE INFORMADO - HORAS
	AAdd(aCabecDRE,nCPI)					// [37] CPI (INDICE DE PERFORMANCE DO CUSTO) - COEFICIENTE
	AAdd(aCabecDRE,nSPI)					// [38] SPI (INDICE DE PERFORMANCE DO PRAZO) - COEFICIENTE
	AAdd(aCabecDRE,nETCL)					// [39] ETC Linear (ESTIMATIVA PARA A CONCLUSAO SEM VARIACAO) - HORAS
	AAdd(aCabecDRE,nETCT)					// [40] ETC TENDENCIA (ESTIMATIVA PARA A CONCLUSAO COM VARIACAO PREVISTA) - HORAS
	AAdd(aCabecDRE,nEACPL)					// [41] EAC LINEAR PROJETADO - HORAS
    //-- nao usado
	AAdd(aCabecDRE,nEACPT)				    // [42] EAC TENDENCIA PROJETADO - HORAS (EACPT) (*** NAO USADO ***)
	AAdd(aCabecDRE,nEACA)					// [43] EAC ATUAL - HORAS (*** NAO USADO ***)
    //-- nao usado
	AAdd(aCabecDRE,cWBS)					// [44] WBS DO PROJETO
	AAdd(aCabecDRE,cCOMPLEX)				// [45] COMPLEXIDADE
	AAdd(aCabecDRE,nIMIT)					// [46] IMIT - PERCENTUAL
	AAdd(aCabecDRE,nEACT)					// [47] EAC Tendencia (hrs)			
	AAdd(aCabecDRE,nEACLC)					// [48]	EAC Linear Custo			
	AAdd(aCabecDRE,nEACTC)					// [49] EAC Tendencia Custo			
	AAdd(aCabecDRE,nMCLV)					// [50]	Margem Contribuicao Linear ($)		
	AAdd(aCabecDRE,nMCLP)					// [51]	Margem Contribuicao Linear (%)		
	AAdd(aCabecDRE,nMCTV)					// [52]	Margem Contribuicao Tendencia ($)	
	AAdd(aCabecDRE,nMCTP)					// [53]	Margem Contribuicao Tendencia (%)	
	AAdd(aCabecDRE,nEACAG) 					// [54] EAC Agenda (hrs)
	AAdd(aCabecDRE,nEACAGC)					// [55] EAC Agenda Custo
	AAdd(aCabecDRE,nMCAGV)					// [56] Margem Contribuicao Agenda ($)
	AAdd(aCabecDRE,nMCAGP)					// [57] Margem Contribuicao Agenda (%)

	AAdd(aCabecDRE,nHrBuffAnt) 				// [58] horas de buffer mes anterior (DRE por movimento)
	AAdd(aCabecDRE,nHrBackAnt)  			// [59] horas de backlog mes anterior (DRE por movimento)
	AAdd(aCabecDRE,nVlBuffAnt)  			// [60] valor de buffer mes anterior (DRE por movimento)
	AAdd(aCabecDRE,nVlBackAnt)  			// [61] valor de backlog mes anterior (DRE por movimento)
	AAdd(aCabecDRE,nHrBuffAtu)  			// [62] horas de buffer mes atual (DRE por movimento)	
	AAdd(aCabecDRE,nHrBackAtu)  			// [63] horas de backlog mes atual (DRE por movimento)	
	AAdd(aCabecDRE,nVlBuffAtu)  			// [64] valor de buffer mes atual (DRE por movimento)	
	AAdd(aCabecDRE,nVlBackAtu)  			// [65] valor de backlog mes atual (DRE por movimento)	
	AAdd(aCabecDRE,nHrMedBuff) 				// [66] valor hora medio de buffer (DRE por movimento)	
	AAdd(aCabecDRE,nHrMedBack) 				// [67] valor hora medio de backlog (DRE por movimento)	
	AAdd(aCabecDRE,nMCSim)     				// [68] MARGEM DE CONTRIBUICAO SIMULADA
	AAdd(aCabecDRE,nRecSim) 				// [69] VALOR RECEITA SIMULADA 
	AAdd(aCabecDRE,nVlrRepasse) 			// [70] Valor repasse utilizado para calcular a receita simulada
	AAdd(aCabecDRE,nLimRNP)               	// [71] Limite RNP                 
	AAdd(aCabecDRE,nHrSuspensas)            // [72] Qtd de Horas Suspensas                 

	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Grava array com os dados do sumario do projeto                                                      �
	//�������������������������������������������������������������������������������������������������������

	AADD( aSumarioProjeto , { cChaveProjeto , aItemFaturado , aTitulos , aComissoes , aCabecDRE , aSumario , aDespesasProjeto , aDespAvulsa , aDespViagem , cIndice , AClone(aComissPai) , aOrdemServico , aResumoMotivos, aFatDespesa } )


	TSR016DRE->(DbSkip())	
EndDo
TSR016DRE->(DbCloseArea())	

RestArea(aArea)
Return aSumarioProjeto


/*/{Protheus.doc} A50Cabecalho
A50Cabecalho
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
Static Function A50Cabecalho( lFull , lResumo , lGeraArq , nMenu, lGeraMov, lBuBaMov, lCalcRV, cFilZCT )
	
Local dDataContr 	:= CtoD("")  // data de assinatura do contrato
Local cSimbMoeda 	:= GetMv("MV_SIMB"+Str(nMoedaRel,1))
Local lImpTudo   := .T.

Local cDescTpProj 	:= Alltrim(POSICIONE('ZX5',1,XFILIAL('ZX5')+"SRV003"+PE5->PE5_TPPROJ,"ZX5_DESCRI"))
Local cUnSrvDesc 		:= PE5->PE5_UNSRV + ' - ' + Posicione("PE1",1,xFilial("PE1")+PE5->PE5_UNSRV,"PE1_DESCRI")

Default lFull    := .F.
DEFAULT lGeraArq	:= .F.

dbSelectArea("PE5")
dbSetOrder(1)
dbSeek(FWxFilial("PE5")+_cChaveProjeto)

dbSelectArea("SA1")
dbSetOrder(1)
dbSeek(FWxFilial("SA1")+PE5->PE5_CLIENT+PE5->PE5_LOJA)

AI0->(DbSetOrder(1))
If AI0->(DbSeek(xFilial("SA1")+PE5->PE5_CLIENT+PE5->PE5_LOJA))
	dDataContr := AI0->AI0_XDTCTR  //SA1->A1_PRICCDU // data da primeira compra de CDU
Endif


dbSelectArea("PE5")

If lGeraArq
	
	DRE->PROJETO 	:= PE5->PE5_PROJET
	If ! lResumo
		DRE->ITEM 		:= PE5->PE5_ITEM
		DRE->VERSAO 	:= PE5->PE5_VERSAO
		DRE->DESCPRO 	:= PE5->PE5_DESPRO
		DRE->STATPRJ 	:= PE5->PE5_STATUS
		DRE->DESCSTAT 	:= _cStatus
	Else
		DRE->DESCPRO 	:= "RESUMO DO PROJETO"
	Endif
	DRE->CLIENTE 	:= PE5->PE5_CLIENT
	DRE->LOJA 		:= PE5->PE5_LOJA
	DRE->NOMECLI 	:= Posicione("SA1",1,xFilial("SA1")+PE5->PE5_CLIENT+PE5->PE5_LOJA,"A1_NOME")
	DRE->CODGAR 	:= PE5->PE5_GAR
	DRE->NOMEGAR 	:= Posicione("RD0",1,xFilial("RD0")+PE5->PE5_GAR,"RD0_NOME")
	DRE->CODARQ 	:= PE5->PE5_CN
	DRE->NOMEARQ 	:= Posicione("RD0",1,xFilial("RD0")+PE5->PE5_CN,"RD0_NOME")
	DRE->CODCP 		:= PE5->PE5_RESPON
	DRE->NOMECP 	:= PE5->PE5_NOMRES
	DRE->CODGPP 	:= PE5->PE5_GPP
	DRE->NOMEGPP 	:= Posicione("RD0",1,xFilial("RD0")+PE5->PE5_GPP,"RD0_NOME")
	DRE->MOTIVO		:= PE5->PE5_CODMOT
	DRE->DESCMOT 	:= Posicione("PE0",1,xFilial("PE0")+PE5->PE5_CODMOT,"PE0_DESC") //Posicione("ZC6",1,xFilial("ZC6")+PE5->PE5_CODMOT,"ZC6_DESC")
	DRE->FHD        := Posicione("ZC6",1,xFilial("ZC6")+PE5->PE5_CODMOT,"ZC6_FHD")   // 1=FHD;2=FHDG;3=Ambos;4=Nao Considera
	cFHDDesc := ""
	If DRE->FHD == "1"
		cFHDDesc := "FATURAVEIS" // "FHD"
	ElseIf DRE->FHD == "2"
		cFHDDesc := "GERENCIAIS" // "FHDG"
	ElseIf DRE->FHD == "3"
		cFHDDesc := "AMBOS"
	ElseIf DRE->FHD == "4"
		cFHDDesc := "NAO CONSIDERA"
	Endif
	DRE->FHDDESC    := cFHDDesc
	DRE->PRIVISITA	:= _dPriOS
	DRE->ULTVISITA	:= _dUltOS
	DRE->CODEXEC   	:= PE5->PE5_VEND
	DRE->NOMEEXEC  	:= Posicione("SA3",1,xFilial("SA3")+PE5->PE5_VEND,"A3_NOME")
	DRE->CODANAPMO	:= PE5->PE5_ANAPMO
	DRE->NOMEANAPMO	:= Posicione("RD0",1,xFilial("RD0")+PE5->PE5_ANAPMO,"RD0_NOME")
	DRE->PROPOSTA	:= PE5->PE5_PROPOS
	DRE->EMISSAO    := PE5->PE5_EMISSA
	DRE->PRODUT     := PE5->PE5_PRODUT
	DRE->DESCPROD	:= Tabela("YR",PE5->PE5_PRODUT,.F.)
	DRE->HRSALDO	:= (_nOrcamento + _nLimRNP - _nHrSuspensa) - _nQtdRea
	DRE->HRAPTO    	:= _nQtdReaAbo
	DRE->HRABONO   	:= _nQtdAbo
	DRE->HRREAL		:= _nQtdRea
	If Empty(PE5->PE5_TPPROJ)
		DRE->TPPROJ := "0"
	Else
		DRE->TPPROJ := PE5->PE5_TPPROJ
	Endif
	If DRE->TPPROJ == "0"
		cDescTpProj := "NAO INFORMADO"
	ElseIf DRE->TPPROJ == "1"
		cDescTpProj := "GESTAO PROJETO"
	ElseIf DRE->TPPROJ == "2"
		cDescTpProj := "PERSONALIZACAO"
	ElseIf DRE->TPPROJ == "3"
		cDescTpProj := "SPEED"
	ElseIf DRE->TPPROJ == "4"
		cDescTpProj := "BANCO DE HORAS"
	ElseIf DRE->TPPROJ == "5"
		cDescTpProj := "ATUALIZACAO VERSAO"
	ElseIf DRE->TPPROJ == "6"
		cDescTpProj := "ORCAMENTO"
	ElseIf DRE->TPPROJ == "7"
		cDescTpProj := "PROJETO AVULSO"
	ElseIf DRE->TPPROJ == "8"
		cDescTpProj := "ANALISE CRITICA"
	ElseIf DRE->TPPROJ == "9"
		cDescTpProj := "SSIM"
	Else
		cDescTpProj := "NAO DEFINIDO"
	Endif
	DRE->DESCTPPROJ := cDescTpProj
	DRE->DTASSCON   := dDataContr
	
	If Select("ZRL") > 0 .And. PE5->(FieldPos("PE5_UNSRV")) > 0
		DRE->UNSRV      := PE5->PE5_UNSRV
		DRE->NOMUNSRV   := Posicione("ZRL",1,xFilial("ZRL")+PE5->PE5_UNSRV,"ZRL_DESCRI")
	Endif
	
	DRE->CODSEG     := PE5->PE5_CODSEG
	DRE->NOMSEG     := Posicione("ZL4",1,xFilial("ZL4")+PE5->PE5_CODSEG,"ZL4_DESPOR")
	
	nPercFin := (_nQtdRea / _nOrcamento) * 100
	
	If nPercFin < -999999.99
		nPercFin := -99.99  //// -999999.99
	Endif
	If nPercFin > 9999999.99
		nPercFin := 99.99  //// 9999999.99
	Endif
	
	DRE->PERCFIN	:= nPercFin
	
Else
	
	nLin+=2
	//	lCabe := A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilPE5)
	
	
	/*
	=> DADOS DO PROJETO                                                                        => CONTROLE DE HORAS
	
	Cliente      : 999999 - XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX (40)                      Orcado       : 99999.99 horas
	Projeto      : 9999999999-01/01 - XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX (40)            Apontado     : 99999.99 horas
	Status       : xxxxxxxxxxxxxxxxxxxx (20)                                                   Abonado      : 99999.99 horas
	Cod.Motivo   : 99 - xxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (40)                                    Realizado    : 99999.99 horas
	GPP          : 999999 - XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX (40)                      Saldo        : 99999.99 horas
	CP Projeto   : 999999 - XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX (40)                      % Financeiro : 999.99%
	Gerente AR   : 999999 - XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX (40)                      Primeira OS  : 99/99/99
	Arquiteto    : 999999 - XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX (40)                      Ultima OS    : 99/99/99
	Executivo    : 999999 - XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX (40)
	Analista PMO : 999999 - XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX (40)
	Proposta MSAC: 99999999-99 de 99/99/99
	0              15       24                                                                 91           104
	*/
	
	If lFull
		
		cDescTpProj := Alltrim(POSICIONE('ZX5',1,XFILIAL('ZX5')+"SRV003"+PE5->PE5_TPPROJ,"ZX5_DESCRI"))
		cUnSrvDesc := PE5->PE5_UNSRV + ' - ' + Posicione("PE1",1,xFilial("PE1")+PE5->PE5_UNSRV,"PE1_DESCRI")
		
		
		@ nLin   ,00  PSAY "=> DADOS DO PROJETO"
		@ nLin   ,85  PSAY "=> CONTROLE DE HORAS"
//		@ nLin   ,155 PSAY "=> DADOS DO PROJETO (cont.)"
		@ nLin+2 ,00 PSAY "Cliente               : " + PE5->PE5_CLIENT + ' - ' + Substr(Posicione("SA1",1,xFilial("SA1")+PE5->PE5_CLIENT+PE5->PE5_LOJA,"A1_NOME"),1,40)
		@ nLin+2 ,85 PSAY "Orcado                : " + Transform(_nOrcamento,"@E 9,999,999.99")+" hrs "
		@ nLin+3 ,00  PSAY "Projeto               : " + PE5->PE5_PROJET+" - "+SubStr(PE5->PE5_DESPRO,1,45)
		@ nLin+3 ,85  PSAY "Apontado              : " + Transform(_nQtdReaAbo,"@E 9,999,999.99")+" hrs "
		@ nLin+4 ,00  PSAY "Status                : " + RetX3Combo( "PE5_STATUS" , PE5->PE5_STATUS )
		@ nLin+4 ,85  PSAY "Abonado               : " + Transform(_nQtdAbo,"@E 9,999,999.99")+" hrs "
//		@ nLin+4 ,155 PSAY "Vlr.Repasse Rec. Apur.: " +cSimbMoeda+" " + Transform(_nVlrRepasse,"@E 99,999.99")
		@ nLin+5 ,00  PSAY "Cod.Motivo            : " + PE5->PE5_CODMOT + ' - ' + Substr(Posicione("PE0",1,xFilial("PE0")+PE5->PE5_CODMOT,"PE0_DESC"),1,35)
		@ nLin+5 ,85  PSAY "Realizado             : " + Transform(_nQtdRea,"@E 9,999,999.99")+" hrs "
		@ nLin+6 ,00  PSAY "1a. Assinat. Contr.   : " + DtoC(dDataContr	)
		@ nLin+6 ,85 PSAY "Saldo                 : " + Transform(_nOrcamento - _nQtdRea,"@E 9,999,999.99")+" hrs "
		@ nLin+7 ,00 PSAY "Segmento              : " + PE5->PE5_CODSEG + ' - ' + SubStr(Posicione("AOV",1,xFilial("AOV")+PE5->PE5_CODSEG,"AOV_DESSEG"),1,30)
		@ nLin+7 ,85 PSAY "% Financeiro          : " + Transform( ( _nQtdRea / _nOrcamento) * 100,"@E 9,999,999.99%")
		@ nLin+8 ,00 PSAY "Unidade de Negocio    : " + cUnSrvDesc
		@ nLin+8 ,85 PSAY "% Progresso Fisico    : " + Transform(_nPF,"@E 9,999,999.99%")
		@ nLin+9, 00 PSAY "Tipo Projeto          : " + cDescTpProj
		@ nLin+9 ,85 PSAY "Primeira OS           : " + DTOC(_dPriOS)
		@ nLin+10,85 PSAY "Ultima OS             : " + DTOC(_dUltOS)
		
		nLin+=11
/*
		@ nLin   , 00 PSAY REPL("_",220)
		nLin+=1
		@ nLin   , 00 PSAY "=> TIME DE PROJETO"
		nLin+=1
		PE7->(DbSetOrder(1)) //PE7_FILIAL+PE7_PROJET+PE7_CODREC+PE7_PAPEL
		PE7->(MsSeek(FWxFilial("PE7")+PE5->PE5_PROJET))
		While !PE7->(EOF()) .AND. FWxFilial("PE7")+PE5->PE5_PROJET == PE7->(PE7_FILIAL+PE7_PROJET)
			A50Quebra(99, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

			@ nLin,010 PSAY "Recurso: "+PE7->PE7_CODREC+"-"+POSICIONE('RD0',1,XFILIAL('RD0')+PE7->PE7_CODREC,"RD0_NOME")+"     Papel: "+PE7->PE7_PAPEL+" - "+POSICIONE('ZX5',1,XFILIAL('ZX5')+"SRV001"+PE7->PE7_PAPEL,"ZX5_DESCRI")
			nLin++
			PE7->(DbSkip())
		EndDo

		@ nLin   , 00 PSAY REPL("_",220)
		nLin+=1
		@ nLin   , 00 PSAY "=> PROPOSTA(S)"
		nLin+=1
		PE6->(DbSetOrder(1)) //PE6_FILIAL+PE6_PROJET+PE6_PROPOS+PE6_REVISA
		PE6->(MsSeek(FWxFilial("PE6")+PE5->PE5_PROJET))
		While !PE6->(EOF()) .AND. FWxFilial("PE6")+PE5->PE5_PROJET == PE6->(PE6_FILIAL+PE6_PROJET)
			A50Quebra(99, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

			@ nLin,010 PSAY "Proposta: "+PE6->PE6_PROPOS+"/"+PE6->PE6_REVISA+" - "+PE6->PE6_DESCR +"       Hrs.Or�adas: "+Transform(PE6->PE6_QTDORC,PesqPict("PE6","PE6_QTDORC"))+"      Vlr.Hrs.Orc.: "+Transform(PE6->PE6_VLRORC,PesqPict("PE6","PE6_VLRORC"))
			nLin++
			PE6->(DbSkip())
		EndDo

		@ nLin   , 00 PSAY REPL("_",220)
		nLin+=1
		@ nLin   , 00 PSAY "=> TIME DE VENDAS"
		nLin+=1
		PE9->(DbSetOrder(1)) //PE9_FILIAL+PE9_PROJET+PE9_VEND+PE9_CODPAP+PE9_PROPOS+PE9_REVISA
		PE9->(MsSeek(FWxFilial("PE9")+PE5->PE5_PROJET))
		While !PE9->(EOF()) .AND. FWxFilial("PE9")+PE5->PE5_PROJET == PE9->(PE9_FILIAL+PE9_PROJET)
			A50Quebra(99, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

			@ nLin,010 PSAY "Vendedor: "+PE9->PE9_VEND+"-"+POSICIONE("SA3",1,XFILIAL("SA3")+PE9->PE9_VEND,"A3_NOME")+"     Papel: "+PE9->PE9_CODPAP+"-"+POSICIONE("SUM",1,XFILIAL("SUM")+PE9->PE9_CODPAP,"UM_DESC")+"    % Particip.: "+Transform(PE9->PE9_PERC,PesqPict("PE9","PE9_PERC"))+"     Proposta: "+PE9->PE9_PROPOS+"/"+PE9->PE9_REVISA
			nLin++
			PE9->(DbSkip())
		EndDo

		@ nLin   , 00 PSAY REPL("_",220)
		nLin+=1
		@ nLin   , 00 PSAY "=> FRENTES DE ENTREGA"
		nLin+=1
		PE8->(DbSetOrder(1)) //PE8_FILIAL+PE8_PROJET+PE8_CODIGO+PE8_PRJPMS+PE8_EDT
		PE8->(MsSeek(FWxFilial("PE8")+PE5->PE5_PROJET))
		While !PE8->(EOF()) .AND. FWxFilial("PE8")+PE5->PE5_PROJET == PE8->(PE8_FILIAL+PE8_PROJET)
			A50Quebra(99, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

			@ nLin,010 PSAY "Frente: "+PE8->PE8_CODIGO+"  Descricao: "+PE8->PE8_DESCR+"   Qtde Hrs:"+Transform(PE8->PE8_QTDHRS,PesqPict("PE8","PE8_QTDHRS"))+"  Tipo:"+RetX3Combo( "PE8_TPFRT" , PE8->PE8_TPFRT )+"  Motivo:"+PE8->PE8_MOTIVO+"-"+Substr(Posicione("PE0",1,xFilial("PE0")+PE8->PE8_MOTIVO,"PE0_DESC"),1,35)+"   Resp: "+PE8->PE8_COORD+"-"+POSICIONE('RD0',1,XFILIAL('RD0')+PE8->PE8_COORD,"RD0_NOME")
			nLin++
			PE8->(DbSkip())
		EndDo
*/

//Listar Time de Projeto		
	//		@ nLin+6 ,00  PSAY "GPP                   : " + PE5->PE5_GPP    + ' - ' + Substr(Posicione("SA9",1,xFilial("SA9")+PE5->PE5_GPP,"A9_NOME"),1,35)
	//		@ nLin+7 ,00 PSAY "CP Projeto            : " + PE5->PE5_RESPON  + ' - ' + Substr(PE5->PE5_NOMRES,1,35)
	//		@ nLin+8 ,00 PSAY "Gerente AR            : " + PE5->PE5_GAR     + ' - ' + Substr(Posicione("SA9",1,xFilial("SA9")+PE5->PE5_GAR,"A9_NOME"),1,35)
	//		@ nLin+9 ,00 PSAY "Arquiteto             : " + PE5->PE5_ARQ     + ' - ' + Substr(Posicione("SA3",1,xFilial("SA3")+PE5->PE5_ARQ,"A3_NOME"),1,35)
	//		@ nLin+10,00 PSAY "Executivo             : " + PE5->PE5_VEND    + ' - ' + Substr(Posicione("SA3",1,xFilial("SA3")+PE5->PE5_VEND,"A3_NOME"),1,35)
	//		@ nLin+11,00 PSAY "Analista PMO          : " + PE5->PE5_ANAPMO  + ' - ' + Substr(Posicione("SA9",1,xFilial("SA9")+PE5->PE5_ANAPMO,"A9_NOME"),1,35)

//Listar Propostas
//Listar Time de vendas		
//		@ nLin+12,00 PSAY "Proposta Comercial    : " + PE5->PE5_PROPOS
		
	Else
		
		@ nLin   ,00  PSAY "=> DADOS DO PROJETO"
		@ nLin+2 ,00 PSAY "Cliente               : " + PE5->PE5_CLIENT + ' - ' + Substr(Posicione("SA1",1,xFilial("SA1")+PE5->PE5_CLIENT+PE5->PE5_LOJA,"A1_NOME"),1,40)
		@ nLin+3 ,00 PSAY "Projeto               : " + PE5->PE5_PROJET+" - "+SubStr(PE5->PE5_DESPRO,1,45)
		@ nLin+4 ,00 PSAY "Status                : " + RetX3Combo( "PE5_STATUS" , PE5->PE5_STATUS )
		@ nLin+5 ,00 PSAY "Cod.Motivo            : " + PE5->PE5_CODMOT + ' - ' + Posicione("PE0",1,xFilial("PE0")+PE5->PE5_CODMOT,"PE0_DESC")
		nLin+=5
		
	EndIf
	
EndIf
	
Return

/*/{Protheus.doc} RetX3Combo
RetX3Combos
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
Static Function RetX3Combo( cCampo , cConteudo )

Local cCaption	:= ""
Local aSx3Box   := RetSx3Box( Posicione("SX3", 2, cCampo, "X3CBox()" ),,, 1 )
Local nPos		:= Ascan( aSx3Box, { |aBox| aBox[2] = cConteudo } )

If	nPos > 0
	cCaption := AllTrim( aSx3Box[nPos][3] )
EndIf

Return(cCaption)



/*/{Protheus.doc} CA50FatDesp
CA50FatDesp
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
Static Function CA50FatDesp( cCliente, cProjeto )
                     
Local aAliasAnt := GetArea()
Local cQuery    := ""
Local nPos      := 0
Local nTotDesp  := 0 
Local nPerc     := 0

//-- traz notas de pedidos de faturamento de despesa (liquido, sem impostos), proporcional ao projeto
cQuery := "  SELECT EMPRESA, FILIAL, PEDIDO, C6_SERIE, C6_NOTA, C6_DATFAT, TOTALPED, SUM(TOTAL) TOTAL " 
cQuery += "  FROM ( "
cQuery += "  SELECT PFM_GRPCOB AS EMPRESA, PFM_EMPCOB AS FILIAL, PFM_PEDVEN AS PEDIDO , SUM(PFM_VLREPC) AS TOTAL "
cQuery += "  FROM "+RetSQLName("PFM")+" PFM "
cQuery += "  INNER JOIN "+RetSQLName("PF9")+" PF9 ON PF9.D_E_L_E_T_ = '' AND PF9_FILIAL = '" + FWxFilial("PF9") + "' AND PF9_PROJET = '" + cProjeto + "' AND PF9_STATUS = '3' "
cQuery += "  WHERE PFM.D_E_L_E_T_ = '' "
cQuery += "  AND PFM_FILIAL = '" + FWxFilial("PFM") + "' "
cQuery += "  AND PFM_PEDVEN <> '' "
cQuery += "  GROUP BY PFM_GRPCOB , PFM_EMPCOB, PFM_PEDVEN "
cQuery += "  )  "
cQuery += "  INNER JOIN  "
cQuery += "  ( "
cQuery += "  SELECT C6_FILIAL, C6_NUM, C6_SERIE, C6_NOTA, C6_DATFAT, SUM(C6_VALOR) TOTALPED "
cQuery += "  FROM "+RetSQLName("SC6")+" SC6 "
cQuery += "  WHERE SC6.D_E_L_E_T_ = '' "
cQuery += "  AND C6_CLI = '" + cCliente + "' "
cQuery += "  GROUP BY C6_FILIAL, C6_NUM, C6_SERIE, C6_NOTA, C6_DATFAT "
cQuery += "  )  "
cQuery += "  ON C6_FILIAL = FILIAL "
cQuery += "  AND C6_NUM = PEDIDO "
cQuery += "  GROUP BY EMPRESA, FILIAL, PEDIDO, C6_SERIE, C6_NOTA, C6_DATFAT, TOTALPED " 
cQuery += "  ORDER BY EMPRESA, FILIAL, PEDIDO, C6_SERIE, C6_NOTA, C6_DATFAT "

cQuery := ChangeQuery(cQuery)

MPSysOpenQuery(cQuery,"SC6QRY")

dbSelectArea("SC6QRY")

While ! Eof()
	nPerc := Round((TOTAL/TOTALPED)*100,2)
	AAdd(aFatDespesa, {"DESPESA", PEDIDO, C6_SERIE+"-"+C6_NOTA, C6_DATFAT, TOTAL, nPerc})
	nTotDesp += xMoeda(TOTAL,1,nMoedaRel,StoD(C6_DATFAT),2) 
	dbSkip()
Enddo

dbCloseArea()
/*
//-- traz valores de viagens faturados ou a faturar para clientes 
cQuery := "    SELECT LHQ_CODIGO, LHQ_EMISS, LHQ_FATCLI, LHQ_AVULSO, LHQ_PEDIDO, C5_SERIE, C5_NOTA, LHQ_VLFATC "
cQuery += "      FROM "+RetSQLName("LHQ")+" LHQ "
cQuery += "INNER JOIN "+RetSQLName("SC5")+" SC5 "
cQuery += "        ON SC5.D_E_L_E_T_ <> '*' "
cQuery += "       AND C5_FILIAL = '"+xFilial("SC5")+"' "
cQuery += "       AND C5_NUM = LHQ_PEDIDO "
If lGeraMov // quando esta filtrando movimento busca somente os do periodo
	cQuery += "    AND C5_EMISSAO BETWEEN '"+DtoS(dDtMovDe)+"' AND '"+DtoS(dDtMovAte)+"' "
Endif	
cQuery += "     WHERE LHQ.D_E_L_E_T_ <> '*' "
cQuery += "       AND LHQ_FILIAL = '"+xFilial("LHQ")+"' "
cQuery += "       AND LHQ_EMPCLI = '"+cCliente+"' "
cQuery += "       AND LHQ_PROJET = '"+cProjeto+cItem+cVersao+"' "
//cQuery += "       AND LHQ_FLAG IN ('L','V','F') "	// despesas avulsa e de viagem liberadas pelo depto viagens
cQuery += "       AND LHQ_PEDIDO <> '' "
cQuery += "       AND LHQ_FATCLI > 0 "
//If lGeraMov // quando esta filtrando movimento busca somente os do periodo
//	cQuery += " AND LHQ_EMISS BETWEEN '"+DtoS(dDtMovDe)+"' AND '"+DtoS(dDtMovAte)+"' "
//Endif	

cQuery := ChangeQuery(cQuery)

dbUseArea(.T.,"TOPCONN",TCGENQRY(,,cQuery),"LHQQRY",.F.,.T.)

dbSelectArea("LHQQRY")

While ! Eof()
	AAdd(aFatDespesa, {IIf(LHQ_AVULSO=="T","AVULSA ","VIAGEM "), LHQ_PEDIDO, C5_SERIE+"-"+C5_NOTA, LHQ_EMISS, LHQ_VLFATC, LHQ_FATCLI})  
	nTotDesp += xMoeda(LHQ_VLFATC,1,nMoedaRel,StoD(LHQ_EMISS),2) 
	dbSkip()
Enddo

dbCloseArea()
*/
//********** Sumarizando -> FATURAMENTOS **********
nPos := Ascan( aSumario , {|x| Substr(x[2],1,2) ==  "A1" })
If	nPos <> 0
	aSumario[nPos][3] += nTotDesp
EndIf
	
//nVlrFat += nTotDesp

RestArea(aAliasAnt)

Return

/*/{Protheus.doc} RetCusHr
CA50FatDesp
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
User Function RetCusHr(dPData,cPTecnico,nValorSCA,nValor30,nTotHROS,cSCAMotOS,lCalcRV,nVRepasse,EhTerceiro)

Local aAliasAnt  := GetArea()
Local nCusto     := 0  
Local cProdMarca := ""
Local nValTerc   := GetMv("TI_#CTPPJ",,70)       // valor default para terceiros
Local cPMFab     := GetMv("TI_#PMFABD",,"")      // prod/marca da fabrica/CD (para ver se o tecnico eh da fabrica ou CD)
Local nValFab    := GetMv("TI_#VLFABD",,70)      // valor default para tecnicos fabrica/CD
Local nValTec    := GetMv("TI_#CSTANP",,40)      // valor defaul para analistas quando estiver zerado
Local cTecCFP    := GetMv("TI_#TECCFP",,"")      // prefixo para identificar tecnico CFPxxx usado para consumir horas do projeto
Local cMot05     := GetMv("TI_#R50MT5",,"05")    // lista de motivos considerados para erro de produto, pra considerar custo zero
Local lCusTerRea := GetMv("TI_#CTRERV",,.F.)     // se usa o custo real para o terceiro na RV

DEFAULT EhTerceiro:= .F.
DEFAULT nValor30  := 0
DEFAULT nTotHROS  := 0
DEFAULT cSCAMotOS := ""
DEFAULT lCalcRV   := .F.

//-- verifica se e terceiro (se ja tiver valor de terceiro gravado, nem vai para funcao 
//--                          para ir mais rapido, pois nao precisa rodar a query)                    
If nValor30 <> 0
	EhTerceiro := .T.
Endif	

    
//-- se tiver valor de fechamento de terceiro, pega o valor calculado
//-- independente do codigo
If nValor30 <> 0
	                
		nCusto := xMoeda(nValor30,1,nMoedaRel,dPData,2) 

		//-- se tiver valor de repasse, considera o valor de repasse como custo
		If nVRepasse > 0                                
			nCusto := xMoeda(nVRepasse,1,nMoedaRel,dPData,2) 
		Endif

//-- tratamento para PJ ( TA, SI, GARE )
ElseIf EhTerceiro
        
    //-- se nao tem custo no fechamento de terceiro e o codigo do tecnico eh de terceiro
    //-- busca custo default do terceiro no parametro
	nCusto := xMoeda(nValTerc,1,nMoedaRel,dPData,2)

	//-- se tiver valor de repasse, considera o valor de repasse como custo
	If nVRepasse > 0                                
		nCusto := xMoeda(nVRepasse,1,nMoedaRel,dPData,2) 
	Endif

//-- tecnico normal
Else
                   
    //-- pega custo calculado da query
	nCusto := nValorSCA
/*	
	//-- verifica se e prod/marca da fabrica
	//-- se for fixa valor do parametro
	cProdMarca := Posicione("SA9",1,xFilial("SA9")+cPTecnico,"A9_EMPSOFT")
	If cProdMarca $ cPMFab
		nCusto := xMoeda(nValFab,1,nMoedaRel,dPData,2)		
	Endif
*/
	//-- caso nao encontre retorna o default do parametro		
	If nCusto == 0
		nCusto := xMoeda(nValTec,1,nMoedaRel,dPData,2)
	EndIf
	
	//-- se tiver valor de repasse, considera o valor de repasse como custo
	If nVRepasse > 0                                
		nCusto := xMoeda(nVRepasse,1,nMoedaRel,dPData,2) 
	Endif
	
	//-- verifica se o tecnico utilizado eh o tecnico padrao para consumo de horas CFPxxx. Se for considera custo zero.
	If Substr(cPTecnico,1,3) $ cTecCFP
		nCusto := 0
	Endif

Endif
                   
RestArea(aAliasAnt)

Return(nCusto)


/*
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
�������������������������������������������������������������������������ͻ��
���Programa  � SQLxMoeda  �Autor�Jader               � Data �  30/04/2010 ���
�������������������������������������������������������������������������͹��
���Desc.     � Funcao para retorno texto com query SQL simulando a funcao ���
���          � xMoeda() para usar direto em querys                        ���
�������������������������������������������������������������������������͹��
���Uso       � TOTVS                                                      ���
�������������������������������������������������������������������������ͼ��
�����������������������������������������������������������������������������
�����������������������������������������������������������������������������
*/

/*/{Protheus.doc} SQLMoeda
CA50FatDesp
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
User Function SQLMoeda(cCpoValor,cCpoMoedaDe,cCpoMoedaPara,cCpoDataConv,cTipo)

cQMoeda := cCpoValor
/*
Local cDbMs     := Upper(TcGetDb())
Local cQMoeda   := ""
Local cQMoeda0  := ""
Local cQMoeda1  := ""
Local cQMoeda2  := ""
Local nQtdMoeda := 0
Local cCampo    := ""

cQMoeda0 := " (SELECT "
If cDbMs <> "DB2"
	cQMoeda0 += " TOP 1 "
Endif
//-- determina qual campo de moeda sera retornado
cQMoeda0 += "          CASE "
For nQtdMoeda:=2 To 20                                                             
    cCampo   := "M2_MOEDA"+Alltrim(Str(nQtdMoeda))
    If SM2->(FieldPos(cCampo)) > 0
		cQMoeda0 += "  WHEN _CPO_MOEDA="+IIf(cTipo=="C","'","")+Alltrim(Str(nQtdMoeda))+IIf(cTipo=="C","'","")+" THEN "+cCampo+" "
	Endif	
Next nQtdMoeda	
cQMoeda0 += "               ELSE 1 "
cQMoeda0 += "           END TAXA "
cQMoeda0 += "     FROM "+RetSqlName("SM2")+" SM2 "
cQMoeda0 += "    WHERE SM2.D_E_L_E_T_<>'*' "
//-- retorna o soft seek 
cQMoeda0 += "      AND M2_DATA IN (SELECT MIN(M2_DATA) M2_DATA "
cQMoeda0 += "                        FROM "+RetSqlName("SM2")+" SM2 "
cQMoeda0 += "                       WHERE SM2.D_E_L_E_T_<>'*' "
cQMoeda0 += "   	                  AND M2_DATA >= "+cCpoDataConv+" "
cQMoeda0 += "                     UNION "
//-- retorna o eof() com dbskip() -1
cQMoeda0 += "                      SELECT MAX(M2_DATA) M2_DATA "
cQMoeda0 += "                        FROM "+RetSqlName("SM2")+" SM2 "
cQMoeda0 += "                       WHERE SM2.D_E_L_E_T_<>'*') "
If cDbMs == "DB2"
	cQMoeda0 += " FETCH FIRST 1 ROWS ONLY "
Endif	
cQMoeda0 += ") "

//-- troca os campos de moeda "de" e moeda "para"
cQMoeda1 := StrTran(cQMoeda0,"_CPO_MOEDA",cCpoMoedaDe)
cQMoeda2 := StrTran(cQMoeda0,"_CPO_MOEDA",cCpoMoedaPara)

cQMoeda := " CASE WHEN "+cCpoMoedaDe+"="+cCpoMoedaPara+" THEN "+cCpoValor+" " //-- se a moeda de / para forem iguais retorna o proprio valor
cQMoeda += "      WHEN "+cQMoeda2+"=0 THEN 0 " //-- busca valor da moeda para na data (se for 0, retorna 0, resultado da divisao por 0)
cQMoeda += "      ELSE ("+cCpoValor+"*"        //-- se forem diferentes, multiplica valor pela moeda de 
cQMoeda += "             "+cQMoeda1+"/"        //-- busca valor da moeda de na data e divide pela moeda para
cQMoeda += "             "+cQMoeda2+")"        //-- busca valor da moeda para na data
cQMoeda += "  END "

//-- tira espacos em branco
While .T.
	If At(Space(2),cQMoeda) == 0
		Exit
	Endif
	cQMoeda := StrTran(cQMoeda,Space(2),Space(1))	
Enddo

*/
Return(cQMoeda)

/*/{Protheus.doc} RDW50Comiss
RDW50Comiss
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
Static Function RDW50Comiss(lDREAnalitico,cCliente,cLoja,cProjeto,nMoedaRel,lConvMoeda,lGeraMov)

Local aRetorno       := {}
Local nTotalComissao := 0
Local nTotalFaturado := 0
Local aProduto       := {}
Local aComissao      := {}
Local cQuery         := ""
Local cQuery1        := ""
Local nMoedaSB1      := 0
Local nValComiss     := 0
Local nValBase       := 0
Local cDBMS          := Upper(TCGETDB())

DEFAULT lDREAnalitico	:= .F.
DEFAULT lConvMoeda   := .F.
DEFAULT lGeraMov     := .F.

If ! TcCanOpen(RetSQLName("ZXO"))
	Return(aRetorno)
Endif
                         
//-- tabela de provisao de comissao
//-- somente ira gerar registro nesta tabela quando faturar a nota, onde ira fazer a provisao
cQuery := " SELECT ZXO_VEND, ZXO_PRODUT, ZXO_PCOM, ZXO_TIPAGN, CONV_ZXO_BASE, CONV_ZXO_COMISS, ZXO_DTGER " //, B1_MOEDA 
cQuery += "   FROM "+RetSQLName("ZXO")+" ZXO "
//-- join com a tabela de produtos para trazer moeda para conversao
cQuery += "   LEFT JOIN "+RetSQLName("SB1")+" SB1 "
cQuery += "          ON SB1.D_E_L_E_T_ <> '*' "
cQuery += "         AND B1_FILIAL = '"+xFilial("SB1")+"' "
cQuery += "         AND B1_COD = ZXO_PRODUT "
cQuery += " WHERE ZXO.D_E_L_E_T_ <> '*' "
cQuery += "   AND ZXO_FILIAL = '"+xFilial("ZXO")+"' "
cQuery += "   AND ZXO_CLIENT = '"+cCliente+"' "
cQuery += "   AND ZXO_LOJA   = '"+cLoja+"' "
cQuery += "   AND ZXO_PROJET = '"+cProjeto+"' "
cQuery += "   AND ZXO_ORIGEM = 'PROJETO' "  // somente itens gerados por projeto
cQuery += "   AND ZXO_EMPFAT = '"+cEmpAnt+cFilAnt+"' " // considera empresa/filial
cQuery := StrTran(cQuery,"CONV_ZXO_BASE"   ,"ZXO_BASE" )
cQuery := StrTran(cQuery,"CONV_ZXO_COMISS" ,"ZXO_COMISS" )
//cQuery := StrTran(cQuery,"CONV_ZXO_BASE"   ,U_SQLMoeda("ZXO_BASE"   ,"B1_MOEDA",Alltrim(Str(nMoedaRel)),"ZXO_DTGER","N")+" ZXO_BASE "  )
//cQuery := StrTran(cQuery,"CONV_ZXO_COMISS" ,U_SQLMoeda("ZXO_COMISS" ,"B1_MOEDA",Alltrim(Str(nMoedaRel)),"ZXO_DTGER","N")+" ZXO_COMISS ")

//If ! lDREAnalitico
//	cQuery1 := " SELECT B1_MOEDA, SUM(ZXO_BASE) ZXO_BASE, SUM(ZXO_COMISS) ZXO_COMISS "
//	cQuery1 += "   FROM ( "+cQuery+" ) TRBZXO "	
//	cQuery1 += "  GROUP BY B1_MOEDA "
//Else	
	cQuery1 := cQuery
//Endif

//-- tira espacos em branco
While .T.
	If At(Space(2),cQuery1) == 0
		Exit
	Endif
	cQuery1 := StrTran(cQuery1,Space(2),Space(1))	
Enddo
	   
If cDBMS == "DB2"
	cQuery1 += " FOR READ ONLY"
Endif
	
MPSysOpenQuery(cQuery1,"ZXOQRY")

dbSelectArea("ZXOQRY")

While ! ZXOQRY->(Eof())

	nValComiss := ZXOQRY->ZXO_COMISS
	nValBase   := ZXOQRY->ZXO_BASE
//	nMoedaSB1  := Iif(Empty(ZXOQRY->B1_MOEDA),1,ZXOQRY->B1_MOEDA)

	If lDREAnalitico
		AAdd(aComissao,	{ZXOQRY->ZXO_VEND, ZXOQRY->ZXO_PRODUT, ZXOQRY->ZXO_PCOM, nValBase, nValComiss, ZXOQRY->ZXO_TIPAGN})
    Else
		AAdd(aComissao,	{"", "", 0, nValBase, nValComiss, ""})
	Endif
	
	nTotalComissao += nValComiss
	nTotalFaturado += nValBase

	ZXOQRY->(dbSkip())	

Enddo

ZXOQRY->(dbCloseArea())

aRetorno := { nTotalComissao,;								//-- total comissao
			 nTotalFaturado,;                              	//-- total faturado
			 (( nTotalComissao / nTotalFaturado ) * 100),;	//-- total comissao / total faturado
			 aProduto,;										//-- array produtos
			 aComissao }									//-- array comissao

Return(aRetorno)


/*/{Protheus.doc} CA50Faturamento
CA50Faturamento
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
Static Function CA50Faturamento( cCliente, cProjeto, lDREAnalitico, lOnlyArray, lOnlyFile, lGeraMov, dDtMovDe, dDtMovAte )

Local _xw1,nPos                   
Local cPortadorPDD := Alltrim(GetMv("TI_#PORTPDD",,"PDD"))
Local nMoedaPed	   := 1
Local cQuery       := ""
Local cQuery1      := ""
Local cQuery2      := ""
Local cQuery3      := ""
Local cQuery4      := ""
Local nX           := 1
Local nQtdSC6      := 0
Local nValLiqC6    := 0
Local nUntLiqC6    := 0
Local nQuantD1     := 0
Local nUntliqD1    := 0
Local nValLiqD1    := 0
Local nQuantD2     := 0
Local nUntliqD2    := 0
Local nValLiqD2    := 0
Local nQtdSD2      := 0
Local cDBMS        := Upper(TCGETDB())
Local aPedCFP	     := {}
Local cPed         := ""
Local cEmp		 := ""
Local cFil		 := ""
Local lOk          := .F.
Local aPrjFat     := {}
Local nY           := 0

DEFAULT lOnlyArray	:= .F.
DEFAULT lOnlyFile	:= .F.
DEFAULT lGeraMov    := .F.
DEFAULT dDtMovDe    := CtoD("")
DEFAULT dDtMovAte   := dDataBase

//-- se retornar vazio nao teve rateio de faturamento, entao eh ele mesmo 
If Len(aPrjFat) == 0
	AAdd(aPrjFat,{cCliente, cProjeto})
Endif


PEA->(DbsetOrder(1)) //PEA_FILIAL+PEA_PROJET+PEA_PARCEL
PEA->(MsSeek(FWxFilial("PEA")+cProjeto))
While !PEA->(EOF()) .AND. FWxFilial("PEA")+cProjeto == PEA->(PEA_FILIAL+PEA_PROJET) 
	
	If !Empty(PEA->PEA_PEDIDO)
		
			
		nPos := AScan(aPedCFP, {|x| x[1]==PEA->PEA_FILFAT})
		
		If nPos == 0
			AAdd(aPedCFP, {PEA->PEA_FILFAT, PEA->PEA_PEDIDO})
		Else
			If At(PEA->PEA_PEDIDO, aPedCFP[nPos,2]) == 0
				aPedCFP[nPos,2] += "," + PEA->PEA_PEDIDO
			Endif
		Endif
		
		nQuantD2		:= PEA->PEA_QTDPAR
		cMoeda 		:= Alltrim(PEA->PEA_MOEFAT)
		If cMoeda $ "1,2,3,4,5,6,7"
			cCpoVl := "PEA_VFMOE"+cMoeda
		Else	
			cCpoVl := "PEA_VFMOE1"
		Endif	
		nValliqD2	 	:= &("PEA->"+cCpoVl)

		//-- cancelamento igual a 1 e parcela maior, considera a quantidade da parcela
		If PEA->PEA_QTDCAN == 1 .And. PEA->PEA_QTDPAR > 1
			nQuantD1 := PEA->PEA_QTDPAR
		Else
			nQuantD1 := PEA->PEA_QTDCAN
		Endif
		If cMoeda $ "1,2,3,4,5,6,7"
			cCpoVl := "PEA_VLCAN"+cMoeda
		Else	
			cCpoVl := "PEA_VLCAN1"
		Endif	
		
		nValliqD1		:= &("PEA->"+cCpoVl)
		
		//********** Sumarizando -> FATURAMENTOS **********
		nPos := Ascan( aSumario , {|x| Substr(x[2],1,2) ==  "A1" })
		If	nPos <> 0
			aSumario[nPos][3] += nValliqD2
		EndIf
		
		nQtdFat += nQuantD2
		nVlrFat += nValLiqD2
		
		//********** Sumarizando -> DESCONTO/CANCELAMENTO **********
		nPos := Ascan( aSumario , {|x| Substr(x[2],1,2) ==  "A3" })
		If	nPos <> 0
			aSumario[nPos][3] += (nValliqD1*-1)
		EndIf
		
		nQtdDev += nQuantD1
		nVlrDev += nValLiqD1
		
	Endif
	
	PEA->(DbSkip())
EndDo

If lDREAnalitico
	
	//-- inclui empresa HS caso esteja rodando a matriz
	/*
	For nX:=1 To Len(aPedCFP)
		If aPedCFP[nX,1] == "0001"
			cPed := aPedCFP[nX,2]
			AAdd(aPedCFP, {"HS01", cPed})
			Exit
		Endif
	Next nX
	*/
	
	aPedCFP := ASort(aPedCFP,,,{|x,y| x[1] < y[1]})
	
	//�����������������������������������������������������������������������������������������������������Ŀ
	//�	Selecionando Itens dos pedidos de venda X Nota fiscal de saida X Nota fiscal de cancelamento        �
	//�������������������������������������������������������������������������������������������������������
	
	cQuery := " SELECT C6_NUM, C6_ITEM, C6_PRODUTO, C6_ENTREG, C6_TES, C6_CF, C6_QTDVEN, C6_PRCVEN, CONV_C6_VALOR, "
	cQuery += "        C5_MOEDA, C5_EMISSAO, F2_MOEDA, SF1.F1_MOEDA, SF1A.F1_MOEDA AF1_MOEDA,  "
	cQuery += "        D2_DOC, D2_SERIE, D2_EMISSAO, D2_QUANT, D2_PRUNIT, CONV_D2_TOTAL, "
	cQuery += "        SD1.D1_DOC, SD1.D1_SERIE, SD1.D1_EMISSAO, SD1.D1_QUANT, SD1.D1_VUNIT, CONV_D1_TOTAL, "
	cQuery += "        SD1A.D1_DOC AD1_DOC, SD1A.D1_SERIE AD1_SERIE, SD1A.D1_EMISSAO AD1_EMISSAO, SD1A.D1_QUANT AD1_QUANT, SD1A.D1_VUNIT AD1_VUNIT, CONV_AD1_TOTAL, "
	cQuery += "        PEA_MOEFAT, PEA_VFMOE1, PEA_VFMOE2, PEA_VFMOE3, PEA_VFMOE4, PEA_VFMOE5, PEA_VFMOE6, PEA_VFMOE7, PEA_QTDCAN, PEA_VLCAN1, PEA_VLCAN2, PEA_VLCAN3, PEA_VLCAN4, PEA_VLCAN5, PEA_VLCAN6, PEA_VLCAN7 "
	
	cQuery += "   FROM TABLE_SC6 SC6 "
	
	cQuery += "  INNER JOIN TABLE_SC5 SC5 "
	cQuery += "          ON SC5.D_E_L_E_T_ <> '*' "
	cQuery += "         AND C5_FILIAL = 'FILIAL_SC5' "
	cQuery += "         AND C5_NUM = C6_NUM "
	
	cQuery += "   LEFT JOIN TABLE_SD2 SD2 "
	cQuery += "          ON SD2.D_E_L_E_T_ <> '*' "
	cQuery += "         AND D2_FILIAL = 'FILIAL_SD2' "
	cQuery += "         AND D2_PEDIDO = C6_NUM "
	cQuery += "         AND D2_ITEMPV = C6_ITEM "
	
	cQuery += "   LEFT JOIN TABLE_SF2 SF2 "
	cQuery += "          ON SF2.D_E_L_E_T_ <> '*' "
	cQuery += "         AND F2_FILIAL = 'FILIAL_SF2' "
	cQuery += "         AND F2_CLIENTE = D2_CLIENTE "
	cQuery += "         AND F2_LOJA = D2_LOJA "
	cQuery += "         AND F2_DOC = D2_DOC "
	cQuery += "         AND F2_SERIE = D2_SERIE "
	
	cQuery += "   LEFT JOIN TABLE_SD1 SD1 "
	cQuery += "          ON SD1.D_E_L_E_T_ <> '*' "
	cQuery += "         AND SD1.D1_FILIAL  = 'FILIAL_SD1' "
	cQuery += "         AND SD1.D1_SERIORI = D2_SERIE "
	cQuery += "         AND SD1.D1_NFORI   = D2_DOC "
	cQuery += "         AND SD1.D1_COD     = D2_COD "
	cQuery += "         AND SD1.D1_FORNECE = D2_CLIENTE "
	cQuery += "         AND SD1.D1_LOJA    = D2_LOJA "
	cQuery += "         AND SD1.D1_SERIE   <> 'DES' "
	// tratamento para fazer relacionamento com o item origem, mas tambem trazer quando estiver com o item origem vazio
	cQuery += "         AND ( ( SD1.D1_ITEMORI <> ' ' AND SD1.D1_ITEMORI = D2_ITEM ) OR ( SD1.D1_ITEMORI = ' ' ) ) "
	
	cQuery += "   LEFT JOIN TABLE_SF1 SF1 "
	cQuery += "          ON SF1.D_E_L_E_T_ <> '*' "
	cQuery += "         AND SF1.F1_FILIAL = 'FILIAL_SF1' "
	cQuery += "         AND SF1.F1_DOC = SD1.D1_DOC "
	cQuery += "         AND SF1.F1_SERIE = SD1.D1_SERIE "
	cQuery += "         AND SF1.F1_FORNECE = SD1.D1_FORNECE "
	cQuery += "         AND SF1.F1_LOJA = SD1.D1_LOJA "
	
	cQuery += "   LEFT JOIN ( SELECT D1_SERIORI, D1_NFORI, D1_ITEMORI, D1_FORNECE, D1_LOJA, D1_COD, "
	cQuery += "                      MAX(D1_DOC) D1_DOC, 'DES' D1_SERIE, MAX(D1_EMISSAO) D1_EMISSAO, "
	cQuery += "                      SUM(D1_QUANT) D1_QUANT, SUM(D1_VUNIT) D1_VUNIT, SUM(D1_TOTAL) D1_TOTAL "
	cQuery += "                 FROM TABLE_SD1 SD1 "
	cQuery += "                WHERE SD1.D_E_L_E_T_ <> '*' "
	cQuery += "                  AND SD1.D1_FILIAL = 'SD1_FILIAL' "
	cQuery += "                  AND SD1.D1_SERIE  = 'DES' "
	cQuery += "                GROUP BY D1_SERIORI, D1_NFORI, D1_ITEMORI, D1_FORNECE, D1_LOJA, D1_COD ) SD1A "
	cQuery += "          ON SD1A.D1_SERIORI = D2_SERIE "
	cQuery += "         AND SD1A.D1_NFORI   = D2_DOC "
	cQuery += "         AND SD1A.D1_COD     = D2_COD "
	cQuery += "         AND SD1A.D1_FORNECE = D2_CLIENTE "
	cQuery += "         AND SD1A.D1_LOJA    = D2_LOJA "
	// tratamento para fazer relacionamento com o item origem, mas tambem trazer quando estiver com o item origem vazio
	cQuery += "         AND ( ( SD1A.D1_ITEMORI <> ' ' AND SD1A.D1_ITEMORI = D2_ITEM ) OR ( SD1A.D1_ITEMORI = ' ' ) ) "
	
	cQuery += "   LEFT JOIN TABLE_SF1 SF1A "
	cQuery += "          ON SF1A.D_E_L_E_T_ <> '*' "
	cQuery += "         AND SF1A.F1_FILIAL = 'SF1_FILIAL' "
	cQuery += "         AND SF1A.F1_DOC = SD1A.D1_DOC "
	cQuery += "         AND SF1A.F1_SERIE = SD1A.D1_SERIE "
	cQuery += "         AND SF1A.F1_FORNECE = SD1A.D1_FORNECE "
	cQuery += "         AND SF1A.F1_LOJA = SD1A.D1_LOJA "
	
	cQuery += "   LEFT JOIN TABLE_PEA PEA "
	cQuery += "          ON PEA.D_E_L_E_T_ <> '*' "
	cQuery += "         AND PEA_FILIAL = 'FILIAL_PEA' "
	cQuery += "         AND PEA_CLIENT = '"+cCliente+"' "
	cQuery += "         AND PEA_PROJET = '"+cProjeto+"' "
	cQuery += "         AND PEA_PEDIDO = C6_NUM "
	cQuery += "         AND PEA_PARCEL = C6_XPARCFP "


	cQuery += "  WHERE SC6.D_E_L_E_T_ <> '*' "
	cQuery += "    AND C6_FILIAL = 'FILIAL_SC6' "
	cQuery += "    AND C6_NUM IN _PEDIDOS_ "
	
	//-- ajusta os campos de valores para query de conversao de moeda
	cQuery := StrTran(cQuery,"CONV_C6_VALOR"  ,"C6_VALOR")
	cQuery := StrTran(cQuery,"CONV_D2_TOTAL"  ,"CASE WHEN D2_QUANT = 0 THEN 0 ELSE (C6_VALOR * (D2_QUANT / D2_QUANT)) END D2_TOTAL ")
	cQuery := StrTran(cQuery,"CONV_D1_TOTAL"  ,"CASE WHEN SD1.D1_QUANT = 0 THEN 0 ELSE (C6_VALOR * (SD1.D1_QUANT / SD1.D1_QUANT)) END D1_TOTAL ")
	cQuery := StrTran(cQuery,"CONV_AD1_TOTAL" ,"SD1A.D1_TOTAL AD1_TOTAL ")
	
	For nX:=1 To Len(aPedCFP)
		
		cEmp := Substr(aPedCFP[nX,1],1,2)
		cFil := Substr(aPedCFP[nX,1],3,FWGETTAMFILIAL)
		cPed := FormatIn(aPedCFP[nX,2],",")
		
		//-- ajusta os alias das tabelas para empresa atual
		cQuery3 := cQuery
		
		cQuery3 := StrTran(cQuery3,"TABLE_SC5","SC5"+cEmp+"0")
		cQuery3 := StrTran(cQuery3,"TABLE_SC6","SC6"+cEmp+"0")
		cQuery3 := StrTran(cQuery3,"TABLE_SF1","SF1"+cEmp+"0")
		cQuery3 := StrTran(cQuery3,"TABLE_SD1","SD1"+cEmp+"0")
		cQuery3 := StrTran(cQuery3,"TABLE_SF2","SF2"+cEmp+"0")
		cQuery3 := StrTran(cQuery3,"TABLE_SD2","SD2"+cEmp+"0")
		cQuery3 := StrTran(cQuery3,"TABLE_PEA",RetSQLName("PEA"))
		cQuery3 := StrTran(cQuery3,"TABLE_SCA","SCA"+cEmp+"0")
		
		cQuery3 := StrTran(cQuery3,"FILIAL_SC5",cFil)
		cQuery3 := StrTran(cQuery3,"FILIAL_SC6",cFil)
		cQuery3 := StrTran(cQuery3,"FILIAL_SF1",cFil)
		cQuery3 := StrTran(cQuery3,"FILIAL_SD1",cFil)
		cQuery3 := StrTran(cQuery3,"FILIAL_SF2",cFil)
		cQuery3 := StrTran(cQuery3,"FILIAL_SD2",cFil)
		cQuery3 := StrTran(cQuery3,"FILIAL_PEA",xFilial("PEA"))
		cQuery3 := StrTran(cQuery3,"FILIAL_SCA",cFil)
		
		cQuery3 := StrTran(cQuery3,"_PEDIDOS_",cPed)
		
		
		cQuery3 += " ORDER BY C6_NUM, C6_ITEM "
		
		//-- tira espacos em branco
		While .T.
			If At(Space(2),cQuery3) == 0
				Exit
			Endif
			cQuery3 := StrTran(cQuery3,Space(2),Space(1))
		Enddo
		
		If cDBMS == "DB2"
			cQuery3 += " FOR READ ONLY"
		Endif
		
		MPSysOpenQuery(cQuery3,"SC6QRY")
		
		//-- ajuste dos campos data
		If lDREAnalitico
			TcSetField("SC6QRY", "C6_ENTREG"  , "D")
			TcSetField("SC6QRY", "C6_DATFAT"  , "D")
			TcSetField("SC6QRY", "D2_EMISSAO" , "D")
			TcSetField("SC6QRY", "D1_EMISSAO" , "D")
			TcSetField("SC6QRY", "AD1_EMISSAO" , "D")
		Endif
		
		While ! SC6QRY->(Eof())
			
			//���������������������������������������Ŀ
			//�Tratamento de Moedas                   �
			//�����������������������������������������
			nMoedaPed := SC6QRY->C5_MOEDA
			
			nQtdSC6   := SC6QRY->C6_QTDVEN

			cMoeda 	:= Alltrim(SC6QRY->PEA_MOEFAT)
			If cMoeda $ "1,2,3,4,5,6,7"
				cCpoVl := "PEA_VFMOE"+cMoeda
			Else	
				cCpoVl := "PEA_VFMOE1"
			Endif	

			nValLiqC6 := &("SC6QRY->"+cCpoVl)
			nUntLiqC6 := NoRound(nValLiqC6 / nQtdSC6,2)
			
			nQtdSD2   := SC6QRY->D2_QUANT
			nValLiqD2 := &("SC6QRY->"+cCpoVl)
			nUntliqD2 := NoRound(nValLiqD2 / nQtdSD2,2)
			
			nQuantD1  := 0
			nUntliqD1 := 0
			nValLiqD1 := 0
			
			//-- notas de cancelamento
			If SC6QRY->D1_QUANT > 0
				
				
				//-- cancelamento igual a 1 e parcela maior, considera a quantidade da parcela
				//				If SC6QRY->D1_QUANT == 1 .And. SC6QRY->ZCU_QUANT > 1
				//					nQuantD1 := SC6QRY->ZCU_QUANT
				//				Else
				//					nQuantD1 := SC6QRY->D1_QUANT
				//				Endif
				
				nQuantD1 := SC6QRY->D1_QUANT

				cMoeda := Alltrim(SC6QRY->PEA_MOEFAT)
				If cMoeda $ "1,2,3,4,5,6,7"
					cCpoVl := "PEA_VLCAN"+cMoeda
				Else
					cCpoVl := "PEA_VLCAN1" 
				Endif	
						
				nValliqD1 := &("SC6QRY->"+cCpoVl)
				nUntliqD1 := Round(nValliqD1 / nQuantD1,2)
				
				
				//-- notas de desconto
			ElseIf SC6QRY->AD1_QUANT > 0
				
				nQuantD1 := SC6QRY->AD1_QUANT
				nQuantD1 := SC6QRY->AD1_QUANT
				
				//-- calcula o unitario da nota dividindo o total convertido pela quantidade (para considerar sem imposto)
				nUntliqD1 := Round(SC6QRY->AD1_TOTAL / SC6QRY->AD1_QUANT,2)
				nValLiqD1 := SC6QRY->AD1_TOTAL
				
				
			Endif
			
			//			If lDREAnalitico
			
			AAdd( aItemFaturado, { cChaveProjeto ,;
				SC6QRY->C6_PRODUTO ,;
				SC6QRY->C6_TES ,;
				PadR(SC6QRY->C6_CF,4) ,;
				SC6QRY->C6_NUM ,;
				SC6QRY->C6_ENTREG ,;
				nQtdSC6 ,;
				nUntLiqC6 ,;
				nValLiqC6,;
				SC6QRY->D2_DOC ,;
				SC6QRY->D2_SERIE ,;
				SC6QRY->D2_EMISSAO ,;
				nQtdSD2 ,;
				nUntliqD2 ,;
				nValLiqD2 ,;
				If(!Empty(SC6QRY->D1_DOC),SC6QRY->D1_DOC,SC6QRY->AD1_DOC) ,;
				PadR(If(!Empty(SC6QRY->D1_DOC),SC6QRY->D1_SERIE,SC6QRY->AD1_SERIE),3) ,;
				If(!Empty(SC6QRY->D1_DOC),SC6QRY->D1_EMISSAO,SC6QRY->AD1_EMISSAO) ,;
				If(!Empty(SC6QRY->D1_DOC),SC6QRY->D1_QUANT,SC6QRY->AD1_QUANT) ,;
				(nUntliqD1*-1) ,;
				(nValLiqD1*-1) ,;
				cEmp+cFil})
			
			SC6QRY->(dbSkip())
			
		EndDo
		
		SC6QRY->(dbCloseArea())
		
	Next nX
	
Endif
	

	
//�����������������������������������������������������������������������������������������������������Ŀ
//�	Selecionando Posicao financeira                                                                     �
//�������������������������������������������������������������������������������������������������������

cQuery  := ""
cQuery1 := ""
cQuery2 := ""
cQuery3 := ""
cQuery4 := ""

cQuery := " SELECT E1_PREFIXO, E1_NUM, E1_PARCELA, E1_TIPO, E1_EMISSAO, E1_VENCREA, "
cQuery += "        CONV_E1_VALOR, CONV_E1_SALDO, E1_PORTADO, E1_MOEDA "
cQuery += "   FROM TABLE_SE1 SE1 "
cQuery += "  WHERE SE1.D_E_L_E_T_ <> '*' "
cQuery += "    AND E1_FILIAL = 'FILIAL_SE1' "
If ! lDREAnalitico	
	cQuery += " AND E1_PORTADO = '"+cPortadorPDD+"' "
Endif
cQuery += "    AND EXISTS ( "
cQuery += "                 SELECT C6_NUM "
cQuery += "                   FROM TABLE_SC6 SC6 "
cQuery += "                  WHERE SC6.D_E_L_E_T_ <> '*' " 
cQuery += "                    AND C6_FILIAL = 'FILIAL_SC6' "
cQuery += "                    AND C6_SERIE = E1_PREFIXO "
cQuery += "                    AND C6_NOTA = E1_NUM "
cQuery += "                    AND C6_CLI = E1_CLIENTE "
cQuery += "                    AND C6_LOJA = E1_LOJA "
cQuery += "                    AND C6_NUM IN _PEDIDOS_ " 
cQuery += "                ) "

cQuery := ChangeQuery(cQuery)

cQuery := StrTran(cQuery,"FOR READ ONLY","")

//-- ajusta os campos de valores para query de conversao de moeda
cQuery := StrTran(cQuery,"CONV_E1_VALOR" ," E1_VALOR ")
cQuery := StrTran(cQuery,"CONV_E1_SALDO" ," E1_SALDO ")
//cQuery := StrTran(cQuery,"CONV_E1_VALOR" ,U_SQLMoeda("E1_VALOR" ,"E1_MOEDA",Alltrim(Str(nMoedaRel)),"E1_EMISSAO","N")+" E1_VALOR ")
//cQuery := StrTran(cQuery,"CONV_E1_SALDO" ,U_SQLMoeda("E1_SALDO" ,"E1_MOEDA",Alltrim(Str(nMoedaRel)),"E1_EMISSAO","N")+" E1_SALDO ")


For nX:=1 To Len(aPedCFP)

	cEmp := Substr(aPedCFP[nX,1],1,2)
	cFil := Substr(aPedCFP[nX,1],3,FWGETTAMFILIAL)
	cPed := FormatIn(aPedCFP[nX,2],",")

	//-- ajusta os alias das tabelas para empresa atual
	cQuery3 := cQuery

	cQuery3 := StrTran(cQuery3,"TABLE_SE1","SE1"+cEmp+"0")
	cQuery3 := StrTran(cQuery3,"TABLE_SC6","SC6"+cEmp+"0")

	cQuery3 := StrTran(cQuery3,"FILIAL_SE1",cFil)
	cQuery3 := StrTran(cQuery3,"FILIAL_SC6",cFil)

	cQuery3 := StrTran(cQuery3,"_PEDIDOS_",cPed)

	If ! lDREAnalitico
	
		cQuery4 := " SELECT E1_MOEDA, E1_PORTADO, SUM(E1_VALOR) E1_VALOR, SUM(E1_SALDO) E1_SALDO "
		cQuery4 += "   FROM ( "+cQuery3+" ) SE1TRB "
		cQuery4 += "  GROUP BY E1_MOEDA, E1_PORTADO "
		
		cQuery3 := cQuery4
	
	Endif
	
	If cDBMS == "DB2"
		cQuery3 += " FOR READ ONLY"
	Endif
		
	MPSysOpenQuery(cQuery3,"SE1QRY")
			
	If lDREAnalitico		
		TcSetField("SE1QRY", "E1_EMISSAO"  , "D")
		TcSetField("SE1QRY", "E1_VENCREA"  , "D")
	Endif
		
	dbSelectArea("SE1QRY")
		
	While ! SE1QRY->(Eof())
		//���������������������������������������Ŀ
		//�Tratamento de Moedas                   �
		//�����������������������������������������
		//If !lConvMoeda .AND. IIf(Empty(SE1QRY->E1_MOEDA),1,SE1QRY->E1_MOEDA) <> nMoedaRel
		//	SE1QRY->(DbSkip())
		//	Loop
		//EndIf
	
		nE1VALOR := SE1QRY->E1_VALOR
		nE1SALDO := SE1QRY->E1_SALDO
					 
		If lDREAnalitico
							
			AAdd( aTitulos , { cChaveProjeto ,;
								SE1QRY->E1_EMISSAO ,;
								SE1QRY->(E1_PREFIXO+"-"+E1_NUM+"-"+E1_PARCELA) ,;
								SE1QRY->E1_TIPO ,;
								SE1QRY->E1_VENCREA ,;
								((nE1SALDO-nE1VALOR)*-1) ,;
								(nE1SALDO*-1) ,;
								If(SE1QRY->E1_PORTADO$cPortadorPDD,(nE1SALDO*-1),0) })
	
		Endif
											
		If SE1QRY->E1_PORTADO $ cPortadorPDD
			//********** Sumarizando -> PERDA POR INADIMPLENCIA
			nPos := Ascan( aSumario , {|x| Substr(x[2],1,2) ==  "A5" })
			If	nPos <> 0
				aSumario[nPos][3] += (nE1SALDO*-1)
			EndIf
		EndIf
				
		SE1QRY->(dbSkip())
	
	EndDo
		
	SE1QRY->(dbCloseArea())

Next nX
	
Return



/*/{Protheus.doc} A50Quebra
A50Quebra
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
Static Function A50Quebra(nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT)

Local lCabe := .F.

Default cFilZCT := cFilAnt

If	nLin > 64
	Cabec(Titulo,Cabec1,Cabec2,NomeProg,Tamanho,nTipo)
	nLin := 4
	A50Cabecalho( .F. , .F. , .F. , nMenu, lGeraMov, lBubaMov, lCalcRV, cFilZCT )
	nLin += 2
	A50Menu(nMenu)
	lCabe := .T.
EndIf

Return(lCabe)


/*/{Protheus.doc} A50Menu
Impressao do Demontrativo de resultado de projetos - DRE
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
Static Function A50Menu(nMenu)

If nMenu == 1

	@ nLin+1, 00 PSAY "                                      |--------------------- P E D I D O S ---------------||------------------------- F A T U R A S ------------------------||------------------- C A N C E L A M E N T O S ------------------|"
	@ nLin+2, 00 PSAY "GRP/FILIAL    PRODUTO         TES CFOP PEDIDO DATA       QTDE        VL UNIT.     VL TOTAL  DOCUMENTO     EMISSAO    QUANTIDADE   VL UNITARIO       VL TOTAL  DOCUMENTO     EMISSAO    QUANTIDADE   VL UNITARIO       VL TOTAL"
	@ nLin+3, 00 PSAY "------------- --------------- --- ---- ------ ---------- --------- ---------- ------------  ------------- ---------- ---------- ------------- --------------  ------------- ---------- ---------- ------------- --------------"
	nLin+=3

ElseIf nMenu == 2

	@ nLin+1, 05 PSAY "No.OS      EMISSAO    TECNICO                                         KM     VALOR KM   ESTACIONAMENTO       PEDAGIO     ZONA AZUL      COLETIVO          TAXI     REFEICOES       CELULAR        OUTROS         TOTAL"
	@ nLin+2, 05 PSAY "---------- ---------- ------------------------------------ ------------- ------------ ---------------- ------------- ------------- ------------- ------------- ------------- ------------- ------------- -------------"
	nLin+=2

ElseIf nMenu == 3

	@ nLin+1, 00 PSAY "                                                                                       |---------- C U S T O S -------||---- R E C E I T A S ---|
	@ nLin+2, 00 PSAY "No.OS      PRODUTO         DATA OS       INICIO   TERMINO  TRASLADO     ABONO     TOTAL UNITARIO TRASLADO        HORAS        HORAS     TRASLADO  TECNICO                          MOT DEB.PRJ PREMIO PEDIDO DT.DIGIT   CARGO "
	@ nLin+3, 00 PSAY "---------- --------------- ---------- --------- --------- --------- --------- --------- -------- -------- ------------  ----------- ------------  -------------------------------- --- ------- ------ ------ ---------- ------"
	nLin+=3

ElseIf nMenu == 4

	@ nLin+1, 05 PSAY "FUNCIONARIO                                                            EMISSAO   DETALHAMENTO                                                                              QUANTIDADE     REEMBOLSAVEL"
	@ nLin+2, 05 PSAY "---------------------------------------------------------------------  --------  ----------------------------------------------------------------------------------------- ---------- ----------------"
	nLin+=2

ElseIf nMenu == 5

	@ nLin+1, 05 PSAY "EMISSAO     PRF/NUMERO/PARC.      TIPO  VENC.REAL            VALOR BAIXA       VALOR SALDO   INADIMPLENCIA"
	@ nLin+2, 05 PSAY "----------  --------------------- ----  ----------- -------------------- ----------------- ---------------"
	nLin+=2

ElseIf nMenu == 6

	@ nLin+1, 10 PSAY "PRODUTO                VLR TOTAL     VLR UNITARIO   DATA"
	@ nLin+2, 10 PSAY "-------------- ----------------- ---------------- --------"
	nLin+=2

ElseIf nMenu == 7

	@ nLin+1, 10 PSAY "TECNICO  PRODUTO             VLR COMISSAO       VALOR BASE       % COMISSAO   TIPO VENDEDOR"
	@ nLin+2, 10 PSAY "-------  --------------- ---------------- ---------------- ----------------   -------------"
	nLin+=2

ElseIf nMenu == 8

	@ nLin+1, 05 PSAY "MOTIVO                                                        HORAS   CUSTO HORAS RECEITA HORAS"
	@ nLin+2, 05 PSAY "---------------------------------------------------------- -------- ------------- -------------"
	nLin+=2

ElseIf nMenu == 9

	@ nLin+1, 10 PSAY "DATA      QTD. FAT.  VLR. FATURAM.  QT.BUFFER    VLR.BUFFER  QT.BACKLOG    VLR.BACKLOG"
	@ nLin+2, 10 PSAY "-------- ---------- -------------- ---------- -------------- ---------- --------------"
	//                 99/99/99 999,999.99 999,999,999.99 999,999.99 999,999,999.99 999,999.99 999,999,999.99  
	nLin+=2

ElseIf nMenu == 10

	@ nLin+1, 05 PSAY "TIPO    PEDIDO DOCUMENTO     DATA             VALOR     %  "
	@ nLin+2, 05 PSAY "------- ------ ------------- -------- -------------- ------"
	//                 1234567 123456 111-111111111 99/99/99 999,999,999.99 999.99% 
	nLin+=2

EndIf

Return


/*/{Protheus.doc} IsTerceiro
IsTerceiro
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
Static Function IsTerceiro(cCodTec)
Local lRet 	:= .F.
Local aArea	:= GetArea()

RD0->(DbSetOrder(1)) //RD0_FILIAL+RD0_CODIGO
If RD0->(MsSeek(FWxFilial("RD0")+cCodTec))
	If U_Franquia(cEmpAnt+cFilAnt)
		PEP->(DbSetOrder(2)) //PEP_FILIAL+PEP_CODTEC+PEP_FORNEC+PEP_LOJFOR
		If PEP->(MsSeek(FWxFilial("PEP")+cCodTec)) .AND. !Empty(PEP->(PEP_FORNEC+PEP_LOJFOR)) 
			lRet := .T.
		EndIf	
	Else
		If !Empty(RD0->(RD0_FORNEC+RD0_LOJA))
			lRet := .T.
		EndIf
	EndIf

EndIf

RestArea(aArea)
Return lRet

/*/{Protheus.doc} RetHora
RetHora
<AUTHOR>
@since 28/01/2016
@version 1.0
/*/
STATIC Function RetHora(nTime,nTam)

LOCAL cRetTime
If nTam == NIL
	nTam := 7
EndIf

nTime    := Int(nTime) + ( ( nTime - Int(nTime) ) * 0.6 )
cRetTime := StrTran(Str(nTime,nTam,2),".",":")

Return(cRetTime)

