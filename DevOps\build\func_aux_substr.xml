<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project basedir="." default="substr">

	<tstamp />

	<!-- carrega as variaveis de ambiente -->
	<property name="textOriginal" 	value="" />
	<property name="textARemover" 	value="" />
    <property name="textAInserir" 	value="" />

	<!--	Dependencias 	-->
	<taskdef resource="net/sf/antcontrib/antcontrib.properties">
		<classpath>
			<pathelement location="${env.ANT_HOME}/lib/ant-contrib-0.3.jar"/>
		</classpath>
	</taskdef>
	<taskdef resource="net/sf/antcontrib/antlib.xml" >
		<classpath>
			<pathelement location="${env.ANT_HOME}\lib\ant-contrib-1.0b3.jar"/>
		</classpath>
	</taskdef>	

    <!-- Substitui dentro de um texto origem o texto indicado como remover pelo texto indicado como a Inserir -->
    <target name="substr" description="Substitui dentro de um texto origem o texto indicado como remover pelo texto indicado como a Inserir">
		<!-- impressao em tela apenas para teste
		<echo message="Dados da string que será tratada:" />
		<echo message=" - Texto origem : ${textOriginal}" />
		<echo message=" - Texto Remover: ${textARemover}" />
		<echo message=" - Texto Inserir: ${textAInserir}" />
		-->
        <script language="javascript">
            var origem = project.getProperty("textOriginal");
            var destino= origem.replace(project.getProperty("textARemover"), project.getProperty("textAInserir"));
            project.setProperty("substrReturn", destino);                
        </script>
		<!-- impressao em tela apenas para teste
		<echo message=" - Texto a devolver: ${substrReturn}" />
		-->
    </target>

	

</project>