#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVF012     
Callback de  envio do arquivo para actio
@type  Function
<AUTHOR> Menabue Lima
@since 11/11/2023
@version 1.0
	/*/
User Function TIRVF012(cCodReq,cResponse,cAsync,cStatReq)

	Local oReceptor	:=TIINTERCEPTOR():New()
	Local oJson 	:= JsonObject():new()
	Local oBody 	:= JsonObject():new()
	Local oActio	:= TIRVACTIO():New()
	Local aAreaP92  := P92->(GetArea())
	Local nI

	If (cStatReq == "2")//Processado a REquisicao
		oJson:fromJson( (cResponse))
		oBody:fromJson('{ "items":'+P37->P37_BODY+' }')
		iF ValType(oBody['items'])== "A"
			aItems:=oBody['items']
			For nI := 1 to Len(aItems)
				P92->(DbSetOrder(3)) // P92_FILIAL+P92_IDINTE
				If P92->(DbSeek(xFilial("P92")+aItems[nI]['itemCode']))
					oReceptor:DelFile(P92->P92_FILENA,P92->P92_CAMINH)
				EndIf
			Next nI
		EndIF
		If oJson:hasProperty("statusCode") .And. oJson['statusCode'] == 200
			P92->(DbSetOrder(2)) // P92_FILIAL+P92_FILP37+P92_CODP37+P92_CODAPI+P92_DATA
			If P92->(DbSeek(xFilial("P92")+P37->P37_FILIAL+P37->P37_COD+P37->P37_CODAPI+DTOS(P37->P37_DATA)))
				oReceptor:DelFile(P92->P92_FILENA,P92->P92_CAMINH)
			EndIf
		ElseIf oJson:hasProperty("mesagesList") .And. ValType(oJson['mesagesList']) == "A"
			For nI := 1 to Len(oJson['mesagesList'])
				If oJson['mesagesList'][nI]['item']['hasError']
					RecLock("P37",.F.)
					P37->P37_STATUS := "5"
					MsUnLock()
					Exit
				EndIF
			Next
		EndIF
	EndIF
	P92->(RestArea(aAreaP92))
	aSize(aAreaP92,0)
	FreeObj(oActio)
	FreeObj(oReceptor)
	FreeObj(oJson)
	FreeObj(oBody)
Return
