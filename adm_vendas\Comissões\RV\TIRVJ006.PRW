#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVJ006     
Job Responsavel por criar a P37 para consultar a API do PSA View  APontamentos PSA PRODUTIVIDADE
@type  Function
<AUTHOR> Menabue Lima
@since 27/10/2023
@version 1.0
U_TIRVJ006(,,,"2023-11-01","2023-11-30")
U_TIRVJ006(,,,"2023-10-01","2023-10-31")
U_TIRVJ006(,,,"2023-09-01","2023-10-01")
U_TIRVJ006(,,,"2023-08-01","2023-09-01")
U_TIRVJ006(,,,"2023-07-01","2023-08-01")
	/*/
User Function TIRVJ006(nCount,nPage,cCodReq,cIni,cFim)
	Local oReceptor:=TIINTERCEPTOR():New()
	Local cFecthXml :=""
	Local cApiPSA:=GetMv("TI_RVJ006A" , .F., "RVF006")
	Local nMonth:=GetMv("TI_RVJ06NM" , .F., 5)
	Default nCount := GetMv("TI_RVJ06CT" , .F., 5000)
	Default nPage := 1
	Default cCodReq:=""
	Default cIni :=""
	Default cFim := ""
	cFecthXml +='<fetch no-lock="true" latematerialize="true" count="'+cValToChar(nCount)+'" page="'+cValToChar(nPage)+'">'
	cFecthXml +='<entity name="msdyn_timeentry">
	cFecthXml +='<attribute name="msdyn_timeentryid" alias="APONTAMENTO_ID" />
	cFecthXml +='<attribute name="modifiedon" alias="DATA_ULT_MODIFICACAO_APONTAMENTO" />
	cFecthXml +='<attribute name="msdyn_type" alias="TP_APONTAMENTO" />
	cFecthXml +='<attribute name="msdyn_date" alias="DATA_APONTAMENTO" />
	cFecthXml +='<attribute name="msdyn_entrystatus" alias="STATUS_APONTAMENTO" />
	cFecthXml +='<attribute name="msdyn_duration" alias="MINUTOS_PRODUTIVIDADE" />
	cFecthXml +='<attribute name="ctm_dt_inicio" alias="DT_INICIO" />
	cFecthXml +='<attribute name="ctm_dt_fim" alias="DT_FIM" />
	cFecthXml +='<attribute name="ctm_totalhoras" alias="TOTAL_MINUTOS" />
	cFecthXml +='<attribute name="ctm_horasexcedentes" alias="HORAS_EXCEDENTES" />
	cFecthXml +='<attribute name="ctm_dt_inicio_extra" alias="DT_INICIO_EXTRA" />
	cFecthXml +='<attribute name="ctm_dt_fim_extra" alias="DT_FIM_EXTRA" />
	cFecthXml +='<order attribute="msdyn_bookableresource" descending="false" />
	cFecthXml +='<link-entity name="msdyn_project" from="msdyn_projectid" to="msdyn_project" link-type="outer" alias="projeto">
	cFecthXml +='<attribute name="msdyn_subject" alias="NOME_PROJETO" />
	cFecthXml +='<attribute name="ctm_codigo" alias="CODIGO_PROJETO" />
	cFecthXml +='<link-entity name="bookableresource" from="bookableresourceid" to="ctm_principalcp" alias="cp">
	cFecthXml +='<attribute name="name" alias="NOME_CP" />
	cFecthXml +='<link-entity name="systemuser" from="systemuserid" to="userid" alias="cp_usuario">
	cFecthXml +='<attribute name="ctm_st_cpf" alias="CPF_CP" />
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="salesorder" from="salesorderid" to="msdyn_salesorderid" alias="contrato">
	cFecthXml +='<attribute name="ctm_codigo" alias="NRO_PROPOSTA" />
	cFecthXml +='<attribute name="ctm_pl_tipoinclusao" alias="TIPO_INCLUSAO" />
	cFecthXml +='<attribute name="ctm_pl_modalidadeprojeto" alias="MODALIDADE_PROJETO_COD" />
	cFecthXml +='<attribute name="ctm_tipodeprojeto" alias="TIPO_PROJETO" />
	cFecthXml +='<attribute name="ctm_opcaotipoprojeto" alias="TIPO_NAO_FATURAVEL" />
	cFecthXml +='<link-entity name="ctm_empresafilial" from="ctm_empresafilialid" to="ctm_empresafilial" link-type="outer" alias="empfilial">
	cFecthXml +='<attribute name="ctm_codigo" alias="CODIGO_FILIAL" />
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="bookableresource" from="bookableresourceid" to="msdyn_bookableresource" alias="recurso">
	cFecthXml +='<attribute name="name" alias="NOME_RECURSO" />
	cFecthXml +='<attribute name="resourcetype" alias="TIPO_RECURSO" />
	cFecthXml +='<link-entity name="systemuser" from="systemuserid" to="userid" link-type="outer" alias="recurso_usuario">
	cFecthXml +='<attribute name="ctm_st_cpf" alias="CPF_RECURSO_USUARIO" />
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="contact" from="contactid" to="contactid" link-type="outer" alias="recurso_contato">
	cFecthXml +='<attribute name="ctm_st_cpf" alias="CPF_RECURSO_CONTATO" />
	cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="ctm_mes_contabil" from="ctm_mes_contabilid" to="ctm_mes_contabilid" link-type="inner" alias="mes_contabil">
	cFecthXml +='<attribute name="ctm_name" alias="MES_CONTABIL" />
	cFecthXml +='<attribute name="ctm_data_fim" alias="DT_CTB_FIN" />
	cFecthXml +='<attribute name="ctm_data_inicio" alias="DT_CTB_INI" />

	cFecthXml +='<filter>
	cFecthXml +="<condition attribute='ctm_name' operator='eq' value='"+cIni+"' />
	
	cFecthXml +='</filter>
	cFecthXml +='</link-entity>

	cFecthXml +='</entity>
	cFecthXml +='</fetch>
	cCodP37:= oReceptor:GetP37COD()
	BeginTran()

	Reclock("P37",.T.)
	P37->P37_FILIAL:= Xfilial("P37")
	P37->P37_COD :=cCodP37
	P37->P37_DATA:= ddatabase
	P37->P37_HORA:= TIME()
	P37->P37_PATH2:=EncodeUtf8("/api/data/v9.2/msdyn_timeentries?$count=true&fetchXml="+ESCAPE(cFecthXml))
	P37->P37_CODAPI:=cApiPSA
	If P36->(DbSeek(xFilial("P36")+P37->P37_CODAPI))
		P37->P37_URL   := P36->P36_URL
	EndIf
	P37->P37_METHOD:= "GET"
	P37->P37_ASYNC1:= "N"
	P37->P37_STATUS:= "0"
	P37->P37_TIPORE:= "1"//Envio
	P37->P37_CALLBA:= "U_TIRVF006"
	P37->P37_HEADER:="Accept: application/json|OData-MaxVersion: 4.0|OData-Version: 4.0|"+'Prefer: odata.include-annotations="OData.Community.Display.V1.FormattedValue"'
	P37->P37_CODREQ:=cCodReq
	P37->P37_BODY  :='{"nPage":'+cValToChar(nPage)+', "nCount":'+cValToChar(nCount)+',"inicio":"'+cIni+'", "fim":"'+cFim+'"}'
	P37->(MsUnlock())

	EndTran()
	FreeObj(oReceptor)
Return
