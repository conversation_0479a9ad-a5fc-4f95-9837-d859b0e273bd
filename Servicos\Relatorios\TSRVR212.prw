#INCLUDE "TOTVS.CH"


Static __lHaveAddIn

//-----------------------------------------------------------------------
/*/{Protheus.doc} TSRVR212
Relatorio de Planilha Preta com integracao EXCEL (VBA)

 <AUTHOR> 
 @since  05/10/2015
 @param  Nil	
 @return Nil
 @version P12 

/*/
//-----------------------------------------------------------------------
User Function TSRVR212()
Local nY, nX
Local lRet			 := .T.
Local cPathOri	 := "C:\APEXCEL\"
Local cLckCr	  	 := cPathOri +"ApExcel.lck"
Local cArqXla     := "TSRVR212.xla"//"CRCSAXLS.xla"
Local cTarget     := "TSRVR212.xls" //Caso altere o nome deste arquivo e' necessario alterar no arquivo CRCSAXLA.xla
Local cArqDest	  := ""
Local cPathSiga   := GetPvProfString( GetEnvServer(), "StartPath", "ERROR", GetADV97() )
Local oExcelApp   := Nil
Local nHandle     := 0
Local aDados      := {}
Local cArqCSV		:= ''
Private cArqDestino	:= "TSRVR212.DBF"
Private cLocalPath 	//:= U_GetLocEnd()
Private cPerg     	:= "TSRVR212"
Private aCabec1   	:= {"TECNICO", "NOME", "GPP", "CELULAR", "COD_COORD", "NOME_COORD", "DIAS" }
Private nQtdDias  	:= 0
Private cTecnicos 	:= ""
Private aDatas    	:= {}
Private nTamArray 	:= 0
Private nDiaSemOS 	:= 0
Private oTempTable	:= Nil

// AADD(aRegs,{cPerg,"01","Data De?"              ,"Data De?"              ,"Data De?"              ,"mv_ch1","D", 8,0,0,"G",""                    ,"mv_par01",""   ,""   ,""   ,"","",""   ,""   ,""   ,"","","","","","","","","","","","","","","","",""   ,""})
// AADD(aRegs,{cPerg,"02","Data Ate?"             ,"Data Ate?"             ,"Data Ate?"             ,"mv_ch2","D", 8,0,0,"G",""                    ,"mv_par02",""   ,""   ,""   ,"","",""   ,""   ,""   ,"","","","","","","","","","","","","","","","",""   ,""})
// AADD(aRegs,{cPerg,"03","Tecnico De?"           ,"Tecnico De?"           ,"Tecnico De?"           ,"mv_ch3","C", 6,0,0,"G",""                    ,"mv_par03",""   ,""   ,""   ,"","",""   ,""   ,""   ,"","","","","","","","","","","","","","","","","RD0",""})
// AADD(aRegs,{cPerg,"04","Tecnico Ate?"          ,"Tecnico Ate?"          ,"Tecnico Ate?"          ,"mv_ch4","C", 6,0,0,"G",""                    ,"mv_par04",""   ,""   ,""   ,"","",""   ,""   ,""   ,"","","","","","","","","","","","","","","","","RD0",""})
// AADD(aRegs,{cPerg,"05","Cargo De?"             ,"Cargo De?"             ,"Cargo De?"             ,"mv_ch5","C", 5,0,0,"G",""                    ,"mv_par05",""   ,""   ,""   ,"","",""   ,""   ,""   ,"","","","","","","","","","","","","","","","","ZC4",""})
// AADD(aRegs,{cPerg,"06","Cargo Ate?"            ,"Cargo Ate?"            ,"Cargo Ate?"            ,"mv_ch6","C", 5,0,0,"G",""                    ,"mv_par06",""   ,""   ,""   ,"","",""   ,""   ,""   ,"","","","","","","","","","","","","","","","","ZC4",""})
// AAdd(aRegs,{cPerg,"07","GPP De?"               ,"GPP De?"               ,"GPP De?"               ,"mv_ch7","C", 6,0,0,"G",""                    ,"mv_par07",""   ,""   ,""   ,"","",""   ,""   ,""   ,"","","","","","","","","","","","","","","","","A9C",""})
// AAdd(aRegs,{cPerg,"08","GPP Ate?"              ,"GPP Ate?"              ,"GPP Ate?"              ,"mv_ch8","C", 6,0,0,"G",""                    ,"mv_par08",""   ,""   ,""   ,"","",""   ,""   ,""   ,"","","","","","","","","","","","","","","","","A9C",""})
// AADD(aRegs,{cPerg,"09","Considera zerados?"    ,"Considera zerados?"    ,"Considera zerados?"    ,"mv_ch9","N", 1,0,1,"C",""                    ,"mv_par09","Sim","Sim","Sim","","","Nao","Nao","Nao","","","","","","","","","","","","","","","","",""   ,""})
// AADD(aRegs,{cPerg,"10","Somente dias uteis?"   ,"Somente dias uteis?"   ,"Somente dias uteis?"   ,"mv_cha","N", 1,0,1,"C",""                    ,"mv_par10","Sim","Sim","Sim","","","Nao","Nao","Nao","","","","","","","","","","","","","","","","",""   ,""})
// AADD(aRegs,{cPerg,"11","Unid.Servico Tecnico?" ,"Unid.Servico Tecnico?" ,"Unid.Servico Tecnico?" ,"mv_chb","C",99,0,0,"G","CRFUN01K(MV_PAR11)","mv_par11",""   ,""   ,""   ,"","",""   ,""   ,""   ,"","","","","","","","","","","","","","","","",""   ,""})

If !Pergunte(cPerg,.T.)
	Return Nil
EndIf

If !ExistDIR("C:\APEXCEL")
	If	MAKEDIR("C:\APEXCEL")!= 0
		Aviso("Erro" ,"Nao foi possivel criar diretorio: " +cPathOri,{"OK"}) //"Inconsistencia"###"Nao foi possivel criar diretorio "###".Finalizando ..."
		return nil
	EndIf
EndIf

PROCESSA({|| aDados := TSRVR212B()}, "Montando Temporï¿½rio. Aguarde..." )

// cria arquivo temporario
aCampos := {}

For nY:=1 To Len(aDados[1])
	If nY<=Len(aCabec1)
		AAdd(aCampos,{aDados[1][nY] , "C" ,  40 , 0})
	Else
		cCampo := "D"+StrZero(Day(CtoD(aDados[1][nY])),2)+Substr("ABCDEFGHIJKL",Month(CtoD(aDados[1][nY])),1)+SubStr(StrZero(Year(CtoD(aDados[1][nY])),4),3,2)
		
		AAdd(aCampos,{cCampo, "C" ,25 , 0})
		
	EndIf
Next nY
oTemptable := FWTemporaryTable():New( "TRBEX")
oTemptable:SetFields( aCampos )
oTempTable:AddIndex("index1", {"TECNICO"} )
oTempTable:Create()


DbSelectArea("TRBEX")

For nY:=1 To Len(aDados)
	RecLock("TRBEX", .T.)
		For nX:=1 To Len(aDados[nY])
			If nX > Len(aCabec1)
				cCampo := "TRBEX->D"+StrZero(Day(CtoD(aDados[1][nX])),2)+Substr("ABCDEFGHIJKL",Month(CtoD(aDados[1][nX])),1)+SubStr(StrZero(Year(CtoD(aDados[1][nX])),4),3,2)
			Else
				cCampo := "TRBEX->"+aDados[1][nX]
			EndIf

			If ValType(aDados[nY][nX]) == "N"
				cConteudo := Str(aDados[nY][nX],8,2)
			ElseIf ValType(aDados[nY][nX]) == "U"
				cConteudo := ""
			Else
				cConteudo := aDados[nY][nX]
			EndIf
			
			&cCampo := cConteudo
		Next
	MsUnlock()
Next

dbSelectArea("TRBEX")
//U_SRVXCopy(Alltrim(cArqTRB),"TRBEX")
cArqDestino := cPathOri + "TSRVR212.xls"
U_TRGXLX01("TRBEX", cArqDestino ,  , /*cTitle*/, .T./*lAskOpen*/)
dbCloseArea()

MsgAlert("Arquivo: " + cArqDestino + CRLF + " Gerado com sucesso !", "TSRVR212")


//cArqDestino := cPathOri + "TSRVR212.DBF"

//__CopyFile( cArqTRB + ".DBF" , cArqDestino)

//MsgAlert("Arquivo: " + cLocalPath + cArqDestino + CRLF + " Gerado com sucesso !", "TSRVR212")


 //Esse arquivo "TSRVR212.xla" e' onde contem as macros com as chamada para carregar a planilha
 //Ele encontra-se no \system\ para qualquer alteracao esta' no Source Safe depois das alteracao deve ser copiado para \system\ novamente
/*
If !File(cPathOri+cArqXla) .OR. Directory(cPathOri+cArqXla)[1][3]  < StoD("20101001")
	MsgRun( "Atualizando estacao: " + cPathOri+cArqXla , "Aguarde..." , {|| __CopyFile(cPathSiga+cArqXla, cPathOri+cArqXla) })
Else
	//-- se existir, apaga e copia de novo do servidor para garantir que esta sempre com a versao atualizada no diretorio local
	If File(cPathOri+cArqXla)
		fErase(cPathOri+cArqXla)
		MsgRun( "Atualizando estacao: " + cPathOri+cArqXla , "Aguarde..." , {|| __CopyFile(cPathSiga+cArqXla, cPathOri+cArqXla) })
	Endif		
EndIf

If !File( cPathOri+cArqXla )
	MsgStop( 'Arquivo .xla nï¿½o instalado.' )
	Return Nil
EndIf

If File(cLocalPath + cTarget)
	nDel := fErase(cLocalPath + cTarget)
	If nDel <> 0
		Aviso("Erro" ,"Nao foi possivel excluir arquivo anterior : " + cLocalPath + cTarget,{"OK"})
	EndIf
EndIf

MsgRun("Sincronizando com MSExcel! Aguarde...",,{|| CursorWait(),TSRVR212C( @oExcelApp, cLckCr, cLocalPath + cTarget ),CursorArrow()})

FErase( cLckCr ) 
*/

oTempTable:Delete()
Return Nil


//-----------------------------------------------------------------------
/*/{Protheus.doc} TSRVR212B
Carrega array de todos tecnicos para preenchimento da Planilha Preta 

 <AUTHOR> Fernandes 
 @since  19/10/2015
 @param  Nil	
 @return Nil
 @version P12 

/*/
//-----------------------------------------------------------------------
Static Function TSRVR212B()
Local nY, nX
Local dDataXLS   	:= Nil
Local cQuery     	:= ""
Local lUtilEmpst 	:= GetMV("TI_EMPRES",,.T.)
Local lFeriado	:= (MV_PAR10 == 2)
Local lValidDt	:= .T.
Local cTecRD0   	:= ""
Local aDiaMes		:= {}

SET CENTURY ON	// Define que sera utilizado ano com 4 digitos (AAAA)

//Monta array com as datas 
dDataXLS  := MV_PAR01

aDiasMes  := U_SRVXRetDias(MV_PAR01,MV_PAR02,lFeriado)

If mv_par10 == 1
	nQtdDias  := aDiasMes[1]
Else
	nQtdDias  := aDiasMes[1]+aDiasMes[2]
EndIf

If mv_par10 == 1
	nDiaSemOS := aDiasMes[1]*-1
Else
	nDiaSemOS :=((aDiasMes[1]+aDiasMes[2])*-1)
EndIf
	
nTamArray := Len(aCabec1) + nQtdDias

AADD(aDatas, Array(nTamArray) )

For nX:=1 To Len(aDatas)
	For nY:=1 To nTamArray
		If nY <= Len(aCabec1)
			aDatas[nX][nY] := aCabec1[nY]
		Else
			//Conforme alinhado com Vanessa Karla, os feriados devem ser considerados, exceto quando cairem de 
			//sï¿½bado ou domingo. Egidio JR - 29/09/2016 - # 22169 
			If mv_par10 == 2
				If U_SrvXFeriado(dDataXLS) 
					nDiaSemOS++
				Endif
			EndIf
								
			lValidDt := .T.
			While lValidDt
			//Conforme alinhado com Vanessa Karla, os feriados devem ser considerados, exceto quando cairem de 
			//sï¿½bado ou domingo. Egidio JR - 29/09/2016 - # 22169 
				If mv_par10 == 2 .OR. (mv_par10 == 1 .And. !(U_SrvXFeriado(dDataXLS)) ) //(mv_par10 == 1 .AND. U_SrvXFeriado(dDataXLS))
					aDatas[nX][nY] := DtoC(dDataXLS)
					lValidDt := .F.
				EndIf
				dDataXLS++
			End
		EndIf
	Next
Next nX

	
//***********************************DADOS DAS OSs Confirmadas/Agenda
	cQuery  := " SELECT "
	cQuery  += " PF9.PF9_CODTEC TECNICO, PF9_DATA DATAREG, PF9.PF9_STATUS , PF9.PF9_TOTAL , SA1.A1_NREDUZ, "      //CASE WHEN PF9_STATUS = '3' THEN PF9_HRTOT ELSE A1_NREDUZ END  REGISTRO, "
	cQuery  += " RD0TEC.RD0_NOME NOME, RD0TEC.RD0_XSUPER GPP, RD0TEC.RD0_NUMCEL CELULAR, RD0TEC.RD0_XCOORD COD_COORD, "
	//cQuery  += " RD0COR.RD0_NOME NOME_COORD, "+ AllTrim(Str(nDiaSemOS))+" DIASEM," + AllTrim(Str(nQtdDias))+" DIAS "
	cQuery  += " RD0COR.RD0_NOME NOME_COORD," + AllTrim(Str(nQtdDias))+" DIAS "
	cQuery  += "FROM "+ RetSqlName('PF9')+ " PF9 "

	cQuery  += " INNER JOIN "+RetSqlName('RD0')+" RD0TEC "
	cQuery  += "     ON RD0TEC.RD0_FILIAL = '" +xFilial("RD0")+ "'"
	cQuery  += "    AND RD0TEC.RD0_CODIGO = PF9_CODTEC " 
	cQuery  += "    AND RD0TEC.RD0_XCARGO BETWEEN '" +MV_PAR05+ "' AND '" +MV_PAR06+ "' "
	cQuery  += "    AND RD0TEC.RD0_XSUPER BETWEEN '" +MV_PAR07+ "' AND '" +MV_PAR08+ "' "
	If !Empty(MV_PAR11)
		cQuery += "    AND RD0TEC.RD0_XUNSRV IN " +FormatIn(AllTrim(MV_PAR11),";")
	EndIf
	cQuery += "     AND RD0TEC.RD0_MSBLQL = '2'  "
	cQuery += "     AND RD0TEC.D_E_L_E_T_ = ' ' "

	cQuery += " LEFT JOIN "+RetSqlName('RD0')+" RD0GPP "
	cQuery += "     ON RD0GPP.RD0_FILIAL = '" +xFilial("RD0")+ "'"
	cQuery += "    AND RD0GPP.RD0_CODIGO = PF9_CODTEC " 
	cQuery += "    AND RD0GPP.RD0_MSBLQL = '2' "
	cQuery += "    AND RD0GPP.D_E_L_E_T_ = ' ' "
	
	cQuery += " LEFT JOIN "+RetSqlName('RD0')+" RD0COR "
	cQuery += "     ON RD0COR.RD0_FILIAL = '" +xFilial("RD0")+ "'"
	cQuery += "    AND RD0COR.RD0_CODIGO = RD0TEC.RD0_XCOORD " 
	cQuery += "    AND RD0COR.RD0_MSBLQL = '2' "
	cQuery += "    AND RD0COR.D_E_L_E_T_ = ' ' "

	cQuery += " INNER JOIN "+RetSqlName('SA1')+" SA1 "
	cQuery += "     ON SA1.A1_FILIAL = '" +xFilial("SA1")+ "'"
	cQuery += "    AND SA1.A1_COD    = PF9_CLIENT  " 
	cQuery += "    AND SA1.A1_LOJA   = PF9_LOJA  "
	cQuery += "    AND SA1.D_E_L_E_T_ = ' ' "

	cQuery += " WHERE PF9.PF9_DATA BETWEEN '" + DtoS(MV_PAR01) + "' AND '" + DtoS(MV_PAR02) +"' "
	cQuery += "    AND PF9.PF9_CODTEC BETWEEN '" +MV_PAR03+ "' AND '" +MV_PAR04+ "' "
	cQuery += "    AND PF9.D_E_L_E_T_ = ' ' "
	//cQuery += " ORDER BY PF9_STATUS DESC, TECNICO "
	cQuery += "ORDER BY TECNICO,DATAREG,PF9_STATUS DESC

	PROCESSA({|| TSRVR212A(cQuery, "PF9")}, "Processando O.S.s migradas. Aguarde..." )

	//***********************************TECNICOS SEM DADOS
	cTecRD0 := SubStr(cTecnicos, 1,Len(cTecnicos)-1)
	
	cQuery := " SELECT RD0TEC.RD0_CODIGO TECNICO, RD0TEC.RD0_NOME NOME, RD0TEC.RD0_XSUPER GPP, RD0TEC.RD0_NUMCEL CELULAR, "
	cQuery += "       RD0TEC.RD0_XCOORD COD_COORD,  RD0CORD.RD0_NOME NOME_COORD,"+AllTrim(Str(nQtdDias))+" DIAS, "
	cQuery += "       'SEM AGENDA' REGISTRO "	
	cQuery += " FROM " +RetSqlName("RD0")+ " RD0TEC "
	
	cQuery += " LEFT JOIN "+RetSqlName('RD0')+" RD0CORD "
	cQuery += "     ON RD0CORD.RD0_FILIAL = RD0TEC.RD0_FILIAL "
	cQuery += "    AND RD0CORD.RD0_CODIGO = RD0TEC.RD0_XCOORD " 
	cQuery += "    AND RD0CORD.RD0_MSBLQL = '2'  "
	cQuery += "    AND RD0CORD.D_E_L_E_T_ = ' ' "
	
	cQuery += " WHERE RD0TEC.RD0_FILIAL = '" +xFilial("RD0")+ "' "
	cQuery += "    AND RD0TEC.RD0_CODIGO NOT IN " +FormatIn(cTecRD0,";")+ "  "
	cQuery += "    AND RD0TEC.RD0_XCARGO BETWEEN '" +MV_PAR05+ "' AND '" +MV_PAR06+ "' "
	cQuery += "    AND RD0TEC.RD0_XSUPER BETWEEN '" +MV_PAR07+ "' AND '" +MV_PAR08+ "' "
	If !Empty(MV_PAR11)
		cQuery += "    AND RD0TEC.RD0_XUNSRV IN " +FormatIn(AllTrim(MV_PAR11),";")
	EndIf

	cQuery += "        AND RD0TEC.RD0_MSBLQL = '2' "
	cQuery += "        AND RD0TEC.D_E_L_E_T_ = ' ' "
	cQuery += "ORDER BY 1 "
	
	PROCESSA({|| TSRVR212A(cQuery, "RD0" )}, "Processando Tecnicos sem dados. Aguarde...")

	
SET CENTURY OFF		// Define que sera utilizado ano com 2 digitos (AA)

Return aDatas

//-----------------------------------------------------------------------
/*/{Protheus.doc} TSRVR212A
Busca qual a data deve ser processada   

 <AUTHOR> Fernandes 
 @since  08/10/2015
 @param  Nil	
 @return Nil
 @version P12 

/*/
//-----------------------------------------------------------------------
Static Function TSRVR212A(cQuery, cAlias)
Local nY
Local nPosTec   	:= 0
Local cAliasQry 	:= ""
Local nTotReg   	:= 0
Local nPosForm  	:= Ascan( aCabec1, "DIAS")
Local xFormat		

cQuery := ChangeQuery(cQuery)
cAliasQry:=GetNextAlias()
If Select(cAliasQry) > 0
	(cAliasQry)->(DbCloseArea())
EndIf

dbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQuery), cAliasQry , .F., .T.)
aEval( (cAlias)->(dbStruct()),{|x| If(x[2]!="C", TcSetField(cAliasQry,AllTrim(x[1]),x[2],x[3],x[4]),Nil)})
aEval( RD0->(DbStruct()),{|x| If(x[2]!="C", TcSetField(cAliasQry,AllTrim(x[1]),x[2],x[3],x[4]),Nil)})

nTotReg:=0
(cAliasQry)->( DbEval( {|| nTotReg++},{|| .T. },{|| .T. } ) )
ProcRegua(nTotReg)

(cAliasQry)->(DbGoTop())
While (cAliasQry)->(!Eof())
	IncProc( "Processando Tecnico -> " + (cAliasQry)->TECNICO )
	
	//Conforme alinhado com Vanessa Karla, os feriados devem ser considerados, exceto quando cairem de 
	//sbado ou domingo. Egidio JR - 29/09/2016 - # 22169 	
	If cAlias <> "RD0" .And. (mv_par10 == 1 .And. (Dow( StoD((cAliasQry)->DATAREG) ) == 1 .Or. Dow( StoD((cAliasQry)->DATAREG) ) == 7))//U_SrvXFeriado(StoD((cAliasQry)->DATAREG))
		(cAliasQry)->( dbSkip() )
		Loop
	EndIf

	If cAlias <> "RD0" 
		
		If AllTrim((cAliasQry)->PF9_STATUS) <> '3'
			xFormat := "(" + AllTrim((cAliasQry)->PF9_STATUS) + ") " + AllTrim((cAliasQry)->A1_NREDUZ)
		Else
			xFormat := (cAliasQry)->PF9_TOTAL
		EndIf
		
		nPosDia := Ascan( aDatas[1], DtoC(StoD((cAliasQry)->DATAREG))  )
		nPosTec := Ascan( aDatas, { |x| x[1] == (cAliasQry)->TECNICO } )
	Else
		nPosDia := 0
		nPosTec := 0
	EndIf
	
	If nPosTec == 0
		AADD(aDatas, Array(nTamArray) )	
		nPosTec := Len(aDatas)
		//Inicializando os campos para prevenir os erros dos registros em branco
		
		For nY:=1 To Len(aCabec1)
			cCampo := aCabec1[nY]			
			aDatas[nPosTec][nY] := &cCampo
		Next nY
		cTecnicos += AllTrim((cAliasQry)->TECNICO) + ";"
	EndIf

	If nPosDia > 0
		//PRIORIDADE PARA AS OSs MIGRADAS DEPOIS AS CONFIRMADAS POR ULTIMO A AGENDA	
		If ValType(aDatas[nPosTec][nPosDia]) == "U"
			aDatas[nPosTec][nPosDia] := xFormat
		
			//Conforme alinhado com Vanessa Karla, os feriados devem ser considerados, exceto quando cairem de 
			//sbad ou domingo. Egidio JR - 29/09/2016 - # 22169 
			If ( (mv_par10 == 1 .And. Dow( StoD((cAliasQry)->DATAREG) ) <> 1 .And. Dow( StoD((cAliasQry)->DATAREG) ) <> 7) ;
				.Or. ;
				mv_par10 == 2 );
				.AND. (cAliasQry)->PF9_STATUS == "3"
				aDatas[nPosTec][nPosForm]--
			Endif
		Else
			//If !Empty(Alltrim(aDatas[nPosTec][nPosDia])) 
				If Valtype(aDatas[nPosTec][nPosDia]) == "N" .And. ValType(xFormat) == "N"
					aDatas[nPosTec][nPosDia] += xFormat
				ElseIf Valtype(aDatas[nPosTec][nPosDia]) <> "N" .And. ValType(xFormat) == "N"
					aDatas[nPosTec][nPosDia] := AllTrim(aDatas[nPosTec][nPosDia]) + " /n " + StrTran(Str(xFormat,5,2),".",",")
				ElseIf Valtype(aDatas[nPosTec][nPosDia]) == "N" .And. ValType(xFormat) <> "N"
					aDatas[nPosTec][nPosDia] := StrTran(Str(aDatas[nPosTec][nPosDia],5,2),".",",") + " /n " + xFormat
				Else
					aDatas[nPosTec][nPosDia] := aDatas[nPosTec][nPosDia] + " /n " + xFormat
				EndIf
			//EndIf
		EndIf
	EndIf
		
	(cAliasQry)->( dbSkip() )
Enddo
(cAliasQry)->( dbCloseArea() )

Return Nil

//-----------------------------------------------------------------------
/*/{Protheus.doc} TSRVR212C
 Abre os arquivos e faz a chamada para procesamento no Excel 

 <AUTHOR> Fernandes 
 @since  03/10/2015
 @param  Nil	
 @return Nil
 @version P12 

/*/
//-----------------------------------------------------------------------
Static Function TSRVR212C( oExcelApp, cLckCr, cXLSFile )
Local aCfg	:= ExecInClient( 400, { 'XlInfo' } )
Local lRet	:= .T.
Local cDefPath := U_GetLocEnd()

If ( __lHaveAddIn == Nil ) .Or. ( __lHaveAddIn == .F.)
	ExecInClient( 400, { 'XlCopy', 'apExcel80.xla' } )
	__lHaveAddIn	:= File('C:\ApExcel\apExcel80.xla' )
	If !__lHaveAddIn
		__CopyFile("\ApExcel\apExcel80.xla", 'C:\ApExcel\apExcel80.xla')
		__lHaveAddIn := File( 'C:\ApExcel\apExcel80.xla' )
	EndIf
EndIf

FClose(FCreate( cLckCr ))

If ! ApOleClient( 'MsExcel' )
	MsgStop( 'MsExcel nao instalado' )
	Return .F.
EndIf

If ( ! __lHaveAddIn )
	MsgStop( 'AddIn ApSmall nao instalado' )
	Return .F.
EndIf

If ( Len( aCfg ) < 4 )
	MsgStop( 'Parametros de conexao invalidos' )
	Return .F.
EndIf

oExcelApp := MsExcel():New()

oExcelApp:Workbooks:Add()

//oExcelApp:WorkBooks:Open( 'C:\ApExcel\apExcel80.xla' )

//CONOUT(TINICIO)
//oExcelApp:Run( 'apExcel80.xla!Ap5_Excel_8.XlApConnect', cEmpAnt, cFilAnt, Dtos(dDataBase), aCfg [1], aCfg [2], Val(aCfg[3]), cLckCr, aCfg[4] )

oExcelApp:WorkBooks:Open( 'C:\ApExcel\TSRVR212.xla' )
//oExcelApp:SetVisible(.T.)

oExcelApp:Run( 'TSRVR212.xla!CarregaArray')

If File( cLckCr )
	
	fErase( cLckCr ) // apaga o arquivo de teste de gravacao para quebra o link com excel

EndIf

oExcelApp:Quit()

oExcelApp:Destroy()


If File(cXLSFile)
	MsgInfo( "ARQUIVO DISPONIVEL EM: " + cXlsFile )	
EndIf


Return lRet
