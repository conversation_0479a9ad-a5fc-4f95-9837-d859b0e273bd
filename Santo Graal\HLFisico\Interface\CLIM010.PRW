#include "protheus.ch"

User Function Clim010()
Processa({|| U_CliConvert()},"Aguarde","Convertendo Registros ...",.F.)
Return NIL

User Function CliConvert()
Local cAlias := Alias(), nIndex := IndexOrd()

If File("\SIGACLI\sz1ee0.dbf")
	DbUseArea(.T.,"DBFNTX","\SIGACLI\sz1ee0.dbf","CLI",.F.)
	DbGoTop()
    ProcRegua(RecCount())
	While ( !Eof() )
		IncProc()
		DbSelectArea("ZA1")
		DbSetOrder(1)
		If !( DbSeek(xFilial("ZA1")+CLI->Z1_NOMEEMP+CLI->Z1_NOMEFIL) )
			RecLock("ZA1",.T.)
			ZA1->ZA1_FILIAL := CLI->Z1_FILIAL
			ZA1->ZA1_CODIGO := CLI->Z1_CODIGO
			ZA1->ZA1_CODFIL := CLI->Z1_CODFIL
			ZA1->ZA1_NOMEEM := CLI->Z1_NOMEEMP
			ZA1->ZA1_NOMEFI := CLI->Z1_NOMEFIL
			ZA1->ZA1_VALIDA := CLI->Z1_VALIDAD
			ZA1->ZA1_VENCTO := CLI->Z1_VENCTO
			ZA1->ZA1_TIPO   := CLI->Z1_TIPO
			ZA1->ZA1_COMENT := CLI->Z1_COMENT
			ZA1->ZA1_DTREF  := CLI->Z1_DTREF
			ZA1->ZA1_ACER   := CLI->Z1_ACER
			ZA1->ZA1_USERS  := CLI->Z1_USERS
			ZA1->ZA1_VERSAO := CLI->Z1_VERSAO
			ZA1->ZA1_DOS    := CLI->Z1_DOS
			ZA1->ZA1_CLIENT := CLI->Z1_CLIENTE
			ZA1->ZA1_SOLICI := CLI->Z1_SOLICIT
			ZA1->ZA1_PAGS   := CLI->Z1_PAGS
			ZA1->ZA1_MNT    := CLI->Z1_MNT
			ZA1->ZA1_LOJ    := CLI->Z1_LOJ
			ZA1->ZA1_EIC    := CLI->Z1_EIC
			ZA1->ZA1_OBSERV := CLI->Z1_OBSERV
			MsUnlock()
		EndIf
		DbSelectArea("CLI")
		DbSkip()
	End
	CLI->(DbCloseArea())
EndIf

DbSelectArea(cAlias)
DbSetOrder(nIndex)
Return NIL


