<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project name="TI_Protheus:CRM" default="sonar" basedir="." xmlns:sonar="antlib:org.sonar.ant">
    
    <!-- car<PERSON>ga as variaveis de ambiente -->
	<property environment="env" />
    
    <!-- Define the SonarQube global properties (the most usual way is to pass these properties via the command line) -->
    <property name="sonar.host.url" value="http://************:9000" />
    
    <!-- Define the SonarQube project properties -->
    <property name="sonar.login" value="${env.sonar_agent_user}" />
    <property name="sonar.password" value="${env.sonar_agent_password}" />
    <property name="sonar.projectKey" value="TI_Protheus:CRM" />
    <property name="sonar.projectName" value="TI_Protheus:CRM" />
    <property name="sonar.projectVersion" value="1.0" />
    <property name="sonar.projectBaseDir" value="..\..\" />
    <property name="sonar.sources" value="${env.BUILD_SOURCESDIRECTORY}\adm_vendas\CRM,${env.BUILD_SOURCESDIRECTORY}\Pontos de Entrada" />
    <property name="sonar.sourceEncoding" value="Cp1252" />
    <property name="sonar.advpl.file.chs_paths" value="${env.BUILD_SOURCESDIRECTORY}\includes;${env.INCLUDE_PATH}" />
    <property name="sonar.skipPackageDesign" value="true" />
    <property name="sonar.skipDesign" value="true" />
    <property name="sonar.exclusions" value="**/RUP*,**/RUP*.prw,**/RUP*.PRW,**/*.java,**/*.js,**/*.jav*,**/*.jsx,**/*.ptm,**/*.ps1,**/*.json,**/*.bat,**/*.gradle,,**/*.properties,**/*.png,**/*.html,**/*.txt,**/*.md,**/*.css,**/*.xml,**/*.py" />
    <property name="sonar.test.exclusions" value="**/RUP*,**/RUP*.prw,**/RUP*.PRW" />
    <property name="sonar.ws.timeout" value="3600" />
    

    <!-- Define SonarScanner for Ant Target -->
    <target name="sonar">
        <taskdef uri="antlib:org.sonar.ant" resource="org/sonar/ant/antlib.xml">
            <!-- Update the following line, or put the "sonarqube-ant-task-*.jar" file in your "$HOME/.ant/lib" folder -->
            <!--<classpath path="/home/<USER>/ant/lib/sonarqube-ant-task-*.jar" />-->
        </taskdef>
    
        <!-- Execute SonarScanner for Ant Analysis -->
        <sonar:sonar />
    </target>
</project>