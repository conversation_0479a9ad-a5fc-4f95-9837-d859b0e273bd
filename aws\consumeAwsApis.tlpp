#include "tlpp-core.th"

Class   consumeAwsApis
    Private Data    cUser                                           As  Character
    Private Data    cPassword                                       As  Character
    Private Data    cTokenUrl                                       As  Character   
    Private Data    cTokenPath                                      As  Character
    Private Data    cStatusCodeToken                                As  Character
    Private Data    cMessageReturnToken                             As  Character
    Private Data    cAccessToken                                    As  Character
    Private Data    cStatusCodePost                                 As  Character
    Private Data    cMessageReturnPost                              As  Character
    Private Data    cApiUrl                                         As  Character
    Private Data    cApiPath                                        As  Character
    Private Data    cBodyToPost                                     As  Character
    Private Data    lSucessPost                                     As  Logical
    Private Data    lSucessGetToken                                 As  Logical

    Public  Method  new()   Constructor
    Public  Method  setUserCognito(cValue  As  Character)           As  Object
    Public  Method  setPasswordCognito(cValue  As  Character)       As  Object
    Public  Method  setTokenUrl(cValue  As  Character)              As  Object
    Public  Method  setTokenPath(cValue  As  Character)             As  Object
    Public  Method  execRequest()                                   As  Object
    Public  Method  setApiUrl(cValue    As  Character)              As  Object
    Public  Method  setApiPath(cValue   As  Character)              As  Object
    Public  Method  setBodyToPost(cValue    As  Character)          As  Object

    Public  Method  returnStatusRequestToken()                      As  Array
    Public  Method  returnStatusRequestPost()                       As  Array

    Private Method  getToken()  As  Object
    Private Method  postBody()  As  Object

EndClass


Method  new()   Class  consumeAwsApis
    ::cUser                     :=  ''
    ::cPassword                 :=  ''
    ::cTokenUrl                 :=  ''
    ::cTokenPath                :=  ''
    ::cStatusCodeToken          :=  ''
    ::cAccessToken              :=  ''
    ::cMessageReturnToken       :=  ''
    ::cStatusCodePost           :=  ''
    ::cMessageReturnPost        :=  ''
    ::cApiUrl                   :=  ''
    ::cApiPath                  :=  ''
    ::cBodyToPost               :=  ''
    ::lSucessPost               :=  .F.
    ::lSucessGetToken           :=  .F.
Return Self


Method  setUserCognito(cValue  As  Character)           As  Object  Class  consumeAwsApis
    ::cUser :=  cValue
Return Self

Method  setPasswordCognito(cValue  As  Character)       As  Object  Class  consumeAwsApis
    ::cPassword :=  cValue
Return Self

Method  setTokenUrl(cValue  As  Character)              As  Object  Class  consumeAwsApis
    ::cTokenUrl :=  cValue
Return Self
Method  setTokenPath(cValue  As  Character)             As  Object  Class  consumeAwsApis
    ::cTokenPath    :=  cValue
Return Self


Method  getToken()                                      As  Object  Class  consumeAwsApis
    Local   oResult         :=  JsonObject():new()      As  Json
    Local   oRest           :=  Nil                     As  Object
    Local   aHeader         :=  {}                      As  Array
    Local   cAuthToken      :=  ''                      As  Character
    Local   cResponse       :=  ''                      As  Character
 

    cAuthToken  :=  Encode64(::cUser + ":" + ::cPassword)

    aadd(aHeader,'Content-Type: application/x-www-form-urlencoded')
    AAdd(aHeader, "Authorization: Basic " + cAuthToken )

    oRest := FWRest():New(::cTokenUrl)
    oRest:SetPath(::cTokenPath)
    
    ::lSucessGetToken           :=  oRest:Post(aHeader)
    ::cMessageReturnToken       :=  oRest:GetLastError()
    cResponse := oRest:CRESULT
    oResult:FromJson(cResponse)


    ::cStatusCodeToken  := oRest:GetHTTPCode()
    If ::cStatusCodeToken   ==  '200'  .And. oResult:HasProperty("access_token")
        ::cAccessToken := oResult["access_token"]
    EndIf
    
Return Self



Method  setApiUrl(cValue    As  Character)  As  Object   Class  consumeAwsApis
    ::cApiUrl   :=  cValue
Return Self

Method  setApiPath(cValue   As  Character)  As  Object   Class  consumeAwsApis
    ::cApiPath  :=  cValue
Return Self

Method  setBodyToPost(cValue    As  Character)  As  Object    Class  consumeAwsApis
    ::cBodyToPost   :=  EncodeUtf8(cValue)
Return Self


   

Method  postBody()  As  Object   Class  consumeAwsApis
    Local aHeaderPost   := {}   As  Array
    Local   oRestPost  :=  Nil  AS  Object
    


    oRestPost := FWRest():New(::cApiUrl)
    oRestPost:SetPath(::cApiPath)
    oRestPost:SetPostParams(::cBodyToPost)

    aAdd(aHeaderPost, 'Content-Type: application/json')
    AAdd(aHeaderPost, "Accept: application/json")
    aAdd(aHeaderPost, "Authorization: Bearer " + ::cAccessToken)


    ::lSucessPost           :=  oRestPost:Post(aHeaderPost)
    ::cMessageReturnPost    :=  oRestPost:GetLastError()
    ::cStatusCodePost       :=  oRestPost:GetHTTPCode()


Return Self






Method  returnStatusRequestToken()  As  Array   Class  consumeAwsApis
Return {::lSucessGetToken, ::cStatusCodeToken, ::cMessageReturnToken}


Method  returnStatusRequestPost()   As  Array   Class  consumeAwsApis
Return {::lSucessPost, ::cStatusCodePost, ::cMessageReturnPost}








Method  execRequest()           As  Object  Class  consumeAwsApis
    ::getToken()
    ::postBody() 
Return Self














