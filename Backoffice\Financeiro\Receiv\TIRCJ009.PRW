#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWADAPTEREAI.CH"

User Function TIRCJ009(lJob,dDataExc)

	Local cQry     := ""
	Local lNewCase := .F.  
	Local cAlsPro := GetNextAlias()

	Default lJob := .T.
	DEFAULT  dDataExc:= Date()
	
	If U_RCVENV(cEmpAnt,cFilAnt)
		lNewCase := GetMv('TI_CVX10' , , .T.)

		DBSELECTAREA("SE1")
		dbSetORder(1)

		DBSELECTAREA("P39")
		dbSetORder(1)


		cQry:=	"SELECT DISTINCT P38_FILIAL,P38_NUM,(SELECT P38_PARCEL FROM    " + RetSQLName("P38") + " P38J WHERE  P38J.P38_NUM = P38.P38_NUM and P38J.P38_PARCEL = (P38.P38_PARCEL + 1) "
		cQry+=	"   AND P38.D_E_L_E_T_ = ' '    )PARCEL,(SELECT P38_VENCTO FROM    " + RetSQLName("P38") + " P38J WHERE  P38J.P38_NUM = P38.P38_NUM and P38J.P38_PARCEL = (P38.P38_PARCEL + 1) "
		cQry+=	"  AND P38.D_E_L_E_T_ = ' '    ) NEWDATE "
		cQry+=	"		FROM    " + RetSQLName("P38") + " P38 "
		cQry+=	"		INNER JOIN  " + RetSQLName("P39") + " P39 ON P38.P38_FILIAL = P39.P39_FILACD "
		cQry+=	"			AND P39_IDACOR = P38_NUM "
		cQry+=	"			AND P39.D_E_L_E_T_ = ' ' "
		cQry+=	"		INNER JOIN  " + RetSQLName("SE1") + " SE1 ON SE1.E1_FILIAL = P39.P39_FILIAL "
		cQry+=	"			AND SE1.E1_NUM = P39.P39_NUM "
		cQry+=	"			AND SE1.E1_PREFIXO = P39.P39_PREFIX "
		cQry+=	"			AND SE1.E1_PARCELA = P39.P39_PARCEL "
		cQry+=	"			AND SE1.E1_TIPO = P39.P39_TIPO "
		cQry+=	"			AND SE1.D_E_L_E_T_ = ' ' AND E1_SALDO > 0"
		cQry+=	"		WHERE  P38.P38_DTBAIX = '"+DTOS(dDataExc)+"' "
		cQry+=	"		AND (	SELECT P38_VENCTO	FROM  " + RetSQLName("P38") + " P38J	WHERE  P38J.P38_NUM = P38.P38_NUM	AND P38J.P38_PARCEL = (P38.P38_PARCEL + 1)	AND P38.D_E_L_E_T_ = ' ' ) >   SE1.E1_VENCREA AND P38.P38_QUEBRA = ' ' "
		cQry+=	"		AND P38.D_E_L_E_T_ = ' '  AND P38.P38_TIPO = 'A' "

		If lNewCase 
			cQry +=	" UNION "

			cQry +=	" SELECT DISTINCT P38_FILIAL,P38_NUM, P38_PARCEL AS PARCEL, P38_VENCTO AS NEWDATE "
			cQry +=	" FROM " + RetSQLName("P38") + " P38  " 
			cQry +=	" INNER JOIN " + RetSQLName("P39") + " P39 ON   "
			cQry +=	" P38_NUM = P39_IDACOR AND P38.P38_FILIAL = P39.P39_FILACD   "
			cQry +=	" INNER JOIN " + RetSQLName("SE1") + " SE1 ON SE1.E1_FILIAL = P39.P39_FILIAL   "
			cQry +=	" AND SE1.E1_NUM = P39.P39_NUM   "
			cQry +=	" AND SE1.E1_PREFIXO = P39.P39_PREFIX   "
			cQry +=	" AND SE1.E1_PARCELA = P39.P39_PARCEL   "
			cQry +=	" AND SE1.E1_TIPO = P39.P39_TIPO   "
			cQry +=	" AND SE1.D_E_L_E_T_ = ' ' AND E1_SALDO > 0   "
			cQry +=	" AND SE1.E1_CLIENTE = P38.P38_CLIENT   "
			cQry +=	" WHERE   "
			cQry +=	"     P38.D_E_L_E_T_ = ' ' "
			cQry +=	" AND P39.D_E_L_E_T_ = ' ' "   
			cQry +=	" AND SE1.D_E_L_E_T_ = ' ' "  
			cQry +=	" AND ( ( P38_TIPO = 'P' AND P38_PROBOL = 'S' ) OR ( P38_TIPO = 'A' AND P38_PROBOL = 'S' ) )    "
			cQry +=	" AND P38_ASALDO > 0   "
			cQry +=	" AND P38_VENCTO = (  SELECT MIN (P38J.P38_VENCTO)  "
			cQry +=	"            FROM  " + RetSQLName("P38") + " P38J   "
			cQry +=	"             WHERE  P38J.P38_NUM = P39_IDACOR AND P38J.P38_FILIAL = P39_FILACD   "
			cQry +=	"            AND P38J.D_E_L_E_T_ = ' ' AND P38J.P38_ASALDO > 0   )   "
			cQry +=	" AND (  SELECT MIN (P38J.P38_VENCTO)  "
			cQry +=	"             FROM  " + RetSQLName("P38") + " P38J   "
			cQry +=	"             WHERE  P38J.P38_NUM = P39_IDACOR AND P38J.P38_FILIAL = P39_FILACD   "
			cQry +=	"            AND P38J.D_E_L_E_T_ = ' ' AND P38J.P38_ASALDO > 0   ) >   SE1.E1_VENCREA   " 
		EndIf 


		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAlsPro,.T.,.T.)
		
		
		while !(cAlsPro)->(EOF())
			DbSelectArea("P38")
			DbSetOrder(1)
			If P38->(DbSeek((cAlsPro)->P38_FILIAL+(cAlsPro)->P38_NUM + (cAlsPro)->PARCEL))
				RecLock("P38",.F.)
				P38->P38_PROBOL := 'S'
				MsUnLock()
			EndIf

			DBSELECTAREA("P39")
			dbSetORder(3)
			If  P39->(DbSeek((cAlsPro)->P38_FILIAL+(cAlsPro)->P38_NUM ))
				While !P39->(EOF()) .and. P39->P39_FILACD == (cAlsPro)->P38_FILIAL .and. (cAlsPro)->P38_NUM  == P39->P39_IDACOR
					RecLock("P39",.F.)
					P39->P39_EFETIV := 'N'
					MsUnLock()
					P39->(Dbskip())
				EndDo
			EndIF

			(cAlsPro)->(Dbskip())
		EndDo
	EndIF
Return
