#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'APWEBSRV.CH'
#INCLUDE 'FILEIO.CH'  

/*
Recupera Arquivo
*/

User Function ESPX010B(cTokenExt, cFilePath)
    Local cDir         := ''
    Local cFileContent := ''
    Local cFileName    := ''
    Local cExtensao    := ''

    Local nHdl         := 0
    Local nSize        := 0
    
    Local lCompress    := .F.
    
    Default cFilePath := GetNewPar('FS_ACEDIR', '\system\') + cTokenExt +'\' + 'SDFBRA.TXT'
    
    SplitPath(cFilePath,, @cDir, @cFileName, @cExtensao)
    
    IF cExtensao $ ('.dtc|.dbf')
        cFilePath := MsCompress(cFilePath)
        IF Empty(cFilePath)
            Return cFileContent
        EndIF
        lCompress := .T.
    EndIF
    
    IF (nHdl := FOpen(cFilePath, FO_READWRITE)) > 0
        nSize := FSeek(nHdl, 0, 2)
        cFileContent := Space(nSize)
        
        FSeek(nHdl, 0, 0)
        FRead(nHdl, @cFileContent, nSize)
        
        FClose(nHdl)
    EndIF

    IF lCompress
        FErase(cFilePath)
    EndIF
    
Return cFileContent
