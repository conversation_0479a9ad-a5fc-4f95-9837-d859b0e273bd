#Include "Protheus.ch"
/*/{Protheus.doc} XFCD2SFT
Ponto de Entrada Gravar campos no SFT
@since 10/07/2019
@version P12
@description Gravar campos no SFT
@return Nil
/*/
User Function XFCD2SFT()
If SFT->FT_ESTADO == 'EX'
	SFT->FT_CODISS := GetAdvFVal("CE1","CE1_CTOISS",xFilial("CE1")+SFT->FT_CODISS,1,"")
EndIf

///////////////////////////////////////////////////////////////////////////////
If SFT->FT_FILIAL == "00001002600"  //CAXIAS DO SUL
//	SFT->FT_TRIBMUN := GetAdvFVal("CE1","CE1_TRIBMU",xFilial("CE1")+SFT->FT_CODISS,1,"")
	SFT->FT_TRIBMUN := fTrazTribM(xFilial("CE1"),SFT->FT_CODISS)  //Retorna o campo CE1_TRIBMU de Caxias do Sul-RS
EndIf
///////////////////////////////////////////////////////////////////////////////

Return Nil 
//-----------------------------------------------------------------------------

Static Function fTrazTribM(cFilCE1,cCodISS)  //Retorna o campo CE1_TRIBMU de Caxias do Sul-RS
Local aAreaCE1:=CE1->(GetArea())
Local cRet:=Space(Len(CE1->CE1_TRIBMU))

CE1->(dbSetOrder(1))  //CE1_FILIAL+CE1_CODISS+CE1_ESTISS+CE1_CNUISS+CE1_PROISS
CE1->(dbSeek(cFilCE1))  //Posiciona no CE1
While CE1->(!Eof() .and. CE1_FILIAL==cFilCE1)
	If CE1->(AllTrim(CE1_CTOISS)==AllTrim(cCodISS))
		cRet:=CE1->CE1_TRIBMU
		Exit
	EndIf
	CE1->(dbSkip())
End

CE1->(RestArea(aAreaCE1))
Return(cRet)
//-----------------------------------------------------------------------------
