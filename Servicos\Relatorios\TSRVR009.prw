#INCLUDE "Totvs.CH"
#INCLUDE "TSRVR009.CH"


/*/{Protheus.doc} TSRVR009
Relat�rio de  Transfer�ncias e Despesas por Centros de Custos - Controle de Projetos
ANTIGO CRPMOR13.PRW - remodelado V12.
<AUTHOR>
@since 30/07/2016
@version 1.0
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
User Function TSRVR009()

Local cPerg 			:= "TSRVR009"
Local lRet				:= .F.

Private lEnd			:= .F.
Private cArqTRBTrf   := ""
Private aCpsPE0		:= {}
Private oProcess

SV009A01() // Cria pergunte do SX1	
If !Pergunte("TSRVR009",.T.)
	ApMsgAlert("Relatorio Cancelado!", "Transferencia de Despesas")
	Return
Endif

oProcess := MsNewProcess():New({|lEnd| lRet := SV009B01() },"Transferencia de Despesas","Selecionando os dados, aguarde ...",.T.)
oProcess:Activate()

Return lRet

/*/{Protheus.doc} SV009B01
Relat�rio de  Transfer�ncias e Despesas por Centros de Custos - Controle de Projetos
ANTIGO CRPMOR13.PRW - remodelado V12.
<AUTHOR> Jr
@since 30/07/2016
@version 1.0
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
Static Function SV009B01()

Local lRet 		:= .F.
Local lReclck	:= .F.
Local cMesRef 	:= MV_PAR01
Local cAnoRef 	:= MV_PAR02
Local cUnSrv	:= IIF(!Empty(MV_PAR03), FormatIn(ALLTRIM(MV_PAR03),";"), "")
Local nVlrAna	:= MV_PAR04
Local nVlrCor	:= MV_PAR05
Local cQryPF8	:= ""
Local cAliasTRB	:= GetNextAlias()
Local nRegAtu	:= 0
Local nTotReg	:= 0
Local nPosMt	:= 0
Local cTpMot	:= ""
Local _aCbTpMot	:= RetSx3Box( Posicione("SX3", 2, "PE0_TPMOT",  "X3CBox()" ),,, 1 )//Tipo do Motivo
Local cfile        	:= ""
Local cArqDestino	:= ""
//Local oProcess 

oProcess:SetRegua1( 2 )
oProcess:IncRegua1( "Processando...." )
oProcess:IncRegua2( "Filtrando registros...." )
oProcess:SetRegua2( 0 )

//QUERY BASE SOMANDO A PF8
cQryPF8 := ""
cQryPF8 += "SELECT MESREF,PROJETO, FRENTE, CLVLPRJ, MOTIVO, TECNICO, UNSRV, CARGO, FORNE3, SUM(TOTAL) TOTAL, SUM(CUSTO) CUSTO" + CRLF
cQryPF8 += "FROM (SELECT PF8ATU.PF8_MESREF MESREF" + CRLF
cQryPF8 += "             ,PF8ATU.PF8_CODTEC TECNICO" + CRLF
cQryPF8 += "             ,PF8ATU.PF8_UNSRV UNSRV" + CRLF
cQryPF8 += "             ,PF8ATU.PF8_CARGO CARGO" + CRLF
cQryPF8 += "             ,PF8ATU.PF8_FORNE3 FORNE3" + CRLF 
cQryPF8 += "             ,SBM.BM_XCLVL CLVLPRJ" + CRLF
cQryPF8 += "             ,PF8ATU.PF8_PRJATU PROJETO" + CRLF
cQryPF8 += "             ,PF8ATU.PF8_FRTATU FRENTE" + CRLF
cQryPF8 += "             ,PF8ATU.PF8_MOTATU MOTIVO" + CRLF
cQryPF8 += "             ,PF8ATU.PF8_TOTAL TOTAL" + CRLF
cQryPF8 += "             ,PF9.PF9_CUSTO CUSTO" + CRLF
cQryPF8 += "      FROM "+RetSqlName("PF8")+" PF8ATU" + CRLF
cQryPF8 += "      LEFT JOIN " + RetSqlName("SB1") +" SB1 "
cQryPF8 += "         ON SB1.B1_FILIAL = '" + xFilial("SB1")+ "' "
cQryPF8 += "	        AND SB1.D_E_L_E_T_ = ' ' "
cQryPF8 += "	        AND SB1.B1_COD = PF8ATU.PF8_PRODUT "
cQryPF8 += "      LEFT JOIN " + RetSqlName("SBM") + " SBM "
cQryPF8 += "         ON SBM.BM_FILIAL = '" + xFilial("SBM") + "' "
cQryPF8 += "            AND SBM.D_E_L_E_T_ = ' ' "
cQryPF8 += "            AND SBM.BM_GRUPO = SB1.B1_GRUPO "
// para buscar o custo da OS que esta reprocessado, da PF9, considerar os deletados para pegar as OS's excluidas
cQryPF8 += "      LEFT JOIN " + RetSqlName("PF9") +" PF9 "
cQryPF8 += "         ON PF9.PF9_FILIAL = '" + xFilial("PF9")+ "' "
cQryPF8 += "	        AND PF9.PF9_IDOS = PF8ATU.PF8_IDOS "
cQryPF8 += "	        AND PF9.PF9_CODTEC = PF8ATU.PF8_CODTEC "
cQryPF8 += "	        AND PF9.PF9_DATA = PF8ATU.PF8_DATOS "
cQryPF8 += "            AND PF9.PF9_STATUS = '3' "
cQryPF8 += "      WHERE PF8ATU.PF8_FILIAL = '" + xFilial("PF8") +"' "
cQryPF8 += "             AND PF8ATU.PF8_MESREF = '" + cAnoRef + cMesRef +"' "
If !Empty(cUnSrv)
	cQryPF8 += "             AND PF8ATU.PF8_UNSRV IN " + cUnSrv + " "
EndIf 
cQryPF8 += "             AND PF8ATU.PF8_STATUS = '3' "
cQryPF8 += "             AND PF8ATU.D_E_L_E_T_ = ' ' "

cQryPF8 += "      UNION ALL" + CRLF

cQryPF8 += "      SELECT PF8ANT.PF8_MESREF MESREF" + CRLF
cQryPF8 += "             ,PF8ANT.PF8_CODTEC TECNICO" + CRLF
cQryPF8 += "             ,PF8ANT.PF8_UNSRV UNSRV" + CRLF
cQryPF8 += "             ,PF8ANT.PF8_CARGO CARGO" + CRLF
cQryPF8 += "             ,PF8ANT.PF8_FORNE3 FORNE3" + CRLF 
cQryPF8 += "             ,SBM.BM_XCLVL CLVLPRJ" + CRLF
cQryPF8 += "             ,PF8ANT.PF8_PRJANT PROJETO" + CRLF
cQryPF8 += "             ,PF8ANT.PF8_FRTANT FRENTE" + CRLF
cQryPF8 += "             ,PF8ANT.PF8_MOTANT MOTIVO" + CRLF
cQryPF8 += "             ,(PF8ANT.PF8_TOTAL *-1) TOTAL" + CRLF
cQryPF8 += "             ,(PF9.PF9_CUSTO *-1) CUSTO" + CRLF
cQryPF8 += "      FROM "+RetSqlName("PF8")+" PF8ANT" + CRLF
cQryPF8 += "      LEFT JOIN " + RetSqlName("SB1") +" SB1 "
cQryPF8 += "         ON SB1.B1_FILIAL = '" + xFilial("SB1")+ "' "
cQryPF8 += "	        AND SB1.D_E_L_E_T_ = ' ' "
cQryPF8 += "	        AND SB1.B1_COD = PF8ANT.PF8_PRODUT "
cQryPF8 += "      LEFT JOIN " + RetSqlName("SBM") + " SBM "
cQryPF8 += "         ON SBM.BM_FILIAL = '" + xFilial("SBM") + "' "
cQryPF8 += "            AND SBM.D_E_L_E_T_ = ' ' "
cQryPF8 += "            AND SBM.BM_GRUPO = SB1.B1_GRUPO "
// para buscar o custo da OS que esta reprocessado, da PF9, considerar os deletados para pegar as OS's excluidas
cQryPF8 += "      LEFT JOIN " + RetSqlName("PF9") +" PF9 "
cQryPF8 += "         ON PF9.PF9_FILIAL = '" + xFilial("PF9")+ "' "
cQryPF8 += "	        AND PF9.PF9_IDOS = PF8ANT.PF8_IDOS "
cQryPF8 += "	        AND PF9.PF9_CODTEC = PF8ANT.PF8_CODTEC "
cQryPF8 += "	        AND PF9.PF9_DATA = PF8ANT.PF8_DATOS "
cQryPF8 += "            AND PF9.PF9_STATUS = '3' "
cQryPF8 +="       WHERE PF8ANT.PF8_FILIAL = '" + xFilial("PF8") +"' "
cQryPF8 += "             AND PF8ANT.PF8_MESREF ='" + cAnoRef + cMesRef +"' "
If !Empty(cUnSrv)
	cQryPF8 += "             AND PF8ANT.PF8_UNSRV IN " + cUnSrv + " "
EndIf 
cQryPF8 += "             AND PF8ANT.PF8_STATUS = '3' "
cQryPF8 += "             AND PF8ANT.D_E_L_E_T_ = ' ' "

cQryPF8 += ")" + CRLF

cQryPF8 += "GROUP BY MESREF,PROJETO, FRENTE, CLVLPRJ, MOTIVO, TECNICO, UNSRV, CARGO, FORNE3" + CRLF


// Instru��o SQL de Trabalho.
cQuery := "SELECT "
cQuery += "	PE5_EMPPRJ EMPRESA "
cQuery += "	,PE5_FILPRJ FILIAL  "
cQuery += "	,PE5.PE5_UNSRV UNSRVORI "
cQuery += "	,PE1.PE1_DESCRI DUNSRVORI "
cQuery += "	,PE1.PE1_CCUSTO CCUSTORI "
cQuery += "	,CTTA.CTT_DESC01 DCCUSTORI "
cQuery += "	,SA1.A1_COD CLIENTE "
cQuery += "	,SA1.A1_NOME NOMCLI "
cQuery += "	,PE5.PE5_PROJET PROJETO "
cQuery += "	,PE5.PE5_DESPRO DSCPRJ "
cQuery += "	,PE8.PE8_CODIGO FRENTE "
cQuery += "	,PE8.PE8_DESCR DSCFRENTE "
cQuery += "	,PE8.PE8_UNSRES UNSRESFRT "
cQuery += "	,PE1F.PE1_DESCRI DUNRESFRT "
cQuery += "	,SA1.A1_CODSEG CODSEGCLI "
cQuery += "	,AOV.AOV_DESSEG DSCSEGCLI "
cQuery += "	,AOV.AOV_XITECO ITECTBCLI "
cQuery += "	,PF8.UNSRV UNSRVDST "
cQuery += "	,PE1T.PE1_DESCRI DUNSRVDST "
cQuery += "	,PE1T.PE1_CCUSTO CCUSTODST "
cQuery += "	,CTTT.CTT_DESC01 DCCUSTDST "
cQuery += "	,PF8.TECNICO "
cQuery += "	,RD0TEC.RD0_NOME NOMETEC "
cQuery += "	,CASE WHEN PF8.FORNE3 <> '' THEN 'SIM' ELSE 'NAO' END TERCEIRO "
cQuery += "	,RD0TEC.RD0_XTPCGO TIPOTEC "
cQuery += "	,PF8.CARGO CARGOTEC "
cQuery += "	,SQ3.Q3_DESCSUM DSCCGOTEC " 
cQuery += "	,RD0TEC.RD0_XCDSEG CODSEGTEC "
cQuery += "	,AOVT.AOV_DESSEG DSCSEGTEC "
cQuery += "	,AOVT.AOV_XITECO ITESEGTEC "
cQuery += "	,PF8.CLVLPRJ  "
cQuery += "	,RD0TEC.RD0_XCLVL  "
cQuery += "	,PF8.TOTAL  "
cQuery += "	,PF8.MOTIVO "
cQuery += "	,PF8.MESREF "
cQuery += "	,PE5.PE5_VLRORC "
cQuery += "	,PE5.PE5_QTDORC "
cQuery += "	,RD0TEC.RD0_FILATU "
cQuery += "	,RD0TEC.RD0_XCARGO "
cQuery += "	,PF8.CUSTO "
cQuery += "FROM (" + cQryPF8 + ") PF8  "

cQuery += "INNER JOIN " + RetSqlName("PE5") + " PE5 ON "
cQuery += "	PE5.PE5_FILIAL = '"+xFilial("PE5") +"' "
cQuery += "	AND PE5.D_E_L_E_T_ = ' ' "
cQuery += "	AND PE5.PE5_PROJET = PF8.PROJETO "
cQuery += "LEFT JOIN " + RetSqlName("PE1") +" PE1 ON "
cQuery += "	PE1.PE1_FILIAL = '"+ xFilial("PE1")+"' "
cQuery += "	AND PE1.D_E_L_E_T_ = ' ' "
cQuery += "	AND PE1.PE1_CODIGO = PE5.PE5_UNSRV "
cQuery += "LEFT JOIN "+ RetSqlName("CTT") +" CTTA ON " 
cQuery += "	CTTA.CTT_FILIAL='"+ xFilial("CTT")+ "' "
cQuery += "	AND CTTA.D_E_L_E_T_ = ' ' "
cQuery += "  AND CTTA.CTT_CUSTO = PE1.PE1_CCUSTO "

cQuery += "LEFT JOIN " + RetSqlName("PE8") + " PE8 ON " 
cQuery += "	PE8.PE8_FILIAL = '"+ xFilial("PE8") +"' "
cQuery += "	AND PE8.D_E_L_E_T_ = ' ' "
cQuery += "	AND PE8.PE8_PROJET = PF8.PROJETO "
cQuery += "	AND PE8.PE8_CODIGO = PF8.FRENTE "
cQuery += "LEFT JOIN " + RetSqlName("PE1") +" PE1F ON "
cQuery += "	PE1F.PE1_FILIAL = '"+ xFilial("PE1")+"' "
cQuery += "	AND PE1F.D_E_L_E_T_ = ' ' "
cQuery += "	AND PE1F.PE1_CODIGO = PE8.PE8_UNSRES "

cQuery += "LEFT JOIN " + RetSqlName("SA1") + " SA1 ON "
cQuery += "  SA1.A1_FILIAL = '" + xFilial("SA1") +"' " 
cQuery += "	AND SA1.D_E_L_E_T_ = ' ' " 
cQuery += "	AND SA1.A1_COD = PE5.PE5_CLIENT "
cQuery += "	AND SA1.A1_LOJA = PE5.PE5_LOJA "

cQuery += "LEFT JOIN " + RetSqlName("AOV") +" AOV ON "
cQuery += "	AOV.AOV_FILIAL = '" + xFilial("AOV")+ "' "
cQuery += "	AND AOV.D_E_L_E_T_ = ' ' "
cQuery += "	AND AOV.AOV_CODSEG = SA1.A1_CODSEG "

//Dados do t�cnico
cQuery += "LEFT JOIN " + RetSqlName("RD0") + " RD0TEC" + CRLF
cQuery += "	   ON RD0TEC.RD0_FILIAL = '" + xFilial("RD0") + "'" + CRLF
cQuery += "    AND RD0TEC.RD0_CODIGO = PF8.TECNICO" + CRLF
cQuery += "    AND RD0TEC.D_E_L_E_T_ = ' '" + CRLF

cQuery += "LEFT JOIN " + RetSqlName("PE1") +" PE1T ON "
cQuery += "	PE1T.PE1_FILIAL = '"+ xFilial("PE1")+"' "
cQuery += "	AND PE1T.D_E_L_E_T_ = ' ' "
cQuery += "	AND PE1T.PE1_CODIGO = PF8.UNSRV "
cQuery += "LEFT JOIN "+ RetSqlName("CTT") +" CTTT ON " 
cQuery += "	CTTT.CTT_FILIAL='"+ xFilial("CTT")+ "' "
cQuery += "	AND CTTT.D_E_L_E_T_ = ' ' "
cQuery += "  AND CTTT.CTT_CUSTO = PE1T.PE1_CCUSTO "
cQuery += "LEFT JOIN " + RetSqlName("SQ3") + " SQ3 ON "
cQuery += "	SQ3.Q3_FILIAL = '" + xFilial("SQ3") + "' "
cQuery += "	AND SQ3.D_E_L_E_T_ = ' ' "
cQuery += "	AND SQ3.Q3_CARGO = PF8.CARGO "
cQuery += "LEFT JOIN " + RetSqlName("AOV") +" AOVT ON "
cQuery += "	AOVT.AOV_FILIAL = '" + xFilial("AOV")+ "' "
cQuery += "	AND AOVT.D_E_L_E_T_ = ' ' "
cQuery += "	AND AOVT.AOV_CODSEG = RD0TEC.RD0_XCDSEG

cQuery += "UNION ALL "

cQuery += "SELECT  "
cQuery += "	PE5_EMPPRJ EMPRESA "
cQuery += "	,PE5_FILPRJ FILIAL "
cQuery += "	,PE5.PE5_UNSRV UNSRVORI "
cQuery += "	,PE1.PE1_DESCRI DUNSRVORI "
cQuery += "	,PE1.PE1_CCUSTO CCUSTORI "
cQuery += "	,CTTA.CTT_DESC01 DCCUSTORI "
cQuery += "	,SA1.A1_COD CLIENTE "
cQuery += "	,SA1.A1_NOME NOMCLI "
cQuery += "	,PE5.PE5_PROJET PROJETO "
cQuery += "	,PE5.PE5_DESPRO DSCPRJ "
cQuery += "	,PE8.PE8_CODIGO FRENTE "
cQuery += "	,PE8.PE8_DESCR DSCFRENTE "
cQuery += "	,PE8.PE8_UNSRES UNSRESFRT "
cQuery += "	,PE1F.PE1_DESCRI DUNRESFRT "
cQuery += "	,SA1.A1_CODSEG CODSEGCLI "
cQuery += "	,AOV.AOV_DESSEG DSCSEGCLI "
cQuery += "	,AOV.AOV_XITECO ITECTBCLI "
cQuery += "	,PE8.PE8_UNSRES UNSRVDST "
cQuery += "	,PE1F.PE1_DESCRI DUNSRVDST "  
cQuery += "	,PE1F.PE1_CCUSTO CCUSTODST "
cQuery += "	,CTTF.CTT_DESC01 DCCUSTDST "		
cQuery += "	,'' TECNICO "
cQuery += "	,'SALDO PROJETO' NOMETEC "
cQuery += "	,'NAO'	TERCEIRO "
cQuery += "	,''	TIPOTEC  "
cQuery += "	,''	CARGOTEC "
cQuery += "	,''	DSCCGOTEC "					
cQuery += "	,'' CODSEGTEC "					
cQuery += "	,'' DSCSEGTEC "					
cQuery += "	,'' ITESEGTEC "					
cQuery += "	,'' CLVLPRJ "
cQuery += "	,'' RD0_XCLVL "
cQuery += "	,PE8.PE8_QTDHRS TOTAL "
cQuery += "	,PE8.PE8_MOTIVO MOTIVO "
cQuery += "	,PE8.PE8_MSREF "
cQuery += "	,PE5.PE5_VLRORC "
cQuery += "	,PE5.PE5_QTDORC "
cQuery += "	,'' RD0_FILATU "
cQuery += "	,'' RD0_XCARGO "
cQuery += "	,0  CUSTO "
cQuery += "FROM " + RetSqlName("PE8") + " PE8  "

cQuery += "INNER JOIN " + RetSqlName("PE5") + " PE5 ON "
cQuery += "	PE5.PE5_FILIAL = '"+xFilial("PE5") +"' "
cQuery += "	AND PE5.D_E_L_E_T_ = ' ' "
cQuery += "	AND PE5.PE5_PROJET = PE8.PE8_PROJET "
cQuery += "	AND PE5.PE5_UNSRV <> PE8.PE8_UNSRES "

cQuery += "LEFT JOIN " + RetSqlName("PE1") +" PE1 ON "
cQuery += "	PE1.PE1_FILIAL = '"+ xFilial("PE1")+"' "
cQuery += "	AND PE1.D_E_L_E_T_ = ' ' "
cQuery += "	AND PE1.PE1_CODIGO = PE5.PE5_UNSRV "
cQuery += "LEFT JOIN "+ RetSqlName("CTT") +" CTTA ON " 
cQuery += "	CTTA.CTT_FILIAL='"+ xFilial("CTT")+ "' "
cQuery += "	AND CTTA.D_E_L_E_T_ = ' ' "
cQuery += "  AND CTTA.CTT_CUSTO = PE1.PE1_CCUSTO "

cQuery += "LEFT JOIN " + RetSqlName("SA1") + " SA1 ON "
cQuery += "  SA1.A1_FILIAL = '" + xFilial("SA1") +"' " 
cQuery += "	AND SA1.D_E_L_E_T_ = ' ' " 
cQuery += "	AND SA1.A1_COD = PE5.PE5_CLIENT "
cQuery += "	AND SA1.A1_LOJA = PE5.PE5_LOJA "

cQuery += "LEFT JOIN " + RetSqlName("AOV") +" AOV ON "
cQuery += "	AOV.AOV_FILIAL = '" + xFilial("AOV")+ "' "
cQuery += "	AND AOV.D_E_L_E_T_ = ' ' "
cQuery += "	AND AOV.AOV_CODSEG = SA1.A1_CODSEG "

cQuery += "LEFT JOIN " + RetSqlName("PE1") +" PE1F ON "
cQuery += "	PE1F.PE1_FILIAL = '"+ xFilial("PE1")+"' "
cQuery += "	AND PE1F.D_E_L_E_T_ = ' ' "
cQuery += "	AND PE1F.PE1_CODIGO = PE8.PE8_UNSRES "

cQuery += "LEFT JOIN "+ RetSqlName("CTT") +" CTTF ON " 
cQuery += "	CTTF.CTT_FILIAL='"+ xFilial("CTT")+ "' "
cQuery += "	AND CTTF.D_E_L_E_T_ = ' ' "
cQuery += "  AND CTTF.CTT_CUSTO = PE1F.PE1_CCUSTO "

cQuery += "WHERE "
cQuery += "	PE8.PE8_FILIAL = '" + xFilial("PE8") +"' " 
cQuery += "	AND PE8.D_E_L_E_T_ = ' ' "
cQuery += "	AND PE8.PE8_MSREF ='" + cAnoRef + cMesRef +"' "

If !Empty(cUnSrv)
	cQuery += "	AND PE8.PE8_UNSRES IN " + cUnSrv + " "
EndIf

cQuery += "	AND PE8.PE8_TPFRT = '5' "
	
cQuery += "ORDER BY PROJETO"
cQuery += "	,FRENTE "
cQuery += "	,TECNICO "

cQuery := ChangeQuery(cQuery)

If Select(cAliasTRB) > 0
	(cAliasTRB)->( DbCloseArea())
EndIf


MPSysOpenQuery(cQuery, cAliasTRB)


If (cAliasTRB)->(EoF())
	Return lRet
EndIf

(cAliasTRB)->(DbGoTop())


oProcess:IncRegua2( "Contando registros...." )
oProcess:SetRegua2( 0 )

nTotReg:=0
(cAliasTRB)->( DbEval( {|| nTotReg++},{|| .T. },{|| .T. } ) )
oProcess:SetRegua1(nTotReg)

//Cria��o da tabela de trabalho

If !(lRet := SV009C01())
	Return lRet
EndIf

(cAliasTRB)->(DbGoTop())

nRegAtu := 0

(cAliasTRB)->(DbGoTop())
While !(cAliasTRB)->(EoF())
	nRegAtu ++
	oProcess:IncRegua2( "Processando Registro -> " + StrZero(nRegAtu,8) + " de "+ StrZero(nTotReg,8) )
	cTpMot	:= ""
	nPosMt	:= 0
	
	cTpmot := Posicione("PE0",1,xFilial("PE0")+(cAliasTRB)->MOTIVO,"PE0_TPMOT")
	nPosMt := aScan(_aCbTpMot,{|x| AllTrim(cTpMot) == x[2] })
	
	//Defini��o de valoriza��o
	//Quando (UNSRVTEC = UNSRESFRT) E UNSRESFRT <> UNSRVORI = Acatar o valor do par�metro
	//sen�o valorizar a partir do cargo
	//Para os valores 'AN|AP' pegar do parametro de Analista os demais coordenador.  
	//ALTERADO A FORMA DE CALCULAR CUSTO
	//SE EXISTIR CUSTO CALCULADO NO REGISTRO UTILIZA ELE, SEN�O FAZ DO JEITO ANTIGO
	nVlrHora  := 0
	
	If (cAliasTRB)->CUSTO > 0 
		// para o caso das fabricas pegar o valor do parametro
		If ((cAliasTRB)->UNSRVDST = (cAliasTRB)->UNSRESFRT) .And. ((cAliasTRB)->UNSRESFRT <> (cAliasTRB)->UNSRVORI)
			If Alltrim((cAliasTRB)->TIPOTEC) $ 'AN|AP' .Or. Empty(AllTrim((cAliasTRB)->TIPOTEC))//Esta condi��o � para atender o Saldo de Projeto
				nVlrHora := nVlrAna
			Else 
				nVlrHora := nVlrCor
			EndIf
		Else		
			nVlrHora  := IIF((cAliasTRB)->TOTAL == 0, 0, NoRound( ( (cAliasTRB)->CUSTO / (cAliasTRB)->TOTAL ), 2) )
		EndIf		
	Else
		If ((cAliasTRB)->UNSRVDST = (cAliasTRB)->UNSRESFRT) .And. ((cAliasTRB)->UNSRESFRT <> (cAliasTRB)->UNSRVORI)
			
			nVlrHora:= SV009E01((cAliasTRB)->RD0_FILATU,(cAliasTRB)->MESREF,iIf(!Empty(AllTrim((cAliasTRB)->CARGOTEC)),(cAliasTRB)->CARGOTEC, (cAliasTRB)->RD0_XCARGO ))
			If nVlrHora == 0
				If Alltrim((cAliasTRB)->TIPOTEC) $ 'AN|AP' .Or. Empty(AllTrim((cAliasTRB)->TIPOTEC))//Esta condi��o � para atender o Saldo de Projeto
					nVlrHora := nVlrAna
				Else 
					nVlrHora := nVlrCor
				EndIf
			EndIf
		Else
			nVlrHora:= SV009E01((cAliasTRB)->RD0_FILATU,(cAliasTRB)->MESREF,iIf(!Empty(AllTrim((cAliasTRB)->CARGOTEC)),(cAliasTRB)->CARGOTEC, (cAliasTRB)->RD0_XCARGO ))
			If nVlrHora == 0
				nVlrHora := nVlrAna
			EndIf
		EndIf
	EndIf

	lReclck := !(TRBTRF->(DbSeek((cAliasTRB)->(PROJETO + FRENTE + TECNICO))))
	If TRBTRF->(RecLock("TRBTRF",lReclck))	
    	TRBTRF->EMPRESA 	:= 	(cAliasTRB)->EMPRESA
    	TRBTRF->FILIAL 		:= 	(cAliasTRB)->FILIAL
    	TRBTRF->UNSRVORI 	:=	(cAliasTRB)->UNSRVORI
    	TRBTRF->DUNSRVORI	:=	(cAliasTRB)->DUNSRVORI
    	TRBTRF->CCUSTORI 	:=	(cAliasTRB)->CCUSTORI
    	TRBTRF->DCCUSTORI	:=	(cAliasTRB)->DCCUSTORI
    	TRBTRF->CLIENTE		:=	(cAliasTRB)->CLIENTE
    	TRBTRF->NOMCLI 		:=	(cAliasTRB)->NOMCLI
    	TRBTRF->PROJETO 	:=	(cAliasTRB)->PROJETO
    	TRBTRF->DSCPRJ 		:=	(cAliasTRB)->DSCPRJ
    	TRBTRF->FRENTE 		:=	(cAliasTRB)->FRENTE
    	TRBTRF->DSCFRENTE 	:=	(cAliasTRB)->DSCFRENTE
    	TRBTRF->UNSRESFRT 	:=	(cAliasTRB)->UNSRESFRT
    	TRBTRF->DUNRESFRT	:=	(cAliasTRB)->DUNRESFRT
    	TRBTRF->CODSEGCLI 	:=	(cAliasTRB)->CODSEGCLI
    	TRBTRF->DSCSEGCLI 	:=	(cAliasTRB)->DSCSEGCLI
    	TRBTRF->ITECTBCLI 	:=	(cAliasTRB)->ITECTBCLI
    	TRBTRF->VLHORAPRJ 	:=	(cAliasTRB)->PE5_VLRORC / (cAliasTRB)->PE5_QTDORC
    	TRBTRF->UNSRVDST	:=	(cAliasTRB)->UNSRVDST
    	TRBTRF->DUNSRVDST 	:=	(cAliasTRB)->DUNSRVDST
    	TRBTRF->CCUSTODST 	:=	(cAliasTRB)->CCUSTODST
    	TRBTRF->DCCUSTDST	:=	(cAliasTRB)->DCCUSTDST
    	TRBTRF->TPLANCTO	:=	If (nPosMt > 0, _aCbTpMot[nPosMt,1],"") 	
    	TRBTRF->TECNICO		:=	(cAliasTRB)->TECNICO
    	TRBTRF->NOMETEC		:=	(cAliasTRB)->NOMETEC
    	TRBTRF->TERCEIRO	:=	(cAliasTRB)->TERCEIRO 
    	TRBTRF->TIPOTEC		:= 	(cAliasTRB)->TIPOTEC
    	TRBTRF->CARGOTEC	:=	(cAliasTRB)->CARGOTEC
    	TRBTRF->DSCCGOTEC	:=	(cAliasTRB)->DSCCGOTEC
		TRBTRF->UNSRVTEC	:=	(cAliasTRB)->UNSRVDST
		TRBTRF->DUNSRVTEC	:= 	(cAliasTRB)->DUNSRVDST
    	TRBTRF->CODSEGTEC	:=	(cAliasTRB)->CODSEGTEC
    	TRBTRF->DSCSEGTEC	:=	(cAliasTRB)->DSCSEGTEC
    	TRBTRF->ITESEGTEC	:=	(cAliasTRB)->ITESEGTEC
    	TRBTRF->CLSVLRORI	:=	(cAliasTRB)->CLVLPRJ
    	TRBTRF->CLSVLRDST	:=	(cAliasTRB)->RD0_XCLVL
    	TRBTRF->VLHORA		:=  nVlrHora
    	 
		nPosMt := AScan(aCpsPE0,{|x| x[2] == (cAliasTRB)->MOTIVO })
		If nPosMt > 0
			TRBTRF->&(aCpsPE0[nPosMt,1]) += (cAliasTRB)->TOTAL
		EndIf
		
		TRBTRF->HRTOTAPONT	+= (cAliasTRB)->TOTAL
		If cTpMot == "1"
   			TRBTRF->HRTOTFAT	+= (cAliasTRB)->TOTAL
   		Else 
   			TRBTRF->HRTOTNFAT	+=	(cAliasTRB)->TOTAL
		EndIf
		TRBTRF->VLTOTAL		:= TRBTRF->VLHORA * TRBTRF->HRTOTAPONT
		TRBTRF->VLRTOTPFX	:= NoRound(TRBTRF->VLTOTAL / 1000)
    	
    	TRBTRF->CONTABILZ		:= iIf( ;
    										SV009G01((cAliasTRB)->CCUSTORI,;
    										 (cAliasTRB)->CCUSTODST,;
    										 (cAliasTRB)->CODSEGCLI,;
    										 (cAliasTRB)->CODSEGTEC,;
											 (cAliasTRB)->TERCEIRO,;
											 (cAliasTRB)->CLVLPRJ,;
    										 (cAliasTRB)->RD0_XCLVL);
											 ,"S" , "N" )
		TRBTRF->(MsUnLock())
	
	Else
		FwLogMsg("ERROR", , "BusinessObject", FunName(), "", "01", ( "Erro de grava��o da tabela tempor�ria !!" ), 0, 0, {})
		(cAliasTRB)->(DbCloseArea())
		Return lRet := .F.
	EndIf 	
	
	(cAliasTRB)->(DbSkip())
End

(cAliasTRB)->(DbCloseArea())

//Copio o Arquivo para a esta��o de trabalho
If lRet
	//SV009F01()

	If !ExistDIR("C:\APEXCEL")
	If	MAKEDIR("C:\APEXCEL")!= 0
		Aviso("Erro" ,"Nao foi possivel criar diretorio: C:\APEXCEL\",{"OK"}) //"Inconsistencia"###"Nao foi possivel criar diretorio "###".Finalizando ..."
		return nil
	EndIf
	EndIf

	cFile := "TSRVR009"+DtoS(Date())+STRTRAN(Time(), ":", "")+".XLS"
	cArqDestino := "C:\APEXCEL\"+cfile

	If U_TRGXLX01("TRBTRF", @cArqDestino, , /*cTitle*/, .f./*lAskOpen*/)
		MsgAlert('Arquivo de Conferencia salvo em '+cArqDestino,'AVISO')
	EndIf



EndIf	

Return lRet

/*/{Protheus.doc} SV009C01
Relat�rio de  Transfer�ncias e Despesas por Centros de Custos - Controle de Projetos
ANTIGO CRPMOR13.PRW - remodelado V12.
<AUTHOR> Jr
@since 30/07/2016
@version 1.0
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
Static Function SV009C01()

Local lCriaTrb	:= .T.
Local aCampos   	:= {}
Local cOrdem    	:= "PROJETO+FRENTE+TECNICO"
Local cRootPath 	:= AllTrim( GetSrvProfString( "ROOTPATH", "" ) )
Local cLocFile  	:= AllTrim( GetSrvProfString( "STARTPATH", "" ) )
Local lRet			:= .T.

Default lCriaTrb 	:= .T.

If lCriaTrb
	
	//Populo a array com os motivos de apontamento.
	aCpsPE0 := SV009D01() 
    
	//CAMPO DE QUEM TOMOU OS RECURSOS, OU SEJA, UNIDADE DE SERVICO DO CFP, PEGOU EMPRESTADO
	AADD(aCampos,{"VLRTOTPFX"  	, "N" , 15 , 2})
	AADD(aCampos,{"EMPRESA"    	, "C" , 02 , 0})
	AADD(aCampos,{"FILIAL"    	, "C" , 11 , 0})
	AADD(aCampos,{"UNSRVORI"   	, "C" , 03 , 0})
	AADD(aCampos,{"DUNSRVORI"  	, "C" , 30 , 0})
	AADD(aCampos,{"CCUSTORI" 	, "C" , 09 , 0})
	AADD(aCampos,{"DCCUSTORI" 	, "C" , 40 , 0})
	AADD(aCampos,{"CLIENTE"    	, "C" , 06 , 0})
	AADD(aCampos,{"NOMCLI"     	, "C" , 50 , 0})
	AADD(aCampos,{"PROJETO"    	, "C" , 10 , 0})
	AADD(aCampos,{"DSCPRJ"    	, "C" , 50 , 0})
	AADD(aCampos,{"FRENTE"    	, "C" , 03	, 0})
	AADD(aCampos,{"DSCFRENTE"	, "C" , 50 , 0})
	AADD(aCampos,{"UNSRESFRT"  	, "C" , 03 , 0})
	AADD(aCampos,{"DUNRESFRT" 	, "C" , 30 , 0})
	AADD(aCampos,{"CODSEGCLI"  	, "C" , 06 , 0})
	AADD(aCampos,{"DSCSEGCLI"  	, "C" , 30 , 0})
	AADD(aCampos,{"ITECTBCLI" 	, "C" , 09 , 0})
	AADD(aCampos,{"VLHORAPRJ"  	, "N" , 15 , 2})
	AADD(aCampos,{"UNSRVDST"  	, "C" , 03 , 0})
	AADD(aCampos,{"DUNSRVDST"  	, "C" , 30 , 0})
	AADD(aCampos,{"CCUSTODST" 	, "C" , 09 , 0})
	AADD(aCampos,{"DCCUSTDST" 	, "C" , 40 , 0})
	AADD(aCampos,{"TPLANCTO"   	, "C" , 15 , 0})
   // AADD(aCampos,{"SEPARAPCO"  	, "C" , 03 , 0})
	AADD(aCampos,{"TECNICO"    	, "C" , 06 , 0})
	AADD(aCampos,{"NOMETEC"    	, "C" , 30 , 0})
	AADD(aCampos,{"TERCEIRO"	, "C" , 03 , 0})
	AADD(aCampos,{"TIPOTEC"    	, "C" , 02 , 0})
//	AADD(aCampos,{"DESCTIPO"   	, "C" , 30 , 0})
   	AADD(aCampos,{"CARGOTEC"   	, "C" , 05 , 0})
   	AADD(aCampos,{"DSCCGOTEC" 	, "C" , 50 , 0})
  	AADD(aCampos,{"UNSRVTEC"   	, "C" , 03 , 0})
   	AADD(aCampos,{"DUNSRVTEC"  	, "C" , 30 , 0})
	AADD(aCampos,{"CODSEGTEC"  , "C" , 06 , 0})
	AADD(aCampos,{"DSCSEGTEC"  , "C" , 30 , 0})
	AADD(aCampos,{"ITESEGTEC" , "C" , 09 , 0})
	AADD(aCampos,{"CLSVLRORI"	, "C", 05, 0})
	AAdd(aCampos,{"CLSVLRDST"	, "C", 05, 0})
	AADD(aCampos,{"VLHORA"     , "N" , 15 , 2})
    //AADD(aCampos,{"LOTECTB"    , "C" , 10 , 0})
    
   	aEval(aCpsPE0,{|x,y| AAdd(aCampos,{x[1] , "N" , 15 , 2}) })
   	
	AADD(aCampos,{"HRTOTAPONT"  , "N" , 15 , 2})
  	AADD(aCampos,{"HRTOTFAT"    , "N" , 15 , 2})
   	AADD(aCampos,{"HRTOTNFAT"   , "N" , 15 , 2})
    AADD(aCampos,{"VLTOTAL"    		, "N" 	, 15 	, 2})
    AAdd(aCampos,{"CONTABILZ"		,"C"	, 1 	,0 })
        
    If  (Select("TRBTRF") > 0)
        TRBTRF->(DbCloseArea())
    EndIf
    

    
	oTemptable := FWTemporaryTable():New( "TRBTRF")
	oTemptable:SetFields( aCampos )
	oTempTable:AddIndex("index1", {"CLIENTE","CODSEGCLI"} )
	oTempTable:Create()


Else
    If !( Select( "TRBTRF" ) > 0 )
        If File( cArqTRBTrf+GetDBExtension() ) .OR. File( cRootPath+cLocFile+cArqTRBTrf+GetDBExtension() )
            oTemptable := FWTemporaryTable():New( "TRBTRF")
			oTemptable:SetFields( aCampos )
			oTempTable:AddIndex("index1", {"CLIENTE","CODSEGCLI"} )
			oTempTable:Create()

        Else
            MsgAlert( "N�o foi possivel ler o arquivo de dados. Entre em contato com o suporte tecnico!","TSRVR009" )
            Return .F.
        EndIf
    EndIf   
EndIf

Return lRet


/*/{Protheus.doc} SV009D01
(Cria array com campos que ser�o criados na tabela tempor�ria. 
Percorre tabela PE0 e cria campos do relatorio conforme motivos encontrados)
<AUTHOR>
@since 
@version 1.0
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
Static Function SV009D01()

Local aCampos	:= {}
Local cQuery	:= ""
Local cAliPE0	:= GetNextAlias()

cQuery := " SELECT PE0_COD, PE0_DESC"
cQuery += " FROM " + RetSqlName("PE0") + " PE0"
cQuery += " WHERE PE0_FILIAL = '" + xFilial("PE0") + "'"
cQuery += " AND PE0.D_E_L_E_T_ = ' ' "
cQuery += " ORDER BY PE0_COD "

cQuery := ChangeQuery(cQuery)



MPSysOpenQuery(cQuery, cAliPE0)

dbSelectArea(cAliPE0)
(cAliPE0)->(dbGoTop())

While (cAliPE0)->(!Eof())
	AADD(aCampos,{"MOT" + Alltrim((cAliPE0)->PE0_COD),(cAliPE0)->PE0_COD,(cAliPE0)->PE0_DESC})
	(cAliPE0)->(DbSkip())
End

(cAliPE0)->(dbCloseArea())

Return aCampos


/*/{Protheus.doc} SV009E01
Static respons�vel por fazer o calculo de valoriza��o do cargo.
<AUTHOR> Junior
@since 02/08/2016
@version 1.0
@return ${Numerico, $Retorna o valor calculado.
@example
(examples)
@see (links_or_references)
/*/

Static Function SV009E01(cFilTec,cDtRef,cCargo)

Local nVlrRet	:= 0
Local aArea 	:= GetArea()
Local cQry		:= ""

If Select("TMPPEQ") > 0
	TMPPEQ->(DbCloseArea())
EndIf

BeginSql Alias "TMPPEQ"

	SELECT
	MAX(PEQ.PEQ_ANOMES)ANOMES
	,PEQ.PEQ_VALOR VALOR
	FROM %Table:PEQ% PEQ
	WHERE 
	PEQ.PEQ_FILIAL = %Exp:cFilTec%
	AND PEQ.%NotDel%
	AND PEQ.PEQ_ANOMES <= %Exp:cDtRef%
	AND PEQ.PEQ_CARGO = %Exp:cCargo%
	GROUP BY 
	PEQ_VALOR
	ORDER BY
	ANOMES DESC

EndSql

cQry := GetLastQuery()[2]

While !(TMPPEQ->(EoF()))
	nVlrRet := TMPPEQ->VALOR
	Exit
	TMPPEQ->(DbSkip())
End

TMPPEQ->(DbCloseArea())
//nVlrRet := Posicione("PEQ", 1 , cFilTec + cDtRef + cCargo, "PEQ_VALOR")

RestArea(aArea)

Return nVlrRet


/*/{Protheus.doc} SV009F01
Static de copia e transfer�ncia do arquivo DBF para a esta��o de trabalho.
<AUTHOR> Junior
@since 02/08/2016
@version 1.0
@return $Booleano, $Verdadeiro ou falso conforme a realiza��o do procedimento
@example
(examples)
@see (links_or_references)
/*/

Static Function SV009F01()

Local lRet 	 		:= .F.
Local cArquivo 		:= "TSRVR009.DBF"
Local cLocalPath 	:= ''//U_GetLocEnd()
Local oExcel		:= MntExcel():New()

oProcess:SetRegua2(0)
oProcess:IncRegua2("Copiando o arquivo do servidor . Aguarde...")
	
dbSelectArea("TRBTRF")
TRBTRF->(DbGoTop())

oExcel:SetNameArq(cArquivo)
oExcel:SetTitle('TSRVR009')
oExcel:SetCab((TRBTRF)->(dbStruct()))
oExcel:SetTabTemp(TRBTRF)
oExcel:Create(oExcel)

If File(cLocalPath+cArquivo)
	MessageBox("Arquivo "+cLocalPath+cArquivo+" criado com sucesso!","Gera��o de arquivo .DBF",0)
Else
	MessageBox("N�o foi poss�vel copiar o arquivo "+cArquivo+" para a pasta local "+cLocalPath+CRLF+"Por favor, verifique suas permiss�es de acesso a esta pasta.","Problema",1)
EndIf
TRBTRF->( DbCloseArea() )
FErase(cArqTRBTrf+GetDBExtension())
FErase(cArqTRBTrf+OrdBagExt())

Return lRet

/*/{Protheus.doc} SV009G01
Static para valida��o das regras do campo CONTABILIZA
<AUTHOR> Junior
@since 03/08/2016
@version 1.0
@return $Booleano, $Verdadeiro ou falso conforme as valida��es existentes.
@example
(examples)
@see (links_or_references)
/*/

Static Function SV009G01 (_cCustoORI, _cCustoDST, _cCodSegCLI, _cCodSegTEC, _cTerceiro, _cClvlORI,_cClvlDST)

Local _lCont := .F.

If Upper(AllTrim(_cTerceiro)) == "SIM"
	Return _lCont
EndIf

If AllTrim(_cCustoORI) <> AllTrim(_cCustoDST)
	_lCont := .T.
ElseIf AllTrim(_cCodSegCLI) <> AllTrim(_cCodSegTEC)
	_lCont := .T.
ElseIf	AllTrim(_cClvlORI) <> AllTrim(_cClvlDST)
	_lCont := .T. 	    		
EndIf

Return _lCont


/*/{Protheus.doc} SV009V01
(Valida��o do Pergunte do m�s de refer�ncia)
<AUTHOR>
@since 23/10/2015
@version 1.0
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
User Function SV009V01()
Local lRet		:= .F.

If !Empty(MV_PAR01)
	If MV_PAR01 >= "01" .And. MV_PAR01 <= "12"
		lRet := .T.
	Else
		MsgAlert("M�s inv�lido!","SV009V01")
	EndIf
Else
	MsgAlert("M�s refer�ncia n�o pode ser vazio!","SV009V01")
EndIf
	
Return lRet


/*/{Protheus.doc} SV009V02
(VAlida��o do Pergutnte do anod e refer�ncia)
<AUTHOR>
@since 23/10/2015
@version 1.0
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
User Function SV009V02()
Local lRet		:= .T.
Local cAnoVld	:= "1234567890"
Local cMsgError := ""
Local ni		:= 0

If !Empty(MV_PAR02)
	If Len(AllTrim(MV_PAR02)) = 4
		For ni := 1 to Len(MV_PAR02)
			If !(SubStr(MV_PAR02,ni,1) $ cAnoVld)
				cMsgError += SubStr(MV_PAR02,ni,1)
				lRet := .F.
			EndIf
		Next
		If !lRet
			MsgAlert("Ano cont�m caracter '" + cMsgerror + "' inv�lido! - ","SV009V02" )
		EndIf
	Else
		MsgAlert("Ano inv�lido!","SV009V02")
	EndIf
Else
	MsgAlert("Ano refer�ncia n�o pode ser vazio!","SV009V02")
EndIf

Return lRet

/*/{Protheus.doc} SV009A01
(Fun��o de cria��o de perguntes do relat�rio)
<AUTHOR>
@since 23/10/2015
@version 1.0
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
Static Function SV009A01()

Local aArea := GetArea()
Local aHelpPor	:= {}
Local aHelpEng	:= {}
Local aHelpSpa	:= {}
Local aRegs		:= {}  
Local nTamSX3	:= 0
Local nDecSx3	:= 0       
Local cGrupoSX3	:= ""
Local cValidX1	:= ""
Local cTipoPerg	:= "G"  //G=Normal ; C=ComboBox
Local cPerg		:= "TSRVR009"

dbSelectArea("SX3")
dbSetOrder(2)

aHelpPor	:= {}
aHelpEng	:= {}
aHelpSpa	:= {}
Aadd( aHelpPor,	"Filtro da M�s Referencia					"	)
Aadd( aHelpPor,	"											"	)

Aadd( aHelpSpa,	"Filtro da M�s Referencia 					"	)
Aadd( aHelpSpa,	"											"	)

Aadd( aHelpEng,	"Filtro da M�s Referencia 					"	)
Aadd( aHelpEng,	"											"	)

nTamSX3		:=	2
nDecSx3		:=	0
cGrupoSX3	:=	Space(Len(GetSx3Cache("PF9_DTPREM","X3_GRPSXG") ) )	
 
cTipoPerg	:= "G"
cValidX1	:= "U_SV009V01()" 
U_FTTVSX001( cPerg, "01","Mes Refer�ncia","M�s referencia","Mes Referencia","mv_ch1","C",nTamSX3,nDecSx3,0,cTipoPerg,cValidX1,"",cGrupoSX3,"",;
	"mv_par01","","","","","","","","","","","","","","","","",aHelpPor,aHelpEng,aHelpSpa)
	
aHelpPor	:= {}
aHelpEng	:= {}
aHelpSpa	:= {}
Aadd( aHelpPor,	"Filtro da Ano referencia					"	)
Aadd( aHelpPor,	"											"	)
	
Aadd( aHelpSpa,	"Filtro da Ano referencia					"	)
Aadd( aHelpSpa,	"											"	)

Aadd( aHelpEng,	"Filtro da Ano Referencia					"	)
Aadd( aHelpEng,	"											"	)

nTamSX3		:=	4
nDecSx3		:=	0
cGrupoSX3	:=	Space(Len(GetSx3Cache("PF9_DTPREM","X3_GRPSXG"))) 	

cTipoPerg	:= "G"
cValidX1	:= "U_SV009V02()" 
U_FTTVSX001( cPerg, "02","Ano referencia","Ano referencia","Ano referencia","mv_ch2","C",nTamSX3,nDecSx3,0,cTipoPerg,cValidX1,"",cGrupoSX3,"",;
	"mv_par02","","","","","","","","","","","","","","","","",aHelpPor,aHelpEng,aHelpSpa)

aHelpPor	:= {}
aHelpEng	:= {}
aHelpSpa	:= {}
Aadd( aHelpPor,	"Filtro Unidades de Servi�o				  	"	)
Aadd( aHelpPor,	"											"	)

Aadd( aHelpSpa,	"Filtro Unidades de Servi�o					"	)
Aadd( aHelpSpa,	"											"	)

Aadd( aHelpEng,	"Filtro Unidades de Servi�o					"	)
Aadd( aHelpEng,	"											"	)

nTamSX3		:=	99
nDecSx3		:=	0
cGrupoSX3	:=	" "	
 
cTipoPerg	:= "G"
cValidX1	:= "U_SV011C01('MV_PAR03')" 
U_FTTVSX001( cPerg, "03","Unidades de Servi�o?","Unidades de Servi�o?","Unidades de Servi�o?","mv_ch3","C",nTamSX3,nDecSx3,0,cTipoPerg,cValidX1,"",cGrupoSX3,"",;
	"mv_par03","","","","","","","","","","","","","","","","",aHelpPor,aHelpEng,aHelpSpa)

aHelpPor	:= {}
aHelpEng	:= {}
aHelpSpa	:= {}
Aadd( aHelpPor,	"Valor hora Analista				  	"	)
Aadd( aHelpPor,	"										"	)

Aadd( aHelpSpa,	"Valor hora Analista					"	)
Aadd( aHelpSpa,	"										"	)

Aadd( aHelpEng,	"Valor hora Analista					"	)
Aadd( aHelpEng,	"										"	)

nTamSX3		:=	7
nDecSx3		:=	2
cGrupoSX3	:=	Space(Len(GetSx3Cache("PF9_DTPREM","X3_GRPSXG"))) 	

cTipoPerg	:= "G"
cValidX1	:= "" 
U_FTTVSX001( cPerg, "04","Valor Analista?","Valor Analista","Valor Analista? ","mv_ch4","N",nTamSX3,nDecSx3,0,cTipoPerg,cValidX1,"",cGrupoSX3,"",;
	"mv_par04","","","","","","","","","","","","","","","","",aHelpPor,aHelpEng,aHelpSpa)

aHelpPor	:= {}
aHelpEng	:= {}
aHelpSpa	:= {}
Aadd( aHelpPor,	"Valor hora Coordenador				 	"	)
Aadd( aHelpPor,	"											"	)

Aadd( aHelpSpa,	"Valor hora Coordenador						"	)
Aadd( aHelpSpa,	"											"	)

Aadd( aHelpEng,	"Valor hora Coordenador					 	"	)
Aadd( aHelpEng,	"											"	)

nTamSX3		:=	7
nDecSx3		:=	2
cGrupoSX3	:=	Space(Len(GetSx3Cache("PF9_DTPREM","X3_GRPSXG"))) 	

cTipoPerg	:= "G"
cValidX1	:= "" 
U_FTTVSX001( cPerg, "05","Valor Hora Cooordenador","Valor Hora Cooordenador","Valor Hora Cooordenador","mv_ch5","N",nTamSX3,nDecSx3,0,cTipoPerg,cValidX1,"",cGrupoSX3,"",;
	"mv_par05","","","","","","","","","","","","","","","","",aHelpPor,aHelpEng,aHelpSpa)

SX3->(dbSetOrder(1))
RestArea(aArea)
Return
