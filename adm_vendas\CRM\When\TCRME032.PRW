#Include 'TOTVS.ch'

/*/{Protheus.doc} TCRME032
Trava do campo ADZ_XEXCJR - <PERSON>cluir <PERSON>
@type function
@version 
<AUTHOR>
@since 01/08/2022
@return return_type, return_description
/*/
User Function TCRME032()
	Local aArea		:= GetArea()
	Local lRet		:= .T.
	Local oModel	:= FWModelActive()
	Local oModelADZ	:= oModel:GetModel("ADZPRODUTO")
	Local lXEXCJR	:= oModelADZ:GetValue("ADZ_XEXCJR")

	If lXEXCJR == "1"
		lRet := .F.
	EndIf

	RestArea(aArea)
Return(lRet)
