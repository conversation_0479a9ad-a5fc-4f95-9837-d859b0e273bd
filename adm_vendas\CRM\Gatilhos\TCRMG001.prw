#Include 'Protheus.ch'

/*/{Protheus.doc} TCRMG001
Retorna superior
@type function
<AUTHOR>
@since 27/01/2016
@version 1.0
@param cCargo, character, Cargo a retornar
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
User Function TCRMG001(cCargo,cVendedor)
Local aArea		:= GetArea()
Local aAreaAZS	:= AZS->(GetArea())
Local cRet		:= ""
Local cVend		:= ""
Default cVendedor := ""

cVend := Iif(!empty(cVendedor),cVendedor,&(ReadVar()))

dbSelectAreA("AZS")
dbSetOrder(4)
dbSeek(xFilial("AZS")+cVend)

If nModulo <> 73
	CRMXSetPaper(AZS->(AZS_CODUSR+AZS_SEQUEN+AZS_PAPEL)) 
Endif

cRet := U_TCRMXSUP(AZS->AZS_CODUSR,cCargo,2,,AZS->AZS_SEQUEN + AZS->AZS_PAPEL,,AZS->AZS_NVESTN)

RestArea(aAreAAZS)
RestArea(aArea)
Return(cRet)
