#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Utils.ExtractValueArrayUtils

/*/{Protheus.doc} ExtractValueArray
This Class aims to extract a value from the income array param
@type class
@version  1.0
<AUTHOR>
@since 10/31/2024
/*/
Class   ExtractValueArray
	static method extractValue(cCampo as character, aDados as array) as variant
EndClass

/*/{Protheus.doc} ExtractValueArray::extractValue
This Static Method aims to return the value of the Field from the income Array
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@param cCampo, character, The name of the field from which to extract the value
@param aDados, array, The income array that will be used to extract the value of the field
@return variant, The extracted value; if the field does not exist in the array, this will be nil
/*/
Method  extractValue(cCampo as character, aDados as array)  As  Variant Class   ExtractValueArray
    Local nPos  as integer
    Local uRet  as variant

    nPos := aScan( aDados, { |x| AllTrim( Upper( x[1] ) ) == AllTrim( Upper( cCampo ) )  } )
    
    If(nPos > 0)
    	uRet := aDados[nPos][2]
    Else
    	uRet := Nil
    EndIf

Return uRet


