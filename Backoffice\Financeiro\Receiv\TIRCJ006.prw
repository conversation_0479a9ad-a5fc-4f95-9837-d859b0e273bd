#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWADAPTEREAI.CH"

User Function TIRCJ006(lJob)

	Local cQry     := ""

	Local cAlsPro := GetNextAlias()
	Local cHora	:=  " "
	Local cHoras	:= ""
	Default lJob := .T.


	If U_RCVENV(cEmpAnt,cFilAnt)
		cHora	:= Substr(cvaltochar(time()),1,2)
		cHoras  := GetMv("TI_RCJ06H", .F.,"20")	//Habilita a exclusao dO JOB NESSAS DETERMINADAS HORAS
		nDias   := GetMv("TI_RCJ06D", .F.,60)	//Habilita a exclusao dO JOB NESSAS DETERMINADAS HORAS

		DBSELECTAREA("SE1")
		dbSetORder(1)

		DBSELECTAREA("P39")
		dbSetORder(1)

		if cHora $ cHoras
			cQry := " SELECT Min(P38_EMISSA) P38_EMISSA"
			cQry += " FROM "+ RetSqlName("P38") +" P38 "
			cQry += " WHERE   P38.D_E_L_E_T_ = ' '    and P38_NUMBCO = ' '  and P38_QUEBRA <> '1' AND P38_TIPO ='A' "
			cQry += " AND P38_ASALDO > 0 "


			dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAlsPro,.T.,.T.)


			while !(cAlsPro)->(EOF())
				IF  Stod((cAlsPro)->P38_EMISSA) > (DDatabase-nDias) // Controle pra pegar sempre os ultimos 60 Dias
					U_TRCV005I(Stod((cAlsPro)->P38_EMISSA))
				Else
					U_TRCV005I((DDatabase-60) )
				EndIF
				(cAlsPro)->(Dbskip())
			EndDo
		EndIf
	EndIF
Return
