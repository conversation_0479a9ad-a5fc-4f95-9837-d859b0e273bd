#Include 'Protheus.ch'
#Include 'FWMVCDef.ch'

/*/{Protheus.doc} U_TGCVA015
Cadastro de 'De para' de produtos.

<AUTHOR> <PERSON>
@since 05/06/2014
@version 11.5
/*/

User Function TGCVA015()

	Local oBrowse := FWmBrowse():New()

	oBrowse:SetAlias("ZOK")
	oBrowse:SetDescription("De para de produtos")
	oBrowse:Activate()

Return NIL

/*/{Protheus.doc} MenuDef
Cadastro de 'De para' de produtos.

<AUTHOR> <PERSON>
@since 05/06/2014
@version 11.5
/*/

Static Function MenuDef()

Return FWMVCMenu("TGCVA015")

/*/{Protheus.doc} ModelDef
Cadastro de 'De para' de produtos.

<AUTHOR> <PERSON>
@since 05/06/2014
@version 11.5
/*/

Static Function ModelDef()
	Local oModel
	Local oStr := FWFormStruct(1,"ZOK")

	oModel := MPFormModel():New("ModelName")
	oModel:AddFields("ZOKMASTER",,oStr)
	oModel:SetDescription("De para de produtos")
	oModel:GetModel("ZOKMASTER"):SetDescription("De para de produtos")
	oModel:SetPrimaryKey({})

Return oModel

/*/{Protheus.doc} MenuDef
Cadastro de 'De para' de produtos.

<AUTHOR> Ribeiro
@since 05/06/2014
@version 11.5
/*/

Static Function ViewDef()

	Local oView
	Local oModel := FWLoadModel( 'TGCVA015' )
	Local oStr := FWFormStruct(2,"ZOK")

	oView := FWFormView():New()

	oView:SetModel(oModel)
	oView:AddField("VIEW_ZOK",oStr,"ZOKMASTER")
	oView:CreateHorizontalBox("SUPERIOR",100)
	oView:SetOwnerView("VIEW_ZOK","SUPERIOR")

Return oView