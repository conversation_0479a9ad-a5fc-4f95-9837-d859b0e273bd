#Include 'TOTVS.ch'

/*/{Protheus.doc} TCRME029
Validar o usuario tem acesso a rotina.
<AUTHOR> | <PERSON>
@version   1.xx
@since     08/11/2021
/*/
User Function TCRME029()
	Local clQuery	:= "" 
	Local clAlias	:= GetNextAlias()
	Local clRet		:= ""
    Local lRet		:= .F.
    Local cCodUsr   := RetCodUsr()
	
    
	clQuery += " SELECT ZX5_CHAVE2  "
	clQuery += " FROM " + RetSqlName("ZX5") + " ZX5 "
	clQuery += " WHERE ZX5.ZX5_FILIAL = '" + xFilial("ZX5") + "' "
	clQuery += " AND ZX5.ZX5_TABELA = 'WEBORC' "
	clQuery += " AND ZX5.ZX5_CHAVE2 = '" + cCodUsr + "' "
	clQuery += " AND ZX5.D_E_L_E_T_ = ' ' "

	dbUseArea( .T., "TOPCONN", TCGENQRY(,, clQuery), clAlias, .F., .T.) 

	While (clAlias)->(!EOF())

		If !Empty(clRet)
			clRet += "/"+alltrim((clAlias)->ZX5_CHAVE2)
		Else
			clRet += alltrim((clAlias)->ZX5_CHAVE2)
		EndIF

		(clAlias)->(dbSkip())
	EndDO
    
    lRet := !Empty(clRet)

	(clAlias)->(dbCloseArea())

Return(lRet)
