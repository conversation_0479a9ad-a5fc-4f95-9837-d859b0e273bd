#Include "Protheus.ch"
#Include "FwMVCDef.ch"

/*/{Protheus.doc} Ft600Tp09

Ponto de entrada responsável por inicializar a variável estática do padrão aTipo09 para 
trazer os valores corretos dos parcelamentos gravados na tabela PKI.

@sample    Ft600Tp09( oModel ) 

@param 		ExpO = Objeto do modelo de dados da proposta

@return	aTipo09 = Array com o retorno das informações, sendo: 
				Pos 1 = Vazio (pois corresponde ao CK_PRODUTO) 
				Pos 2 = Data de vencimento (PKI_DATA)
				Pos 3 = Valor da parcela (PKI_VALOR)
				Pos 4 = Vazio (pois corresponde ao CJ_CONDPAG)
				Pos 5 = Item (PKI_ITEM)
				Pos 6 = Folder (PKI_FOLDER)

<AUTHOR> Villa 
@since      27/10/2015 
@version    P12.1.7
/*/
//-----------------------------------------------------------------------------------------
User Function Ft600Tp09()

Local aCond09 	:= {}
Local aArea		:= GetArea()
Local oModel 	:= PARAMIXB[1]

dbSelectArea("AZS")
dbSetOrder(3)

If oModel:GetOperation() <> 3	// Inclusao
	DbSelectArea( "PKI" )
	PKI->( DbSetOrder( 1 ) ) //PKI_FILIAL+PKI_PROPOS+PKI_PREVIS+PKI_FOLDER+PKI_ITEM+PKI_PARCEL
	ADZ->( DbSetOrder( 3 ) ) //ADZ_FILIAL+ADZ_PROPOS+ADZ_REVISA+ADZ_FOLDER+ADZ_ITEM
	If PKI->( DbSeek( xFilial( "PKI" ) + ADY->ADY_PROPOS + ADY->ADY_PREVIS ) )
		While PKI->( !Eof() ) .And. PKI->PKI_PROPOS + PKI->PKI_PREVIS  ==  ADY->ADY_PROPOS + ADY->ADY_PREVIS 
			ADZ->(dbSeek(xFilial("ADZ")+PKI->PKI_PROPOS+PKI->PKI_PREVIS+PKI->PKI_FOLDER+PKI->PKI_ITEM))
			aAdd( aCond09, { ADZ->ADZ_PRODUT, PKI->PKI_DATA , PKI->PKI_VALOR, ADZ->ADZ_CONDPG , PKI->PKI_ITEM, PKI->PKI_FOLDER } )
			PKI->( DbSkip() )
		EndDo 
	EndIf
EndIf

RestArea( aArea )

//-----------------------------------------------------------
// Limpa a referência do array na memória
//-----------------------------------------------------------
aArea := ASize( aArea, 0 ) 
aArea := Nil 

Return( aCond09 )
