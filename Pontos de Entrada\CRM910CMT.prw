#Include 'Protheus.ch'

User Function CRM910CMT()
Local oModel 	:= ParamIXB[1]
Local oMdlPKG := oModel:GetModel("FOL_PKG")
Local oMdlAOM := oModel:GetModel("AOMDETAIL")
Local nI
Local cField	:= ""

If Empty(oMdlPKG:GetValue("PKG_TPVEND"))
	Return
EndIf

If PKG->(DBSEEK(xFilial("PKG") + oMdlAOM:GetValue("AOM_CODAGR") + oMdlAOM:GetValue("AOM_CODNIV"))) 

	Reclock("PKG",.F.)
	For nI := 1 to Len(oMdlPKG:aDataModel[1])
		cField  := Alltrim(oMdlPKG:aDataModel[1,nI,1])
		If !(cField $ "PKG_CODAGR/PKG_CODNIV/PKG_FILIAL")
			PKG->(FieldPut(FieldPos(cField)),oMdlPKG:GetValue(cField))
		EndIf
	Next nI
	PKG->(MSUNLOCK())

ELSE

	Reclock("PKG",.T.)
	For nI := 1 to Len(oMdlPKG:aDataModel[1])

		cField  := Alltrim(oMdlPKG:aDataModel[1,nI,1])
	
		If cField=="PKG_FILIAL"
			PKG->PKG_FILIAL	:= xFilial("PKG")
		ElseIf cField=="PKG_CODAGR"
			PKG->PKG_CODAGR 	:= oMdlAOM:GetValue("AOM_CODAGR")
		ElseIf cField=="PKG_CODNIV"
			PKG->PKG_CODNIV 	:= oMdlAOM:GetValue("AOM_CODNIV")
		Else
			PKG->(FieldPut(FieldPos(cField)),oMdlPKG:GetValue(cField))
		EndIf		

	Next nI
	PKG->(MSUNLOCK())

ENDIF

Return

