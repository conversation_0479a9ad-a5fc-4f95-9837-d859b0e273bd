#Include "Totvs.Ch"
#Include "RESTFUL.Ch"
#Include 'Protheus.ch'

Static cEmail := ""

/*/
@Function:	TCFGW001
@version: 	1.00
@since: 	17/01/2020
/*/

User Function TCFGW001()
Return

/*/
    @Method:	GetAllUsers
    @desc:		Metodo responsavel por retornar todos usuarios do protheus
    <AUTHOR> Menabue Lima
    @since 08/01/2020
   @version 1.0
/*/

WsRestFul GETALLUSERS  Description "Lista Todos Usuarios Do Protheus"
WSDATA email AS CHARACTER 
     WsMethod Get Description "Lista Todos Usuarios Do Protheus" WsSyntax "/GETALLUSERS/{email}"
		 
End WsRestFul

WsMethod Get WsReceive email WsService GETALLUSERS

Local email   :=""
Local nLinha    := 1
Local cJson     := ""
Local aAllusers := FWSFALLUSERS()
Local nPosRes   := 0
Local nI        := 0
    // ----------------------------------
	// Define o tipo de retorno do metodo
    // ----------------------------------

	::SetContentType("application/json")

    If Len(::aURLParms) > 0
        email  := ::aURLParms[1]
     Else
        email  := IIf( ::email == Nil, "" , ::email ) 
    EndIf
    conout("email->"+email)
 nPosRes   :=aScan(aAllusers, {|x| Lower(AllTrim(x[5])) == lower(Alltrim(email)) })
 conout("ascan->"+cValToChar(nPosRes))
if (nPosRes > 0)
    cJson := '{'
    cJson += '"usuarios":['
    cJson += '{'
    cJson += '"codigo":"' + AllTrim(aAllusers[nPosRes][2])  + '",'
    cJson += '"nome":"'  + AllTrim(aAllusers[nPosRes][4]) + '",'
    cJson += '"email":"'  + AllTrim(aAllusers[nPosRes][5]) + '"'
    cJson += '}'
    cJson += ']'
    cJson += '}'  
Else 
     if Alltrim(lower(email)) $ "all"
        conout("ascan->"+cValToChar(nPosRes))
        For nI:= 1 To Len(aAllusers)
                If nI == 1
                        cJson := '{'
                        cJson += '"usuarios":['
                EndIf      
                cJson += '{'
                cJson += '"codigo":"' + AllTrim(aAllusers[nI][2])  + '",'
                cJson += '"nome":"'  + AllTrim(aAllusers[nI][4]) + '",'
                cJson += '"email":"'  + AllTrim(aAllusers[nI][5]) + '"'
                cJson += '},'
        Next nI
        If Empty(cJson)
            cJSON := RetJson("Sem registros localizados", "Nao encontrado registros com o filtro selecionado")
        Else
            cJson := Left(cJson, RAT(",", cJson) - 1)
            cJson += ']'
            cJson += '}'        
        EndIf
    Else
        cJSON := RetJson("Usuario nao localizado", "Nao encontrado registros com o filtro selecionado")  
        
    EndIF
EndIF       
   


    Self:SetResponse( cJson )

Return(.T.)

/*/{Protheus.doc} GetUserGroups
   Metodo responsavel por listar todos os grupos do usuario
    <AUTHOR> Menabue Lima
    @since 08/01/2020
    @version 1.0
    
/*/
 
 
WsRestFul GetUserGroups  Description "Lista todos os grupos do usuario"
WSDATA id AS CHARACTER 
WsMethod Get Description "Lista todos os grupos do usuario" WsSyntax "/GetUserGroups/{id}"
		 
End WsRestFul

WsMethod Get WsReceive id WsService GetUserGroups

Local cId   :=""
Local nLinha    := 1
Local cJson     := ""
Local nPosRes   := 0
Local cAli      :=""
    // ----------------------------------
	// Define o tipo de retorno do metodo
    // ----------------------------------

	::SetContentType("application/json")

    If Len(::aURLParms) > 0
        cId  := ::aURLParms[1]
     Else
        cId  := IIf( ::id == Nil, "" , ::id ) 
    EndIf
 
cAli:=GetGroups(cID)

While !(cAli)->(Eof())
    If nLinha == 1
        cJson := '{'
        cJson += '"grupos":['
    EndIf      
    cJson += '{'
    cJson += '"id":"' + AllTrim((cAli)->GR__ID)  + '",'
    cJson += '"codigo":"'  +AllTrim((cAli)->GR__CODIGO) + '",'
    cJson += '"descricao":"'  + AllTrim((cAli)->GR__NOME)+ '"'
    cJson += '},'
     nLinha++               
 (cAli)->(DbSkip())
EndDo

    If Empty(cJson)
            cJson := RetJson("Sem registros localizados", "Nao encontrado registros com o filtro selecionado")
        Else
            cJson := Left(cJson, RAT(",", cJson) - 1)
            cJson += ']'
            cJson += '}'        
    EndIF    

(cAli)->(dbCloseArea())   
Self:SetResponse( cJson )

Return(.T.)

/*/{Protheus.doc} GetUserGroups
   Metodo responsavel por acessar o SCRIM USERS e Gravar o LOG
    <AUTHOR> Menabue Lima
    @since 10/02/2020
    @version 1.0
     /*/
 

WsRestFul ChangeUsers Description "Altera o Usuario usando a API do PAdrao User, nesta API Temos controle de LOG's"

WsData id		As String
WsData email		As String
WsMethod PUT Description "Altera o Usuario usando a API do PAdrao User" WsSyntax "/changeUsers/{id}/{email}"

End WsRestFul

WsMethod PUT WsReceive id, email WsService ChangeUsers

Local cBody	    := IIf( Empty( Self:GetContent() ), '', Self:GetContent() )
Local cUrl      := Alltrim( SuperGetMV("TI_CFG001U", ,"http://************:8041/users/") )
Local cUrlSrv   := cUrl+IIf( Empty(Self:id ), '', Self:id ) // "http://************:8041/users/"+id
Local cHttpRet  := Nil  
Local cRet      := Nil
Local cErro     := ""
Local nHttp     := 0 
Local cLog      := ""
Local oJson     := JsonObject():new()
Local uJson     := oJson:fromJson(cBody)   


if ! (ValType(uJson) == "U")
    self:setResponse(uJson)
    self:setStatus(503)
    Return  .T.
    
EndIF
cLog        += "["
cLog        += '{"solicitante":"'+Self:email+'" },'
//Pega Status do Usuario Anterior
cRet    := Httpquote(cUrlSrv,"GET", "", cBody, ,{"Content-Type: application/json"},@cHttpRet)
//conout(cRet)
If (ValType(cRet) == "U")
    cRet :=""
    self:setResponse("Tente novamente mais tarde")
    self:setStatus(503)
    Return(.T.)
Else 
 cLog    += cRet
    nHttp := HTTPGetStatus(@cErro) 
    If nHttp != 200
      self:setResponse(cErro)
      self:setStatus(503)
    Else

    EndIF
EndIf	

//Alterar usuario conforme requisição principal
cHttpRet    := Nil
cRet        := Httpquote(cUrlSrv,"PUT", "", cBody, ,{"Content-Type: application/json"},@cHttpRet)
cLog        += ","
//conout(cRet)
If (ValType(cRet) == "U")
    self:setResponse("Tente novamente mais tarde")
    self:setStatus(503)
Else 
cLog        += cRet
    nHttp := HTTPGetStatus(@cErro) 
    If nHttp != 200
        self:setResponse(cErro)
        self:setStatus(503)
    Else
        cLog        += "]"
        //Grava os Logs antes e apos alteracao
        GrvLog(cLog,Self:id)
    EndIF
EndIf	

Self:SetResponse( cRet )
Return(.T.)

WsRestFul GetUsers Description "Pega os dados do Usuario no Protheus"

WsData id		As String
WsMethod GET Description "Pega os dados do Usuario no Protheus" WsSyntax "/GetUsers?id={id}"

End WsRestFul

WsMethod GET WsReceive id WsService GetUsers

//Local cBody	    := IIf( Empty( Self:GetContent() ), '', Self:GetContent() )
Local cUrl      := Alltrim( SuperGetMV("TI_CFG001U", ,"http://************:8041/users/") )
Local cUrlSrv   := cUrl+IIf( Empty(Self:id ), '', Self:id ) // "http://************:8041/users/"+id
Local cHttpRet  := Nil  
Local cRet      := Nil
Local cErro     := ""
Local nHttp     := 0 

ConOut("acessou")

//Pega Status do Usuario Anterior
cRet    := Httpquote(cUrlSrv,"GET", "", "", ,{"Content-Type: application/json"},@cHttpRet)
//conout(cRet)
If (ValType(cRet) == "U")
    cRet :=""
    self:setResponse("Tente novamente mais tarde")
    self:setStatus(503)
    Return(.T.)
Else 

    nHttp := HTTPGetStatus(@cErro) 
    If nHttp != 200
      self:setResponse(cErro)
      self:setStatus(503)
    Else

    EndIF
EndIf	
 
Self:SetResponse( cRet )
Return(.T.)
/*/{Protheus.doc} GetUserRules
   Metodo responsavel por Lista todos as regras dos grupos do usuario
    <AUTHOR>
    @since 08/01/2020
    @version version
    @param param_name, param_type, param_descr
    @return return_var, return_type, return_description
/*/
 
 
WsRestFul GetUserRules  Description "Lista todos as regras dos grupos do usuario" 
WSDATA id AS CHARACTER 
WSDATA idgrupo AS CHARACTER OPTIONAL

WsMethod Get Description "Lista todos as regras dos grupos do usuario" WsSyntax "/GetUserRules/{id}/{idgrupo}"
		 
End WsRestFul

WsMethod Get WsReceive id, idgrupo WsService GetUserRules

Local cId       :=""
Local cGrupo    :=""
Local nLinha    := 1
Local cJson     := ""
Local nPosRes   := 0
Local cAli      :=""
Local cGrupo    :=""
    // ----------------------------------
	// Define o tipo de retorno do metodo
    // ----------------------------------

	::SetContentType("application/txt")
    If Len(::aURLParms) > 0
      if Valtype(::aURLParms[1]) <>"U"
         cId      := ::aURLParms[1]
      EndIF 
      IF Len(::aURLParms) ==2
         cGrupo   := ::aURLParms[2]
       EndIF
     Else
        cId     := IIf( ::id == Nil, "" , ::id ) 
        cGrupo  := IIf( ::idgrupo == Nil, "" , ::idgrupo ) 
    EndIf
cId    :=AllTrim(cId)
cGrupo :=AllTrim(cGrupo)
cAli   :=GetRules(cID,cGrupo)
IF !Empty(cId)
    While !(cAli)->(Eof())
        If nLinha == 1
            cJson := '{'
            cJson += '"grupos":['
        EndIf      
    //Verifica se mudou de Grupo 
        if cGrupo <> AllTrim((cAli)->GR__CODIGO)
        cGrupo:=AllTrim((cAli)->GR__CODIGO)
        If nLinha <> 1
                cJson := Left(cJson, RAT(",", cJson) - 1)
                cJson += ']'
                cJson += '},'
            EndIF
            cJson += '{'
            cJson += '"id":"' + AllTrim((cAli)->GR__ID)  + '",'
            cJson += '"codigo":"'  +AllTrim((cAli)->GR__CODIGO) + '",'
            cJson += '"descricao":"'  + AllTrim((cAli)->GR__NOME)+ '",'
            cJson += '"privilegios":['
        EndIF
            cJson += '{'
            cJson += '"id":"' + AllTrim((cAli)->RL__ID)  + '",'
            cJson += '"codigo":"'  +AllTrim((cAli)->RL__CODIGO) + '",'
            cJson += '"descricao":"'  + AllTrim((cAli)->RL__DESCRI)+ '",'
            cJson += '},'
            nLinha++               
    (cAli)->(DbSkip())
    EndDo
  If !Empty(cJson)   
    cJson := Left(cJson, RAT(",", cJson) - 1)
    cJson += ']},'
  EndIF 
EndIF
If Empty(cJson) 
    cJson := RetJson("Sem registros localizados", "Nao encontrado registros com o filtro selecionado")
    Else
    cJson := Left(cJson, RAT(",", cJson) - 1)
    cJson += ']'
    cJson += '}'        
EndIF    

(cAli)->(dbCloseArea())   
Self:SetResponse( cJson )

Return(.T.)
/*/{Protheus.doc} GrvLog
    Funcao responsavel pela gravação dos logs na tabela CV8
    @type  Static Function
    <AUTHOR> Menabue Lima
    @since 11/02/2020
    @version 1.0

/*/
Static Function GrvLog(cLogs,cIDUser)
    ProcLogIni( {},"TCFGW001" )
	ProcLogAtu("INICIO", "Rotina de alteração de usuarios")
	ProcLogAtu("MENSAGEM",cIDUser+" Usuario alterado com sucesso",cLogs)
	ProcLogAtu("FIM", "Rotina de alteração de usuarios")
Return 

/*
@Method:	RetJson
@desc:		Metodo Generico de retorno de json
@author:	Mauricio Madureira
@version: 	1.00
- 1 = operaÃ§Ã£o realizada com sucesso;
- 2 = operaÃ§Ã£o nÃ£o realizada e seguida do atributo com a mensagem informando o motivo;
- 3 = validaÃ§Ã£o de  dados e seguida do atributo mensagem informando qual a validaÃ§Ã£o que deve ser feita. 
@since: 	20/09/2018
*/
Static Function RetJson(cRet, cMsg, cDetail)

Default cDetail := ""

cJSON := '{'
cJSON += '"code":"'+ cRet +  '",'
cJSON += '"message":"' + cMsg + '",'
cJSON += '"detailedMessage":"' + cMsg + '"'
cJSON += '}'

Return cJson


/*/{Protheus.doc} GetGroups
    Busca o Grupo do usuario selecionado
    @type  Static Function
    <AUTHOR> Menabue Lima
    @since 08/01/2020
    @version 1.0
/*/
Static Function GetGroups(cID)
Local cNextCl	:= 	GetNextAlias()
Local cQuery    := ""

cQuery +=" SELECT USR_NOME, USR_EMAIL, USR_GRUPO, B.GR__CODIGO, B.GR__ID, B.GR__NOME "
cQuery +=" FROM SYS_USR_GROUPS A, SYS_GRP_GROUP B, SYS_USR C "
cQuery +=" WHERE A.USR_GRUPO = B.GR__ID "
cQuery +=" AND A.USR_ID = C.USR_ID "
cQuery +=" AND A.USR_ID ='"+cID+"'"
cQuery +=" AND A.D_E_L_E_T_ = ' ' "
cQuery +=" AND B.D_E_L_E_T_ = ' ' "
cQuery +=" AND C.D_E_L_E_T_ = ' ' "
    
IF SELECT(cNextCl) > 0
	(cNextCl)->(DBCLOSEAREA())
ENDIF
DbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQuery), cNextCl , .F., .T.)


Return cNextCl

/*/{Protheus.doc} GetRules
    Busca as regras dos grupos dos usuarios
    @type  Static Function
    <AUTHOR> Menabue Lima
    @since 08/01/2020
    @version 1.0
/*/
Static Function GetRules(cID,cIdGrp)
Local cNextCl	:= 	GetNextAlias()
Local cQuery    := ""

cQuery +=" SELECT DISTINCT USR_NOME, USR_EMAIL, USR_GRUPO, B.GR__CODIGO, B.GR__ID, B.GR__NOME,E.RL__CODIGO, E.RL__DESCRI,E.RL__ID "
cQuery +=" FROM SYS_USR_GROUPS A, SYS_GRP_GROUP B, SYS_USR C,  SYS_RULES_GRP_RULES D, SYS_RULES E "
cQuery +=" WHERE  A.USR_ID ='"+cID+"'"
If !Empty(cIdGrp)
    cQuery +="  AND A.USR_GRUPO ='"+cIdGrp+"'" 
EndIf

cQuery +=" AND A.USR_GRUPO = B.GR__ID  "
cQuery +=" AND A.USR_ID = C.USR_ID "
cQuery +=" AND A.USR_GRUPO = D.GROUP_ID "
cQuery +=" AND D.GR__RL_ID = E.RL__ID "
cQuery +=" AND A.D_E_L_E_T_ = ' ' "
cQuery +=" AND B.D_E_L_E_T_ = ' ' "
cQuery +=" AND C.D_E_L_E_T_ = ' ' "
cQuery +=" AND D.D_E_L_E_T_ = ' ' "
cQuery +=" AND E.D_E_L_E_T_ = ' ' "
cQuery +=" ORDER BY USR_GRUPO "
    
IF SELECT(cNextCl) > 0
	(cNextCl)->(DBCLOSEAREA())
ENDIF
DbUseArea( .T., 'TOPCONN', TCGENQRY(,,cQuery), cNextCl , .F., .T.)


Return cNextCl