#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"
/*/{Protheus.doc} User Function TIGESUP1
Update para criacao em Producao do campo STAMP nas tabelas abaixo
    @type  Function
    <AUTHOR> Menabue Lima
    @since 16/09/2021
    @version 1.0
/*/

User Function TIGESUP1()
	Local cRet	 := ""
	Local c_Emp  := Space(2)
	Local c_Fil	 := Space(11)
	Local c_Table:= Space(3)
	Local aBoxParam	:= {}
	Local aRetParam	:= {}
	RpcSetEnv( "00", "00001000100")
	RpcSetType(3)
	Aadd(aBoxParam, {1, "Empresa"	, c_Emp	  , "@!'"	, ""," " , ".T.", 50, .T.})
	Aadd(aBoxParam, {1, "Filial"	, c_Fil   , "@!"	, ""," " , ".T.", 60, .T.})

	If  ParamBox(aBoxParam, "Criar INSDT e STAMP em qual empresa?", @aRetParam)  // Se for Re-processamento Individual, nao exibe a tela Parambox
		RPCClearEnv()
		RpcSetEnv( aRetParam[1], aRetParam[2])
		RpcSetType(3)
		cRet := TCConfig( "SETUSEROWSTAMP=ON" )
		cRet := TCConfig( "SETAUTOSTAMP=ON" )
		cRet := TCConfig( "SETUSEROWINSDT=ON" )
		cRet := TCConfig( "SETAUTOINSDT=ON" )

		While MsgYesNo("Deseja criar em mais outra tabela?", "Criacao Campos")
			aBoxParam:={}
			aRetParam	:= {}
			Aadd(aBoxParam, {1, "Tabela "	, c_Table , "@!"	, ""," " , ".T.", 60, .T.})
			If  ParamBox(aBoxParam, "Criar INSDT e STAMP", @aRetParam)  // Se for Re-processamento Individual, nao exibe a tela Parambox
				creatField(aRetParam[1])
			EndIF
		Enddo
		cRet := TCConfig( "SETUSEROWINSDT=OFF" )
		cRet := TCConfig( "SETAUTOINSDT=OFF" )
		cRet := TCConfig( "SETUSEROWSTAMP=OFF" )
		cRet := TCConfig( "SETAUTOSTAMP=OFF" )
		Alert("Processamento finalizado", "Termino")
	EndIF
RETURN


/*/{Protheus.doc} creatField
	Chama para criacao do Stamp
	@type    Function
	<AUTHOR> Menabue Lima
	@since 30/08/2022
	@version 12.1.33
 /*/
Static Function creatField(cTable)

	If Select(cTable) > 0
		(cTable)->(DbCloseArea())
	EndIf
	dbUseArea(.T.,"TOPCONN",RetSqlName(cTable), cTable)
Return
