#include    "tlpp-core.th"

namespace   Backoffice.Configurador.Gestao_Usuarios.Utils.ExtractArrayErrorFromModelObjectUtils

/*/{Protheus.doc} ExtractArrayErrorFromModelObject
This class aims to extract the array with the message
of error from oUser object.
@type class
@version  1.0
<AUTHOR>
@since 11/26/2024
/*/
Class   ExtractArrayErrorFromModelObject
    Static  Method  extract(oUser   As  Object) As  Array
EndClass

/*/{Protheus.doc} ExtractArrayErrorFromModelObject::extract
This method method aims to extract the message of error
and return any array {.F. , message of error}
@type method
@version  1.0
<AUTHOR>
@since 11/26/2024
@param oUser, object, Object User from Model
@return array, The array with message of error
/*/
Method  extract(oUser   As  Object) As  Array   Class   ExtractArrayErrorFromModelObject
    Local   aRetFun                 :=  {}  As  Array
    Local   aError                  :=  {}  As  Array
    Local   MODEL_MSGERR_MESSAGE	:=  6   As Numeric

    aError := oUser:GetErrorMessage()
    aRetFun := {.F.,AllTrim( aError[MODEL_MSGERR_MESSAGE])}

Return  aRetFun
