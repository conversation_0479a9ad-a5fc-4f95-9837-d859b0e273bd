# Script Sonar

Script Sonar é um script feito em Node para salvar localmente três informações do sonar para um determinado projeto: o quality gate, issues e o caminho dos arquivos que geraram as issues.

## Instalação

Faça a instalação do [Node 14.1.6](https://nodejs.org/dist/v14.16.1/).

Acesse o diretorio onde está o conteúdo do projeto e execute o comando

```bash
npm install
```

## Utilização
Configurar o arquivo .env com a chave do projeto e credenciais de acesso ao sonar.

Executar o comando dentro do diretório do script.
```
npm run start
```

## Contribuição
Pull requests são bem vindos. Para mundaças importantes, favor abrir primeiro uma issue para que se possa ser discutido o que se quer mudar.


## Licenca
[MIT](https://choosealicense.com/licenses/mit/)