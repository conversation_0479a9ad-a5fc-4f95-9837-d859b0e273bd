#include 'totvs.ch'
#include 'parmtype.ch'
#include 'topconn.ch'
#INCLUDE "Totvs.ch"
#INCLUDE "Restful.ch"

/*/{Protheus.doc} auditoria
WS Granting Access
<AUTHOR>
@since 29/03/2023
@version undefined
@type function
/*/
WsRestful auditoria Description "WS Auditoria Granting Access" FORMAT 'application/json,text/html'

	WSDATA fields               AS STRING	OPTIONAL
	WSDATA order			    AS STRING	OPTIONAL
	WSDATA page				    AS INTEGER	OPTIONAL
	WSDATA pageSize             AS INTEGER	OPTIONAL
    WSDATA filter               AS STRING	OPTIONAL
    WSDATA ano                  AS STRING	OPTIONAL

    WsMethod GET RD0 Description "Retorna dados dos Participantes";
        WSSYNTAX "/api/auditoria/v1/participante/?{fields, order, page, pageSize, filter} ";
        PATH '/api/auditoria/v1/participante';
        TTALK 'v1';
        PRODUCES APPLICATION_JSON
    
    WsMethod GET SA2 Description "Retorna dados dos Fornecedores";
        WSSYNTAX "/api/auditoria/v1/fornecedor/?{fields, order, page, pageSize, filter} ";
        PATH '/api/auditoria/v1/fornecedor';
        TTALK 'v1';
        PRODUCES APPLICATION_JSON
    
    WsMethod GET SYS_USR Description "Retorna dados dos Usuarios ";
        WSSYNTAX "/api/auditoria/v1/usuario/?{fields, order, page, pageSize, filter} ";
        PATH '/api/auditoria/v1/usuario';
        TTALK 'v1';
        PRODUCES APPLICATION_JSON
    
    WsMethod POST TVS Description "Retorna dados ";
        WSSYNTAX "/api/auditoria/v1/tvs/ ";
        PATH '/api/auditoria/v1/tvs';
        TTALK 'v1';
        PRODUCES APPLICATION_JSON


End WsRestful

/*/{Protheus.doc} GET
	Retorna dados de Participante
<AUTHOR>
@since 29/03/2023
@version undefined
@type function
/*/
WsMethod GET RD0 WsReceive fields, order, page, pageSize, filter WsService auditoria

    Local lRet          := .T.
    Local oResponse     := JsonObject():New()
    Local oDataBase     := nil
    Local cWhere        := ''
    Local cQuery        := ''
    Local aStruRD0      := RD0->(DBStruct())
    Local cErroBlk      := ''
    Local nX            := 0
    Local oException	:= ErrorBlock({|e| cErroBlk := + e:Description + e:ErrorStack, lRet := .F. })

    Default self:fields   := ''
    Default self:order    := 'RD0_CODIGO'
    Default self:page     := 1
    Default self:pageSize := 100
    Default self:filter   := ''

    Begin Sequence

        // self:pageSize := If(self:pageSize > 50, 50, self:pageSize)

        If Empty(self:fields)

            //Insere todos os campos da tabela
            aEval(aStruRD0, { | _aRD0 | self:fields += _aRD0[1] + ',' } )

            self:fields := Left(self:fields,Len(self:fields)-1) //Retira ultima virgula

        EndIf

        // Classe para consulta via Rest
        oDataBase := FWAdapterBaseV2():new( 'GET', .T. )
        oDataBase:setPage(self:page)
        oDataBase:setPageSize(self:pageSize)
        oDataBase:SetOrderQuery(self:order)
        oDataBase:SetUrlFilter({{'FILTER',self:filter}})
        oDataBase:SetFields( self:fields )

        // Mapa de campos retornaveis
        aEval(aStruRD0, { | _aRD0 | oDataBase:AddMapFields( _aRD0[1]   , _aRD0[1]  , .T., .F., gTpField(_aRD0[1]) ) } )

        // Dados da Query
        cQuery := " SELECT #QueryFields#"
        cQuery += " FROM " + RetSqlName( 'RD0' ) + " RD0 "
        cQuery += " WHERE #QueryWhere#"
        cWhere := " RD0_FILIAL = '"+ FWxFilial('RD0') +"' AND D_E_L_E_T_ = ' '"

        oDataBase:SetQuery( cQuery )
        oDataBase:SetWhere( cWhere )
        oDataBase:SetOrder( self:order )

        //Executa a consulta, se retornar .T. tudo ocorreu conforme esperado
        If oDataBase:Execute()

            // Gera o arquivo Json com o retorno da Query
            oDataBase:FillGetResponse()

        EndIf

    End Sequence

    ErrorBlock(oException)

    // Verifica errorBlock
    If lRet
        // Verifica execução da query
        If oDataBase:lOk
            oResponse:fromJson(oDataBase:getJSONResponse())
            For nX:= 1 to Len(oResponse["items"])
                oResponse["items"][nX]["rd0_nome"] := U_EspecMsg(oResponse["items"][nX]["rd0_nome"])
            Next
            self:SetResponse(oResponse:ToJson())
        Else
            oResponse['code'] := 2 // oDataBase:GetCode()
            oResponse['status'] := 400
            oResponse['message'] := 'Não foi possível realizar o filtro dos registros!'
            oResponse['detailedMessage'] := oDataBase:GetMessage()
            lRet := .F.
        EndIf
    Else
        oResponse['code'] := 001
        oResponse['status'] := 500
        oResponse['message'] := 'Aconteceu um erro inesperado no serviço!'
        oResponse['detailedMessage'] := cErroBlk
    EndIf

    If !lRet
        SetRestFault( oResponse['code'],;
                        U_EspecMsg(oResponse['message']),;
                        .T.,;
                        oResponse['status'],;
                        U_EspecMsg(oResponse['detailedMessage']);
                    )
    EndIf

    FreeObj(oDataBase)
    FreeObj(oResponse)
    aFields := nil

Return lRet

/*/{Protheus.doc} GET
	Retorna dados de Fornecedor
<AUTHOR>
@since 29/03/2023
@version undefined
@type function
/*/
WsMethod GET SA2 WsReceive fields, order, page, pageSize, filter WsService auditoria

    Local lRet          := .T.
    Local oResponse     := JsonObject():New()
    Local oDataBase     := nil
    Local cWhere        := ''
    Local cQuery        := ''
    Local aStruSA2      := SA2->(DBStruct())
    Local cErroBlk      := ''
    Local nX            := 0
    Local oException	:= ErrorBlock({|e| cErroBlk := + e:Description + e:ErrorStack, lRet := .F. })

    Default self:fields   := ''
    Default self:order    := 'A2_COD'
    Default self:page     := 1
    Default self:pageSize := 100
    Default self:filter   := ''

    Begin Sequence

        // self:pageSize := If(self:pageSize > 50, 50, self:pageSize)

        If Empty(self:fields)

            //Insere todos os campos da tabela
            aEval(aStruSA2, { | _aSA2 | self:fields += _aSA2[1] + ',' } )

            self:fields := Left(self:fields,Len(self:fields)-1) //Retira ultima virgula

        EndIf

        // Classe para consulta via Rest
        oDataBase := FWAdapterBaseV2():new( 'GET', .T. )
        oDataBase:setPage(self:page)
        oDataBase:setPageSize(self:pageSize)
        oDataBase:SetOrderQuery(self:order)
        oDataBase:SetUrlFilter({{'FILTER',self:filter}})
        oDataBase:SetFields( self:fields )

        // Mapa de campos retornaveis
        aEval(aStruSA2, { | _aSA2 | oDataBase:AddMapFields( _aSA2[1]   , _aSA2[1]  , .T., .F., gTpField(_aSA2[1]) ) } )

        // Dados da Query
        cQuery := " SELECT #QueryFields#"
        cQuery += " FROM " + RetSqlName( 'SA2' ) + " SA2 "
        cQuery += " WHERE #QueryWhere#"
        cWhere := " A2_FILIAL = '"+ FWxFilial('SA2') +"' AND D_E_L_E_T_ = ' '"

        oDataBase:SetQuery( cQuery )
        oDataBase:SetWhere( cWhere )
        oDataBase:SetOrder( self:order )

        //Executa a consulta, se retornar .T. tudo ocorreu conforme esperado
        If oDataBase:Execute()

            // Gera o arquivo Json com o retorno da Query
            oDataBase:FillGetResponse()

        EndIf

    End Sequence

    ErrorBlock(oException)

    // Verifica errorBlock
    If lRet
        // Verifica execução da query
        If oDataBase:lOk
            oResponse:fromJson(oDataBase:getJSONResponse())
            For nX:= 1 to Len(oResponse["items"])
                oResponse["items"][nX]["a2_nome"] :=  U_EspecMsg(oResponse["items"][nX]["a2_nome"])
            Next
            self:SetResponse(oResponse:ToJson())
        Else
            oResponse['code'] := 2 // oDataBase:GetCode()
            oResponse['status'] := 400
            oResponse['message'] := 'Não foi possível realizar o filtro dos registros!'
            oResponse['detailedMessage'] := oDataBase:GetMessage()
            lRet := .F.
        EndIf
    Else
        oResponse['code'] := 001
        oResponse['status'] := 500
        oResponse['message'] := 'Aconteceu um erro inesperado no serviço!'
        oResponse['detailedMessage'] := cErroBlk
    EndIf

    If !lRet
        SetRestFault( oResponse['code'],;
                        U_EspecMsg(oResponse['message']),;
                        .T.,;
                        oResponse['status'],;
                        U_EspecMsg(oResponse['detailedMessage']);
                    )
    EndIf

    FreeObj(oDataBase)
    FreeObj(oResponse)
    aFields := nil

Return lRet

/*/{Protheus.doc} GET
	Retorna dados de Usuario
<AUTHOR>
@since 29/03/2023
@version undefined
@type function
/*/
WsMethod GET SYS_USR WsReceive fields, order, page, pageSize, filter WsService auditoria

    Local lRet          := .T.
    Local oResponse     := JsonObject():New()
    Local oDataBase     := nil
    Local cWhere        := ''
    Local cQuery        := ''
    Local cAliasTrb	    := ''
    Local cColType      := ''
    Local aStruSYS_USR  := {}
    Local cErroBlk      := ''
    Local nX            := 0
    Local oException	:= ErrorBlock({|e| cErroBlk := + e:Description + e:ErrorStack, lRet := .F. })

    Default self:fields   := ''
    Default self:order    := 'USR_ID'
    Default self:page     := 1
    Default self:pageSize := 100
    Default self:filter   := ''

    Begin Sequence

        // self:pageSize := If(self:pageSize > 50, 50, self:pageSize)

        cAliasTrb := GetNextAlias()

        BeginSQL Alias cAliasTrb
            SELECT table_name, column_name, data_type, data_length
            FROM USER_TAB_COLUMNS
            WHERE table_name = 'SYS_USR'
        EndSql

        while (cAliasTrb)->(!eof())
            If !(alltrim((cAliasTrb)->column_name) $ 'D_E_L_E_T_,R_E_C_D_E_L_,USR_ULTSPSW')
                If alltrim((cAliasTrb)->data_type) == 'NUMBER'
                    cColType := 'N'
                ElseIf alltrim((cAliasTrb)->data_type) == 'DATE'
                    cColType := 'D'
                Else
                    cColType := 'C'
                EndIf
                AADD(aStruSYS_USR ,{alltrim((cAliasTrb)->column_name) ,cColType, (cAliasTrb)->data_length, 0})
            EndIf
            (cAliasTrb)->( dbSkip() )
        End
        
        If Select(cAliasTrb) > 0
            (cAliasTrb)->(dbCloseArea())
        Endif

        //Insere todos os campos da tabela
        If Empty(self:fields)

            aEval(aStruSYS_USR, { | _aSYS_USR | self:fields += _aSYS_USR[1] + ',' } )

            self:fields := Left(self:fields,Len(self:fields)-1) //Retira ultima virgula

        EndIf

        // Classe para consulta via Rest
        oDataBase := FWAdapterBaseV2():new( 'GET', .T. )
        oDataBase:setPage(self:page)
        oDataBase:setPageSize(self:pageSize)
        oDataBase:SetOrderQuery(self:order)
        oDataBase:SetUrlFilter({{'FILTER',self:filter}})
        oDataBase:SetFields( self:fields )

        // Mapa de campos retornaveis
        aEval(aStruSYS_USR, { | _aSYS_USR | oDataBase:AddMapFields( _aSYS_USR[1]   , _aSYS_USR[1]  , .T., .F., { _aSYS_USR[1], _aSYS_USR[2], _aSYS_USR[3], _aSYS_USR[4] } ) } )

        // Dados da Query
        cQuery := " SELECT #QueryFields#"
        cQuery += " FROM SYS_USR "
        cQuery += " WHERE #QueryWhere#"
        cWhere := " D_E_L_E_T_ = ' '"

        oDataBase:SetQuery( cQuery )
        oDataBase:SetWhere( cWhere )
        oDataBase:SetOrder( self:order )

        //Executa a consulta, se retornar .T. tudo ocorreu conforme esperado
        If oDataBase:Execute()

            // Gera o arquivo Json com o retorno da Query
            oDataBase:FillGetResponse()

        EndIf

    End Sequence

    ErrorBlock(oException)

    // Verifica errorBlock
    If lRet
        // Verifica execução da query
        If oDataBase:lOk
            oResponse:fromJson(oDataBase:getJSONResponse())
            For nX:= 1 to Len(oResponse["items"])
                oResponse["items"][nX]["usr_nome"] := U_EspecMsg(oResponse["items"][nX]["usr_nome"])
            Next
            self:SetResponse(oResponse:ToJson())
        Else
            oResponse['code'] := 2 // oDataBase:GetCode()
            oResponse['status'] := 400
            oResponse['message'] := 'Não foi possível realizar o filtro dos registros!'
            oResponse['detailedMessage'] := oDataBase:GetMessage()
            lRet := .F.
        EndIf
    Else
        oResponse['code'] := 001
        oResponse['status'] := 500
        oResponse['message'] := 'Aconteceu um erro inesperado no serviço!'
        oResponse['detailedMessage'] := cErroBlk
    EndIf

    If !lRet
        SetRestFault( oResponse['code'],;
                        U_EspecMsg(oResponse['message']),;
                        .T.,;
                        oResponse['status'],;
                        U_EspecMsg(oResponse['detailedMessage']);
                    )
    EndIf
    
    FreeObj(oDataBase)
    FreeObj(oResponse)
    aFields := nil

Return lRet

/*/{Protheus.doc} POST
	Retorna dados
<AUTHOR>
@since 04/04/2023
@version undefined
@type function
/*/
WsMethod POST TVS WsService auditoria

    Local oAux          := JsonObject():New()
    Local cAlias        := GetNextAlias()
    Local cQuerySQL     := ''
    Local lRet          := .T.
    Local oResponse     := JsonObject():New()
    Local aFields       := {}
    Local aJson         := {}
    Local nX            := 0
    Local cErroBlk      := ''
    Local oException	:= ErrorBlock({|e| cErroBlk := + e:Description + e:ErrorStack, lRet := .F. })

    Default self:GetContent() := ''

    if empty(self:GetContent())
        SetRestFault(400, "Body obrigatório!")
        return .F.
    endif

    Begin Sequence
        
        cQuerySQL := ChangeQuery(self:GetContent())

        If Select(cAlias) > 0; (cAlias)->(dbCloseArea()); Endif  

        TCQUERY cQuerySQL NEW ALIAS ( cAlias )

        while (cAlias)->(!eof())
            
            oAux := JSonObject():New()

            for nX := 1 to (cAlias)->(FCount())
                oAux[Lower((cAlias)->(FieldName(nX)))] := U_EspecMsg( alltrim((cAlias)->(fieldGet(nX))) )
            next nX
            
            AADD(aJson, oAux)

            (cAlias)->( dbSkip() )
        end
        (cAlias)->(DBCloseArea())

    End Sequence

    ErrorBlock(oException)

    // Verifica errorBlock
    If lRet
        oResponse['items'] := aJson 
        self:SetResponse(oResponse:toJson())
    Else
        oResponse['code'] := 001
        oResponse['status'] := 500
        oResponse['message'] := U_EspecMsg('Aconteceu um erro inesperado no serviço!')
        oResponse['detailedMessage'] :=  U_EspecMsg(cErroBlk)
    EndIf

    If !lRet
        SetRestFault( oResponse['code'],;
                        U_EspecMsg(oResponse['message']),;
                        .T.,;
                        oResponse['status'],;
                        U_EspecMsg(oResponse['detailedMessage']);
                    )
    EndIf

    FreeObj(oAux)
    oAux := nil
    FreeObj(oResponse)
    aFields := nil
    aJson := nil

Return lRet

/*/{Protheus.doc} gTpField
Retorna estrutura do campo
@type function
@version 1/0
<AUTHOR>
@since 29/03/2023
@param cCampo, character, nome do campo SX3
@param cNovoNome, character, novo nome para o campo
@return array, estrutura { nome, tipo, tamanho, decimais } ou NIL se não encontrar
/*/
static function gTpField( cCampo, cNovoNome )
local   aReturn   := nil
local   aTmp      := TamSX3( cCampo )
default cNovoNome := cCampo

    if len( aTmp ) > 2
        aReturn := { cNovoNome, aTmp[3], aTmp[1], aTmp[2] }
    endif
return aReturn

/*/{Protheus.doc} gTpField
Retorna estrutura do campo
@type function
@version 1/0
<AUTHOR>
@since 31/03/2023
@param cCampo, character, nome do campo SX3
@param cNovoNome, character, novo nome para o campo
@return array, estrutura { nome, tipo, tamanho, decimais } ou NIL se não encontrar
/*/
static function GetField( cCampo, aFields )
Local nPos := Ascan(aFields,{ |x| x[1] == cCampo })
return iif(len(nPos) > 0, aFields[nPos], nil)
