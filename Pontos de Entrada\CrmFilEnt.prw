#Include 'Protheus.ch'

/*/{Protheus.doc} CrmFilEnt
Retorna segmentos para filtros do CRM
@type function
<AUTHOR>
@since 19/11/2015
@version 1.0
@return cFiltro, Filtro alterado
@example
(examples)
@see (links_or_references)
/*/
User Function CrmFilEnt()
Local aArea		:= GetArea()
Local aAreaSA3	:= SA3->(GetArea())
Local cAlias	:= ParamIxb[1]
Local lTipo		:= ParamIxb[2]
Local cFiltro	:= ParamIxb[3]
Local cCompl	:= ""

If cAlias $ GetMV("TI_ALIASFL",,"SA1/AD1/ADY/AOF")
	RestArea(aAreaSA3)
	RestArea(aArea)
	Return(cFiltro)
EndIf

dbSelectArea("SA3")
dbSetOrder(7)
If !dbSeek(xFilial("SA3")+__cUserID) .or. Empty(SA3->A3_CARGO) .or. !AliasInDic("PKJ")
	RestArea(aAreaSA3)
	RestArea(aArea)
	Return(cFiltro)
EndIf

dbSelectArea("SUM")
dbSetOrder(1)
If !dbSeek(xFilial("SUM")+SA3->A3_CARGO) .or. FieldPos("UM_XUSASEG")==0 .or. SUM->UM_XUSASEG <> "2"
	RestArea(aAreaSA3)
	RestArea(aArea)
	Return(cFiltro)
EndIf

dbSelectArea("PKJ")
dbSetOrder(1)
dbSeek(xFilial("PKJ")+SA3->A3_COD)

Do While !PKJ->(EoF()) .and. PKJ->(PKJ_FILIAL+PKJ_VEND)==xFilial("PKJ")+SA3->A3_COD

	If !Empty(PKJ->PKJ_CODSEG)
	
		If lTipo
			cCompl += Iif(Empty(cCompl),"",", ")
			cCompl += "'"+PKJ->PKJ_CODSEG+"'"
		Else
			cCompl += Iif(Empty(cCompl),""," .OR. ")
			cCompl += "AO4_XSEGME=='"+PKJ->PKJ_CODSEG+"'"
		EndIf
		
	EndIf
	
	PKJ->(dbSkip())
	
EndDo

If !Empty(cCompl)

	If lTipo
		cFiltro += " AND AO4_XSEGME IN ( "+cCompl+") " 
	Else
		cFiltro += " .AND. ( "+cCompl+" )" 
	EndIf 
	
EndIf

RestArea(aAreaSA3)
RestArea(aArea)

Return(cFiltro)