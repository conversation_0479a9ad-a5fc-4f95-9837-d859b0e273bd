#INCLUDE "TOTVS.CH"
#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "CRMDEF.CH"
#INCLUDE "TRYEXCEPTION.CH"

User Function TCRMX233(cPWJ_PRNUM)
    Local aArea := GetArea()
    Local aAreaPWJ := PWJ->(GetArea())
    Local cQry := ""
    Local cAliPWJ := ""
    Local oBody := JsonObject():New()

    Default cPWJ_PRNUM := ""

	cAliPWJ := GetNextAlias()

	cQry := " SELECT PWJ.R_E_C_N_O_ As Recno "
	cQry += " FROM " + RetSqlName("PWJ") + " PWJ "
	cQry += " WHERE PWJ.PWJ_FILIAL = '" + xFilial("PWJ") + "' "
	cQry += " AND PWJ.PWJ_PRNUM = '" + cPWJ_PRNUM + "' "
	cQry += " AND PWJ.D_E_L_E_T_ = ' ' "
	cQry += " ORDER BY Recno "
	cQry := ChangeQuery(cQry)

	dbUseArea(.T., "TOPCONN", TcGenQry(,,cQry), cAliPWJ, .T., .T.)

    If !(cAliPWJ)->(Eof())
        PWJ->(DbGoTo((cAliPWJ)->Recno))
        oBody:FromJson(PWJ->PWJ_JREC)
    EndIf

    (cAliPWJ)->(DbCloseArea())
    
    PWJ->(RestArea(aAreaPWJ))
    RestArea(aArea)
    
Return oBody    
