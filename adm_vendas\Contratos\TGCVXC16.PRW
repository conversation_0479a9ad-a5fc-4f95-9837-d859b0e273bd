#include "totvs.ch"

#xtranslate SomaAM(<cAM>, <nQ>) => eval({|x, y| cAM:= x, aA := Array(y), aeval(aA, {|| cAM := If(Right(cAM, 2) == "12", soma1(Left(cAM, 4)) + "01", soma1(cAM))}), cAM}, <cAM>, <nQ>)
#xtranslate TiraAM(<cAM>, <nQ>) => eval({|x, y| cAM:= x, aA := Array(y), aeval(aA, {|| cAM := If(Right(cAM, 2) == "01", Tira1(Left(cAM, 4)) + "12", Tira1(cAM))}), cAM}, <cAM>, <nQ>)
#xtranslate CmptoAM( <v1> )       => Right(<v1>, 4) + Left(<v1>, 2) 


User Function Tgcvxc16
Return


Class Tgcvxc16 
    Data aCroFat  as Array
	Data cCliente  
	Data cLoja
	Data nPH5VlFtTS
	Data nVlFatur
	Data nvlFatcl
	Data nvlBoncl
	Data nVlIncCl
	Data nVlCIncFat
	Data nvlTotBo
	Data nSldCanTot
	Data nVlTIncFat
	Data nVlTDevFat
	Data nPH5VlTFat  
	Data nPH5VlFats
	Data nPH5VlrFat
	Data nPH5SldCan
	Data nPH5VlBoni
	Data nPH5VlCare
	Data nPH5VlIncr
	Data nPH5VlReaj
	Data nPH5VlTran
	Data nPH5VlrTrc
	Data nPH5QtdCan
	Data nPH5VlCanc
	Data nPH5QtdRea
	Data nPH5VlReat

    Method New(aInfFat, cCliente, cLoja) Constructor
	Method CalcTotais()
	Method RetPh5(nLinha, cNameCpo)
    Method Destroy()
   
EndClass


Method New(aInfFat, cCliente, cLoja) Class Tgcvxc16

	::aCroFat    := aClone(aInfFat)
	::cCliente   := cCliente
	::cLoja      := cLoja

	::nPH5VlFtTS := 0
	::nVlFatur   := 0
	::nvlFatcl   := 0
	::nvlBoncl   := 0
	::nVlIncCl   := 0
	::nVlCIncFat := 0
	::nvlTotBo   := 0
	::nSldCanTot := 0
	::nVlTIncFat := 0
	::nVlTDevFat := 0
	::nPH5VlTFat := 0
	::nPH5VlFats := 0
	::nPH5VlrFat := 0
	::nPH5SldCan := 0
	::nPH5VlBoni := 0
	::nPH5VlCare := 0
	::nPH5VlIncr := 0
	::nPH5VlReaj := 0
	::nPH5VlTran := 0
	::nPH5VlrTrc := 0
	::nPH5QtdCan := 0
	::nPH5VlCanc := 0
	::nPH5QtdRea := 0
	::nPH5VlReat := 0
Return

Method CalcTotais() Class Tgcvxc16
	Local ni         := 0
	Local nPerDev	 := 0
	Local nVlDevS    := 0
	Local nVlFats    := 0 
	Local nVlrFat    := 0
	Local nSldCan    := 0
	Local nVlBoni    := 0
	Local nVlCare    := 0
	Local nVlIncr    := 0
	Local nVlReaj    := 0
	Local nVlTran    := 0
	Local nVlrTrc    := 0
	Local nQtdCan    := 0
	Local nVlCanc    := 0
	Local nQtdRea    := 0
	Local nVlReat    := 0
	Local nVlNccInc  := 0  
	Local nVlFatot   := 0   
	Local nPerRat    := 0   


	For ni:=1 to Len(::aCroFat)
		nPerDev	:= 0
		nVlDevS := 0

		nVlFats := Self:RetPH5(ni, "PH5_VLFATS")
		nVlrFat := Self:RetPH5(ni, "PH5_VLRFAT")
		nSldCan := Self:RetPH5(ni, "PH5_SLDCAN")
		nVlBoni := Self:RetPH5(ni, "PH5_VLBONI")
		nVlCare := Self:RetPH5(ni, "PH5_VLCARE")
		nVlIncr := Self:RetPH5(ni, "PH5_VLINCR")
		nVlReaj := Self:RetPH5(ni, "PH5_VLREAJ")
		nVlTran := Self:RetPH5(ni, "PH5_VLTRAN")
		nVlrTrc := Self:RetPH5(ni, "PH5_VLRTRC")
		nQtdCan := Self:RetPH5(ni, "PH5_QTDCAN")
		nVlCanc := Self:RetPH5(ni, "PH5_VLCANC")
		nQtdRea := Self:RetPH5(ni, "PH5_QTDREA")
		nVlReat := Self:RetPH5(ni, "PH5_VLREA" )

		::nPH5VlTFat  += nVlrFat - nSldCan

		If nSldCan > 0
			::nSldCanTot  += nSldCan
			nPerDev	:= nSldCan  / nVlrFat
			nVlDevS := nVlFatS * nPerDev
			nVlNccInc := nVlIncr - (nVlIncr * nPerDev)
			::nVlTIncFat += nVlNccInc
		Else
			::nVlTIncFat += nVlIncr
		EndIf
		::nVlTDevFat += nSldCan
		::nvlTotBo += nVlBoni
		nVlFatot += nVlFatS - nVlDevS

		nPerRat    := Self:RetPH5(ni, "PH5_PERRAT" )
		If nPerRat == 0
			nPerRat := 100
		EndIf

		If !(::cCliente == Self:RetPH5(ni, "PH5_CLIENT" ) .And. ::cLoja == Self:RetPH5(ni, "PH5_LOJA" ))
			Loop
		EndIf

		::nVlFatur   += nVlFatS - nVlDevS
		::nvlFatcl   += nVlrFat - nSldCan
		::nvlBoncl   += nVlBoni - ( nVlBoni * nPerDev)
		::nVlIncCl   += nVlIncr 
		::nVlCIncFat += nVlIncr-nVlNccInc
	Next
	
	::nPH5VlFtTS := nVlFatot

Return

Method RetPH5(nLinha, cNameCpo) Class Tgcvxc16
    Local uRet := NIL    
    Local np   := 0

    np:= Ascan(::aCroFat[nLinha], {|x| x[1] == cNameCpo})

    uRet := ::aCroFat[nLinha, np, 2]

Return uRet

Method Destroy() Class Tgcvxc16

	aSize(::aCroFat, 0)
	::aCroFat := Nil
Return


