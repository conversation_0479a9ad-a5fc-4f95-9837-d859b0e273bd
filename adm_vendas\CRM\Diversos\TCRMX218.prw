//------------------------------------------------------------------------------
/*/{Protheus.doc} TCRMX218
Retorna os Tipos de Serviços da proposta comercial no X3_CBOX.
@sample 	TCRMX218()
@param		Nenhum
@Return   	cRet	, caracter, Status da proposta comercial.
<AUTHOR>
@since		10/03/2022
@version	12.1.27
/*/
//------------------------------------------------------------------------------
User Function TCRMX218()

Local cCombo  := ""
Local cIdioma := lower(GetPvProfString(GetEnvServer(),"RpoLanguage","",GetADV97())) 	//Idioma do RPO (Portuguese/Spanish)

DbSelectArea("PVX")
PVX->(DbSetOrder(1))
PVX->(DbGoTop())

    While PVX->(! Eof())
        cCombo += PVX->PVX_CODIGO+"="

        If cIdioma == 'spanish' .AND. !Empty(PVX->PVX_DESESP)
            cCombo += Alltrim(PVX->PVX_DESESP)+";"
        ElseIf cIdioma == 'english' .AND. !Empty(PVX->PVX_DESENG)
            cCombo += Alltrim(PVX->PVX_DESENG)+";"
        Else
            cCombo += Alltrim(PVX->PVX_DESCRI)+";"
        EndIf
        
        PVX->(DbSkip())
    EndDo

Return( cCombo )

