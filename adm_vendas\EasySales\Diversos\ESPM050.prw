#INCLUDE 'FWMVCDEF.CH'
#INCLUDE 'TOTVS.CH'

User Function ESPM050(cCodPais, cCodProp, cToken)

    Local aAlias        := {}
    Local aBackup       := {}
    Local aAceleradores := {}
    Local lRetAcel      := .F.

    Default cCodPais := "105"
    Default cCodProp := ""
    Default cToken   := "TOKENTEST"

    aAceleradores := RetAceleradores(cCodPais, cCodProp)

    If !Empty(aAceleradores)
        aAlias := PrepBase(aAceleradores, @aBackup)

        GeraBackup(aBackup, cToken)

        U_GeraSDF(aAlias, cToken)

        lRetAcel := .T.
    Else
        lRetAcel := ExistArq()
    EndIf
        
Return lRetAcel

Static Function RetAceleradores(cCodPais, cCodProp)
    Local aAreaPV7 := PV7->(GetArea())
    Local aAceleradores := {}
    Local oCampos
    Local oParametros
    Local oMarca
    Local lAcelerador   := .F.
    Local nDic          := 0
    Local aDic := { "SIX", "SX1", "SX2", "SX3", "SX5", "SX6",;
                    "SX7", "SX9", "SXA", "SXB", "SXD", "SXG",;
                    "SXQ", "SXR", "SXS", "XB3", "XXA", "XXG",;
                    "XXI", "XXK", "XXL", "XXM", "XXN", "XXO",;
                    "XXQ", "XXR", "XXS", "XXU"}
    
    PV7->( DbSetOrder(4) )
    If !PV7->(DbSeek(xFilial("PV7") + cCodPais + cCodProp))
        Return aAceleradores
    EndIf

    oCampos     := RetAceCampo(cCodPais, cCodProp)
    oParametros := RetAceParam(cCodPais, cCodProp)
    oMarca      := RetAceMarca(cCodPais, cCodProp)

    If !Empty(oCampos["aRegistros"])
        lAcelerador := .T.
    EndIf

    If !Empty(oParametros["aRegistros"])
        lAcelerador := .T.
    EndIf

    If lAcelerador

        For nDic := 1 to Len(aDic)
            oDic := RetDicStruct(aDic[nDic])
            AAdd(aAceleradores, oDic)
        Next

        aAceleradores[4]  := oCampos
        aAceleradores[6]  := oParametros
        aAceleradores[16] := oMarca
    EndIf

RestArea( GetArea() )
Return aAceleradores

Static Function RetAceCampo(cCodPais, cCodProp)

    Local cAliasAce   := ""
    Local oAcelerador := JsonObject():New()
    Local aRegistros  := {}

    cAliasAce := GetNextAlias()

    BeginSQL Alias cAliasAce
        SELECT PVK.PVK_CAMPO,
               PVK.PVK_ORDEM,
               PVK.PVK_DESCAM,
               PVK.PVK_PICTUR,
               PVK.PVK_VISUAL,
               PVK.PVK_RELACA,
               PVK.PVK_WHEN,
               PVK.PVK_VLDUSE,
               PVK.PVK_USADO,
               PVK.PVK_BROWSE,
               PVK.PVK_FOLDER,
               PVK.PVK_OBRIG,
               PVK.PVK_RESORI,
               PVK.PVK_USAORI
          FROM %Table:PV7% PV7
         INNER JOIN %Table:PVD% PVD
                 ON PVD_PAIS   = PV7.PV7_PAIS
                AND PVD_CODPRJ = PV7.PV7_CODPRJ
                AND PVD_VALIDA = '1'
                AND PVD.%NotDel%
         INNER JOIN %Table:PVQ% PVQ
                 ON PVQ.PVQ_PAIS   = PV7.PV7_PAIS
                AND PVQ.PVQ_CODPER = PVD.PVD_CODPRG
                AND CAST(PVQ.PVQ_COMBO AS VARCHAR) = (SELECT DISTINCT   CASE PVA.PVA_TIPRES   
                                                                        WHEN '3' THEN '1'
                                                                        WHEN '4' THEN '1'
                                                                        ELSE PVD.PVD_RESPUE END
																
														FROM %Table:PVA% PVA
														WHERE PVA.PVA_CODPRG = PVD.PVD_CODPRG
														AND PVA.PVA_PAIS = PV7.PV7_PAIS
														AND PVA.%NotDel%)
                AND PVQ.PVQ_TIPO   = '1'
                AND PVQ.%NotDel%
         INNER JOIN %Table:PVK% PVK
                 ON PVK.PVK_PAIS   = PV7.PV7_PAIS
                AND PVK.PVK_CODACE = PVQ.PVQ_CODACE
                AND PVK.%NotDel%
          WHERE PV7_PAIS       = %Exp:cCodPais%
            AND PV7.PV7_PROPOS = %Exp:cCodProp%
            AND PV7.%NotDel%
        UNION
        SELECT PVK.PVK_CAMPO,
               PVK.PVK_ORDEM,
               PVK.PVK_DESCAM,
               PVK.PVK_PICTUR,
               PVK.PVK_VISUAL,
               PVK.PVK_RELACA,
               PVK.PVK_WHEN,
               PVK.PVK_VLDUSE,
               PVK.PVK_USADO,
               PVK.PVK_BROWSE,
               PVK.PVK_FOLDER,
               PVK.PVK_OBRIG,
               PVK.PVK_RESORI,
               PVK.PVK_USAORI
          FROM %Table:PV7% PV7
         INNER JOIN %Table:PVD% PVD
                 ON PVD_PAIS   = PV7.PV7_PAIS
                AND PVD_CODPRJ = PV7.PV7_CODPRJ
                AND PVD_VALIDA = '1'
                AND PVD.%NotDel%
         INNER JOIN %Table:PVP% PVP
                 ON PVP.PVP_PAIS   = PV7.PV7_PAIS
                AND PVP.PVP_CODPER = PVD.PVD_CODPRG
                AND CAST(PVP.PVP_COMBO AS VARCHAR) = (SELECT DISTINCT   CASE PVA.PVA_TIPRES   
                                                                        WHEN '3' THEN '1'
                                                                        WHEN '4' THEN '1'
                                                                        ELSE PVD.PVD_RESPUE END
																
														FROM %Table:PVA% PVA
														WHERE PVA.PVA_CODPRG = PVD.PVD_CODPRG
														AND PVA.PVA_PAIS = PV7.PV7_PAIS
														AND PVA.%NotDel%)
                AND PVP.%NotDel%
         INNER JOIN %Table:PVO% PVO
                 ON PVO.PVO_PAIS   = PV7.PV7_PAIS
                AND PVO.PVO_CODGRP = PVP.PVP_CODGRP
                AND PVO.PVO_TIPO   = '1'
                AND PVO.%NotDel%
         INNER JOIN %Table:PVK% PVK
                 ON PVK.PVK_PAIS   = PV7.PV7_PAIS
                AND PVK.PVK_CODACE = PVO.PVO_CODACE
                AND PVK.%NotDel%
          WHERE PV7_PAIS       = %Exp:cCodPais%
            AND PV7.PV7_PROPOS = %Exp:cCodProp%
            AND PV7.%NotDel%
    EndSQL

    aRegistros := {}

    While (cAliasAce)->(!EoF())

        AAdd(aRegistros, JsonObject():New())
        aRegistros[Len(aRegistros)]["cChave"] := (cAliasAce)->PVK_CAMPO

        aAlteracao := {}

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_TITULO"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVK_DESCAM

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_ORDEM"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVK_ORDEM

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_PICTURE"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVK_PICTUR

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_VISUAL"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVK_VISUAL

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_RELACAO"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVK_RELACA

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_WHEN"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVK_WHEN

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_VLDUSER"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVK_VLDUSE

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_BROWSE"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVK_BROWSE

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_FOLDER"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVK_FOLDER

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_OBRIGAT"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVK_OBRIG
        
        cReserv := (cAliasAce)->PVK_RESORI
        cReserv := Subs(cReserv,1, 6) + If((cAliasAce)->PVK_OBRIG == "S", "x", " ") + Subs(cReserv,8)
        //cReserv := X3Reserv(cReserv)

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_RESERV"
        aAlteracao[Len(aAlteracao)]["Valor"] := cReserv
        
        cUso := FirstBitOff((cAliasAce)->PVK_USAORI)
        cUso := Subs(cUso,1, 99) + If((cAliasAce)->PVK_USADO == "S", "x", " ") + Subs(cUso,101)
        //cUso := Str2Bin(FirstBitOn(cUso))

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X3_USADO"
        aAlteracao[Len(aAlteracao)]["Valor"] := cUso

        aRegistros[Len(aRegistros)]["aAlteracao"] := aAlteracao

        (cAliasAce)->(DbSkip())
    End

    (cAliasAce)->(DbCloseArea())

    oAcelerador["cAlias"] := "SX3"
    oAcelerador["nIndex"] := 2
    oAcelerador["aRegistros"] := aRegistros

Return oAcelerador

Static Function RetAceParam(cCodPais, cCodProp)

    Local cAliasAce   := ""
    Local oAcelerador := JsonObject():New()
    Local aRegistros  := {}

    cAliasAce := GetNextAlias()

    BeginSQL Alias cAliasAce
        SELECT PVL.PVL_VAR,
                PVL.PVL_CONTEU,
                PVL.PVL_CONSPA,
                PVL.PVL_CONENG,
                PVL.PVL_DESVAR,
                PVL.PVL_DSCSPA,
                PVL.PVL_DSCENG,
                PVL.PVL_DESC1,
                PVL.PVL_DCSPA1,
                PVL.PVL_DCENG1,
                PVL.PVL_DESC2,
                PVL.PVL_DCSPA2,
                PVL.PVL_DCENG2
          FROM %Table:PV7% PV7
         INNER JOIN %Table:PVD% PVD
                 ON PVD_PAIS   = PV7.PV7_PAIS
                AND PVD_CODPRJ = PV7.PV7_CODPRJ
                AND PVD_VALIDA = '1'
                AND PVD.%NotDel%
         INNER JOIN %Table:PVQ% PVQ
                 ON PVQ.PVQ_PAIS   = PV7.PV7_PAIS
                AND PVQ.PVQ_CODPER = PVD.PVD_CODPRG
                AND CAST(PVQ.PVQ_COMBO AS VARCHAR) = (SELECT DISTINCT   CASE PVA.PVA_TIPRES   
                                                                        WHEN '3' THEN '1'
                                                                        WHEN '4' THEN '1'
                                                                        ELSE PVD.PVD_RESPUE END
																
														FROM %Table:PVA% PVA
														WHERE PVA.PVA_CODPRG = PVD.PVD_CODPRG
														AND PVA.PVA_PAIS = PV7.PV7_PAIS
														AND PVA.%NotDel%)
                AND PVQ.PVQ_TIPO   = '2'
                AND PVQ.%NotDel%
         INNER JOIN %Table:PVL% PVL
                 ON PVL.PVL_PAIS   = PV7.PV7_PAIS
                AND PVL.PVL_CODACE = PVQ.PVQ_CODACE
                AND PVL.%NotDel%
          WHERE PV7_PAIS       = %Exp:cCodPais%
            AND PV7.PV7_PROPOS = %Exp:cCodProp%
            AND PV7.%NotDel%
        UNION
        SELECT PVL.PVL_VAR,
                PVL.PVL_CONTEU,
                PVL.PVL_CONSPA,
                PVL.PVL_CONENG,
                PVL.PVL_DESVAR,
                PVL.PVL_DSCSPA,
                PVL.PVL_DSCENG,
                PVL.PVL_DESC1,
                PVL.PVL_DCSPA1,
                PVL.PVL_DCENG1,
                PVL.PVL_DESC2,
                PVL.PVL_DCSPA2,
                PVL.PVL_DCENG2
         FROM %Table:PV7% PV7
         INNER JOIN %Table:PVD% PVD
                 ON PVD_PAIS   = PV7.PV7_PAIS
                AND PVD_CODPRJ = PV7.PV7_CODPRJ
                AND PVD_VALIDA = '1'
                AND PVD.%NotDel%
         INNER JOIN %Table:PVP% PVP
                 ON PVP.PVP_PAIS   = PV7.PV7_PAIS
                AND PVP.PVP_CODPER = PVD.PVD_CODPRG
                AND CAST(PVP.PVP_COMBO AS VARCHAR) = (SELECT DISTINCT   CASE PVA.PVA_TIPRES   
                                                                        WHEN '3' THEN '1'
                                                                        WHEN '4' THEN '1'
                                                                        ELSE PVD.PVD_RESPUE END
																
														FROM %Table:PVA% PVA
														WHERE PVA.PVA_CODPRG = PVD.PVD_CODPRG
														AND PVA.PVA_PAIS = PV7.PV7_PAIS
														AND PVA.%NotDel%)
                AND PVP.%NotDel%
         INNER JOIN %Table:PVO% PVO
                 ON PVO.PVO_PAIS   = PV7.PV7_PAIS
                AND PVO.PVO_CODGRP = PVP.PVP_CODGRP
                AND PVO.PVO_TIPO   = '2'
                AND PVO.%NotDel%
         INNER JOIN %Table:PVL% PVL
                 ON PVL.PVL_PAIS   = PV7.PV7_PAIS
                AND PVL.PVL_CODACE = PVO.PVO_CODACE
                AND PVL.%NotDel%
          WHERE PV7_PAIS       = %Exp:cCodPais%
            AND PV7.PV7_PROPOS = %Exp:cCodProp%
            AND PV7.%NotDel%
    EndSQL

    aRegistros := {}

    While (cAliasAce)->(!EoF())

        AAdd(aRegistros, JsonObject():New())
        aRegistros[Len(aRegistros)]["cChave"] := XFilial("SX6") + (cAliasAce)->PVL_VAR

        aAlteracao := {}

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_CONTEUD"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_CONTEU

        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_CONTSPA"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_CONSPA
        
        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_CONTENG"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_CONENG
       
        // Incluindo campos de descri��o
        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_DESCRI"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_DESVAR
        
        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_DSCSPA"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_DSCSPA
        
        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_DSCENG"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_DSCENG
        
        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_DESC1"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_DESC1
        
        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_DSCSPA1"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_DCSPA1
        
        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_DSCENG1"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_DCENG1
        
        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_DESC2"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_DESC2
        
        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_DSCSPA2"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_DCSPA2
        
        AAdd(aAlteracao, JsonObject():New())
        aAlteracao[Len(aAlteracao)]["cCampo"] := "X6_DSCENG2"
        aAlteracao[Len(aAlteracao)]["Valor"] := (cAliasAce)->PVL_DCENG2
        // Fim campos de descri��o
        
        aRegistros[Len(aRegistros)]["aAlteracao"] := aAlteracao

        (cAliasAce)->(DbSkip())
    End

    (cAliasAce)->(DbCloseArea())

    oAcelerador["cAlias"] := "SX6"
    oAcelerador["nIndex"] := 1
    oAcelerador["aRegistros"] := aRegistros

Return oAcelerador


Static Function RetDicStruct(cAlias)

    Local cAliasAce   := ""
    Local oAcelerador := JsonObject():New()
    Local aRegistros  := {}

    oAcelerador["cAlias"] := cAlias
    oAcelerador["nIndex"] := 1
    oAcelerador["aRegistros"] := aRegistros

Return oAcelerador

Static Function RetAceMarca()

    Local oAcelerador := JsonObject():New()

    oAcelerador["cAlias"] := "XB3"
    oAcelerador["nIndex"] := 1
    oAcelerador["aRegistros"] := {}

    AAdd(oAcelerador["aRegistros"], JsonObject():New())

    oAcelerador["aRegistros"][1]["cChave"] := XB3->XB3_MARK01
    oAcelerador["aRegistros"][1]["aAlteracao"] := {}

    AAdd(oAcelerador["aRegistros"][1]["aAlteracao"], JsonObject():New())
    oAcelerador["aRegistros"][1]["aAlteracao"][1]["cCampo"] := "XB3_MARK01"
    oAcelerador["aRegistros"][1]["aAlteracao"][1]["Valor"] := AsxEncMark("999", 3) //-- com 999 t� dando pau

Return oAcelerador

Static Function PrepBase(aAceleradores, aBackup)

    Local aStruct   := {}
    Local aAlias    := {}
    Local cDir      := GetMV("FS_UPDSYS")
    Local nAlias    := 0
    Local nCampo    := 0
    Local nPos      := 0
    Local nRegistro := 0
    Local cDicTOP   := "|SX5|XB3|XXG|XXI|XXK|XXL|XXM|XXN|XXO|XXQ|XXR|XXS|XXU|"

    For nAlias := 1 to Len(aAceleradores)

        oAcelerador := aAceleradores[nAlias]
        cAlias     := oAcelerador["cAlias"]
        nIndex     := oAcelerador["nIndex"]
        cAliasTemp := "ACE" + cAlias
        cAliasBase := "BAS" + cAlias

        If cAlias $ cDicTOP
            //-- Copiar dicion�rios que est�o no banco direto dele.
            cAliasBase := cAlias
        Else
            // Abre o Dicion�rio no System Externo
            //DbUseArea(.T., __LocalDriver, cDir + cAlias + GetDBExtension(), cAliasBase, .T., .F.)
            USE (cDir +'sx6.dtc') ALIAS (SX6AP) SHARED NEW VIA (GetDBExtension())
            DbSetIndex(cDir + cAlias + ".cdx")
            DbSetOrder(nIndex)
        EndIf

        // Faz um arquivo tempor�rio com a mesma estrutura
        aStruct := (cAliasBase)->(DbStruct())

        &('DbCreate(cAliasTemp, aStruct, __LocalDriver )')
        &('DbUseArea( .T., __LocalDriver, cAliasTemp, cAliasTemp, .F. )')

        aRegistros := oAcelerador["aRegistros"]

        For nRegistro := 1 To Len(aRegistros)

            cChave := oAcelerador["aRegistros"][nRegistro]["cChave"]

            If (cAliasBase)->(DbSeek(cChave))
                RecLock(cAliasTemp, .T.)

                AAdd(aBackup,{cAlias, StrZero(nIndex, 2), cChave})

                // Copia os Campos da Estrutura Padr�o
                For nPos := 1 to Len(aStruct)
                    FieldPut(nPos, (cAliasBase)->(FieldGet(nPos)))
                Next

                aAlteracao := oAcelerador["aRegistros"][nRegistro]["aAlteracao"]

                // Faz as altera��es registradas nos aceleradores
                For nCampo := 1 to Len(aAlteracao)
                     FieldPut(FieldPos(aAlteracao[nCampo]["cCampo"]), aAlteracao[nCampo]["Valor"])
                Next

                (cAliasTemp)->(MsUnlock())
            EndIf
        Next

        AAdd(aAlias, cAlias)
        (cAliasBase)->(DbCloseArea())

    Next

Return aAlias

Static Function GeraBackup(aBackup, cFolder)

    Local nAlias    := 0
    Local cFilePath := GetNewPar("FS_ACEDIR", "\system\") + cFolder + "\"
    Local cText     := ""

    If !Empty(aBackup)

        For nAlias := 1 to Len(aBackup)
            cText += aBackup[nAlias][1] + "|" + ; // Alias
                     aBackup[nAlias][2] + "|" + ; // Index
                     aBackup[nAlias][3] + "|" + ; // Chave
                     CRLF
        Next

        If !ExistDir(cFilePath)
            MakeDir(cFilePath)
        EndIf

        If File(cFilePath + "ACEBAK.TXT")
            FRename( cFilePath + "ACEBAK.TXT", cFilePath + FwTimeStamp() + "_ACEBAK.TXT")
        EndIf

        MemoWrite(cFilePath + "ACEBAK.TXT", cText)

    EndIf

Return

User Function GeraSDF(aAlias, cFolder, cFileName)

    Local nAlias    := 0
    Local nHdl      := 0
    Local nSizeOf   := 1
    Local aRodape   := {}
    Local cFilePath := "\system\" + cFolder + "\"

    Default cFileName := "SDFBRA.TXT"

    If !Empty(aAlias)
        nHdl := FCreate(cFileName)

        For nAlias := 1 to Len(aAlias)
            DbSelectArea("ACE" + aAlias[nAlias])
            DbGoTop()
            GeraDic(nHdl, aAlias[nAlias], @nSizeOf, @aRodape)
            DbCloseArea() 
            FErase("ACE" + aAlias[nAlias] +".DBF")
        Next

        GeraRodape(nHdl, aRodape)

        FClose(nHdl)

        If !ExistDir(cFilePath)
            MakeDir(cFilePath)
        EndIf

        If File(cFilePath +cFileName)
            FRename(cFilePath + cFileName, cFilePath + FwTimeStamp() + "_" + cFileName)
        EndIf

        FRename("\System\" + cFileName, cFilePath + cFileName)
    EndIf

Return

Static Function GeraDic(nHdl, cAlias, nSizeOf, aRodape)

    Local nA         := 0
    Local nCount     := 0
    Local nWritten   := 0
    Local nLenStru   := 0
    Local nBlockSize := 2560
    Local cText      := ""
    Local aStru      := {}

    aStru := DbStruct()

    nLenStru := Len(aStru)

    While (! Eof())

        For nA := 1 To nLenStru

            If (aStru[nA][2] == 'C')
                cText += Padr(FieldGet(nA), aStru[nA][3])
            ElseIf (aStru[nA][2] == 'N')
                cText += StrZero(FieldGet(nA), aStru[nA][3], aStru[nA][4])
            ElseIf (aStru[nA][2] == 'D')
                cText += Dtos(FieldGet(nA))
            ElseIf (aStru[nA][2] == 'L')
                cText += If(FieldGet(nA) , 'T', 'F')
            EndIf
        Next

        If Len(cText) > nBlockSize
            nWritten += FWrite(nHdl, cText)
            cText := ''
        EndIf

        DbSkip()
        nCount ++
    End

    nWritten += FWrite(nHdl, cText)
    cText := ''

    For nA := 1 To nLenStru
        cText += Padr(aStru[nA][1], 10)
        cText += aStru[nA][2]
        cText += StrZero(aStru[nA][3], 3, 0)
        cText += StrZero(aStru[nA][4], 3, 0)
    Next

    AAdd(aRodape, {Chr(255) + cAlias, nSizeOf, nWritten, cText})

    nSizeOf += nWritten

Return nSizeOf

Static Function GeraRodape(nHdl, aRodape)

    Local nA           := 0
    Local nWriteFooter := 0

    For nA := 1 To Len(aRodape)

        cText := aRodape[nA][1]
        cText += StrZero(aRodape[nA][2], 12, 0)
        cText += StrZero(aRodape[nA][3], 12, 0)
        cText += aRodape[nA][4]

        nWriteFooter += FWrite(nHdl, cText)
    Next

    FWrite(nHdl, StrZero(nWriteFooter, 12, 0) )

Return

Static Function ExistArq()

    Local cAliasArq := GetNextAlias()
    Local lFoundArq := .F.
    
    BeginSQL Alias cAliasArq
        SELECT PVM.PVM_TIPO,
               PVM.PVM_ORIGEM,
               PVM.PVM_DESTIN
          FROM %Table:PV7% PV7
         INNER JOIN %Table:PVD% PVD
                 ON PVD_PAIS   = PV7.PV7_PAIS
                AND PVD_CODPRJ = PV7.PV7_CODPRJ
                AND PVD_VALIDA = '1'
                AND PVD.%NotDel%
         INNER JOIN %Table:PVQ% PVQ
                 ON PVQ.PVQ_PAIS   = PV7.PV7_PAIS
                AND PVQ.PVQ_CODPER = PVD.PVD_CODPRG
                AND CAST(PVQ.PVQ_COMBO AS VARCHAR) = (SELECT DISTINCT   CASE PVA.PVA_TIPRES   
                                                                        WHEN '3' THEN '1'
                                                                        WHEN '4' THEN '1'
                                                                        ELSE PVD.PVD_RESPUE END
																
														FROM %Table:PVA% PVA
														WHERE PVA.PVA_CODPRG = PVD.PVD_CODPRG
														AND PVA.PVA_PAIS = PV7.PV7_PAIS
														AND PVA.%NotDel%)
                AND PVQ.PVQ_TIPO   = '3'
                AND PVQ.%NotDel%
         INNER JOIN %Table:PVM% PVM
                 ON PVM.PVM_PAIS   = PV7.PV7_PAIS
                AND PVM.PVM_CODACE = PVQ.PVQ_CODACE
                AND PVM.%NotDel%
          WHERE PV7.PV7_PROPOS  = %Exp:PV7_PROPOS%
            AND PV7.%NotDel%
        UNION
        SELECT PVM.PVM_TIPO,
               PVM.PVM_ORIGEM,
               PVM.PVM_DESTIN
          FROM %Table:PV7% PV7
         INNER JOIN %Table:PVD% PVD
                 ON PVD_PAIS   = PV7.PV7_PAIS
                AND PVD_CODPRJ = PV7.PV7_CODPRJ
                AND PVD_VALIDA = '1'
                AND PVD.%NotDel%
         INNER JOIN %Table:PVP% PVP
                 ON PVP.PVP_PAIS   = PV7.PV7_PAIS
                AND PVP.PVP_CODPER = PVD.PVD_CODPRG
                AND CAST(PVP.PVP_COMBO AS VARCHAR) = (SELECT DISTINCT   CASE PVA.PVA_TIPRES   
                                                                        WHEN '3' THEN '1'
                                                                        WHEN '4' THEN '1'
                                                                        ELSE PVD.PVD_RESPUE END
																
														FROM %Table:PVA% PVA
														WHERE PVA.PVA_CODPRG = PVD.PVD_CODPRG
														AND PVA.PVA_PAIS = PV7.PV7_PAIS
														AND PVA.%NotDel%)
                AND PVP.%NotDel%
         INNER JOIN %Table:PVO% PVO
                 ON PVO.PVO_PAIS   = PV7.PV7_PAIS
                AND PVO.PVO_CODGRP = PVP.PVP_CODGRP
                AND PVO.PVO_TIPO   = '3'
                AND PVO.%NotDel%
         INNER JOIN %Table:PVM% PVM
                 ON PVM.PVM_PAIS   = PV7.PV7_PAIS
                AND PVM.PVM_CODACE = PVO.PVO_CODACE
                AND PVM.%NotDel%
          WHERE PV7.PV7_PROPOS  = %Exp:PV7_PROPOS%
            AND PV7.%NotDel%
    EndSQL
    
    lFoundArq := (cAliasArq)->(! Eof())
 
    (cAliasArq)->( DbCloseArea())
Return ( lFoundArq )
