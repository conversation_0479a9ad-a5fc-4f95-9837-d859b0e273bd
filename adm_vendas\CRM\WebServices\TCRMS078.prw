#include "totvs.ch"
#include "restful.ch"
#include "parmtype.ch"
#include "tryexception.ch"

// Define usado pela static function CampoProt 
#define IDUSRFULL "000000" // ID do Usuário com Acesso Full
#define IDUSRLIMT "000000" // ID do Usuário Limitado (Padrão)
#define LGDP .F.           // Habilita o Uso da LGPD

#define POSTUSASX2 "" // Relação de Tabelas permitidas no POST (Incluir)

/*/{Protheus.doc} TCRMS078
Reservando função
@type function
@version P12 
<AUTHOR>
@since 13/09/2023
/*/

User Function TCRMS078()

Return

	CLASS TCRMS078Adapter FROM FWAdapterBaseV2
		METHOD New()
		METHOD GetListMod()
		METHOD GetListMany()
	EndClass

Method New( cVerb ) CLASS TCRMS078Adapter
	_Super:New( cVerb, .T. )
return

Method GetListMany( cTabela, cOrdem, oRelation, lAuth, lFilial ) CLASS TCRMS078Adapter
	Local aArea 	AS ARRAY
	Local cWhere	AS CHAR
	Local cCpoFil := GetSX3Filial(cTabela)
	aArea   := FwGetArea()
	//Adiciona o mapa de campos Json/ResultSet
	AddFldMany( self, cTabela, cOrdem, oRelation, lAuth )
	//Informa a Query a ser utilizada pela API
	::SetQuery( GetQueryMany(cTabela, oRelation, lFilial) )
	//Informa a clausula Where da Query
	cWhere := " " + cTabela + ".D_E_L_E_T_ = ' ' "
	If !Empty(cCpoFil) .and. lFilial
		cWhere += " AND " + cTabela + "."+cCpoFil+" = '" + xFilial(cTabela) + "'"
	EndIf
	::SetWhere( cWhere )
	//Informa a ordenação padrão a ser Utilizada pela Query
	::SetOrder( cOrdem )
	//Executa a consulta, se retornar .T. tudo ocorreu conforme esperado
	If ::Execute()
		// Gera o arquivo Json com o retorno da Query
		// Pode ser reescrita, iremos ver em outro artigo de como fazer
		::FillGetResponse()
	EndIf
	FwrestArea(aArea)
Return

Method GetListMod( cTabela, cOrdem, lAuth, lFilial ) CLASS TCRMS078Adapter
	Local aArea 	AS ARRAY
	Local cWhere	AS CHAR
	Local cCpoFil := iif(lFilial, GetSX3Filial(cTabela), "")
	Default lAuth := .f.
	aArea   := FwGetArea()
	//Adiciona o mapa de campos Json/ResultSet
	AddMapFields( self, cTabela, cOrdem, lAuth)
	//Informa a Query a ser utilizada pela API
	::SetQuery( GetQuery(cTabela) )
	//Informa a clausula Where da Query
	cWhere := " " + cTabela + ".D_E_L_E_T_ = ' ' "
	If !Empty(cCpoFil)
		cWhere += " AND " + cTabela + "."+cCpoFil+" = '" + xFilial(cTabela) + "'"
	EndIf
	::SetWhere( cWhere )
	//Informa a ordenação padrão a ser Utilizada pela Query
	::SetOrder( cOrdem )
	//Executa a consulta, se retornar .T. tudo ocorreu conforme esperado
	If ::Execute()
		// Gera o arquivo Json com o retorno da Query
		// Pode ser reescrita, iremos ver em outro artigo de como fazer
		::FillGetResponse()
	EndIf
	FwrestArea(aArea)
Return

Static Function AddMapFields( oSelf, cTab, cOrd, lAuth )

	Local aCampos := FWSX3Util():GetAllFields( cTab, .F. )
	Local nCampo  := 0
	Local aOrds := StrToKarr(upper(cOrd),",")
	Default lAuth := .F.

	aCampos := CampoProt(lAuth, aCampos) // função para verificar campos protegidos pelo sistema

	For nCampo := 1 to Len(aCampos)
		If !(At("USERLGI",aCampos[nCampo]) > 0 .or. At("USERLGA",aCampos[nCampo]) > 0)
			oSelf:AddMapFields( aCampos[nCampo]	, aCampos[nCampo] , .T., Ascan(aOrds,upper(aCampos[nCampo])) > 0, { aCampos[nCampo], GetSx3Cache(aCampos[nCampo], 'X3_TIPO'), TamSX3( aCampos[nCampo] )[1], TamSX3( aCampos[nCampo] )[2] } )
		EndIf
	Next

Return

User Function AtoS(aContent, sTk)
	Local nAC := 1
	Local sReturn := ""
	Default aContent := {}
	Default sTk := ","
	For nAC := 1 to len(aContent)
		sReturn += cValtoChar(aContent[nAC])
		If nAC < len(aContent)
			sReturn += sTk
		EndIf
	Next
Return sReturn

Static Function GetQuery(cTab)
	Local cQuery := ""

	cQuery += " SELECT #QueryFields# "
	cQuery +=   " FROM " + RetSqlName( cTab ) + " " + cTab + " "
	cQuery += " WHERE #QueryWhere# "
	//FwLogMsg("INFO", , "WSADAPT", FunName(), "", "01", "GetQuery = " + cQuery)

Return cQuery

Static Function AddFldMany( oSelf, cTab, cOrd, oRelation, lAuth )

	Local aCampos := {}
	Local nCampo  := 0
	Local aOrds   := StrToKarr(upper(cOrd),",")
	Local aTab    := {}
	Local cTbl    := ""
	Local nTbl    := 0

	aTab := oRelation:GetNames()

	aadd(aTab, cTab)

	For nTbl:= 1 to len(aTab)
		cTbl := aTab[nTbl]
		aCampos := FWSX3Util():GetAllFields( Right(cTbl,3), .F. )
		aCampos := CampoProt(lAuth, aCampos)
		For nCampo := 1 to len(aCampos)
			If cTbl <> cTab
				If oRelation[cTbl]["TYPE"] == "DETAIL"
					Loop
				EndIf
			EndIf
			If !(At("USERLGI",aCampos[nCampo]) > 0 .or. At("USERLGA",aCampos[nCampo]) > 0)
				oSelf:AddMapFields( aCampos[nCampo]	, aCampos[nCampo] , .T., Ascan(aOrds,upper(aCampos[nCampo])) > 0, { aCampos[nCampo], GetSx3Cache(aCampos[nCampo], 'X3_TIPO'), TamSX3( aCampos[nCampo] )[1], TamSX3( aCampos[nCampo] )[2] } )
			EndIf
		Next
	Next

Return

Static Function GetQueryMany(cTab, oRelation, lFilial)
	Local cQuery := ""

	Local aTbl := ASORT(oRelation:GetNames())
	Local nTbl := 0
	Local cTbl := ""
	Local aCpo := {}
	Local nCpo := ""

	cQuery += " SELECT #QueryFields# "
	cQuery += " FROM " + RetSqlName( cTab ) + " " + cTab + "  "
	For nTbl := 1 to Len(aTbl)
		cTbl := aTbl[nTbl]
		If oRelation[cTbl]["TYPE"] == "MASTER"
			cQuery += " " + oRelation[cTbl]["JOIN"] + " JOIN " + RetSqlName( Right(cTbl,3) ) + " " + Right(cTbl,3) + " "
			cQuery += " ON ( "
			aCpo := oRelation[cTbl]["FIELDS"]
			For nCpo := 1 to Len(aCpo)
				cQuery += " " + iif(nCpo == 1, "", " AND ") + aCpo[nCpo][1] + " = " + aCpo[nCpo][2]
			Next
			cQuery += " AND " + Right(cTbl,3) + ".D_E_L_E_T_ = ' ' ) "
		EndIf
	Next
	cQuery += " WHERE #QueryWhere# "

	//FwLogMsg("INFO", , "WSADAPT", FunName(), "", "01", "GetQueryMany = " + cQuery)

Return cQuery

Static Function GetSX3Filial(cTab) // Retorna campo filial da tabela informada

	Local aCampos := FWSX3Util():GetAllFields( Right(cTab,3), .F. )
	Local nCampo  := aScan(aCampos, {|campo| "FILIAL" $ UPPER(ALLTRIM(campo)) })
	If nCampo > 0
		Return aCampos[nCampo]
	Else
		Return ""
	EndIf

Return ""

	WSRESTFUL TCRMS078 DESCRIPTION 'endpoint TCRMS078 API' FORMAT "application/json,text/html"

		WSDATA Page     AS INTEGER OPTIONAL
		WSDATA PageSize AS INTEGER OPTIONAL
		WSDATA Order    AS CHARACTER
		WSDATA Fields   AS CHARACTER OPTIONAL
		WSDATA Filter   AS CHARACTER OPTIONAL
		WSDATA auth     AS LOGICAL OPTIONAL
		WSDATA hash     AS LOGICAL OPTIONAL

		WSMETHOD GET ModList;
			DESCRIPTION "Retorna uma lista de modelos";
			WSSYNTAX "/TCRMS078" ;
			PATH "/TCRMS078/{cnpj}/{tabela}" ;
			PRODUCES APPLICATION_JSON

		WSMETHOD PUT ModPut;
			DESCRIPTION "Altera campos do modelo";
			WSSYNTAX "/TCRMS078" ;
			PATH "/TCRMS078/{cnpj}/{tabela}" ;
			PRODUCES APPLICATION_JSON

		WSMETHOD POST ModListMany;
			DESCRIPTION "Retorna uma lista com dados relacionados";
			WSSYNTAX "/TCRMS078/relacionados" ;
			PATH "/TCRMS078/relacionados/{cnpj}/{tabela}" ;
			PRODUCES APPLICATION_JSON

		WSMETHOD POST ModPost;
			DESCRIPTION "Inclui campos do modelo";
			WSSYNTAX "/TCRMS078" ;
			PATH "/TCRMS078/{cnpj}/{tabela}" ;
			PRODUCES APPLICATION_JSON

	END WSRESTFUL

	WSMETHOD GET ModList WSRECEIVE auth, hash WSREST TCRMS078
Return getModList(self)

Static Function getModList( oWS )
	Local lRet  as logical
	Local oMod as object
	Local cResponse := ""
	Local nPosEmp   := 0
	Local nEmp   := 0
	Local nRec   := 0
	Local aEmpresas := FwLoadSM0()
	Local oEmpresas := JsonObject():New()
	Local nEmpresa  := 1
	Local lAuth     := iif(lower(cvaltochar(oWS:auth)) == "true", .t., .f.)  // oWS:GetHeader( "auth" )
	Local lHash     := iif(lower(cvaltochar(oWS:hash)) == "true", .t., .f.) // oWS:GetHeader( "hash" )
	Local cCNPJURL  := Upper(AllTrim(oWS:aURLParms[1]))
	Local lFilial   := len(cCNPJURL) == 14
	Local lEmpresas := .F.
	Local aGrupos   := {}
	Local nGrupo    := 1
	Local oResponse := JsonObject():New()
	Local cCpoFil   := ""

	DEFAULT oWS:Page      := 1
	DEFAULT oWS:PageSize  := 10
	DEFAULT oWS:Fields    := ""
	DEFAULT oWS:Filter    := ""
	DEFAULT oWS:Order     := ""
	DEFAULT lAuth         := .f.

	lRet        := .T.

	If cCNPJURL == "ALL" .OR. cCNPJURL == "TODOS"
		SetRestFault(400,MyEncode("Em Desenvolvimento","cp1252"))
		return .F.

		nPosEmp := 1
		lEmpresas := .T.
		For nEmpresa := 1 to len(aEmpresas)
			nGrupo := Ascan(aGrupos,{|grupo| grupo[1] = aEmpresas[nEmpresa][1]})
		Next

	Else
		If len(cCNPJURL) = 8
			nPosEmp := Ascan(aEmpresas, {|x| Left(Alltrim(x[18]),8) == Left(AllTrim(oWS:aURLParms[1]),8) })
		ElseIf len(cCNPJURL) = 14
			nPosEmp := Ascan(aEmpresas, {|x| Alltrim(x[18]) == AllTrim(oWS:aURLParms[1]) })
		EndIf
	EndIf

	If nPosEmp == 0
		SetRestFault(400,MyEncode("Empresa não encontrada","cp1252"))
		return .F.
	Else
		cEmpAnt := aEmpresas[nPosEmp][1]
		cFilAnt := aEmpresas[nPosEmp][2]
	EndIf

	if Empty(FWSX2Util():GetFile( oWS:aURLParms[2] ))
		SetRestFault(400,MyEncode("Tabela não encontrada","cp1252"))
		return .F.
	endif

	for nEmp:= 1 to len(aEmpresas)

		if valtype(oEmpresas[aEmpresas[nEmp][1]]) == "U"
			oEmpresas[aEmpresas[nEmp][1]] := JsonObject():New()
		endif

		if valtype(oEmpresas[aEmpresas[nEmp][1]][aEmpresas[nEmp][2]]) == "U"
			oEmpresas[aEmpresas[nEmp][1]][aEmpresas[nEmp][2]] := JsonObject():New()
		endif

		oEmpresas[aEmpresas[nEmp][1]][aEmpresas[nEmp][2]]["sm0_nome"] := alltrim(aEmpresas[nEmp][6]) + " - " + alltrim(aEmpresas[nEmp][7])
		oEmpresas[aEmpresas[nEmp][1]][aEmpresas[nEmp][2]]["sm0_cgc"] := alltrim(aEmpresas[nEmp][18])

	next

	//FwLogMsg("INFO", , "WSADAPT", FunName(), "", "01", "oEmpresas:ToJSON()"+CRLF+oEmpresas:ToJSON()+CRLF)

	cCpoFil := GetSX3Filial(oWS:aURLParms[2])
	If !Empty(oWS:Fields)
		If !(cCpoFil $ oWS:Fields)
			oWS:Fields += "," + cCpoFil
		EndIf
	EndIf
	/*
	oMod := TCRMS078Adapter():new( 'GET' )
	oMod:setPage(oWS:Page)
	oMod:setPageSize(oWS:PageSize)
	oMod:SetOrderQuery(oWS:Order)
	oMod:SetUrlFilter(oWS:aQueryString )
	oMod:SetFields( oWS:Fields )
	oMod:GetListMod(oWS:aURLParms[2], oWS:Order, lAuth, lFilial)
	*/
	U_ADPTRGET(@oMod, oWS, lAuth, lFilial)
	//Se tudo ocorreu bem, retorna os dados via Json

	If oMod:lOk
		cResponse := MyEncode(oMod:getJSONResponse(),"cp1252")

		cErrorJson := oResponse:FromJson(cResponse)

		If ValType(cErrorJson) == "C"
			SetRestFault(400, MyEncode("Erro no parse do response:"+cErrorJson,"cp1252"))
			Return .F.
		EndIf

		//FwLogMsg("INFO", , "WSADAPT", FunName(), "", "01", "oResponse:ToJSON()"+CRLF+oResponse:ToJSON()+CRLF)

		For nRec := 1 To len(oResponse["items"])
			if Empty(oResponse["items"][nRec][lower(cCpoFil)])
				oResponse["items"][nRec]["sm0_nome"] := ""
				oResponse["items"][nRec]["sm0_cgc"] := ""
			else
				If lFilial
					oResponse["items"][nRec]["sm0_nome"] := oEmpresas[aEmpresas[nPosEmp][1]][ cFilAnt ]["sm0_nome"]
					oResponse["items"][nRec]["sm0_cgc"] := oEmpresas[aEmpresas[nPosEmp][1]][ cFilAnt ]["sm0_cgc"]
				Else
					oResponse["items"][nRec]["sm0_nome"] := oEmpresas[aEmpresas[nPosEmp][1]][oResponse["items"][nRec][lower(cCpoFil)]]["sm0_nome"]
					oResponse["items"][nRec]["sm0_cgc"] := oEmpresas[aEmpresas[nPosEmp][1]][oResponse["items"][nRec][lower(cCpoFil)]]["sm0_cgc"]
				endif
			endif
		Next

		If lHash
			ApplyHash(oWS:Order,@oResponse)
		EndIf
		oWS:SetResponse(MyEncode(oResponse:ToJson(),"cp1252"))

	Else
		//Ou retorna o erro encontrado durante o processamento
		SetRestFault(oMod:GetCode(),oMod:GetMessage())
		lRet := .F.
	EndIf

	oMod:DeActivate()
	oMod := nil
Return lRet
/* Usado para pegar de vÃ¡rias empresas com StartJob*/
User Function ADPTRGET(oMod, oWS, lAuth, lFilial)

	oMod := TCRMS078Adapter():new( 'GET' )
	oMod:setPage(oWS:Page)
	oMod:setPageSize(oWS:PageSize)
	oMod:SetOrderQuery(oWS:Order)
	oMod:SetUrlFilter({{"ORDER", oWS:Order},{"FILTER", oWS:Filter}} )
	oMod:SetFields( oWS:Fields )
	oMod:GetListMod(oWS:aURLParms[2], oWS:Order, lAuth, lFilial)

Return .t.

	WSMETHOD POST ModListMany WSRECEIVE auth, hash WSREST TCRMS078
Return postModListMany(self)

Static Function postModListMany( oWS )
	Local lRet  as logical
	Local oMod as object
	Local oModDetail as object
	Local oResponse := JsonObject():New()
	Local oRespDetl := JsonObject():New()
	Local nItemResp := 0
	Local cResponse := ""
	Local cErrorJson:= ""
	Local nPosEmp   := 0
	Local nField
	Local nItem  := 0
	Local aEmpresas := FwLoadSM0()
	Local oEmpresas := JsonObject():New()
	Local cBody     := oWS:GetContent()
	Local oBody     := JsonObject():New()
	Local aTab      := {}
	Local nTab      := 0
	Local aProp     := {}
	Local nProp     := {}
	Local lType     := .F.
	Local lJoin     := .F.
	Local lFields   := .F.
	Local lTable    := .F.
	Local lDetail   := .F.
	Local aFields   := {}
	Local aTabSX3   := {}
	Local nCpoDtl
	Local aCpoDtl
	Local cOrderDtl
	Local nFieldDtl
	Local aFieldDtl
	Local cFieldDtl
	Local lAuth     := iif(lower(cvaltochar(oWS:auth)) == "true", .t., .f.)  // oWS:GetHeader( "auth" )
	Local lHash     := iif(lower(cvaltochar(oWS:hash)) == "true", .t., .f.) // oWS:GetHeader( "hash" )
	Local cCNPJURL  := Upper(AllTrim(oWS:aURLParms[2]))
	Local lFilial   := len(cCNPJURL) == 14
	Local lEmpresas := .F.
	Local cCpoFil   := ""
	Local nEmp   := 1
	Local nRec   := 1
	Local oBodyDetail := JsonObject():New()
	Local aTabDtl := {}

	DEFAULT oWS:Page      := 1
	DEFAULT oWS:PageSize  := 10
	DEFAULT oWS:Fields    := ""
	DEFAULT oWS:Filter    := ""
	DEFAULT oWS:Order     := ""
	DEFAULT lAuth         := .f.

	lRet        := .T.

	cErrorJson := oBody:FromJson(cBody)

	If ValType(cErrorJson) == "C"
		SetRestFault(400, MyEncode("Erro no parse do body:"+cErrorJson,"cp1252"))
		Return .F.
	EndIf

	//FwLogMsg("INFO", , "WSADAPT", FunName(), "", "01", "oBody:ToJSON()"+CRLF+oBody:ToJSON()+CRLF)

	If cCNPJURL == "ALL" .OR. cCNPJURL == "TODOS"
		nPosEmp := 1
		lEmpresas := .T.
	Else
		If len(cCNPJURL) = 8
			nPosEmp := Ascan(aEmpresas, {|x| Left(Alltrim(x[18]),8) == Left(AllTrim(oWS:aURLParms[2]),8) })
		ElseIf len(cCNPJURL) = 14
			nPosEmp := Ascan(aEmpresas, {|x| Alltrim(x[18]) == AllTrim(oWS:aURLParms[2]) })
		EndIf
	EndIf

	//nPosEmp := Ascan(aEmpresas, {|x| Alltrim(x[18]) == AllTrim(oWS:aURLParms[2]) })

	If nPosEmp == 0
		SetRestFault(400,MyEncode("Empresa não encontrada:"+AllTrim(oWS:aURLParms[2]),"cp1252"))
		return .F.
	Else
		//RpcClearEnv()
		//RpcSetType(3)
		//RpcSetEnv(aEmpresas[nPosEmp][1],aEmpresas[nPosEmp][2])
		cEmpAnt := aEmpresas[nPosEmp][1]
		cFilAnt := aEmpresas[nPosEmp][2]

	EndIf

	if Empty(FWSX2Util():GetFile( oWS:aURLParms[3] ))
		SetRestFault(400,MyEncode("Tabela "+oWS:aURLParms[3]+" não encontrada","cp1252"))
		return .F.
	endif

	for nEmp:= 1 to len(aEmpresas)
		if valtype(oEmpresas[aEmpresas[nEmp][1]]) == "U"
			oEmpresas[aEmpresas[nEmp][1]] := JsonObject():New()
		endif

		if valtype(oEmpresas[aEmpresas[nEmp][1]][aEmpresas[nEmp][2]]) == "U"
			oEmpresas[aEmpresas[nEmp][1]][aEmpresas[nEmp][2]] := JsonObject():New()
		endif

		oEmpresas[aEmpresas[nEmp][1]][aEmpresas[nEmp][2]]["sm0_nome"] := alltrim(aEmpresas[nEmp][6]) + " - " + alltrim(aEmpresas[nEmp][7])
		oEmpresas[aEmpresas[nEmp][1]][aEmpresas[nEmp][2]]["sm0_cgc"] := alltrim(aEmpresas[nEmp][18])
	next

	cCpoFil := GetSX3Filial(oWS:aURLParms[3])
	If !Empty(oWS:Fields)
		If !(cCpoFil $ oWS:Fields)
			oWS:Fields += "," + cCpoFil
		EndIf
	EndIf

	aTab := oBody:GetNames()
	aTabSX3 := AClone(aTab)
	aadd(aTabSX3, oWS:aURLParms[3])
	For nTab := 1 to Len(aTab)

		lType     := .F.
		lJoin     := .F.
		lFields   := .F.
		lTable    := .F.

		if Empty(FWSX2Util():GetFile( right(aTab[nTab],3) ))
			SetRestFault(400,MyEncode("Tabela: "+aTab[nTab]+" não encontrada","cp1252"))
			Exit
		endif
		lTable := .T.
		aProp := oBody[aTab[nTab]]:GetNames()

		For nProp := 1 to len(aProp)
			Do Case
			Case aProp[nProp] == "TYPE"
				If oBody[aTab[nTab]][aProp[nProp]] $ "MASTER#DETAIL"
					if oBody[aTab[nTab]][aProp[nProp]] == "DETAIL"
						lDetail := .T.
					Endif
					lType := .T.
				Else
					SetRestFault(400, MyEncode("TYPE não é válido: " + oBody[aTab[nTab]][aProp[nProp]],"cp1252"))
					Exit
				EndIf
			Case aProp[nProp] == "JOIN"
				If oBody[aTab[nTab]][aProp[nProp]] $ "LEFT#RIGHT#INNER"
					lJoin     := .T.
				Else
					SetRestFault(400, MyEncode("Tipo do JOIN não é válido: " + oBody[aTab[nTab]][aProp[nProp]],"cp1252"))
					Exit
				EndIf
			Case aProp[nProp] == "FIELDS"
				aFields := oBody[aTab[nTab]][aProp[nProp]]
				If valtype(aFields) == "A"
					lFields := .T.
					For nField := 1 to Len(aFields)
						For nItem := 1 to Len(aFields[nField])
							If !CheckSX3(aTabSX3, aFields[nField][nItem])
								SetRestFault(400, MyEncode("Campo: " + aFields[nField][nItem] + " não existe nas tabelas informadas","cp1252"))
								lFields := .F.
								Exit
							EndIf
						Next
					Next
				EndIf
			End Case
		Next
	Next

	If !lType .OR. !lJoin .OR. !lFields .OR. !lTable
		Return .F.
	EndIf

	oMod := TCRMS078Adapter():new( 'GET' )
	oMod:setPage(oWS:Page)
	oMod:setPageSize(oWS:PageSize)
	oMod:SetOrderQuery(oWS:Order)
	oMod:SetUrlFilter({{"ORDER", oWS:Order},{"FILTER", oWS:Filter}} ) //oMod:SetUrlFilter(oWS:aQueryString)
	oMod:SetFields( oWS:Fields )
	oMod:GetListMany(oWS:aURLParms[3], oWS:Order, oBody, lAuth, lFilial, lEmpresas)
	//Se tudo ocorreu bem, retorna os dados via Json
	If oMod:lOk
		cResponse := MyEncode(oMod:getJSONResponse(),"cp1252")

		cErrorJson := oResponse:FromJson(cResponse)

		If ValType(cErrorJson) == "C"
			SetRestFault(400, MyEncode("Erro no parse do response:"+cErrorJson,"cp1252"))
			Return .F.
		EndIf


		If lDetail

			For nItemResp := 1 to Len(oResponse["items"])
				For nTab := 1 to Len(aTab)
					aTabDtl := {}
					Tbl2Detalhe(oBody[aTab[nTab]],@aTabDtl)
					aadd(aTabDtl,aTab[nTab])
					If oBody[aTab[nTab]]["TYPE"] == "DETAIL"
						nFieldDtl := 0
						aFieldDtl := StrToKarr(oWS:Fields, ",")
						cFieldDtl := ""
						For nFieldDtl := 1 to Len(aFieldDtl)
							if CheckSX3(aTabDtl,alltrim(aFieldDtl[nFieldDtl]))
								cFieldDtl += aFieldDtl[nFieldDtl]+","
							endif
						Next
						cFieldDtl:=left(cFieldDtl,len(cFieldDtl)-1)

						cOrderDtl := ""
						cFilteDtl := ""
						aCpoDtl := oBody[aTab[nTab]]["FIELDS"]
						nCpoFil := 0
						//1 C5_FILIAL C6_FILIAL
						//2 C6_FILIAL C5_FILIAL
						//nPassou := 0
						For nCpoDtl := 1 to Len(aCpoDtl)

							if CheckSX3({aTab[nTab]}, aCpoDtl[nCpoDtl][1])
								cOrderDtl += aCpoDtl[nCpoDtl][1]+","
								cFilteDtl += " "+aCpoDtl[nCpoDtl][1]+" "
								nCpoFil++
								cFilteDtl += " eq "
								cFilteDtl += " '"+oResponse["items"][nItemResp][LOWER(aCpoDtl[nCpoDtl][2])]+"' "
							else
								if CheckSX3({aTab[nTab]}, aCpoDtl[nCpoDtl][2])
									cOrderDtl += aCpoDtl[nCpoDtl][2]+","
									cFilteDtl += " "+aCpoDtl[nCpoDtl][2]+" "
									nCpoFil++
								endif
								cFilteDtl += " eq "
								cFilteDtl += " '"+oResponse["items"][nItemResp][LOWER(aCpoDtl[nCpoDtl][1])]+"' "
							endif
							/*
							if CheckSX3({aTab[nTab]}, aCpoDtl[nCpoDtl][2])
								cOrderDtl += aCpoDtl[nCpoDtl][2]+","
								cFilteDtl += " "+aCpoDtl[nCpoDtl][2]+" "
								nCpoFil++
							else
								cFilteDtl += " '"+oResponse["items"][nItemResp][LOWER(aCpoDtl[nCpoDtl][2])]+"' "
							endif
							*/
							if (nCpoDtl) < Len(aCpoDtl)
								cFilteDtl +=" AND "
							endif
						Next nCpoDtl
						cOrderDtl := left(cOrderDtl,len(cOrderDtl)-1)

						If nCpoFil == 0
							SetRestFault(400, MyEncode("Campos do atributo FIELD devem ter ao menos 1 campo da tabela:"+aTab[nTab],"cp1252"))
							Return .F.
						Endif

						oModDetail := TCRMS078Adapter():new( 'GET' )
						oModDetail:setPage(1)
						oModDetail:setPageSize(999999)
						oModDetail:SetOrderQuery(cOrderDtl)
						oModDetail:SetUrlFilter({{"ORDER",cOrderDtl},{"FILTER", cFilteDtl}})
						oModDetail:SetFields(cFieldDtl)
						JsonDetalhe(oBody[aTab[nTab]],@oBodyDetail)
						oModDetail:GetListMany(aTab[nTab], cOrderDtl, oBodyDetail, lAuth, lFilial)

						If oModDetail:lOk
							cErrorJson := oRespDetl:FromJson(oModDetail:getJSONResponse())

							If ValType(cErrorJson) == "C"
								SetRestFault(400, MyEncode("Erro no parse do response:"+cErrorJson,"cp1252"))
								Return .F.
							EndIf

							oResponse["items"][nItemResp][aTab[nTab]] := oRespDetl["items"]
						EndIf
						oModDetail:DeActivate()
					EndIf

				Next nRab
			Next nItemResp
		Endif

		For nRec := 1 To len(oResponse["items"])
			if Empty(oResponse["items"][nRec][lower(cCpoFil)])
				oResponse["items"][nRec]["sm0_nome"] := ""
				oResponse["items"][nRec]["sm0_cgc"] := ""
			else
				oResponse["items"][nRec]["sm0_nome"] := oEmpresas[aEmpresas[nPosEmp][1]][oResponse["items"][nRec][lower(cCpoFil)]]["sm0_nome"]
				oResponse["items"][nRec]["sm0_cgc"] := oEmpresas[aEmpresas[nPosEmp][1]][oResponse["items"][nRec][lower(cCpoFil)]]["sm0_cgc"]
			endif
		Next

		If lHash
			ApplyHash(oWS:Order, @oResponse)
		EndIf

		oWS:SetResponse(MyEncode(oResponse:ToJson(),"cp1252"))
	Else
		//Ou retorna o erro encontrado durante o processamento
		SetRestFault(oMod:GetCode(), oMod:GetMessage())
		lRet := .F.
	EndIf

	oMod:DeActivate()
	oMod := nil
Return lRet


	WSMETHOD PUT ModPut WSREST TCRMS078
Return PutMod(self)

Static Function PutMod( oWS )
	Local lRet  as logical
	Local nPosEmp
	Local aEmpresas := FwLoadSM0()
	Local cOrder    := oWS:Order
	Local cBody     := oWS:GetContent()
	Local oBody     := JsonObject():New()
	Local oJson     := JsonObject():New()
	Local cTbl      := oWS:aURLParms[2]
	Local cErrorJson:= ""
	Local aAtrib    := {}
	Local cAtrib    := ""
	Local nAtrib    := 0
	Local aCampos   := FWSX3Util():GetAllFields( cTbl, .F. )
	Local nChave    := Ascan(oWS:aQueryString, {|parametro| parametro[1] == "CHAVE"})
	Local uChave    := iif (nChave > 0, oWS:aQueryString[nChave][2], "")
	//Local cChave    := ""
	Local lAlterado := .F.

	lRet        := .T.

	nPosEmp := Ascan(aEmpresas, {|x| Alltrim(x[18]) == AllTrim(oWS:aURLParms[1]) })

	If nPosEmp == 0
		SetRestFault(400,MyEncode("Empresa não encontrada","cp1252"))
		return .F.
	Else
		//RpcClearEnv()
		//RpcSetType(3)
		//RpcSetEnv(aEmpresas[nPosEmp][1],aEmpresas[nPosEmp][2])
		cEmpAnt := aEmpresas[nPosEmp][1]
		cFilAnt := aEmpresas[nPosEmp][2]
	EndIf

	if Empty(uChave)
		SetRestFault(400,MyEncode("Parâmetro Chave não informado","cp1252"))
		return .F.
	endif

	if Empty(FWSX2Util():GetFile(cTbl))
		SetRestFault(400,MyEncode("Tabela não encontrada","cp1252"))
		return .F.
	endif

	if !FWSIXUtil():ExistIndex(cTbl , cOrder)
		SetRestFault(400,MyEncode("Order não encontrado","cp1252"))
		return .F.
	endif
/*
	TryException
			cChave := &uChave
	CatchException Using oError
			RollBackSX8()
			DisarmTransaction()
			SetRestFault(400, MyEncode("Erro na execução da Chave:" + oError:Description ))
			return .F.
	EndException
*/

	cErrorJson := oBody:FromJson(cBody)

	If ValType(cErrorJson) == "C"
		SetRestFault(400, MyEncode("Erro no parse do body:"+cErrorJson,"cp1252"))
		Return .F.
	EndIf

	aAtrib := oBody:GetNames()
	For nAtrib := 1 to len(aAtrib)
		cAtrib := aAtrib[nAtrib]
		If AScan(aCampos, {|campo| campo == cAtrib}) == 0
			SetRestFault(400, MyEncode("Campo do body não existe:"+cAtrib,"cp1252"))
			Return .F.
		EndIf
	Next

	DbSelectArea(cTbl)
	DbSetOrder(val(cOrder))
	(cTbl)->(DbSeek(uChave))

	If !(cTbl)->(Eof())
		RecLock(cTbl, .F.)
		// for no get names para gravar com fieldput
		For nAtrib := 1 to len(aAtrib)
			cAtrib := aAtrib[nAtrib]

			Do Case
			Case GetSx3Cache(Upper(cAtrib), "X3_TIPO") == "C"
				(cTbl)->(FieldPut((cTbl)->(FieldPos(Upper(Alltrim(cAtrib)))), oBody[cAtrib]))
			Case GetSx3Cache(Upper(cAtrib), "X3_TIPO") == "N"
				(cTbl)->(FieldPut((cTbl)->(FieldPos(Upper(Alltrim(cAtrib)))), oBody[cAtrib]))
			Case GetSx3Cache(Upper(cAtrib), "X3_TIPO") == "D"
				(cTbl)->(FieldPut((cTbl)->(FieldPos(Upper(Alltrim(cAtrib)))), CtoD(oBody[cAtrib])))
			Case GetSx3Cache(Upper(cAtrib), "X3_TIPO") == "L"
				(cTbl)->(FieldPut((cTbl)->(FieldPos(Upper(Alltrim(cAtrib)))), oBody[cAtrib]))
			EndCase
			oJson[cAtrib] := (cTbl)->(FieldGet((cTbl)->(FieldPos(Upper(Alltrim(cAtrib))))))

		Next
		oJson["RecNo"] := (cTbl)->(RecNo())
		(cTbl)->(MsUnLock())
		lAlterado := .T.
	EndIf
	If lAlterado
		oWS:SetResponse(MyEncode(oJson:ToJson(),"cp1252"))
	Else
		SetRestFault(400, MyEncode("Registro não encontrado","cp1252"))
		Return .F.
	EndIf

Return lRet

	WSMETHOD POST ModPost WSREST TCRMS078
Return PostMod(self)

Static Function PostMod( oWS )
	Local lRet  as logical
	Local nPosEmp
	Local aEmpresas := FwLoadSM0()
	Local cBody     := oWS:GetContent()
	Local oBody     := JsonObject():New()
	Local oJson     := JsonObject():New()
	Local cTbl      := oWS:aURLParms[2]
	Local cErrorJson:= ""
	Local aAtrib    := {}
	Local cAtrib    := ""
	Local nAtrib    := 0
	Local aCampos   := FWSX3Util():GetAllFields( cTbl, .F. )

	lRet        := .T.

	nPosEmp := Ascan(aEmpresas, {|x| Alltrim(x[18]) == AllTrim(oWS:aURLParms[1]) })

	If nPosEmp == 0
		SetRestFault(400,MyEncode("Empresa não encontrada","cp1252"))
		return .F.
	Else
		//RpcClearEnv()
		//RpcSetType(3)
		//RpcSetEnv(aEmpresas[nPosEmp][1],aEmpresas[nPosEmp][2])
		cEmpAnt := aEmpresas[nPosEmp][1]
		cFilAnt := aEmpresas[nPosEmp][2]
	EndIf

	if Empty(FWSX2Util():GetFile(cTbl))
		SetRestFault(400,MyEncode("Tabela não encontrada","cp1252"))
		return .F.
	endif

	if  cTbl $ POSTUSASX2
		SetRestFault(400,MyEncode("Tabela não faz parte do grupo permitido para inclusão de dados","cp1252"))
		return .F.
	endif

	cErrorJson := oBody:FromJson(cBody)

	If ValType(cErrorJson) == "C"
		SetRestFault(400, MyEncode("Erro no parse do body:"+cErrorJson,"cp1252"))
		Return .F.
	EndIf

	aAtrib := oBody:GetNames()
	For nAtrib := 1 to len(aAtrib)
		cAtrib := aAtrib[nAtrib]
		If AScan(aCampos, {|campo| campo == cAtrib}) == 0
			SetRestFault(400, MyEncode("Campo do body não existe:"+cAtribV))
			Return .F.
		EndIf
	Next

	DbSelectArea(cTbl)

	RecLock(cTbl, .T.)
	// for no get names para gravar com fieldput
	For nAtrib := 1 to len(aAtrib)
		cAtrib := aAtrib[nAtrib]

		Do Case
		Case GetSx3Cache(Upper(cAtrib), "X3_TIPO") == "C"
			(cTbl)->(FieldPut((cTbl)->(FieldPos(Upper(Alltrim(cAtrib)))), oBody[cAtrib]))
		Case GetSx3Cache(Upper(cAtrib), "X3_TIPO") == "N"
			(cTbl)->(FieldPut((cTbl)->(FieldPos(Upper(Alltrim(cAtrib)))), oBody[cAtrib]))
		Case GetSx3Cache(Upper(cAtrib), "X3_TIPO") == "D"
			(cTbl)->(FieldPut((cTbl)->(FieldPos(Upper(Alltrim(cAtrib)))), CtoD(oBody[cAtrib])))
		Case GetSx3Cache(Upper(cAtrib), "X3_TIPO") == "L"
			(cTbl)->(FieldPut((cTbl)->(FieldPos(Upper(Alltrim(cAtrib)))), oBody[cAtrib]))
		EndCase

	Next
	(cTbl)->(MsUnLock())
	oJson["nReg"] := (cTbl)->(RecNo())

	oWS:SetResponse(MyEncode(oJson:ToJson(),"cp1252"))

Return lRet


	WSRESTFUL TCRMS078funcao DESCRIPTION 'endpoint funcao API' FORMAT "application/json,text/html"


		WSDATA cFunction AS CHARACTER

		WSMETHOD GET GetFuncaoExec;
			DESCRIPTION "Executa função e devolve o retorno";
			WSSYNTAX "/TCRMS078funcao" ;
			PATH "/TCRMS078funcao/{cnpj}" ;
			PRODUCES APPLICATION_JSON

		WSMETHOD POST PostFuncaoExec;
			DESCRIPTION "Executa função e devolve o retorno";
			WSSYNTAX "/TCRMS078funcao" ;
			PATH "/TCRMS078funcao/{cnpj}" ;
			PRODUCES APPLICATION_JSON

	END WSRESTFUL

	WSMETHOD GET GetFuncaoExec WSREST TCRMS078funcao

	// Define um code block de tratamento de erro e guarda o code block atual
	//Local oError    := Errorblock({|e| Break(e) })
	Local aEmpresas := FwLoadSM0()
	Local nPosEmp   := 0
	Local uReturn   := ""
	Local uFunction := ::cFunction
	Local oJson     := JsonObject():New()
	Local lExecutou := .F.
	Local nLog

	Private lMsHelpAuto := .T.
	Private lMsErroAuto := .F.
	Private lAutoErrNoFile := .T.

	nPosEmp := Ascan(aEmpresas, {|x| Alltrim(x[18]) == AllTrim(::aURLParms[1])})

	If nPosEmp == 0
		SetRestFault(400,MyEncode("Empresa não encontrada","cp1252"))
		return .F.
	Else
		//RpcClearEnv()
		//RpcSetType(3)
		//RpcSetEnv(aEmpresas[nPosEmp][1],aEmpresas[nPosEmp][2])
		cEmpAnt := aEmpresas[nPosEmp][1]
		cFilAnt := aEmpresas[nPosEmp][2]
	EndIf

	if Empty(uFunction)
		SetRestFault(400,MyEncode("função em Branco","cp1252"))
		return .F.
	endif

	TryException
	uReturn := &uFunction
	lExecutou := .T.
	CatchException Using oError
	RollBackSX8()
	DisarmTransaction()
	SetRestFault(400, MyEncode("Erro na execução da função:" + oError:Description,"cp1252" ))
return .F.
	EndException


	If lExecutou
		oJson["return"] := uReturn

		If lMsErroAuto
			RollBackSX8()
			DisarmTransaction()

			cLogErro := ""
			aErroAuto := GetAutoGRLog()
			For nLog := 1 To Len(aErroAuto)
				cLogErro += StrTran(StrTran(aErroAuto[nLog], "<", ""), "-", "") + " " + CRLF
			Next nLog
			oJson["error"] := MyEncode(cLogErro,"cp1252")
			::SetStatus(400)
		Else
			ConfirmSX8()
			::SetStatus(200)
		EndIf

		::SetResponse(MyEncode(oJson:ToJson(),"cp1252"))
	Else
		SetRestFault(500, MyEncode("Erro na execução","cp1252"))
		return .F.
	EndIf

	// Restaura o code block de tratamento de erro original
	//ErrorBlock( oError )

Return .T.

	WSMETHOD POST PostFuncaoExec WSREST TCRMS078funcao

	// Define um code block de tratamento de erro e guarda o code block atual
	Local oError
	Local aEmpresas := FwLoadSM0()
	Local nPosEmp   := 0
	Local uReturn   := ""
	Local cErrorJson  := ""
	Local uFunction := ::cFunction
	Local cBody     := ::GetContent()
	Local oJson     := JsonObject():New()
	Local oBody     := JsonObject():New()
	Local nVar      := 0
	Local aVar      := {}
	Local lExecutou := .F.
	Local cLogErro
	Local aErroAuto
	Local nLog
	Local cVar      := ""

	Private lMsHelpAuto := .T.
	Private lMsErroAuto := .F.
	Private lAutoErrNoFile := .T.

	nPosEmp := Ascan(aEmpresas, {|x| Alltrim(x[18]) == AllTrim(::aURLParms[1])})

	If nPosEmp == 0
		SetRestFault(400, MyEncode("Empresa não encontrada","cp1252"))
		return .F.
	Else
		//RpcClearEnv()
		//RpcSetType(3)
		//RpcSetEnv(aEmpresas[nPosEmp][1], aEmpresas[nPosEmp][2])
		cEmpAnt := aEmpresas[nPosEmp][1]
		cFilAnt := aEmpresas[nPosEmp][2]
	EndIf

	if Empty(uFunction)
		SetRestFault(400, MyEncode("função em Branco","cp1252"))
		return .F.
	endif

	cErrorJson := oBody:FromJson(cBody)

	If ValType(cErrorJson) == "C"
		SetRestFault(400, MyEncode("Erro no parse do body:"+cErrorJson,"cp1252"))
		Return .F.
	EndIf

	ParseJSON(@oBody, ::aQueryString, @oJson)

	aVar := oBody:GetNames()
	For nVar := 1 to len(aVar)
		cVar  := aVar[nVar]
		&cVar := oBody[aVar[nVar]]
	Next nVar

	Begin Transaction

		TryException
		uReturn := &uFunction
		lExecutou := .T.
		CatchException Using oError
		RollBackSX8()
		DisarmTransaction()
		SetRestFault(400, MyEncode("Erro na execução da função:" + oError:Description,"cp1252" ))
		lExecutou := .f.
		EndException

		If lExecutou

			oJson["return"] := uReturn

			If lMsErroAuto
				RollBackSX8()
				DisarmTransaction()

				cLogErro := ""
				aErroAuto := GetAutoGRLog()
				For nLog := 1 To Len(aErroAuto)
					cLogErro += StrTran(StrTran(aErroAuto[nLog], "<", ""), "-", "") + " " + CRLF
				Next nLog
				oJson["error"] := MyEncode(cLogErro,"cp1252")
				::SetStatus(400)
			Else
				ConfirmSX8()
				::SetStatus(200)
			EndIf


			::SetResponse(MyEncode(oJson:ToJson(),"cp1252"))

		Else
			SetRestFault(500,MyEncode("Erro na execução","cp1252"))
			lExecutou := .f.
		EndIf
	End Transaction


Return lExecutou

Static Function ParseJSON(oJson, aParam, oReturn) // Instancia funções e variaveis passadas no JSON

	Local aAtrib := oJson:GetNames()
	Local nAtrib := 0
	Local uAtrib := Nil
	Local nParam := 0
	Local cParam := ""
	Local uParam := Nil
	Local nDetal := 1
	Local nDetal2 := 1
	Local nDetal3 := 1

	For nAtrib := 1 to len(aAtrib)

		uAtrib := oJson[aAtrib[nAtrib]]
		Do Case
		Case valtype(uAtrib) == "C"
			nParam := AScan(aParam, {|parametro| parametro[1] == Upper(Alltrim(uAtrib))})
			If nParam > 0
				cParam := aParam[nParam][2]
				uParam := &cParam
				oJson[aAtrib[nAtrib]] := uParam
				oReturn[cParam] := uParam
			EndIf
		Case valtype(uAtrib) == "A"
			If Len(oJson[aAtrib[nAtrib]]) > 0
				For nDetal := 1 to len(oJson[aAtrib[nAtrib]])
					Do Case
					Case valtype(oJson[aAtrib[nAtrib]][nDetal]) == "C"
						nParam := AScan(aParam, {|parametro| parametro[1] == Upper(Alltrim(oJson[aAtrib[nAtrib]][nDetal]))})
						If nParam > 0
							cParam := aParam[nParam][2]
							uParam := &cParam
							oJson[aAtrib[nAtrib]][nDetal] := uParam
							oReturn[aParam[nParam][1]] := uParam
						EndIf
					Case valtype(oJson[aAtrib[nAtrib]][nDetal]) == "A"
						For nDetal2 := 1 to len(oJson[aAtrib[nAtrib]][nDetal])
							Do Case
							Case valtype(oJson[aAtrib[nAtrib]][nDetal][nDetal2]) == "C"
								nParam := AScan(aParam, {|parametro| parametro[1] == Upper(Alltrim(oJson[aAtrib[nAtrib]][nDetal][nDetal2]))})
								If nParam > 0
									cParam := aParam[nParam][2]
									uParam := &cParam
									oJson[aAtrib[nAtrib]][nDetal][nDetal2] := uParam
									oReturn[aParam[nParam][1]] := uParam
								EndIf
							Case valtype(oJson[aAtrib[nAtrib]][nDetal][nDetal2]) == "A"

								For nDetal3 := 1 to len(oJson[aAtrib[nAtrib]][nDetal][nDetal2])
									Do Case
									Case valtype(oJson[aAtrib[nAtrib]][nDetal][nDetal2][nDetal3]) == "C"
										nParam := AScan(aParam, {|parametro| parametro[1] == Upper(Alltrim(oJson[aAtrib[nAtrib]][nDetal][nDetal2][nDetal3]))})
										If nParam > 0
											cParam := aParam[nParam][2]
											uParam := &cParam
											oJson[aAtrib[nAtrib]][nDetal][nDetal2][nDetal3] := uParam
											oReturn[aParam[nParam][1]] := uParam
										EndIf
									Case valtype(oJson[aAtrib[nAtrib]][nDetal][nDetal2][nDetal3]) == "A"
										FwLogMsg("INFO", , "WSADAPT", FunName(), "", "01", "Array não mapeado oJson[aAtrib[nAtrib]][nDetal][nDetal2][nDetal3] = " + aAtrib[nAtrib]+":"+cValtoChar(nDetal)+":"+cValtoChar(nDetal2)+":"+cValtoChar(nDetal3))
									EndCase
								Next nDetal3


							EndCase
						Next nDetal2
					EndCase
				Next nDetal
			EndIf
		EndCase

	Next nAtrib

Return

Static Function CheckSX3(aTab, cCpo)
	Local nTbl    := 0
	Local nCampo  := 0
	Local cTab    := ""
	Local aFld    := {}
	Local nFld    := 0
	For nTbl := 1 to Len(aTab)
		cTab := Right(aTab[nTbl],3)
		nCampo  := (cTab)->(FieldPos(cCpo))
		if (nCampo > 0)
			Exit
		endif
	Next

	If (nCampo == 0)
		For nTbl := 1 to Len(aTab)
			cTab := Right(aTab[nTbl],3)
			aFld := FWSX3Util():GetListFieldsStruct( cTab , .F. )
			For nFld := 1 to Len(aFld)
				If aFld[nFld][1] $ cCpo
					nCampo := nFld
					Exit
				EndIf
			Next
		Next
	EndIf

Return nCampo > 0

Static Function CampoProt(lAuth, aCpoChk)

	Local aCps := {}
	Default lAuth := .f.

	If LGDP
		aCps := FwProtectedDataUtil():UsrAccessPDField( iif(lAuth, IDUSRFULL, IDUSRFULL), aCpoChk )
	Else
		aCps := aCpoChk
	EndIf

Return aCps


Static Function ApplyHash(cOrder, oJson, cCpo, lHash)

	Local nR1 := 1
	Local cHash := ""
	Local nAtri := 0
	Local cAtri := ""

	Default cOrder  := ""
	Default cCpo  := "items"
	Default lHash := .t.

	cOrder += ",sm0_nome"

	If aScan(oJson:GetNames(), {|atributo| atributo == cCpo}) > 0
		For nR1 := 1	To len(oJson[cCpo])

			for nAtri := 1 to len(oJson[cCpo][nR1]:GetNames())
				cAtri := oJson[cCpo][nR1]:GetNames()[nAtri]
				if !(upper(cAtri) $ upper(cOrder))
					if valtype(oJson[cCpo][nR1][cAtri]) = "A"
						cHash += ApplyHash(cOrder, oJson[cCpo][nR1], cAtri, .f.)
					else
						cHash += cvaltochar(oJson[cCpo][nR1][cAtri])
					endif
				endif
			next

			if lHash
				oJson[cCpo][nR1]["hash"] := SHA512(cHash)
				//FwLogMsg("INFO", , "WSADAPT", FunName(), "", "01", CRLF+"cHash = " + cHash+CRLF+" SHA512 = "+SHA512(cHash))
				cHash := ""
			endif
		Next
	EndIf

Return cHash

Static Function Tbl2Detalhe(oBody, aTab)
	Local aAtrib := {}
	Local nAtrib := 1

	aAtrib := oBody:GetNames()

	For nAtrib := 1 to len(aAtrib)

		if !(aAtrib[nAtrib] $ "TYPE#JOIN#FIELDS")
			aadd(aTab, aAtrib[nAtrib])
		endif

	Next nAtrib

Return .t.
Static Function JsonDetalhe(oBody, oDetail)
	Local aAtrib := {}
	Local nAtrib := 1

	aAtrib := oBody:GetNames()

	For nAtrib := 1 to len(aAtrib)

		if !(aAtrib[nAtrib] $ "TYPE#JOIN#FIELDS")
			oDetail[aAtrib[nAtrib]] := oBody[aAtrib[nAtrib]]
		endif

	Next nAtrib

Return .t.
Static Function Macro(cMacro)
	If Empty(cMacro)
		Return
	EndIf
return &cMacro

Static Function MyEncode(cJson, cCodePage)
	Local cRegEx := "Â°#Âº#Âª#&#'#<#>"
	Local aRegex := StrToKarr(cRegEx, "#")
	Local cUTF8  := ""
	Local nChar  := 1
	Local aCaracteresEspeciais  := {}
	Local cCharEspecial         := ""
	Local cCharCorreto          := ""
	Local nT                    := 0
	Default cJson               := ""

	aAdd( aCaracteresEspeciais, { 'Ã¡',  'A' } )
	aAdd( aCaracteresEspeciais, { 'Ã¡',  'A' } )
	aAdd( aCaracteresEspeciais, { 'Ã ',  'A' } )
	aAdd( aCaracteresEspeciais, { 'Ã¢',  'A' } )
	aAdd( aCaracteresEspeciais, { 'Ã£',  'A' } )
	aAdd( aCaracteresEspeciais, { 'Ã¤',  'A' } )
	aAdd( aCaracteresEspeciais, { 'Ã�',  'A' } )
	aAdd( aCaracteresEspeciais, { 'Ã€',  'A' } )
	aAdd( aCaracteresEspeciais, { 'Ã‚',  'A' } )
	aAdd( aCaracteresEspeciais, { 'Ãƒ',  'A' } )
	aAdd( aCaracteresEspeciais, { 'Ã„',  'A' } )
	aAdd( aCaracteresEspeciais, { 'Ã©',  'E' } )
	aAdd( aCaracteresEspeciais, { 'Ã¨',  'E' } )
	aAdd( aCaracteresEspeciais, { 'Ãª',  'E' } )
	aAdd( aCaracteresEspeciais, { 'Ãª',  'E' } )
	aAdd( aCaracteresEspeciais, { 'Ã‰',  'E' } )
	aAdd( aCaracteresEspeciais, { 'Ãˆ',  'E' } )
	aAdd( aCaracteresEspeciais, { 'ÃŠ',  'E' } )
	aAdd( aCaracteresEspeciais, { 'Ã‹',  'E' } )
	aAdd( aCaracteresEspeciais, { 'Ã­',  'I' } )
	aAdd( aCaracteresEspeciais, { 'Ã¬',  'I' } )
	aAdd( aCaracteresEspeciais, { 'Ã®',  'I' } )
	aAdd( aCaracteresEspeciais, { 'Ã¯',  'I' } )
	aAdd( aCaracteresEspeciais, { 'Ã�',  'I' } )
	aAdd( aCaracteresEspeciais, { 'ÃŒ',  'I' } )
	aAdd( aCaracteresEspeciais, { 'ÃŽ',  'I' } )
	aAdd( aCaracteresEspeciais, { 'Ã�',  'I' } )
	aAdd( aCaracteresEspeciais, { 'Ã³',  'O' } )
	aAdd( aCaracteresEspeciais, { 'Ã²',  'O' } )
	aAdd( aCaracteresEspeciais, { 'Ã´',  'O' } )
	aAdd( aCaracteresEspeciais, { 'Ãµ',  'O' } )
	aAdd( aCaracteresEspeciais, { 'Ã¶',  'O' } )
	aAdd( aCaracteresEspeciais, { 'Ã“',  'O' } )
	aAdd( aCaracteresEspeciais, { 'Ã’',  'O' } )
	aAdd( aCaracteresEspeciais, { 'Ã”',  'O' } )
	aAdd( aCaracteresEspeciais, { 'Ã•',  'O' } )
	aAdd( aCaracteresEspeciais, { 'Ã–',  'O' } )
	aAdd( aCaracteresEspeciais, { 'Ãº',  'U' } )
	aAdd( aCaracteresEspeciais, { 'Ã¹',  'U' } )
	aAdd( aCaracteresEspeciais, { 'Ã»',  'U' } )
	aAdd( aCaracteresEspeciais, { 'Ã¼',  'U' } )
	aAdd( aCaracteresEspeciais, { 'Ãš',  'U' } )
	aAdd( aCaracteresEspeciais, { 'Ã™',  'U' } )
	aAdd( aCaracteresEspeciais, { 'Ã›',  'U' } )
	aAdd( aCaracteresEspeciais, { 'Ã§',  'C' } )
	aAdd( aCaracteresEspeciais, { 'Ã‡',  'C' } )
	aAdd( aCaracteresEspeciais, { 'Ã±',  'N' } )
	aAdd( aCaracteresEspeciais, { 'Ã‘',  'N' } )
	aAdd( aCaracteresEspeciais, { '&',  'E' } )
	aAdd( aCaracteresEspeciais, { "'",  '' } )
	aAdd( aCaracteresEspeciais, { chr(160),  '' } )

	For nT := 1 To Len( aCaracteresEspeciais )
		cCharCorreto    := aCaracteresEspeciais[nT][2]
		cCharEspecial   := aCaracteresEspeciais[nT][1]

		If cCharEspecial $ cJson
			cJson := StrTran( cJson, cCharEspecial, cCharCorreto )
		EndIf

	Next nT

	For nChar := 1 To Len(aRegex)
		cJSON := StrTran(cJSON, aRegex[nChar], "")
	Next nChar

	cJSON := FwCutOff(cJSON, .t.)
	cJSON := noAcento(cJSON)
	cJSON := FwNoAccent(cJSON)
	cUTF8 := EncodeUtf8(cJSON)
	If ValType(cUTF8) = "C"
		cJSON := cUTF8
	EndIf
Return cJSON

