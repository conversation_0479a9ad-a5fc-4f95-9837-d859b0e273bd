#INCLUDE 'TOTVS.CH'
#INCLUDE "FWBROWSE.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "TOPCONN.CH"
#DEFINE CRLF		Chr(13)+Chr(10)


/*/{Protheus.doc} TIRCJ000()
	Funcao Job Responsavel por executar o envio e recebimento do Interceptor
	@type  Function
	<AUTHOR> Menabue Lima
	@since 05/10/2021
	@version 1.0

	/*/ 
User Function TIINTJ000()
	Local oReceptor	:= Nil
	Local lInterR	:= .F.
	Local lInterT	:= .F.
	Local nInternT	:= .F.
	Local lInterE	:= .F.
	Local lSendFile := .F.

	If Select("SM0") == 0
		RpcSetEnv( '00', '00001000100')
	EndIf

	lInterR	:=GetMv("TI_RCVJ00R" , .F., .T.) //Processa envio de dados do Interceptor
	lInterT	:=GetMv("TI_RCVJ00T" , .F., .T.) //Processa o Interceptor em Threads
	nInternT:=GetMv("TI_RCVJ00N" , .F.,  15 ) //Numero de Threads que o Interceptor vai gerar
	nInternR:=GetMv("TI_RCVJ0NR" , .F.,  6 ) //Numero de Threads que o Interceptor vai gerar para recebimento
	lInterE	:=GetMv("TI_RCVJ00E" , .F., .T.) //Processa o Recebimento do Interceptor
	lSendFile:=GetMv("TI_RCVJ00F" , .F., .T.) //Processa o Arquivos do Interceptor
	//Inicia o Interceptor
	oReceptor:=TIINTERCEPTOR():New()
	oReceptor:lMultiThread := lInterT

	IF lInterT
		oReceptor:nMaxThreads  := nInternT
		oReceptor:nMaxThrdsR   := nInternR
	EndIF
	//Prepara e Envia Todas as Requests de envio
	IF lInterE
		FWMonitorMsg("***** Inicio Rodando Interceptors Envio ***** " )
		oReceptor:PrepareAllRequests() //p37_tpreq =1
		FWMonitorMsg( "***** Termino Rodando Interceptors Envio ***** ")
	EndIF

	//Prepara e Envia Todas as Requests de Recebimento
	IF lInterR
		FWMonitorMsg("***** Inicio Rodando Interceptors Recebimento ***** " )
		oReceptor:PrepareReceiveAll() //p37_tpreq =2
		FWMonitorMsg( "***** Termino Rodando Interceptors Recebimento ***** ")
	EndIF
	IF lSendFile
		FWMonitorMsg("***** Inicio Rodando Interceptors Envio e Arquivos ***** " )
		oReceptor:GetFiles()
		FWMonitorMsg( "***** Termino Rodando Interceptors Envio e Arquivos ***** ")
	EndIF
	FREEOBJ( oReceptor )
	oReceptor := nIL
	RpcClearEnv()
Return

/*/{Protheus.doc} TIRCJ0E()
	Funcao Job Responsavel por executar o envio  do Interceptor
	@type  Function
	<AUTHOR> Menabue Lima
	@since 05/10/2021
	@version 1.0
	/*/ 
User Function TIINTJ0E()
	Local oReceptor	:= Nil
	Local lInterR	:= .F.
	Local lInterT	:= .F.
	Local nInternT	:= .F.
	Local lInterE	:= .F.


	If Select("SM0") == 0
		RpcSetEnv( '00', '00001000100')
	EndIf

	lInterR	:=GetMv("TI_RCVJ00R" , .F., .T.) //Processa envio de dados do Interceptor
	lInterT	:=GetMv("TI_RCVJ00T" , .F., .T.) //Processa o Interceptor em Threads
	nInternT:=GetMv("TI_RCVJ00N" , .F.,  15 ) //Numero de Threads que o Interceptor vai gerar
	nInternR:=GetMv("TI_RCVJ0NR" , .F.,  6 ) //Numero de Threads que o Interceptor vai gerar para recebimento
	lInterE	:=GetMv("TI_RCVJ00E" , .F., .T.) //Processa o Recebimento do Interceptor

	//Inicia o Interceptor
	oReceptor:=TIINTERCEPTOR():New()
	oReceptor:lMultiThread := lInterT

	IF lInterT
		oReceptor:nMaxThreads  := nInternT
		oReceptor:nMaxThrdsR   := nInternR
	EndIF
	//Prepara e Envia Todas as Requests de envio
	IF lInterE
		FWMonitorMsg("***** Inicio Rodando Interceptors Envio ***** " )
		oReceptor:PrepareAllRequests() //p37_tpreq =1
		FWMonitorMsg( "***** Termino Rodando Interceptors Envio ***** ")
	EndIF

	FREEOBJ( oReceptor )
	oReceptor := nIL
	RpcClearEnv()
Return
/*/{Protheus.doc} TIRCJ00R()
	Funcao Job Responsavel por executar o   recebimento do Interceptor
	@type  Function
	<AUTHOR> Menabue Lima
	@since 05/10/2021
	@version 1.0
	/*/ 
User Function TIINTJ0R()
	Local oReceptor	:= Nil
	Local lInterR	:= .F.
	Local lInterT	:= .F.
	Local nInternT	:= .F.
	Local lInterE	:= .F.
	Local lSendFile := .F.


	If Select("SM0") == 0
		RpcSetEnv( '00', '00001000100')
	EndIf

	lInterR	:=GetMv("TI_RCVJ00R"  , .F., .T.) //Processa envio de dados do Interceptor
	lInterT	:=GetMv("TI_RCVJ00T"  , .F., .T.) //Processa o Interceptor em Threads
	nInternT:=GetMv("TI_RCVJ00N"  , .F.,  15 ) //Numero de Threads que o Interceptor vai gerar
	nInternR:=GetMv("TI_RCVJ0NR"  , .F.,  6 ) //Numero de Threads que o Interceptor vai gerar para recebimento
	lInterE	:=GetMv("TI_RCVJ00E"  , .F., .T.) //Processa o Recebimento do Interceptor
	lSendFile:=GetMv("TI_RCVJ00F" , .F., .T.) //Processa o Arquivos do Interceptor
	//Inicia o Interceptor
	oReceptor:=TIINTERCEPTOR():New()
	oReceptor:lMultiThread := lInterT

	IF lInterT
		oReceptor:nMaxThreads  := nInternT
		oReceptor:nMaxThrdsR   := nInternR
	EndIF

	//Prepara e Envia Todas as Requests de Recebimento
	IF lInterR
		FWMonitorMsg("***** Inicio Rodando Interceptors Recebimento ***** " )
		oReceptor:PrepareReceiveAll() //p37_tpreq =2
		FWMonitorMsg( "***** Termino Rodando Interceptors Recebimento ***** ")
	EndIF
	IF lSendFile
		FWMonitorMsg("***** Inicio Rodando Interceptors Envio e Arquivos ***** " )
		oReceptor:GetFiles()
		FWMonitorMsg( "***** Termino Rodando Interceptors Envio e Arquivos ***** ")
	EndIF
	FREEOBJ( oReceptor )
	oReceptor := nIL
	RpcClearEnv()
Return
/*/{Protheus.doc} TIINTJ0D()
	Funcao Job Responsavel por apagar as linhas da P37 que ja foram processadas de acordo com o cadastro e preenchimento os campos na tabela P36
	@type  Function
	<AUTHOR> Menabue Lima
	@since 05/10/2021
	@version 1.0
	/*/ 
User Function TIINTDEL(cEmp,cFil)
	Local oReceptor	:= Nil

	If Select("SM0") == 0
		RpcSetEnv( cEmp, cEmp)
	EndIf

	//Inicia o Interceptor
	oReceptor:=TIINTERCEPTOR():New()
	//Roda Metodo que apaga a p37 conforme cadastro
	oReceptor:deleteHistoryData()
	FREEOBJ( oReceptor )
	oReceptor := nIL
Return
