#Include 'Protheus.ch'


User Function TGetImpF()
Local lRet      := .T.
Local aArea     := GetArea()
Local cFileImp  := cGetFile('Arquivo CSV|*.csv','Todos os Drives',0,'C:\Dir\',.T.,GETF_LOCALFLOPPY+GETF_LOCALHARD+GETF_NETWORKDRIVE,.T.)

If !File( cFileImp )
  MsgStop( 'Arquivo Indicado para ser Importado Nao Foi Localizado!', 'Verifique' )
  lRet := .F.
Else
 Processa( { || lRet := PrcFileImp( cFileImp ) },'Aguarde','Processando Registros...' )  
EndIf

RestArea( aArea )
Return lRet

Static Function PrcFileImp( cFileImp )
Local lRet          := .T.
Local nCount        := 0
Local nTotReg       := 0
Local aLinAux       := {}

Local cIdent        := Nil
Local cLinAnt       := Nil
Local cLinProx      := Nil
Local cLinhaM100    := Nil
Local cLinhaTmp     := Nil
Local aTabCpo       := {}
Local nHdl          := Ft_FUse( cFileImp )

dbSelectArea( 'ZXO' )
ZXO->( dbOrderNickName( 'ZXOREVISAO' ) )

dbSelectArea( 'ZPX' )
ZPX->( dbOrderNickName( 'ZPXREVISAO' ) )


If nHdl <= 0
  MsgStop( 'Ocorreu um Erro Na Abertura do Arquvivo [ '+ cFileImp +' ]' )
  lRet := .F.
        
Else
  Ft_FGoTop()
  nTotReg := Ft_FLastRec()
  lRet := nTotReg > 0
  Ft_FGoTop()
        
EndIf

If !lRet
  MsgStop( 'O Arquvivo [ '+ cFileImp +' ] Nao Possui Registros...' )
Else
  ProcRegua( 0 )
  Ft_FGoTop()
  While !FT_FEof()
    nCount ++
    cLinhaTmp := Ft_FReadLn()
    IncProc( '[ '+ cValToChar( nCount )+' ][ '+ cValToChar( nTotReg )+' ]-Processando...' )
    ConOut( '[ '+ cValToChar( nCount )+' ][ '+ cValToChar( nTotReg )+' ]-Processando...' )
    
    If !Empty( cLinhaTmp )
      cIdent:= SubStr( cLinhaTmp, 1, 4 )
    
      If Len( cLinhaTmp ) < 1023
        cLinhaM100  := cLinhaTmp
      Else
        cLinAnt     := cLinhaTmp
        cLinhaM100  += cLinAnt
        Ft_FSkip()
        
        cLinProx := Ft_FReadLn()
        If Len( cLinProx ) >= 1023 .And. SubStr( cLinProx, 1, 4 ) <> cIdent
          While Len( cLinProx ) >= 1023 .And. SubStr( cLinProx, 1,4 ) <> cIdent .And. !Ft_FEof()
            cLinhaM100 += cLinProx
                
            Ft_FSkip()
            cLinProx := Ft_FReadLn()
            If Len( cLinProx ) < 1023 .And. SubStr( cLinProx, 1, 4 ) <> cIdent
              cLinhaM100 += cLinProx
            EndIf
                
          EndDo
        Else
          cLinhaM100 += cLinProx
        EndIf
        
      EndIf
    EndIf
    
    If nCount == 1
      aCabec  := StrToKArr( cLinhaM100, ';' )
    Else
      aLinAux := U_Str2Arr( cLinhaM100, ';',.F. )
      cTpProv := AllTrim( aLinAux[ Ascan( aCabec,'TPPROV' ) ] )
      xAlias  := IIf( cTpProv == 'PENDENTE', 'ZPX', 'ZXO' )
      aTabCpo := GetCpos( IIf( xAlias == 'ZXO', 1, 2 ) )
      
      If !TGrvRevCom( aCabec, aLinAux, xAlias, aTabCpo )
        ApMsgStop( 'ERRO ATUALIZACAO', 'ERRO' )
      EndIf

    EndIf

    FT_FSkip()
  EndDo

EndIf

Ft_FUse()
Return lRet


User Function Str2Arr(cString, cChar, lPulaLn)
Local lExit     := .F.
Local aRet      := {}
Local nPosInit  := 0
Local nPosChar  := 0
Local nPosAux   := 1
Local cAuxChar  := AllTrim( cString )

Default cString := ''
Default cChar   := ";"
Default lPulaLn := .F.

While .T.
  nPosInit := nPosAux
  nPosChar := At( cChar, cAuxChar )

  If nPosChar == nPosInit
    Aadd( aRet, Space( 1 ) )
    If lExit
      Exit
    EndIf

  ElseIf nPosChar > nPosInit
    Aadd( aRet, SubStr( cAuxChar, nPosInit, nPosChar - 1 ) )
    If lExit
      Exit
    EndIf

  ElseIf nPosChar == 0 .And. !Empty( cAuxChar )
    Aadd( aRet, SubStr( cAuxChar, nPosInit ) )
    Exit

  ElseIf nPosChar == 1 .And. Len( cAuxChar ) == 1
    Aadd( aRet, Space( 1 ) )
    Exit

  ElseIf nPosChar == 0 .And. Empty( cAuxChar )
    Exit

  EndIf
    
  If Empty( cAuxChar )
    Exit
  Else
    If Len( SubStr( cAuxChar, nPosChar + 1 ) ) == 0 .And. SubStr( cAuxChar , Len( cAuxChar ) ) == cChar
      cAuxChar := SubStr( cAuxChar, nPosChar )
      lExit := .T.
    Else
      cAuxChar := SubStr( cAuxChar, nPosChar + 1 )
    EndIf
  EndIf

EndDo

Return aRet

Static Function GetKey( aKey, xAlias )
Local nInd  := 0
Local cRet  := ''
Local aCpo  := {}

If xAlias == 'ZXO'
  aCpo := StrToKArr( ZXO->( dbNickIndexKey( 'ZXOREVISAO' ) ), '+' )
Else
  aCpo := StrToKArr( ZPX->( dbNickIndexKey( 'ZPXREVISAO' ) ), '+' )
EndIf

For nInd := 1 To Len( aKey )
  cRet += Padr( aKey[ nInd ], TamSx3( aCpo[ nInd ] )[ 01 ] )
Next nInd

Return cRet

Static Function TGrvRevCom( aCabec, aLinAux, xAlias, aTabCpo )
Local lRet     := .T.
Local lFound   := .F.
Local lUpdate  := .F.
Local xNamCpo  := Nil
Local xValCpo  := Nil
Local xValPla  := Nil

Local nInd     := 0
Local aArea    := GetArea()
Local aPCCArea := PCC->( GetArea() )
Local aPCDArea := PCD->( GetArea() )
Local aZPXArea := ZPX->( GetArea() )
Local aZXOArea := ZXO->( GetArea() )
Local nIdSeq   := Val( aLinAux[ Ascan( aCabec, 'IDSEQ' ) ] )
Local cKeyOri  := aLinAux[ Ascan( aCabec, 'LOOKUP' ) ]

dbSelectArea( 'PCC' )
PCC->( dbSetOrder( 4 ) )

dbSelectArea( 'PCD' )
PCD->( dbSetOrder( 1 ) )

dbSelectArea( 'ZPX' )
ZPX->( dbOrderNickName( 'ZPXIDSEQ' ) )

dbSelectArea( 'ZXO' )
ZXO->( dbOrderNickName( 'ZXOIDSEQ' ) )

Begin Transaction

If !( xAlias )->( dbSeek( xFilial( xAlias ) + Str( nIdSeq, 17, 0 ) ) )
  disarmTransaction()
  lRet := .F.
Else
  For nInd := 1 To Len( aTabCpo )
    xNamCpo := aTabCpo[ nInd ][ 02 ]
    xValCpo := IIf( TamSx3( xNamCpo )[ 03 ] == 'N', ( xAlias )->( FieldGet( FieldPos( xNamCpo ) ) ),IIf( TamSx3( xNamCpo )[ 03 ] == 'D', ( xAlias )->( FieldGet( FieldPos( xNamCpo ) ) ), AllTrim( ( xAlias )->( FieldGet( FieldPos( xNamCpo ) ) ) ) ) )
    xValPla := IIf( TamSx3( xNamCpo )[ 03 ] == 'N', Val( StrTran( aLinAux[ Ascan( aCabec, aTabCpo[ nInd ][ 01 ] ) ], ',', '.' ) ), IIf( TamSx3( xNamCpo )[ 03 ] == 'D', Stod( AllTrim( aLinAux[ Ascan( aCabec, aTabCpo[ nInd ][ 01 ] ) ] ) ) , AllTrim( aLinAux[ Ascan( aCabec, aTabCpo[ nInd ][ 01 ] ) ] ) ) )

    If !( xValCpo == xValPla )
      lUpdate := .T.
      Exit
    EndIf

  Next nInd

  If !lUpdate
    ConOut( '[ '+ xAlias +' ][ '+ Dtoc( Date() ) +' ][ '+ Time() +' ]-Sequencial [ '+ cValToChar( nIdSeq ) +' ] Nao Foi Alterado')
  Else
      //Gera PCD
    cPCDItem  := GrvPcdItem( xAlias, nIdSeq )

    RecLock( 'PCD', .T. )
    PCD->PCD_FILIAL := xFilial( 'PCD' )
    PCD->PCD_TABORI := xAlias
    PCD->PCD_IDORI  := nIdSeq
    PCD->PCD_ITEMID := cPCDItem
    PCD->PCD_DATA   := Date()
    PCD->PCD_HORA   := Time()
    PCD->PCD_USRNAM := cUserName
    PCD->( MsUnLock() )

      //Gera PCC
    lFound := !PCC->( dbSeek( xFilial( 'PCC' ) + xAlias + Str( nIdSeq, 17, 0 ) + cPCDItem ) )
    RecLock( 'PCC', lFound )
    For nInd := 1 To PCC->( FCount() )
      PCC->( FieldPut( FieldPos( PrefixoCpo('PCC')+SubStr( ( xAlias )->( FieldName( nInd ) ), 4 ) ) , ( xAlias )->( FieldGet( nInd ) ) ) )
    Next nInd
    If xAlias == 'ZPX'
        PCC->PCC_PRODUT := AllTrim( ZPX->ZPX_CODPRO )
    EndIf
    PCC->PCC_DATA   := dDataBase
    PCC->PCC_HORA   := Time()
    PCC->PCC_USRID  := __cUserId
    PCC->PCC_USRNAM := cUserName
    PCC->PCC_TABORI := xAlias
    PCC->PCC_IDORI  := nIdSeq
    PCC->PCC_KEYORI := cKeyOri
    PCC->PCC_ITEMID := cPCDItem
    PCC->PCC_FILIAL := xFilial( 'PCC' )
    PCC->( MsUnLock() )
  
      //Atualiza Alias ( ZXO / ZPX )
    RecLock( xAlias, .F. )
    For nInd := 1 To Len( aTabCpo )
      xNamCpo := aTabCpo[ nInd ][ 02 ]
      xValCpo := IIf( TamSx3( xNamCpo )[ 03 ] == 'N', ( xAlias )->( FieldGet( FieldPos( xNamCpo ) ) ),IIf( TamSx3( xNamCpo )[ 03 ] == 'D', ( xAlias )->( FieldGet( FieldPos( xNamCpo ) ) ), AllTrim( ( xAlias )->( FieldGet( FieldPos( xNamCpo ) ) ) ) ) )
      xValPla := IIf( TamSx3( xNamCpo )[ 03 ] == 'N', Val( StrTran( aLinAux[ Ascan( aCabec, aTabCpo[ nInd ][ 01 ] ) ], ',', '.' ) ), IIf( TamSx3( xNamCpo )[ 03 ] == 'D', Stod( AllTrim( aLinAux[ Ascan( aCabec, aTabCpo[ nInd ][ 01 ] ) ] ) ) , AllTrim( aLinAux[ Ascan( aCabec, aTabCpo[ nInd ][ 01 ] ) ] ) ) )
      If !( xValCpo == xValPla )
        ( xAlias )->( FieldPut( FieldPos( xNamCpo ), xValPla ) )
      EndIf
    Next nInd
    ( xAlias )->( FieldPut( FieldPos( &( xAlias )->( xAlias+'_GERADO' ) ) , 'Z' ) )
    ( xAlias )->( FieldPut( FieldPos( &( xAlias )->( xAlias+'_REVISA' ) ) , Soma1( ( xAlias )->( FieldGet( FieldPos( &( xAlias )->( xAlias+'_REVISA' ) ) ) ) ) ) )
    ( xAlias )->( MsUnLock() )
  EndIf

EndIf


End Transaction


RestArea( aZXOArea )
RestArea( aZPXArea )
RestArea( aPCDArea )
RestArea( aPCCArea )
RestArea( aArea )	
Return lRet


Static Function StrDePara()
Local aCampos   := {}

Aadd( aCampos, { 'CODIGOAR'     , { 'ZXO_VEND'  , 'ZPX_VEND'    } } )
Aadd( aCampos, { 'CCUSTO'       , { 'ZXO_CCUSTO', 'ZPX_CCUSTO'  } } )
Aadd( aCampos, { 'ITEMCC'       , { 'ZXO_ITEMCC', 'ZPX_ITEMCT'  } } )
Aadd( aCampos, { 'CODPAP'       , { 'ZXO_CODPAP', ''            } } )
Aadd( aCampos, { 'PERCVENDA'    , { 'ZXO_PCOM'  ,	'ZPX_PCOM'    } } )
Aadd( aCampos, { 'PERCTERRIT'   , { 'ZXO_PCOM2' , 'ZPX_PCOM2'   } } )
Aadd( aCampos, { 'PERCDEMO'     , { 'ZXO_PCOM3' , 'ZPX_PCOM3'   } } )
Aadd( aCampos, { 'VLRCOMIS'     , { 'ZXO_SALDO' , 'ZPX_COMISS'  } } )
Aadd( aCampos, { 'BASECOMIS'    , { 'ZXO_BASE'  , 'ZPX_BASE'    } } )
Aadd( aCampos, { 'COMPROV'      , { 'ZXO_COMISS', 'ZPX_COMISS'  } } )
Aadd( aCampos, { 'PARTIC'       , { 'ZXO_PARTIC', ''            } } )
Aadd( aCampos, { 'EMPRESA'      , { 'ZXO_EMPFAT', 'ZPX_EMPFAT'  } } )
Aadd( aCampos, { 'OBSERVACAO'   , { 'ZXO_OBS'   , 'ZPX_OBS'     } } )


/*
Aadd( aCampos, { 'CLIENTE'      , { 'ZXO_CLIENT', 'ZPX_CLIENT'  } } )
Aadd( aCampos, { 'LOJA'         , { 'ZXO_LOJA'  , 'ZPX_LOJA'    } } )
Aadd( aCampos, { 'NF'           , { 'ZXO_DOC'   , 'ZPX_DOC'     } } )
Aadd( aCampos, { 'SERIE'        , { 'ZXO_SERIE' , 'ZPX_SERIE'   } } )
Aadd( aCampos, { 'PARCELA'      , { 'ZXO_PARCEL', 'ZPX_PARCEL'  } } )
Aadd( aCampos, { 'PRODUTO'      , { 'ZXO_PRODUT', 'ZPX_CODPRO'  } } )
Aadd( aCampos, { 'ITEMNF'       , { 'ZXO_ITEM'  , 'ZPX_ITEM'    } } )
Aadd( aCampos, { 'EMISSAO'      , { 'ZXO_EMISS' , 'ZPX_EMISS'   } } )
Aadd( aCampos, { 'F2IMPDEV'     , { 'ZXO_IMPOST', 'ZPX_IMPOST'  } } )
Aadd( aCampos, { 'ORIGEM'       , { 'ZXO_ORIGEM', 'ZPX_ORIGEM'  } } )
Aadd( aCampos, { 'CONTRATO'     , { 'ZXO_CONTRA', ''            } } )
Aadd( aCampos, { 'DTCONTRA'     , { 'ZXO_DTCONT', ''            } } )
Aadd( aCampos, { 'PROJETO'      , { 'ZXO_PROJET', ''            } } )
Aadd( aCampos, { 'E1TIPO'       , { 'ZXO_TIPO'  , 'ZPX_TIPO'    } } )   
Aadd( aCampos, { 'REDUTOR'      , { 'ZXO_REDUTO', 'ZPX_REDUT'  } } )
Aadd( aCampos, { 'DTBAIXA'      , { ''          , 'ZPX_BAIXA'   } } )
Aadd( aCampos, { 'BANCO'        , { ''          , 'ZPX_BANCO'   } } )
Aadd( aCampos, { 'DOCENTDAT'    , { 'ZXO_DOCENT', ''            } } )
Aadd( aCampos, { 'PERCTOTAL'    , { ''  , ''    } } )
Aadd( aCampos, { 'QTDENFS'      , { 'ZXO_RECORR', ''            } } )
Aadd( aCampos, { 'REFERENCIA'   , { 'ZXO_REFER' , ''            } } )
Aadd( aCampos, { 'DESCDU'       , { 'ZXO_DESCDU', ''            } } )
Aadd( aCampos, { 'GC'           , { 'ZXO_GC'    , 'ZPX_GC'      } } )
Aadd( aCampos, { 'VERTMV'       , { 'ZXO_VERTMV', ''            } } )
Aadd( aCampos, { 'EMPORI'       , { 'ZXO_EMPORI', 'ZPX_EMPORI'  } } )
Aadd( aCampos, { 'MESBASE'      , { 'ZXO_MESBSE', ''            } } )
Aadd( aCampos, { 'PROPITEM'     , { 'ZXO_PROPIT', ''            } } )
Aadd( aCampos, { 'DTUPLOAD'     , { 'ZXO_DTUPLD', ''            } } )
Aadd( aCampos, { 'REFORI'       , { 'ZXO_REFORI', ''            } } )
Aadd( aCampos, { 'FATOR'        , { 'ZXO_FATOR' , ''            } } )
Aadd( aCampos, { 'PROPBX'       , { ''          , 'ZPX_PROPBX'  } } ) 
Aadd( aCampos, { 'STATUS'       , { 'ZXO_STATUS', ''            } } )

*/

Return aCampos

Static Function GetCpos( nOpcx )
Local nInd  := 0
Local aRet  := {}
Local aCpo  := StrDePara()

For nInd := 1 To Len( aCpo )
  If Empty( aCpo[ nInd ][ 02 ][ nOpcx ] )
    Loop
  Else
    Aadd( aRet, { aCpo[ nInd ][ 01 ] , aCpo[ nInd ][ 02 ][ nOpcx ] } )
  EndIf

Next nInd

Return aRet

Static Function GrvPcdItem( xAlias, nIdSeq )
Local cRet      := Nil
Local cQuery    := Nil
Local cPCDAlias := GetNextAlias()
Local cItem     := StrZero( 0, TamSx3( 'PCD_ITEMID' )[ 01 ] )

cQuery := " SELECT DISTINCT COALESCE( PCD.PCD_TABORI, 'XXX' ) PCD_TABORI, COALESCE( PCD.PCD_IDORI, 000000 ) PCD_IDORI , COALESCE( MAX( PCD.PCD_ITEMID ), '0000' ) ITEM "
cQuery += " FROM "+ RetSQLName( 'PCD' )+" PCD "
cQuery += " WHERE PCD.PCD_FILIAL = '"+ xFilial( 'PCD' ) +"' "+CRLF
cQuery += "     AND PCD.PCD_TABORI = '"+ xAlias +"' "+CRLF
cQuery += "     AND PCD.PCD_IDORI = "+ cValToChar( nIdSeq ) +" "+CRLF
cQuery += "     AND PCD.D_E_L_E_T_ = ' ' "+CRLF	
cQuery += " GROUP BY PCD.PCD_TABORI, PCD.PCD_IDORI "+CRLF
cQuery += " ORDER BY PCD_TABORI, PCD_IDORI "+CRLF
cQuery += " FETCH FIRST 1 ROWS ONLY "

dbUseArea( .T., __cRdd, TcGenQry(,,cQuery ), cPCDAlias, .T., .F. )
dbSelectArea( cPCDAlias )
cRet := IIf( ( cPCDAlias )->( Eof() ), Soma1( cItem ), Soma1( ( cPCDAlias )->ITEM ) )

IIf( Select( cPCDAlias ) > 0, ( cPCDAlias )->( dbCloseArea() ), Nil )	
Return cRet