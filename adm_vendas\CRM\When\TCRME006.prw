#Include 'Protheus.ch'

/*/{Protheus.doc} TCRME006
When do campo ADZ_QTDVEN
@type function
<AUTHOR>
@since 04/03/2016
@version 1.0
@return lRet, Retorna se permite alterar campo ADJ_XNVREF 
@example
(examples)
@see (links_or_references)
/*/
User Function TCRME006()
Local aArea			:= GetArea()
Local aAreaPKG		:= PKG->(GetArea())
Local lRet			:= .F.
Local cADJ_CODAGR	:= FwFldGet("ADJ_CODAGR")
Local cADJ_CODNIV	:= FwFldGet("ADJ_CODNIV")

dbSelectArea("PKG")
dbSetOrder(1)
If (dbSeek(xFilial()+cADJ_CODAGR+cADJ_CODNIV) .and. PKG->PKG_CATREF == "2") .or. ;
	(dbSeek(xFilial()+cADJ_CODAGR+cADJ_CODNIV) .and. PKG->PKG_CATREF <> "2" .and. IsInCallStack("u_TCRMV058")) .Or. IsInCallStack("U_TCRMX22B")	
	lRet := .T.	
Endif

RestArea(aAreaPKG)
RestArea(aArea)

Return(lRet)
