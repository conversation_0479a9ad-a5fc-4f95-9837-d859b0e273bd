#INCLUDE 'TOTVS.CH'

/*/{Protheus.doc} ESPG070
	Description
	<AUTHOR>
	@example Example
	@param   [Parameter_Name],Parameter_type,Parameter_Description
	@return  cRetorno
	@table   Tables
	@since   25-08-2020
/*/
User Function ESPG070()	
	Local cChave   := ''
	Local cRetorno := ''
	
	IF !Inclui
		cChave 		:= FWxFilial('PVN') + PVP->PVP_PAIS + PVP->PVP_CODGRP
		cAlias   	:= 'PVN'
		cRetorno 	:= Posicione(cAlias, 1, cChave, cAlias + '_DESACE')
	EndIF
	
Return(cRetorno)
