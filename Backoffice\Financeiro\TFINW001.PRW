﻿#Include "Totvs.Ch"
#Include "RESTFUL.Ch"
#Include 'Protheus.ch'

Static cEmail := ""

/*/
@Function:	TFINW001
@version: 	1.00
@since: 	15/03/2019
/*/

User Function TFINW001()
Return

/*/
@Method:	GetDashSerasa 
@desc:		Metodo responsavel por retornar os dados sinteticos de envio e retorno do serasa 
@author:	<PERSON><PERSON><PERSON>
@version: 	1.00
@since: 	15/03/2019
/*/

WsRestFul GetDashSerasa Description "Consulta dashboard Serasa."
	 
	WsData dataini	AS String
    WsData datafim	AS String
    
    WsMethod Get Description "Consulta dashboard Serasa." WsSyntax "/GetDashSerasa"
		 
End WsRestFul

WsMethod Get WsReceive dataini, datafim WsService GetDashSerasa

Local cDataIni  := ""
Local cDataFim  := ""
Local cQuery    := ""
Local cAlsSer   := CriaTrab(Nil,.F.)
Local nLinha    := 1
Local cJson     := ""
Local nQtdRet   := 0
Local nQtdOk    := 0
Local nQtdErr   := 0

    // ----------------------------------
	// Define o tipo de retorno do metodo
	// ----------------------------------
	::SetContentType("application/json")

    If Len(::aURLParms) > 0
        cDataini  := ::aURLParms[1]
        cDatafim  := ::aURLParms[2]
    Else
    	cDataini  := IIf( ::dataini == Nil, "" , ::dataini ) 
        cDatafim  := IIf( ::datafim == Nil, "" , ::datafim ) 
	EndIf

    If Empty(cDataIni) .Or. Empty(cDataFim)
        cJSON := RetJson("Variavel nao localizada", "Nao localizado dataini ou datafim")        
        
        Self:SetResponse( cJson )

        Return(.T.)
    Else
        
        cQuery := " SELECT COUNT(DISTINCT (PRH_CGC )) QTDRET, 0 QTDOK, 0 QTDERR FROM " + RetSqlName("PRH") "
        cQuery += " WHERE PRH_DTPROP >= '" + cDataini + "' AND PRH_DTPROP <= '" + cDataFim + "' AND D_E_L_E_T_ = ' ' "
        cQuery += " UNION ALL " 
        cQuery += " SELECT 0 QTDRET, COUNT(DISTINCT (PRH_CGC )) QTDOK, 0 QTDERR FROM " + RetSqlName("PRH") "
        cQuery += " WHERE PRH_DTPROP >= '" + cDataini + "' AND PRH_DTPROP <= '" + cDataFim + "' AND D_E_L_E_T_ = ' '  AND PRH_CODERR = ' ' "
        cQuery += " UNION ALL " 
        cQuery += " SELECT 0 QTDRET, 0 QTDOK, COUNT(DISTINCT (PRH_CGC )) QTDERR FROM " + RetSqlName("PRH") "
        cQuery += " WHERE PRH_DTPROP >= '" + cDataini + "' AND PRH_DTPROP <= '" + cDataFim + "' AND D_E_L_E_T_ = ' '  AND PRH_CODERR <> ' ' "

        dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsSer,.T.,.T.)
        
        While !(cAlsSer)->(Eof())
           
           nQtdRet += (cAlsSer)->QTDRET
           nQtdOk  += (cAlsSer)->QTDOK
           nQtdErr += (cAlsSer)->QTDERR
        
           (cAlsSer)->(DbSkip())
        End
        
        
        cJson := '{'
        cJson += '"enviados":"' + AllTrim(Str(nQtdRet))  + '",'
        cJson += '"sucesso":"'  + AllTrim(Str(nQtdOk ))   + '",'
        cJson += '"erro":"'     + AllTrim(Str(nQtdErr))  + '"
        cJson += '}'
        
    EndIf

    If Empty(cJson)
       cJSON := RetJson("Sem resultado", "Nenhum registro localizado de acordo com o filtro")        
    EndIf

    (cAlsSer)->(dbCloseArea())

    Self:SetResponse( cJson )

Return(.T.)







/*/
@Method:	GetClienteSerasa
@desc:		Metodo responsavel por retornar os dados sinteticos de envio e retorno do serasa 
@author:	Mauricio Madureira
@version: 	1.00
@since: 	15/03/2019
/*/

WsRestFul GetClienteSerasa Description "Consulta dashboard Serasa."
	 
	WsData CpfCgc  	AS String
	WsData dataini	AS String
    WsData datafim	AS String
    
    WsMethod Get Description "Consulta Cliente Serasa." WsSyntax "/GetClienteSerasa"
		 
End WsRestFul

WsMethod Get WsReceive CpfCgc, dataini, datafim WsService GetClienteSerasa

Local cCpfCgc     := ""
Local cDataini  := ""
Local cDatafim    := ""
Local cAlsSer   := CriaTrab(Nil,.F.)
Local nLinha    := 1
Local cJson     := ""

    // ----------------------------------
	// Define o tipo de retorno do metodo
	// ----------------------------------
	::SetContentType("application/json")

    If Len(::aURLParms) > 0
        cCpfCgc   := ::aURLParms[1]
        cDataini  := ::aURLParms[2]
        cDatafim  := ::aURLParms[3]
    Else
    	cCpfCgc   := IIf( ::CpfCgc  == Nil, "" , ::CpfCgc  )
    	cDataini  := IIf( ::dataini == Nil, "" , ::dataini ) 
        cDatafim  := IIf( ::datafim == Nil, "" , ::datafim ) 
	EndIf

    If Empty(cCpfCgc)
       cJSON := RetJson("variavel nao localizada", "variavel CpfCgc nao encontrada")
       Self:SetResponse( cJson )
       Return(.T.)
    Else
        
        cQuery := " SELECT PRH.PRH_IDJOB,PRH.PRH_STATUS,PRH.PRH_CODIGO,PRH.PRH_LOJA,PRH.PRH_CLIENT, "
        cQuery += " PRH.PRH_CGC, PRH.PRH_PESSOA, PRH.PRH_PRPANT, PRH.PRH_DTANTP,PRH.PRH_DTSANT,"
        cQuery += " PRH.PRH_PROPEN,PRH.PRH_DTPROP,PRH.PRH_CODERR, PRH.PRH_DESCER, PRP.PRP_DTEXEC, PRP.PRP_NOMEAR " 
        cQuery += " FROM " + RetSqlName("PRH") + " PRH "
        cQuery += " INNER JOIN " + RetSqlName("PRP") + " PRP ON PRP.PRP_IDJOB = PRH.PRH_IDJOB "  
        cQuery += " WHERE PRH.PRH_CGC = '" + cCpfCgc + "'"  
        
        If !Empty(cDataIni)
            cQuery += " AND PRH.PRH_DTPROP >= '" + cDataini + "'"
        EndIf    
        
        If !Empty(cDataFim)
            cQuery += " AND PRH.PRH_DTPROP <= '" + cDataFim + "'"
        EndIf
        
        cQuery += " AND PRH.D_E_L_E_T_ = ' ' AND PRP.D_E_L_E_T_ = ' '"
        cQuery += " ORDER BY PRH.PRH_IDJOB "

        dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsSer,.T.,.T.)
        
        While !(cAlsSer)->(Eof())

            If nLinha == 1
                cJson := '{'
                cJson += '"hasNext":"false",'
                cJson += '"items":['
            EndIf
            
            cFile   := AllTrim ( (cAlsSer)->PRP_NOMEAR )
            cCaract := Len(cFile) - len(strtran(cFile,"\",""))             
            While cCaract > 0 
                  
                  cCaract := cCaract - 1
                  cIndice := At("\", cFile)              
                  cFile   := Right(cFile, Len(cFile) -cIndice )
                  
            End
            
            cJson += '{'
            cJson += '"idjob":"'      +    AllTrim((cAlsSer)->PRH_IDJOB)     + '",'
            cJson += '"dtexec":"'     +    AllTrim((cAlsSer)->PRP_DTEXEC)    + '",'
            cJson += '"nomearquivo":"'+           cFile                      + '",'
            cJson += '"idjob":"'      +    AllTrim((cAlsSer)->PRH_IDJOB)     + '",'
            cJson += '"status":"'     + If((cAlsSer)->PRH_STATUS="E","Envio","Retorno") + '",'
            cJson += '"cliente":"'    +    AllTrim((cAlsSer)->PRH_CODIGO)    + '",'
            cJson += '"loja":"'       +    AllTrim((cAlsSer)->PRH_LOJA)	     + '",'
            cJson += '"nomecli":"'    +   AllTrim((cAlsSer)->PRH_CLIENT)	 + '",'
            cJson += '"doc":"'        +   AllTrim((cAlsSer)->PRH_CGC)	     + '",'
            cJson += '"tipo":"'       + If((cAlsSer)->PRH_PESSOA="F","Pessoa Fisica","Pessoa Juridica")	+ '",'
            cJson += '"prpant":"'     + If((cAlsSer)->PRH_PRPANT="B","Baixa",If((cAlsSer)->PRH_PRPANT="M","Media","Alta"))+ '",'
            cJson += '"dtantp":"'     + If(!Empty((cAlsSer)->PRH_DTANTP),dtoc(stod((cAlsSer)->PRH_DTANTP)), "") + '",'
            cJson += '"dtsant":"'     + If(!Empty((cAlsSer)->PRH_DTSANT),dtoc(stod((cAlsSer)->PRH_DTSANT)), "") + '",'
            cJson += '"propensao":"'  +           (cAlsSer)->PRH_PROPEN	     + '",'
            cJson += '"dtpropensao":"'+ If(!Empty((cAlsSer)->PRH_DTPROP),dtoc(stod((cAlsSer)->PRH_DTPROP)), "") + '",'
            cJson += '"coderro":"'    +    AllTrim((cAlsSer)->PRH_CODERR)	     + '",'
            cJson += '"descricao":"'  +   AllTrim((cAlsSer)->PRH_DESCER)     + '"
            cJson += '},'
            
            nLinha++

            (cAlsSer)->(DbSkip())
        
        End

    EndIf

    If Empty(cJson)
       cJSON := RetJson("Sem registros localizados", "Nao encontrado registros com o filtro selecionado")
     Else
        cJson := Left(cJson, RAT(",", cJson) - 1)
        cJson += ']'
        cJson += '}'        
    EndIf

    (cAlsSer)->(dbCloseArea())

    Self:SetResponse( cJson )

Return(.T.)





/*/
@Method:	GetDetailStatus
@desc:		Metodo responsavel por retornar os dados sinteticos de envio e retorno do serasa 
@author:	Mauricio Madureira
@version: 	1.00
@since: 	15/06/2019
/*/

WsRestFul GetDetailStatus Description "Consulta detalhes por status."
	 
	WsData dataini AS String
	WsData datafim AS String
	WsData status  AS String
	WsData RegPorPag AS Integer
	WsData Pagina    AS Integer
    
    WsMethod Get Description "Consulta Detalhes por Status." WsSyntax "/GetDetailStatus"
		 
End WsRestFul

WsMethod Get WsReceive dataini, datafim, status, RegPorPag, Pagina WsService GetDetailStatus
Local cDataini   := ""
Local cDataFim   := ""
Local cStatus   := ""
Local nRegPorPag := 0
Local nPagina    := 0
Local cAlsSer   := CriaTrab(Nil,.F.)
Local nLinha    := 1
Local cJson     := ""

    // ----------------------------------
	// Define o tipo de retorno do metodo
	// ----------------------------------
	::SetContentType("application/json")

    If Len(::aURLParms) > 0
        cDataini  := ::aURLParms[1]
        cDatafim  := ::aURLParms[2]
        cStatus   := ::aURLParms[3]
        nRegPorPag:= ::aURLParms[4]
        nPagina   := ::aURLParms[5]
    Else
    	cDataIni  := IIf( ::DataIni   == Nil, "" , ::DataIni    )
    	cDataFim  := IIf( ::DataFim   == Nil, "" , ::DataFim    )
    	cStatus   := IIf( ::status    == Nil, "" , ::status     )
    	nRegPorPag:= IIf( ::RegPorPag == Nil, 0  , ::RegPorPag  )
    	nPagina   := IIf( ::Pagina    == Nil, 0  , ::Pagina     )
	EndIf

    If Empty(cDataini) .or.  Empty(cDatafim) .or. Empty(nRegPorPag) .or. Empty(nPagina)
       cJSON := RetJson("variavel nao localizada", "variavel data e controle de paginacao nao informada")
       Self:SetResponse( cJson )
       Return(.T.)
    Else
        
        /*cQuery := " SELECT ROWNUM + (" + Str(nRegPorPag) + " * (PAGINA - 1)) AS QTDPAG, B.* " 
        cQuery += " FROM (SELECT COUNT(DISTINCT (PRH_CGC )) OVER() AS TOTALREG, "
        cQuery += " TRUNC((ROW_NUMBER() OVER(ORDER BY PRH_CGC) - 1) / " + Str(nRegPorPag)+ "  ) + 1 AS PAGINA, TMP.* "
        cQuery += " FROM ( "*/ 
        cQuery := " SELECT ROWNUM + (" + Str(nRegPorPag) + " * (PAGINA - 1)) AS QTDPAG, B.* " 
        cQuery += " FROM (SELECT COUNT(DISTINCT (PRH_CGC )) OVER() AS TOTALREG, "
        cQuery += " TRUNC((ROW_NUMBER() OVER(ORDER BY PRH_CGC) - 1) / " + Str(nRegPorPag)+ "  ) + 1 AS PAGINA, TMP.* "
        cQuery += " FROM ( " 
        cQuery += " SELECT DISTINCT PRH.PRH_CGC,PRH.PRH_FILIAL "

        /*cQuery += " SELECT PRH_IDJOB,PRH_FILIAL, PRH_STATUS, PRH_CODIGO, PRH_LOJA,"
        cQuery += " PRH_CGC, PRH_PESSOA, PRH_PRPANT, PRH_DTANTP,PRH_DTSANT,"
        cQuery += " PRH_PROPEN,PRH_DTPROP,PRH_CODERR, PRH_DESCER " */
        cQuery += " FROM " + RetSqlName("PRH") + " PRH "
        cQuery += " WHERE "
        cQuery += "     PRH_DTPROP >= '" + cDataini + "'"
        cQuery += " AND PRH_DTPROP <= '" + cDataFim + "'"
        
        Do Case
           // OK
           Case cStatus == "1"
                cQuery += " AND PRH_CODERR = ' ' "
           
           // ERRO
           Case cStatus == "2"
                cQuery += " AND PRH_CODERR <> ' ' "            
                
           OtherWise 
                // Retornar todos os status ( Erro ou Ok )
                                 
        EndCase 
        
        cQuery += " AND D_E_L_E_T_ = ' ' ) TMP ) "
        cQuery += " B WHERE PAGINA = " + Str(nPagina)

        dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsSer,.T.,.T.)
       dbSelectArea("PRH")
       DbSetOrder(5)
        While !(cAlsSer)->(Eof())
            PRH->(DBSEEK((cAlsSer)->PRH_FILIAL+(cAlsSer)->PRH_CGC))
            If nLinha == 1
                cJson := '{'
                cJson += '"hasNext":"false",'
                cJson += '"totalreg":"' + AllTrim(Str((cAlsSer)->TOTALREG)) + '",'    
                cJson += '"RegPorPag":"'+ AllTrim(Str(nRegPorPag))          + '",'
                cJson += '"Pagina":"'   + AllTrim(Str(nPagina))             + '",'
                cJson += '"items":['
            EndIf
             
            cJson += '{'
            cJson += '"idjob":"'      +     AllTrim((PRH->PRH_IDJOB)) 	     + '",'
            cJson += '"dtexec":"'     +          DTOS( PRH->PRH_DTSANT)	     + '",'
            cJson += '"idjob":"'      +   AllTrim((PRH->PRH_IDJOB))      + '",'
            cJson += '"status":"'     + If(PRH->PRH_STATUS="E","Envio","Retorno") + '",'
            cJson += '"cliente":"'    +           PRH->PRH_CODIGO	     + '",'
            cJson += '"loja":"'       +           PRH->PRH_LOJA	     + '",'
            cJson += '"nomecli":"'    +   AllTrim(PRH->PRH_CLIENT)	 + '",'
            cJson += '"doc":"'        +   AllTrim(PRH->PRH_CGC)	     + '",'
            cJson += '"tipo":"'       + If(PRH->PRH_PESSOA="F","Pessoa Fisica","Pessoa Juridica")	+ '",'
            cJson += '"prpant":"'     + If(PRH->PRH_PRPANT="B","Baixa",If(PRH->PRH_PRPANT="M","Media","Alta"))+ '",'
            cJson += '"dtantp":"'     + If(!Empty(PRH->PRH_DTANTP),dtoc((PRH->PRH_DTANTP)), "") + '",'
            cJson += '"dtsant":"'     + If(!Empty(PRH->PRH_DTSANT),dtoc((PRH->PRH_DTSANT)), "") + '",'
            cJson += '"propensao":"'  +           PRH->PRH_PROPEN	     + '",'
            cJson += '"dtpropensao":"'+ If(!Empty(PRH->PRH_DTPROP),dtoc((PRH->PRH_DTPROP)), "") + '",'
            cJson += '"coderro":"'    +   AllTrim(PRH->PRH_CODERR)     + '",'
            cJson += '"descricao":"'  +   AllTrim(PRH->PRH_DESCER)     + '"
            cJson += '},'
            
            nLinha++

            (cAlsSer)->(DbSkip())
        
        End

    EndIf

    If Empty(cJson)
       cJSON := RetJson("Sem registros localizados", "Nao encontrado registros com o filtro selecionado")
     Else
        cJson := Left(cJson, RAT(",", cJson) - 1)
        cJson += ']'
        cJson += '}'        
    EndIf

    (cAlsSer)->(dbCloseArea())

    Self:SetResponse( cJson )

Return(.T.)






/*/
@Method:	GetBuscaAvanc
@desc:		Metodo responsavel por retornar os dados sinteticos de envio e retorno do serasa 
@author:	Mauricio Madureira
@version: 	1.00
@since: 	15/03/2019
/*/

WsRestFul GetBuscaAvanc Description "Filtro Avan�ado."
	 
	WsData pfpj      AS String
	WsData status    AS String
	WsData dataini   AS String
	WsData datafim   AS String
	WsData cpfcnpj   AS String
	WsData RegPorPag AS Integer
	WsData Pagina    AS Integer 
	
    
    WsMethod Get Description "Filtro Avan�ado." WsSyntax "/GetBuscaAvanc"
		 
End WsRestFul

WsMethod Get WsReceive pfpj, status, dataini, datafim, cpfcnpj, RegPorPag, Pagina WsService GetBuscaAvanc
Local cPfpj      := ""
Local cStatus    := ""
Local cDataini   := ""
Local cDataFim   := ""
Local cCpfCnpj   := ""
Local nRegPorPag := 0
Local nPagina    := 0
Local cAlsSer   := CriaTrab(Nil,.F.)
Local nLinha    := 1
Local cJson     := ""

    // ----------------------------------
	// Define o tipo de retorno do metodo
	// ----------------------------------
	::SetContentType("application/json")

    If Len(::aURLParms) > 0
        cPfpj     := ::aURLParms[1]
        cStatus   := ::aURLParms[2]
        cDataini  := ::aURLParms[3]
        cDatafim  := ::aURLParms[4]
        cCpfcnpj  := ::aURLParms[5]
        nRegPorPag:= ::aURLParms[6]
        nPagina   := ::aURLParms[7]
    Else
    	cPfpj     := IIf( ::pfpj      == Nil, "" , ::pfpj       )
    	cStatus   := IIf( ::status    == Nil, "" , ::status     )
    	cDataIni  := IIf( ::DataIni   == Nil, "" , ::DataIni    )
    	cDataFim  := IIf( ::DataFim   == Nil, "" , ::DataFim    )
    	cCpfCnpj  := IIf( ::CpfCnpj   == Nil, "" , ::CpfCnpj    )
    	nRegPorPag:= IIf( ::RegPorPag == Nil, 0  , ::RegPorPag  )
    	nPagina   := IIf( ::Pagina    == Nil, 0  , ::Pagina     )
	EndIf

    If Empty(cDataIni) .or. Empty(cDataFim) .or. Empty(nRegPorPag) .or. Empty(nPagina)
       cJSON := RetJson("variavel nao localizada", "Status, data ou controle de paginacao nao informada")
       Self:SetResponse( cJson )
       Return(.T.)
    Else
                
        // Para pegar os registros
        cQuery := " SELECT ROWNUM + (" + Str(nRegPorPag) + " * (PAGINA - 1)) AS QTDPAG, B.* " 
        cQuery += " FROM (SELECT COUNT(DISTINCT (PRH_CGC )) OVER() AS TOTALREG, "
        cQuery += " TRUNC((ROW_NUMBER() OVER(ORDER BY PRH_CGC) - 1) / " + Str(nRegPorPag)+ "  ) + 1 AS PAGINA, TMP.* "
        cQuery += " FROM ( " 
        cQuery += " SELECT DISTINCT PRH.PRH_CGC,PRH.PRH_FILIAL "

        /*cQuery += " SELECT PRH_IDJOB, PRH_STATUS, PRH_CODIGO, PRH_LOJA,"
        cQuery += " PRH_CLIENT, PRH_CGC, PRH_PESSOA, PRH_PRPANT, PRH_DTANTP,PRH_DTSANT,"
        cQuery += " PRH_PROPEN,PRH_DTPROP,PRH_CODERR, PRH_DESCER " */
        cQuery += " FROM " + RetSqlName("PRH") + " PRH "
        cQuery += " WHERE "
        cQuery += "     PRH_DTPROP >= '" + cDataini + "'"
        cQuery += " AND PRH_DTPROP <= '" + cDataFim + "'" 
        
        // Informou se � F ou J 
        If cPfPj <> "A"
           cQuery += " AND PRH_PESSOA = '" + cPfpj + "'"
        EndIf    
        
        If !Empty(cCpfCnpj)
            cQuery += " AND PRH_CGC = '" + cCpfCnpj + "'"        
        EndIf
        
        Do Case
           // OK
           Case cStatus == "1"
                cQuery += " AND PRH_CODERR = ' ' "
           
           // ERRO
           Case cStatus == "2"
                cQuery += " AND PRH_CODERR <> ' ' "            
                
           OtherWise 
                // Retornar todos os status ( Erro ou Ok )
                                 
        EndCase 
        cQuery += " AND D_E_L_E_T_ = ' ') TMP ) 
        cQuery += " B WHERE PAGINA = " + Str(nPagina) 
         
        dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsSer,.T.,.T.)
        dbSelectArea("PRH")
        DbSetOrder(5)
        While !(cAlsSer)->(Eof())
            PRH->(DBSEEK((cAlsSer)->PRH_FILIAL+(cAlsSer)->PRH_CGC))
            If nLinha == 1
                cJson := '{'
                cJson += '"hasNext":"false",'
                cJson += '"totalreg":"' + AllTrim(Str((cAlsSer)->TOTALREG)) + '",'    
                cJson += '"RegPorPag":"'+ AllTrim(Str(nRegPorPag))          + '",'
                cJson += '"Pagina":"'   + AllTrim(Str(nPagina))             + '",'
                cJson += '"items":['		
            EndIf
             
            cJson += '{'
            cJson += '"idjob":"'      +     AllTrim((PRH->PRH_IDJOB)) 	     + '",'
            cJson += '"dtexec":"'     +          DTOS( PRH->PRH_DTSANT)	     + '",'
            cJson += '"idjob":"'      +   AllTrim((PRH->PRH_IDJOB))      + '",'
            cJson += '"status":"'     + If(PRH->PRH_STATUS="E","Envio","Retorno") + '",'
            cJson += '"cliente":"'    +           PRH->PRH_CODIGO	     + '",'
            cJson += '"loja":"'       +           PRH->PRH_LOJA	     + '",'
            cJson += '"nomecli":"'    +   AllTrim(PRH->PRH_CLIENT)	 + '",'
            cJson += '"doc":"'        +   AllTrim(PRH->PRH_CGC)	     + '",'
            cJson += '"tipo":"'       + If(PRH->PRH_PESSOA="F","Pessoa Fisica","Pessoa Juridica")	+ '",'
            cJson += '"prpant":"'     + If(PRH->PRH_PRPANT="B","Baixa",If(PRH->PRH_PRPANT="M","Media","Alta"))+ '",'
            cJson += '"dtantp":"'     + If(!Empty(PRH->PRH_DTANTP),dtoc((PRH->PRH_DTANTP)), "") + '",'
            cJson += '"dtsant":"'     + If(!Empty(PRH->PRH_DTSANT),dtoc((PRH->PRH_DTSANT)), "") + '",'
            cJson += '"propensao":"'  +           PRH->PRH_PROPEN	     + '",'
            cJson += '"dtpropensao":"'+ If(!Empty(PRH->PRH_DTPROP),dtoc((PRH->PRH_DTPROP)), "") + '",'
            cJson += '"coderro":"'    +   AllTrim(PRH->PRH_CODERR)     + '",'
            cJson += '"descricao":"'  +   AllTrim(PRH->PRH_DESCER)     + '"
            cJson += '},'
            
            nLinha++

            (cAlsSer)->(DbSkip())
        
        End

    EndIf

    If Empty(cJson)
       cJSON := RetJson("Sem registros localizados", "Nao encontrado registros com o filtro selecionado")
     Else
        cJson := Left(cJson, RAT(",", cJson) - 1)
        cJson += ']'
        cJson += '}'        
    EndIf

    (cAlsSer)->(dbCloseArea())

    Self:SetResponse( cJson )

Return(.T.)




/*
@Method:	RetJson
@desc:		Metodo Generico de retorno de json
@author:	Mauricio Madureira
@version: 	1.00
- 1 = a��o realizada com sucesso;
- 2 = a��o n�o realizada e seguida do atributo com a mensagem informando o motivo;
- 3 = valida��o de  dados e seguida do atributo mensagem informando qual a valida��o que deve ser feita. 
@since: 	20/09/2018
*/
Static Function RetJson(cRet, cMsg, cDetail)

Default cDetail := ""

cJSON := '{'
cJSON += '"code":"'+ cRet +  '",'
cJSON += '"message":"' + cMsg + '",'
cJSON += '"detailedMessage":"' + cMsg + '"'
cJSON += '}'

Return cJson