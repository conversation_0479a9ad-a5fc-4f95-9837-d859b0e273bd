0001#STR0001#ALL#Search
0002#STR0002#ALL#Order
0003#STR0003#ALL#Prep. &Docs
0004#STR0004#ALL#Rever.Docs
0005#STR0005#ALL#Outflow Documents Elaboration
0006#STR0006#ALL#This program will conclude the distribution/services rendering 
0007#STR0007#ALL#rendering process by elaborating the goods outflow/Shipment.
0008#STR0008#ALL#goods 
0009#STR0009#ALL#Outflow/Shipment Documents Elaboration 
0010#STR0010#ALL#Docs Elaboration: 
0011#STR0011#ALL#Elaborated Loads: 
0012#STR0012#ALL#The documents below cannot be elaborated at this moment.
0013#STR0013#ALL#Do you want to try again?
0014#STR0014#ALL#Customer+Branch+Aggregator
0015#STR0015#ALL#Customer+Branch+Product
0016#STR0016#ALL#Order+Item
0017#STR0017#ALL#Customer+Branch+Group+Order
0018#STR0018#ALL#Messages
0019#STR0019#ALL#Some orders were not generated as there is no rate for the currency
0020#STR0020#ALL#Problems on the Files Locking
0021#STR0021#ALL#Ok
0022#STR0022#ALL#Attention
0023#STR0023#ALL#Confirm the Release Reverse?
0024#STR0024#ALL#Confirm the Release Reverse for all selected items?
0025#STR0025#ALL#Generating 
0026#STR0026#ALL#Generating invoices
0027#STR0027#ALL#Preparing the load documents
0028#STR0028#ALL#One or more documents can not be billed/sent; you may bill/send them later if necessary.
0029#STR0029#ALL#Confirm invoicing/sending ?
0030#STR0030#ALL#Fiscal Document Type?
0031#STR0031#ALL#Some items of the order were not generated since the sold quantity is lower than the released one or there are divergences concerning to the balances of lot/address!
0032#STR0032#ALL#This document has an associated WMS service already carried out. The service must be reversed.
0033#STR0033#ALL#View Document.
0034#STR0034#ALL#Attention!
0035#STR0035#ALL#This releasing item did not generate an outgoing document. 
0036#STR0036#ALL#Ok
0037#STR0037#ALL#There is no rate for the Selected Currency
0038#STR0038#ALL#The system identified change of date of the operating system, wish to update the system base date?
0039#STR0039#ALL#Yes
0040#STR0040#ALL#No
0041#STR0041#ALL#B. of Lading
0042#STR0042#ALL#Caption
0043#STR0043#ALL#Sale order released 
0044#STR0044#ALL#Sales order finished  
0045#STR0045#ALL#Sales order locked  
0046#STR0046#ALL#Error calculating installments:
0047#STR0047#ALL#This document has a WMS activity in progress 
0048#STR0048#ALL#Problem numbering invoice
0049#STR0049#ALL#Some order items were not generated because item total value equals zero
0050#STR0050#ALL#Indicate CPF / CNPJ for print
0051#STR0051#ALL#Enter below:
0052#STR0052#ALL#CPF/CNPJ not valid!
0053#STR0053#ALL#Itens below were not generated due to lack of product in inventory. Check available balance in inventory for the items below: 
0054#STR0054#ALL#Order no.: 
0055#STR0055#ALL#Item : 
0056#STR0056#ALL#Sequence : 
0057#STR0057#ALL#Orders were found using payment term associated with 'Advance', but without any Advance associated.
0058#STR0058#ALL#Do you want to select Advance now?
0059#STR0059#ALL#Advance value not entered
0060#STR0060#ALL#Order Information
0061#STR0061#ALL#Order Values
0062#STR0062#ALL#Number
0063#STR0063#ALL#Customer
0064#STR0064#ALL#Name
0065#STR0065#ALL#Dismiss Order
0066#STR0066#ALL#Advances
0067#STR0067#ALL#To change Advance value, click Advances.
0068#STR0068#ALL#Invalid Environment
0069#STR0069#ALL#This is a type PAF-ECF environment. Fiscal Vouchers cannot be issued in this environment. Please, use the DAV option
0070#STR0070#ALL#Back
0071#STR0071#ALL#Invalid Environment
0072#STR0072#ALL#No valid PAF-ECF environment. Unable to continue.
0073#STR0073#ALL#DAV already issued
0074#STR0074#ALL#DAV already issued for this order. Unable to continue.
0075#STR0075#ALL#Generating DAV
0076#STR0076#ALL#Please, wait...
0077#STR0077#ALL#LjGrvBatch: Sales order not found (SC5) for updating.
0078#STR0078#ALL#LjGrvBatch: Item not found in SL1
0079#STR0079#ALL#LjGrvBatch: Error while updating sales order (SC5)
0080#STR0080#ALL#LjGrvBatch: Item not found in SC9
0081#STR0081#ALL#DAV generation aborted
0082#STR0082#ALL#Order(s): 
0083#STR0083#ALL#In compliance with the legislation of Tax Application Program - Fiscal Printer (PAF - ECF) Act COTEPE 06/08 developed by CONFAZ, the modules Point of Sales/Store Control (Assisted Sales routine) are the only Protheus interfaces that allow receipt print.
0084#STR0084#ALL#Current Order, please select the Parent Order!
0085#STR0085#ALL#Order may be Blocked\Invoiced!
0086#STR0086#ALL#Some items were not generated, as financial transactions with dates earlier than the Financials transaction date limit are not allowed. Check parameter MV_DATAFIN.
0087#STR0087#ALL#Operation not done. Use the routine Exchange/Return of module Store Control (SIGALOJA)'.
0088#STR0088#ALL#Fiscal Coupons issuing is possible only with entry point M461IMPF
0089#STR0089#ALL#Selecting records
0090#STR0090#ALL#Expired invoicing
0091#STR0091#ALL#There are clients with deadline date for invoicing earlier than the current date. Mark them for invoicing ?
0092#STR0092#ALL#Client Selection
0093#STR0093#ALL#Order
0094#STR0094#ALL#Item
0095#STR0095#ALL#Day Limit
0096#STR0096#ALL#Deadline
0097#STR0097#ALL#Clients with expired invoicing date
0098#STR0098#ALL#Confirm
0099#STR0099#ALL#Invalid option for batch generation of outflow Invoice. The option Bring Ord. Mark must be equal to No.
0100#STR0100#ALL#Order
0101#STR0101#ALL#Due Date
0102#STR0102#ALL#Messages
0103#STR0103#ALL#No order was selected !
0104#STR0104#ALL#Ok
0105#STR0105#ALL#The following orders were not generated, because it has the day earlier than the invoicing date.
0106#STR0106#ALL#Some items were not invoiced, as the discount related to compensation (C5_DESCONT) is higher than the total value of the invoice.
0107#STR0107#ALL#Unable to generated the outbound invoice as the accounting calendar is closed.
0108#STR0108#ALL#Released
0109#STR0109#ALL#Blocked
0110#STR0110#ALL#Invoiced
0111#STR0111#ALL#Unable to reverse
0112#STR0112#ALL#release of e-commerce orders.
0113#STR0113#ALL#Process interrupted due to configuration of parameters MV_RTIPESP and MV_RTIPFIN the value of 
0114#STR0114#ALL# is higher than the value of the bill.
0115#STR0115#ALL#A461NUMNF - Error trying to generate new numbers in table SD9.
0116#STR0116#ALL#A461NUMNF - Error reserving the next number available in SD9.
0117#STR0117#ALL#Document Numbering
0118#STR0118#ALL#Generating number in SD9 file
0119#STR0119#ALL#Num. occupied
0120#STR0120#ALL#A461NUMNF - Error reserving the next number available in SD9.
