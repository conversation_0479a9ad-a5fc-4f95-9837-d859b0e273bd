#INCLUDE "PROTHEUS.CH"

/*/{Protheus.doc} GravarTabela
Retorna um array com o conteudo de um parametro
@type user function
<AUTHOR>
@since 13/07/2021
/*/
user function aParamGet(nPosicao, cParametro)

    local cretorno := ""
    cretorno := StrTokArr(GETMV(cParametro), ";" )[nPosicao]

return cretorno

/*/{Protheus.doc} GravarTabela
Retorna o proximo codigo de um alias
@type user function
<AUTHOR>
@since 13/07/2021
/*/
user function NextCode(cAlias,cCampo,cFilter)
    
    local cRetorno := ""
    local oObject := nil

    oObject := MatrizRisco():New()
    cRetorno := oObject:UltimoNum(cAlias, cCampo, .t., cFilter)

return cRetorno

/*/{Protheus.doc} GravarTabela
Retorna o proximo codigo de um alias
@type user function
<AUTHOR>
@since 13/07/2021
/*/
user function DateUtcForm(dData)
    
    local cData := Transform(DTOS(dData), "@R 9999-99-99")+ "T03:00:00.000Z"

return cData

/*/{Protheus.doc} ParseParcl
 Formata o array de parcelas para json
@type user function
<AUTHOR>
@since 23/06/2022
/*/
user function ParseParcl(aParcela)
    local nCount := 0
    local oAux := nil
    local aAux := {}    

    for nCount := 1 to len(aParcela)
        oAux := JSonObject():New()
        oAux["parcela"] := nCount
        oAux["data"] := aParcela[nCount][1]
        oAux["valor"] := aParcela[nCount][2]
        AADD(aAux, oAux)
    next
   
   oAux := nil

return aAux
