#include "tlpp-core.th"

Class   GetTokenAfterCheckoutService
    Private Data    cCheckoutCode                               As  Character
    Private Data    cUuid                                       As  Character
    Private Data    cResponseIpaas                              As  Character

    Public  Method  new()                                       As  Object
    Public  Method  setCheckoutCode(cValue  As  Character)      As  Object
    Public  Method  execute()                                   As  Object
    Public Method   sendToIpaas(cJson   As  Character)          As  Object
    Private Method  writeOnDb()                                 As  Object
    Public Method   setUuid(cValue  As  Character)              As  Object
EndClass


Method      new()       As Object   Class    GetTokenAfterCheckoutService
    ::cCheckoutCode     :=  ''
    ::cUuid             :=  ''
    ::cResponseIpaas    :=  ''
Return Self

Method  setCheckoutCode(cValue  As  Character)          As  Object    Class GetTokenAfterCheckoutService
    ::cCheckoutCode :=  cValue
Return Self

Method  setUuid(cValue  As  Character)          As  Object  Class   GetTokenAfterCheckoutService
    ::cUuid :=  cValue
Return Self


Method  sendToIpaas(cJson   As  Character)   As  Object Class   GetTokenAfterCheckoutService
    
    Local   cUrl            := GetNewPar("TI_LNKIPAS","https://api-ipaas.totvs.app/sync-hook/api/v1/integrations/8fe38407-48a1-4d9d-84ba-1f80d3c9a641/api-key/43805049-7f8d-41ad-9969-c2d4513c0fa2")
    Local   oIpaas          :=  Nil

    oIpaas := TIiPaaS():New()
	oIpaas:SetWebHook(cUrl)
	oIpaas:SetBodyWebHook(cJson)
	oIpaas:SendToiPaaS()

    ::cResponseIpaas  :=  oIpaas:getIpaasCharacterResponse()

    FreeObj(oIpaas)
	oIpaas := Nil 
Return



Method  writeOnDb()                             As      Object      Class   GetTokenAfterCheckoutService

    Local   oJsonIpaas              :=  JsonObject():new()      As  Json
    Local   cBandeira               :=  ''                      As  Character
    //Local   cDescricaoBandeira      :=  ''                      As  Character
    Local   cNomeTitularCartao      :=  ''                      As  Character
    Local   cValidadeCartao         :=  ''                      As  Character
    Local   cMacaraCartao           :=  ''                      As  Character
    Local   cStatusTokenizacao      :=  ''                      As  Character
    Local   cStatusCartao           :=  ''                      As  Character

    Local   cCustomerDocument       :=  ''                      AS  Character
    Local   cTokenGerado            :=  ''                      As  Character
    Local   cStatus                 :=  ''                      As  Character
    Local   cIdPagamento            :=  ''                      As  Character
    Local   nX                      :=  1                       As  Numeric
    Local   cHora                   :=  ''                      As  Character
    Local   dData                   :=  ''                      As  Character

    oJsonIpaas:FromJson(::cResponseIpaas)
/*
    cBandeira           :=  oJsonIpaas['result']['response_data']['flag']
    //cDescricaoBandeira  :=  FWGetSX5('ZZ',cBandeira)[1][4]
    cNomeTitularCartao  :=  oJsonIpaas['result']['response_data']['card_name']
    cValidadeCartao     :=  oJsonIpaas['result']['response_data']['card_validate']
    cMacaraCartao       :=  oJsonIpaas['result']['response_data']['card_number']
    cStatusTokenizacao  :=  '6'

    cCustomerDocument       :=  oJsonIpaas['result']['customer']['document']
    cStatus                 :=  '1'
    cStatusCartao           :=  '1'
*/

     cBandeira           :=  oJsonIpaas['response_data']['flag']
    //cDescricaoBandeira  :=  FWGetSX5('ZZ',cBandeira)[1][4]
    cNomeTitularCartao  :=  oJsonIpaas['response_data']['card_name']
    cValidadeCartao     :=  oJsonIpaas['response_data']['card_validate']
    cMacaraCartao       :=  oJsonIpaas['response_data']['card_number']
    cStatusTokenizacao  :=  '6'

    cCustomerDocument       :=  oJsonIpaas['customer']['document']
    cStatus                 :=  '1'
    cStatusCartao           :=  '1'
    


    cHora                   :=  Time()
    dData                   :=  Date()

//---------------------------------------------------------------------------------------------
//-----GRUPO 90--------------------------------------------------------------------------------

    RpcClearEnv()
    RpcSetType(3)
    RpcSetEnv("90", "90001000100")
    

    PXG->(dbSetOrder(1))
    If PXG->(dbSeek(::cUuid))
        PXG->(RecLock("PXG",.F.))
            PXG->PXG_BANDEI         :=  cBandeira
            PXG->PXG_NOMTIT         :=  cNomeTitularCartao
            PXG->PXG_VLDCAR         :=  cValidadeCartao
            PXG->PXG_MASCAR         :=  cMacaraCartao
            PXG->PXG_STOKEN         :=  cStatusTokenizacao
            PXG->PXG_STATUS         :=  '1'
        PXG->(MsUnLock())
    EndIf
/*
    For nX:= 1 to Len(oJsonIpaas['result']['response_data']['tokens'])
        If(oJsonIpaas['result']['response_data']['tokens'][nX]['document'] == '13021784000186')
            cTokenGerado    :=  oJsonIpaas['result']['response_data']['tokens'][nX]['token']
            cIdPagamento    :=  oJsonIpaas['result']['response_data']['tokens'][nX]['customer_id_maxipago']
        EndIf
    Next nX
*/    
    For nX:= 1 to Len(oJsonIpaas['response_data']['tokens'])
        If(oJsonIpaas['response_data']['tokens'][nX]['document'] == '13021784000186')
            cTokenGerado    :=  oJsonIpaas['response_data']['tokens'][nX]['token']
            cIdPagamento    :=  oJsonIpaas['response_data']['tokens'][nX]['customer_id_maxipago']
        EndIf
    Next nX


    PXJ->(RecLock("PXJ", .T.))
        PXJ->PXJ_FILIAL     :=  xFilial("PXJ") 
        PXJ->PXJ_UUID       :=  ::cUuid
        PXJ->PXJ_CNPJ       :=  cCustomerDocument
        PXJ->PXJ_TOKEN      :=  cTokenGerado
        PXJ->PXJ_IDPAGM     :=  cIdPagamento
        PXJ->PXJ_DATA       :=  dData
        PXJ->PXJ_HORA       :=  cHora
        PXJ->PXJ_STATUS     :=  '1'
    PXJ->(MsUnLock())



//---------------------------------------------------------------------------------------------
//-----GRUPO 00--------------------------------------------------------------------------------
/*
    For nX:= 1 to Len(oJsonIpaas['result']['response_data']['tokens'])
        If(oJsonIpaas['result']['response_data']['tokens'][nX]['document'] == '53113791000122')
            cTokenGerado    :=  oJsonIpaas['result']['response_data']['tokens'][nX]['token']
            cIdPagamento    :=  oJsonIpaas['result']['response_data']['tokens'][nX]['customer_id_maxipago']
        EndIf
    Next nX
*/   
    For nX:= 1 to Len(oJsonIpaas['response_data']['tokens'])
        If(oJsonIpaas['response_data']['tokens'][nX]['document'] == '53113791000122')
            cTokenGerado    :=  oJsonIpaas['response_data']['tokens'][nX]['token']
            cIdPagamento    :=  oJsonIpaas['response_data']['tokens'][nX]['customer_id_maxipago']
        EndIf
    Next nX


    RpcClearEnv()
    RpcSetType(3)   
    RpcSetEnv("00", "00001000100")
    
    PXJ->(RecLock("PXJ", .T.))
        PXJ->PXJ_FILIAL     :=  xFilial("PXJ") 
        PXJ->PXJ_UUID       :=  ::cUuid
        PXJ->PXJ_CNPJ       :=  cCustomerDocument
        PXJ->PXJ_TOKEN      :=  cTokenGerado
        PXJ->PXJ_IDPAGM     :=  cIdPagamento
        PXJ->PXJ_DATA       :=  dData
        PXJ->PXJ_HORA       :=  cHora
        PXJ->PXJ_STATUS     :=  '1'
    PXJ->(MsUnLock())

//---------------------------------------------------------------------------------------------
//-----GRUPO 90--------------------------------------------------------------------------------

Return Self




Method  execute()   As  Object  Class   GetTokenAfterCheckoutService
    Local cBody :=  '{"CheckouCode":"'+::cCheckoutCode +'"}'
    ::sendToIpaas(cBody)
    ::writeOnDb()
Return Self
