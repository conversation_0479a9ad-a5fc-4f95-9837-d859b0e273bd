#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Service.ProtheusSupplierSa2Service


using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.TransformCharacterToJsonObjectUtils
using namespace Backoffice.Configurador.Gestao_Usuarios.Repository.ConsultDataBaseSupplierRepository
using namespace Backoffice.Configurador.Gestao_Usuarios.Repository.ConsultDataBaseCityRepository
using namespace Backoffice.Configurador.Gestao_Usuarios.Service.StructureArrayToSa2Service

/*/{Protheus.doc} ProtheusSupplierSa2
This Class aims to create a Supplier on SA2 Table
@type class
@version 1.0
<AUTHOR>
@since 10/31/2024
/*/
Class   ProtheusSupplierSa2
     Public  Data    cJson                  As  Character
     Public  Data    cCodeSupplier          As  Character
     Private Data    cStoreSupplier         As  Character
     Private Data    cCodeCity              As  Character
     Public  Data    nOperation             As  Numeric
     Public  Data    oJsonSa2               As  Json
     Public  Data    aRetProc               As  Array
     Private Data    aArraySa2              As  Array
     Private Data    oDataSupplier          As  Object
     Private Data    oDataCity              As  Object

    Private Data    lMsErroAuto             As  Logical
    Private Data    INCLUI                  As  Logical
    Private Data    ALTERA                  As  Logical
    Public  Method  new() Constructor
    Public  Method  setJson(cJsonInicial As Character)
    Public  Method  createOrUpdateSupplierSA2() As Array
    Public  Method  setJsonSa2()
    Public  Method  getJsonSa2()
    Public 	Method  getCodeSupplier()   As  Character
    Public	Method  getStoreSupplier()  As  Character
    Private Method  setOperationMata()
    Private Method  execOperationMata()
    Private Method  setObjectSupplier()
    Private Method  setObjectCity()
    Private Method  setCodeSupplier()
    Private Method  setStoreSupplier()
    Private Method  setCodeCity()
    Private Method  setTypeOperation()
    Private Method  setArrayOperationMata() 
    Private Method  finishObjectCity()
    Private Method  finishObjectSupplier()
    Private Method  ConsultError()
EndClass

/*/{Protheus.doc} ProtheusSupplierSa2::new
This method aims to create a new instance object of the class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the new object instantiated
/*/
Method  new()   Class   ProtheusSupplierSa2
    ::cJson         :=  ''
    ::oJsonSa2      :=  JsonObject():new()
    ::aRetProc      :=  { .T., "" }
    ::aArraySa2     :=  {}
    ::lMsErroAuto   := .F.
    ::INCLUI        := .F.
    ::ALTERA        := .F.
Return Self

/*/{Protheus.doc} ProtheusSupplierSa2::setJson
This method aims to set the json from the income system on the class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@param cJsonInicial, character, The income Json Character
@return variant, Return the instantiated object itself
/*/
Method  setJson(cJsonInicial    As  Character)  Class   ProtheusSupplierSa2
	::cJson := cJsonInicial
Return Self


/*/{Protheus.doc} ProtheusSupplierSa2::setJsonSa2
This method aims to create the necessary Json Sa2 from income Character Json
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  setJsonSa2()    Class   ProtheusSupplierSa2
	::oJsonSa2 := TransformCharacterToJsonObject():exec(::cJson, "SA2")
Return Self

/*/{Protheus.doc} ProtheusSupplierSa2::setOperationMata
This method aims to set the variables of the Protheus to decide the type of operation
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  setOperationMata()  Class   ProtheusSupplierSa2        
    If ::nOperation == 3
        ::INCLUI := .T.
        ::ALTERA := .F.
    Else
        ::INCLUI := .F.
        ::ALTERA := .T.
    Endif
Return  Self

/*/{Protheus.doc} ProtheusSupplierSa2::execOperationMata
This method aims to execute the routine mata020 on Protheus to create the Supplier on SA2 Table
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  execOperationMata()     Class   ProtheusSupplierSa2
    Private INCLUI, ALTERA, lMsErroAuto 
        
    INCLUI	:=	::INCLUI 
    ALTERA	:=	::ALTERA 
    lMsErroAuto := .F.
    MSExecAuto( { |x, y| MATA020( x, y ) }, ::aArraySa2, ::nOperation )
    ::lMsErroAuto := lMsErroAuto
Return  Self

/*/{Protheus.doc} ProtheusSupplierSa2::setObjectSupplier
This method aims to create the object supplier and execute the search on SA2 Table 
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  setObjectSupplier()     Class   ProtheusSupplierSa2
    ::oDataSupplier := ConsultDataBaseSupplier():new(::oJsonSa2)
    ::oDataSupplier:searchSupplier()
Return  Self

/*/{Protheus.doc} ProtheusSupplierSa2::setObjectCity
This method aims to set the object on class that search for the city on CC2 Table
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  setObjectCity()  Class   ProtheusSupplierSa2
    ::oDataCity := ConsultDataBaseCity():new(::oJsonSa2)
    ::oDataCity:SearchCity()
Return  Self


/*/{Protheus.doc} ProtheusSupplierSa2::setCodeSupplier
This method aims to set the code of the supplier using another class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  setCodeSupplier()    Class  ProtheusSupplierSa2
    ::cCodeSupplier       :=  ::oDataSupplier:getCodeSupplier()
Return Self

/*/{Protheus.doc} ProtheusSupplierSa2::setStoreSupplier
This method aims to set the store of Supplier using another class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  setStoreSupplier()   Class  ProtheusSupplierSa2
    ::cStoreSupplier       :=  ::oDataSupplier:getStoreSupplier()
Return Self

/*/{Protheus.doc} ProtheusSupplierSa2::setCodeCity
This method aims to set the City Code using another class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  setCodeCity()        Class  ProtheusSupplierSa2
    ::cCodeCity       :=  ::oDataCity:getCodeCity()
Return Self

/*/{Protheus.doc} ProtheusSupplierSa2::setTypeOperation
This method set the type of operation using another class method
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the Object instantiated itself
/*/
Method  setTypeOperation()   Class  ProtheusSupplierSa2
    ::nOperation    :=  ::oDataSupplier:getOperation()
Return Self

/*/{Protheus.doc} ProtheusSupplierSa2::setArrayOperationMata
This method aims to set the array to create the Supplier using another class method
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  setArrayOperationMata()  Class  ProtheusSupplierSa2
    ::aArraySa2:= StructureArrayToSa2():getArrayFromJson(::oJsonSa2,::oDataSupplier, ::oDataCity, ::nOperation)
Return Self

/*/{Protheus.doc} ProtheusSupplierSa2::finishObjectCity
This method finish the object of another class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  finishObjectCity()  Class   ProtheusSupplierSa2
    ::oDataSupplier:finish()
Return Self

/*/{Protheus.doc} ProtheusSupplierSa2::finishObjectSupplier
This method finish the object of another class
@type method
@version  1,0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  finishObjectSupplier()  Class   ProtheusSupplierSa2
    ::oDataCity:finish()
Return Self

/*/{Protheus.doc} ProtheusSupplierSa2::ConsultError
This method aims to check for errors when the execution of the 'Mata' routine fails
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  ConsultError()  Class   ProtheusSupplierSa2
    If ! ::lMsErroAuto
        ::cCodeSupplier := SA2->A2_COD
        ::cStoreSupplier := SA2->A2_LOJA
        ::aRetProc  	:=  { .T., "" }
    Else
        ::aRetProc[1] := .F.
        ::aRetProc[2] := FwNoAccent( MemoRead( Alltrim( NomeAutoLog() ) ) )
    Endif
Return Self


/*/{Protheus.doc} ProtheusSupplierSa2::getCodeSupplier
This method aims to return the property CodeSupplier of the object of the class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return character, Return the Code of the Supplier in the object instantiated
/*/
Method  getCodeSupplier()   Class   ProtheusSupplierSa2
Return ::cCodeSupplier

/*/{Protheus.doc} ProtheusSupplierSa2::getStoreSupplier
This method aims to return the property StoreSupplier of the object of the class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return character, Return the Store of the supplier
/*/
Method  getStoreSupplier()  Class   ProtheusSupplierSa2
Return ::cStoreSupplier


/*/{Protheus.doc} ProtheusSupplierSa2::createOrUpdateSupplierSA2
This method coordinates other methods to create the supplier
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return object, return the object instantiated itself
/*/
Method  createOrUpdateSupplierSA2() As  Array   Class   ProtheusSupplierSa2
    ::setJsonSa2()
    ::setObjectSupplier()   //cria o objeto / define o tipo da operacao 3-criacao 4-alteracao
    ::setObjectCity()
    ::setCodeSupplier()
    ::setStoreSupplier() 
    ::setCodeCity()
    ::setTypeOperation()
    ::setArrayOperationMata()
    ::finishObjectCity()
    ::finishObjectSupplier()
    ::setOperationMata()
    ::execOperationMata()
    ::ConsultError()
Return Self

