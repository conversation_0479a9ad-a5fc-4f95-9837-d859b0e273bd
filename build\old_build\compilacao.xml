<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project basedir="." name="Compilacao Protheus" default="main">
	<import file="TDS-compilacao.xml" />
	<tstamp />

	<!-- ================================= 
          		dependências
         ================================= -->

	<taskdef resource="net/sf/antcontrib/antlib.xml" >
		<classpath>
			<pathelement location="${env.ANT_HOME}\lib\ant-contrib-1.0b3.jar"/>
		</classpath>
	</taskdef>	


	<!-- ================================= 
      			notificações              
     	================================= -->
	
    <target name="notifica_inicio" description="Notifica o inicio">		
		<exec executable="powershell" >
			<arg value="${env.BUILD_SOURCESDIRECTORY}\build\notify_beginning.ps1" />
		</exec>	
	</target>

	<target name="notifica" description="Notifica os passos do job">
		<exec executable="powershell" >
			<arg value="${env.BUILD_SOURCESDIRECTORY}\build\get_status.ps1" />
			<arg value="${argumento_0}" />
			<arg value="${argumento_1}" />
		</exec>
	</target>

	<target name="notifica_fim" >
		<exec executable="powershell" >
			<arg value="${env.BUILD_SOURCESDIRECTORY}\build\notify_end.ps1" />
			<arg value="${end_status}" />
		</exec>
	</target>

	<!-- ================================= 
          			properties              
         ================================= -->

		<property environment="env"/>

	<!-- ================================= 
          			main              
         ================================= -->
		
	<target name="main" description="Compila e gera pacote de aplicação de um projeto">
		<antcall target="notifica_inicio" />
		<antcall target="prepare"/>
		<antcall target="compileAll"/>
		<antcall target="fontes_compilados"/>
		<antcall target="fontes_nao_compilados"/>
		<antcall target="patches_results"/>
		<!--<antcall target="notifica_fim" >
			<param name="end_status" value="success"/>
		</antcall>-->
	</target>



	<!-- ================================= 
          target: prepare              
         ================================= -->


	<target name="prepare" description="Prepara o ambiente de execução">
		<antcall target="notifica"> 
			<param name="argumento_0" value="Ambiente"/> 
			<param name="argumento_1" value="'Preparando Ambiente'"/> 
		</antcall>
		
		<mkdir dir="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}" />
		<mkdir dir="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}/logs" />
		<mkdir dir="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}/rpo" />
		<touch file="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}/logs/patches.log"/>

		<copy overwrite="true" file="${env.PROTHEUS_PROD_PATH}\tttp120.rpo" tofile="${env.PROTHEUS_WORKING_PATH}\tttp120.rpo"/>
		<antcall target="notifica"> 
			<param name="argumento_0" value="Ambiente"/>
			<param name="argumento_1" value="'Preparando Ambiente'"/>	
		</antcall>
	</target>

	<!-- ================================= 
          target: compileAll
         ================================= -->

	<target name="compileAll" description="Compila em vários ambientes.">
		<echo message="Compile environment ${env.COMPILE_ENVIRONMENT}" />
		<echo message="Protheus working path ${env.PROTHEUS_WORKING_PATH}" />
			<antcall target="notifica">
			<param name="argumento_0" value="Compilacao"/> 
			<param name="argumento_1" value="'Iniciando Compilacao'"/> 
		</antcall>
		<!-- Compile -->
		<antcall target="compile">
			<param name="environment" value="${env.COMPILE_ENVIRONMENT}" />
		</antcall>

		<antcall target="compile_patches" >
			<param name="environment" value="${env.COMPILE_ENVIRONMENT}" />
		</antcall>

		<!-- If error, it fails here -->

		<!--Here I need to adjust the outreport parameter, it's not working-->
		<copy overwrite="true" file="D:\protheus\x64-12.1.25\protheus_data\compile_errors.log" tofile="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}/logs/${env.COMPILE_ENVIRONMENT}_fonts_errors.log"/>
		<copy overwrite="true" file="D:\protheus\x64-12.1.25\protheus_data\compile_success.log" tofile="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}/logs/${env.COMPILE_ENVIRONMENT}_fonts_success.log"/>
		
		<antcall target="verificaErro"/>
		
		<antcall target="notifica">
			<param name="argumento_0" value="Pacote"/>
			<param name="argumento_1" value="'Enviando pacote para ambiente'"/>
		</antcall>

		<antcall target="bundle" />

		<antcall target="notifica">
			<param name="argumento_0" value="Deploy"/>
			<param name="argumento_1" value="'Iniciando deploy'"/>
		</antcall>
		
	</target>

	<!-- ================================= 
          target: compile - fonts                
         ================================= -->

	<target name="compile" description="Compila todos os fontes de um projeto">
		<fileset dir="${env.BUILD_SOURCESDIRECTORY}" id="filesToCompile" casesensitive="false">
			<patternset refid="sources.advPL" />
		</fileset>

		<pathconvert property="programs" refid="filesToCompile">
			<mapper type="identity" />
		</pathconvert>

		<echo file="files.lst" append="false">${programs}</echo>

		<antcall target="TDScompile">
			<param name="program" value="${programs}" />
			<param name="environment" value="${environment}" />
		</antcall>
	</target>



	<!-- ================================= 
          target: compile - patches
         ================================= -->
	
	<target name="compile_patches" description="Aplica patch">
		<sequential>

		<antcall target="notifica">
			<param name="argumento_0" value="Patches"/>
			<param name="argumento_1" value="'Aplicando patches'"/>
		</antcall>
		<for param="files" >
			<path>
				<fileset dir="${env.BUILD_SOURCESDIRECTORY}/Patch/aplicar" />
			</path>
			<sequential>
				<var name="patches" unset="true" />
				<basename property="patches" file="@{files}"/>
			
				<echo message="Aplicando -> ${env.APPSERVER_PATH}\appserver.exe -compile -applypatch -files=@{files} env=${environment} -applyoldprogram" />
				<exec executable="${env.APPSERVER_PATH}\appserver.exe" output="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}/logs/${env.COMPILE_ENVIRONMENT}_${patches}_patch.log">
					<arg value="-compile" />
					<arg value="-applypatch" />
					<arg value="-files=@{files}" />
					<arg value="-env=${environment}" />
					<!-- <arg value="-applyoldprogram"/> 11-05-21 jeferson-->
				</exec>
				<echo file="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}/patches_aplicados.log" append="true">Aplicados: ${patches}</echo>
				<copy overwrite="true" file="@{files}" tofile="${env.APPLIED_PATCHES}/${patches}"/>
			</sequential>
		</for>
		</sequential>
	</target>


	<!-- ================================= 
          		Gera artefato
         ================================= -->

		<target name="bundle">
			<copy overwrite="true" file="${env.PROTHEUS_WORKING_PATH}\tttp120.rpo" tofile="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}/rpo/tttp120.rpo"/>
	       	<!-- <zip destfile="output/rpo/rpo_mi.zip" basedir="output/rpo" includes="ttts120.rpo"/> -->
    	</target>

	<!-- ================================= 
          target:verificaErro
         ================================= -->

	<target name="verificaErro">
		<antcall target="notifica">
			<param name="argumento_0" value="Finalizando"/>
			<param name="argumento_1" value="'Verificando erros'"/>
		</antcall>

		<condition property="erro.exists"> 
			<length file="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}/logs/${env.COMPILE_ENVIRONMENT}_fonts_errors.log" when="greater" length="0" />
		</condition>
		<loadfile property="erros" srcfile="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}/logs/${env.COMPILE_ENVIRONMENT}_fonts_errors.log"> </loadfile>
		<antcall target="erro" />
	</target>
	
	<target name="erro" if="erro.exists" > 
		<!--<antcall target="notifica_fim">
			<param name="end_status" value="fail"/>
		</antcall>
		<fail message="${erros}"> </fail>-->
		<echo message="${erros}" />
	</target>


	
	<!-- ================================= 
        target:patches_aplicados
     ================================= -->

	<target name="patches_results">
		<loadfile property="patches_results" srcfile="${env.OUTPUT_PATH}/${env.BUILD_BUILDID}/logs/patches.log" />
		<echo message="Patches:" />
		<echo message="${patches_results}" />
	</target>
	
	<!-- ================================= 
          target:fontes_compilados
         ================================= -->

	<target name="fontes_compilados">
		<loadfile property="fontes_ok" srcfile="D:\protheus\x64-12.1.25\protheus_data\compile_success.log"/>
		<echo message="Fontes compilados:" />
		<echo message="${fontes_ok}" />
	</target>

	<!-- ================================= 
        target:fontes_nao_compilados
     ================================= -->

	<target name="fontes_nao_compilados">
		<loadfile property="fontes_erro" srcfile="D:\protheus\x64-12.1.25\protheus_data\compile_errors.log" />
		<echo message="Fontes nao compilados:" />
		<echo message="${fontes_erro}" />
	</target>
</project>
