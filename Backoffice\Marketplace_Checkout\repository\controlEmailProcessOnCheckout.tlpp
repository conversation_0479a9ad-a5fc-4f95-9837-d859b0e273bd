#include    "tlpp-core.th"


Class   ControlEmailProcessOnCheckout
    Private Data    PXG_UUID                            As  Character
    Private Data    lStatusEnvio                        As  Logical

    Public  Method  setUUID(cVar    As  Character)      As  Object
    Private Method  dbSeekOnTabControlCheckout()        As  Object
    Private Method  dbSeekOnTabClient()                 As  Object
    Public  Method  new()                               As  Object
    Public  Method  execute()                           As  Object
    Public  Method  newStatus()                         As  Object

EndClass


Method  new()   As  Object  Class   ControlEmailProcessOnCheckout
    ::PXG_UUID              :=  ''
Return  Self

Method  setUUID(cVar    As  Character)  As  Object  Class   ControlEmailProcessOnCheckout
    ::PXG_UUID  :=  cVar
Return  Self

Method  dbSeekOnTabControlCheckout()        As  Object  Class   ControlEmailProcessOnCheckout
    PXG->(dbSetOrder(1))
    PXG->(dbSeek(::PXG_UUID))
Return  Self


Method  dbSeekOnTabClient()                 As  Object  Class   ControlEmailProcessOnCheckout
    Local   cClient :=  ''  As  Character
    Local   cLoja   :=  ''  As  Character

    cClient :=  PXG->PXG_CLIENT
    cLoja   :=  PXG->PXG_LOJA

    SA1->(dbSetOrder(1))
    SA1->(dbSeek(xFilial('SA1')+cClient+cLoja))
Return Self



Method  execute()   As  Object Class   ControlEmailProcessOnCheckout
        ::dbSeekOnTabControlCheckout()
        ::dbSeekOnTabClient()

        oDataEmail  :=  CreateObjectEmailToCheckoutService():new()
        oDataEmail:createObject()
        cJsonBodyToSendEmail    :=  oDataEmail:getJsonEmailBody()
        oDataEmail:freeObjectsFromMemory()
        //Classe envia email
        oSendEmail  :=  SendEmailProcessService():new()
        oSendEmail:setJsonDataToSendEmail(cJsonBodyToSendEmail)
        oSendEmail:SendEmailProcessServiceToClient()
        //Atualiza Status
        ::lStatusEnvio    :=  oSendEmail:getStatus()
        ::newStatus()
        FreeObj(oDataEmail)
        FreeObj(oSendEmail)
Return Self



/*/{Protheus.doc} ControlEmailProcessOnCheckout::newStatus
+---+--------------------------------------------------------------+
| # | Descricao                                                    |
+---+--------------------------------------------------------------+
| 0 | Processo Iniciado                                            |
| 1 | Link do Checkout Criado pronto para enviar                   |
| 2 | Link checkout enviado por Email                              |
| 3 | Erro ao enviar Link do Checkout por Email                    |
| 4 | Email para preenchimento do Checkout Recebido                |
| 5 | Checkout preenchido                                          |
| 6 | Cartao tokenizado com sucesso                                |
| 7 | Cartao cancelado                                             |
| 8 | Tokenizacao Desativada                                       |
+---+--------------------------------------------------------------+
@type method
@version  1.0
<AUTHOR>
@since 4/17/2025
@return object, The object itself
/*/
Method  newStatus()                         As  Object      Class   ControlEmailProcessOnCheckout
    If  ::lStatusEnvio
        PXG->(RecLock("PXG", .F.))
            PXG->PXG_STOKEN :=  '2'
        PXG->(MsUnlock())
    EndIf
Return Self


