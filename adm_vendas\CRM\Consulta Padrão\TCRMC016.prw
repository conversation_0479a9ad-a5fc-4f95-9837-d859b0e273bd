#include 'protheus.ch'
#include 'parmtype.ch'

user function TCRMC016(cTabela,cCBox)
Local aArea		:= GetArea()
Local cTmp		:= GetNExtAlias()
Local cRet		:= ""
Local cLingua	:= ""

Default cTabela	:= "TDI002"
Default cCBox 	:= "000001"

BeginSql Alias cTmp

SELECT ZX5_CHAVE2,ZX5_DESCRI,ZX5_DESESP,ZX5_DESENG
From %table:ZX5%
WHERE ZX5_FILIAL=%xFilial:ZX5%
AND ZX5_TABELA=%exp:cTabela%
AND ZX5_CHAVE=%exp:cCBox%
AND %notDel%
ORDER BY ZX5_CHAVE2 

EndSql

Do While !(cTmp)->(EoF())

	cLingua := ""
	cRet += Iif(Empty(cRet),"",";")
	cRet += Alltrim((cTmp)->ZX5_CHAVE2)+"="
	
	#IFDEF SPANISH
		If !Empty(Alltrim((cTmp)->ZX5_DESESP))
			cLingua := Alltrim((cTmp)->ZX5_DESESP)
		EndIf
	#ENDIF
	
	#IFDEF ENGLISH
		If !Empty(Alltrim((cTmp)->ZX5_DESENG))
			cLingua := Alltrim((cTmp)->ZX5_DESENG)
		EndIf
	#ENDIF
	
	If Empty(cLingua)
		cLingua := Alltrim((cTmp)->ZX5_DESCRI)
	EndIf
	
	cRet += cLingua
	
	(cTmp)->(dbSkip())
	
EndDo

(cTmp)->(dbCloseArea())

RestArea(aArea)
Return(cRet)