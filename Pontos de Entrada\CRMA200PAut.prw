#Include 'Protheus.ch'

/*/{Protheus.doc} CRMA200PAut
Retorna campos customizados para gravar no compartilhamento
@type function
<AUTHOR>
@since 19/11/2015
@version 1.0
@return aRet, Array com campos customizados
@example
(examples)
@see (links_or_references)
/*/
User Function CRMA200PAut
Local aArea			:= GetArea()
Local aAreaSA3		:= SA3->(GetArea())
Local aAO4Detail		:= ParamIxb[1]
Local nPosUsr			:= aScan(aAO4Detail,{|x| x[1]=="AO4_CODUSR"})
Local cUsr				:= ""
Local aRet				:= {{"AO4_XSEGME",Space(TamSX3("PKG_CODSEG")[1]),NIL}}
Local cCargo			:= ""
Local cSegmen			:= ""

If AliasInDic("PKJ") .and. nPosUsr > 0 .and. SUM->(FieldPos("UM_XUSASEG"))>0

	cUsr 	:= aAO4Detail[nPosUsr,2]
	dbSelectArea("SA3")
	dbSetOrder(7)
	If dbSeek(xFilial("SA3")+cUsr) .and. !Empty(SA3->A3_CARGO)
	
		cCargo := SA3->A3_CARGO

		dbSelectArea("SUM")
		dbSetOrder(1)
	
		If !Empty(cCargo) .and. dbSeek(xFilial("SUM")+cCargo) .and. SUM->UM_XUSASEG == "2"
	
			dbSeek(xFilial("PKJ")+SA3->A3_VEND)
			
			Do While !PKJ->(EoF()) .and. PKJ->(PKJ_FILIAL+PKJ_VEND)==xFilial("PKJ")+SA3->A3_VEND
			
				If FieldPos("PKJ_SEGPRI") > 0 .and. PKJ->PKJ_SEGPRI=="S"
				
					cSegmen := PKJ->PKJ_CODSEG
					Exit
				
				EndIf
				
				PKJ->(dbSkip())
				
			EndDo
			
			If !Empty(cSegmen)
				
				aRet[1,2] := cSegmen
				
			EndIf
			
		EndIf
		
	EndIf
	
	RestArea(aAreaSA3)

EndIf

RestArea(aArea) 
Return(aRet)

