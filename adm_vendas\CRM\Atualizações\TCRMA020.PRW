#INCLUDE "PROTHEUS.CH"
#INCLUDE "TOPCONN.CH"
#INCLUDE "FWMVCDEF.CH"  
#INCLUDE "TCRMA020.CH"

/*/{Protheus.doc} TCRMA020
//Maintenance routine of the table PIF (conversion of provider product to totvs product code)
<AUTHOR>
@since 16/07/2015
@version 1.0

@type function
/*/
User function TCRMA020()
	Local bErrorBkp		:= ErrorBlock()
	Local oBrowse		:= Nil

	ErrorBlock( { |oErro| U_CRCLD046(oErro:ERRORSTACK, Nil, Nil, .T.)  } )

	Begin Sequence

		oBrowse := FWMBrowse():New()
		oBrowse:SetAlias("PIF")
		oBrowse:SetDescription(STR0001)	//"STR0001:Cadastro de/para de produto PROVIDER para produto TOTVS"
		oBrowse:SetMenuDef("TCRMA020")	
		oBrowse:Activate()

	End Sequence

	ErrorBlock( bErrorBkp )

Return(.T.)

/*/{Protheus.doc} MenuDef
//Defines the MVC MenuDef from TCRMA020 routine.
<AUTHOR>
@since 16/07/2015
@version 1.0
@type function
/*/
Static Function MenuDef()
	Local aRotina := {}

	ADD OPTION aRotina TITLE OemToANSI(STR0002) ACTION "VIEWDEF.TCRMA020" OPERATION 1 ACCESS 0 //STR0002:Pesquisar	
	ADD OPTION aRotina TITLE OemToANSI(STR0002) ACTION "VIEWDEF.TCRMA020" OPERATION 2 ACCESS 0 //STR0003:Visualizar
	ADD OPTION aRotina TITLE OemToANSI(STR0003) ACTION "VIEWDEF.TCRMA020" OPERATION 3 ACCESS 0 //STR0004:Incluir
	ADD OPTION aRotina TITLE OemToANSI(STR0004) ACTION "VIEWDEF.TCRMA020" OPERATION 4 ACCESS 0 //STR0005:Alterar
	ADD OPTION aRotina TITLE OemToANSI(STR0005) ACTION "VIEWDEF.TCRMA020" OPERATION 5 ACCESS 0 //STR0006:Excluir

Return(aRotina)

/*/{Protheus.doc} ModelDef
//Defines the MVC ModelDef from TCRMA020 routine.
<AUTHOR>
@since 16/07/2015
@version 1.0
@type function
/*/
Static Function ModelDef()
	Local oStruPIF 	:= FWFormStruct(1,"PIF")
	Local oModel	:= Nil
	
	oModel := MPFormModel():New("TCRMA020",,{|oModel| .T.})		
	oModel:AddFields("PIFMASTER",,oStruPIF)	

Return(oModel)

/*/{Protheus.doc} ViewDef
//Defines the MVC ViewDef from TCRMA020 routine.
<AUTHOR>
@since 16/07/2015
@version 1.0

@type function
/*/
Static Function ViewDef()
	Local oModel	:= FWLoadModel("TCRMA020")
	Local oStruPIF	:= FWFormStruct(2,"PIF")		
	Local oView		:= Nil

	oView := FWFormView():New()
	oView:SetModel(oModel)	
	oView:AddField("ViewField",oStruPIF,"PIFMASTER")

Return(oView)