#include 'protheus.ch'

/*/{Protheus.doc} CRMXFILPAP
Filtra usu�rios fora da estrutura de vendas - �re<PERSON>
<AUTHOR>
@since 20/03/2017
@version 1.0
@type function
/*/
User Function CRMXFILPAP
Local cRet		:= ""

If GetMV("TI_FLTPAPB",,.t.)

	cRet += U_CRMRetFPap(.t.)
	
EndIf

Return(cRet) 


/*/{Protheus.doc} CRMRetFPap
//TODO Descrição auto-gerada.
<AUTHOR>
@since 21/03/2017
@version undefined
@param lAdvpl, logical, descricao
@type function
/*/
User Function CRMRetFPap(lAdvpl)
Local cRet		:= GetMV("TI_FLTUSER",," AND AZS_CODUND <> '      '")
Default lAdvpl	:= .f.

If lAdvpl
	cRet := StrTran(cRet,"AND",".AND.")
	cRet := StrTran(cRet,"OR",".OR.")
EndIf

Return(cRet)
