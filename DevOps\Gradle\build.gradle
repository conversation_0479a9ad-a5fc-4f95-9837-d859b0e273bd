import groovy.json.JsonSlurper

// ******** Configuration ********

plugins {
    id 'org.sonarqube' version '3.3'
    id 'org.ajoberstar.grgit' version '1.7.2'
}

task env {
    println '>>>> TASK 1 - var de ambiente'

    env.ext.output_path             = System.getenv('OUTPUT_PATH')
    env.ext.build_id                = System.getenv('BUILD_BUILDID')
    env.ext.protheus_prod_path      = System.getenv('PROTHEUS_PROD_PATH')
    env.ext.protheus_working_path   = System.getenv('PROTHEUS_WORKING_PATH')
    env.ext.build_sources_directory = System.getenv('BUILD_SOURCESDIRECTORY')
    env.ext.appserver_path          = System.getenv('APPSERVER_PATH')
    env.ext.compile_environment     = System.getenv('COMPILE_ENVIRONMENT')
    env.ext.includes_path           = System.getenv('INCLUDES_PATH')
    env.ext.patch_environment       = System.getenv('PATCH_ENVIRONMENT')
    env.ext.sonar_agent_user        = System.getenv('SONAR_AGENT_USER')
    env.ext.sonar_agent_password    = System.getenv('SONAR_AGENT_PASSWORD')
    env.ext.sonar_quick             = System.getenv('SONAR_QUICK')
    env.ext.defragrpo               = System.getenv('DEFRAGRPO')
    env.ext.version                 = System.getenv('BUILD_SOURCEVERSION')
    if ( System.getenv('SYSTEM_PULLREQUEST_SOURCEBRANCH') != null){
        env.ext.branch = System.getenv('SYSTEM_PULLREQUEST_SOURCEBRANCH')
        env.ext.sonar_project_name = System.getenv('SYSTEM_PULLREQUEST_SOURCEBRANCH')
        env.ext.sonar_project = env.ext.sonar_project_name.substring(env.ext.sonar_project_name.lastIndexOf('/') + 1)
    } else {
        env.ext.branch = System.getenv('BUILD_SOURCEBRANCH')
        env.ext.sonar_project_name = System.getenv('BUILD_SOURCEBRANCH')
        env.ext.sonar_project = env.ext.sonar_project_name.substring(env.ext.sonar_project_name.lastIndexOf('/') + 1)
    }


    println "sonar_project-> ${env.sonar_project}"
    println "sonar_project_name-> ${env.sonar_project_name}"
    println "BRANCH-> ${env.branch}"
    println "OUTPUT_PATH -> ${env.output_path}"
    println "BUILD_BUILDID -> ${env.build_id}"
    println "PROTHEUS_PROD_PATH -> ${env.protheus_prod_path}"
    println "PROTHEUS_WORKING_PATH -> ${env.protheus_working_path}"
    println "build_sources_directory -> ${env.build_sources_directory}"
    println "${env.appserver_path}"
    println "${env.compile_environment}"
    println "${env.includes_path}"
    println "${env.patch_environment}"
}

// ******** task prepare ********
task prepare << {
    println '>>>> TASK 2  - preparando'
    mkdir env.output_path
    mkdir env.output_path + '/' + env.build_id + '/logs'
    mkdir env.output_path + '/' + env.build_id + '/rpo'
    mkdir env.build_sources_directory + '/src/quicksonar'

// TODO - Voltar o passo de copiar o RPO de produção enquanto o processo de aplicação de patch não muda
}

// ******** task git_diff ********

task git_diff << {
    println '>>>> TASK GIT DIFF  <<<<'

    def fonts_to_scan = []

    def setGitMaster2 = 'git symbolic-ref refs/remotes/origin/HEAD refs/remotes/origin/master'.execute()
    def runSGM2 = new StringBuffer()
    setGitMaster2.consumeProcessErrorStream(runSGM2)

    println setGitMaster2.text
    println runSGM2.toString()

    def proc = "git diff --name-only origin/master ${env.branch}".replace('refs/heads', 'origin').execute()
    def b = new StringBuffer()
    proc.consumeProcessErrorStream(b)

    def files = proc.getText().split('\n')
    println "Aqui!! "+files
    files.each { file ->
        println 'file-> ' + file
        println 'file-> ' + file.getClass()
        if (file.toUpperCase().contains('.PRW')) {
            fonts_to_scan << file
        }
    }

    println 'b.toString() => ' + b.toString()

    // def git_history = grgit.show {
    //     commit = 'HEAD'
    // }
    // def changes = git_history.modified
    // def added = git_history.added
    // def renamed = git_history.renamed
    // def copied = git_history.copied

    // changes.each { source_code ->
    // println "source_code:" + source_code
    //     fonts_to_scan << source_code
    // }
    // added.each { new_file ->
    //     println 'new_file:' + new_file

    //     fonts_to_scan << new_file
    // }
    // renamed.each { file_renamed ->
    //     println 'file_renamed:' + file_renamed

    //     fonts_to_scan << file_renamed
    // }
    // copied.each { file_copied ->
    //     println 'file_copied:' + file_copied

    //     fonts_to_scan << file_copied
    // }

    fonts_to_scan.each { font_to_scan ->
        println 'Fonte a ser escaneado -> ' + font_to_scan
        def fontPath = font_to_scan.substring(0, font_to_scan.lastIndexOf('/') + 1)

        copy {
            from file(env.build_sources_directory + '/' + font_to_scan)
            into file(code_directory + '/' + fontPath)
        }
        println code_directory + '/' + fontPath
    }
}

// ******** task sonar ********
sonarqube {
    println "sonar_project-> ${env.sonar_project}"

    properties {
        property 'sonar.sourceEncoding', encode
        property 'sonar.login', "${env.sonar_agent_user}"
        property 'sonar.password', "${env.sonar_agent_password}"
        property 'sonar.projectKey', "${env.sonar_project}"
        property 'sonar.projectName', "${env.sonar_project_name}"
        property 'sonar.projectVersion', '2.0'
        property 'sonar.projectBaseDir', code_directory
        property 'sonar.project', code_directory
        property 'sonar.sources', code_directory
        property 'sonar.advpl.file.chs_paths', sonar_includes
        property 'sonar.skipPackageDesign', 'true'
        property 'sonar.skipDesign', 'true'
        property 'sonar.exclusions', '**/RUP*,**/RUP*.prw,**/RUP*.PRW,**/*.java,**/*.js,**/*.jav*,**/*.jsx,**/*.py'
        property 'sonar.test', '**/RUP*,**/RUP*.prw,**/RUP*.PRW'
        property 'sonar.scm.provider', 'git'
    //         property "sonar.branch.name", "release12.1.33" Developer Edition only
    }
}

// ******** task check quality gate ********
// TODO - Aqui vai a task que avalia o Quality Gate e criar o error_files.json ou não
task get_quality_gate() << {
    def stdout = new ByteArrayOutputStream()

    println '>>>> Get Quality Gate'
    println env.build_sources_directory + '/DevOps/Gradle/sonar_remover'
    println env.build_sources_directory
    exec {
        workingDir env.build_sources_directory + '/DevOps/Gradle/sonar_remover'
        commandLine 'cmd', '/c', 'npm install'
    }
    exec {
        workingDir env.build_sources_directory + '/DevOps/Gradle/sonar_remover'
        commandLine 'cmd', '/c', 'npm run start --trace-warnings'
    }
    println "Output: ${stdout}"
}

// ******** task remove fonts ********
task remove_fonts_with_bugs << {
    def jsonSlurper = new JsonSlurper()
    def inputFile = new File(env.build_sources_directory + '/DevOps/Gradle/sonar_remover/data/fonts_with_bugs.json')
    def bugs = jsonSlurper.parseText(inputFile.text)

    bugs.each {
        //Remove da compilação fontes que estão no arquivo files_with_bugs, gerados pelo Sonar
        println 'Deletando ' + it.split(':')[1]
        new File(env.build_sources_directory + '/' + it.split(':')[1]).delete()
        println 'REMOVER' + env.build_sources_directory + '/' + it.split(':')[1]
    }
}

// ******** task compile ********

task what_to_compile << {
    println '>>>> TASK 3 - Generating files.lst..........'
    new File("${env.build_sources_directory}\\devops\\build\\output\\compile.lst"). delete()
    def fonts_dir = new File("${env.build_sources_directory}")
    def files = []

    fonts_dir.traverse { file ->
        if (file.name ==~ ~/.*\.(prw|PRW|tres|TRES|PRG|PRX|APH|AHU|APL|APW|PPP|NGC|NGCO|JPEG|JPG|PNG|BMP|GIF)$/) {
            files << file.path
        }
    }
    //Escreve arquivo files.lst
    def to_compile = new File("${env.build_sources_directory}\\devops\\build\\output", 'compile.lst')

    files.each { to_compile << it
        to_compile << ';'
    }
}

//check error
task check_errors << {
    def filePath = "${env.build_sources_directory}\\devops\\gradle\\sonar_remover\\data\\fonts_with_bugs.json"

    File file = new File(filePath)
    def line, noOfLines = 0
    file.withReader { reader ->
        while ((line = reader.readLine()) != null) {
            println "${line}"
            noOfLines++
            if (line.toUpperCase().contains('.PRW')) {
                println('Contém bug novo')

            }
        }
    }
}
