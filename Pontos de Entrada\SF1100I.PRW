#Include 'Protheus.ch'
#Include 'topconn.ch'
#INCLUDE "FWMVCDEF.CH"

/*/{Protheus.doc} SF1100I
Ponto de Entrada na Inclusao de documento de Entrada
<AUTHOR>
@since 28/09/2023
@version 1.0
/*/
User Function SF1100I()
Local aArea		:= GetArea()
Local cAliasSE2 := GetNextAlias()
Local lUsaNG    := GetMv("TI_X2NATGE",,.T.) 

If lUsaNG .And. !empty(SF1->F1_DUPL) 

	BeginSql Alias cAliasSE2
		SELECT SE2.R_E_C_N_O_ AS RECSE2 
		FROM %Table:SE2% SE2
		WHERE
			SE2.E2_FILIAL = %xFilial:SE2% AND
			SE2.E2_PREFIXO = %Exp:SF1->F1_PREFIXO% AND
			SE2.E2_NUM = %Exp:SF1->F1_DUPL% AND
			SE2.E2_FORNECE = %Exp:SF1->F1_FORNECE% AND
			SE2.E2_LOJA = %Exp:SF1->F1_LOJA% AND
			E2_XNATGER = ' ' AND 
			SE2.%NotDel%
	EndSql

	While (cAliasSE2)->(!Eof())
		SE2->(dbgoto ((cAliasSE2)->(RECSE2)))
		SE2->(RecLock('SE2', .F.))
		SE2->E2_XNATGER := SE2->E2_NATUREZ 
		SE2->(MsUnlock())

		(cAliasSE2)->(DbSkip())
	End 

	(cAliasSE2)->(DbCloseArea())
EndIf 
	
RestArea(aArea)
Return

