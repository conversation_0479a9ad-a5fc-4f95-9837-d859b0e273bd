#Include "RWMAKE.CH"
#Include "TOPCONN.CH"
#Include "Protheus.Ch"
#include "TbiConn.ch"
//-------------------------------------------------------------------
/*/{Protheus.doc}TFINA103
Programa que atualiza o campo de moeda nas tabelas FK2 e SE5
<AUTHOR>
@since  20/11/2020
@version 12
/*/
//-------------------------------------------------------------------
User Function TFINA103()

Local cUpd  := ""
Local cUpd1 := ""

RpcSetType(3)

IF !RpcSetEnv('00', '00001000100')
	Final("Erro ao abrir ambiente")
    Return 
ELSE
    cUpd := " UPDATE FK2010 "
    cUpd += " SET FK2_MOEDA = '01'"
    cUpd += " WHERE D_E_L_E_T_ <> '*'" 
    cUpd += " AND FK2_ORIGEM IN ('FINA381','TFINA381') "    
    cUpd += " AND FK2_MOEDA = '1'"
    TCSQLExec(cUpd)

    cUpd1 := " UPDATE SE5010 "
    cUpd1 += " SET E5_MOEDA = '01'"
    cUpd1 += " WHERE D_E_L_E_T_ <> '*'" 
    cUpd1 += " AND E5_ORIGEM IN ('FINA381','TFINA381') "    
    cUpd1 += " AND E5_MOEDA = '1'"
    TCSQLExec(cUpd1)
   
ENDIF
Return


