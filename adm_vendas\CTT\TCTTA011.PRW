#Include "FWMVCDef.CH"
#INCLUDE "PROTHEUS.CH"
/*/{Protheus.doc} GravarTabela
Crud para cadastro de linha de produtos do CTT
@type user function
<AUTHOR>
@since 13/07/2021
/*/
user  function TCTTA011()
    local oFWMBrowse	:= nil
    local cfiltro       := U_aParamGet(2,'TI_CTTTREI')

    oFWMBrowse = FWMBrowse():New()
    oFWMBrowse:SetDescription("Linha de produtos e treinamentos")
    oFWMBrowse:SetAlias("ZX5")
    oFWMBrowse:SetOnlyFields({"ZX5_TABELA", "ZX5_CHAVE","ZX5_CHAVE2","ZX5_DESCRI"})
    oFWMBrowse:SetFilterDefault(cfiltro)
    oFWMBrowse:Activate()
return nil

static Function MenuDef()
    local aRot := {}

	ADD OPTION aRot TITLE 'Visualizar' ACTION 'VIEWDEF.TCTTA011' OPERATION MODEL_OPERATION_VIEW   ACCESS 0 //OPERATION 1	
	ADD OPTION aRot TITLE 'Incluir'    ACTION 'VIEWDEF.TCTTA011' OPERATION MODEL_OPERATION_INSERT ACCESS 0 //OPERATION 3
	ADD OPTION aRot TITLE 'Alterar'    ACTION 'VIEWDEF.TCTTA011' OPERATION MODEL_OPERATION_UPDATE ACCESS 0 //OPERATION 4
	ADD OPTION aRot TITLE 'Excluir'    ACTION 'VIEWDEF.TCTTA011' OPERATION MODEL_OPERATION_DELETE ACCESS 0 //OPERATION 5
    ADD OPTION aRot TITLE 'Integrar LMS'    ACTION 'U_SendToLMSApi' OPERATION 6                      ACCESS 0 //OPERATION X
Return aRot

static Function ModelDef()

	Local oStruZX5:= FWFormStruct( 1, 'ZX5' )
	Local oModel := nil
    local cInitZX5_Tab   := "U_aParamGet(1, 'TI_CTTTREI')"
    local cInitZX5_chave :=  'U_NextCode("ZX5", "ZX5_CHAVE",' +  '"AND ZX5_TABELA ='+"'CTTPRO'"  +'"' +')'

    oStruZX5:SetProperty('ZX5_TABELA',   MODEL_FIELD_INIT,  FwBuildFeature(STRUCT_FEATURE_INIPAD,cInitZX5_Tab)) 
    oStruZX5:SetProperty('ZX5_CHAVE',    MODEL_FIELD_INIT,  FwBuildFeature(STRUCT_FEATURE_INIPAD,cInitZX5_chave))
    oStruZX5:SetProperty("ZX5_TABELA", MODEL_FIELD_WHEN, {|| .F.})
    oStruZX5:SetProperty("ZX5_CHAVE", MODEL_FIELD_WHEN, {|| .F.})
	
	oModel := MPFormModel():New('COMP011M' )
	oModel:AddFields( 'ZX5MASTER', /*cOwner*/, oStruZX5)
    oModel:SetPrimaryKey({'ZX5_TABELA','ZX5_CHAVE'})
	oModel:SetDescription( 'Linha de produtos e treinamentos' )
	oModel:GetModel( 'ZX5MASTER' ):SetDescription( 'Linha de produtos e treinamentos' )

Return oModel

static Function ViewDef()
	
    local oModel := FWLoadModel( 'TCTTA011' )
    local oStruZX5 := FWFormStruct( 2, 'ZX5' )
    local oView := FWFormView():New()

    oView:SetModel( oModel )
    oView:AddField( 'VIEW_ZX5', oStruZX5, 'ZX5MASTER' )
    oView:CreateHorizontalBox( 'TELA' , 100 )
    oView:SetOwnerView( 'VIEW_ZX5', 'TELA' )
    
    oStruZX5:RemoveField('ZX5_FILIAL')
    oStruZX5:RemoveField('ZX5_DESESP')
    oStruZX5:RemoveField('ZX5_DESENG')
    oStruZX5:RemoveField('ZX5_COMPL')
    oStruZX5:RemoveField('ZX5_MEMO')
    oStruZX5:RemoveField('ZX5_CHAVE2')

Return oView

