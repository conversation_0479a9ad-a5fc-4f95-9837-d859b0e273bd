#include "protheus.ch"
//TIADMVIN-3138
User Function Tgcvxc07
Return 

Class Tgcvxc07
    Data oCTbCro
    Data oCtbCmp

    Data cNota   
    Data cSerie  
    Data cItemNf 
    
    Data cStatusFin
	Data nPH5VlrTot
	Data nPH5VlTrib
    Data nPH5VlAFat
	Data nPH5VlAFaS
    Data nPH5VlFat 
	Data nPH5VlFatS
    Data nPH5VlSld  
    Data nPH5Quant
    Data nPH5QtdCan
    Data nPH5QtdRea
    Data nPH5QtdFat  
    Data nPH5VlCar 
    Data nPH5VlBon 
    Data nPH5VlCan 
    Data nPH5VlReat
    Data nPH5VlReaj
    Data nPH5VlInc
    Data cPH5FlCorp
    Data nPH5VlNova
    Data cNumMed
    Data cSequen
    Data nPH5BilUp   
    Data nPH5BilDown
    Data cPH5BilId  
    Data nPH5BilQtd 
    Data nPH5BilVlr 
    Data nPH5BilVar 
    Data nPH5VlTraS
    Data nPH5VlTraE
    Data nPH5RoyVar
    Data cPH5RoyID 
    Data nPH5Multa
    Data nPH5VlREX
    Data nPH5VlTroca
    Data nPH5PerRat
    Data nRecnoPH5
    Data nPH5VlInt
    Data nPH5IntCan
    
    Method New(oCTbCro, oCtbCmp) 
    
EndClass


Method New(oCtbCro, oCtbCmp) Class Tgcvxc07
    ::oCtbCro    := oCTbCro
    ::oCtbCmp    := oCtbCmp
    
    ::cNota      := ""
    ::cSerie     := "" 
    ::cItemNf    := "" 
    ::cStatusFin := ""
	::nPH5VlrTot := 0
	::nPH5VlTrib := 0 
    ::nPH5VlAFat := 0
	::nPH5VlAFaS := 0
    ::nPH5VlFat  := 0
	::nPH5VlFatS := 0
    ::nPH5VlSld  := 0
    ::nPH5Quant  := 0
    ::nPH5QtdCan := 0
    ::nPH5QtdRea := 0
    ::nPH5QtdFat := 0
    ::nPH5VlCar  := 0
    ::nPH5VlBon  := 0
    ::nPH5VlCan  := 0
    ::nPH5VlReat := 0
    ::nPH5VlReaj := 0
    ::nPH5VlInc  := 0
    ::cPH5FlCorp := ""
    ::nPH5VlNova := 0
    ::cNumMed    := ""
    ::cSequen    := ""
    ::nRecnoPH5  := 0
    ::nPH5BilUp  := 0  
    ::nPH5BilDown:= 0
    ::cPH5BilId  := ""
    ::nPH5BilQtd := 0
    ::nPH5BilVlr := 0
    ::nPH5BilVar := 0
    ::nPH5VlTraS := 0
    ::nPH5VlTraE := 0
    ::nPH5RoyVar := 0
    ::cPH5RoyId  := ""
    ::nPH5Multa  := 0
    ::nPH5VlREX  := 0
    ::nPH5VlTroca := 0
    ::nPH5PerRat := 0
    ::nPH5VlInt  := 0
    ::nPH5IntCan := 0

Return
