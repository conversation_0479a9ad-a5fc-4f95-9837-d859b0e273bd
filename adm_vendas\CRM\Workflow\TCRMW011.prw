#include 'protheus.ch'

/*/{Protheus.doc} TCRMW011
Envio de WF para Cloud - Aditivo Cloud
<AUTHOR>
@since 16/05/2019
@version undefined
@param cProposta, characters, descricao
@param cStatus, characters, descricao
@param cMemo, characters, descricao
@type function
/*/
User Function TCRMW011(cOport,cRevisa)

    Local aArea			:= GetArea()
    Local cPathOrigem	:= GetMV("TI_PATHHTM",,"\workflow\")
    Local cPathTemp		:= GetMV("TI_PTEMPWF",,"\workflow\temp\")
    Local cHTMLOrigem	:= GetMV("TI_HTMLHCR",,"WFHospedagemCloudRecompra.htm")
    Local cFile			:= CriaTrab(nil,.f.)+".htm"
    Local cRet			:= ""
    Local cTitMail		:= "Hospedagem Cloud - Proposta para Aditivo Cloud"
    Local cEMail		:= GetMV("TI_MAILHCR",,"")
    Local cDadosCliente	:= ""

    MakeDir( cPathTemp )

    AD1->(dbSetOrder(1))
    AD1->(dbSeek(xFilial('AD1') + cOport + cRevisa))

    SA1->(dbSetOrder(1))
    SA1->(dbSeek(xFilial('SA1')+AD1->(AD1_CODCLI+AD1_LOJCLI)))

	cDadosCliente	:= AD1->AD1_CODCLI + " - " + SA1->A1_NOME

    oHTML := TWFHtml():New(cPathOrigem+cHTMLOrigem)

    //preenche dados
    oHTML:ValByName( "CDADOSCLIENTE" 	, cDadosCliente )
    oHTML:ValByName( "COPORTUNIDADE" 	, cOport )
    oHTML:ValByName( "CDESCOPOR" 		, AllTrim(AD1->AD1_DESCRI) )
    oHTML:ValByName( "CDATA"          	, dtoc(AD1->AD1_DATA) )
    oHTML:ValByName( "CESN" 		    , Alltrim(AD1->AD1_VEND) + " - " + Alltrim(Posicione("SA3",1,xFilial("SA3")+AD1->AD1_VEND,"A3_NOME")) )
    oHTML:ValByName( "ANO"          	, AllTrim(Str(Year(dDataBase))))
    oHTML:ValByName( "NOMEHTM"        	, cHTMLOrigem )

    // envia WF
    oHTML:Savefile(cPathTemp+cFile)
    cRet := WFLoadFile(cPathTemp+cFile)
    cRet := StrTran(cRet,chr(13)+chr(10),"")

    U_xSendMail( cEMail, cTitMail, cRet,,.T., ,,.T.,.t.)	 //Envia E-mail

    FErase(cPathTemp+cFile)

    RestArea(aArea)

Return