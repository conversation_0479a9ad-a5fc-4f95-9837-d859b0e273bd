#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Utils.ConstructEnvironmentUtils

/*/{Protheus.doc} ConstructEnvironment
This Class aims to create an environment on server
@type class
@version  1.0
<AUTHOR>
@since 11/7/2024
/*/
Class   ConstructEnvironment
    Static  Method  initEnvironment()
EndClass

/*/{Protheus.doc} ConstructEnvironment::initEnvironment
This method aims to create an environment in Protheus, allowing
routines to access system tables and perform operations on them
@type method
@version  2.0
<AUTHOR>
@since 10/28/2024
@return Variant, Return a Nil response
/*/
Method  initEnvironment()  Class  ConstructEnvironment
	If Select("SM0") == 0
		RpcSetType(3)
		RPCSetEnv("00","00001000100")
	EndIf
Return Nil
