#INCLUDE "PROTHEUS.CH"

#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"
#INCLUDE "TRYEXCEPTION.CH"
#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} TCTBA072 
Funcao responsavel pela callback do interceptor referente a gravar dados na P37 de outra empresa ou MPN
@type  Function
<AUTHOR> Menabue Lima
@since 30/05/2023
@version 1.0
/*/
User Function TCTBA072(nPar)
//U_TCTBA072(14019827)
	Local oJson         := JsonObject():new()
	Local oBody
	Local oResponse 	:=  JsonObject():new()
	Local cFilMpn		:= SuperGetMv("TI_TBA069F",.F.,"XXXXXXXXXXX")//Filiais que vao entrar no Midleware 02601000100
	Local cEmpMiddle	:= SuperGetMv("TI_TBA069E",.F.,"XX")//Filiais que vao entrar no Midleware 60
	Local nRecP37		:=0
	Local cURlMpn		:=SuperGetMv("TI_TBA69UM",.F.,"http://172.24.35.212:8048/TIINTERCEPTORWS/api/v1")//URL PARA O REST DO MPN
	Local cURlDimen		:=SuperGetMv("TI_TBA69UD",.F.,"http://172.24.35.106:8041/rest/TIINTERCPTDIMEN/api/v1")//URL PARA O REST DA DIMENSA
	Local oBodySend 	:= JsonObject():new()
	Default nPar        := 0
	if(nPar==0)
		oBody:= oJson:FromJson(PARAMIXB[2])//cBody     := '{ "body":'+cJson+', "requisicao": "'+cReq+'","filial":"'+cFil+'", "empresa":"'+cEmpresa+'" }'
	Else
		DBSELECTAREA( "P37" )
		P37->(DBSETORDER(1))
		P37->(DBGOTO(nPar))//14189199
		oBody:= oJson:FromJson(P37->P37_BODY)
	EndIF
	oResponse["status" ] := 200

	__cUserID := "000000"
	If VALTYPE(oBody) == 'U'
		cBody	 :=EncodeUtf8(oJson:GetJsonObject("body"):toJSON())
		cFilDado :=oJson:GetJsonObject("filial")
		cEmpresa :=oJson:GetJsonObject("empresa")
		nRecP37	 :=oJson:GetJsonObject("recno")

		if( Empty(P37->P37_BODYRP))
			cUrl:=""
			if(cFilDado == cFilMpn  )
				cUrl:=cURlMpn
			Else
				if(cEmpresa $ cEmpMiddle)
					cUrl:=cURlDimen
				EndIF
			EndIF
			cMethdAuth:="POST"
			cRespBody:=HTTPQUOTE( cUrl+"/000103", cMethdAuth,"", cBody, 120,  {})
			lSucess:=!Empty( cRespBody )
			IF(lSucess)
				oResponse:FromJson(cRespBody)
				nRec:= 0
				if(oResponse["status" ] ==200)
					cStatus  := "0"
				Else
					cStatus  := "5"
					nRec:=nRecP37
				EndIF
				SetP37Status(nRec,cRespBody,cStatus,.T.)
			Else
				oResponse["status" ] := 500
				oResponse["mensagem" ] := cRespBody
				SetP37Status(nRecP37,cRespBody,"3",.T.)
			EndIF

		ElseIF( !Empty(P37->P37_BODYRP))//Consulta o Status
			oBodySend:FromJson(P37->P37_BODYRP)
			cUrl:=""
			if(cFilDado == cFilMpn  )
				cUrl:=cURlMpn
			Else
				if(cEmpresa $ cEmpMiddle)
					cUrl:=cURlDimen
				EndIF
			EndIF
			cUrl+="/request/"+oBodySend:GetJsonObject("RequestID")
			cMethdAuth:="GET"
			cRespBody:=HTTPQUOTE( cUrl, cMethdAuth,"", "", 120,  {})
			lSucess:=!Empty( cRespBody )
			IF(lSucess)
				oResponse:FromJson(cRespBody)
				if( !(oResponse["statusRequest" ] $ 'F|0|1'))
					cStatus  := oResponse["statusRequest" ]
					SetP37Status(nRecP37,oResponse["body"] ,cStatus,.f.)
				Else
					cStatus  := "1"
					SetP37Status(nRecP37,"" ,cStatus,.f.)
				EndIF
			Else

				oResponse["status" ] := 500
				oResponse["mensagem" ] := cRespBody
				SetP37Status(nRecP37,cRespBody,"3",.f.)
			EndIF


		EndIF

	Else
		P37->(RecLock("P37",.F.))
		oResponse["status" ] := 500
		oResponse["mensagem" ] := "Body is Empty"
		P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
		P37->P37_STATUS := "3"
		P37->P37_TIMERP:= TIME()
		P37->(MsUnLock())
	EndIF
	FreeObj(oBody)
	FreeObj(oBodySend)
	FreeObj(oResponse)
Return

/*/{Protheus.doc} SetP37Status
	Seta o status tanto na P37 PRINCIPAL quanto na volta
	@type    Function
	<AUTHOR> Menabue Lima
	@since 06/09/2023
	@version 12.1.33
/*/
Static Function SetP37Status(nRecPrin,cBody,cStatus,lBody)
	Local aAreaP37:= P37->(GetArea())
	P37->(RecLock("P37",.F.))
	if (lBody)
		if(!Empty(cBody))
			P37->P37_BODYRP := cBody
		EndIF
	EndIF
	P37->P37_STATUS := cStatus
	P37->P37_TIMERE := TIME()
	P37->(MsUnLock())
	if(nRecPrin > 0)
		//Atualiza a msm msg na requisicao principal
		DbSelectArea("P37")
		P37->(DBGOTO(nRecPrin))
		P37->(RecLock("P37",.F.))
		if(!Empty(cBody))
			P37->P37_BODYRP := cBody
			P37->P37_STATUS := cStatus
		EndIF
		P37->P37_TIMERE := TIME()
		P37->(MsUnLock())
	EndIF
	restArea(aAreaP37)
Return
