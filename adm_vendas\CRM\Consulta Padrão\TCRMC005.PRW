#include 'totvs.ch'

/*/{Protheus.doc} TC98Tipo
Retorna checkbox para campo 
<AUTHOR>
@since 14/07/2017
@version 1.0
@param nLanguage, numeric, Lingua (1=POR;2=ESP;3=ING)
@type function
/*/
User Function TCRMC005(nLanguage)
Local cRet			:= ""
Local ctemp			:= ""
Local cTCBOXTV		:= GetMV("TI_TCBOXTV",,"CRM017")
Default nLanguage	:= 1

dbSelectArea("ZX5")
dbSetOrder(1)
dbSeek(xFilial("ZX5")+cTCBOXTV)

Do While !ZX5->(EoF()) .and. ZX5->(ZX5_FILIAL+ZX5_TABELA)==xFilial("ZX5")+cTCBOXTV

	If !Empty(ZX5->ZX5_CHAVE)
		
		cRet += Iif(Empty(cRet),"",";")
		cRet += Alltrim(ZX5->ZX5_CHAVE)+"="
		cTemp := ""
		
		If nLanguage==3
			cTemp := Alltrim(ZX5->ZX5_DESENG)
		ElseIf nlanguage==2
			cTemp := Alltrim(ZX5->ZX5_DESESP)
		EndIf
		
		If nLanguage==1 .and. Empty(cTemp)
			cTemp := Alltrim(ZX5->ZX5_DESCRI)
		EndIf
		
		cREt += cTemp
		
	EndIf
	
	ZX5->(dbSkip())
	
EndDo

Return(cRet)
