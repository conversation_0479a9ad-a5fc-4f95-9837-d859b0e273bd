#include "tlpp-core.th"


Class   BasicDataToSendEmailDTO
    Public  Data    messageGroupId  As  Character
    Public  Data    delay           As  Numeric
    Public  Data    count           As  Numeric
    Public  Data    dlq             As  Character
    Public  Data    queue           As  Character
    Public  Data    MessageBody     As  Array
    Public  Data    text            As  Character
    Public  Data    subject         As  Character

    Public  Method  new()           As  Object
    Public  Method  toJson() As Character
    Public  Method  addBcc(email    As  Character, name As  Character) As  Object
    Public  Method  addDynamicData(oDynamicData     As  Object) As  Object

EndClass



Method  new()   As  Object  Class   BasicDataToSendEmailDTO
    ::messageGroupId        :=    ''
    ::delay                 :=    0
    ::count                 :=    0
    ::dlq                   :=    ''
    ::queue                 :=    ''
    ::MessageBody           :=    {}
    ::text                  :=    ''
    ::subject               :=    ''
    
    Aadd(::MessageBody,MessageBodyDTO():new())
Return Self    


Method  addBcc(email    As  Character, name As  Character) As  Object   Class   BasicDataToSendEmailDTO
    Local oBccToAdd :=  Nil As  Object
    oBccToAdd   :=  ConstructionEmailDTO():new()
    oBccToAdd:name      :=  name
    oBccToAdd:email     :=  email

    Aadd(self:MessageBody[1]:bcc, oBccToAdd)

    //FreeObj(oBccToAdd)
    //oBccToAdd   :=  Nil
Return Self


Method  addDynamicData(oDynamicData     As  Object) As  Object     Class   BasicDataToSendEmailDTO
    self:MessageBody[1]:dynamicData := oDynamicData
Return Self



Method toJson() As Character Class BasicDataToSendEmailDTO
    Local   oJsonRoot       := JsonObject():New()
    Local   oMessageBody    :=  JsonObject():New() 
    Local   oBcc            :=  JsonObject():New() 
    Local   nX              :=  1   As  Numeric


    // Dados simples
    oJsonRoot['messageGroupId'] :=  Self:messageGroupId
    oJsonRoot['delay']          :=  Self:delay
    oJsonRoot['count']          :=  Self:count
    oJsonRoot['dlq']            :=  Self:dlq
    oJsonRoot['queue']          :=  Self:queue
    oJsonRoot['MessageBody']    :=  {}

    oMessageBody['text']            :=  self:MessageBody[1]:text
    oMessageBody['subject']         :=  self:MessageBody[1]:subject
    oMessageBody['template']        :=  self:MessageBody[1]:template
    oMessageBody['bucket']          :=  self:MessageBody[1]:bucket


    oMessageBody['from']            :=  JsonObject():new()
    oMessageBody['from']['email']   :=  self:MessageBody[1]:from1:email
    oMessageBody['from']['name']    :=  self:MessageBody[1]:from1:name

    oMessageBody['to']              :=  JsonObject():new()
    oMessageBody['to']['email']     :=  self:MessageBody[1]:to1:email
    oMessageBody['to']['name']      :=  self:MessageBody[1]:to1:name

    oMessageBody['bcc']              :=  {}
    For nX  :=  1   to  len(self:MessageBody[1]:bcc)
        oBcc    :=  JsonObject():new()
        oBcc['name']    :=      self:MessageBody[1]:bcc[nX]:name  
        oBcc['email']    :=      self:MessageBody[1]:bcc[nX]:email                             
        Aadd(oMessageBody['bcc'], oBcc)
    Next    nX

    oMessageBody['dynamicData'] := self:MessageBody[1]:dynamicData:transformToJson()

    Aadd(oJsonRoot['MessageBody'], oMessageBody)



Return oJsonRoot:ToJson()


