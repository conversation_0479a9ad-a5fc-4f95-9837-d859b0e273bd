#include "tlpp-core.th"


Class   ContractDataToCheckoutTableDTO
    Public  Data    cliente         As  Character
    Public  Data    loja            As  Character
    Public  Data    contrato        As  Character
    Public  Data    numero          As  Character
    Public  Data    proposta        As  Character
    Public  Data    email           As  Character
    Public  Data    tipoPagamento   As  Character
    Public  Data    nVlrTitulo      As  Numeric
    Public  Data    listaProdutos   As  Array
    Public  Data    dDtVld          As  Date

    Public  Method  new()       As  Object
    Public  Method  addItem(item    As  Character, produto  As  Character, descricao As Character)   As  Object
EndClass


Method  new()   As  Object  Class   ContractDataToCheckoutTableDTO
    ::cliente           :=  ''
    ::loja              :=  ''
    ::contrato          :=  ''
    ::numero            :=  ''
    ::proposta          :=  ''
    ::email             :=  ''
    ::tipoPagamento     :=  ''
    ::nVlrTitulo        :=  0
    ::listaProdutos     :=  {}
    ::dDtVld            := ctod("")
Return Self


Method  addItem(item As     Character, produto As   Character, descricao    As  Character)   As  Object     Class   ContractDataToCheckoutTableDTO
    Local   oItem   :=  JsonObject():new()

    oItem['item']       :=  item
    oItem['produto']    :=  produto
    oItem['descricao']  :=  descricao

    aAdd(::listaProdutos, oItem)
    oItem   :=  Nil
Return Self
