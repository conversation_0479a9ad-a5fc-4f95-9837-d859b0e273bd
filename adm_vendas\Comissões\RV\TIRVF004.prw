#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"
#INCLUDE "TRYEXCEPTION.CH"
#define CRLF Chr(13) + Chr(10)

/*/{Protheus.doc} User Function TIRVF004
	Funcao Callback de cadastro de Areas
	@type  Function
	<AUTHOR> Menabue Lima
	@since 17/08/2023
	@version 12.1.33
 
	/*/
User Function TIRVF004(nPar)
	Local oJson         := JsonObject():new()
	Local oBody
	Local lFalhou       := .F.
	Local cFalha        := ""
	Local aLancam		:= {}
	Local oResponse 	:=  JsonObject():new()
	Private cCodReq		:= ""
	Default nPar        := 0
	if(nPar==0)
		oBody:= oJson:FromJson(PARAMIXB[2])
		cCodReq		:= PARAMIXB[1]
	Else
		DBSELECTAREA( "P37" )
		P37->(DBSETORDER(1))
		P37->(DBGOTO(nPar))//14189199
		cJson:=getJsonTst()
		//oBody:= oJson:FromJson(P37->P37_BODY)
		oBody:= oJson:FromJson(cJson)
	EndIF
	oResponse["status" ] := 200
	//Desmarca o registro para evitar sujeira
	P37->(RecLock("P37",.F.))
	P37->P37_STATUS := "F"
	P37->P37_TIMERE := TIME()
	P37->P37_BODYRP := '{ "mensagem":"Preparando pra processamento"}'
	P37->(MsUnLock())
	__cUserID := "000000"
	If VALTYPE(oBody) == 'U'
		aLancam := oJson:GetJsonObject("items")
		If ValType(aLancam) == "A"
			If Len(aLancam) > 0
				cFalha :=SaveArea(aLancam)
				lFalhou:=!Empty(Alltrim(cFalha))
				P37->(RecLock("P37",.F.))
				If lFalhou
					P37->P37_STATUS := "5"
					oResponse["status" ]  := 500
					oResponse["mensagem" ]:=cFalha
					P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
					P37->P37_TIMERP:= TIME()
				Else
					P37->P37_STATUS := "2"
					P37->P37_BODYRP := '{"status":200, "mensagem":"Registros incluidos com sucesso"}'
					P37->P37_TIMERP:= TIME()
				EndIf
				P37->(MsUnLock())
				oResponse := Nil
				FreeObj(oResponse)
			Else
				P37->(RecLock("P37",.F.))
				oResponse["status" ] := 500
				oResponse["mensagem" ] := "no items"
				P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
				P37->P37_STATUS := "3"
				P37->P37_TIMERP:= TIME()
				P37->(MsUnLock())
			EndIF
		Else
			P37->(RecLock("P37",.F.))
			oResponse["status" ] := 500
			oResponse["mensagem" ] := "items is not Array"
			P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
			P37->P37_STATUS := "3"
			P37->P37_TIMERP:= TIME()
			P37->(MsUnLock())
		EndIF
	Else
		P37->(RecLock("P37",.F.))
		oResponse["status" ] := 500
		oResponse["mensagem" ] := "Body is Empty"
		P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
		P37->P37_STATUS := "3"
		P37->P37_TIMERP:= TIME()
		P37->(MsUnLock())
	EndIF
Return

/*/{Protheus.doc} SaveArea
	Salva os lancamentos Contabeis ""
	@type   Function
	<AUTHOR> Menabue Lima
	@since 29/08/2022
	@version 12.1.33
 
/*/
Static Function SaveArea(aDados)
	Local nI
	Local aArea         := GetArea()
	Local aItens        := {}
	Local cCodigo	 	:=""
	Local cDescricao 	:=""
	Local cResponsa  	:=""
	Local oError
	Local cError		:=""
	Local lValid		:=.F.
	Local nJ			:= 0
	Default cEmp 		:= cEmpAnt
	Default cFil 		:= cFilAnt
	Private lMsErroAuto := .F.
	Private lMsHelpAuto := .T.
	TRYEXCEPTION

	nTot:=Len(aDados)
	For nI:= 1 to nTot
		lValid		:= .F.
		lMsErroAuto := .F.
		lMsHelpAuto := .T.
		aItens :={}
		P37->(RecLock("P37",.F.))
		P37->P37_BODYRP := '{ "mensagem":"Gravando Linhas '+cValToChar(nI)+' de '+cValToChar(nTot)+'  "}'
		P37->(MsUnLock())
		cCodigo			:= aDados[nI]["codigo"]
		cDescricao		:= aDados[nI]["descricao"]
		cResponsa		:= aDados[nI]["responsavel"]


		aAdd(aItens,  { ;
			{'P79_FILIAL' 	,xFilial("P79")				, NIL},;
			{'P79_CODIGO' 	,cCodigo					, NIL},;
			{'P79_DESCRI'	,cDescricao					, NIL},;
			{'P79_RESPON' 	,cResponsa 					, NIL}})
		DbSelectArea("P79")
		DbSetOrder(1)
		nOpcR:=3
		If(P79->(Dbseek(xFilial("P79")+cCodigo)))
			nOpcR:=4
			//Verifica se ao menos um campo na alteracao esta diferente para evitar erro de salvar formulario nao alterado
			aItem:=aItens[1]
			For nJ:=1 to len(aItem)
				if(Alltrim(aItem[nJ][2]) <> Alltrim(P79->&(aItem[nJ][1])))
					lValid:=.T.
					Exit
				EndIF
			Next nJ
		Else
			lValid:=.T.
		EndIF
		if lValid
			MSExecAuto({|x, y| U_TIRVA004(x,y)}, aItens, nOpcR)

			If !lMsErroAuto

			Else // EM ESTADO DE JOB
				cTime:= dTos(Date()) + strtran(IncTime(time() ,3,0,0 ),":")
				cError := MostraErro("/system/RVRM/", "TIRVF004_"+cTime+".log")
			EndIf
		EndIF
	Next nI
	CATCHEXCEPTION USING oError
	cMsgFault:=""
	IF ( ValType( oError ) == "O" )
		cMsgFault := IF( !Empty( oError:Description )	, oError:Description	, "" )
		cMsgFault += IF( !Empty( oError:ErrorStack )	, oError:ErrorStack 	, "" )

	EndIF
	cError:='{ "mensagem":"Error '+cMsgFault+'"}'
	IF InTransact()
		DisarmTransaction()
	EndIF
	ENDEXCEPTION
	RestArea(aArea)

Return cError

/*/{Protheus.doc} User Function getJsonTst
Funcao para teste de inclusao e alteracao de dados
	@type  Function
	<AUTHOR> Menabue Lima
	@since 21/08/2023
	@version 12.1.33
/*/

Static Function getJsonTst()
	Local nI:= 1
	Local cJson:= '{ "items" :['
	For nI:= 1 to  10
		cJson+=	'			{'
		cJson+=	'				"codigo"  		: "0000'+StrZero(nI,2)+'",   '
		cJson+=	'				"descricao" 	: "BEMATEC",   '
		cJson+=	'				"responsavel"	: "SUB00'+StrZero(nI,2)+'"   '
		cJson+=	'			},'
	Next nI
	cJson:=SUBSTR( cJson, 1, len(cJson)-1)
	cJson+= "] }"

Return cJson
