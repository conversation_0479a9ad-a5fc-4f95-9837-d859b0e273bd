#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVJ014     
Job Responsavel por criar a P37 para consultar a API do PSA View  Informacoes PSA - CUBO SERVICOS
@type  Function
<AUTHOR> Menabue Lima
@since 27/10/2023
@version 1.0
U_TIRVJ009(,,,"2023-11-01","2023-11-30",.F.)
U_TIRVJ009(,,,"2023-10-01","2023-10-31",.F.)
U_TIRVJ009(,,,"2023-11-01","2023-11-30",.T.)
U_TIRVJ009(,,,"2023-10-01","2023-10-31",.T.)
U_TIRVJ009(,,,"2023-09-01","2023-09-30",.F.)
U_TIRVJ009(,,,"2023-08-01","2023-08-31",.F.)
U_TIRVJ009(,,,"2023-07-01","2023-07-31",.F.)
U_TIRVJ009(,,,"2023-09-01","2023-09-30",.T.,"")
U_TIRVJ009(,,,"2023-08-01","2023-08-31",.T.,"")
U_TIRVJ009(,,,"2023-07-01","2023-07-31",.T.,"")
U_TIRVJ009(,,,"2023-07-01","2023-07-31",.T.,"SET/2023")
U_TIRVJ009(,,,"2023-07-01","2023-07-31",.T.,"AGO/2023")
U_TIRVJ009(,,,"2023-07-01","2023-07-31",.T.,"JUL/2023")
	/*/
User Function TIRVJ014(nCount,nPage,cCodReq,cIni,cFim,lIni,cMesName,cCodPrj)
	Local oReceptor:=TIINTERCEPTOR():New()
	Local cFecthXml :=""
	Local cApiPSA:=GetMv("TI_RVJ014A" , .F., "RVF014")
	Local cNaoTrazSR:=GetMv("TI_RVJ014B" , .F., .T.)

	Default nCount := GetMv("TI_RVJ14CT" , .F., 5000)
	Default nPage := 1
	Default cCodReq:=""
	Default cIni :=""
	Default cFim :=""
	Default lIni := .T.
	Default cMesName :=""
	Default cCodPrj :=""
	cFecthXml +='<fetch no-lock="true" latematerialize="true" count="'+cValToChar(nCount)+'" page="'+cValToChar(nPage)+'">'
	cFecthXml +='<entity name="salesorder">
	cFecthXml +='<attribute name="ordernumber" alias="NUMERO_CONTRATO" />
	cFecthXml +='<attribute name="ctm_metodocob" alias="CONTRATO_METODO_COBRANCA" />
	cFecthXml +='<attribute name="ctm_codigo" alias="NRO_PROPOSTA" />
	cFecthXml +='<attribute name="ctm_pl_modalidadeprojeto" alias="MODALIDADE_CONTRATO" />
	cFecthXml +='<attribute name="ctm_margemvenda" alias="MARGEM_VENDA_CONTRATO" />
	cFecthXml +='<attribute name="ctm_dataencerramentocontrato" alias="DATA_ENCERRAMENTO_CONTRATO" />
	cFecthXml +='<attribute name="ctm_vlrtotalliq" alias="VALOR_TOTAL_LIQUIDO" />
	cFecthXml +='<attribute name="ctm_totalhorasvendidas" alias="QUANTIDADE_ORCADA_VENDA" />
	cFecthXml +='<attribute name="ctm_data_contabilizacaoo" alias="DATA_CTB_CONTRATO" />
	cFecthXml +='<attribute name="createdon" alias="DATA_CRIACAO_CONTRATO" />
	cFecthXml +='<attribute name="modifiedon" alias="DATA_MODIFICACAO_CONTRATO" />
	cFecthXml +='<attribute name="ctm_tipodeprojeto" alias="CONTRATO_TIPO_PROJETO" />
	cFecthXml +='<attribute name="ctm_dataemissaomigracao" alias="CONTRATO_DT_MIGRA" />
	cFecthXml +='<link-entity name="quote" from="quoteid" to="quoteid" link-type="inner" alias="cotacao">
	cFecthXml +='<attribute name="ctm_vlrhorasorcadas" alias="COTACAO_VALOR" />
	cFecthXml +='<attribute name="ctm_margemvendida" alias="COTACAO_MARGEM_VENDIDA" />
	cFecthXml +='<attribute name="ctm_metodocobranca" alias="COTACAO_METODO_COBRANCA" />
	cFecthXml +='<attribute name="createdon" alias="COTACAO_CREATED_ON" />
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="ctm_empresafilial" from="ctm_empresafilialid" to="ctm_empresafilial" link-type="outer" alias="empresa_filial">
	cFecthXml +='<attribute name="ctm_codigo" alias="CODIGO_EMPRESA_FILIAL" />
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="msdyn_organizationalunit" from="msdyn_organizationalunitid" to="msdyn_contractorganizationalunitid" link-type="outer" alias="unidade_contrato">
	cFecthXml +='<attribute name="ctm_codigo" alias="CODIGO_UNIDADE_CONTRATO" />
	cFecthXml +='<attribute name="msdyn_name" alias="NOME_UNIDADE_CONTRATO" />
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="account" from="accountid" to="customerid" link-type="outer" alias="cliente">
	cFecthXml +='<attribute name="name" alias="CLIENTE_PROJETO" />
	cFecthXml +='<attribute name="ctm_codigo" alias="CODIGO_CLIENTE_PROJETO" />
	cFecthXml +='</link-entity>
	cFecthXml +='<link-entity name="ctm_mes_contabil" from="ctm_mes_contabilid" to="ctm_mes_contabil" link-type="outer" alias="mes_contabil">
	cFecthXml +='<attribute name="ctm_data_fim" alias="DT_CTB_FIN" />
	cFecthXml +='<attribute name="ctm_data_inicio" alias="DT_CTB_INI" />
	cFecthXml +='<attribute name="ctm_mes_contabilid" alias="MES_CONTABIL_ID" />
	cFecthXml +='<attribute name="ctm_name" alias="MES_CONTABIL" />
	//cFecthXml +='<filter>
	//cFecthXml +="<condition attribute='ctm_data_inicio' operator='on-or-after' value='"+cIni+"' />
	//cFecthXml +="<condition attribute='ctm_data_inicio' operator='on-or-before' value='"+cFim+"' />
	//cFecthXml +='</filter>
	cFecthXml +='</link-entity>

	cFecthXml +='<link-entity name="msdyn_project" from="msdyn_salesorderid" to="salesorderid" link-type="outer" alias="projeto">
	cFecthXml +='<attribute name="ctm_codigo" alias="NUMERO_PROJETO" />
	cFecthXml +='<attribute name="msdyn_subject" alias="NOME_PROJETO" />
	cFecthXml +='<attribute name="statuscode" alias="RAZAO_STATUS" />
	cFecthXml +='<attribute name="msdyn_totalactualcost" alias="CUSTO_REALIZADO_PROJETO" />
	cFecthXml +='<attribute name="ctm_metodocob" alias="METODO_COBRANCA" />
	cFecthXml +='<attribute name="createdon" alias="DATA_EMISSAO_PROJETO" />
	cFecthXml +='<attribute name="ctm_valorcontratado" alias="VALOR_TOTAL_PROJETO" />
	cFecthXml +='<attribute name="ctm_pl_tipobancohoras" alias="TIPO_BANCO_HORAS" />
	cFecthXml +='<attribute name="msdyn_actualhours" alias="HORAS_REALIZADAS" />
	cFecthXml +='<attribute name="ctm_ultimo_custoplanejado_aprovado" alias="CUSTO_APROVADO" />
	cFecthXml +='<attribute name="ctm_custototalplanejado" alias="CUSTO_ESPERADO" />
	cFecthXml +='<attribute name="ctm_margemprojeto" alias="MARGEM_PROJETO" />
	cFecthXml +='<attribute name="ctm_tipodeprojeto" alias="TIPO_PROJETO" />
	cFecthXml +='<attribute name="ctm_opcaotipoprojeto" alias="OPCAO_TIPO_PROJETO" />
	cFecthXml +='<attribute name="ctm_margemgerencial" alias="MARGEM_GERENCIAL" />
	cFecthXml +='<link-entity name="ctm_classificacaoprojetofaturavel" from="ctm_classificacaoprojetofaturavelid" to="ctm_classificacaoprojetofaturavel" link-type="outer" alias="classif">
	cFecthXml +='<attribute name="ctm_name" alias="CLAS_PRJ_FAT" />
	cFecthXml +='</link-entity>


	cFecthXml +='<link-entity name="msdyn_organizationalunit" from="msdyn_organizationalunitid" to="msdyn_contractorganizationalunitid" link-type="outer" alias="unidadeProjeto">
    cFecthXml +='<attribute name="ctm_codigo" alias="CODIGO_UNIDADE_SERVICOS" />
    cFecthXml +='</link-entity>
    cFecthXml +='<link-entity name="msdyn_organizationalunit" from="msdyn_organizationalunitid" to="ctm_unidadeservicoresp" link-type="outer" alias="unidadeRespEntrega">
    cFecthXml +='<attribute name="ctm_codigo" alias="CODIGO_UNIDADE_ATENDIMENTO" />
    cFecthXml +='</link-entity>
	cFecthXml +='</link-entity>

	cFecthXml +='<filter  type="and">
	cFecthXml +='<filter type="or" >
	cFecthXml +='<filter type="and">
	cFecthXml +="<condition attribute='ctm_dataemissaomigracao' operator='on-or-after' value='"+cIni+"' />
	cFecthXml +="<condition attribute='ctm_dataemissaomigracao' operator='on-or-before' value='"+cFim+"' />
	cFecthXml +='</filter>
	cFecthXml +='<filter type="and">
	cFecthXml +="<condition entityname='cotacao' attribute='createdon' operator='on-or-after' value='"+cIni+"' />
	cFecthXml +="<condition entityname='cotacao' attribute='createdon' operator='on-or-before' value='"+cFim+"' />
	cFecthXml +='</filter>
	cFecthXml +='</filter>
	cFecthXml +='<filter type="and">
	//Filtra desconsidera contratos "Hora e Material" pois vai no outro filtro
	cFecthXml +="<condition  attribute='ctm_metodocob' operator='ne' value='192350000' />
	If cNaoTrazSR
	   cFecthXml +="<condition  attribute='ctm_metodocob' operator='ne' value='192350002' />
	EndIf
	cFecthXml +='</filter>
	cFecthXml +='</filter>
	cFecthXml +='</entity>
	cFecthXml +='</fetch>
	cCodP37:= oReceptor:GetP37COD()
	BeginTran()

	Reclock("P37",.T.)
	P37->P37_FILIAL:= Xfilial("P37")
	P37->P37_COD   :=cCodP37
	P37->P37_DATA  := ddatabase
	P37->P37_HORA  := TIME()
	P37->P37_PATH2 :=EncodeUtf8("/api/data/v9.2/salesorders?$count=true&fetchXml="+ESCAPE(cFecthXml))
	P37->P37_CODAPI:=cApiPSA
	If P36->(DbSeek(xFilial("P36")+P37->P37_CODAPI))
		P37->P37_URL   := P36->P36_URL
	EndIf
	P37->P37_METHOD:= "GET"
	P37->P37_ASYNC1:= "N"
	P37->P37_STATUS:= "0"
	P37->P37_TIPORE:= "1"//Envio
	P37->P37_CALLBA:= "U_TIRVF014"
	P37->P37_HEADER:= "Accept: application/json|OData-MaxVersion: 4.0|OData-Version: 4.0|"+'Prefer: odata.include-annotations="OData.Community.Display.V1.FormattedValue"'
	P37->P37_CODREQ:= cCodReq
	P37->P37_BODY  := '{"nPage":'+cValToChar(nPage)+', "nCount":'+cValToChar(nCount)+',"inicio":"'+cIni+'", "fim":"'+cFim+'","projeto":"'+cCodPrj+'","lInicio":"'+iif(lIni,'sim','nao')+'"}'
	P37->(MsUnlock())
	EndTran()
	FreeObj(oReceptor)
Return
