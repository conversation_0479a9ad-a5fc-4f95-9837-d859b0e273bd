#INCLUDE 'TOTVS.CH'
//----------------------------------------------------------------------------------------------------------------
/*/{Protheus.doc} TCRMG030
Gatilho para preenchimento do campo PL2_DESCRI
@param Nenhum
@return <PERSON><PERSON>
<AUTHOR>
@since 15/02/2021
/*/
//----------------------------------------------------------------------------------------------------------------
User Function TCRMG030()

Local aArea		:= GetArea()
Local cDesc		:= ""

If !Empty(FWFLDGET("PL2_CHAVE"))
    If FWFLDGET("PL2_ENTIDA") == "1"
	    cDesc := POSICIONE("SA3",1,XFILIAL("SA3")+FWFLDGET("PL2_CHAVE"),"A3_NOME")
    ElseIf FWFLDGET("PL2_ENTIDA") == "2"
        cDesc := POSICIONE("SUM",1,XFILIAL("SUM")+FWFLDGET("PL2_CHAVE"),"UM_DESC")
    EndIf
EndIf

RestArea(aArea)

Return cDesc
