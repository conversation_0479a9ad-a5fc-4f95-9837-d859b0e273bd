#Include 'TOTVS.ch'

/*/{Protheus.doc} TCRME030
Validar o agrupador de vendas se tem acesso a rotina.
<AUTHOR> | <PERSON>
@version   1.xx
@since     07/02/2022
/*/
User Function TCRME030()
	Local clQuery	:= "" 
	Local clAlias	:= GetNextAlias()
	Local clRet		:= ""
    Local lRet		:= .F.
    Local oModel   	:= FWModelActive()
    Local oModelADY	:= oModel:GetModel("ADYMASTER")
    Local cXModal   := Alltrim(oModelADY:GetValue("ADY_XMODAL"))
	Local cCodNiv   := Alltrim(oModelADY:GetValue("ADY_XCODNV"))
	
    
	clQuery := " SELECT ZX5_CHAVE "
	clQuery += " FROM " + RetSqlName("ZX5") + " ZX5 "
	clQuery += " WHERE ZX5.ZX5_FILIAL = '" + xFilial("ZX5") + "' "
	clQuery += " AND ZX5.ZX5_TABELA = 'XUNCOR' "
	clQuery += " AND (ZX5.ZX5_CHAVE = '" + cXModal + "' OR ZX5.ZX5_CHAVE = '" + cCodNiv + "') "
	clQuery += " AND ZX5.ZX5_CHAVE <> ' ' "
	clQuery += " AND ZX5.D_E_L_E_T_ = ' ' "

	dbUseArea( .T., "TOPCONN", TCGENQRY(,, clQuery), clAlias, .F., .T.) 

	While (clAlias)->(!EOF())

		If !Empty(clRet)
			clRet += "/"+alltrim((clAlias)->ZX5_CHAVE)
		Else
			clRet += alltrim((clAlias)->ZX5_CHAVE)
		EndIF

		(clAlias)->(dbSkip())
	EndDO
    
    lRet := !Empty(clRet)

	(clAlias)->(dbCloseArea())

Return(lRet)
