Import-Module -name GHCWebhook
$uri = "https://chat.googleapis.com/v1/spaces/AAAAXD6T1Bo/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=I4MHd57Sqm02BIemDNi0MFkqIBpzJlUpPJ9MpTxhjEU%3D"
$end_status = $args[0]

if ("$end_status" -eq "success" ) {
    $end_message = "Finalizado com Sucesso"
    $url_icon = "https://img.icons8.com/color/48/000000/ok--v1.png" 
} else {
    $end_message = "Finalizado com falha"
    $url_icon = "https://img.icons8.com/color/48/000000/cancel--v1.png"
}

function notify_end {

$fun_endstatus=$args[0]
$fun_urlicon=$args[1]

$message = New-GHCCardMessage -Content {
    New-GHCCardHeader -Title "Protheus v25" -Subtitle "$fun_endstatus" -ImageURL "$fun_urlicon" -ImageStyle avatar
    New-GHCCardSection -Content {
        New-GHCCardWidget -Content {
            New-GHCWidgetKeyValue -TopLabel "Ambiente" -Content "$Env:BUILD_SOURCEBRANCHNAME"   
            New-GHCWidgetKeyValue -TopLabel "Build" -Content "$Env:BUILD_BUILDNUMBER"
        }
        New-GHCCardWidget -Content {
            New-GHCWidgetTextButton -text "Veja o log no TFS" -onclickurl "http://tfs2015.totvs.com.br:8080/tfs/TDITOTVS12/Protheus/_build?_a=summary&buildId=$Env:BUILD_BUILDNUMBER"
        }
    }
}

Write-host "Finalizando: $fun_endstatus"

Send-GHCWebhookMessage -URI $uri -Message $message  >$null 2>&1
}

notify_end $end_message $url_icon



