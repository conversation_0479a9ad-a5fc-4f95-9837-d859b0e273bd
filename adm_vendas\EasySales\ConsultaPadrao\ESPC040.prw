#INCLUDE 'TOTVS.CH'
#INCLUDE 'ESPC040.CH'

//--------------------------------------------------------------------
/*/{Protheus.doc} ESPC040
Consulta Especifica customizada aceleradores de campos, EasySales.
/*/
//--------------------------------------------------------------------
User Function ESPC040()
	
	Local cDir     := GetMV("FS_UPDSYS")
	
	Local lRet     := .F.
	
	Local oDlg 	   := Nil
	Local oBrw     := Nil
	Local oCol	   := Nil	
	
	Local bOk      := {|| lRet := .T., oDlg:End()}
	Local bCancel  := {|| oDlg:End()}
	
	Local aColunas := {}
		
	If Select("SX3AC") == 0
		//DbUseArea(.T., "CTREECDX", cDir + "sx3.dtc", "SX3AC", .T., .F.)
		USE (cDir + "sx3.dtc") ALIAS ("SX3AC") SHARED NEW VIA ("CTREECDX")
		DbSetIndex(cDir + "sx3.cdx")
		DbSetOrder(2)
	EndIf
	
	DEFINE DIALOG oDlg TITLE DecodeUTF8(STR0001) FROM 0, 0 TO 320, 600 PIXEL STYLE DS_MODALFRAME
	
	oBrw := FWFormBrowse():New(oDlg)
	oBrw:DisableConfig()
	oBrw:DisableDetails()
	oBrw:DisableReport()
	oBrw:SetDataTable(.T.)
	oBrw:SetAlias("SX3AC")
	oBrw:SetDescription(STR0002)
	oBrw:Setdoubleclick(bOk)
	oBrw:AddButton("Cancel", bCancel)
	oBrw:AddButton("OK", bOk)	
	oBrw:SetDBFFilter()
	oBrw:SetUseFilter() 
	oBrw:SetSeek()
	
	oCol := FWBrwColumn():New()
	oCol:SetTitle(STR0003)
	oCol:SetData({||SX3AC->&("X3_CAMPO")})
	oCol:SetSize(Len(SX3AC->&("X3_CAMPO")))
	aAdd(aColunas, oCol)
	
	oCol := FWBrwColumn():New()
	oCol:SetTitle(STR0004)
	oCol:SetData({||SX3AC->&("X3_DESCRIC")})
	aAdd(aColunas, oCol)
	
	oBrw:SetColumns(aColunas)
	oBrw:Activate(oDlg)
	
	ACTIVATE MSDIALOG oDlg CENTERED
			
Return lRet
