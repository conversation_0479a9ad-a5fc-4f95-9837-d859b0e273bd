#include 'protheus.ch'
#include 'parmtype.ch'

User Function TRCPARTIC()

Local cInitp 	:= space(06)

Local nX 		:= 0

Local aTabs 	:= {"FLC","FLD","FLE","FLF","FLL","FLM","FLN","FLR","FLU"}
Local aPergs	:= {}
Local aRet 		:= {}


aAdd( aPergs,{1,"Participante De   : ",cInitp,"@!",'.T.','RD0','.T.',06,.F.}) 
aAdd( aPergs,{1,"Participante Para : ",cInitp,"@!",'.T.','RD0','.T.',06,.F.}) 

If ParamBox(aPergs ,"Transf. Viajante",aRet)

	For nx := 1 To Len(aTabs)
		
		MsgRun("Aguarde...Verificando Tabela " +aTabs[nX]+" !",,{||GRAVATBS(aTabs[nX],aRet)})	 
	
	Next nX

Else
	Alert("Processo Cancelado!")
	Return()
EndIf

Static Function GRAVATBS(cTabela,aRet)

Local cWkArea	:= GetNextAlias()
Local cChave	:= ""
Local cINDC		:= ""
Local cTBL		:= cTabela
Local cQry		:= ""

If Select(cWkArea) > 0
	(cWkArea)->(DbCloseArea())
EndIf

Do Case
	Case cTBL = 'FLC'
		BeginSQL Alias cWkArea
			SELECT FLC_FILIAL, FLC_VIAGEM, FLC_PARTIC 
			  FROM %Table:FLC% FLC 
			 WHERE FLC_FILIAL <> ' ' 
			   AND FLC_PARTIC = %EXP:aRet[1]%
			   AND FLC.%NotDel%
					   
		EndSQL
		
		DbSelectArea("FLC")
		FLC->(DbSetOrder(1))
				
		While (cWkArea)->(!EOF())
			cChave := (cWkArea)->(FLC_FILIAL+FLC_VIAGEM+FLC_PARTIC)
					
			If (cTBL)->(DbSeek(cChave))
					
				Reclock("FLC",.F.)
					FLC->FLC_PARTIC := aRet[2]
				MsUnlock()
						
			EndIf
			
			(cWkArea)->(DbSkip())
		EndDo
				
	Case cTBL = 'FLD'
		BeginSQL Alias cWkArea
			SELECT FLD_FILIAL, FLD_VIAGEM, FLD_PARTIC, FLD_ADIANT 
			  FROM %Table:FLD% FLD 
			 WHERE FLD_FILIAL <> ' ' 
			   AND FLD_PARTIC = %EXP:aRet[1]%
			   AND FLD.%NotDel% 
					   
		EndSQL
		
		DbSelectArea("FLD")
		FLD->(DbSetOrder(1))
				
		While (cWkArea)->(!EOF())
			cChave := (cWkArea)->(FLD_FILIAL+FLD_VIAGEM+FLD_PARTIC+FLD_ADIANT)
					
			If (cTBL)->(DbSeek(cChave))
					
				Reclock("FLD",.F.)
					FLD->FLD_PARTIC := aRet[2]
				MsUnlock()
						
			EndIf
			
			(cWkArea)->(DbSkip())
		EndDo
		
	Case cTBL = 'FLE'
		BeginSQL Alias cWkArea
			SELECT FLE_FILIAL,FLE_TIPO,FLE_PRESTA,FLE_PARTIC,FLE_ITEM 
			  FROM %Table:FLE% FLE 
			 WHERE FLE_FILIAL <> ' ' 
			   AND FLE_PARTIC = %EXP:aRet[1]%
			   AND FLE.%NotDel%  
					   
		EndSQL
		
		DbSelectArea("FLE")
		FLE->(DbSetOrder(1))
				
		While (cWkArea)->(!EOF())
			cChave := (cWkArea)->(FLE_FILIAL+FLE_TIPO+FLE_PRESTA+FLE_PARTIC+FLE_ITEM)
					
			If (cTBL)->(DbSeek(cChave))
					
				Reclock("FLE",.F.)
					FLE->FLE_PARTIC := aRet[2]
				MsUnlock()
						
			EndIf
			
			(cWkArea)->(DbSkip())
		EndDo
		
	Case cTBL = 'FLF'
		BeginSQL Alias cWkArea
			SELECT FLF_FILIAL,FLF_TIPO,FLF_PRESTA,FLF_PARTIC 
			  FROM %Table:FLF% FLF 
			 WHERE FLF_FILIAL <> ' ' 
			   AND FLF_PARTIC = %EXP:aRet[1]%
			   AND FLF.%NotDel%
					   
		EndSQL
		
		DbSelectArea("FLF")
		FLF->(DbSetOrder(1))
				
		While (cWkArea)->(!EOF())
			cChave := (cWkArea)->(FLF_FILIAL+FLF_TIPO+FLF_PRESTA+FLF_PARTIC)
					
			If (cTBL)->(DbSeek(cChave))
					
				Reclock("FLF",.F.)
					FLF->FLF_PARTIC := aRet[2]
				MsUnlock()
						
			EndIf
			
			(cWkArea)->(DbSkip())
		EndDo
		
	Case cTBL = 'FLL'
		BeginSQL Alias cWkArea
			SELECT FLL_FILIAL,FLL_GRUPO,FLL_PARTIC 
			  FROM %Table:FLL% FLL 
			 WHERE FLL_FILIAL <> ' ' 
			   AND FLL_PARTIC = %EXP:aRet[1]%
			   AND FLL.%NotDel% 
					   
		EndSQL
		
		DbSelectArea("FLL")
		FLL->(DbSetOrder(1))
				
		While (cWkArea)->(!EOF())
			cChave := (cWkArea)->(CFLL_FILIAL+FLL_GRUPO+FLL_PARTIC)
					
			If (cTBL)->(DbSeek(cChave))
					
				Reclock("FLL",.F.)
					FLL->FLL_PARTIC := aRet[2]
				MsUnlock()
						
			EndIf
			
			(cWkArea)->(DbSkip())
		EndDo
				
	Case cTBL = 'FLM'
		BeginSQL Alias cWkArea
			SELECT FLM_FILIAL,FLM_VIAGEM,FLM_PARTIC,FLM_ADIANT,FLM_SEQ,FLM_TIPO
			  FROM %Table:FLM% FLM 
			 WHERE FLM_FILIAL <> ' ' 
			   AND FLM_PARTIC = %EXP:aRet[1]%
			   AND FLM.%NotDel% 
					   
		EndSQL
		
		DbSelectArea("FLM")
		FLM->(DbSetOrder(1))
				
		While (cWkArea)->(!EOF())
			cChave := (cWkArea)->(FLM_FILIAL+FLM_VIAGEM+FLM_PARTIC+FLM_ADIANT+FLM_SEQ+FLM_TIPO)
					
			If (cTBL)->(DbSeek(cChave))
					
				Reclock("FLM",.F.)
					FLM->FLM_PARTIC := aRet[2]
				MsUnlock()
						
			EndIf
			
			(cWkArea)->(DbSkip())
		EndDo
		
	Case cTBL = 'FLN'
		BeginSQL Alias cWkArea
			SELECT FLN_FILIAL,FLN_TIPO,FLN_PRESTA,FLN_PARTIC,FLN_SEQ,FLN_TPAPR
			  FROM %Table:FLN% FLN 
			 WHERE FLN_FILIAL <> ' ' 
			   AND FLN_PARTIC = %EXP:aRet[1]%
			   AND FLN.%NotDel% 
					   
		EndSQL
		
		DbSelectArea("FLN")
		FLN->(DbSetOrder(1))
				
		While (cWkArea)->(!EOF())
			cChave := (cWkArea)->(FLN_FILIAL+FLN_TIPO+FLN_PRESTA+FLN_PARTIC+FLN_SEQ+FLN_TPAPR)
					
			If (cTBL)->(DbSeek(cChave))
					
				Reclock("FLN",.F.)
					FLN->FLN_PARTIC := aRet[2]
				MsUnlock()
						
			EndIf
			
			(cWkArea)->(DbSkip())
		EndDo
							
	Case cTBL = 'FLR'
		BeginSQL Alias cWkArea
			SELECT FLR_FILIAL,FLR_CONFER,FLR_VIAGEM,FLR_ITEMVI,FLR_PARTIC
			  FROM %Table:FLR% FLR 
			 WHERE FLR_FILIAL <> ' ' 
			   AND FLR_PARTIC = %EXP:aRet[1]%
			   AND FLR.%NotDel% 
					   
		EndSQL
		
		DbSelectArea("FLR")
		FLR->(DbSetOrder(1))
				
		While (cWkArea)->(!EOF())
			cChave := (cWkArea)->(FLR_FILIAL+FLR_CONFER+FLR_VIAGEM+FLR_ITEMVI+FLR_PARTIC)
					
			If (cTBL)->(DbSeek(cChave))
					
				Reclock("FLR",.F.)
					FLR->FLR_PARTIC := aRet[2]
				MsUnlock()
						
			EndIf
			
			(cWkArea)->(DbSkip())
		EndDo
										
	Case cTBL = 'FLU'
		BeginSQL Alias cWkArea
			SELECT FLU_FILIAL,FLU_VIAGEM,FLU_ITEM,FLU_PARTIC 
			  FROM %Table:FLU% FLU 
			 WHERE FLU_FILIAL <> ' ' 
			   AND FLU_PARTIC = %EXP:aRet[1]%
			   AND FLU.%NotDel% 
					   
		EndSQL
		
		DbSelectArea("FLU")
		FLU->(DbSetOrder(1))
				
		While (cWkArea)->(!EOF())
			cChave := (cWkArea)->(FLU_FILIAL+FLU_VIAGEM+FLU_ITEM+FLU_PARTIC)
					
			If (cTBL)->(DbSeek(cChave))
					
				Reclock("FLU",.F.)
					FLU->FLU_PARTIC := aRet[2]
				MsUnlock()
						
			EndIf
			
			(cWkArea)->(DbSkip())
		EndDo
						
EndCase
	

(cWkArea)->(DbCloseArea())
cQry := ""

Return