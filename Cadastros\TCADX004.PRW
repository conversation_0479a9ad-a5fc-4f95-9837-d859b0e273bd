#Include "TOTVS.CH"

/*/{Protheus.doc} TCADX004
Envio de dados do produto para outras empresas

<AUTHOR> Carneiro
@since 09/02/2021
@return nil
/*/

User Function TCADX004(cTipo, lPox)
Local aArea     := GetArea()
Local lWait     := .F.
Local lEmpRep 	:= SuperGetMv("TI_REPSB1",,.T.) //liga/desliga a Replica Produto SB1/SB5/SBM
Local cEmpRep 	:= SuperGetMv("TI_REPPEMP",,"01/02/04") //Empresas do grupo Replica Produto
Local aEmpRep   := Strtokarr2( cEmpRep,"/")
Local oJson     := nil
Local cJson     := ""
Local oAuxJson  := nil
Local oAuxJson2 := nil
Local cAlias    := ""
Local aAlias    := Nil //alias a serem replicadas
Local cCampo    := ""
Local aRegAux   := {}
Local nI        := 1
Local nX        := 1
Local nZ        := 1
Local xVal      := nil
Local nRecnoSM0 := 0
Local aSX3      := {}
Local aTamSx3   := 0
Local cMailPara := GetMv("TI_TIDMAIL",,"<EMAIL>")
Local cChave    := ""
Local nOrder    := 1
Local aEmpCpd   := {}
Local cEmpCpd   := ""
Local cVDefault := ""
Local nPos      := 0

Default lPox := .F.

aAlias := Iif( lPox, {"SB1","SB5"}, {"SB1","SB5","SBM"} )

If !lEmpRep //liga/desliga a Replica Produto SB1/SB5/SBM
    RestArea(aArea)
    Return
EndIf

oJson := JsonObject():new()
aRegAux := {}
GetZX5(@aEmpCpd) //Verifica ha empresa liberadas para os campos

For nI:=1 to Len(aAlias)
    cAlias := aAlias[nI]
    DbSelectArea(cAlias)
    aSx3 := FWSX3Util():GetListFieldsStruct(cAlias,.F. )
    aTamSx3 := Len(aSx3)
    cChave := ""
    If cAlias == "SB1" 
        nOrder := 1
        cChave := SB1->B1_FILIAL+SB1->B1_COD
    ElseIf cAlias == "SB5" 
        If SB1->B1_COD<>SB5->B5_COD //Garante o posiconamento da tabela
            SB5->(DbSetOrder(1))
            If !SB5->(DbSeek(SB1->B1_FILIAL+SB1->B1_COD))
                Loop
            EndIf
        EndIf
        nOrder := 1
        cChave := SB5->B5_FILIAL+SB5->B5_COD
    ElseIf cAlias == "SBM" 
        If SB1->B1_GRUPO<>SBM->BM_GRUPO //Garante o posiconamento da tabela
            SBM->(DbSetOrder(1))
            IF !SBM->(DbSeek(SB1->B1_FILIAL+SB1->B1_GRUPO))
                Loop
            EndIf
        EndIf
        nOrder := 1
        cChave := SBM->BM_FILIAL+SBM->BM_GRUPO
    EndIf
    oAuxJson2 := JsonObject():new()   
    For nZ:=1 To aTamSx3
        cEmpCpd := ""
        cVlpadrao := ""
        If (nPos := Ascan(aEmpCpd,{|x| Alltrim(x[1])==Alltrim(aSx3[nZ,1]) })) > 0 //Verifica ha empresa liberadas para este campo
            cEmpCpd := aEmpCpd[nPos,2]
            cVDefault := aEmpCpd[nPos,3]
        Else 
            Loop
        EndIf
        cCampo := cAlias+"->"+Alltrim(aSx3[nZ,1])
        If aSx3[nZ,2] == "D"
            xVal := Alltrim(DTOS(&cCampo.))
        ElseIf aSx3[nZ,2]  $ "C.M"
            xVal := Alltrim(&cCampo.)
        Else
            xVal := &cCampo.   
        EndIf 
        If Empty(cVDefault)   
            oAuxJson2[Alltrim(aSx3[nZ,1])]:= {xVal,cEmpCpd}
        Else
            oAuxJson2[Alltrim(aSx3[nZ,1])]:= {xVal,cEmpCpd,cVDefault}
        EndIf
    Next
    oAuxJson := JsonObject():new()
    oAuxJson["tabela"]:= cAlias
    oAuxJson["campos"]:= oAuxJson2
    oAuxJson["norder"]:= nOrder
    oAuxJson["chave"]:= cChave
    AADD(aRegAux,oAuxJson)
Next

oJson["produto"]:= SB1->B1_COD //Codigo do produto para email de erro.
oJson["email"] := cMailPara
oJson["atabelas"] := aRegAux

cJson := FwJsonSerialize(oJson,.F.,.T.)

If !lPox

    nRecnoSM0 := SM0->(Recno())
    For nX := 1 To len(aEmpRep)

        If (aEmpRep[nX] <> cEmpAnt)
            SM0->( DbSeek(aEmpRep[nX]) )            
            StartJob("U_TCADX04R",GetEnvServer(),lWait, SM0->M0_CODIGO,Alltrim(SM0->M0_CODFIL), cJson, cTipo)            
        EndIf

    Next 
    SM0->(DbGoTo(nRecnoSM0))

EndIf

// inicia processo de limpeza da memória
aSize(aEmpRep,0)
aEmpRep = NIL
aSize(aEmpCpd,0)
aEmpCpd = NIL
aSize(aRegAux,0)
aRegAux = NIL
aSize(aSx3,0)
aSx3 = NIL
FreeObj(oJson)
oJson := Nil
FreeObj(oAuxJson2)
oAuxJson2 := Nil
FreeObj(oAuxJson)
oAuxJson := Nil

RestArea(aArea)

Return cJson

/*/{Protheus.doc} GetZX5
Tabelas Genenicas

<AUTHOR> Carneiro
@since 09/02/2021
@return nil
/*/
Static Function GetZX5(aEmpCpd)
Local cAliasZX5 := GetNextAlias()

BeginSql Alias cAliasZX5
    SELECT ZX5.ZX5_CHAVE2,ZX5.ZX5_COMPL,UTL_RAW.CAST_TO_VARCHAR2(DBMS_LOB.SUBSTR(ZX5_MEMO,2047,1)) AS ZX5_MEMO
        FROM %table:ZX5% ZX5 
        WHERE ZX5.ZX5_FILIAL = %xFilial:ZX5%
            AND ZX5.ZX5_TABELA = 'REPLB1'
            AND ZX5.ZX5_CHAVE2 <> ''
            AND ZX5.%notdel%
        ORDER BY %Order:ZX5,2%
EndSql
While (cAliasZX5)->(!Eof())
    aAdd(aEmpCpd, {Alltrim((cAliasZX5)->ZX5_CHAVE2),Alltrim((cAliasZX5)->ZX5_COMPL),Alltrim((cAliasZX5)->ZX5_MEMO)} )
    (cAliasZX5)->(DbSkip())
EndDo
(cAliasZX5)->(DbCloseArea())

Return NIL

/*/{Protheus.doc} TCADX04R
Grava dados da Tabelas para outras empresas

<AUTHOR> Carneiro
@since 09/02/2021
@return nil
/*/
User Function TCADX04R(cEmp, cFil, cJson, cTipo)
Local oJson     := nil
Local nA        := 1
Local nB        := 1
Local nC        := 1
Local nD        := 1
Local nI        := 1
Local nX        := 1
Local nZ        := 1
Local aRet      := {}
Local aTamRet   := 0
Local aRecl     := {}
Local aTamRec   := 0
Local lRet      := .T.
Local aAux      := {}
Local lInc      := (cTipo == 'I')
Local cCampo    := ""
Local xVal      := 0
Local cAlias    := ""
Local cChave    := ""
Local nOrder    := ""
Local cMailPara := ""
Local cAssunto  := ""
Local cMensagem := ""
Local cCodProd  := ""
Local cMsg      := ""
Local lJob      := IsBlind()
Local aSX3      := {}
Local cEmpZX5   := ""
Local lRecLock  := .T.
Local lNovoInc  := .F.
Local xVlCpo    := nil
Local aVDefault := {}

Default cEmp    := ""
Default cFil    := ""

If !Empty(cEmp) .Or. !Empty(cFil)
    RpcSetType(3)
    lRet := RpcSetEnv(cEmp, cFil)
Else
    cEmp := cEmpAnt
    cFil := cFilAnt
EndIf

If lRet 
    If FWJsonDeserialize(cJson, @oJson)
        cMailPara := oJson:email 
        cCodProd := oJson:produto
        If Len(oJson:atabelas) > 0
            For nA := 1 To Len(oJson:atabelas) 
                aAux := {}
                aAdd(aAux, oJson:atabelas[nA]:tabela)
                aAdd(aAux, ClassDataArr(oJson:atabelas[nA]:campos))
                aAdd(aAux, oJson:atabelas[nA]:norder)
                aAdd(aAux, oJson:atabelas[nA]:chave)
                aAdd(aRet, aAux)
            Next           
            For nI := 1 To Len(aRet) 
                cAlias := aRet[nI,1]
                nOrder := aRet[nI,3]
                cChave := aRet[nI,4]
                aSx3 := FWSX3Util():GetListFieldsStruct(cAlias,.F. )
                aadd(aRecl,{cAlias,{},nOrder,cChave})
                aTamRet := Len(aRet[nI,2])
                For nX := 1 To aTamRet
                    cCampo := cAlias+"->"+aRet[nI,2,nX,1]
                    xVlCpo  := aRet[nI,2,nX,2,1] //Valor do campo
                    cEmpZX5 := aRet[nI,2,nX,2,2] //Empresa 
                    If ((nZ := Ascan(aSx3,{|x| Alltrim(x[1])==Alltrim(aRet[nI,2,nX,1])})) > 0) .AND. (cEmp$cEmpZX5)                      
                        If len(aRet[nI,2,nX,2])>=3 //Recupera o valor padrao ZX5 no memo no formato emp:valor
                            aVDefault := Strtokarr2( aRet[nI,2,nX,2,3],"/")
                            for nD := 1 to len(aVDefault)
                                If Subs(aVDefault[nD],1,2)==cEmp
                                    xVlCpo := Subs(aVDefault[nD],4)
                                EndIf
                            next 
                        EndIf
                        If aSx3[nZ,2] == "D"
                            xVal :=  STOD(xVlCpo)
                        ElseIf aSx3[nZ,2] $ "C.M"
                            xVal := Alltrim(xVlCpo)
                            If aSx3[nZ,2] == "C" .AND. Empty(xVal)
                                xVal := Space(aSx3[nZ,3])
                            EndIf
                        ElseIf aSx3[nZ,2]== "L"
                            xVal := xVlCpo
                        Else
                            xVal := xVlCpo  
                        EndIf
                        aadd(aRecl[Len(aRecl),2],{cCampo, xVal})
                    EndIf
                Next
            Next
        Else
            cMensagem := "Erro no Json, Tabelas de replicação não existem."+Chr(13)+Chr(10) 
            lRet := .F.
        EndIf
    Else
        cMensagem := "Erro no Json, FWJsonDeserialize falhou."+Chr(13)+Chr(10)
        lRet := .F.
    EndIf

    If lRet
         For nB := 1 To Len(aRecl)  
            cAlias := aRecl[nB,1]
            nOrder := aRecl[nB,3]
            cChave := aRecl[nB,4]
            lNovoInc := .F. 
            If lInc  //Inclusao
                (cAlias)->(DbSetOrder(nOrder))
                If (cAlias)->(DbSeek(xFilial(cAlias)+ALLTRIM(cChave)))//tibackop-1705
                    If !(cAlias == "SBM")
                        cMensagem += "Chave de pesquisa "+cChave+" da Inclusao da tabela "+cAlias+" falhou "+Chr(13)+Chr(10)
                        lRet := .F.
                    EndIf
                    Loop
                EndIf
            Else  //Alteracao
                (cAlias)->(DbSetOrder(nOrder))
                If !(cAlias)->(DbSeek(xFilial(cAlias)+ALLTRIM(cChave)))//tibackop-1705
                    lNovoInc := .T. //Alteracao vira inclusao pois nao existe no cadastro.
                EndIf
            EndIf
            If lRet
                aTamRec := Len(aRecl[nB,2])
                lRecLock := lInc
                If !lInc .AND. lNovoInc
                    lRecLock := .T.
                EndIf
                RecLock(cAlias,lRecLock)
                For nC := 1 To  aTamReC
                    cCampo := aRecl[nB,2,nC,1]
                    &cCampo. := aRecl[nB,2,nC,2]
                Next
                MsUnLock()
            EndIf
        Next
    EndIf
    If !lRet
        If Empty(cMailPara)
            cMailPara := GetMv("TI_TIDMAIL",,"<EMAIL>")
        EndIf
        cAssunto  := "Cadastro - Erro na Replica de Produto"
        cMsg := "Erro na Replica de Produto para Empresa: "+cEmp+"   Cod.Produto: "+cCodProd+Chr(13)+Chr(10)
        cMsg += cMensagem

        U_xSendMail(cMailPara,cAssunto,cMsg,,lJob) 
    EndIf

EndIf
// inicia processo de limpeza da memória
aSize(aAux,0)
aAux = NIL
aSize(aRet,0)
aRet = NIL
aSize(aRecl,0)
aRecl = NIL
aSize(aSx3,0)
aSx3 = NIL
FreeObj(oJson)
oJson := Nil
cJson := Nil

Return TCadSuces()

/*/{Protheus.doc} TCadSuces
    Retorno do processo POX
<AUTHOR>
@since 26/09/2022
@return nil
/*/
Static Function TCadSuces()
Local aRet  := {}

// Grava no campo Memo resultado das alterações
AAdd(aRet, .T.)
AAdd(aRet, 'Produtos replicados com sucesso.')
AAdd(aRet, .T.)
AAdd(aRet, '')

Return aRet

/*/{Protheus.doc} CDX04POX
    Grava POX para processo criação de produtos SB1 nas empresas do MI
<AUTHOR> Osti
@since 26/09/2022
@return nil
/*/
User Function CDX04POX(cEmpPox, cFilPox, cCodCons, aJsonProds)
Local _lEnv := .F.
Local nFor  := 1

Default aJsonProds := {}

RpcClearEnv()
RpcSetType(3)
_lEnv := RpcSetEnv(cEmpPox,cFilPox)

If _lEnv

    For nFor := 1 to Len(aJsonProds) 
        U_TCRMX155(cCodCons, aJsonProds[nFor][1] ,,,,, aJsonProds[nFor][2])
    Next nFor
    
EndIf

Return

/*/{Protheus.doc} CDX4Proc
Replica dos produtos para as empresas MI processamento dos registros POX
<AUTHOR>
@since 26/09/2022
@return nil
/*/
User Function CDX4PROC()
Local cJson := Alltrim(ParamIxb[4])
Local aRet  := {.F.,'',.F.,''}

Begin Transaction
    aRet := U_TCADX04R(,, cJson, 'A')
End Transaction

Return aRet
