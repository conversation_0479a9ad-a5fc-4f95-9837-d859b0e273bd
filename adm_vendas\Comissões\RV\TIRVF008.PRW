#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVF008    
Callback de VIEW PAPEIS PROJETOS  PSA ACTIO
@type  Function
<AUTHOR> Menabue Lima
@since 27/10/2023
@version 1.0
	/*/
User Function TIRVF008(cCodReq,cResponse,cAsync,cStatReq)

	Local oReceptor	:=TIINTERCEPTOR():New()
	Local oJson 	:= JsonObject():new()
	Local oActio	:= TIRVACTIO():New()
	Local nCount	:=0
	Local nPage		:=0
	Local nItens	:=0
	Local nMaxPg := GetMv("TI_RVJ06PG" , .F., 50)
	If (cStatReq == "2")//Processado a REquisicao
		oJson:fromJson(cResponse)
		Reclock("P37",.F.)

		P37->P37_STATUS :="2"
		P37->(MsUnlock())

		if oJson:hasProperty('value')
			aItens:=oJson['value']
			nItens:= Len(aItens)
			oJson:fromJson(P37->P37_BODY)
			//Verifica ser tem dados na proxima pagina
			IF(oJson:hasProperty('nCount'))
				nCount:=oJson['nCount']
				if (oJson:hasProperty('nPage'))
					nPage:=oJson['nPage']
					if ( nItens>=nCount .And. nPage <= nMaxPg )
						U_TIRVJ008(nCount,nPage+1,IIF(Empty(Alltrim(P37->P37_CODREQ)),P37->P37_COD,P37->P37_CODREQ),oJson['inicio'],oJson['fim'])
					EndIF
				EndIF
			EndIF
			oActio:setRVPsaData(aItens,"PAPEIS")
			//Le o Body da requisicao pois esta informado o count e a Pagina atual

			if !oActio:isError()

			else
				Reclock("P37",.F.)
				P37->P37_STATUS :="3"
				P37->P37_BODYRP  := '{"message":"'+oActio:GetError()+'"}'
				P37->(MsUnlock())
			EndIF
		else
			Reclock("P37",.F.)
			P37->P37_STATUS :="3"
			P37->P37_BODYRP  := '{"message":"Tag Values nao encontrada"}'
			P37->(MsUnlock())
		EndIF
	EndIF
	FreeObj(oActio)
	FreeObj(oReceptor)
Return
