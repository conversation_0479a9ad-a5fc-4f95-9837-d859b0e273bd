#INCLUDE 'TOTVS.CH'
#INCLUDE "FWBROWSE.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "TOPCONN.CH"

#DEFINE CRLF		Chr(13)+Chr(10)

#DEFINE THREAD_IDLE		'TIINTEXEC - ##IDLE'
#DEFINE POS_USER 		01
#DEFINE POS_PC	 		02
#DEFINE POS_IDTHREAD	03
#DEFINE POS_INSTRUC		09
#DEFINE POS_OBS 		11

/*/{Protheus.doc} TIITC001
    Funcoes Responsavel por retornar as APIS cadastradas na P36 
    @type  Function
    <AUTHOR> Menabue Lima
    @since 22/11/2022
    @version 1.0
    /*/
User Function TIITC001()
	Local aRequest  := PARAMIXB //Parametros recebidos do Interceptor  [1]= Codigo da Requisicao P37_COD [2]= Body Recebido da requisicao  P37_BODY
	Local cAlsPro := GetNextAlias()
	Local oJson     := JsonObject():new()
	Local oBody     := oJson:FromJson( aRequest[2] )
	Local cEpico	:= ""
	Local cQry		:= ""
	If ValType( oBody ) == "U"
		cEpico:=oJson:GetJsonObject( "epico_code" )
		If cEpico != Nil
			cQry := " SELECT DISTINCT  P36_FILA P36_EPIC,P36_FILA P36_EPICO,P36_CODIGO,'-' P36_TIMEMO,P36_DESCRI,'po-icon po-icon-hdd' P36_POICON,' ' P36_CONFIG,R_E_C_N_O_ RNOP36 "
			cQry += " FROM "+ RetSqlName("P36") +" P36 WHERE D_E_L_E_T_ = ' ' AND P36_STATUS = 'A' "
			if(cEpico=="GENERICA")
				cEpico:=" "
			EndIF
			cQry += " AND  P36_FILA ='"+cEpico+"'"
			cQry += " ORDER BY P36_CODIGO "
		else
			cQry := " SELECT DISTINCT P36_FILA P36_EPIC,P36_FILA P36_EPICO,'po-icon po-icon-hdd ' P36_POICON  "
			cQry += " FROM "+ RetSqlName("P36") +" P36 WHERE D_E_L_E_T_ = ' '   AND P36_STATUS = 'A' "
			cQry += " ORDER BY P36_FILA "

		Endif

	EndIF
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAlsPro,.T.,.T.)


	nLinha := 0
	aJson:={}
	cJson:= '{ "epicos": [ '
	While (cAlsPro)->(!Eof())
		DbSelectArea("P36")
		DbSetOrder(1)

		If cEpico != Nil
			P36->(DbGoTo( (cAlsPro)->RNOP36 ))
			cJson +=  '{ "epico_code": "'+Alltrim((cAlsPro)->P36_EPIC)+'","epico": "'+Alltrim((cAlsPro)->P36_EPICO)+'", "descricao": "'+Alltrim((cAlsPro)->P36_DESCRI)+'","api": "'+Alltrim((cAlsPro)->P36_CODIGO)+'","timestatus":"'+Alltrim(cValToChar((cAlsPro)->P36_TIMEMO))+'"'
			cJson +='},'

		else
			IF(Empty(Alltrim((cAlsPro)->P36_EPIC))			)
				cJson +=  '{ "epico_code": "GENERICA","epico": "GENERICA"'
				cJson +=',"icon": "po-icon po-icon-world"'
			else
				cJson +=  '{ "epico_code": "'+Alltrim((cAlsPro)->P36_EPIC)+'","epico": "'+Alltrim((cAlsPro)->P36_EPICO)+'"'
				cJson +=',"icon": "'+Alltrim((cAlsPro)->P36_POICON)+'"'
			EndIF
			cJson +=  '},'
		EndIF
		(cAlsPro)->(DbSkip())
	EndDO

	cJson:= SubStr(cJson,1,len(cJson)-1)+'] }'

	(cAlsPro)->(DbCloseArea())
	RecLock("P37", .F.)
	P37->P37_BODYRP:=cJson
	P37->(MsUnlock())

Return

