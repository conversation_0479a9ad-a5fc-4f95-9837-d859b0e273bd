#INCLUDE 'TOTVS.CH'
#INCLUDE 'FWMVCDEF.CH'
#INCLUDE 'FINA008.CH'

//-------------------------------------------------------------------
//{Protheus.doc} FINA008
//Cadastros dos aprovadores x pap�is

//<AUTHOR>
//@since 14/05/2015
//@version 12.1.6
//
//-------------------------------------------------------------------

USER Function TFINA008()
    Local oBrowse

    oBrowse := FWmBrowse():New()
    oBrowse:SetAlias( 'FRW' )
    oBrowse:SetDescription( STR0001 ) //"Aprovadores x Pap�is"
    oBrowse:Activate()

Return NIL

//-------------------------------------------------------------------
//{Protheus.doc} MenuDef
//Defini��o de Menu

//<AUTHOR> Flor�ncio Domingos Filho
//@since 14/05/2015
//@version 12.1.6
////-------------------------------------------------------------------
Static Function MenuDef()
    Local aRotina := {}

    ADD OPTION aRotina Title STR0002  Action 'VIEWDEF.TFINA008' OPERATION MODEL_OPERATION_VIEW		ACCESS 0 //"Visualizar"
    ADD OPTION aRotina Title STR0003  Action 'VIEWDEF.TFINA008' OPERATION MODEL_OPERATION_INSERT	ACCESS 0 //"Incluir"
    ADD OPTION aRotina Title STR0004  Action 'VIEWDEF.TFINA008' OPERATION MODEL_OPERATION_UPDATE	ACCESS 0 //"Alterar"
    ADD OPTION aRotina Title STR0005  Action 'VIEWDEF.TFINA008' OPERATION MODEL_OPERATION_DELETE	ACCESS 0 //"Excluir"
    ADD OPTION aRotina Title STR0006  Action 'VIEWDEF.TFINA008' OPERATION 8 ACCESS 0 //"Imprimir"
    ADD OPTION aRotina Title STR0007  Action 'VIEWDEF.TFINA008' OPERATION 9 ACCESS 0 //"Copiar"

Return aRotina

//-------------------------------------------------------------------
//{Protheus.doc} ModelDef()
// Cria a estrutura a ser usada no Modelo de Dados

//<AUTHOR> Flor�ncio Domingos Filho
//@since 14/05/2015
//@version 12.1.6
//

//-------------------------------------------------------------------
Static Function ModelDef()

    Local oStruFRW := FWFormStruct( 1, 'FRW', /*bAvalCampo*/, /*lViewUsado*/ )
    Local oStruFRX := FWFormStruct( 1, 'FRX', /*bAvalCampo*/, /*lViewUsado*/ )
    Local oModel

    oModel := MPFormModel():New( 'FINA008M', /*bPreValidacao*/, /*bPosValidacao*/, /*bCommit*/, /*bCancel*/ )
    oModel:SetActivate ()
    oModel:AddFields( 'FRWMASTER', /*cOwner*/, oStruFRW )
    oStruFRX:SetProperty('FRX_CODGES',MODEL_FIELD_WHEN,{|| U_TF08VldS('FRX_CODGES')})
    oStruFRX:SetProperty('FRX_ATIVO',MODEL_FIELD_WHEN,{||U_TF08VldS('FRX_ATIVO')})
    oModel:AddGrid( 'FRXDETAIL', 'FRWMASTER',oStruFRX, {|oModel, nLine, cAction| F008PVLD(oModel,nLine,cAction )}, /*bPosValidacao*/ , /*bPreVal*/, /*bPosVal*/, /*BLoad*/ )
    oModel:SetRelation( 'FRXDETAIL', { { 'FRX_FILIAL', 'FWxFilial( "FRX" )' }, { 'FRX_CODIGO', 'FRW_CODIGO' } }, FRX->( IndexKey( 1 ) ) )
    oModel:GetModel( 'FRXDETAIL' ):SetUniqueLine( { 'FRX_CODGES' } )
    oModel:SetDescription(STR0001) //"Aprovadores x Pap�is"
    oModel:GetModel( 'FRWMASTER' ):SetDescription(STR0010 ) //"Cabe�alho Controle de Papeis"
    oModel:GetModel( 'FRXDETAIL' ):SetDescription(STR0011) //"Itens Controle de Pap�is"

Return oModel

//-------------------------------------------------------------------
//{Protheus.doc} ViewDef()
// Defini��o de View do Sistema

//<AUTHOR> Flor�ncio Domingos Filho
//@since 14/05/2015
//@version 12.1.6
//
//-------------------------------------------------------------------
Static Function ViewDef()

    Local oStruFRW := FWFormStruct( 2, 'FRW' )
    Local oStruFRX := FWFormStruct( 2, 'FRX', {|cCampo| !( AllTrim(cCampo) $ "FRX_CODIGO") } )
    Local oModel   := FWLoadModel( 'TFINA008' )
    Local oView

    oView := FWFormView():New()
    oView:SetModel( oModel )
    oView:AddField( 'VIEW_FRW', oStruFRW, 'FRWMASTER' )
    oView:AddGrid(  'VIEW_FRX', oStruFRX, 'FRXDETAIL' )
    oView:CreateHorizontalBox( 'SUPERIOR', 30 )
    oView:CreateHorizontalBox( 'INFERIOR', 70 )
    oView:SetOwnerView( 'VIEW_FRW', 'SUPERIOR' )
    oView:SetOwnerView( 'VIEW_FRX', 'INFERIOR' )
    oView:AddIncrementField( 'VIEW_FRX', 'FRX_ITEM' )
    oView:EnableTitleView('VIEW_FRX',STR0008) //"Gestores"

Return oView

//-------------------------------------------------------------------
//{Protheus.doc} TF08TRIG -> FA008TRIGG(cCampo1,cCampo2)
// Fun��o de Gatilhos

//<AUTHOR> Flor�ncio Domingos Filho
//@since 14/05/2015
//@version 12.1.6
//
//-------------------------------------------------------------------

USER Function TF08TRIG(cCampo1,cCampo2)

    Local cChave1 := xFilial("FRP") + PadR(cCampo1,TamSX3("FRP_COD")[1])
    Local xRetTrigger := FRP->(GetAdvFVal("FRP",cCampo2,cChave1,1,""))

Return(xRetTrigger)

//-------------------------------------------------------------------
//{Protheus.doc} F008PVLD(oModel,nLine,cAction )
// Valida se linha pode ser deletada

//<AUTHOR> Flor�ncio Domingos Filho
//@since 14/05/2015
//@version 12.1.6
//
//-------------------------------------------------------------------
Static Function F008PVLD(oModel,nLine,cAction )
    Local lRet       := .T.
    Local nOperation := oModel:GetOperation()
    Local cCodUser	 := ""


    If nOperation == MODEL_OPERATION_UPDATE .And. cAction == 'DELETE'
        cCodUser := oModel:GetValue('FRX_USER', nLine)
        FWL->(DbSetOrder(1))
        If FWL->(MsSeek(xFilial("FWL")+cCodUser))
            Help( ,, 'Help',,STR0009, 1, 0 ) //"N�o � permitido deletar em modo de altera��o"
            lRet := .F.
        Endif

    EndIf

Return lRet

//-------------------------------------------------------------------
//{Protheus.doc} F008VldSt

//Fun��o para valida��o do campo de Status do registro de aprova��o de border�

//@return lRet Retorno se � poss�vel atualizar o Status do gestor vinculado ao papel
//<AUTHOR> Ara�jo Silva
//@since 07/07/2015
//@version 12.1.6
//@param cCampo Campo que est� sendo alterado o Modo de Edi��o de acordo com o acesso ao cadastro de pap�is
//
//-------------------------------------------------------------------

USER Function TF08VldS(cCampo)
    Local lRet		:= .T.
    Local oModel		:= FWModelActive()
    Local nOperation	:= 0//oModel:GetOperation()
    Local oFRXMdl		:= 0
    Local nLinePos		:= 0

    If ( Valtype(oModel) == "O" )
        oFRXMdl		:= oModel:GetModel("FRXDETAIL")
        nOperation 	:= oModel:GetOperation()
        nLinePos	:= oFRXMdl:GetLine()

        If nOperation == MODEL_OPERATION_UPDATE

            If cCampo == "FRX_CODGES" .AND. !oFRXMdl:IsInserted(nLinePos)
                lRet := .F.
            EndIf
        EndIf

    EndIf


Return lRet