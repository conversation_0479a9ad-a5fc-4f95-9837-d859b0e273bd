
#include 'protheus.ch'

// -----------------------------------------------------------------
/*{Protheus.doc} TCRMI006
Inicializador do campo Vigencia dos produtos da proposta
<AUTHOR>
@since 21/02/2020
@version 1.0
*/
// -----------------------------------------------------------------
User Function TCRMI006()

Local dRet          := SToD("20491231")
Local cNivGarEst    := GetMV("TI_NIVGAES",,"0070;0026;0185")
Local oModel		:= FwModelActive()
Local oModelADY	    := oModel:GetModel("ADYMASTER")
Local cModal		:= oModelADY:GetValue("ADY_XMODAL")
Local cDtVig        := GetMV("TI_XDTVIGE",,"20210630")


If cModal $ cNivGarEst
    dRet := SToD(cDtVig)     
EndIf

Return dRet
