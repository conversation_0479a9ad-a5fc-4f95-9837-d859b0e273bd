#Include 'Totvs.ch'
#Include 'TopConn.ch'
#INCLUDE "FWMBROWSE.CH"
#INCLUDE "FWMVCDEF.CH"

/*/{Protheus.doc} TCRMX155
Processo de Integração Protheus x Totvs Store
@type function
<AUTHOR>
@since 11/06/2018
@version 1.0
@param cCodCons, character, (<PERSON><PERSON><PERSON> da <PERSON>)
@param cChave, character, (<PERSON><PERSON><PERSON> da Chave para Consulta)
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/

User Function TCRMX155(cCodCons,cChave,cId,cStatus,dDtProxEn,cHrProxEn,cReqJson,lSendBR)
	Local cProcPOX		:= ""
	Local lSkipDpl		:= GetMV('TI_X155DPL',,.T.)

	Default cCodCons 	:= "000001"
	Default cChave 	 	:= ""
	Default cId			:= ""
	Default cStatus		:= "1"
	Default dDtProxEn	:= Ctod("//")
	Default cHrProxEn	:= ""
	Default cReqJson	:= "{}"
	Default lSendBR		:= .f.

	If lSendBR
		cProcPOX := T155BR(cCodCons,cChave)
		return(cProcPOX)
	EndIf

	If lSkipDpl .And. !Empty(cProcPOX := U_TX155DPL(cCodCons, cChave))
		Return cProcPOX
	EndIf

	DbselectArea("POR")
	POR->(DbSetOrder(1))
	If POR->(dbSeek( xFilial('POR')  +  cCodCons  ))
	
		If POR->POR_ATIVO == "1"
			DbselectArea("POX")
			
			RecLock("POX",.T.)
				cProcPOX		:= GetSXENum('POX','POX_PROCES')
				POX->POX_PROCES	:= cProcPOX
				POX->POX_CODIGO	:= cCodCons
				POX->POX_CHAVE	:= cChave
				POX->POX_DTCRIA	:= dDataBase
				POX->POX_HRCRIA	:= Time()
				POX->POX_USCRIA	:= __cUserId
				POX->POX_STATEN	:= cStatus
				POX->POX_IDSTOR	:= cId
				POX->POX_DTPRXE	:= dDtProxEn
				POX->POX_HRPRXE := cHrProxEn
				POX->POX_RQBODY	:= cReqJson
				ConfirmSX8()
			MsUnLock()
		EndIf

	EndIf
			
Return cProcPOX

/*/{Protheus.doc} TCRM155BRW
Browse de Integração Protheus x Totvs Store
<AUTHOR> William da Silva
@since 12/06/2018
@version 1.0
/*/

User Function TCRM155BRW()
	
Local aArea	 := GetArea()
Local oBrowse:= FWMBrowse():New()

oBrowse:SetAlias("POX")
oBrowse:AddLegend( "POX_STATEN=='1' .AND. POX_STATRE==' '", "WHITE"  	, "Pendente de Envio" )
oBrowse:AddLegend( "POX_STATEN=='1' .AND. POX_STATRE=='2'", "RED"  	, "Falha no Envio" )
oBrowse:AddLegend( "POX_STATEN=='2'", "GREEN"  	, "Enviado" )
oBrowse:SetDescription("Integração Protheus/Middleware")
oBrowse:AddButton("Modo Aleatório", "U_TX155ABC()", , 2, 0, .F., 4, "1", )
oBrowse:Activate()
 
RestArea(aArea)
Return Nil

User Function exec155a()
	u_tdiexec("TX155ABC")
Return

User Function TX155ABC()

Local oDlg
Local oPnlFol
Local nQtd	:= SPACE(6)

// Cria diálogo
oDlg := FWDialogModal():New()
  oDlg:SetTitle( 'Modo Aleatório' )
  oDlg:SetBackground( .T. )   
  oDlg:SetEscClose( .T. )
  oDlg:SetSize(100,160) 
  oDlg:CreateDialog()
  oDlg:EnableFormBar( .T. )
  oDlg:CreateFormBar()
  
  oPnlFol := oDlg:GetPanelMain()    

  @ 010,010 SAY "Aleatório: Marcar para reenviar"   SIZE 100,007 OF oPnlFol PIXEL
  @ 007,090 MSGET oGet01 VAR nQtd  SIZE 023,010 OF oPnlFol PIXEL
  oGet01:cTooltip := "Quantidade de registros"
  @ 010,115 SAY "registros"       SIZE 040,007 OF oPnlFol PIXEL
  @ 022,010 SAY "de cada tipo de consulta disponível para Store via Notify!"       SIZE 180,007 OF oPnlFol PIXEL

  // Botões da barra de formulario
  oDlg:AddButton( "Confirmar",{|| nQtd:=Val(nQtd), FWMsgRun(, {|oSay| TX155CHK(oSay, nQtd)}, "Marcando registros", "Aguarde, processando..."), oDlg:Deactivate() }, "Confirmar" , , .T., .F., .T., )
  oDlg:AddButton( "Cancelar", {|| oDlg:Deactivate() }, "Fechar"  , , .T., .F., .T., )

// Ativa diálogo
oDlg:Activate()

Return

Static Function TX155CHK(oSay, nQtd)

Local aArea	 		:= GetArea()
Local aAreaPOR	 	:= POR->(GetArea())
Local aAreaPOX 		:= POX->(GetArea())
Local nX	 		:= 1
Local nt	 		:= 0
Local cAux	 		:= ''

Default nQtd		:= 0

DbselectArea("POR")
POR->(DbSetOrder(1))
POR->(dbGotop())

While POR->(!Eof())
	nX:=1
	If POR->POR_ATIVO != '1'
		POR->(dbSkip())
		Loop
	EndIf

	DbselectArea("POX")
	POX->(DbSetOrder(2))
	If POX->(dbSeek(xFilial("POX")+POR->POR_CODIGO))
		While nX <= nQtd .And. POR->POR_CODIGO == POX->POX_CODIGO
			
			If AllTrim(POX->POX_CHAVE) == cAux
				POX->(dbSkip())
				Loop
			EndIf
			
			oSay:cCaption := "Marcando para reenvio " + AllTrim(Str(nX)) + " de " + AllTrim(Str(nQtd)) + " para a consulta " + AllTrim(POR->POR_CODIGO)
    		ProcessMessages()

			cAux := AllTrim(POX->POX_CHAVE)
			RecLock("POX",.F.)
				POX_STATEN := '1'
				POX_STATRE := ' '
			POX->(MsUnLock())

			nX++
			nt++
			POX->(dbSkip())
		End
	EndIf
	POR->(dbSkip())
End

Aviso("Processo finalizado", "Foram marcados "+Alltrim(Str(nt))+" registros para reenvio!", {"Fechar"}, 2)

RestArea(aAreaPOR)
RestArea(aAreaPOX)
RestArea(aArea)
Return


Static Function T155BR(cCodCons,cChave)

Local aArea			:= getArea()
Local oRest 		:= NIL
Local cRet 			:= ""
Local aHeader		:= {}
Local cUsrJ			:= GetMv("MV_#USEZEN",,"sp01\ws.integrador")			//Usuário integrador/Zendesk
Local cPasJ			:= GetMv("MV_#PASZEN",,'^);P#9J"uVjw')					//Senha usuário integrador/Zendesk
Local cUrl      	:= GetMv("MV_#URLZEN",,"https://wscorp.totvs.com.br")	//URL integrador/Zendesk (Base DEV OPERACOES: http://*************:8049/)
Local cMetodo   	:= GetMv("MV_#METZEN",,"generatePOR")					// Metodo usado integrador/Zendesk
Local cParams		:= ""
Local oJson 		:= JsonObject():New()

aadd(aHeader, 'Authorization: Basic ' + Encode64(cUsrJ+":"+cPasJ))

cParams := "cCodCons="+cCodCons+"&cChave="+cChave

oRest 	:= FWREST():New(cURL)
oRest:SetPath(cMetodo)
oRest:SetGetParams(cParams)

oRest:Post(aHeader)

If AllTrim( oRest:oResponseH:cStatusCode ) $ '200/201/204'
	oJson:FromJson(oRest:GetResult())
	cRet := oJson["cPox"]
EndIF

restArea(aArea)
return(cRet)

/*/{Protheus.doc} TX155DPL
	Verifica se possui processo de integração	em aberto na fila (POX)
	@type  User Function
	<AUTHOR>
	@since 22/02/2023
	@param cCodCons, string, código da Integração POR
	@param cChave, string, chave do processo
	@return lRet, booleano, informando se existe processo pendente
/*/
User Function TX155DPL(cCodCons, cChave)
Local aArea		:= getArea()
Local cTemp		:= getNextAlias()
Local cCodigo	:= ""

BeginSql Alias cTemp

	SELECT POX_PROCES FROM %table:POX%
	WHERE POX_FILIAL=%xFilial:POX% 
	AND POX_CODIGO=%exp:cCodCons% 
	AND POX_CHAVE=%exp:cChave% 
	AND POX_STATEN='1'
	AND POX_DTENV=' '
	AND %notDel%

EndSql

cCodigo := (cTemp)->POX_PROCES

(cTemp)->(dbCLoseArea())
restArea(aArea)
return cCodigo
