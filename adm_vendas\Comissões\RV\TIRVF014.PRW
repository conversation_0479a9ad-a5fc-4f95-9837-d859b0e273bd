#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVF014     
Callback de VIEW INFORMACOES  PSA ACTIO - CUBO SERVICOS
@type  Function
<AUTHOR> Menabue Lima
@since 27/10/2023
@version 1.0
	/*/
User Function TIRVF014(cCodReq,cResponse,cAsync,cStatReq)

	Local oReceptor	:=TIINTERCEPTOR():New()
	Local oJson 	:= JsonObject():new()
	Local oActio	:= TIRVACTIO():New()
	Local nCount	:=0
	Local nPage		:=0
	Local nItens	:=0
	Local nMaxPg 	:= GetMv("TI_RVJ14PG" , .F., 50)
	Local aItens 	:= {}
	Local lHasNext  := .F.
	Local aArea     := P37->(GetArea())
	Local nI        :=0
	If (cStatReq == "2")//Processado a REquisicao
		oJson:fromJson(cResponse)
		Reclock("P37",.F.)

		P37->P37_STATUS :="F"
		P37->(MsUnlock())

		if oJson:hasProperty('value')
			aItens:=oJson['value']
			nItens:= Len(aItens)
			oJson:fromJson(P37->P37_BODY)
			//Verifica ser tem dados na proxima pagina
			IF(oJson:hasProperty('nCount'))
				nCount:=oJson['nCount']

				if (oJson:hasProperty('nPage'))
					nPage:=oJson['nPage']
					lHasNext:=(nItens>=nCount  .And. nPage <= nMaxPg)
				EndIF
			EndIF
			//se for a ultima pagina encerra e calcula p66
			if lHasNext .And. nPage == 1 .And. oJson['projeto']==""
				For nI:= 1 To nMaxPg
					U_TIRVJ014(nCount,nPage+nI,IIF(Empty(Alltrim(P37->P37_CODREQ)),P37->P37_COD,P37->P37_CODREQ),oJson['inicio'],oJson['fim'],oJson['lInicio']=="sim",,oJson['projeto'])
				Next nI
			ElseIF  lHasNext .And. nPage+1 > nMaxPg
				U_TIRVJ014(nCount,nPage+1,IIF(Empty(Alltrim(P37->P37_CODREQ)),P37->P37_COD,P37->P37_CODREQ),oJson['inicio'],oJson['fim'],oJson['lInicio']=="sim",,oJson['projeto'])
			ElseIF 	 lHasNext
				U_TIRVJ014(nCount,nPage+1,IIF(Empty(Alltrim(P37->P37_CODREQ)),P37->P37_COD,P37->P37_CODREQ),oJson['inicio'],oJson['fim'],oJson['lInicio']=="sim",,oJson['projeto'])
			EndIF
			cComp:=Left(STRTRAN(oJson['inicio'],"-",""),6)
			P37->(RestArea(aArea))
			//Grava dados na tabela
			oActio:setRVPsaData(aItens,"CUBO",cComp)

			if !oActio:isError()
				Reclock("P37",.F.)
				P37->P37_STATUS :="2"
				P37->(MsUnlock())
				if !lHasNext
					//Chama o Metodo que calcula do Realizado do PSA e grava na P66
					//oActio:CalcRVPsa("MARGEM ESPERADA")
				EndIF

			else
				Reclock("P37",.F.)
				P37->P37_STATUS :="3"
				P37->P37_BODYRP  := '{"message":"'+oActio:GetError()+'"}'
				P37->(MsUnlock())
			EndIF
		else
			Reclock("P37",.F.)
			P37->P37_STATUS :="3"
			P37->P37_BODYRP  := '{"message":"Tag Values nao encontrada"}'
			P37->(MsUnlock())
		EndIF
	EndIF
	FreeObj(oActio)
	FreeObj(oReceptor)
Return
