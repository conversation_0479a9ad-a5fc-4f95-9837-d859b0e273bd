#INCLUDE "PROTHEUS.CH"

/*------------------------------------------------------*/
/* ROBERTO MARQUES - TDI - 29/01/2020 
/* PONTO DE ENTRADA  F685STA()
/* Tratamento do tamanhos dos campos de acordo com dicionario
/*------------------------------------------------------*/

USER FUNCTION F692STRU()

    Local oStr := PARAMIXB[1]


    //Cliente
	oStr:AddField("Cliente","","YYY_CLIENTE","C",TamSX3("A1_COD")[1])
	//Loja
	oStr:AddField("Loja","","YYY_<PERSON>OJ<PERSON>","C",TamSX3("A1_LOJA")[1])
	//Nome
	oStr:AddField("Nome","","YYY_NOME","C",TamSX3("A1_NOME")[1])
	//Total
	oStr:AddField("Total","","YYY_TOTFAT","N",TamSX3("YYY_TOTFAT")[1],TamSX3("YYY_TOTFAT")[2])




Return oSTr 