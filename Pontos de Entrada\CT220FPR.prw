#Include "Totvs.ch"

//-------------------------------------------------------------------
/*/{Protheus.doc} CT220FPR
Ponto de Entrada após a preparação do ambiente para o processo 
de consolidação são carregados os dados das empresas e arquivos que serão agrupados.
Permite ou não continuar o processamento.
Usado para desbloqueio de calendário que existia na V12 na Tovs.

Projeto Migrção P12 - Requisito 0067
<AUTHOR>
@since 19/03/2020
@version 1.0
/*/
//-------------------------------------------------------------------

User Function CT220FPR()
    Local dDataIni	 := MV_PAR02
    Local dDataFim	 := MV_PAR03
    Local lBlCTBA010 := U_RetBA010()
    Local lBlCTBA190 := U_RetBA190()
    Local lBlCTBA193 := U_RetBA193()
    Local lPerBloq   := U_RetPBloq()

    //------------------------------------------------
    // Desbloqueia a rotina CTBA010
    //------------------------------------------------
    If lBlCTBA010
        UnLockByName("CTBA010",.T.,.T.)
    EndIf

    //------------------------------------------------
    // Desbloqueia a rotina CTBA190 
    //------------------------------------------------
    If lBlCTBA190
        UnLockByName("CTBA190",.T.,.F.)
    EndIf

    //------------------------------------------------
    // Desbloqueia a rotina CTBA193
    //------------------------------------------------
    If lBlCTBA193
        UnLockByName('CTBFILA'+XFilial("CT2"),.T.,.F.)
    EndIf

    //------------------------------------------------
    // Desbloqueia o periodo processado no calendario
    //------------------------------------------------
    If lPerBloq
        U_CT220CTG(3,dDataIni,dDataFim)
    EndIf
Return