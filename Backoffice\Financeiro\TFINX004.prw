/*/
{PROJETO} - TFINX004.PRW
@desc:		Migra��o de dados Bordero.   
@author:	<PERSON><PERSON>
@version: 	1.00
@since: 	07/01/2015
/*/

#include "protheus.ch"

User Function TFINX004()

Local cQry		:= ""
Local cStatus	:= ""
Local nPos		:= 0
Local nItem		:= 1
Local cTamPro	:= TamSx3("PSB_PROAPR")[1]
Local cProApr	:= ""
Local aStatus	:= {}
Local aIDP11	:= {}
Local cQry2		:= ""
Local cQry3		:= ""
Local nPosP11	:= 0
Local cVersao	:= ""

	cQry2 := " SELECT ID_IDUPV12, ID_IDUP11M FROM PA_IDUSRCDP "
	
	If Select("QRY2") > 0; ("QRY2")->(dbCloseArea()); Endif  
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry2),"QRY2",.T.,.T.)
	
	DbSelectArea("QRY2")
	
	QRY2->(DbGoTop())
	
	While !QRY2->(Eof())
	
		aADD(aIDP11, {QRY2->ID_IDUPV12, QRY2->ID_IDUP11M})
		
		QRY2->(DbSkip())
	
	End
	
	
	//Array com o De - Para dos Status
	
	aADD(aStatus, {"M", "2"})
	aADD(aStatus, {"R", "5"})
	aADD(aStatus, {"T", ""})
	aADD(aStatus, {"C", ""})
	aADD(aStatus, {"L", "4"})

	//Query para preenche informa��es complementares
	cQry := " SELECT ZJB_FILIAL, ZJB_NUM, ZJB_STATUS, ZJB_USER, ZJB_SOLICI, ZJB_FILIAL, ZJB_DTLIB, ZJB_TOTAL, ZJB_USRLIB "
	cQry += " FROM " + RetSqlName("ZJB") + " ZJB "
	cQry += " WHERE ZJB.D_E_L_E_T_ <> '*' AND ZJB_TIPO = 'BR' AND ZJB_STATUS = 'L' "
	
	If Select("QRY") > 0; ("QRY")->(dbCloseArea()); Endif  
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),"QRY",.T.,.T.)
	
	DbSelectArea("QRY")
	
	QRY->(DbGoTop())
	
	While QRY->(!EOF())	

		nPos		:= aScan(aStatus,{|x| Alltrim(x[1]) == Alltrim(QRY->ZJB_STATUS)})
		cStatus		:= aStatus[nPos][2]
		cProApr		:= U_PROcod()
		nPosUser	:= aScan(aIDP11,{|x| Alltrim(x[2]) == Alltrim(QRY->ZJB_USER)})
		
		cQry3 := " SELECT MAX(R_E_C_N_O_) MAIOR FROM ZJB000 ZJB "
		cQry3 += " WHERE ZJB.D_E_L_E_T_ <> '*' AND ZJB_NUM = '" + QRY->ZJB_NUM + "' "
		
		If Select("QRY3") > 0; ("QRY3")->(dbCloseArea()); Endif  
		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry3),"QRY3",.T.,.T.)
		
		cVersao := StrZero(QRY3->MAIOR,4,0)
		
		If QRY->ZJB_SOLICI <> "******        "
			nPosSol		:= aScan(aIDP11,{|x| Alltrim(x[2]) == Alltrim(QRY->ZJB_SOLICI)})
			Else
			nPosSol := 0
		EndIf
		
		DbSelectArea("PSB")
		DbSetOrder(1)

		If PSB->(DbSeek(xFilial("PSB") + AvKey(QRY->ZJB_NUM, "PSB_BORDER")))
			
			PSB->(RecLock("PSB",.F.))
	
				PSB->PSB_STATUS	:= cStatus
				PSB->PSB_USUCRI	:= IIf(nPosSol > 0, Alltrim(aIDP11[nPosUser][1]),QRY->ZJB_USER)
				PSB->PSB_USUSOL	:= IIf(nPosSol > 0, Alltrim(aIDP11[nPosSol][1]), "")
				PSB->PSB_PROAPR	:= cProApr
				PSB->PSB_VERSAO	:= cVersao
		
			PSB->(MsUnlock())
		
			PRO->(RecLock("PRO",.T.))
		
				PRO->PRO_FILIAL	:= QRY->ZJB_FILIAL
				PRO->PRO_CODIGO	:= cProApr
				PRO->PRO_DATA	:= STOD(QRY->ZJB_DTLIB)
				PRO->PRO_USUCRI	:= IIf(nPosSol > 0, Alltrim(aIDP11[nPosUser][1]),QRY->ZJB_USER)
				PRO->PRO_STATUS	:= cStatus
		
			PRO->(MsUnlock())
		
			PSO->(RecLock("PSO",.T.))
		
				PSO->PSO_FILIAL	:= QRY->ZJB_FILIAL
				PSO->PSO_CODIGO	:= cProApr
				PSO->PSO_ITEM	:= "000001"
				PSO->PSO_PAPEL	:= "000001"
				PSO->PSO_CODAPR	:= IIf(nPosSol > 0, Alltrim(aIDP11[nPosUser][1]),QRY->ZJB_USER)
				PSO->PSO_STATUS	:= cStatus
			
			PSO->(MsUnlock())
		
			PSP->(RecLock("PSP",.T.))
		
				PSP->PSP_FILIAL	:= QRY->ZJB_FILIAL
				PSP->PSP_BORDER	:= QRY->ZJB_NUM
				PSP->PSP_PROAPR	:= cProApr
				PSP->PSP_USER	:= IIf(nPosSol > 0, Alltrim(aIDP11[nPosUser][1]),QRY->ZJB_USER)
				PSP->PSP_DATA	:= STOD(QRY->ZJB_DTLIB)
				PSP->PSP_VALBOR	:= QRY->ZJB_TOTAL
				PSP->PSP_MOVIME	:= IIf(Alltrim(QRY->ZJB_STATUS) == "L", "1", "2")
				PSP->PSP_VERSAO := cVersao
				PSP->PSP_PAPEL	:= "000001"
				
			PSP->(MsUnlock())
			
			nItem++
		
		EndIf
		
		cVersao := ""
		
		QRY->(DbSkip())
		
	End 
	
	Alert("Acabou: " + Time())
		
Return		