User Function PROCLIST()

Local lRet       := .T. 
Local aParam1 	 := {}
Local aRetP1	 := {}
Local cObs       := ''
Local nI		 := 0
Local nPontos    := 0 
Local oDlg       := Nil    
Local nOpc       := 2 
Local cNomeArq   := '' 
Private cErrArqRet := '' 
Private cLogArqRet := '' 

cObs := cGetFile("Arquivos CSV|*.CSV|","Lista de Clientes em CSV para reprocessar ZQE",0,,.T.,GETF_ONLYSERVER+GETF_LOCALFLOPPY+GETF_LOCALHARD  )
	
If !Empty(cObs)
	nOpc := 1
EndIf
	
If nOpc == 1 
	cObs := Alltrim(cObs)	
	If Empty( cObs ) 
		lRet := .F.
	Endif
			
	If !File(  (cObs)  )
		lRet := .F.
	EndIf
		
	If lRet 
		For ni := 1 to Len(cObs) 
			If substr(cObs,ni,1) == "\"
				nPontos := ni + 1
			EndIf 
		Next
		If nPontos > 0 
			cNomeArq := substr(cObs, nPontos, Len(cObs))
		EndIf

        FwMsgRun(, { || Reprocessa(cObs) }, "Processando","Aguarde")
		
	EndIf
EndIf

Return

//-------------------------------------------------------------------
/*{Protheus.doc} Reprocessa
Varre CSV invocando rotina de reprocessamento padrao
<AUTHOR> Leite
@since 15/07/2015
@version Protheus12
*/
//-------------------------------------------------------------------
Static function Reprocessa(cArqRet)
Local cBuffer := ""
Local nCpos
Local cString := ""
Local aCampos := {}
Local nHandler
Local nLinha := 0 

Procregua(0)
cErrArqRet := strtran(cArqRet, ".", "_erro.")
cLogArqRet := strtran(cArqRet, ".", "_log.")
nHandler := fOpen(cArqRet,68)
	
FT_FUSE(cArqRet)
FT_FGOTOP()

While !FT_FEOF()

	nLinha += 1
	cBuffer := FT_FReadLn()
	aCampos := {}
	cString := "" 

	For nCpos := 1 to Len(cBuffer)
		cString += IIf( Substr(cBuffer,nCpos,1) != ";", Substr(cBuffer,nCpos,1), "")

		If Substr(cBuffer,nCpos,1) == ";"
	
			aAdd(aCampos, Upper(cString))
			cString := ""
		Endif
	Next
	
	If !Empty(cString)
		aAdd(aCampos, Upper(cString))
	EndIf
	
	If Len(aCampos) > 0
		
        bBlock	:= ErrorBlock({|e|  ThrdChkErr( e, aCampos[1], aCampos[2])  })
		BEGIN SEQUENCE
		dbselectarea('SA1')
		dbsetorder(1)
		SA1->(Dbseek( xfilial('SA1') + Alltrim(aCampos[1])  )  )

		If SA1->(!Eof()) .And. AllTrim(SA1->A1_COD) == Alltrim(aCampos[1])
			aCampos[2] := SA1->A1_LOJA 
		Endif
		
		U_GC004PROC( aCampos[1], aCampos[2] )
		END SEQUENCE
		ErrorBlock(bBlock)

        U_xacalog(cLogArqRet, aCampos[1] + ";" +  aCampos[2] + ";feito"  )

        
	EndIf
		
	FT_FSKIP()
EndDo

FT_FUSE()
FCLOSE(nHandler)
MsgInfo('Processo Finalizado!')
return


//----------------------------------------------------------------
/*/{Protheus.doc} ThrdChkErr
Captura errlog 

<AUTHOR>
@since 10/07/2020
@version 1.0
/*/
//----------------------------------------------------------------
Static Function ThrdChkErr(oErroArq, cCli, cLoja) 
Local n:= 2
Local cLogError 
Local cDir :=  ""
Local cbErrInThrd := ''
Local cRet := oErroArq:Description + oErroArq:ErrorStack
U_xacalog(cErrArqRet , (cCli + '-' + cLoja + ' <-----'  )   )
U_xacalog(cErrArqRet , cRet)
If oErroArq:GenCode > 0

    cbErrInThrd += Alltrim( Str( oErroArq:GenCode ) )
    cbErrInThrd += chr(13)+chr(10)
    cbErrInThrd += AllTrim( oErroArq:Description )
    cbErrInThrd += chr(13)+chr(10)
EndIf 

While ( !Empty(ProcName(n)) )
    cbErrInThrd += Trim(ProcName(n)) + " (" + Alltrim(Str(ProcLine(n))) + ") " + chr(13)+chr(10)
    n++
End   

cbErrInThrd += chr(13)+chr(10)

U_xacalog(cErrArqRet , cbErrInThrd )          

Break
Return cRet 

