#include    "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Service.StructureArrayToChangeGroupToSysUsrService

/*/{Protheus.doc} StructureArrayToChangeGroupToSysUsr
This static class aims to construct the necessary array to
change the group of a user
@type class
@version 1.0
<AUTHOR>
@since 11/26/2024
/*/
Class   StructureArrayToChangeGroupToSysUsr
    Static  Method  execute(oJsonUsr    As  Json)   As  Array
EndClass

/*/{Protheus.doc} StructureArrayToChangeGroupToSysUsrService::execute
This method construct the array with the necessary field
@type method
@version  1.0
<AUTHOR>
@since 11/26/2024
@param oJsonUsr, json, The oJsonUsr as parameter
@return array, The necessary array
/*/
Method  execute(oJsonUsr    As  Json)   As  Array   Class   StructureArrayToChangeGroupToSysUsr
    Local   aDados  :=  {}  As  Array   

    aAdd( aDados, { "USR_CODIGO" ,  Upper( oJsonUsr:GetJsonObject( "USR_CODIGO" ) ) } )
    aAdd( aDados, { "USR_GRUPO"  ,  oJsonUsr:GetJsonObject( "USR_GRUPO" )           } )
Return  aDados
