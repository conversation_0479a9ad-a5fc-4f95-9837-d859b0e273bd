#include 'protheus.ch'
#include 'parmtype.ch'

/*/{Protheus.doc} TFATAX95
//TODO Preenche o campo PZG_DTEMIS no browse.
<AUTHOR>
@since 01/10/2019
@version 1.0
@return Data de emissao do pedido

@type function
/*/
user function TFATAX95()
	Local dRet := cTod('')
	Local cPedido := ""

	DbSelectArea("PZH")
	PZH->(DbSetOrder(1))

	PZH->(DbGotop())
	If PZH->(DbSeek(xFilial("PZG")+STR(PZG->PZG_ID,10,0))) 

		While STR(PZG->PZG_ID) == STR(PZH->PZH_IDPLN)
			cPedido:= PZH->PZH_PEDIDO
			If cPedido < PZH->PZH_PEDIDO
				cPedido:= PZH->PZH_PEDIDO
			EndIf

			PZH->(DbSkip())
		EndDO

		DbSelectarea("SC5")
		SC5->(DbSetOrder(1))
		If SC5->(DbSeek(xFilial("SC5")+cPedido))
			dRet:= SC5->C5_EMISSAO
		EndIf
	EndIf



return dRet