#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Service.StructureArrayToUsrService

using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.TransformCharacterToJsonObjectUtils
using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.CollectionOfParamUtils
/*/{Protheus.doc} StructureArrayToUsr
This statical class creates the array to use on the SYS_USR Table 
@type class
@version  1.0
<AUTHOR>
@since 10/31/2024
/*/
Class StructureArrayToUsr 
    static method getArrayFromJson() as Array 
EndClass


/*/{Protheus.doc} StructureArrayToUsr::getArrayFromJson
This method aims to create the array to use on SYS_USR Table 
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@param cJson, character, the income json 
@return array, the array to use in the routines to create the user
/*/
Method getArrayFromJson(cJson as Character) as Array class StructureArrayToUsr 
    Local oJsonUSR  :=  JsonObject():new()   as Json
    Local oJsonSSI  :=  JsonObject():new()   as Json
    Local aDados    :=  {} as Array
    Local lSSOObr:=     .F.  as Logical
    Local cPassDef := 'TOTVS@um1dois2tres3' as Character
    Local oParamStore

    oJsonUSR  := TransformCharacterToJsonObject():exec(cJson, "USR")
    oJsonSSI  := TransformCharacterToJsonObject():exec(cJson, "USRSO")
    
    

    aAdd( aDados, { "USR_CODIGO" ,  Upper( oJsonUSR:GetJsonObject( "USR_CODIGO" ) )                     } )
    aAdd( aDados, { "USR_NOME"   ,  SubStr(Upper( oJsonUSR:GetJsonObject( "USR_NOME" ) ),0, 40)         } )
    aAdd( aDados, { "USR_EMAIL"  ,  Lower( oJsonUSR:GetJsonObject( "USR_EMAIL" ) )                      } )
    aAdd( aDados, { "USR_GRUPO"  ,  oJsonUSR:GetJsonObject( "USR_GRUPO" )                               } )
    aAdd( aDados, { "USR_CARGO"  ,  oJsonUSR:GetJsonObject( "USR_CARGO" )                               } )
    aAdd( aDados, { "USR_PSW"    ,  cPassDef                                                          } ) 

    oParamStore :=  CollectionOfParam():new()
    lSSOObr   :=  oParamStore:getUseSso()

    If lSSOObr .And. ValType(oJsonSSI) == 'J'
        aAdd( aDados, { "USR_SO_DOMINIO"   ,  Upper( oJsonSSI:GetJsonObject( "USR_SO_DOMINIO" ) )           } )
        aAdd( aDados, { "USR_SO_USERLOGIN" ,  Lower( oJsonSSI:GetJsonObject( "USR_SO_USERLOGIN" ) )         } )
    EndIf

Return aDados 
