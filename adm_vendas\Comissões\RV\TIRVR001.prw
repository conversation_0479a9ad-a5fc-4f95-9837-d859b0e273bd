#include 'protheus.ch'
#include 'FWBROWSE.CH'
#INCLUDE 'FWMVCDEF.CH'

/*/{Protheus.doc} TIRVR001
Relatorio de indicadores Template Actio
	@type  Function
	<AUTHOR> Menabue Lima
	@since 05/10/2023
	@version 12.1.33
/*/
User function TIRVR001()
	Local aBoxParam := {}
	Private aRetParam := {}

	Aadd(aBoxParam, {1, "Indicador De"		    , Space(6)     , "@!", "","P63" , ".T.", 60, .F.})
	Aadd(aBoxParam, {1, "Indicador Ate "		, "ZZZZZZ" , "@!", "","P63" , ".T.", 60, .F.})
	aAdd(aBoxParam, {6, "Salvar Em"         ,"C:\totvs_relatorios\","","","",80,.T.,"XLS (*.xls*) |*.xls*","C:\",GETF_LOCALHARD+ GETF_LOCALFLOPPY + GETF_NETWORKDRIVE+128})

	If ParamBox(aBoxParam, "Relatorio de Indicadores Actio", @aRetParam)
		FwMsgRun(,{|oSay|U_TIRVRE01(oSay,aRetParam)}, "Imprimindo dados","Aguarde"	)
	else
		MsgInfo("Cancelado pelo Usuario")
	EndIF
Return

/*/{Protheus.doc} TFA099XLS
  Funcao Responsavel por Extrair os indicadores RV
  	@type  Function
    <AUTHOR> Menabue Lima
    @since 05/10/2023
    @version 1.0
    /*/
User Function TIRVRE01(oSay,aRetParam)
	Local cArquivo  := ""
	Local oExcel    := FWMsExcelEx():New()
	Local cAba      := "Indicadores Actio"
	Local cTitAba   := cAba
	Local oExcelMs  := MsExcel():New()
	Local aLinha    := {}
	Local aCampos	:= {}
	Local cMesComp  := ""
	Local cAlsP81   := CriaTrab(Nil,.F.)
	Local cQuery    := ""
	Local nI		:= 0
	Local cIndTri:= SuperGetMV( "TI_RVTRI", .F., "ESCOPO|MGESPE|PRODEQ|PRODCP" )

	IF Len(aRetParam) > 0
		cMesComp := aRetParam[1]
		cArquivo := Alltrim(aRetParam[3]) +cAba+"_"+dTos(Date()) + strtran(IncTime(time() ,3,0,0 ),":")+".xls"
		cTitAba  := cAba
		oExcel:AddworkSheet(cAba)
		oExcel:AddTable(cAba,cTitAba,.F.)

		aadd( aCampos , {"Codigo item"	 				,1,"P81_INDFIN"})
		aadd( aCampos , {"Nome do item"	 				,1,"P81_NOMEIN"})
		aadd( aCampos , {"Matricula"	 				,1,"P81_MATRIC"})
		aadd( aCampos , {"Status"  		 				,1,"P81_MSBLQL"})
		aadd( aCampos , {"Codigo da area"				,1,"P81_AREA"})
		aadd( aCampos , {"Codigo faixa de farol"  		,1,"Faixa-5"})
		aadd( aCampos , {"Codigo unidade de medida"		,1,"R$"})
		aadd( aCampos , {"Periodicidade"				,1,"4"})
		aadd( aCampos , {"Polaridade"					,1,"1"})
		aadd( aCampos , {"Numero de casas decimais"		,1,"2"})
		aadd( aCampos , {"Codigo do desdobramento"		,1,""})
		aadd( aCampos , {"Data inicio"					,1,"P81_DTINI"})
		aadd( aCampos , {"Observacao"					,1,""})
		aadd( aCampos , {"Tipo consolidacao Meta"		,1,"4"})
		aadd( aCampos , {"Tipo consolidacao realizado"	,1,"4"})



		For nI:= 1 to len(aCampos)
			oExcel:AddColumn(cAba,cTitAba,aCampos[nI][1],1,aCampos[nI][2])
		Next nI

		If Select(cAlsP81) > 0
			(cAlsP81)->(dbCloseArea())
		Endif
		//TRIM(P81_MATRIC)||'-'||TRIM(P65_DSAREA)||'-'||TRIM(P65_DCARGO)||'-'||
		cQuery := " SELECT DISTINCT P64_CAREA AS P81_AREA, TRIM(COALESCE(P63_DESC,' ')) AS P81_NOMEIN,P81.* "
		cQuery += " FROM " + RetSqlName("P81") + " P81 "
		cQuery += " INNER JOIN " + RetSqlName("P64") + " P64 ON "
		cQuery += " 	P64_FILIAL=P81_FILIAL  "
		cQuery += " 	AND P64_MATRIC=P81_MATRIC  "
		cQuery += " 	AND P64.D_E_L_E_T_ = ' '   "
		cQuery += " LEFT JOIN " + RetSqlName("P63") + " P63 ON "
		cQuery += " 	P63_FILIAL=P81_FILIAL  "
		cQuery += " 	AND P81_INDICA = P63_CODIND  "
		cQuery += " 	AND P63.D_E_L_E_T_ = ' '   "
		cQuery += " WHERE "
		cQuery += "   P81.D_E_L_E_T_ = ' ' "
		cQuery += "   AND P81.P81_INDICA BETWEEN '"+aRetParam[1]+"' AND '"+aRetParam[2]+"'"
		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsP81,.T.,.T.)
		ProcessMessage()
		While !(cAlsP81)->( Eof() )
			aLinha 	:=	{}
			For nI:= 1 to Len(aCampos)
				if ("P81" $ aCampos[nI][3])
					IF( aCampos[nI][3]=="P81_STATUS")
						aAdd(aLinha,iif((cAlsP81)->&(aCampos[nI][3])=="1","4","2"))
					ElseIF( aCampos[nI][3]=="P81_DTINI")
						aAdd(aLinha,StoD((cAlsP81)->&(aCampos[nI][3])))
					ElseIF( aCampos[nI][3]=="P81_NOMEIN")
						Do Case
						Case  "MGESPE" $ (cAlsP81)->P81_INDFIN
							cComNam:="MARGEM ESPERADA"
						Case  "PRODEQ" $ (cAlsP81)->P81_INDFIN
							cComNam:="PRODUTIVIDADE EQUIPE"
						Case  "PRODCP" $ (cAlsP81)->P81_INDFIN
							cComNam:="PRODUTIVIDADE CP"
						Case  "ESCOPO" $ (cAlsP81)->P81_INDFIN
							cComNam:="HORAS ESCOPO FECHADO"
						Case  ".SUB" $ (cAlsP81)->P81_INDFIN
							cComNam:=Alltrim((cAlsP81)->&(aCampos[nI][3]))+" - SUB"
						Case  ".CNPJ" $ (cAlsP81)->P81_INDFIN
							cComNam:=Alltrim((cAlsP81)->&(aCampos[nI][3]))+" - CNPJ"
						Case  "IMPL" $ (cAlsP81)->P81_INDFIN
							cComNam:= "IMPLANTACAO"
						Otherwise
							cComNam:=Alltrim((cAlsP81)->&(aCampos[nI][3]))
						EndCase
						aAdd(aLinha,Alltrim(cComNam))
					Else
						aAdd(aLinha,(cAlsP81)->&(aCampos[nI][3]))
					EndIF
				Else
					if aCampos[nI][1] != "Periodicidade"
						aAdd(aLinha,aCampos[nI][3])
					Else
						cInd:=ALLTRIM((cAlsP81)->P81_INDFIN )
						nTamInd:=Len(cInd)
						nPosPt:=Rat(".",cInd)+1
						if(Substr(cInd,nPosPt,nTamInd) $ cIndTri)
							aAdd(aLinha,"6") // para esses indicadores vai 6 por ser trimestra e nao mensal
						Else
							aAdd(aLinha,aCampos[nI][3])
						EndIF
					EndIF
				EndIF
			Next nI

			oExcel:AddRow(cAba,cTitAba,aLinha )
			(cAlsP81)->( dbSkip() )
		EndDo
		(cAlsP81)->(dbCloseArea())
		//Criando o XML
		oExcel:Activate()
		oExcel:GetXMLFile(cArquivo)
		if File( cArquivo)
			//Abrindo o excel e abrindo o arquivo xml
			oExcelMs:WorkBooks:Open(cArquivo)     //Abre uma planilha
			oExcelMs:SetVisible(.T.)                 //Visualiza a planilha
			oExcelMs:Destroy()                        //Encerra o processo do gerenciador de tarefas
			MsgInfo("Relatorio Gerado no Caminho ("+cArquivo+")")
		Else
			Alert("Relatorio nao gerado no Caminho ("+cArquivo+"), verifique o diretorio e tente novamente!")
		EndIF
	Else
		MsgInfo("Parametros Cancelados")
	EndIF
Return
