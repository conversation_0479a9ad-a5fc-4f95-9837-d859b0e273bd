#Include "Totvs.ch"

Static lPerBloq	  := .F. //Variavel utilizada para indicar se o periodo foi bloqueado, realizando o desbloqueio na conclusao
Static lBlCTBA010 := .F. //Variavel utilizada para indicar se a rotina CTBA010 foi bloqueada, realizando o desbloqueio na conclusao
Static lBlCTBA190 := .F. //Variavel utilizada para indicar se a rotina CTBA190 foi bloqueada, realizando o desbloqueio na conclusao
Static lBlCTBA193 := .F. //Variavel utilizada para indicar se a rotina CTBA193 foi bloqueada, realizando o desbloqueio na conclusao

//-------------------------------------------------------------------
/*/{Protheus.doc} CT220SEL
Ponto de Entrada no momento da preparação do ambiente para o processo 
de consolidação são carregados os dados das empresas e arquivos que serão agrupados.
Permite ou não continuar o processamento.
Usado para validar o bloqueio de calendário que existia na V12 na Tovs.

Projeto Migrção P12 - Requisito 0067
<AUTHOR> Soares
@since 19/03/2020
@version 1.0
/*/
//-------------------------------------------------------------------
User Function CT220SEL()
    Local __lBlqCal  := SuperGetMv("MV_TPBLCON",.F.,"1") == "2" 
    Local aTabExc	 := {"CT2","CQ0","CQ1","CQ2","CQ3","CQ4","CQ5","CQ6","CQ7","CQ8","CQ9","CTG"} 
    Local lRet       := .T.
    Local dDataIni	 := MV_PAR02
    Local dDataFim	 := MV_PAR03
    Local nX         := 0

    //------------------------------------------------------------------------------------
    // Consulta se as tabelas de movimento, saldos e calendario sao totalmente exclusivas
    //------------------------------------------------------------------------------------
    If lRet .And. __lBlqCal
        For nX := 1 To Len(aTabExc)
            If FWModeAccess(aTabExc[nX],3) != "E" 
                lRet := .F.
                Help(" ",1,"CT220SEL",,"Para o processamento com bloqueio de calendário, a tabela " + aTabExc[nX] + " precisa ser totalmente exclusiva. Processo definido no parâmetro MV_TPBLCON.",1,0) 
                Exit
            EndIf
        Next nX
    EndIf

    //--------------------------------------------------------------------
    // Consulta se nao ha fila de processamento para atualizacao de saldo
    //--------------------------------------------------------------------
    If lRet .And. __lBlqCal
        lRet := U_CT22VCQA(dDataIni,dDataFim)
    EndIf

    //--------------------------------------------------------------------
    // Consulta se a rotina CTBA010 nao esta em execucao por outra thread
    //--------------------------------------------------------------------
    If lRet .And. __lBlqCal
        If LockByName("CTBA010",.T.,.T.)
            lBlCTBA010 := .T.
        Else
            lRet := .F.
            Help(" ",1,"CT220SEL",,"O Sistema não conseguiu proteger o calendário contábil da filial consolidadora de ser editado. Não pode haver usuários editando o calendário da filial consolidadora.",1,0) 
        EndIf
    EndIf

    //--------------------------------------------------------------------
    // Consulta se a rotina CTBA190 nao esta em execucao por outra thread
    //--------------------------------------------------------------------
    If lRet .And. __lBlqCal
        If LockByName("CTBA190",.T.,.F.)
            lBlCTBA190 := .T.
        Else
            lRet := .F.
            Help(" ",1,"CT220SEL",,"O Sistema não conseguiu proteger a rotina Reprocessamento de Saldos de ser executada. A rotina Reprocessamento de Saldos não pode estar em uso.",1,0) 
        EndIf
    EndIf

    //--------------------------------------------------------------------
    // Consulta se a rotina CTBA193 nao esta em execucao por outra thread
    //--------------------------------------------------------------------
    If lRet .And. __lBlqCal
        If LockByName('CTBFILA'+XFilial("CT2"),.T.,.F.)
            lBlCTBA193 := .T.
        Else
            lRet := .F.
            Help(" ",1,"CT220SEL",,"O Sistema não conseguiu proteger a rotina Processamento de Saldos de ser executada. A rotina Processamento de Saldos não pode estar em uso.",1,0) //
        EndIf
    EndIf

    //-----------------------------------------------------------------------------------
    // Consulta se o calendario tem periodo em aberto anterior ao que sera consolidado
    //-----------------------------------------------------------------------------------
    If lRet .And. __lBlqCal
        lRet := U_CT220CTG(1,dDataIni,dDataFim) 
    EndIf

    //---------------------------------------------------
    // Bloqueia o periodo a ser processado no calendario
    //---------------------------------------------------
    If lRet .And. __lBlqCal
        If U_CT220CTG(2,dDataIni,dDataFim)
            lRet		:= .T.
            lPerBloq	:= .T.
        EndIf
    EndIf

Return lRet

User Function RetBA010()
Return (lBlCTBA010)

User Function RetBA190()
Return (lBlCTBA190)

User Function RetBA193()
Return (lBlCTBA193)

User Function RetPBloq()
Return (lPerBloq)
