
#INCLUDE "TOTVS.CH"

Static n060QtdTit   := 0

/*/{Protheus.doc} FA60CAN2 tico-449
Geracao de bordero  FINA060 e FINA061 desmarcar e1_situaca dos titulos filhos ao remover o titulo pai do <PERSON> 
<AUTHOR>
@since  09/01/2019
/*/
User Function FA60CAN2()
	Local lUsa	:= GetMV("TI_ENSIT",,.T.)

	//-------------------------------------------------------------------------------
	//Projeto TDI Upgrade P12
	//Wagner Soares - 15/02/20
	Local oBordero := TIBorderoVersao():New(.F.,,.T.)
	Local cFilBor  := xFilial("SEA")
	Local cNumBor  := mv_par01
	//-------------------------------------------------------------------------------

	If lUsa //funcionalidade ligada
		U_AtuSitImps(SE1->E1_SITUACA)//SE1 esta posicionado no titulo Pai neste trecho
	EndIf
	
	//-------------------------------------------------------------------------------
    If ( ValType(oBordero) == "O" .And. oBordero:IsInitialized() )

        n060QtdTit++

        If ( n060QtdTit == oBordero:GetLength() )

            If oBordero:CancelBordero(cFilBor,cNumBor,"R")
            	n060QtdTit := 0
			EndIf
            oBordero:Finalize()
			
        EndIf
    EndIf
	//-------------------------------------------------------------------------------
Return 