<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project basedir="." default="move_patch">
	<import file="func_aux_substr.xml" />
	<tstamp />

	<!-- car<PERSON>ga as variaveis de ambiente -->
	<property environment="env" />
	<property name="fileerro" 	value="" />

	<tstamp>
		<format property="touch.time" pattern="yyyyMMdd"/>
	</tstamp>	

	<!--	Dependencias 	-->
	<taskdef resource="net/sf/antcontrib/antcontrib.properties">
		<classpath>
			<pathelement location="${env.ANT_HOME}/lib/ant-contrib-0.3.jar"/>
		</classpath>
	</taskdef>
	<taskdef resource="net/sf/antcontrib/antlib.xml" >
		<classpath>
			<pathelement location="${env.ANT_HOME}\lib\ant-contrib-1.0b3.jar"/>
		</classpath>
	</taskdef>

	<!-- Move os patchs informados nos parâmetros para as estruturas de aplicados nas releases e mains -->
	<target name="move_patch">
		<!-- Move o patch da producao/aplicar para a producao/aplicados/data -->
		<echo message="Move o patch da producao/aplicar para a producao/aplicados/${touch.time} --" level="info"/>
		<echo message=" - $/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_SOURCE_FONTES}/Patch/aplicar/${PROTHEUS_PATCHS_TYPE}/${fileerro}" level="info"/>
		<echo message=" - $/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_SOURCE_FONTES}/Patch/aplicados/${touch.time}/${fileerro}" level="info"/>
		
		<sequential>
			<echo message="Move..." level="info"/>
			<exec executable="tf.exe" searchpath="true" dir="${env.BUILD_SOURCESDIRECTORY}" >
				<arg value="move" />
				<arg value="$/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_SOURCE_FONTES}/Patch/aplicar/${PROTHEUS_PATCHS_TYPE}/${fileerro}" />
				<arg value="$/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_SOURCE_FONTES}/Patch/aplicados/${touch.time}/${fileerro}" />
			</exec>
			
			<echo message="Checkin..." level="info"/>
			<exec executable="tf.exe" searchpath="true" dir="${env.BUILD_SOURCESDIRECTORY}" >
				<arg value="checkin" />
				<arg value="/comment:Aplicado" />
				<arg value="$/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_SOURCE_FONTES}/Patch/aplicados/${touch.time}/${fileerro}" />
				<arg value="/noprompt" />
			</exec>	

			<echo message="sleep 5 seconds" level="info"/>	
			<sleep seconds="5"/>
		
			<echo message="Desbloqueando..." level="info"/>
			<exec executable="tf.exe" searchpath="true" dir="${env.BUILD_SOURCESDIRECTORY}" >
				<arg value="lock" />
				<arg value="/lock:none" />
				<arg value="$/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_SOURCE_FONTES}/Patch/aplicados/${touch.time}/${fileerro}" />
			</exec>	
		</sequential>

		<!-- Move o patch da pre-producao/aplicar para a pre-producao/aplicados/data -->
		<echo message="Move o patch da pre-producao/aplicar para a pre-producao/aplicados/${touch.time} --" level="info"/>
		<echo message=" - $/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_PATCHS_PRE}/Patch/aplicar/${PROTHEUS_PATCHS_TYPE}/${fileerro}" level="info"/>
		<echo message=" - $/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_PATCHS_PRE}/Patch/aplicados/${touch.time}/${fileerro}" level="info"/>
		
		<sequential>
			<echo message="Move..." level="info"/>
			<exec executable="tf.exe" searchpath="true" dir="${env.BUILD_SOURCESDIRECTORY}" >
				<arg value="move" />
				<arg value="$/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_PATCHS_PRE}/Patch/aplicar/${PROTHEUS_PATCHS_TYPE}/${fileerro}" />
				<arg value="$/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_PATCHS_PRE}/Patch/aplicados/${touch.time}/${fileerro}" />
			</exec>
			
			<echo message="Checkin..." level="info"/>
			<exec executable="tf.exe" searchpath="true" dir="${env.BUILD_SOURCESDIRECTORY}" >
				<arg value="checkin" />
				<arg value="/comment:Aplicado" />
				<arg value="$/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_PATCHS_PRE}/Patch/aplicados/${touch.time}/${fileerro}" />
				<arg value="/noprompt" />
			</exec>	

			<echo message="sleep 5 seconds" level="info"/>	
			<sleep seconds="5"/>
		
			<echo message="Desbloqueando..." level="info"/>
			<exec executable="tf.exe" searchpath="true" dir="${env.BUILD_SOURCESDIRECTORY}" >
				<arg value="lock" />
				<arg value="/lock:none" />
				<arg value="$/${env.BUILD_SOURCEBRANCHNAME}/${env.PROTHEUS_PATCHS_PRE}/Patch/aplicados/${touch.time}/${fileerro}" />
			</exec>	
		</sequential>					

	</target>

</project>