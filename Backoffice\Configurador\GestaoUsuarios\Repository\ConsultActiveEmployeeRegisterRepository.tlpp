#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Repository.ConsultActiveEmployeeRegisterRepository


/*/{Protheus.doc} ConsultActiveEmployeeRegister
Class that has the purpose of consult Table RD0 for the Active employee register
@type class
@version 1.0 
<AUTHOR>
@since 10/24/2024
/*/
Class   ConsultActiveEmployeeRegister
	Static  Method SearchOnTableRd0(oJsonRd0 As  Json, nTypeOfOperation  As   Numeric, cLogin   As  Character)
EndClass



/*/{Protheus.doc} ConsultActiveEmployeeRegister::SearchOnTableRd0
Search on Table RDO using the CPF from JSON that was send by Ipaas
@type method
@version 1.0  
<AUTHOR>
@since 10/24/2024
@param oJsonRd0, Json, Object Json with the information about the data of the employee 
@return logical, Return a logical state thats informs if the employee exist on Table RD0
/*/
Method  SearchOnTableRd0(oJsonRd0   As  Json, nTypeOfOperation  As   Numeric, cLogin    As  Character)    Class   ConsultActiveEmployeeRegister
	Local   lAtivo      :=  .F.     As  Logical
	Local   cCpf        :=  oJsonRd0:GetJsonObject( "RD0_CIC" ) As  Character
    //Local   cLogin      :=  oJsonRd0:GetJsonObject( "RD0_LOGIN" ) As  Character
    Local   lExistence  :=  .F. As  Logical
    Local   cCodRD0     :=  ''  As  Character
    Do Case
        Case    nTypeOfOperation == 3   //criacao
            RD0->( dbSetOrder( 6 ) )
            If RD0->( dbSeek( xFilial( "RD0" ) + cCpf ) )
                lExistence  :=  .T.
                While RD0->( ! Eof() ) .and. RD0->( RD0_FILIAL + RD0_CIC ) == xFilial( "RD0" ) + cCpf .and. ! lAtivo
                    If RD0->RD0_MSBLQL != "1"
                        lAtivo := .T.
                    Else
                        lAtivo := .F.
                    EndIf

                    If ! lAtivo
                        RecLock( "RD0", .F. )
                        RD0->RD0_XCCPF := RD0->RD0_CIC
                        RD0->RD0_CIC   := ""
                        RD0->( MsUnLock() )
                    Endif
                    RD0->( dbSkip() )
                End
            Endif
        Case    nTypeOfOperation == 4   //alteracao
            RD0->( dbSetOrder( 10 ) )
            If RD0->( dbSeek( xFilial( "RD0" ) + PadR( cLogin, TamSX3( "RD0_LOGIN" )[1] ) ) )
                lExistence  :=  .T.
                While RD0->( ! Eof() ) .and. RD0->( RD0_FILIAL + RD0_LOGIN ) == xFilial( "RD0" ) + PadR( cLogin, TamSX3( "RD0_LOGIN" )[1] )
                    RD0->( dbSkip() )
                End

                RD0->( dbSkip( -1 ) )
                cCodRD0 := RD0->RD0_CODIGO
            EndIf
            
    EndCase

Return  {lExistence,lAtivo,cCodRD0}
