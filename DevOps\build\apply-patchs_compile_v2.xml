<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project basedir="." default="compile">
	<import file="move_patch.xml" />

	<tstamp />

	<!-- car<PERSON><PERSON> as variaveis de ambiente -->
	<property environment="env" />
	<property name="output" 	value="${env.PROTHEUS_SOURCESDIRECTORY}\devops\build\output" />
	<property name="src" 		value="${env.PROTHEUS_SOURCESDIRECTORY}" />
	<property name="fail" 		value="${env.ERRO_PIPELINE}" />
	<if>
		<equals arg1="${PROTHEUS_PATCHS_TYPE}" arg2="applyoldprogram"/>
		<then>
			<property name="cmdPatchType" 		value="-applyoldprogram" />
		</then>
		<else>
			<property name="cmdPatchType" 		value="" />
		</else>
	</if>

	<!--	Dependencias 	-->
	<taskdef resource="net/sf/antcontrib/antcontrib.properties">
		<classpath>
			<pathelement location="${env.ANT_HOME}/lib/ant-contrib-0.3.jar"/>
		</classpath>
	</taskdef>
	<taskdef resource="net/sf/antcontrib/antlib.xml" >
		<classpath>
			<pathelement location="${env.ANT_HOME}\lib\ant-contrib-1.0b3.jar"/>
		</classpath>
	</taskdef>	

	<!-- Cria diretório de log devops  -->
	<mkdir dir="${output}" />

	<!-- Target de preparação para compilação dos patchs  -->
	<target name="compile" description="Aplica patch">

		<!-- extensões reconhecidas pelo compilador AdvPL  -->
		<patternset id="sources.Patchs">
			<include name="**/*.PTM" />
			<include name="**/*.UPD" />
			<include name="**/*.PAK" />
		</patternset>

		<echo message="Dados da compilação:" level="info"/>
		<echo message="- Ambiente...........: ${env.COMPILE_ENVIRONMENT}" level="info"/>
		<echo message="- Nome da branch.....: ${env.PROTHEUS_SOURCEBRANCH}" level="info"/>
		<echo message="- Local dos patchs...: ${src}\Patch\aplicar\${PROTHEUS_PATCHS_TYPE}\" level="info"/>
		<echo message="- CMD a executar.....: ${env.APPSERVER_PATH}\${env.APPSERVER_APP} -compile -applypatch -files=/caminho_absoluto_dos_patchs/ env=${env.COMPILE_ENVIRONMENT} ${cmdPatchType}" level="info"/>

		<if>
			<available file="${src}\Patch\aplicar\${PROTHEUS_PATCHS_TYPE}\" type="dir"/>
			<then>
				<sequential>
				<for param="files" >
					<path>
						<fileset dir="${src}\Patch\aplicar\${PROTHEUS_PATCHS_TYPE}\" casesensitive="false">
							<patternset refid="sources.Patchs" />
						</fileset>				
					</path>
					<sequential>
						<var name="patches" unset="true" />
						<basename property="patches" file="@{files}"/>

						<antcall target="compila"> 
							<param name="patch" value="@{files}"/> 
						</antcall>

					</sequential>
				</for>
				</sequential>
			</then>
			<else>
				<echo message="- Nao existe patchs para aplicar." level="info" />
			</else>
		</if>

	</target>
	
	<!-- Aplica o patch de forma individualmente  -->
	<target name="compila" description="compila o patch no ambiente informado e trata possiveis erros de compilação">
		
		<!-- Aplica o patch informado  -->
		<echo message="Aplicando -> ${env.APPSERVER_PATH}\${env.APPSERVER_APP} -compile -applypatch -files=${patch} -env=${env.COMPILE_ENVIRONMENT} ${cmdPatchType}" level="info" />
		<exec executable="${env.APPSERVER_PATH}\${env.APPSERVER_APP} " output="${output}\applypatchlog.log">
			<arg value="-compile" />
			<arg value="-applypatch" />
			<arg value="-files=${patch}" />
			<arg value="-env=${env.COMPILE_ENVIRONMENT}" />
			<arg value="${cmdPatchType}" />
		</exec>
		<loadfile property="APPSERVERapplypatchloglog" srcfile="${output}\applypatchlog.log" />
		<echo message="${APPSERVERapplypatchloglog}" level="info" />

		<!-- Procura por registro de erros no log de compilação  -->
		<if>
			<available file="${output}\applypatchlog.log"/>
			<then>

				<!-- Obtem o nome do arquivo patch dentro da string que contem o caminho absoluto do patch  -->
				<antcallback target="substr" return="substrReturn"> 
					<param name="textOriginal" value="${patch}"/> 
					<param name="textARemover" value="${src}\Patch\aplicar\${PROTHEUS_PATCHS_TYPE}\"/> 
					<param name="textAInserir" value=""/> 
				</antcallback>
				<property name="ferro" 	value="${substrReturn}" />		

				<loadfile property="APPSERVERCompileApplyPatch" srcfile="${output}\applypatchlog.log" />
				<echo message=":: Analisando erros no log" level="info"/>
				<verificaErro pattern="\[ERROR\]\[SERVER\]" fileerro="${ferro}" filepatch="${patch}" />

			</then>
		</if>
	</target>

	<!-- Verificação de erro e disparo de aviso na sala google meet -->
	<macrodef name="verificaErro">
		<attribute name="pattern"/>
		<attribute name="fileerro"/>
		<attribute name="filepatch"/>
		
		<sequential>

			<echo message=":: Analisando erro: @{pattern}" level="info"/>
			<echo message=":: Analisando erro: @{fileerro}" level="info"/>
			
			<!-- Cria a propriedade 'erro.exists' se encontrar 'pattern' no arquivo e log informado -->
			<loadfile property="erro.exists" srcfile="${output}\applypatchlog.log">
				<filterchain>
					<linecontainsregexp>
						<regexp pattern="@{pattern}" />
					</linecontainsregexp>
				</filterchain>
			</loadfile>

			<!-- Se propriedade  'erro.exists' existe, cria arquivo com a parte do erro e realiza o processo de aviso e quebra de pipeline -->
			<if>
				<isset property="erro.exists" />
				<then>
					<echo file="${output}\applypatchlog_erro.log" message="${erro.exists}"/>	

					<if>
						<available file="${output}\applypatchlog_erro.log" />
						<then>
							
							<echo message="Envio de erro na sala google meet" level="info"/>
								<exec executable="powershell" >
									<arg value="${env.PROTHEUS_SOURCESDIRECTORY}\devops\build\msgGoogleChatv01.ps1 " />
									<arg value="-pathText ${output}\applypatchlog_erro.log " />
									<arg value="-title Compilação:${env.PROTHEUS_SOURCEBRANCH} " />
									<arg value="-subtitle Patchs:${env.PROTHEUS_REPOSITORY.NAME} " />
									<arg value="-typeMessage E " />
								</exec>
							<if>
								<equals arg1="${fail}" arg2="true"/>
								<then>
									<echo message="Erros encontrados, pipeline interrompida devido erro acima" level="info"/>
									<fail if="erro.exists" message="${erro.exists}" />
								</then>
							</if>

						</then>
						<!--
						<else>
							<echo message="Else: 143" level="info"/>
						</else>						
						-->
					</if>	
				</then>
				<else>
					<!-- Move patch aplicados com sucesso em builds das releases -->
					<if>
						<or>
							<equals arg1="${env.PROTHEUS_SOURCE_FONTES}" arg2="release"/>
							<equals arg1="${env.PROTHEUS_SOURCE_FONTES}" arg2="release_mi"/>
						</or>
						<then>
							<antcall target="move_patch"> 
								<param name="fileerro" value="@{fileerro}"/> 
							</antcall>						
						</then>
					</if>
				</else>					
			</if>
		</sequential>
	</macrodef>

</project>