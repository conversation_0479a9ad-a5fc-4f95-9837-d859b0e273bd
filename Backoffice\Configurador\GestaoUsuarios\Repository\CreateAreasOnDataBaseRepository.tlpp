#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Repository.CreateAreasOnDataBaseRepository

/*/{Protheus.doc} CreateAreasOnDataBase
Class that create areas on Tables RDO, RDZ, SQ3
@type class
@version  1.0
<AUTHOR>
@since 10/25/2024
/*/
Class   CreateAreasOnDataBase
  Private   Data  aAreaAtu  As  Array
  Private   Data  aAreaRD0  As  Array
  Private   Data  aAreaRDZ  As  Array
  Private   Data  aAreaSQ3  As  Array 

  Public    Method  new()   Constructor
  Public    Method  destroy() 
EndClass

/*/{Protheus.doc} CreateAreasOnDataBase::new
Method that create the areas
@type method
@version  1.0
<AUTHOR>
@since 10/25/2024
@return Object, Return a Nil Value
/*/
Method  new()   Class   CreateAreasOnDataBase
  ::aAreaAtu	:= GetArea()
  ::aAreaRD0	:= RD0->( GetArea() )
  ::aAreaRDZ	:= RDZ->( GetArea() )
  ::aAreaSQ3	:= SQ3->( GetArea() )
Return Self

/*/{Protheus.doc} CreateAreasOnDataBase::destroy
Method that apply an RestArea
@type method
@version  1.0
<AUTHOR>
@since 10/25/2024
@return variant, Return a Nil Value
/*/
Method  destroy()   Class   CreateAreasOnDataBase
  RestArea( ::aAreaSQ3 )
  RestArea( ::aAreaRD0 )
  RestArea( ::aAreaRDZ )
  RestArea( ::aAreaAtu )
Return Nil





