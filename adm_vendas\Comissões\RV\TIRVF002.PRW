#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVF002     
Callback de autenticacao PSA ACTIO
@type  Function
<AUTHOR> Menabue Lima
@since 27/10/2023
@version 1.0
	/*/
User Function TIRVF002(cRespBody)
	Local oReceptor:=TIINTERCEPTOR():New()
	Local cToken   :=""
	Local cExpire  := ""
	Local cTypeTk  :=""
	Local cHeader  :=""
	cTypeTk:= oReceptor:SearchJsonKey(cRespBody,"token_type")
	cToken := oReceptor:SearchJsonKey(cRespBody,"access_token")
	cExpire:= oReceptor:SearchJsonKey(cRespBody,"expires_on")
	cHeader:= 'Authorization: '+cTypeTk+" " + cToken
	Reclock("P36",.F.)
	P36->P36_HEADRE :=cHeader
	P36->P36_EXPIRE := strtran(strtran(cExpire,":")," ") //Grava a data+time de validade do token
	P36->(MsUnlock())

	FreeObj(oReceptor)
Return
