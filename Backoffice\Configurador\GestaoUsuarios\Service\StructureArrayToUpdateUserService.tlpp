#include "tlpp-core.th"


namespace   Backoffice.Configurador.Gestao_Usuarios.Service.StructureArrayToUpdateUserService

using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.CollectionOfParamUtils

/*/{Protheus.doc} StructureArrayToUpdateUser
This aims to create the array to modify an user.
@type class
@version  1.0
<AUTHOR>
@since 11/27/2024
/*/
Class   StructureArrayToUpdateUser
    Static  Method  constructToSysUsr(oJsonUSR  As  Json,   oJsonSSI    As  Json) As  Array
EndClass


/*/{Protheus.doc} StructureArrayToUpdateUser::constructToSysUsr
This method aims to construct the array using
the Json of SSI and USR.
@type method
@version  1.0
<AUTHOR>
@since 11/27/2024
@param oJsonUSR, json, The Json with SYS_USR Data
@param oJsonSSI, json, The Json with SSI Data
@return array, Return any array with the necessary structure to modify the user
/*/
Method  constructToSysUsr(oJsonUSR  As  Json,   oJsonSSI    As  Json) As  Array   Class   StructureArrayToUpdateUser
    Local   aDados  :=  {}  As  Array
    Local   oParamStore     As  Object
    Local   lSSOObr         As  Logical

    oParamStore :=  CollectionOfParam():new()

    lSSOObr :=  oParamStore:getUseSso()

    If oJsonUSR <> Nil
        aAdd( aDados, { "USR_CODIGO" ,  Upper( oJsonUSR:GetJsonObject( "USR_CODIGO" ) )                     } )
        aAdd( aDados, { "USR_NOME"   ,  SubStr(Upper( oJsonUSR:GetJsonObject( "USR_NOME" ) ),0,40)          } )
        aAdd( aDados, { "USR_EMAIL"  ,  Lower( oJsonUSR:GetJsonObject( "USR_EMAIL" ) )                      } )
        aAdd( aDados, { "USR_CARGO"  ,  oJsonUSR:GetJsonObject( "USR_CARGO" )                               } )
    EndIf


    If lSSOObr  .And. oJsonSSI <> Nil
        aAdd( aDados, { "USR_SO_DOMINIO"   ,  Upper( oJsonSSI:GetJsonObject( "USR_SO_DOMINIO" ) )           } )
        aAdd( aDados, { "USR_SO_USERLOGIN" ,  Lower( oJsonSSI:GetJsonObject( "USR_SO_USERLOGIN" ) )         } )   
    EndIf
Return  aDados
