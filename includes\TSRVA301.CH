#ifdef SPANISH
	#define STR0001 "Tarefa posicionada"
	#define STR0002 "Todas Tarefas do Projeto"
	#define STR0003 "Intervalo de Tarefas"
	#define STR0004 "Fim-no-Inicio"
	#define STR0005 "Inicio-no-Inicio"
	#define STR0006 "Fim-no-Fim"
	#define STR0007 "Inicio-no-Fim"
	#define STR0008 "Acessos do Usuario"
	#define STR0009 "Usuario sem acesso a alterar a estrutura do projeto"
	#define STR0010 "Predecessoras"
	#define STR0011 "Tipo"
	#define STR0012 "Retardo"
	#define STR0013 "Aplicar Para"
	#define STR0014 "Tarefa De"
	#define STR0015 "Tarefa Ate"
	#define STR0016 "Alocacao de recurso so pode ser realizado para uma Tarefa. "
	#define STR0017 "A operacao nao sera realizada."
	#define STR0018 "A operacao selecionada sera executada para todas as tarefas do projeto. Confirma?"
	#define STR0019 "Atencao"
	#define STR0020 "Operacao cancelada"
	#define STR0021 "Tarefa Inicial"
	#define STR0022 "Tarefa inicial deve ser informada. "
	#define STR0023 "Tarefa Final"
	#define STR0024 "Tarefa final deve ser informada. "
	#define STR0025 "Tarefa inicial nao cadastrada. "
	#define STR0026 "Tarefa final nao cadastrada. "
	#define STR0027 "Processando Tarefa: "
#else
	#ifdef ENGLISH
		#define STR0001 "Tarefa posicionada"
		#define STR0002 "Todas Tarefas do Projeto"
		#define STR0003 "Intervalo de Tarefas"
		#define STR0004 "Fim-no-Inicio"
		#define STR0005 "Inicio-no-Inicio"
		#define STR0006 "Fim-no-Fim"
		#define STR0007 "Inicio-no-Fim"
		#define STR0008 "Acessos do Usuario"
		#define STR0009 "Usuario sem acesso a alterar a estrutura do projeto"
		#define STR0010 "Predecessoras"
		#define STR0011 "Tipo"
		#define STR0012 "Retardo"
		#define STR0013 "Aplicar Para"
		#define STR0014 "Tarefa De"
		#define STR0015 "Tarefa Ate"
		#define STR0016 "Alocacao de recurso so pode ser realizado para uma Tarefa. "
		#define STR0017 "A operacao nao sera realizada."
		#define STR0018 "A operacao selecionada sera executada para todas as tarefas do projeto. Confirma?"
		#define STR0019 "Atencao"
		#define STR0020 "Operacao cancelada"
		#define STR0021 "Tarefa Inicial"
		#define STR0022 "Tarefa inicial deve ser informada. "
		#define STR0023 "Tarefa Final"
		#define STR0024 "Tarefa final deve ser informada. "
		#define STR0025 "Tarefa inicial nao cadastrada. "
		#define STR0026 "Tarefa final nao cadastrada. "
		#define STR0027 "Processando Tarefa: "
	#else
		#define STR0001 "Tarefa posicionada"
		#define STR0002 "Todas Tarefas do Projeto"
		#define STR0003 "Intervalo de Tarefas"
		#define STR0004 "Acessos do Usuario"
		#define STR0005 "Usuario sem acesso a alterar a estrutura do projeto"
		#define STR0006 "Alteracao de Calendario"
		#define STR0007 "Novo Calendario"
		#define STR0008 "Aplicar Para"
		#define STR0009 "Tarefa De"
		#define STR0010 "Tarefa Ate"
		#define STR0011 "Recurso Alocado De"
		#define STR0012 "Recurso Alocado Ate"
		#define STR0013 "Novo Calendario nao cadastrado. "
		#define STR0014 "A operacao nao sera realizada."
		#define STR0015 "Recurso De nao cadastrado. "
		#define STR0016 "Recurso Ate nao cadastrado. "
		#define STR0017 "Troca de calendario so pode ser realizado para uma Tarefa. "
		#define STR0018 "A operacao selecionada sera executada para todas as tarefas do projeto. Confirma?"
		#define STR0019 "Atencao"
		#define STR0020 "Tarefa Inicial"
		#define STR0021 "Tarefa inicial deve ser informada. "
		#define STR0022 "Tarefa Final"
		#define STR0023 "Tarefa final deve ser informada. "
		#define STR0024 "Tarefa inicial nao cadastrada. "
		#define STR0025 "Tarefa final nao cadastrada. "
		#define STR0026 "Atualizando Tarefas..."
		#define STR0027 "Processando Tarefa: "

	#endif
#endif