#include "protheus.ch"
#include "apwebsrv.ch"
#include "apwebex.ch"

#define seed 4397

WsStruct WsHLArray
	WsData HardLock AS Array Of WsHardLock
EndWsStruct

WsStruct WsHardLock
	WsData ID		AS String
	WsData License	AS Array Of WsLicense
EndWsStruct

WsStruct WsLicense
	WsData Description	AS String
	WsData License		AS Integer
	WsData Code			AS Integer
EndWsStruct

WsStruct WsHLList
	WsData License	AS Array Of WsLicense
	WsData HardLock	AS Array Of WsHardLock
EndWsStruct

WsStruct WsHLAutArray
	WsData HardLock AS Array Of WsHLAutorize
EndWsStruct

WsStruct WsHLAutorize
	WsData ApplyLic	AS BASE64BINARY
	WsData ID		AS String
	WsData HLDate	AS Date
	WsData Checksum	AS Integer
	WsData License	AS Array Of WsLicense
EndWsStruct

WsService GetHardLock

	WsData stInfoCli	As String
	WsData Version		As String
	WsData HardLock		As WsHLArray
	WsData ID			As String

	WsData ListReturn		As WsHLList
	WsData AutorizeReturn	As WsHLAutArray
	WsData AutHLReturn		As WsHLAutorize
	
	WsMethod List
	WsMethod Autorize
	WsMethod AutorizeHL
	
EndWsService                           

WsMethod List WsReceive stInfoCli WsSend ListReturn WsService GetHardLock
Local oHL
Local oLicense
Local cMsg := ""
Local aNumLic
Local aNumLight
Local aSayLic
Local aSayLight
Local aGetLic         	
Local aGetLight
Local ni
Local nLenLic
Local nLenLight
Local nPos            
DbSelectArea("ZDP")
DbSetOrder(1)
::stInfoCli := Padr(::stInfoCli,Len(ZDP->ZDP_CLIENT))
//::stInfoCli := Padr("T59504",Len(ZDP->ZDP_CLIENT))
If DbSeek(xFilial() + ::stInfoCli)
	If !U_ValVincHL(::stInfoCli,.F.,@cMsg)
		SetSoapFault("Erro",StrTran(cMsg,CRLF," "))
 		Return .F.
	EndIf
	
	If !U_ValSitCli(::stInfoCli,.F.,@cMsg)
		SetSoapFault("Erro",StrTran(cMsg,CRLF," "))
 		Return .F.
	EndIf

	aSayLic := U_Licen710()
	aSayLight := U_Light710()
	aNumLic := U_CLNumLic()
	aNumLight := U_CLNumLight()
	nLenLic := Len(aNumLic)
	nLenLight := Len(aNumLight)

	U_HLLicenses(::stInfoCli,@aGetLic,@aGetLight)
	
	For ni := 1 To nLenLic
		If aGetLic[ni] > 0
			oLicense := WsClassNew("WSLicense")
			oLicense:Description := AllTrim(Subs(aSayLic[ni],At(")",aSayLic[ni])+1))
			oLicense:License := aGetLic[ni]
			oLicense:Code := aNumLic[ni]
			Aadd(::ListReturn:License,oLicense)
		EndIf
	Next

	For ni := 1 To nLenLight
		If aGetLight[ni] > 0
			oLicense := WsClassNew("WSLicense")
			oLicense:Description := AllTrim(Subs(aSayLight[ni],At(")",aSayLight[ni])+1))
			oLicense:License := aGetLight[ni]
			oLicense:Code := aNumLight[ni]
			Aadd(::ListReturn:License,oLicense)
		EndIf
	Next    
	
	If Empty(::ListReturn:License)
		SetSoapFault("Erro","O cliente " + ::stInfoCli + "nao tem nenhuma licenca")
 		Return .F.
	EndIf

	DbSelectArea("ZDP")
	DbSetOrder(1)
	DbSeek(xFilial() + ::stInfoCli)
	While !Eof() .and. ZDP->ZDP_CLIENT == ::stInfoCli
		If !Empty(ZDP->ZDP_PROTOC) .or. !Empty(ZDP->ZDP_PROAUT)
	        oHL := WsClassNew("WsHardLock")
	        oHL:ID := ZDP->ZDP_ID
	        oHL:License := {}
	        
	        For ni := 1 To Len(::ListReturn:License)
				oLicense := WsClassNew("WSLicense")
				oLicense:Description := ::ListReturn:License[ni]:Description
				oLicense:License := 0
				oLicense:Code := ::ListReturn:License[ni]:Code
				Aadd(oHL:License,oLicense)
	        Next
	
			If !Empty(ZDP->ZDP_LICENS)
				aMemoLic := Str2Array(ZDP->ZDP_LICENS)
				aGetLic := aMemoLic[1]
				aGetLight := aMemoLic[2]
	
				For ni := 1 To Len(aGetLic)
					If aGetLic[ni] > 0
						nPos := Ascan(oHL:License,{|x| x:Code == aNumLic[ni]})
						If nPos > 0
							oHL:License[nPos]:License := aGetLic[ni]
						EndIf
					EndIf
				Next
			
				For ni := 1 To Len(aGetLight)
					If aGetLight[ni] > 0
						nPos := Ascan(oHL:License,{|x| x:Code == aNumLight[ni]})
						If nPos > 0						
							oHL:License[nPos]:License := aGetLight[ni]
						EndIf
					EndIf
				Next
			EndIf
	
	        Aadd(::ListReturn:HardLock,oHL)
		EndIf
		DbSkip()
	End
	
	If Empty(::ListReturn:HardLock)
		SetSoapFault("Erro","Nao existe HardLock que possa ser autorizado para o cliente " + ::stInfoCli)
		Return .F.
	EndIf
Else
	SetSoapFault("Erro","Nao existe HardLock cadastrado para o cliente " + ::stInfoCli)
	Return .F.
EndIf
Return .T.

WsMethod Autorize WsReceive stInfoCli,HardLock,Version WsSend AutorizeReturn WsService GetHardLock
Local nErro
Local aHASP := {}
Local aNumLic
Local aNumLight
Local aGetLic
Local aGetLight
Local nLenLic
Local nLenLight
Local ni
Local nPos
Local nValidade
Local cMsg
Local nSeek
Local aLicenses := {}
Local nj
Local aHL := ::HardLock:HardLock
Local oObj
Local nx
Local aSayLic
Local aSayLight
Local oLicense

DbSelectArea("ZDP")
DbSetOrder(1)
::stInfoCli := Padr(::stInfoCli,Len(ZDP->ZDP_CLIENT))
If DbSeek(xFilial()+::stInfoCli)
	nValidade := U_VPEGetValid(::stInfoCli,@nErro,.F.,@cMsg)
	If nErro < -1
		SetSoapFault("Erro",cMsg)
		Return .F.
	EndIf

	For nj := 1 To Len(aHL)
		If DbSeek(xFilial()+::stInfoCli+aHL[nj]:ID)
			If Empty(ZDP->ZDP_PROTOC) .and. Empty(ZDP->ZDP_PROAUT)
				SetSoapFault("Erro","O HardLock " + aHL[nj]:ID + " nao pode ser autorizado por nao ter o protocolo registrado")
				Return .F.
			EndIf
		Else
			SetSoapFault("Erro","HardLock " + aHL[nj]:ID + " nao encontrado")
			Return .F.
		EndIf
	Next

	aSayLic := U_Licen710()
	aSayLight := U_Light710()
	aNumLic := U_CLNumLic()
	aNumLight := U_CLNumLight()
	nLenLic := Len(aNumLic)
	nLenLight := Len(aNumLight)

	ni := 0
	DbSelectArea("ZDP")
	DbSetOrder(1)
	DbSeek(xFilial()+::stInfoCli)
	While !Eof() .and. ZDP->ZDP_CLIENT == ::stInfoCli
		ni++
		Aadd(aHASP,{.F.,ZDP->ZDP_ID,ZDP->ZDP_VENCTO,ZDP->ZDP_CHKSUM,Array(nLenLic),Array(nLenLight)})
		AFill(aHASP[ni][5],0)
		AFill(aHASP[ni][6],0)

		nj := Ascan(aHL,{|x| AllTrim(x:ID) == AllTrim(ZDP->ZDP_ID)})
		If nj > 0
			aHASP[ni][1] := .T.
			For nx := 1 To Len(aHL[nj]:License)
				nSeek := aHL[nj]:License[nx]:Code
				nPos := Ascan(aNumLic,nSeek)
				If nPos > 0
					aHASP[nj][5][nPos] := aHL[nj]:License[nx]:License
				Else
					nPos := Ascan(aNumLight,nSeek)
					If nPos > 0
						aHASP[nj][6][nPos] := aHL[nj]:License[nx]:License
					EndIf
				EndIf
			Next			
		Else		
			If Empty(ZDP->ZDP_LICENS)
				If aHASP[ni][1]
					aHASP[ni][5] := AClone(aGetLic)
					aHASP[ni][6] := AClone(aGetLight)
				EndIf
			Else
				aMemoLic := Str2Array(ZDP->ZDP_LICENS)
				For nj := 1 To Len(aMemoLic[1])
					aHASP[ni][5][nj] := aMemoLic[1][nj]
				Next
		
				For nj := 1 To Len(aMemoLic[2])
					aHASP[ni][6][nj] := aMemoLic[2][nj]
				Next
			EndIf
		EndIf
		DbSkip()
	End

	U_CLSetVer(Trim(::Version))
	U_HLLicenses(::stInfoCli,@aGetLic,@aGetLight)
	
	If U_CLValHasp(::stInfoCli,nValidade,aGetLic,aGetLight,aHASP,.F.,@cMsg)
		U_CLCalcHASP(MsDate(),nValidade,@aHASP)
		U_CLGrvHASP(aHASP,::stInfoCli,MsDate(),nValidade,ZDP->ZDP_SOLICI,ZDP->ZDP_CONTAT,ZDP->ZDP_OBSERV)
		
		For ni := 1 To Len(aHASP)
			If aHASP[ni][1]
				oObj := WsClassNew("WsHLAutorize")
				oObj:License := {}
			    aLicenses := {}
	
				For nj := 1 To Len(aNumLic)
					Aadd(aLicenses,{aNumLic[nj],aHASP[ni][5][nj]})
					If aHASP[ni][5][nj] > 0
						oLicense := WsClassNew("WSLicense")
						oLicense:Description := AllTrim(Subs(aSayLic[ni],At(")",aSayLic[ni])+1))
						oLicense:License := aHASP[ni][5][nj]
						oLicense:Code := aNumLic[ni]
						Aadd(oObj:License,oLicense)
					EndIf
				Next
		
				For nj := 1 To Len(aNumLight)
					If aNumLight[nj] > 0
						Aadd(aLicenses,{aNumLight[nj],aHASP[ni][6][nj]})
						If aHASP[ni][6][nj] > 0
							oLicense := WsClassNew("WSLicense")
							oLicense:Description := AllTrim(Subs(aSayLight[ni],At(")",aSayLight[ni])+1))
							oLicense:License := aHASP[ni][6][nj]
							oLicense:Code := aNumLight[ni]
							Aadd(oObj:License,oLicense)
						EndIf
					EndIf
				Next
	
				If U_SaveLicense(seed, Val(aHASP[ni][2]), aHASP[ni][3], aLicenses, aHASP[ni][4],@oObj:ApplyLic,.F.,.F.,@cMsg)
					oObj:ID := aHASP[1][2]
					oObj:HLDate := aHASP[1][3]
					oObj:Checksum := aHASP[1][4]
					Aadd(::AutorizeReturn:HardLock,oObj)
				Else
					SetSoapFault("Erro",cMsg)
					Return .T.
				EndIf
			EndIf
		Next
	Else
		SetSoapFault("Erro",cMsg)
		Return .F.
	EndIf
Else
	SetSoapFault("Erro","Nao existe HardLock cadastrado para o cliente " + ::stInfoCli)
	Return .F.
EndIf
Return .T.

WsMethod AutorizeHL WsReceive stInfoCli,ID,Version WsSend AutHLReturn WsService GetHardLock
Local nErro
Local aHASP := {}
Local aNumLic
Local aNumLight
Local aGetLic
Local aGetLight
Local nLenLic
Local nLenLight
Local ni
Local nValidade
Local cMsg
Local aLicenses := {}
Local nHdl
Local nSize
Local nj
Local oObj
Local aMemoLic
Local aSayLic
Local aSayLight
Local oLicense
Local nTotal := 0
Local aCNPJ	:= {}

DbSelectArea("ZDP")
DbSetOrder(1)                      
//::stInfoCli := "T59504"
//::ID := "**********"
::stInfoCli := Padr(::stInfoCli,Len(ZDP->ZDP_CLIENT))
::ID := Padr(::ID,Len(ZDP->ZDP_ID))
If DbSeek(xFilial()+::stInfoCli+::ID)
	If Empty(ZDP->ZDP_PROTOC) .and. Empty(ZDP->ZDP_PROAUT)
		SetSoapFault("Erro","O HardLock " + ::ID + " nao pode ser autorizado por nao ter o protocolo registrado")
		Return .F.
	EndIf

	nValidade := U_VPEGetValid(::stInfoCli,@nErro,.F.,@cMsg)
	If nErro < -1
		SetSoapFault("Erro",cMsg)
		Return .F.
	EndIf

	aSayLic := U_Licen710()
	aSayLight := U_Light710()
	aNumLic := U_CLNumLic()
	aNumLight := U_CLNumLight()
	nLenLic := Len(aNumLic)
	nLenLight := Len(aNumLight)

	ni := 0
	DbSelectArea("ZDP")
	DbSetOrder(1)
	DbSeek(xFilial()+::stInfoCli)
	While !Eof() .and. ZDP->ZDP_CLIENT == ::stInfoCli
		ni++
		Aadd(aHASP,{(::ID == ZDP->ZDP_ID),ZDP->ZDP_ID,ZDP->ZDP_VENCTO,ZDP->ZDP_CHKSUM,Array(nLenLic),Array(nLenLight),{/*CNPJ*/}})
		AFill(aHASP[ni][5],0)
		AFill(aHASP[ni][6],0)
	
		If Empty(ZDP->ZDP_LICENS)
			If aHASP[ni][1]
				aHASP[ni][5] := AClone(aGetLic)
				aHASP[ni][6] := AClone(aGetLight)
			EndIf
		Else
			aMemoLic := Str2Array(ZDP->ZDP_LICENS)
			For nj := 1 To Len(aMemoLic[1])
				aHASP[ni][5][nj] := aMemoLic[1][nj]
			Next
	
			For nj := 1 To Len(aMemoLic[2])
				aHASP[ni][6][nj] := aMemoLic[2][nj]
			Next
		EndIf
		DbSkip()
	End

	U_CLSetVer(Trim(::Version))                         
	U_HLLicenses(::stInfoCli,@aGetLic,@aGetLight,@aCNPJ)
		//Adiciona os CNPJs
	For ni := 1 To Len(aCNPJ)
		aCNPJ[ni] := AllTrim(aCNPJ[ni])
		For nj := 1 To Len(aHASP)
			Aadd(aHASP[nj][7],Val(Subs(aCNPJ[ni],1,2)))
			Aadd(aHASP[nj][7],Val(Subs(aCNPJ[ni],3,3)))
			Aadd(aHASP[nj][7],Val(Subs(aCNPJ[ni],6,3)))
			Aadd(aHASP[nj][7],Val(Subs(aCNPJ[ni],9,4)))
			Aadd(aHASP[nj][7],Val(Subs(aCNPJ[ni],13,2)))
		Next nJ
		aCNPJ[ni] := Transform(aCNPJ[ni],"@R 99.999.999/9999-99")
	Next nI

	If U_CLValHasp(::stInfoCli,nValidade,aGetLic,aGetLight,aHASP,.F.,@cMsg)
		U_CLCalcHASP(MsDate(),nValidade,@aHASP)
		U_CLGrvHASP(aHASP,::stInfoCli,MsDate(),nValidade,ZDP->ZDP_SOLICI,ZDP->ZDP_CONTAT,ZDP->ZDP_OBSERV)
		ni := Ascan(aHasp,{|x| x[1]})

		For nj := 1 To Len(aNumLic)
			Aadd(aLicenses,{aNumLic[nj],aHASP[ni][5][nj]})
			If aHASP[ni][5][nj] > 0
				oLicense := WsClassNew("WSLicense")
				oLicense:Description := AllTrim(Subs(aSayLic[ni],At(")",aSayLic[ni])+1))
				oLicense:License := aHASP[ni][5][nj]
				oLicense:Code := aNumLic[ni]
				Aadd(::AutHLReturn:License,oLicense)
				nTotal += aHASP[ni][5][nj]
			EndIf
		Next

		For nj := 1 To Len(aNumLight)
			If aNumLight[nj] > 0
				Aadd(aLicenses,{aNumLight[nj],aHASP[ni][6][nj]})
				If aHASP[ni][6][nj] > 0
					oLicense := WsClassNew("WSLicense")
					oLicense:Description := AllTrim(Subs(aSayLight[ni],At(")",aSayLight[ni])+1))
					oLicense:License := aHASP[ni][6][nj]
					oLicense:Code := aNumLight[ni]
					Aadd(::AutHLReturn:License,oLicense)
					nTotal += aHASP[ni][6][nj]
				EndIf
			EndIf    			
		Next         
		For nj := 1 To Len(aHASP[ni][7])
			Aadd(aLicenses,{2000+nj,aHASP[ni][7][nj]})
		Next    
		If nTotal > 0 
			If U_SaveLicense(seed, Val(aHASP[ni][2]), aHASP[ni][3], aLicenses, aHASP[ni][4],@::AutHLReturn:ApplyLic,.F.,.F.,@cMsg)
				::AutHLReturn:ID := aHASP[1][2]
				::AutHLReturn:HLDate := aHASP[1][3]
				::AutHLReturn:Checksum := aHASP[1][4]
			Else
				SetSoapFault("Erro",cMsg)
				Return .T.
			EndIf
		Else
			SetSoapFault("Erro","HardLock " + ::ID + " sem licenca para liberar")
			Return .F.
		EndIf
	Else
		SetSoapFault("Erro",cMsg)
		Return .F.
	EndIf
Else
	SetSoapFault("Erro","HardLock " + ::ID + " nao encontrado")
	Return .F.
EndIf
Return .T.
