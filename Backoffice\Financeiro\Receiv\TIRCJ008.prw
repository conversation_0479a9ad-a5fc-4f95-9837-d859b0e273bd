#INCLUDE 'TOTVS.CH'

/*/{Protheus.doc} TIRCJ008
JOB Calculo da porcentagem do acordo Receiv
<AUTHOR> Carneiro
@since 18/11/2021
@return nil
/*/

User Function TIRCJ008(aParam)

Local cAQ       := ""
Local cAliasP   := ""
Local lRet      := .T.
 
Local cIdAcordo := ""
Local cFilAcd   := ""

If !U_RCVENV(cEmpAnt,cFilAnt)
    lRet := .F.  
Else
    cAQ       := GetNextAlias()
    cAliasP   := GetNextAlias() 

    BeginSql Alias cAliasP
        SELECT P38.P38_FILIAL, P38.P38_NUM, P38.P38_PARCEL 
        FROM %Table:P38% P38
            WHERE P38.%NotDel%             
            AND P38.P38_ASALDO > 0
    EndSql

    While (cAliasP)->(!Eof())
        cIdAcordo := (cAliasP)->P38_NUM
        cFilAcd   := (cAliasP)->P38_FILIAL

        BeginSql Alias cAQ

            SELECT  P39A.R_E_C_N_O_ RECP39, 
                    ((P39A.P39_VALOR+P39A.P39_VLJURO+P39A.P39_VLMULT-P39A.P39_DESCON)/
                                                                    (SELECT    
                                                                        SUM(P39B.P39_VALOR+P39B.P39_VLJURO+P39B.P39_VLMULT-P39B.P39_DESCON) 
                                                                    FROM 
                                                                        %Table:P39% P39B 
                                                                    WHERE 
                                                                        P39B.P39_FILACD = %exp:cFilAcd%  
                                                                        AND P39B.P39_IDACOR = %Exp:cIdAcordo%
                                                                        AND P39B.%NotDel%)) RATTIT            
            FROM 
                %Table:P39% P39A
            WHERE
                P39A.P39_FILACD = %exp:cFilAcd%
                AND P39A.P39_VALOR > 0
                AND P39A.P39_PERCEN = 0 
                AND P39A.P39_IDACOR = %Exp:cIdAcordo%
                AND P39A.%NotDel%
        EndSql
        While (cAQ)->(!Eof())
            P39->(DBGOTO((cAQ)->RECP39))
            RecLock("P39", .F.)
            P39->P39_PERCEN := (cAQ)->RATTIT
            MsUnlock()    
            (cAQ)->(DbSkip())
        EndDo
        (cAQ)->(DBCLOSEAREA())
        (cAliasP)->(dbSkip())
    EndDo
     (cAliasP)->(DBCLOSEAREA())

EndIf
Return lRet
