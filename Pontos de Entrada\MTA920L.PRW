#Include "Protheus.ch"
/*/{Protheus.doc} MTA920L
Ponto de Entrada Gravar campos no SF3
@since 10/07/2019
@version P12
@description Gravar campos no SF3
@return Nil
/*/
User Function MTA920L()
Local cFils920		:= GetMv("TI_MV920",,"00001002600/04601000100/00001000100")

If SF3->F3_ESTADO == 'EX'
	SF3->F3_CODISS := GetAdvFVal("CE1","CE1_CTOISS",xFilial("CE1")+SF3->F3_CODISS,1,"")
EndIf

///////////////////////////////////////////////////////////////////////////////
If alltrim(SF3->F3_FILIAL) $ cFils920  //CAXIAS DO SUL e FLORIANOPOLIS
	SF3->F3_TRIBMUN := fTrazTribM(xFilial("CE1"),SF3->F3_CODISS)  //Retorna o campo CE1_TRIBMU de Caxias do Sul-RS / FLORIANOPOLIS-Feedz
EndIf
///////////////////////////////////////////////////////////////////////////////

Return Nil 
//-----------------------------------------------------------------------------

Static Function fTrazTribM(cFilCE1,cCodISS)  //Retorna o campo CE1_TRIBMU de Caxias do Sul-RS
Local aAreaCE1:=CE1->(GetArea())
Local cRet:=Space(Len(CE1->CE1_TRIBMU))

CE1->(dbSetOrder(1))  //CE1_FILIAL+CE1_CODISS+CE1_ESTISS+CE1_CNUISS+CE1_PROISS
CE1->(dbSeek(cFilCE1))  //Posiciona no CE1
While CE1->(!Eof() .and. CE1_FILIAL==cFilCE1)
	If CE1->(AllTrim(CE1_CTOISS)==AllTrim(cCodISS))
		cRet:=CE1->CE1_TRIBMU
		Exit
	EndIf
	CE1->(dbSkip())
End

CE1->(RestArea(aAreaCE1))
Return(cRet)
//-----------------------------------------------------------------------------
