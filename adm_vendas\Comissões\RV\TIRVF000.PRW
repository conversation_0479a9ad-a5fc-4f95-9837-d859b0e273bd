#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} User Function TIRVF000     
	Callback de autenticacao rv customizado
	@type  Function
	<AUTHOR> Menabue Lima
	@since 18/09/2023
	@version 1.0
	/*/
User Function TIRVF000(cRespBody)
	Local oReceptor:=TIINTERCEPTOR():New()
	Local cToken   :=""
	Local cExpire  := ""	
	cToken:= oReceptor:SearchJsonKey(cRespBody,"accessToken")
	cExpire:= oReceptor:SearchJsonKey(cRespBody,"expiration")
	Reclock("P36",.F.)
	P36->P36_HEADRE := 'Authorization: Bearer ' + cToken//+ '|Content-Type:application/json'
	P36->P36_EXPIRE := Alltrim(strtran(strtran(strtran(cExpire,":"),"-")," ")) //Grava a data+time de validade do token
	P36->(MsUnlock())
Return
