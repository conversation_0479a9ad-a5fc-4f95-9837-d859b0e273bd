#Include "RwMake.ch"
#Include "Protheus.ch"
#Include "Topconn.ch"

//-------------------------------------------------------------------
/*/{Protheus.doc} TFATA029
Rotina para acerto do ISS nas Notas Fiscais Eletronicas

<AUTHOR>
@since 26/07/2016
@version P12
/*/
//-------------------------------------------------------------------

User Function TFATA029()

While .T.
	aRet   := {Space(TAMSX3( "F2_DOC" )[1]),'UNE',0}                  
	                                                                                  
	aDados := { {1,"Nota     ",Space(TAMSX3( "F2_DOC" )[1]),      "@!","","","", (TAMSX3( "F2_DOC"   )[1]),.F.},;
	            {1,"Serie    ",'UNE'                        ,      "@!","","","", (TAMSX3( "F2_SERIE" )[1]) ,.F.},;   
				{1,"Vlr. ISS ",0                            ,"99999.99","","","",                          08 ,.F.} }
	                                                                                   
	If  !( U_PRCParams(@aRet, aDados, "Nota para acerto do ISS...", 180/*Largura*/, 125/*Altura*/) )             
		Exit
	EndIf
	
	cNota     := aRet[1]
	cSerie    := aRet[2]                                                                     
	nValor    := aRet[3]                                      
	
	dbSelectArea("SF2")
	dbSetOrder(1)
	dbSeek(xFilial("SF2")+cNota+cSerie)   // F2_FILIAL+F2_DOC+F2_SERIE+F2_CLIENTE+F2_LOJA+F2_FORMUL
	
	If Eof()
		MsgStop('Nota fiscal nao encontrada')
		Loop
	Endif
	
	nDif := SF2->F2_VALISS - nValor
	
	If Abs(nDif) > 0.05
		MsgStop('Diferenca maior que 5 centavos. Verifique valor digitado.')
		Loop
	Endif
	
	If nDif == 0
		MsgStop('Nao ha diferenca. Verifique valor digitado.')
		Loop
	Endif
	
	MsgStop('ISS Digitado: '+Alltrim(Str(nValor))+' ISS Nota: '+Alltrim(Str(SF2->F2_VALISS))+' Diferenca: '+Alltrim(Str(nDif)))
	
	dbSelectArea("SF2")
	If ! Eof()
		RecLock("SF2",.F.)
		SF2->F2_VALISS := nValor
		MsUnlock()
	Else
		MsgStop('Nao encontrado SF2 para esta nota')
	Endif
	
	dbSelectArea("SD2")
	dbSetOrder(3)
	dbSeek(xFilial("SD2")+cNota+cSerie)   // D2_FILIAL+D2_DOC+D2_SERIE+D2_CLIENTE+D2_LOJA+D2_COD+D2_ITEM
	
	cCliente := SD2->D2_CLIENTE
	cLoja    := SD2->D2_LOJA
	cItem    := SD2->D2_ITEM
	cProd	  := SD2->D2_COD
	
	If ! Eof()
		RecLock("SD2",.F.)
		If SD2->D2_VALICM > 0
			SD2->D2_VALICM := SD2->D2_VALICM - nDif
		Endif
		If SD2->D2_VALISS > 0
			SD2->D2_VALISS := SD2->D2_VALISS - nDif
		Endif
		MsUnlock()
	Else
		MsgStop('Nao encontrado SD2 para esta nota')
	Endif
	
	dbSelectArea("SF3")
	dbSetOrder(6)
	dbSeek(xFilial("SF3")+cNota+cSerie)   // F3_FILIAL+F3_NFISCAL+F3_SERIE
	
	If ! Eof()
		RecLock("SF3",.F.)
		SF3->F3_VALICM := nValor
		MsUnlock()
	Else
		MsgStop('Nao encontrado SF3 para esta nota')
	Endif
	
	dbSelectArea("SFT")
	dbSetOrder(1)
	dbSeek(xFilial("SFT")+"S"+cSerie+cNota+cCliente+cLoja+cItem)   // FT_FILIAL+FT_TIPOMOV+FT_SERIE+FT_NFISCAL+FT_CLIEFOR+FT_LOJA+FT_ITEM+FT_PRODUTO
	
	If ! Eof()
		RecLock("SFT",.F.)
		SFT->FT_VALICM := SFT->FT_VALICM - nDif
		MsUnlock()
	Else
		MsgStop('Nao encontrado SFT para esta nota')
	Endif
	
	dbSelectArea("CD2")
	dbSetOrder(1)
	If dbSeek(xFilial("CD2")+"S"+cSerie+cNota+cCliente+cLoja+cItem+"  "+cProd+"ISS")
		RecLock("CD2",.F.)
		CD2->CD2_VLTRIB := CD2->CD2_VLTRIB - nDif		
		MsUnlock()
	EndIf
	
	
	
	MsgStop('Alteracao realizada !!!')
Enddo

Return