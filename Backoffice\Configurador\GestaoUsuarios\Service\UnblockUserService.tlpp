#include "tlpp-core.th"

namespace Backoffice.Configurador.GestaoUsuarios.Service.UnblockUserService

using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.ExtractValueArrayUtils
using namespace Backoffice.Configurador.Gestao_Usuarios.Repository.WriteOnRd0ToBlockEmployeeRepository
using namespace Backoffice.Configurador.Gestao_Usuarios.Service.ProtheusUserRegisterService
using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.ConstructEnvironmentUtils
using namespace Backoffice.Configurador.Gestao_Usuarios.Service.StructureArrayToBlockUserServiceService
using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.CheckPasswordWithSeekUtils
using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.MessageToResponseUserProcessUtils
using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.ValidateTheFormatOfJsonUtils
using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.SetValueToReturnArrayUtils
using namespace   Backoffice.Configurador.Gestao_Usuarios.Utils.ExtractArrayErrorFromModelObjectUtils
using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.TransformCharacterToJsonObjectUtils

/*/{Protheus.doc} UnblockUser
This Class aims to process a block or unblock of a user on Protheus
@type class
@version  1.0
<AUTHOR>
@since 12/11/2024
/*/
Class   UnblockUser
    Public  Data    aRetProc                                            As  Array
    Private Data    cJsonString                                         As  Character
	Private Data    oJson                                               As  Json
	Private Data    oBody                                               As  Json
    Private Data    aDados                                              As  Array
    Private Data    cUsrCode                                            As  Character   //USR_COD..nome.sobrenome
    Private Data    oUser                                               As  Object
    Private Data    oJsonUsr                                            As  Json

    Public  Method  new()                                               As  Object
    Public  Method  setJsonCharacter(cIncomeJson    As  Character)      As  Object
    Public  Method  executeProcess()                                    As  Object
    Public  Method  setValTest(lTest As  Logical)                       As  Object
    Public  Method  createTextResponse()                                As  Character                  
    Private Method  stepsOnModel()                                      As  Object
    Private Method  setCodUserUsrCod()                                  As  Object
    Private Method  setDataOnModel()                                    As  Object
    Private Method  validateJson()                                      As  Logical
EndClass


/*/{Protheus.doc} UnblockUser::new
This method creates a new instance of the object
@type method
@version  1.0
<AUTHOR>
@since 12/11/2024
@return object, The object itself
/*/
Method  new()   As  Object   Class   UnblockUser
    ::aRetProc      :=  { .T., "" }
    ::cJsonString   :=  ''
    ::oJson         :=  JsonObject():new()
    ::oUser  	    :=	FWLoadModel( "MPUSERACCOUNTDATA" )
    ::oJsonUsr      :=  JsonObject():new()
Return Self


/*/{Protheus.doc} UnblockUser::setJsonCharacter
This method aims to set the Json Character on the class property ::cJsonString
@type method
@version  1.0
<AUTHOR>
@since 12/11/2024
@param cIncomeJson, character, The income Json from external system with all 
@return object, The object itself
/*/
Method  setJsonCharacter(cIncomeJson    As  Character)  As  Object   Class   UnblockUser
    ::cJsonString   :=  cIncomeJson
Return Self



/*/{Protheus.doc} UnblockUser::setDataOnModel
This method aims to set the Data Block when it exists on User Model
@type method
@version  2.0
<AUTHOR>
@since 12/11/2024
@return object, The object iself
/*/
Method  setDataOnModel() As  Object  Class   UnblockUser
    ::oUser:SetValue( "DATAUSER","USR_MSBLQL", "2") 
Return self


/*/{Protheus.doc} UnblockUser::stepsOnModel
This method aims to coordinate the process of block/unblock the user
@type method
@version  1.0
<AUTHOR>
@since 12/11/2024
@return object, The object itself
/*/
Method  stepsOnModel()   As  Object  Class   UnblockUser
    Local   aUsers  :=  {}  As  Array
    
    PSWOrder( 2 )
    If PSWSeek(::cUsrCode)
        aUsers := FWSFLoadUser(::cUsrCode)
        ::oUser:SetOperation(4)
        ::oUser:Cargo   :=    {aUsers[1],aUsers[2],.T.}
        ::oUser:Activate()
        ::setDataOnModel()
        ::oUser:GetModel("PROTHEUSMENU"):DiffuseVoid()
        If ::oUser:VldData() 
            ::oUser:CommitData() 
            ::aRetProc      :=  { .T., "" }
        Else
            ::aRetProc      :=  ExtractArrayErrorFromModelObject():extract(::oUser)
        EndIf
    Else    
        ::aRetProc      :=  { .F., "User not found to perform unblocking on USR" }
    EndIf
    ::oUser:DeActivate()
Return  Self


/*/{Protheus.doc} UnblockUser::setCodUserUsrCod
This method aims to set the USR_CODIGO from income data on the property cUsrCode from the class
@type method
@version  1.0
<AUTHOR>
@since 12/11/2024
@return object, The object itself
/*/
Method  setCodUserUsrCod()    As  Object  Class   UnblockUser
    ::cUsrCode      :=  Upper( ::oJsonUsr:GetJsonObject( "USR_CODIGO" ) )
Return  Self


/*/{Protheus.doc} UnblockUser::validateJson
This method aims to validate the format of Json and set the necessary objects
@type method
@version  1.0
<AUTHOR>
@since 12/11/2024
@return logical, The logical value that indicates success or failure on validation 
/*/
Method  validateJson()  As  Logical Class   UnblockUser
    Local   oBody   As  Json
    Local   lValidation :=  .F. As  Logical
    
    oBody     := ::oJson:FromJson(::cJsonString)
    If ValType(oBody) == "U"
        lValidation  := .T.
    Else
        lValidation  := .F.
    EndIf
Return  lValidation



Method  createTextResponse() As  Character  Class   UnblockUser
    Local   cJsonResponse   :=  ''  As  Character
    Local   cEncodeReponse  :=  ''  As  Character
    Local   oResponse       :=  JsonObject():new()

    oResponse['return_code']:=  '1'
    
    If  ::aRetProc[1]
        oResponse['return_msg'] :=  "User " + ::cUsrCode +" unlocked."
        oResponse['user_unlocked'] :=  '1'
    Else
        oResponse['return_msg'] :=  ::aRetProc[2]
        oResponse['user_unlocked'] :=  '0'
    EndIf
    cJsonResponse   :=  oResponse:toJson()
    cEncodeReponse  :=  EncodeUtf8(cJsonResponse)
Return cEncodeReponse






/*/{Protheus.doc} UnblockUser::executeProcess
This method aims to coordinate the others method from the class to block/unblock the user. This is the main method
@type method
@version  1.0
<AUTHOR>
@since 12/11/2024
@return object, The object itself
/*/
Method executeProcess() As  Object  Class   UnblockUser
    ConstructEnvironment():initEnvironment()
    If  ::validateJson()
        ::oJsonUsr  := TransformCharacterToJsonObject():exec(::cJsonString, "USR")
        ::setCodUserUsrCod()
        Begin Transaction
            ::stepsOnModel()
            If !::aRetProc[1]
                DisarmTransaction()
            EndIf
        End Transaction
    Else
        ::aRetProc      :=  { .F., "Invalid Json Object" }
    EndIf
Return  Self
