<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project basedir="." name="TDSAntTasksProtheus">

	<!--
	**************************************************************************
	Exemplo de arquivo Apache Ant para compilação.
	-->

	<!-- carrega as variaveis de ambiente -->
	<property environment="env" />

	<!-- ================================= 
          extensões reconhecidas pelo compilador AdvPL              
         ================================= -->
	<patternset id="sources.advPL">
		<include name="**/*.PRW" />
		<include name="**/*.PRG" />
		<include name="**/*.PRX" />
		<include name="**/*.APH" />
		<include name="**/*.AHU" />
		<include name="**/*.APL" />
		<include name="**/*.APW" />
		<include name="**/*.PPP" />
		<include name="**/*.NGC" />
		<include name="**/*.NGCO" />
		<include name="**/*.JPEG" />
		<include name="**/*.JPG" />
		<include name="**/*.PNG" />
		<include name="**/*.BMP" />
		<include name="**/*.GIF" />
		<!--<include name="**/*.TRES" />-->
	</patternset>

	<!-- = = = = = = = = = = = = = = = = =
          target: TDScompile          
         = = = = = = = = = = = = = = = = = -->
	<target name="TDScompile">
		<sequential>
			<echo message="Compilando ambiente [${environment}]" />
			<echo message="branch path ${env.BUILD_SOURCEBRANCH}" />
			<echo message="branch path name ${env.BUILD_SOURCEBRANCHNAME}" />
			<echo message="TDS home path ${env.TDS_HOME}" />
			<!--<echo message="Authorization path ${env.AUTORIZA_PATH}" />-->
			<echo message="Default include path ${env.INCLUDE_PATH}" />
			<exec executable="${env.APPSERVER_PATH}\appserver.exe">
			<arg value="-compile"/>
			<arg value="-files=files.lst" />
			<arg value="-includes=${env.BUILD_SOURCESDIRECTORY}\includes" />
			<arg value="-src=${env.BUILD_SOURCESDIRECTORY}" />
			<arg value="-outreport=d:\jeferson\build\output" />
			<arg value="-env=${environment}"/>
			<!--<arg value="-authorization=${env.AUTORIZA_PATH}"/>-->
		</exec>
		</sequential>
	</target>
</project>