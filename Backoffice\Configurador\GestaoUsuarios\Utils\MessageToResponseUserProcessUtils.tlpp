#include    "tlpp-core.th"

namespace   Backoffice.Configurador.Gestao_Usuarios.Utils.MessageToResponseUserProcessUtils

/*/{Protheus.doc} MessageToResponseUserProcess
This class aims to construct the message to return to external system
@type class
@version  1.0
<AUTHOR>
@since 11/8/2024
/*/
Class   MessageToResponseUserProcess
    Static  Method  construct(aRetProc As  Array, cJsonString  As  Character) As  Character  
EndClass

/*/{Protheus.doc} MessageToResponseUserProcess::construct
This method aims to construct the message
@type method
@version  1.0
<AUTHOR>
@since 11/8/2024
@param aRetProc, array, The income array with the status response from the process
@param cJsonString, character, The json with the message
@return character, Return the complete message to external system
/*/
Method construct(aRetProc  As  Array, cJsonString  As  Character) As  Character  Class   MessageToResponseUserProcess
    Local cJsonRet  := ''   As Character
    cJsonRet := '{ "status": 1, '
    cJsonRet += '"data": ' + AllTrim(cJsonString) + ','
    If aRetProc[1]
        cJsonRet += '"errors": []} '
    Else
        cJsonRet += '"errors": ["' + AllTrim(aRetProc[2])  + '"]} '
    EndIf
    cJsonRet := FwNoAccent(cJsonRet )
    cJsonRet := StrTran(cJsonRet, Chr( 13 ) + Chr( 10 ), "\n")
Return cJsonRet
