#include "tlpp-core.th"


Class   CreateCheckoutService 

    Private Data    oCreateObject                           As      Object
    Private Data    cUuid                                   As      Character
    Private Data    cResponseIpaas                          As      Character
    Private Data    cJsonPayload                            As      Character
    Public  Data    dDtVld                                  As      Date

    Public Method   new()                                   As      Object
    Public Method   execute()                               As      Object
    Public Method   sendToIpaas(cJson   As  Character)      As      Object
    Public Method   setUuid(cValue  As  Character)          As      Object
    Private Method  writeOnDb()                             As      Object
    Private Method  formatPayload()                         As      Object
    Private Method  dbSeek()                                As      Object
    
EndClass


Method  new()   As  Object  Class   CreateCheckoutService
    ::oCreateObject     :=  Nil
    ::cUuid             :=  ''
    ::cResponseIpaas    :=  ''
    ::cJsonPayload      :=  ''
    ::dDtVld            := ctod("")
Return Self



Method formatPayload()      As  Object  Class   CreateCheckoutService

    Local cDtVld  := dtos( ::dDtVld )
    
    If Empty(cDtVld)
        cDtVld  := dtos(dDataBase+30)
    EndIf
    
    cDtVld := Left(cDtVld,4)+"-"+Substr(cDtVld,5,2)+"-"+Right(cDtVld,2)+"T23:59:59.879Z" 

    ::oCreateObject   :=  CreateCheckDTO():new()
    ::oCreateObject:return_page                                       :=  GetNewPar("TI_RTPGCK","www.totvs.com.br")
    ::oCreateObject:checkout_type                                     :=  GetNewPar("TI_CHKTYP","marketplace")
    ::oCreateObject:description                                       :=  GetNewPar("TI_DSCCHKT","Processo de Preenchimento do Checkout")
    ::oCreateObject:price                                             :=  cValToChar(PXG->PXG_VALOR)
    ::oCreateObject:customer['name']                                  :=  AllTrim(SA1->A1_NOME)
    ::oCreateObject:customer['document']                              :=  AllTrim(SA1->A1_CGC)         //"87.133.195/0001-26"
    ::oCreateObject:customer['email']                                 :=  AllTrim(PXG->PXG_EMAIL)
    ::oCreateObject:allowed_payment_forms[1]['method']                :=  'credit_card'
    ::oCreateObject:allowed_payment_forms[1]['installments']          :=  12
    ::oCreateObject:tokens_to_generate                                :=  StrTokArr(GetNewPar("TI_TKTOGEN","53113791000122/13021784000186"), "/")
    ::oCreateObject:valid                                             :=  cDtVld

    ::cJsonPayload      :=  ::oCreateObject:ToString()

    FreeObj(::oCreateObject)
    ::oCreateObject   :=  Nil
Return Nil


Method  dbSeek()    As      Object  Class   CreateCheckoutService
    Local   cCliente    :=  ''      As  Character
    Local   cLoja       :=  ''      As  Character

    PXG->(dbSetOrder(1))
    PXG->(dbSeek(::cUuid))

    cCliente        :=  PXG->PXG_CLIENT
    cLoja           :=  PXG->PXG_LOJA
    
    If PXG->(FieldPos("PXG_DTVLD")) > 0 
        ::dDtVld        :=  PXG->PXG_DTVLD
    EndIf

    SA1->(dbSetOrder(1))
    SA1->(dbSeek(xFilial('SA1') + cCliente + cLoja))
Return Self


Method  execute()   As  Object  Class   CreateCheckoutService
    ::dbSeek()
    ::formatPayload()
    ::sendToIpaas(::cJsonPayload)
    ::writeOnDb()
Return Self

Method  setUuid(cValue  As  Character)          As  Object  Class   CreateCheckoutService
    ::cUuid :=  cValue
Return Self

Method  sendToIpaas(cJson   As  Character)   As  Object Class   CreateCheckoutService
    //Local   cUrl          := GetMV("TI_TIPDXX5",,"https://api-ipaas.totvs.app/ipaas/api/v1/integrations/bedf3666-b94d-4893-b51c-e3ddb8b20439/api-key/34084cc2-4eb4-4e09-8774-722909561c37")
    Local   cUrl            := GetNewPar("TI_LNKIPSS",'https://api-ipaas.totvs.app/sync-hook/api/v1/integrations/b8730661-fdef-4ff7-b22d-5779d713eda0/api-key/4a9ac726-d34a-4c15-98be-4cd9a04de29e')
    Local   oIpaas          :=  Nil


    oIpaas := TIiPaaS():New()
	oIpaas:SetWebHook(cUrl)
	oIpaas:SetBodyWebHook(cJson)
	oIpaas:SendToiPaaS()

    ::cResponseIpaas  :=  oIpaas:getIpaasCharacterResponse()

    FreeObj(oIpaas)
	oIpaas := Nil 
Return  Self



Method  writeOnDb()                             As      Object      Class   CreateCheckoutService

    Local   oJsonIpaas      :=  JsonObject():new()      As  Json
    Local   cLink           :=  ''                      As  Character
    Local   cCheckoutCode   :=  ''                      As  Character

    oJsonIpaas:FromJson(::cResponseIpaas)

    cLink           :=  oJsonIpaas['result']['link']
    cCheckoutCode   :=  oJsonIpaas['result']['code'] 

    //dbSelectArea("PXG")
   
        PXG->(RecLock("PXG",.F.))
            PXG->PXG_LINK       :=  cLink
            PXG->PXG_CKCODE     :=  cCheckoutCode
            PXG->PXG_STOKEN     :=  '1'
        PXG->(MsUnLock())


Return Self
