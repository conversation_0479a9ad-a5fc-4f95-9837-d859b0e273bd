
#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWADAPTEREAI.CH"


 /*/{Protheus.doc} TIRCJ011
    Funcao Responsavel por prorrogar o vencimento real dos impostos dos titulos dos acordos
    @type  Function
    <AUTHOR> Menabue Lima
    @since 26/01/2022
    @version 12.1.33
 
    /*/
User Function TIRCJ011(lJob)
    
 

Local cAlsSE1 := GetNextAlias()
Local  cQuery := " "

Default lJob := .T.

If lJob
    RpcSetEnv('00', '00001000100')
    RpcSetType(3)
EndIf

cQuery := " "
cQuery += " SELECT DISTINCT P38_FILIAL,P38_NUM,SE1IMP.E1_NUM,SE1IMP.E1_PREFIXO,SE1IMP.E1_PARCELA,SE1IMP.E1_TIPO,SE1.E1_VENCREA TITREA ,SE1IMP.E1_VENCREA IMPREA , SE1IMP.E1_TIPO , SE1IMP.E1_SALDO,SE1IMP.R_E_C_N_O_ REC,SE1.E1_HIST TITPRIN,SE1IMP.E1_HIST TITIMP  " 
cQuery += " FROM   " + RetSQLName("P38") + " P38   " 
cQuery += " INNER JOIN " + RetSQLName("P39") + " P39 ON P38.P38_FILIAL = P39.P39_FILACD   " 
cQuery += "        AND P39_IDACOR = P38_NUM   " 
cQuery += "        AND P39.D_E_L_E_T_ = ' '   " 
cQuery += " INNER JOIN " + RetSQLName("SE1") + " SE1 ON SE1.E1_FILIAL = P39.P39_FILIAL   " 
cQuery += "        AND SE1.E1_NUM = P39.P39_NUM   " 
cQuery += "        AND SE1.E1_PREFIXO = P39.P39_PREFIX   " 
cQuery += "        AND SE1.E1_PARCELA = P39.P39_PARCEL   " 
cQuery += "        AND SE1.E1_TIPO = P39.P39_TIPO   " 
cQuery += "        AND SE1.D_E_L_E_T_ = ' '   " 
cQuery += "        AND SE1.E1_CLIENTE = P38.P38_CLIENT   " 
cQuery += " INNER JOIN " + RetSQLName("SE1") + " SE1IMP ON SE1IMP.E1_FILIAL = SE1.E1_FILIAL   " 
cQuery += "        AND SE1IMP.E1_NUM = SE1.E1_NUM   " 
cQuery += "        AND SE1IMP.E1_PREFIXO = SE1.E1_PREFIXO   " 
cQuery += "        AND SE1IMP.E1_PARCELA = SE1.E1_PARCELA   " 
cQuery += "        AND SE1IMP.E1_TIPO <> SE1.E1_TIPO AND SE1IMP.E1_VENCREA <> SE1.E1_VENCREA  " 
cQuery += "        AND SE1IMP.D_E_L_E_T_ = ' '   " 
cQuery += "        AND SE1IMP.E1_CLIENTE = SE1.E1_CLIENTE   " 
cQuery += " WHERE   P38.D_E_L_E_T_ = ' '    " 
cQuery += " ORDER BY P38_NUM   " 
cQuery := ChangeQuery(cQuery)           
dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAlsSE1,.T.,.T.)
    
    while !(cAlsSE1)->(Eof())

        DbSelectArea("SE1")
        DbGoTo((cAlsSE1)->REC )
    
        
        IF SE1->(SimpleLock()) //Reclock("SE1",.F.,,.T.,.T.) - TIFIN-22 Pierry Galdino
            SE1->E1_VENCREA := STOD((cAlsSE1)->TITREA) 
            SE1->(MsUnlock())
        ENDIF

        (cAlsSE1)->(dbSkip())
    enddo    

Return                    
