#include    "tlpp-core.th"

Class   DeleteWebHookOnCheckoutDTO
    Public  Data    userCheckout                            As  Character
    Public  Data    endpoints                               As  Array

    Public  Method  new()                                   As  Object
    Public  Method  addEndPoints(cVal   As  Character)      As  Object
    Public  Method  toString()                              As  Character

EndClass



Method  new()   As  Object   Class   DeleteWebHookOnCheckoutDTO
    ::userCheckout  :=  ''
    ::endpoints :=  {}
Return  Self


Method  addEndPoints(cVal   As  Character)  As  Object  Class   DeleteWebHookOnCheckoutDTO
    Aadd(::endpoints, cVal)
Return  Self


Method  toString()      As  Character   Class   DeleteWebHookOnCheckoutDTO
    Local cJsonString   :=  ''  As  Character
    Local   oJson   :=  JsonObject():new()

    oJson['user']       :=  ::userCheckout
    oJson['endpoints']  :=  ::endpoints

    cJsonString :=  oJson:toJson()
    FreeObj(oJson)
Return cJsonString
