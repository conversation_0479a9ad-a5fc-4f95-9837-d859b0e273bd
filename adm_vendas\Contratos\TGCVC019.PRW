#Include 'Protheus.ch'



User Function TGCVC019(cCliente)
	Local cAliCN9    	:= GetNextAlias()
	Local aPH3Cmp    	:= {}
	Local aCanc      	:= {}
	Local nMesVencto 	:= GetMV("TI_NVENCTO",,12)
	Local dDtLimVencto  := LastDay(monthsum(MsDate(), nMesVencto))


	Default cCliente := ""


	PH3->(DbSetOrder(5)) //PH3_FILIAL+PH3_CONTRA+PH3_REVISA+PH3_NUMERO+PH3_ITEM+PH3_CMPCAN+PH3_ITSEQ  
	PU4->(DbSetOrder(1)) //PU4_FILIAL+PU4_MOTBC+PU4_TIPO 
	CNB->(DbSetOrder(3)) //CNB_FILIAL+CNB_CONTRA+CNB_NUMERO+CNB_ITEM+CNB_REVISA 
	CN9->(Db<PERSON><PERSON><PERSON>rde<PERSON>(1)) //CN9_FILIAL+CN9_NUMERO+CN9_REVISA                                                                                                                                                                                                              

	QueryCN9(cCliente, cAliCN9)

	While ! (cAliCN9)->(Eof())
		aSize(aCanc, 0)
		aCanc := {}

		VerificaPH3(cAliCN9, aCanc)

		ItensVencer(cAliCN9, aCanc, dDtLimVencto)

		MontaPH3(cAliCN9, aCanc, aPH3Cmp)

		(cAliCN9)->(DbSkip())
	End

Return aPH3Cmp


Static Function QueryCN9(cCliente, cAliCN9)
	Local cQuery  := ""
	Local aParQry := {}

	cQuery := " "
	cQuery += " SELECT CNC_CLIENT, CNC_LOJACL, CNB_CONTRA, CNB_REVISA, CNB_NUMERO, CNB_ITEM, CNB_PRODUT, CNB_DESCRI" 
	cQuery += " , CNB_QUANT, CNB_VLUNIT, CNB_VLTOT, CNB_SITUAC, CNB_DTSITU, CNB_VIGFIM, CNB_UNINEG  " 
	cQuery += " FROM " + RetSQLName("CN9") + " CN9  " 
	
	cQuery += " INNER JOIN " + RetSQLName("CNC") + " CNC  " 
	cQuery += " ON CNC_FILIAL = ?   " 
	aadd(aParQry, FwxFilial("CNC")) 
	cQuery += " AND CNC_NUMERO = CN9_NUMERO  " 
	cQuery += " AND CNC_REVISA = CN9_REVISA  " 
	cQuery += " AND CNC.D_E_L_E_T_ = ?  "
	aadd(aParQry, " ") 
	cQuery += " INNER JOIN " + RetSQLName("CNB") + " CNB  " 
	cQuery += " ON CNB_FILIAL = ?   " 
	aadd(aParQry, FwxFilial("CNB")) 
	cQuery += " AND CNB_CONTRA = CN9_NUMERO  " 
	cQuery += " AND CNB_REVISA = CN9_REVISA  " 
	cQuery += " AND CNB_SITUAC IN ('A', 'P', 'S', 'M', 'G')  " 
	cQuery += " AND CNB.D_E_L_E_T_ = ?   " 
	aadd(aParQry, " ")
	cQuery += " WHERE CN9_FILIAL = ?  " 
	aadd(aParQry, FwxFilial("CN9")) 
	cQuery += " AND CN9_SITUAC = ?  " 
	aadd(aParQry,  '05' ) 
	cQuery += " AND CN9_ESPCTR = ? " 
	aadd(aParQry,  '2' ) 
	cQuery += " AND CN9_TPCTO = ?  " 
	aadd(aParQry,  '013') 
	cQuery += " AND CN9.D_E_L_E_T_ = ? " 
	aadd(aParQry, " ")
	cQuery += " AND CNC_CLIENT = ?  " 
	aadd(aParQry, cCliente)
	
	cQuery += " ORDER BY CNB_FILIAL, CNB_CONTRA, CNB_REVISA, CNB_NUMERO, CNB_ITEM   " 


	DBUseArea(.T., "TOPCONN", TCGenQry2(,,cQuery, aParQry), cAliCN9 , .F., .T. )

Return

Static Function VerificaPH3(cAliCN9, aCanc)
	Local cAmPH3  := ""
	Local cChvPH3 := FwxFilial("PH3") + (cAliCN9)->(CNB_CONTRA + CNB_REVISA + CNB_NUMERO + CNB_ITEM) //PH3_FILIAL+PH3_CONTRA+PH3_REVISA+PH3_NUMERO+PH3_ITEM+PH3_CMPCAN+PH3_ITSEQ
	Local cAmAtu  := Left(DtoS(MsDate()), 6)
	Local aReat   := {}
	Local nr      := 0
	Local nc      := 0
	Local cAmReat := ""
	Local cAmCanc := ""
	Local cCmpSol := ""
	Local nQtdRea := 0
	Local nQtdCan := 0

	PH3->(DbSeek(cChvPH3))

	Do While !PH3->(Eof()) .and. PH3->(PH3_FILIAL+PH3_CONTRA+PH3_REVISA+PH3_NUMERO+PH3_ITEM) == cChvPH3
		If PH3->PH3_STATUS	== "D" 
			PH3->(Dbskip()) 
			Loop
		EndIf	

		If PH3->PH3_STATUS	== "R" 
			cAmPH3 := CmpToAm(PH3->PH3_CMPREA)
		Else
			cAmPH3 := CmpToAm(PH3->PH3_CMPCAN)
		EndIf


		cCmpSol := CmpToAm(PH3->PH3_CMPSOL)

		If cAmPH3 < cAmAtu 
			PH3->(Dbskip()) 
			Loop
		EndIf

		If PH3->PH3_STATUS	== "R" 
			AADD(aReat, {cAmPH3, PH3->PH3_QUANT , PH3->PH3_MOTBC, cCmpSol})
		Else
			AADD(aCanc, {cAmPH3, PH3->PH3_QTDCAN, PH3->PH3_MOTBC, cCmpSol})
		EndIf
		PH3->(Dbskip()) 
	EndDo

	For nr:= 1 to Len(aReat)
		cAmReat := aReat[nr, 1]
		nQtdRea := aReat[nr, 2]
		For nc:=1 to Len(aCanc)
			If nQtdRea == 0
				Exit
			EndIf
			cAmCanc := aCanc[nc, 1]
			nQtdCan := aCanc[nc, 2]
			If nQtdRea > nQtdCan
				nQtdRea := nQtdRea - nQtdCan
				nQtdCan := 0
				aCanc[nc, 2] := nQtdCan
			Else 
				nQtdCan := nQtdCan - nQtdRea
				nQtdRea := 0
				aCanc[nc, 2] := nQtdCan	
			EndIf

		Next nc
	Next nr
	

	

Return

Static Function MontaPH3(cAliCN9, aCanc, aPH3Cmp)
	Local nc	    := 0
	Local cAmCanc   := ""
	Local cAMSol    := ""
	Local nQtdCan   := 0
	Local cMotCan   := ""
	Local cChurn    := ""
	Local aAux      := {}
	Local nChurn    := 0
	Local cDescUnid := ""


	For nc:=1 to Len(aCanc)
		If aCanc[nc, 2] == 0
			Loop
		EndIf

		cAmCanc := aCanc[nc, 1]
		nQtdCan := aCanc[nc, 2]
		cMotCan := aCanc[nc, 3]
		cAMSol  := aCanc[nc, 4]
		cChurn  := RetChurn(cMotCan)
		nChurn  := 0

		If cChurn == "S"
			nChurn := (cAliCN9)->(CNB_VLUNIT) * nQtdCan
		EndIf

		cDescUnid := FWFilialName(cEmpAnt, (cAliCN9)->(CNB_UNINEG), 2)	
		
		AADD(aAux, {"CONTRATO"  	, (cAliCN9)->(CNB_CONTRA)	})
		AADD(aAux, {"REVISAO" 		, (cAliCN9)->(CNB_REVISA)	})
		AADD(aAux, {"PLANILHA"		, (cAliCN9)->(CNB_NUMERO)	})
		AADD(aAux, {"ITEM"    		, (cAliCN9)->(CNB_ITEM  )	})
		AADD(aAux, {"PRODUTO" 		, (cAliCN9)->(CNB_PRODUT)	})
		AADD(aAux, {"DESCPROD"		, (cAliCN9)->(CNB_DESCRI)	})
		AADD(aAux, {"UNIDADE" 		, (cAliCN9)->(CNB_UNINEG)	})
		AADD(aAux, {"DESCUNIDADE"   , cDescUnid					})
		AADD(aAux, {"MRR"     		, (cAliCN9)->(CNB_VLTOT )	})
		AADD(aAux, {"CMPSOLICITA"	, AmToCMP(cAMSol)			})	
		AADD(aAux, {"CMPCANCELA"	, AmToCMP(cAmCanc)			})
		AADD(aAux, {"QTDCAN"		, nQtdCan					})
		AADD(aAux, {"MOTIVO"		, cMotCan					})
		AADD(aAux, {"CHURN"			, nChurn					})

		AADD(aPH3Cmp, aClone(aAux))
	Next

Return

Static Function ItensVencer(cAliCN9, aCanc, dDtLimVencto)
	Local nc	    := 0
	Local cAmVigF   := Left((cAliCN9)->(CNB_VIGFIM), 6)
	Local cAmCanc   := ""
	Local nQtdPrg   := 0
	Local nQtdCan   := 0
	Local cMotCan   := "232"
	Local cChurn    := "S"
	Local cAMSol    := ""

	Default  dDtLimVencto := LastDay(monthsum(MsDate(), 12))

	Begin Sequence

		If StoD((cAliCN9)->(CNB_VIGFIM)) >= dDtLimVencto .Or. Empty((cAliCN9)->(CNB_VIGFIM))
			Break
		EndIf

		For nc:=1 to Len(aCanc)
			cAmCanc := aCanc[nc, 1]
			nQtdCan := aCanc[nc, 2] 
			If cAmCanc <= cAmVigF
				nQtdPrg += nQtdCan 
			EndIf
		Next

		nQtdCan := (cAliCN9)->(CNB_QUANT) - nQtdPrg

	
		If nQtdCan <= 0
			Break
		EndIf

		cChurn := RetChurn(cMotCan)

		cAMSol := DtSolVigFim(cAliCN9)

		AADD(aCanc, {cAmVigF, nQtdCan, cMotCan, cAMSol})

	End Sequence
Return

Static Function RetChurn(cMotCan)
	Local cChurn := "S"

	If PU4->(DbSeek(FwxFilial("PU4") + cMotCan + "X"))
		If PU4->PU4_CHURN == "2"
			cChurn  := "N"
		EndIf
	EndIf
Return cChurn




Static Function DtSolVigFim(cAliCN9)
	Local cChvPH3  	:= FwxFilial("PH3") + (cAliCN9)->(CNB_CONTRA + CNB_NUMERO + CNB_ITEM) 
	Local cVigFimAt := Left((cAliCN9)->(CNB_VIGFIM), 6)
	Local cVigFimRv := ""
	Local aRevCNB   := {}
	Local ni        := 0
	Local cRevAlt   := Space(Len(CNB->CNB_REVISA))
	Local cRetDtSol := Space(6)

	If CNB->(DbSeek(cChvPH3))
		cRevAlt := CNB->CNB_REVISA
	EndIf

	While CNB->(!EOF()) .And. cChvPH3 == CNB->(CNB_FILIAL + CNB_CONTRA + CNB_NUMERO + CNB_ITEM)
		cVigFimRv := Left(DtoS(CNB->CNB_VIGFIM), 6)
		AADD(aRevCNB, {CNB->CNB_REVISA, cVigFimRv})

		CNB->(DbSkip())
	End

	ASort(aRevCNB,,, {|x, y| x[1] > y[1]})

	For ni:=1 to Len(aRevCNB)
		If cVigFimAt <> aRevCNB[ni, 2]
			cRevAlt := aRevCNB[ni, 1]
			Exit
		EndIf
	Next

	If CN9->(DbSeek(FwxFilial("CN9") + CNB->CNB_CONTRA + cRevAlt))
		cRetDtSol := Left(DtoS(CN9->CN9_DTREV), 6)
		If Empty(cRetDtSol)
			cRetDtSol := Left(DtoS(CN9->CN9_LOGDAT), 6)
		EndIf

		If Empty(cRetDtSol)
			cRetDtSol := CmpToAm(SubStr(FWLeUserlg("CN9_USERGI", 2 ), 4, 7))
		EndIf

	EndIf


Return cRetDtSol
                                                                                                         


Static Function SomaAM(cAm, nMes) 
    Local cMes := ""
    Local cAno := ""
    Local ni   := 0

    For ni:= 1 to nMes
        cMes := Right(cAM, 2)
        cAno := Left(cAM, 4)

        If cMes == "12"
            cAM := soma1(cAno) + "01"
        Else 
            cAM := soma1(cAM)
        EndIf
    Next
Return cAM



Static Function AMtoCmp(cAM)
    Local cCmp := Right(cAM, 2) + "/" + Left(cAM, 4)

Return cCmp

Static Function CmptoAM(cCmp) 
    Local cAM := Right(cCmp, 4) + Left(cCmp, 2)  

Return cAM



