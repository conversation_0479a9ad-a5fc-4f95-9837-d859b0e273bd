#INCLUDE 'TOTVS.CH'
#INCLUDE 'FWMVCDEF.CH'

/*/{Protheus.doc} TCRMG032
    (long_description)
    @type  Function
    <AUTHOR>
    @since 11/08/2022
    @version 1.0
    @param , , 
    @return nenhum
    @example
    (examples)
    @see (links_or_references)
    /*/
User Function TCRMG032()
    Local oModel	:= U_TCRMXADZ()
    Local nI        := 1
    Local aTmpParc  := {}
    Local __lProc   := GETMV( "TI_CRMG032", , .T. )
    
    If __lProc .AND. !IsInCallStack("U_TCRMX181") .and. !IsInCallStack("U_TCRMX229") .And. !IsInCallStack("U_TCRMX22B")
        oModel:GetStruct():setProperty( "ADZ_CONDPG", MODEL_FIELD_WHEN, {|| .T.})
        U_TCRMG31Z()
        For nI := 1 to oModel:Length()
            oModel:GoLine(nI)
            
            aTmpParc := Condicao(oModel:GetValue("ADZ_PRCVEN"), oModel:GetValue("ADZ_CONDPG"), 0, dDataBase)
            If Len(aTmpParc) > 1
                // Reaplica Juros em rotinas execauto chamando o gatilho manualmente 
                U_TCRMG031(.T.)
            EndIf

        Next
    EndIf
Return NIL
