#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Utils.TransformCharacterToJsonObjectUtils



/*/{Protheus.doc} TransformCharacterToJsonObject
Class that transform a Json String into Json Object extracting a specific property     
@type class
@version 1.0  
<AUTHOR>
@since 10/24/2024
/*/

Class   TransformCharacterToJsonObject
    Static  Method  exec(cJsonString    As  Character,  cPropriedade    As  Character)
EndClass

/*/{Protheus.doc} TransformCharacterToJsonObject::exec
This method transform a Json String in Json Object, only is necessary to inform the property
the property that will be extracted.
@type method
@version 1.0  
<AUTHOR>
@since 10/24/2024
@param cJsonString, character, String Json Object that will be transformed 
@param cPropriedade, character, Property that you are interest in
@return object, The return will be the object extracted from Json String
/*/
Method exec(cJsonString As Character, cPropriedade As Character)    Class   TransformCharacterToJsonObject
    Local oJson         := JsonObject():new()   As  Json
    Local oNewJsonObj   := JsonObject():new()   As  Json
    oJson:FromJson( cJsonString )
    oNewJsonObj     := oJson:GetJsonObject(cPropriedade)
Return oNewJsonObj



