#Include 'TOTVS.ch'

/*/{Protheus.doc} TSRVX505
Força integração de proposta com PSA
@type user function
<AUTHOR>
@since 04/07/2024
/*/
User Function TSRVX505()
    Local lUserPode := U_TCRME029()//Valida na ZX5 tabela weborc se usuario tem permissão para utilizar este recurso.

    dbSelectArea('AD1')
	AD1->(dbSetOrder(1)) //AD1_FILIAL+AD1_NROPOR+AD1_REVISA

    IF lUserPode .AND. AD1->(dbSeek(xFilial("AD1")+ADY->(ADY_OPORTU+ADY_REVISA)))
        ForcaIntegraPSA()
    Else
        Aviso("Nao Permitido","Usuario sem permissao para utilizar recurso.",{"Fechar"})
    ENDIF
Return

/*/{Protheus.doc} nomeStaticFunction
    Valida e integra proposta com PSA
    @type  Static Function
    <AUTHOR>
    @since 04/07/2024
/*/
Static Function ForcaIntegraPSA()
    Local lOk       := .T.
    Local aRetSaast := {}
    Local aSaasSrv  := {}
    Local aRet      := {}

    If AD1->AD1_STATUS == '9' .and. !Empty(AD1->AD1_FCS )// INTEGRACAO PSA SERVICOS
        If !U_TSRVX500(ADY->ADY_XMODAL)
            aRetSaast := U_TCRMX228(ADY->ADY_PROPOS, .F.)
            aSaasSrv  := U_TSRVX504( ADY->ADY_PROPOS, "PWF_PROPSV" )
            IF aRetSaast[1] .and. (!aSaasSrv[1] .OR. EMPTY( aSaasSrv[3] ) )
                Aviso("Nao integrou com o PSA ","Proposta com motivo de mesalizacao e ainda Nao foi vinculada a proposta de software.",{"Fechar"})
                lOk := .F.
            ElseIF !aRetSaast[1] .and. EMPTY(AD1->AD1_CODCLI)
                Aviso("Nao integrou com o PSA ","Proposta com prospect Nao pode ser integrada com o PSA.",{"Fechar"})
                lOk := .F.
            ElseIF Empty(ADY->ADY_XUNSRV) .and. Empty(ADY->ADY_XUNCOR)
                Aviso("Nao integrou com o PSA ","Proposta com unidade de servico ou servico recorrente Nao preenchida.",{"Fechar"})
                lOk := .F.
            ElseIF Empty(ADY->ADY_XCDMOT)
                Aviso("Nao integrou com o PSA ","Proposta com motivo de faturamento em branco Nao permite integracao com Dynamics.",{"Fechar"})
                lOk := .F.
            ELSEIF !aRetSaast[1]
                // VERIFICA RATEIO
                // Se a proposta possui rateio, e um dele é prospect nao continua
                if AliasInDic('AZG')
                    
                    AZG->( dbSetOrder(1))
                    AZG->(dbSeek( FWxFilial('AZG') + ADY->( ADY_PROPOS + ADY_PREVIS ) ) )
                    
                    while !AZG->( eof() ) .and. AZG->(AZG_FILIAL + AZG_PROPOS + AZG_REVISA ) == FWxFilial('AZG') + ADY->( ADY_PROPOS + ADY_PREVIS )
                        if AZG->AZG_ENTIDA == "2"
                            lOk := .F.
                            Aviso("Nao integrou com o PSA ","A Proposta possui rateio com prospect relacionado. Eh necessario transformar o prospect em cliente.",{"Fechar"})
                            exit
                        EndIf

                        AZG->(DbSkip())
                    End
                EndIf
            ENDIF
        ENDIF
    ELSE
        Aviso("Nao integrou com o PSA ","Oportunidade Nao foi fechada.",{"Fechar"})
        lOk := .F.
    ENDIF

    IF lOk
        IF !aRetSaast[1]//Se Nao for SAASTIZADA
            aRet := U_cotacPSA(.F.)
        ELSEIF ADY->ADY_STATUS $ 'A|H|J|I|K'
            aRet := U_iPropPSA(ADY->ADY_PROPOS,'ADY',1,.F.)
        ENDIF

        if !aRet[1]
            lOk := .F.
            Aviso("Nao integrou com o PSA ",aRet[2],{"Fechar"})
        else
            Aviso("Integrou com o PSA ","Protposta foi integrada com o PSA.",{"Fechar"})
        endif
    ENDIF
Return lOk
