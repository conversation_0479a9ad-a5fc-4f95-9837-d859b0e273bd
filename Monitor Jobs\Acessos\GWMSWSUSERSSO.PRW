#include 'protheus.ch'
#Include 'parmtype.ch'
#Include 'RestFul.ch'
#Include 'topconn.ch'
#Include 'totvs.ch'


/*/{Protheus.doc} GWMSWSUSERSSO
//Api para monitorar os usuarios sem SSO
<AUTHOR> silva
@since 27/04/2023
@version 1.0
@example
(examples)
@see (links_or_references)
/*/

WsRestFul GWMSWSUSERSSO Description "Retorna informações referente aos usuarios sem SSO"

	WSDATA action AS STRING
	WsMethod Get Description "Get expenses information"  WSSYNTAX "/GWMSWSUSERSSO?action=initialDate=, finalDate=,fila="

End WsRestFul

WsMethod Get WSRECEIVE action WsService GWMSWSUSERSSO

	Local oJson        as object
	Local oJsonAux     as object
	Local cQuery       := ""
	Local cJson        := ""


	::SetContentType("application/json")
	::SetResponse('[')


	oJson := JsonObject():New()

	oJson["detalhes"] := {}

	cQuery := " SELECT USR.USR_ID, USR.USR_CODIGO, USR.USR_NOME, USR.USR_DTINC from SYS_USR USR "
	cQuery += " LEFT JOIN SYS_USR_SSIGNON SIGNON ON (USR.USR_ID= SIGNON.USR_ID AND SIGNON.D_E_L_E_T_=' ') "
	cQuery += " WHERE USR.D_E_L_E_T_=' ' "
	cQuery += " AND USR.USR_MSBLQL = '2' "
	cQuery += " AND (SIGNON.USR_ID IS NULL OR (SIGNON.USR_SO_DOMINIO = ' ' OR SIGNON.USR_SO_USERLOGIN = ' ') )"

	if SELECT("T01") > 0
		T01->(dbCloseArea())
	endif

	ConOut(cQuery)

	TcQuery cQuery new Alias T01

	T01->(DbGoTop())
	while T01->(!eof())

		oJsonAux := JsonObject():new()
		oJsonAux["id"]	:= T01->USR_ID
		oJsonAux["codigo"]	:= T01->USR_CODIGO
		oJsonAux["nome"]	:= T01->USR_NOME
		oJsonAux["data"]	:= T01->USR_DTINC

		aadd(oJson["detalhes"], oJsonAux)

		T01->(dbSkip())
	EndDo

	cJson := oJson:toJson()
	cJson := encodeUtf8(cJson)

	Conout(cJson)

	if !empty(cJson)
		::SetResponse(oJson:ToJson())
	Endif

	FreeObj(oJson)
	::SetResponse(']')
	FreeObj(oJsonAux)

return .T.
