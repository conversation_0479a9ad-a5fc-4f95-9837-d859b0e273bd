#INCLUDE 'TOTVS.CH'
#INCLUDE 'RESTFUL.CH'
#INCLUDE 'FWMVCDEF.CH'


/*/{Protheus.doc} TFINS011 

OFERTAS_DIGITAIS - API para consulta de tipo de cliente e flag de bloqueio de suporte.
Issue: https://jiraproducao.totvs.com.br/browse/TIPDBP-614

<AUTHOR>
@since		28/04/2022
@version	1.0
/*/

User Function TFINS011()

    Local lRet	      := .T.
    Local oResponse   := Nil
    

    Local cBody       := PARAMIXB[2]
    Local oJson       := JsonObject():new()
    Local cRetoJson   := NIL

    Local cErroBlk    := ''
    Local oException  := ErrorBlock({|e| cErroBlk := + e:Description + e:ErrorStack, lRet := .F. })

    Local cCliente    := ''
    Local cLoja       := ''
    Local ccnpj_cpf   := ''
    Local cDescTpCli  := ''
    Local cStatusBlq  := ''
    Local cMessage    := ''
    Local cDetailMsg        

    Begin Sequence

    /*
    //Json modelo
    {
        "codigo": "T00336",
        "loja": "00",
        "cnpj_cpf": "61099008000141"    
    }
    */

        cRetoJson := oJson:FromJson( cBody )

        If ValType( cRetoJson ) == "U"

            cCliente   := AllTrim(oJson:GetJsonObject("cliente"))
            cLoja      := AllTrim(oJson:GetJsonObject("loja"))
            ccnpj_cpf  := AllTrim(oJson:GetJsonObject("cnpj_cpf"))

        Else

            lRet := .F.

        EndIf

        If ( !Empty(cCliente) .And. !Empty(cLoja) ) 

            DbSelectArea("SA1")
            DbSetOrder(1)
            If !(SA1->(DbSeek(xFilial("SA1")+cCliente+cLoja)))
                lRet := .F.          
                cMessage := 'Cliente nÃ£o encontrado'
                cDetailMsg := 'CÃ³digo/Loja nÃ£o existe na base de dados.'
            EndIf

        ElseIf !Empty(ccnpj_cpf)

            DbSelectArea("SA1")
            DbSetOrder(3)
            If !(SA1->(DbSeek(xFilial("SA1")+ccnpj_cpf)))
                lRet := .F.
                cMessage := 'Cliente nÃ£o encontrado'
                cDetailMsg := 'ccnpj_cpf nÃ£o existe na base de dados.'
            EndIf

        Else

            lRet := .f.
            cMessage := 'ParÃ¢metro obrigatÃ³rio nÃ£o preenchido.'
            cDetailMsg := 'Envie o cÃ³digo e loja ou CPF/CNPJ.'

        EndIf

        If lRet 

            //Retorna a descriÃ§Ã£o do tipo do cliente.
            cDescTpCli := DescTpCli(SA1->A1_XCLIVIP)

            //Retorna o status de bloqueio do cliente.
            cStatusBlq := StsBlqCli(SA1->A1_COD, SA1->A1_LOJA)

        EndIf

    End Sequence

    ErrorBlock(oException)

    If Type('oResponse') == 'O'
        FreeObj(oResponse)
    EndIf

    oResponse := JsonObject():New()

    // Verifica errorBlock
    If lRet
    
        oResponse['tipoCliente'] := cDescTpCli
        oResponse['statusBloqueio'] := cStatusBlq

    Else

        If !Empty(cMessage)

            oResponse['code'] := 2
            oResponse['status'] := 400
            oResponse['message'] := DecodeUTF8(cMessage)
            oResponse['detailedMessage'] := DecodeUTF8(cDetailMsg)

        Else
        
            oResponse['code'] := 1
            oResponse['status'] := 500
            oResponse['message'] := DecodeUTF8('Aconteceu um erro inesperado no serviÃ§o!')
            oResponse['detailedMessage'] := cErroBlk

        EndIf

    EndIf

    RecLock( "P37", .F. )
    P37->P37_STATUS := "2"
    P37->P37_TIMERP := Time()
    P37->P37_BODYRP := EncodeUTF8(oResponse:toJson())
    P37->( MsUnLock() )

    oJson     := Nil
    oResponse := Nil

    FreeObj(oJson)
    FreeObj(oResponse)
  
Return Nil

/*/{Protheus.doc} RespValid

    FunÃ§Ã£o de montagem da resposta das validaÃ§Ãµes.

    @type  Function
    <AUTHOR> Soares
    @since 28/04/2022
/*/
Static Function RespValid(nCode, nStatus, cMessage, cDetailMsg)

    Local oResponse   := JsonObject():New()    

    oResponse['code'] := nCode
    oResponse['status'] := nStatus
    oResponse['message'] := cMessage
    oResponse['detailedMessage'] := cDetailMsg

Return oResponse

/*/{Protheus.doc} DescTpCli

    Retorna a descriÃ§Ã£o do tipo do cliente.

    @type  Function
    <AUTHOR> Soares
    @since 28/04/2022
/*/
Static Function DescTpCli(cTpCli)

    Local cDescTpCli:= ''     

    DbSelectArea('ZX5')
    DbSetOrder(1)
    If ZX5->(   DbSeek(xFilial('ZX5')+;
                GETMV("TI_TABCBOX",,"TDI001")+;
                GETMV("TI_TABCBX2",,"000002")+;
                cTpCli))

        cDescTpCli := AllTrim(ZX5->ZX5_DESCRI)

    EndIf

Return cDescTpCli

/*/{Protheus.doc} StsBlqCli

    Retorna o status de bloqueio do cliente.

    @type  Function
    <AUTHOR> Soares
    @since 28/04/2022
/*/
Static Function StsBlqCli(cCliente, cLoja)

    Local cAliasQry := GetNextAlias()
    Local cStatusBlq:= ''

    If Select(cAliasQry) > 0
        (cAliasQry)->(DbCloseArea())
    EndIf

    BeginSql Alias cAliasQry

        SELECT 
            ZQE_ATENDE
        FROM    
            %Table:ZQE% ZQE
        WHERE
            ZQE_FILIAL = %xFilial:ZQE%
            AND ZQE_CODCLI = %Exp:cCliente%
            AND ZQE_LJCLI = %Exp:cLoja%            
            AND R_E_C_N_O_ IN (
                                SELECT 
                                        MAX(ZQEB.R_E_C_N_O_)
                                FROM 
                                        %Table:ZQE% ZQEB
                                WHERE 
                                        ZQEB.ZQE_FILIAL = ZQE.ZQE_FILIAL
                                        AND ZQEB.ZQE_CODCLI = ZQE.ZQE_CODCLI
                                        AND ZQEB.ZQE_LJCLI = ZQE.ZQE_LJCLI
                                        AND ZQEB.%NotDel%
                                )
            AND ZQE.%NotDel%
    
    EndSql

    If (cAliasQry)->(!Eof())
        cStatusBlq := (cAliasQry)->ZQE_ATENDE
    EndIf

    (cAliasQry)->(DbCloseArea())

Return cStatusBlq
