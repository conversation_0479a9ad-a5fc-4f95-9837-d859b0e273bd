#include "tlpp-core.th"

namespace   Backoffice.Configurador.Gestao_Usuarios.Service.ExtractAllGroupsOfUserService

using namespace Backoffice.Configurador.Gestao_Usuarios.Utils.ConstructEnvironmentUtils

/*/{Protheus.doc} ExtractAllGroupsOfUser
This class aims to extract the groups of user from Protheus
@type class
@version  1.0
<AUTHOR>
@since 11/6/2024
/*/
Class   ExtractAllGroupsOfUser
    Private Data    aGroups         As  Array
    Private Data    oResponse       As  Json
    Private Data    oJsonGRP        As  Json
    Private Data    cJsonEncoded    As  Character

    Public  Method  new()                               As  Object
    Public  Method  constructJsonToExternalSystem()     As  Object
    Public  Method  getJsonForExternalSystem()          As  Character
    Private Method  getAllGroups()                      As  Object

EndClass

/*/{Protheus.doc} ExtractAllGroupsOfUser::new
This method aims to create a new instance of object to this class
@type method
@version  1.0
<AUTHOR>
@since 11/6/2024
@return object, The object instantiated itself
/*/
Method  new()   As  Object   Class   ExtractAllGroupsOfUser
    ::aGroups       :=  {}
    ::oResponse     :=  JsonObject():New()
    ::oJsonGRP      :=  JsonObject():New()
    ::cJsonEncoded  :=  ''
Return  Self

/*/{Protheus.doc} ExtractAllGroupsOfUser::getAllGroups
This method aims to extract the array with all groups from Protheus
@type method
@version  1.0
<AUTHOR>
@since 11/6/2024
@return object, The object instantiated itself
@see https://tdn.totvs.com/display/public/framework/FWSFallGrps
/*/
Method  getAllGroups()  As  Object  Class   ExtractAllGroupsOfUser
    ::aGroups       :=  FWSFAllGrps()
Return Self

/*/{Protheus.doc} ExtractAllGroupsOfUser::getJsonForExternalSystem
This method aims to get the character string with all groups 
@type method
@version  1.0
<AUTHOR>
@since 11/6/2024
@return character, The Character String with all the groupds extracted
/*/
Method  getJsonForExternalSystem()  As  Character  Class   ExtractAllGroupsOfUser
Return ::cJsonEncoded

/*/{Protheus.doc} ExtractAllGroupsOfUser::constructJsonToExternalSystem
This method aims to construct the response to external system
with all groups
@type method
@version  1.0
<AUTHOR>
@since 11/6/2024
@return object, The object instantiated itself
/*/
Method  constructJsonToExternalSystem()   As  Object    Class   ExtractAllGroupsOfUser
    Local nX    :=  1   As  Numeric

    ConstructEnvironment():initEnvironment()
    ::getAllGroups()

    ::oResponse['status'] := 200
    ::oResponse['grupos'] := {}
    
    For nX := 1 To Len(::aGroups)
        ::oJsonGRP := JsonObject():New()
        ::oJsonGRP['id']      := ::aGroups[nX,2]
        ::oJsonGRP['nome']    := AllTrim(::aGroups[nX,3])
        ::oJsonGRP['descric'] := AllTrim(::aGroups[nX,4])  
        aAdd(::oResponse['grupos'],::oJsonGRP )
        ::oJsonGRP := Nil
		FreeObj(::oJsonGRP )
    Next nX
    
    ::cJsonEncoded    :=  EncodeUtf8(::oResponse:toJson())
Return  Self
