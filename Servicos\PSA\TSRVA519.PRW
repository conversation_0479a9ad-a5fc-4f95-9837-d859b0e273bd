#Include 'Protheus.ch'
#Include "FWMVCDef.Ch"
#Include 'topconn.ch'

//-------------------------------------------------------------------
/*/{Protheus.doc} GeraPedidoVenda
Tela com os dados da NCC provenientes do PSA
<AUTHOR>
@since 12/04/2019
/*/
//-------------------------------------------------------------------
User function TSRVA519()

	local aArea       := GetArea()
	Local aDados      := {}
	Local nSavN       := N
	Local aSavaRotina := aClone(aRotina)
	Local bSavKeyF4   := SetKey(VK_F4 ,Nil)
	Local bSavKeyF5   := SetKey(VK_F5 ,Nil)
	Local bSavKeyF6   := SetKey(VK_F6 ,Nil)
	Local bSavKeyF7   := SetKey(VK_F7 ,Nil)
	Local bSavKeyF8   := SetKey(VK_F8 ,Nil)
	Local bSavKeyF9   := SetKey(VK_F9 ,Nil)
	Local bSavKeyF10  := SetKey(VK_F10,Nil)
	Local bSavKeyF11  := SetKey(VK_F11,Nil)
	Local oDlgInfNcc  := Nil
	Local aF1_XRSPIMP := RetSx3Box( Posicione("SX3", 2, "F1_XRSPIMP", "X3CBox()" ),,, 1 )	//{"Totvs", "Cliente (Gerar Nota Debito)", "Cliente (Isentar Nota Debito)"}
	Local cF1_XRSPIMP := ""
	Local cQuemInclui := ""
	Local cQuemAprov  := ""
	//Local aRefatura   := RetSx3Box( Posicione("SX3", 2, "PFZ_REFATU", "X3CBox()" ),,, 1 )	//{1=Sim, 2=Nao}
	Local cRefatura   := "" 
	Local bOK		  := {|| oDlgInfNcc:End() } // Botao de Confirmacao da Tela
	Local bcancel	  := {|| oDlgInfNcc:End() } // Botao de Cancelamento da Tela
	Local oObj        := nil

	cF1_XRSPIMP := Alltrim(aF1_XRSPIMP[Ascan(aF1_XRSPIMP,{|x| SF1->F1_XRSPIMP $ x[2]})][3])

    oObj        := FaturamentoPSA():New()
    adados      := oObj:getNccInfo(SF1->F1_DOC, SF1->F1_SERIE)
    
    cQuemInclui := aDados[9]
    cQuemAprov  := adados[7]
    cRefatura   := IIF(aDados[3],'Sim','Nao')

	MT103SetRet(SF1->F1_MOTRET, SF1->F1_HISTRET)

	//��������������������������������������������������������������������������Ŀ
	//� Monta Tela                                                               �
	//����������������������������������������������������������������������������
	DEFINE MSDIALOG oDlgInfNcc FROM 100,100 TO 500,650 TITLE "Informa��es da Pre-NCC" OF oMainWnd PIXEL
		//MOTIVO DO RETORNO
		cMT103Mot  := SF1->F1_MOTRET
		cDescRet   := Posicione("DHI",1,xFilial("DHI")+cMT103Mot,"DHI_DESCRI") 
		cMT103Hist := SF1->F1_HISTRET
		
		If !Empty( cQuemInclui )
			@ 035,005 SAY "Incluido por" Of oDlgInfNcc PIXEL SIZE 32,12	
			@ 035,050 MSGET cQuemInclui SIZE  200, 10 Of oDlgInfNcc PIXEL When .F.
		EndIf
		
		If !Empty( cQuemInclui )
			@ 050,005 SAY "Aprv. NCC" Of oDlgInfNcc PIXEL SIZE 32,12
			@ 050,050 MSGET cQuemAprov SIZE  200, 10 Of oDlgInfNcc PIXEL When .F.
		EndIf
		
		If SF1->(FieldPos("F1_MOTRET")) > 0
			If !Empty(cMT103Mot)
				@ 065, 005 SAY RetTitle("F1_MOTRET")   Of oDlgInfNcc PIXEL
				@ 065, 050 MSGET (cMT103Mot +"  - "+ cDescRet) SIZE  200, 10 Of oDlgInfNcc PIXEL When .F. 
			EndIf
		EndIf
		
		If SF1->(FieldPos("F1_XRSPIMP")) > 0
			@ 080,005 SAY RetTitle("F1_XRSPIMP") Of oDlgInfNcc PIXEL
			@ 080,050 MSGET cF1_XRSPIMP  SIZE  40, 10 Of oDlgInfNcc PIXEL When .F.
		EndIf
		
		If SF1->(FieldPos("F1_XDTNTDE")) > 0
			@ 080,100 SAY RetTitle("F1_XDTNTDE") Of oDlgInfNcc PIXEL
			@ 080,140 MSGET SF1->F1_XDTNTDE SIZE  70, 10 Of oDlgInfNcc PIXEL When .F.
		EndIf
		
		If SF1->(FieldPos("F1_XAPVISE")) > 0
			@ 105,005 SAY RetTitle("F1_XAPVISE") Of oDlgInfNcc PIXEL SIZE 32,12
			@ 105,050 MSGET (SF1->F1_XAPVISE +" - "+ Posicione("RD0",1,xFilial("RD0")+SF1->F1_XAPVISE,"RD0_NOME") ) SIZE  200, 10 Of oDlgInfNcc PIXEL When .F.
		EndIf
		
		If !Empty(cRefatura)
			@ 120,005 SAY "Refaturar" Of oDlgInfNcc PIXEL
			@ 120,050 MSGET cRefatura  SIZE 40, 10 Of oDlgInfNcc PIXEL When .F.
		EndIf
		
		If SF1->(FieldPos("F1_HISTRET")) > 0
			@ 135,005  SAY RetTitle("F1_HISTRET")  Of oDlgInfNcc PIXEL
			@ 135,050  GET oMemoRet VAR cMT103Hist   Of oDlgInfNcc MEMO  When .F. size 200,37 PIXEL
		EndIf
		

	ACTIVATE MSDIALOG oDlgInfNcc CENTERED ON INIT EnchoiceBar( oDlgInfNcc, bOK, bcancel)

	aRotina	:= aClone(aSavaRotina)
	N := nSavN
	SetKey(VK_F4 ,bSavKeyF4)
	SetKey(VK_F5 ,bSavKeyF5)
	SetKey(VK_F6 ,bSavKeyF6)
	SetKey(VK_F7 ,bSavKeyF7)
	SetKey(VK_F8 ,bSavKeyF8)
	SetKey(VK_F9 ,bSavKeyF9)
	SetKey(VK_F10,bSavKeyF10)
	SetKey(VK_F11,bSavKeyF11)
	RestArea(aArea)

Return