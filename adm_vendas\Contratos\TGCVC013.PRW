#INCLUDE "PROTHEUS.CH"
#INCLUDE "RWMAKE.CH"
#INCLUDE "TOPCONN.CH"
#INCLUDE 'FWMVCDef.ch'

/*/{Protheus.doc} TGCVC013
Chamada de consulta de sites de faturamento por CNAE
<AUTHOR>
@version   1.xx
@since     13/11/2018
/*/
User Function TGCVC013

Local cCNAE		:= ParamIXB[1]
Local cSegmento	:= ''
Local cSiteFat	:= ''
Local nAux		:= 0

Local aArea		:= GetArea()
Local oJson		:= Nil
Local aItens	:= {}
Local nPos		:= 0

oJson := JsonUtil():New()

CC3->(dbSetOrder(1))
If CC3->(dbSeek( FwxFilial('CC3')+ cCNAE ))

	cSegmento	:= CC3->CC3_CODSEG
	
	If cSegmento == '000001' //Manufatura
	
		nAux := Val(StrTran(StrTran(cCNAE,'/',''),'-',''))
		If nAux > 1099699
		
			cSiteFat := '00001001700'
		
		EndIf
	
	EndIf

	If Empty(cSiteFat)

		AOV->(dbSetOrder(1))
		If AOV->(dbSeek( FwxFilial('AOV')+ cSegmento ))
			
			cSiteFat	:= AOV->AOV_XFILSF
	
		EndIf
		
	EndIf
	
	If !Empty(cSegmento) .And. !Empty(cSiteFat)

		aAdd(aItens,JsonUtil():New())
		nPos := Len(aItens)
		
		aItens[nPos]:Putval('cnae',cCNAE)
		aItens[nPos]:Putval('segmento',cSegmento)
		aItens[nPos]:Putval('sitefat',cSiteFat)
		
	EndIf
	
EndIf

RestArea(aArea)

If Len(aItens) > 0
	oJson:PutArray(aItens)
EndIf

Return oJson