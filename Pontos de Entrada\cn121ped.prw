#INCLUDE "TOTVS.CH"
/*
Programa  : CN121PED()
Objetivo  : Ponto de entrada executado no momento do encerramento da medicao, quando o sistema gera o pedido.
Substitui o CN120PED
Retorno   :
Autor     : Wagner Soares
Data/Hora : 04/05/2020	
*/
User Function CN121PED()
	Local aArea		:= GetArea()
	Local aCN9Area	:= CN9->( GetArea() )
	Local aCN1Area	:= CN1->( GetArea() )
	Local _cTipo    := ""
	Local nPosNat	:= 0
	Local nPosPrd	:= 0
	Local cProduto	:= Nil
	Local cCliente	:= Nil
	Local cLojaCli	:= Nil
	Local cNaturez	:= Nil


	Private _aCab	:= PARAMIXB[1]	//Cabecalho
	Private _aItm	:= PARAMIXB[2]  //Itens

	dbSelectArea("CN9")
	dbSetOrder(1)
	dbSeek(xFilial("CN9") + CND->CND_CONTRA + CND->CND_REVISA)

	dbSelectArea("CN1")
	dbSetOrder(1)
	dbSeek(xFilial("CN1") + CN9->CN9_TPCTO)
	_cTipo := CN1->CN1_ESPCTR

	Do Case
		Case _cTipo == "1"
		nAscan := aScan(_aCab,{|x|x[1] == "C7_TPPED"})
		If nAscan == 0
			aAdd(_aCab,{"C7_TPPED", "5", Nil})
		Else
			_aCab[nAscan][2] := "5"
		Endif
		CtrCompra()

		Case _cTipo == "2"
		xCabec := AClone( _aCab )

		nPosNat 	:= Ascan( _aCab, { | x | AllTrim( x[1] ) == 'C5_NATUREZ' } )
		nPosPrd		:= Ascan( _aItm[ 1 ], { | x | AllTrim( x[ 1 ] ) == 'C6_PRODUTO' } )

		If nPosPrd == 0
			Aadd( _aCab, { "C5_NATUREZ"	, CriaVar( "C5_NATUREZ", .F. ) , Nil } )
		Else
			cProduto	:= _aItm[ 1 ][ nPosPrd ][ 02 ]
			cCliente	:= _aCab[ Ascan( _aCab, { | x | AllTrim( x[1] ) == 'C5_CLIENTE' } ) ][ 02 ]
			cLojaCli	:= _aCab[ Ascan( _aCab, { | x | AllTrim( x[1] ) == 'C5_LOJACLI' } ) ][ 02 ]
			cNaturez	:= U_TFATA018( cCliente, cLojaCli, cProduto )

			If nPosNat > 0
				_aCab[ nPosNat ][ 02 ] := cNaturez
			Else
				Aadd( _aCab, { "C5_NATUREZ"	, cNaturez , Nil } )
			EndIf
		EndIf
		
		Aadd( _aCab, { "C5_OUTRINF"	, CND->CND_OBS , Nil } )

	EndCase

	RestArea( aCN1Area )
	RestArea( aCN9Area )
	RestArea( aArea )
Return {_aCab,_aItm}

/*
Programa  : CtrCompra()
Objetivo  : Tratamento para geracao de pedidos de compra.
Retorno   :
Autor     : Fabio Satoru Yamamoto
Data/Hora : 23/12/2015 - 12:00 	
*/
Static Function CtrCompra()

	Local cQry      := ""
	Local nx        := 0
	Local nPosCpo   := 0
	Local __cItMed  := ""

	For nx := 1 to Len(_aItm)

		If(nPosCpo:=aScan(_aItm[nx],{|x|x[1] == "C7_ITEMED"})) > 0
			__cItMed  := _aItm[nx,nPosCpo,2]
		Else
			__cItMed  := "001"
		EndIf 			

		cQry	:= " SELECT CNE.CNE_CC  , CNE.CNE_CLVL  , CNE.CNE_CONTRA, CNE.CNE_ITEM , CNE.CNE_ITEMCT "
		cQry	+= " FROM " + RetSqlName( "CNE" ) + " CNE"
		cQry	+= " WHERE CNE.CNE_FILIAL = '" + xFilial( "CNE" ) + "'"
		cQry	+= " AND CNE.CNE_CONTRA = '" + CND->CND_CONTRA + "'"
		cQry	+= " AND CNE.CNE_REVISA = '" + CND->CND_REVISA + "'"
		cQry	+= " AND CNE.CNE_NUMERO = (SELECT CXN_NUMPLA FROM " + RetSqlName( "CXN" ) + " CXN WHERE CXN_FILIAL = '" + xFilial( "CNE" ) + "' AND CXN_CHECK = 'T' AND CXN_CONTRA = '" + CND->CND_CONTRA + "' AND CXN_REVISA = '" + CND->CND_REVISA + "'  AND CXN_NUMMED = '" + CND->CND_NUMMED + "' AND CXN.D_E_L_E_T_ = ' ' FETCH FIRST 1 ROWS ONLY) "
		cQry	+= " AND CNE.CNE_NUMMED = '" + CND->CND_NUMMED + "'"
		cQry	+= " AND CNE.CNE_ITEM   = '" + __cItMed + "'"
		cQry	+= " AND CNE.D_E_L_E_T_ = ' '"

		If Select( "TRABCNE" ) > 0
			dbSelectArea( "TRABCNE")
			dbCloseArea()
		EndIf

		cQry	:= ChangeQuery( cQry )
		dbUseArea( .T., 'TOPCONN', TcGenQry( ,, cQry ), "TRABCNE", .F., .T. )

		If !Eof()

			If(nPosCpo:=aScan(_aItm[nx],{|x|x[1] == "C7_TPPED"})) > 0
				_aItm[nx,nPosCpo,2] := "5"
			Else
				aAdd(_aItm[nx],{"C7_TPPED", "5", Nil})
			Endif

			If(nPosCpo:=aScan(_aItm[nx],{|x|x[1] == "C7_CC"})) > 0
				_aItm[nx,nPosCpo,2] := TRABCNE->CNE_CC
			Else
				aAdd(_aItm[nx],{"C7_CC", TRABCNE->CNE_CC, Nil})
			Endif

			If(nPosCpo:=aScan(_aItm[nx],{|x|x[1] == "C7_XITCT"})) > 0
				_aItm[nx,nPosCpo,2] := TRABCNE->CNE_ITEMCT
			Else
				aAdd(_aItm[nx],{"C7_ITCT", TRABCNE->CNE_ITEMCT, Nil})
			Endif

			//Se conter este campo ele esta executando o valide do henrique BDK
			If(nPosCpo:=aScan(_aItm[nx],{|x|x[1] == "C7_CLVL"})) > 0
				_aItm[nx,nPosCpo,2] := TRABCNE->CNE_CLVL  				
			Else
				aAdd(_aItm[nx],{"C7_CLVL", TRABCNE->CNE_CLVL  , Nil})
			Endif

		EndIf

		TRABCNE->(dbCloseArea())

	Next

Return
