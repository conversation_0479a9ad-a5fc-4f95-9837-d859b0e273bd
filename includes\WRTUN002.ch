#define STR0001 "Serviço que permite consultar informações do CNPJ/CPF de clientes, prospects e unidades de negocio"
#define STR0002 "Valida o CNPJ/CPF informados na URL"
#define STR0003 "/Cnpj"
#define STR0004 "CNPJ CPF invalidos para pesquisa, nao foi possivel encontrar registro em ADK ( unidade de negocio )"
#define STR0005 "Nao ha dados"
#define STR0006 "CNPJ CPF nao informados"
#define STR0007 "cliente"
#define STR0008 "prospect"
#define STR0009 "NA"
#define STR0010 "CNPJ CPF - cliente, sem amarracao com as tabelas AC8 ( Relacao de Contatos x Entidade ) e SU5 ( Contatos )"
#define STR0011 "CNPJ CPF - prospect inativo, sem amarracao com as tabelas AC8 ( Relacao de Contatos x Entidade ) e SU5 ( Contatos )"
#define STR0012 "CNPJ CPF - unidade de negocio, sem amarracao com a tabela SA3 ( Vendedores )"
