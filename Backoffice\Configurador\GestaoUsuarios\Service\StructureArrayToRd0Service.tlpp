#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Service.StructureArrayToRd0Service


/*/{Protheus.doc} StructureArrayToRd0
This Static Class has the purpose of create an array to 
be used on script to create an Employ on Table RD0
@type class
@version 1.0  
<AUTHOR>
@since 10/25/2024
/*/
Class   StructureArrayToRd0
	Static  Method  getArrayFromJson(oEmployee  As  Object)  As  Array
EndClass

/*/{Protheus.doc} StructureArrayToRd0::getArrayFromJson
This Method has the purpose of work on the instantiated object and creates the necessary array 
with the fields and his values from the income parameter 
@type method
@version 1.0  
<AUTHOR>
@since 10/25/2024
@param oEmployee, object, The parameter must to be a object instantiated on the Class ProtheusEmployeeRd0
@return array, The array necessary for execute the routine to create the employee
/*/
Method getArrayFromJson(oEmployee  As  Object)   As  Array   Class   StructureArrayToRd0
    
    Local aData                 :=  {}                              As  Array
    Local oJsonRD0              :=  oEmployee:getJsonRd0()          As  Json
    Local cCodRD0               :=  oEmployee:getCodeRd0()          As  Character               
    Local cTypeRd0              :=  oEmployee:getTypeRD0()          As  Character
    Local cCodUser              :=  oEmployee:getCodeUser()         As  Character   
    Local cPassWord             :=  oEmployee:getPassWord()         As  Character   
    Local cLogin                :=  oEmployee:getLoginUser()        As  Character
    Local cCodeSupplier         :=  oEmployee:getCodeSupplier()     As  Character
    Local cStoreSupplier        :=  oEmployee:getStoreSupplier()    As  Character
    Local cItemCTB              :=  oEmployee:getItemCont()         As  Character
    Local nTypeOfOperation      :=  oEmployee:getTypeOfOperation()  As  Numeric

	aAdd( aData, { "RD0_CODIGO", cCodRD0                                                       , Nil } )
    aAdd( aData, { "RD0_NOME"  , Upper( oJsonRD0:GetJsonObject( "RD0_NOME" ) )                 , Nil } )
    aAdd( aData, { "RD0_BITMAP", Substr( oJsonRD0:GetJsonObject( "RD0_XMATRH" ), 1, 8 )        , Nil } )
    If  (nTypeOfOperation   ==  3)
        aAdd( aData, { "RD0_TIPO"  , cTypeRd0                                                      , Nil } )
        aAdd( aData, { "RD0_RESERV", " " 				                                                   , Nil } )
    EndIf
    aAdd( aData, { "RD0_SEXO"  , oJsonRD0:GetJsonObject( "RD0_SEXO" )                          , Nil } )
    aAdd( aData, { "RD0_DTNASC", CtoD( oJsonRD0:GetJsonObject( "RD0_DTNASC" ) )                , Nil } )
    aAdd( aData, { "RD0_CIC"   , oJsonRD0:GetJsonObject( "RD0_CIC" )                           , Nil } )
    aAdd( aData, { "RD0_DTADMI", CtoD( oJsonRD0:GetJsonObject( "RD0_DTADMI" ) )                , Nil } )
    aAdd( aData, { "RD0_END"   , Upper( oJsonRD0:GetJsonObject( "RD0_END" ) )                  , Nil } )
    aAdd( aData, { "RD0_CMPEND", Upper( oJsonRD0:GetJsonObject( "RD0_CMPEND" ) )               , Nil } )
    aAdd( aData, { "RD0_CEP"   , Upper( oJsonRD0:GetJsonObject( "RD0_CEP" ) )                  , Nil } )
    aAdd( aData, { "RD0_BAIRRO", Upper( oJsonRD0:GetJsonObject( "RD0_BAIRRO" ) )               , Nil } )
    aAdd( aData, { "RD0_MUN"   , Upper( oJsonRD0:GetJsonObject( "RD0_MUN" ) )                  , Nil } )
    aAdd( aData, { "RD0_UF"    , Upper( oJsonRD0:GetJsonObject( "RD0_UF" ) )                   , Nil } )
    aAdd( aData, { "RD0_FONE"  , oJsonRD0:GetJsonObject( "RD0_FONE" )                          , Nil } )
    aAdd( aData, { "RD0_NUMCEL", oJsonRD0:GetJsonObject( "RD0_NUMCEL" )                        , Nil } )
    aAdd( aData, { "RD0_CC"    , oJsonRD0:GetJsonObject( "RD0_CC" )                            , Nil } )
    aAdd( aData, { "RD0_EMAIL" , oJsonRD0:GetJsonObject( "RD0_EMAIL" )                         , Nil } )
    If  (nTypeOfOperation   ==  3)
        aAdd( aData, { "RD0_USER"  , cCodUser                                                      , Nil } )
        aAdd( aData, { "RD0_MSBLQL", "2"                                                           , Nil } )
        aAdd( aData, { "RD0_SENHA" , cPassWord                                                     , Nil } )
    EndIf
    aAdd( aData, { "RD0_DVIAGE", oJsonRD0:GetJsonObject( "RD0_DVIAGE" )                        , Nil } )
    aAdd( aData, { "RD0_TELCOM", oJsonRD0:GetJsonObject( "RD0_TELCOM" )                        , Nil } )
    aAdd( aData, { "RD0_CELCOM", oJsonRD0:GetJsonObject( "RD0_CELCOM" )                        , Nil } )
    aAdd( aData, { "RD0_EMAILC", oJsonRD0:GetJsonObject( "RD0_EMAILC" )                        , Nil } )
    If  (nTypeOfOperation   ==  3)
        aAdd( aData, { "RD0_LOGIN" , Upper( cLogin )                                               , Nil } )
        aAdd( aData, { "RD0_NVLCAR", "1"                                                           , Nil } )
        aAdd( aData, { "RD0_LOGINR", Upper( cLogin )                                               , Nil } )
    EndIf
    aAdd( aData, { "RD0_EMPATU", oJsonRD0:GetJsonObject( "RD0_EMPATU" )                        , Nil } )
    aAdd( aData, { "RD0_FILATU", oJsonRD0:GetJsonObject( "RD0_FILATU" )                        , Nil } )
    If  (nTypeOfOperation   ==  3)
        aAdd( aData, { "RD0_PERMAD", "1"                                                           , Nil } )
        aAdd( aData, { "RD0_APROPC", "000005"                                                      , Nil } )
        aAdd( aData, { "RD0_FORNEC", cCodeSupplier                                                 , Nil } )
        aAdd( aData, { "RD0_LOJA"  , cStoreSupplier                                                , Nil } )
    EndIf
    aAdd( aData, { "RD0_XITCT" , cItemCTB                                                      , Nil } )
    aAdd( aData, { "RD0_XPREMI", oJsonRD0:GetJsonObject( "RD0_XPREMI" )                        , Nil } )
    aAdd( aData, { "RD0_XCLVL" , oJsonRD0:GetJsonObject( "RD0_XCLVL" )                         , Nil } )
    aAdd( aData, { "RD0_XCARGO", oJsonRD0:GetJsonObject( "RD0_XCARGO" )                        , Nil } )
    aAdd( aData, { "RD0_XMATRH", oJsonRD0:GetJsonObject( "RD0_XMATRH" )                        , Nil } )
    aAdd( aData, { "RD0_XDTACO", CtoD( oJsonRD0:GetJsonObject( "RD0_XDTACO" ) )                , Nil } )
    aAdd( aData, { "RD0_XSUPER", oJsonRD0:GetJsonObject( "RD0_XSUPER" )                        , Nil } )
    If  (nTypeOfOperation   ==  3)
        aAdd( aData, { "RD0_XATUAC", "1"                                                           , Nil } )
    EndIf
    aAdd( aData, { "RD0_XCOORD", oJsonRD0:GetJsonObject( "RD0_XCOORD" )                        , Nil } )
    aAdd( aData, { "RD0_XCCPF" , oJsonRD0:GetJsonObject( "RD0_XCCPF" )                         , Nil } )
    If  (nTypeOfOperation   ==  3)
        aAdd( aData, { "RD0_XCOTER", "N"                                                           , Nil } )
    EndIf
    aAdd( aData, { "RD0_XNCARG", oJsonRD0:GetJsonObject( "RD0_XNCARG" )                        , Nil } )
    aAdd( aData, { "RD0_XUNSRV", oJsonRD0:GetJsonObject( "RD0_XUNSRV" )                        , Nil } )

Return aData




