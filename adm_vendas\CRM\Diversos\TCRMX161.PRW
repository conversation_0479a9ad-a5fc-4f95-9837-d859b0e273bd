#include 'protheus.ch'

User Function TCRMX161
Local cPai          := " "
Local aRetorno      := {}
Local oRetorno      := NIL

aRetorno := t161Loop(cPai)

oRetorno := JsonUtil():New()
oRetorno:putArray(aRetorno)

Return(oRetorno)

static Function t161loop(cPai)
Local aArea         := GetArea()
Local cTemp         := GetNextAlias()
Local cWhere        := ""
Local aRet          := {}
Local nPos          := 0
Local cGpProd       := ""
Local cTabela       := GetMV("TI_TABINIL",,"TVO")
Local aTmp          := {}
Local cGrpiili      := AllTrim(GetMv("TI_GRPIILI",,"17TA"))

If Empty(cPai)
    cWhere := "% POV_PROPAI = ' ' %"
Else
    cWhere := "% POV_PROPAI = '"+cPai+"' %"
EndIf

BeginSql Alias cTemp

    SELECT DISTINCT POV_PRODUT, POV_PROPAI, POV_DESCRI, POV_CODINT, POV_MODAPR, POV_FATOR, POV_FATILI, POV_FILFX, POV_FXDE, POV_FXATE, DA1_PRCVEN, DA1_XFRMID
    FROM %table:POV% POV
    LEFT JOIN %table:DA1% DA1
        ON DA1_FILIAL=%xFilial:DA1%
        AND DA1_CODTAB=%exp:cTabela%
        AND DA1_CODPRO=POV_PRODUT
        AND DA1.%notDel%
    WHERE POV_FILIAL=%xFilial:POV%
    AND %exp:cWhere%
    AND POV.%notDel%
    ORDER BY POV_CODINT

EndSql

Do While !(cTemp)->(EoF())

    aAdd(aRet,JsonUtil():New())
    nPos := Len(aRet)

    cGpProd := ReadValue("SB1", 1, xFilial("SB1")+Alltrim((cTemp)->POV_PRODUT), "B1_GRUPO")
    lRecorIli :=  cGpProd $ cGrpiili .And. ReadValue("SBM", 1, xFilial("SBM")+cGpProd, "BM_XGRPREC") == '1'

    aRet[nPos]:putVal("codigo",Alltrim((ctemp)->POV_PRODUT))
    aRet[nPos]:putVal("codPai",Alltrim((ctemp)->POV_PROPAI))
    aRet[nPos]:putVal("produto",U_EspecMsg(Alltrim((ctemp)->POV_DESCRI)))
    aRet[nPos]:putVal("codint",Alltrim((ctemp)->POV_CODINT))
    aRet[nPos]:putVal("modApresentacao",(cTemp)->POV_MODAPR)
    aRet[nPos]:putVal("recorrente",lRecorIli)
    aRet[nPos]:putVal("fatorIds",(cTemp)->POV_FATOR)
    aRet[nPos]:putVal("fatorIlim",(cTemp)->POV_FATILI)
    aRet[nPos]:putVal("aplicaFaixa",(cTemp)->POV_FILFX )
    aRet[nPos]:putVal("faixaDe",(cTemp)->POV_FXDE)
    aRet[nPos]:putVal("faixaAte",(cTemp)->POV_FXATE)
    aRet[nPos]:putVal("precoId",(cTemp)->DA1_PRCVEN)
    aRet[nPos]:putVal("formulaId",(cTemp)->DA1_XFRMID)

    aTmp := t161loop((ctemp)->POV_PRODUT)

    aRet[nPos]:putVal("depend",aTmp)

    (ctemp)->(dbSkip())

EndDo

(cTemp)->(dbCloseArea())
RestArea(aArea)
return(aRet)
