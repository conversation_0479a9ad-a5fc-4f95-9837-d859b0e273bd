#Include "TOTVS.ch"

/*/{Protheus.doc} GetZX51
Pegar ZX5
@type function
@version 1 
<AUTHOR>
@since 02/08/2022
@param cTabela, character, param_description
@param cChave, character, param_description
@param cCampo, character, param_description
@param uDefault, variant, param_description
@return variant, return_description
/*/

User Function GetZX51(cTabela, cChave, cCampo, uDefault)
    Local cZX5_FILIAL := xFilial("ZX5")
    Local cZX5_TABELA := AvKey(cTabela,"ZX5_TABELA")
    Local cZX5_CHAVE := AvKey(cChave,"ZX5_CHAVE")
    Local cContent := Posicione("ZX5", 1, cZX5_FILIAL+cZX5_TABELA+cZX5_CHAVE, cCampo)
    
    Default uDefault := NIL
    
    If Empty(cContent)
        cContent := uDefault
    EndIf

Return Alltrim(cContent)
