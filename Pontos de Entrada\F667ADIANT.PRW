#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'FWMVCDEF.CH'

/*/{Protheus.doc} F667ADIANT

Permite manipular o valor adiantamento complementar.  

<AUTHOR>
@since  01/09/2017
/*/

User Function F667ADIANT()

	//Local nVlrAdiant := StaticCall( F050RAUTO, fVlrAdiant, FL5->FL5_DTINI, FL5->FL5_DTFIM, FL5->FL5_NACION)
	Local nVlrAdiant := U_fVlrAdiant(FL5->FL5_DTINI, FL5->FL5_DTFIM, FL5->FL5_NACION)
	
Return nVlrAdiant
