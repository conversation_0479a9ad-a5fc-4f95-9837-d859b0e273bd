#INCLUDE 'PROTHEUS.CH'
#INCLUDE "TOTVS.CH"
#INCLUDE 'PARMTYPE.CH'
#INCLUDE "RESTFUL.CH"
#INCLUDE "APWEBSRV.CH"
#INCLUDE 'APWEBEX.CH'

User Function TAdqJB23()
	
	Local aParam := { "00","00001000100","000000" }
	
	U_TINTJ023( aParam , .T. )
	
Return

//-------------------------------------------------------------------
/*/{Protheus.doc} TINTJ023
JOB Consulta no HLCloud, clientes que possuem HLCloud - 
Atualiza o TOTVSI  na PNO

@type function
<AUTHOR>
@since 20/06/2018
/*/
//-------------------------------------------------------------------
User Function TINTJ023( aParam , lJob )

	Local clEmp		:= "" 
	Local clFil 	:= ""
	Local cUser 	:= ""
	Local cMsg		:= ""
	Local lOk		:= .T.
	Local cChvLock	:= "TINTJ023"
	Local cDirLck	:= "\semaforo\"
	
	Default aParam	:= { "00","00001000100","000000" }
	Default lJob	:= .T.
	
	clEmp	:= aParam[1]
	clFil	:= aParam[2]
	cUser	:= If(!Empty(aParam[3]),aParam[3],"000000")

    Conout("Iniciando Job TINTJ023 - Consulta HLCloud - Retorno TOTVSI")

	//--------------------------------------
	// Prepara o ambiente
	//--------------------------------------
	If lJob
	
		RpcSetType(3)
		
		If !RpcSetEnv(clEmp,clFil)
		
			cMsg := " TINTJ023 " + "["+Alltrim(clEmp)+ "/" +Alltrim(clFil)+"] - ERRO - Nao foi possivel iniciar a empresa e filial!"
			FWMonitorMsg( cMsg)
			Conout(cMsg)
			lOk := .F.
			
		EndIf
		
	EndIf

	If lOk

        If U_LockByTI( cChvLock )

            FWMonitorMsg( cChvLock  + " ["+Alltrim(clEmp)+ "/" +Alltrim(clFil)+"] Processamento Iniciado - " + Time() )
            
            //Processamento do Job - Clientes que possuem HLCloud - Atualiza o TOTVSI
            U_TIT22Pr2( )

            U_UnLockTI( cChvLock  )
            FErase( cDirLck + cChvLock + ".lck")

            FWMonitorMsg( cChvLock + " ["+Alltrim(clEmp)+ "/" +Alltrim(clFil)+"] Processamento Finalizado - " + Time() )

        Else

            cMsg := "Job ja esta sendo executado. Semaforo: " +cChvLock 
            Conout( cMsg )

        EndIf				
		
	EndIf	
	
    Conout("Finalizado Job TINTJ023 - Consulta HLCloud - Retorno TOTVSI")

	//--------------------------------------
	// Finaliza a thread do Job
	//--------------------------------------
	If lJob
		RpcClearEnv()
	EndIF
	
Return
