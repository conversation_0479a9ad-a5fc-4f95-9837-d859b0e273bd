#include    "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Utils.CheckPasswordWithSeekUtils

/*/{Protheus.doc} CheckPasswordWithSeek
This class aims to validate the password from the income object 
@type class
@version  1.0
<AUTHOR>
@since 11/8/2024
/*/
Class   CheckPasswordWithSeek
    Static  Method  executeSeek(oWithAllData  As  Object) As  Logical
EndClass

/*/{Protheus.doc} CheckPasswordWithSeek::executeSeek
This method aims to validate the password using the function PSWSeek
@type method
@version  1.0
<AUTHOR>
@since 11/8/2024
@param oWithAllData, object, The income Object with the data of the user
@return logical, Logical value true if success false otherwise
/*/
Method  executeSeek(oWithAllData  As  Object) As  Logical Class   CheckPasswordWithSeek
    Local   cCheck      :=  .F. As  Logical

    PSWOrder(2)
    If PSWSeek(oWithAllData:getArrayUser())
        cCheck  :=  .T.
    Else
        cCheck  :=  .F.
        oWithAllData:setArrayReturn(.F., "User not found to perform blocking on USR")
    EndIf
Return cCheck
