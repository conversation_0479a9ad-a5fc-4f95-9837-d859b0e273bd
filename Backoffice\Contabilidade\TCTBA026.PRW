/*/
@Function:	TCTBA026
@desc:		Fonte para retornar o valor presente do AVP.   
@author:	<PERSON><PERSON>
@version: 	1.00
@since: 	09/01/2017
/*/

User Function TCTBA026()

Local nValVP
Local nIndDia
Local nDiasAvp
Local cCodInd
Local cPeriodo
Local nDias
	
	DbSelectArea("FIT")
	FIT->(DbSetOrder(1))

	If cProcAvp == "C"
		cCodInd		:= "01"
		cPeriodo	:= GetAdvFVal("FIT","FIT_PERIOD", xFilial("FIT") + cCodInd)
		nValInd		:= FinRetInd(cCodInd,aAuxAvp[2])
		nTaxa 		:= nValInd
		dDataFin	:= SE1->E1_VENCTO
		dDataIni	:= SE1->E1_EMISSAO
	Else
		cCodInd		:= "01"
		cPeriodo	:= GetAdvFVal("FIT","FIT_PERIOD", x<PERSON><PERSON><PERSON>("FIT") + cCodInd)
		dDataFin	:= SE1->E1_VENCTO
		dDataIni	:= If(aAuxAvp[2] > SE1->E1_VENCTO, SE1->E1_VENCTO , aAuxAvp[2] )
		nTaxa		:= FIN->FIN_TAXAVP
	EndIf
	
	If nTaxa	> 0
		
		DO CASE
			CASE cPeriodo = "1"	//Diario
				nDias := 1
			CASE cPeriodo = "2"	//Mensal
				nDias := 30
			CASE cPeriodo = "3"	//Trimestra
				nDias := 90
			CASE cPeriodo = "4"	//Semestral
				nDias := 180
			CASE cPeriodo = "5"	//Anual
				nDias := 365
			OTHERWISE
				nDias := 0
		ENDCASE
	
		nIndDia		:= (1+(nTaxa/100))**(1/nDias)
		nDiasAvp	:= (dDataFin - dDataIni)
		nValVP		:= nValTit / (nIndDia**nDiasAVP)
		
	EndIf

Return(nValVP)