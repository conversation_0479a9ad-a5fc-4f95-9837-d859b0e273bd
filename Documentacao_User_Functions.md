# Documentação das User Functions - Projeto Protheus BR

Este documento contém a relação de todas as User Functions identificadas no projeto protheus_br, organizadas por módulo/diretório.

## Índice
- [Pontos de Entrada](#pontos-de-entrada)
- [Backoffice](#backoffice)
- [Administração de Vendas](#administração-de-vendas)
- [Serviços](#serviços)
- [Web Services](#web-services)
- [Rotinas Genéricas](#rotinas-genéricas)
- [Monitor Jobs](#monitor-jobs)
- [Santo Graal](#santo-graal)
- [Rot<PERSON> de Ajuste](#rotinas-de-ajuste)

---

## Pontos de Entrada

### TXGV02BP.prw
- **User Function TXGV02BP()**
  - **Autor:** Fernando Nascimento
  - **Data:** 02/07/2019
  - **Descrição:** Ponto de entrada para validação de funções específicas
  - **Parâmetros:** Nenhum
  - **Retorno:** Lógico

### F151BOR.PRW
- **User Function F151BOR()**
  - **Autor:** oswaldo.leite
  - **Data:** 29/09/2016
  - **Descrição:** PE para posicionar bordero ao gerar instrução de cobrança
  - **Parâmetros:** Nenhum
  - **Retorno:** Lógico

### F620QRY.PRW
- **User Function F620QRY()**
  - **Descrição:** Filtro para consulta específica
  - **Retorno:** String com filtro "E5_ORIGEM ='FINA846'"

### CRMXFILPAP.prw
- **User Function CRMXFILPAP()**
  - **Autor:** eder.oliveira
  - **Data:** 20/03/2017
  - **Descrição:** Filtra usuários fora da estrutura de vendas - Área de Trabalho
  - **Retorno:** String com filtro

- **User Function CRMRetFPap(lAdvpl)**
  - **Autor:** eder.oliveira
  - **Data:** 21/03/2017
  - **Parâmetros:** lAdvpl (logical) - Se deve usar sintaxe AdvPL
  - **Retorno:** String com filtro

---

## Backoffice

### Configurador
#### TCFGW001.PRW
- **User Function TCFGW001()**
  - **Data:** 17/01/2020
  - **Descrição:** Função principal do configurador
  - **Retorno:** Nenhum

#### Gestão de Usuários
##### GeneralSeekOnDbRepository.tlpp
- **User Function GeneralSeekOnDbRepositoryExample()**
  - **Autor:** jose.matheus
  - **Data:** 12/11/2024
  - **Descrição:** Exemplo de uso da classe GeneralSeekOnDb
  - **Retorno:** Nil

#### TGCTC002.prw
- **User Function TGCTC002()**
  - **Autor:** Henrique Ghidini da Silva
  - **Data:** 24/03/2015
  - **Descrição:** Fonte para consulta de filiais
  - **Retorno:** Lógico

- **User Function RetItem4()**
  - **Descrição:** Retorna lista de filiais
  - **Retorno:** String com filiais

### Interceptor
#### TIITC001.PRW
- **User Function TIITC001()**
  - **Autor:** Cassio Menabue Lima
  - **Data:** 22/11/2022
  - **Descrição:** Função responsável por retornar as APIs cadastradas na P36
  - **Retorno:** Objeto JSON

---

## Administração de Vendas

### EasySales
#### ESPA050.prw
- **User Function ESPA050()**
  - **Descrição:** Cadastro com browse MVC
  - **Retorno:** Nenhum

#### Diversos
##### ESPG020.prw
- **User Function ESPG020()**
  - **Data:** 25-08-2020
  - **Descrição:** Função para tratamento de tipos de dados
  - **Retorno:** String

##### ESPG050.prw
- **User Function ESPG050()**
  - **Data:** 25-08-2020
  - **Descrição:** Função para processamento de dados específicos
  - **Retorno:** String

##### ESPX010B.prw
- **User Function ESPX010B(cTokenExt, cFilePath)**
  - **Parâmetros:** 
    - cTokenExt: Token externo
    - cFilePath: Caminho do arquivo
  - **Retorno:** String com conteúdo do arquivo

##### ESPX010C.prw
- **User Function ESPX010C(cTokenExt)**
  - **Parâmetros:** cTokenExt: Token externo
  - **Retorno:** Array com arquivos

##### ESPX010D.prw
- **User Function ESPX010D(cTokenExt, cFileName)**
  - **Parâmetros:**
    - cTokenExt: Token externo
    - cFileName: Nome do arquivo
  - **Retorno:** String com conteúdo do arquivo

### Comissões
#### TIPORV01.PRW
- **User Function TIPORV01()**
  - **Autor:** Cassio Menabue Lima
  - **Data:** 31/10/2023
  - **Descrição:** Função do APP projeto RV
  - **Retorno:** Nenhum

#### TIRVF000.PRW
- **User Function TIRVF000(cRespBody)**
  - **Autor:** Cassio Menabue Lima
  - **Data:** 18/09/2023
  - **Descrição:** Callback de autenticação RV customizado
  - **Parâmetros:** cRespBody: Corpo da resposta
  - **Retorno:** Nenhum

#### TGetImpFile.prw
- **User Function TGetImpF()**
  - **Descrição:** Função para importação de arquivos CSV
  - **Retorno:** Lógico

### CRM
#### TCRMX233.PRW
- **User Function TCRMX233(cPWJ_PRNUM)**
  - **Parâmetros:** cPWJ_PRNUM: Número do processo
  - **Retorno:** Objeto JSON

### Contratos
#### TGCVXC08.prw
- **User Function Tgcvxc08()**
  - **Descrição:** Função relacionada a contratos
  - **Retorno:** Nenhum

---

## Serviços

### PSA
#### TSRVA500.prw
- **User Function TSRVA500(aPV)**
  - **Parâmetros:** aPV: Array com dados do pedido de venda
  - **Descrição:** Processamento de pedidos de venda para PSA
  - **Retorno:** Nenhum

#### TSRVA501.prw
- **User Function TSRVA501(cAnoMes, cUnSrv)**
  - **Parâmetros:**
    - cAnoMes: Ano e mês de referência
    - cUnSrv: Unidade de serviço
  - **Descrição:** Fechamento contábil PSA
  - **Retorno:** Nenhum

#### TSRVA502.prw
- **User Function TSRVA502()**
  - **Descrição:** Cadastro DE/PARA de Centro de Custo X Cargos
  - **Retorno:** Lógico

#### TSRVA503.prw
- **User Function TSRVA503()**
  - **Descrição:** Carga de dados PSA
  - **Retorno:** Nenhum

- **User Function cargaPSA(oSay, lJob)**
  - **Parâmetros:**
    - oSay: Objeto de mensagem
    - lJob: Se é execução via job
  - **Retorno:** Nenhum

#### TSRVX505.PRW
- **User Function TSRVX505()**
  - **Autor:** wanderson.santos
  - **Data:** 04/07/2024
  - **Descrição:** Força integração de proposta com PSA
  - **Retorno:** Nenhum

### Educação Empresarial
#### TSGEX001.prw
- **User Function TSGEX001()**
  - **Descrição:** Integração com sistema externo de educação
  - **Retorno:** String

#### TSGEX002.PRW
- **User Function TSGEX002()**
  - **Descrição:** Configuração de URLs por ambiente
  - **Retorno:** Array com URL e chave

---

## Web Services

### TSRVS001.prw
- **User Function FATTICKET(_cEmpPed, _cFilPed, _cUserPed, _cPswPad, nVlrHrConv, aCab, aItens, _cTime, _lSincrono, cIdTicket, _lJob)**
  - **Descrição:** Geração de pedido via ticket
  - **Parâmetros:** Múltiplos parâmetros para geração do pedido
  - **Retorno:** Array

- **User Function FFTObj(_cEmpPed, _cFilPed, _oCab, _oItens)**
  - **Descrição:** Preenchimento de array do pedido com objetos
  - **Retorno:** Nenhum

- **User Function TokenZendesk()**
  - **Autor:** wanderson.santos
  - **Data:** 15/05/2024
  - **Descrição:** Gera Token Zendesk
  - **Retorno:** String

### WRTotvsDigital.PRW
- **User Function RetUndGar(cCodVend)**
  - **Parâmetros:** cCodVend: Código do vendedor
  - **Retorno:** String

- **User Function RetUltStage(cEntidade, cCodEnt, cLojEnt)**
  - **Parâmetros:** Dados da entidade
  - **Retorno:** String

- **User Function RetDescri(cEntidade, cCodigo)**
  - **Parâmetros:** Entidade e código
  - **Retorno:** String

- **User Function RetDtVendFat(cCodEnt, cCodLoj)**
  - **Parâmetros:** Código e loja da entidade
  - **Retorno:** Data

- **User Function RetInfSA3(cCodVend)**
  - **Parâmetros:** cCodVend: Código do vendedor
  - **Retorno:** Array

### WSGTOKEN.PRW
- **User Function WSO2GTKN(cKeySecret)**
  - **Parâmetros:** cKeySecret: Chave secreta
  - **Descrição:** Geração de token OAuth2
  - **Retorno:** String

---

## Rotinas Genéricas

### TRGA070.prw
- **User Function TRGA070(cFilWS)**
  - **Data:** 14/08/2023
  - **Parâmetros:** cFilWS: Filial para validação
  - **Descrição:** Validação de filial em web service
  - **Retorno:** Lógico

### TRGENFUNC.prw
- **User Function CriaFwTrb(cAliasTemp, aFields, aLstIndice)**
  - **Parâmetros:**
    - cAliasTemp: Alias da tabela temporária
    - aFields: Array com campos
    - aLstIndice: Lista de índices
  - **Descrição:** Criação de tabela temporária FW
  - **Retorno:** Objeto

### TPSAX001.PRW
- **User Function CalcMeses(dtEndVig)**
  - **Parâmetros:** dtEndVig: Data de fim de vigência
  - **Descrição:** Cálculo de meses
  - **Retorno:** Numérico

### FTTVSX001.prw
- **User Function FTTVSX001(cGrupo, cOrdem, cPergunt, cPerSpa, cPerEng, cVar, cTipo, nTamanho, nDecimal, ...)**
  - **Descrição:** Criação de perguntas SX1
  - **Parâmetros:** Múltiplos parâmetros para configuração
  - **Retorno:** Nenhum

---

## Monitor Jobs

### GWMSWSUSERSSO.PRW
- **User Function GWMSWSUSERSSO()**
  - **Autor:** weskley silva
  - **Data:** 27/04/2023
  - **Descrição:** API para monitorar os usuários sem SSO
  - **Retorno:** JSON

---

## Santo Graal

### CLIA014.PRW
- **User Function CLIA014()**
  - **Descrição:** Cadastro de Produto com browse
  - **Retorno:** Nenhum

---

## Rotinas de Ajuste

### PROCLIST.PRW
- **User Function PROCLIST()**
  - **Descrição:** Processamento de lista de clientes em CSV para reprocessar ZQE
  - **Retorno:** Lógico

---

## Observações

1. **Padrão de Nomenclatura:** As User Functions seguem o padrão TXXXNNN onde:
   - T = Prefixo TOTVS
   - XXX = Sigla do módulo (SRV=Serviços, CRM=CRM, etc.)
   - NNN = Número sequencial

2. **Documentação:** Muitas funções utilizam o padrão TDoc do Protheus para documentação

3. **Parâmetros:** A maioria das funções aceita parâmetros opcionais com valores default

4. **Retornos:** Os tipos de retorno variam entre lógico, string, array, objeto JSON e nil

5. **Integração:** Várias funções são específicas para integração com sistemas externos (PSA, Zendesk, etc.)

## Serviços - Continuação

### Relatórios
#### TSRVR001.prw
- **User Function TSRVR001()**
  - **Descrição:** Relatório principal de serviços
  - **Retorno:** Nenhum

#### TSRVR013.prw
- **User Function TSRVR013()**
  - **Descrição:** Relatório de faturamento por cliente
  - **Retorno:** Nenhum

#### TSRVR015.prw
- **User Function TSRVR015()**
  - **Descrição:** Relatório específico de serviços
  - **Retorno:** Nenhum

#### TSRVR016.prw
- **User Function TSRVR016()**
  - **Descrição:** Relatório DRE de projetos
  - **Retorno:** Nenhum

- **User Function CA50Impr(...)**
  - **Descrição:** Impressão de relatório CA50
  - **Parâmetros:** Múltiplos parâmetros para configuração
  - **Retorno:** Nenhum

#### TSRVR017.prw
- **User Function TSRVR017()**
  - **Descrição:** Relatório de ordem de serviço
  - **Retorno:** Nenhum

- **User Function MEMOtxt(_chave)**
  - **Parâmetros:** _chave: Chave para busca
  - **Retorno:** String

#### TSRVR018.prw
- **User Function TSRVR018()**
  - **Descrição:** Relatório de apontamentos
  - **Retorno:** Nenhum

#### TSRVR019.prw
- **User Function TSRVR019()**
  - **Descrição:** Relatório de remuneração variável
  - **Retorno:** Nenhum

- **User Function TSRVR19A(...)**
  - **Descrição:** Processamento de dados para RV
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

#### TSRVR020.prw
- **User Function TSRVR020()**
  - **Descrição:** Relatório de garantias
  - **Retorno:** Nenhum

#### TSRVR021.prw
- **User Function TSRVR021()**
  - **Descrição:** Relatório específico de serviços
  - **Retorno:** Nenhum

### TPS (Tecnologia, Produtos e Serviços)
#### TSRVA260.prw
- **User Function TSRVA260()**
  - **Descrição:** Cadastro MVC para TPS
  - **Retorno:** Nenhum

#### TSRVA261.prw
- **User Function TSRVA261()**
  - **Descrição:** Cadastro de atividades TPS
  - **Retorno:** Nenhum

#### TSRVA262.prw
- **User Function TSRVA262(cTipo)**
  - **Parâmetros:** cTipo: Tipo de operação
  - **Descrição:** Gestão de projetos TPS
  - **Retorno:** Nenhum

- **User Function TS262PRJ()**
  - **Descrição:** Seleção de projetos
  - **Retorno:** Nenhum

#### TSRVA263.prw
- **User Function TSRVA263(cTipo)**
  - **Parâmetros:** cTipo: Tipo de operação
  - **Descrição:** Cadastro específico TPS
  - **Retorno:** Nenhum

#### TSRVA264.prw
- **User Function TSRVA264(cTipo)**
  - **Parâmetros:** cTipo: Tipo de operação
  - **Descrição:** Gestão avançada TPS
  - **Retorno:** Nenhum

- **User Function SV264POS(...)**
  - **Descrição:** Posicionamento em tabelas
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Variant

- **User Function T264TIPO(cTipo)**
  - **Parâmetros:** cTipo: Tipo
  - **Retorno:** String

- **User Function SRV264Vld(nTipo)**
  - **Parâmetros:** nTipo: Tipo de validação
  - **Retorno:** String

#### TSRVA265.prw
- **User Function TSRVA265()**
  - **Descrição:** Cadastro complementar TPS
  - **Retorno:** Nenhum

#### TSRVA266.prw
- **User Function TSRVA266(cDesEnv, cCCEnv, aDadCab, aDadIte)**
  - **Parâmetros:**
    - cDesEnv: Destinatário
    - cCCEnv: Cópia
    - aDadCab: Dados do cabeçalho
    - aDadIte: Dados dos itens
  - **Descrição:** Envio de e-mail
  - **Retorno:** Nenhum

#### TSRVA267.prw
- **User Function TSRVA267(cTipo)**
  - **Parâmetros:** cTipo: Tipo
  - **Descrição:** Processamento específico TPS
  - **Retorno:** Nenhum

#### TSRVA268.prw
- **User Function TS268RCD(cTipRec)**
  - **Parâmetros:** cTipRec: Tipo de recurso
  - **Retorno:** Nenhum

- **User Function TS268GAT(...)**
  - **Descrição:** Gatilho para TPS
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

- **User Function TS268BNT(...)**
  - **Descrição:** Busca de técnicos
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

#### TSRVA269.prw
- **User Function TSRVA269()**
  - **Descrição:** Gestão de conhecimento TPS
  - **Retorno:** Nenhum

- **User Function T269Tela()**
  - **Descrição:** Interface de usuário
  - **Retorno:** Nenhum

- **User Function T269Hist(...)**
  - **Descrição:** Histórico de conhecimento
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

#### TSRVA270.PRW
- **User Function TSRVA270()**
  - **Descrição:** Busca avançada de técnicos
  - **Retorno:** Nenhum

### PMS (Project Management System)
#### Milestones
##### TSRVA271.prw
- **User Function TSRVA271()**
  - **Descrição:** Cadastro de milestones
  - **Retorno:** Nenhum

- **User Function SR271VLAC(oModel)**
  - **Parâmetros:** oModel: Modelo de dados
  - **Descrição:** Validação de ações
  - **Retorno:** Lógico

##### TSRVA272.prw
- **User Function TSRVA272()**
  - **Descrição:** Gestão de envolvidos em milestones
  - **Retorno:** Nenhum

- **User Function SR272VLAC(oModel)**
  - **Parâmetros:** oModel: Modelo de dados
  - **Descrição:** Validação de ações
  - **Retorno:** Lógico

##### TSRVA273.prw
- **User Function TSRVA273(cFiltro)**
  - **Parâmetros:** cFiltro: Filtro para dados
  - **Descrição:** Gestão de milestones por projeto
  - **Retorno:** Nenhum

- **User Function TS273Mail(oView, cTipo, cAlias)**
  - **Parâmetros:**
    - oView: Objeto da view
    - cTipo: Tipo de e-mail
    - cAlias: Alias da tabela
  - **Descrição:** Envio de e-mail de milestone
  - **Retorno:** Nenhum

##### TSRVA274.prw
- **User Function TSRVA274(lFilPend, oOwner, cFiltro)**
  - **Parâmetros:**
    - lFilPend: Filtrar pendentes
    - oOwner: Objeto proprietário
    - cFiltro: Filtro
  - **Descrição:** Execução de milestones
  - **Retorno:** Nenhum

- **User Function TS274Carga(oView, cWBS, nComplex)**
  - **Descrição:** Carga de dados de milestone
  - **Retorno:** Nenhum

- **User Function TS274Vld(cCpo)**
  - **Parâmetros:** cCpo: Campo para validação
  - **Retorno:** Lógico

- **User Function TS274Exec(...)**
  - **Descrição:** Execução de milestone
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

- **User Function TS274Fil(cProjeto)**
  - **Parâmetros:** cProjeto: Código do projeto
  - **Retorno:** String

- **User Function TS274SQL()**
  - **Descrição:** Query SQL para milestones
  - **Retorno:** String

- **User Function TS274PMS()**
  - **Descrição:** Integração com PMS
  - **Retorno:** Nenhum

##### TSRVA275.prw
- **User Function TSRVA275()**
  - **Descrição:** Configuração de milestones
  - **Retorno:** Nenhum

- **User Function VlPrjPEF()**
  - **Descrição:** Validação de projeto
  - **Retorno:** Lógico

- **User Function VlWBSPEF()**
  - **Descrição:** Validação de WBS
  - **Retorno:** Lógico

- **User Function AFCTDI()**
  - **Descrição:** Função específica de milestone
  - **Retorno:** Nenhum

- **User Function SR275EDT()**
  - **Descrição:** Gestão de EDT
  - **Retorno:** String

- **User Function SR275PRJ()**
  - **Descrição:** Gestão de projeto
  - **Retorno:** String

- **User Function T275DelM(cProjeto, cEDT)**
  - **Parâmetros:**
    - cProjeto: Código do projeto
    - cEDT: Código da EDT
  - **Descrição:** Exclusão de milestone
  - **Retorno:** Nenhum

##### TSRVA276.prw
- **User Function TSRVA276()**
  - **Descrição:** Relatório de milestones
  - **Retorno:** Nenhum

##### TSRVA288.prw
- **User Function TSRVA288(oTree, cArq)**
  - **Parâmetros:**
    - oTree: Objeto árvore
    - cArq: Arquivo
  - **Descrição:** Gestão de documentos de milestone
  - **Retorno:** Nenhum

- **User Function FExiReg(cTipReg, lPosReg)**
  - **Parâmetros:**
    - cTipReg: Tipo de registro
    - lPosReg: Posicionar registro
  - **Retorno:** Lógico

- **User Function TSRV288A(oMdlRef, cFieRef)**
  - **Parâmetros:**
    - oMdlRef: Modelo de referência
    - cFieRef: Campo de referência
  - **Retorno:** Nenhum

- **User Function TS288PFE(cProjeto, cEDT)**
  - **Parâmetros:**
    - cProjeto: Código do projeto
    - cEDT: Código da EDT
  - **Retorno:** Numérico

- **User Function TS288DPF(nPerc)**
  - **Parâmetros:** nPerc: Percentual
  - **Retorno:** Data

- **User Function TS288UPF(cTipo, cProjeto, cEDT, cCodMil)**
  - **Parâmetros:**
    - cTipo: Tipo
    - cProjeto: Código do projeto
    - cEDT: Código da EDT
    - cCodMil: Código do milestone
  - **Retorno:** Nenhum

- **User Function TS288DTE(cProjeto, cEDT)**
  - **Parâmetros:**
    - cProjeto: Código do projeto
    - cEDT: Código da EDT
  - **Retorno:** Data

##### TSRVA289.prw
- **User Function TSRVA289(nTipExe)**
  - **Parâmetros:** nTipExe: Tipo de execução
  - **Descrição:** Execução específica de milestone
  - **Retorno:** Nenhum

##### TSRVA290.PRW
- **User Function TSRVA290()**
  - **Descrição:** Processamento de milestone
  - **Retorno:** Nenhum

##### TSRVA293.PRW
- **User Function TSRVA293()**
  - **Descrição:** Notificação de milestones
  - **Retorno:** Nenhum

- **User Function TS293Eml(cTab, cPapel)**
  - **Parâmetros:**
    - cTab: Tabela
    - cPapel: Papel do usuário
  - **Retorno:** Nenhum

##### TSRVJ271.prw
- **User Function TSRVJ271(aParExe)**
  - **Parâmetros:** aParExe: Parâmetros de execução
  - **Descrição:** Job de processamento de milestones
  - **Retorno:** Nenhum

#### Outros PMS
##### TSRVA300.PRW
- **User Function TSRVA300()**
  - **Descrição:** Cadastro de predecessoras
  - **Retorno:** Nenhum

##### TSRVA301.PRW
- **User Function TSRVA301()**
  - **Descrição:** Cadastro de recursos em tarefas
  - **Retorno:** Nenhum

##### TSRVA302.prw
- **User Function TSRVA302()**
  - **Descrição:** Função específica PMS
  - **Retorno:** Nenhum

##### TSRVR300.prw
- **User Function TSRVR300()**
  - **Descrição:** Relatório de apontamentos PMS
  - **Retorno:** Nenhum

- **User Function CpoHrPrj(cAlias, lQuery)**
  - **Parâmetros:**
    - cAlias: Alias da tabela
    - lQuery: Se é query
  - **Retorno:** Array

##### TSRVX003.PRW
- **User Function TSRVX003()**
  - **Descrição:** Estrutura temporária de projetos
  - **Retorno:** Nenhum

- **User Function StrTmpPrj()**
  - **Descrição:** Estrutura de projeto temporário
  - **Retorno:** Array

## Serviços PSA - Funções Específicas

### PSABXIDS.PRW
- **User Function psaTab()**
  - **Descrição:** Configuração de tabelas PSA
  - **Retorno:** Nenhum

- **User Function getPSA(cEntidade, oSay, lSA1_PFH, lJob)**
  - **Parâmetros:**
    - cEntidade: Entidade
    - oSay: Objeto de mensagem
    - lSA1_PFH: Flag SA1/PFH
    - lJob: Se é job
  - **Descrição:** Busca dados PSA
  - **Retorno:** Nenhum

- **User Function valUsrPSA()**
  - **Descrição:** Validação de usuário PSA
  - **Retorno:** Lógico

- **User Function setPartPSA(...)**
  - **Descrição:** Configuração de participante PSA
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

- **User Function xAtuRD0()**
  - **Descrição:** Atualização de RD0
  - **Retorno:** Nenhum

- **User Function xAtuRMRD0()**
  - **Descrição:** Atualização RM/RD0
  - **Retorno:** Nenhum

### PSAFUNC.PRW
- **User Function inJobPSA(...)**
  - **Descrição:** Execução de job PSA
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

- **User Function RetClass()**
  - **Descrição:** Retorna classe PSA
  - **Retorno:** String

- **User Function FindClass(cClasse)**
  - **Parâmetros:** cClasse: Nome da classe
  - **Retorno:** Lógico

- **User Function MoedaPSA(cCodPara, cCodMoeda, cEmpTotvs)**
  - **Parâmetros:**
    - cCodPara: Código para
    - cCodMoeda: Código da moeda
    - cEmpTotvs: Empresa TOTVS
  - **Retorno:** String

- **User Function FillCnta(oContato, aDados)**
  - **Parâmetros:**
    - oContato: Objeto contato
    - aDados: Array de dados
  - **Retorno:** Nenhum

- **User Function conPrjF3()**
  - **Descrição:** Consulta F3 de projetos
  - **Retorno:** String

- **User Function iPropPSA(cKey, cTipo, nQtd, lJob, cAmbPSA)**
  - **Parâmetros:**
    - cKey: Chave
    - cTipo: Tipo
    - nQtd: Quantidade
    - lJob: Se é job
    - cAmbPSA: Ambiente PSA
  - **Retorno:** Nenhum

- **User Function GPK5Terc(cFamilia)**
  - **Parâmetros:** cFamilia: Família
  - **Retorno:** String

- **User Function PSAContact(cCdLjCli, cCdContato, lSSIM)**
  - **Parâmetros:**
    - cCdLjCli: Código/Loja cliente
    - cCdContato: Código contato
    - lSSIM: Flag SSIM
  - **Retorno:** String

- **User Function upTabSrv(cTabela)**
  - **Parâmetros:** cTabela: Nome da tabela
  - **Retorno:** Nenhum

- **User Function GetPKUInf(cCCusto, cCargo, cTipRec)**
  - **Parâmetros:**
    - cCCusto: Centro de custo
    - cCargo: Cargo
    - cTipRec: Tipo de recurso
  - **Retorno:** Array

- **User Function PSANccInc(...)**
  - **Descrição:** Inclusão de nota de crédito PSA
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

- **User Function batPSX()**
  - **Descrição:** Processamento em lote PSX
  - **Retorno:** Nenhum

- **User Function numPKU()**
  - **Descrição:** Numeração PKU
  - **Retorno:** String

- **User Function delTempDb()**
  - **Descrição:** Exclusão de tabelas temporárias
  - **Retorno:** Nenhum

- **User Function siTitPSA(aEmp)**
  - **Parâmetros:** aEmp: Array de empresas
  - **Retorno:** Nenhum

- **User Function LimpaTxt(cTexto)**
  - **Parâmetros:** cTexto: Texto para limpeza
  - **Retorno:** String

- **User Function forceRDA(cIdRelDesp)**
  - **Parâmetros:** cIdRelDesp: ID do relatório de despesa
  - **Retorno:** Nenhum

- **User Function userBook()**
  - **Descrição:** Processamento de bookable resources
  - **Retorno:** Nenhum

- **User Function ISolViag(...)**
  - **Descrição:** Integração de solicitação de viagem
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

- **User Function SetFL6(cPartic, cCodPrjPSA, nFator)**
  - **Parâmetros:**
    - cPartic: Participante
    - cCodPrjPSA: Código projeto PSA
    - nFator: Fator
  - **Retorno:** Nenhum

- **User Function IPresCon(...)**
  - **Descrição:** Integração de prestação de contas
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

- **User Function SetFLE(cCodPrjPSA, nFator)**
  - **Parâmetros:**
    - cCodPrjPSA: Código projeto PSA
    - nFator: Fator
  - **Retorno:** Nenhum

- **User Function formatXML(cTextoOrig)**
  - **Parâmetros:** cTextoOrig: Texto original
  - **Retorno:** String

- **User Function cotacPSA(lStartJob, cPropModel, cPropModRv)**
  - **Parâmetros:**
    - lStartJob: Iniciar job
    - cPropModel: Modelo proposta
    - cPropModRv: Modelo revisão
  - **Retorno:** Nenhum

- **User Function IQPSA(lStartJob, cPropModel, cPropModRv, lSoValidar)**
  - **Parâmetros:**
    - lStartJob: Iniciar job
    - cPropModel: Modelo proposta
    - cPropModRv: Modelo revisão
    - lSoValidar: Só validar
  - **Retorno:** Nenhum

- **User Function RecnoPEM(cAliasPEM, nRecnoPEM, cMetodoPEM)**
  - **Parâmetros:**
    - cAliasPEM: Alias PEM
    - nRecnoPEM: Recno PEM
    - cMetodoPEM: Método PEM
  - **Retorno:** Nenhum

- **User Function setCCPKU()**
  - **Descrição:** Configuração centro de custo PKU
  - **Retorno:** Nenhum

- **User Function ccpku()**
  - **Descrição:** Centro de custo PKU
  - **Retorno:** Nenhum

- **User Function StsSysUs()**
  - **Descrição:** Status system users
  - **Retorno:** Nenhum

- **User Function buscaRD0()**
  - **Descrição:** Busca dados RD0
  - **Retorno:** Nenhum

- **User Function CFPPSAM(cPrj, cFrt)**
  - **Parâmetros:**
    - cPrj: Projeto
    - cFrt: Frente
  - **Retorno:** String

- **User Function PSACFPM(cPrjPSA)**
  - **Parâmetros:** cPrjPSA: Projeto PSA
  - **Retorno:** String

- **User Function IPlanViag(...)**
  - **Descrição:** Integração plano de viagem
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

- **User Function SetPZH(...)**
  - **Descrição:** Configuração PZH
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

- **User Function DescViag(cCodigo, cDescr, aDespFLG, lReserve)**
  - **Parâmetros:**
    - cCodigo: Código
    - cDescr: Descrição
    - aDespFLG: Array despesas
    - lReserve: Reserva
  - **Retorno:** Nenhum

- **User Function FlagPlan(nRecnoPZG, cPrjPSA, cIdSolic, cIdPrest)**
  - **Parâmetros:**
    - nRecnoPZG: Recno PZG
    - cPrjPSA: Projeto PSA
    - cIdSolic: ID solicitação
    - cIdPrest: ID prestação
  - **Retorno:** Nenhum

- **User Function IdDesPla(nRecnoPZG, aIDs)**
  - **Parâmetros:**
    - nRecnoPZG: Recno PZG
    - aIDs: Array de IDs
  - **Retorno:** Nenhum

- **User Function ajsf2()**
  - **Descrição:** Ajuste SF2
  - **Retorno:** Nenhum

- **User Function SetX5PSA(cModelo, cEnvServer)**
  - **Parâmetros:**
    - cModelo: Modelo
    - cEnvServer: Servidor
  - **Retorno:** Nenhum

- **User Function tstProp()**
  - **Descrição:** Teste de proposta
  - **Retorno:** Nenhum

- **User Function tstTrib()**
  - **Descrição:** Teste de tributação
  - **Retorno:** Nenhum

- **User Function pMed()**
  - **Descrição:** Processamento de medição
  - **Retorno:** Nenhum

- **User Function numAD1()**
  - **Descrição:** Numeração AD1
  - **Retorno:** String

- **User Function GSX6PSA()**
  - **Descrição:** Configuração SX6 PSA
  - **Retorno:** Nenhum

- **User Function GetAudit(cEmail, cNome)**
  - **Parâmetros:**
    - cEmail: E-mail
    - cNome: Nome
  - **Retorno:** Array

- **User Function pTstp(cBody)**
  - **Parâmetros:** cBody: Corpo da requisição
  - **Retorno:** Nenhum

- **User Function pTstu(cBody)**
  - **Parâmetros:** cBody: Corpo da requisição
  - **Retorno:** Nenhum

- **User Function xpToTab(cTabela, cDbNameHml, cDbSrvHml, nDbPortHml)**
  - **Parâmetros:**
    - cTabela: Tabela
    - cDbNameHml: Nome DB homologação
    - cDbSrvHml: Servidor DB homologação
    - nDbPortHml: Porta DB homologação
  - **Retorno:** Nenhum

- **User Function pToTab(oSay, cTabela, cDbNameHml, cDbSrvHml, nDbPortHml)**
  - **Parâmetros:**
    - oSay: Objeto mensagem
    - cTabela: Tabela
    - cDbNameHml: Nome DB homologação
    - cDbSrvHml: Servidor DB homologação
    - nDbPortHml: Porta DB homologação
  - **Retorno:** Nenhum

### PSAJ001.prw
- **User Function PSAJ001(aEmp, lJob, cEtapa, cAmbPSA)**
  - **Parâmetros:**
    - aEmp: Array empresas
    - lJob: Se é job
    - cEtapa: Etapa
    - cAmbPSA: Ambiente PSA
  - **Retorno:** Nenhum

- **User Function multUPSA(aRD0, cTipo, cAmbPSA)**
  - **Parâmetros:**
    - aRD0: Array RD0
    - cTipo: Tipo
    - cAmbPSA: Ambiente PSA
  - **Retorno:** Nenhum

### PSAJSY01.PRW
- **User Function PSAJSY01(cOpcx)**
  - **Parâmetros:** cOpcx: Opção
  - **Retorno:** Nenhum

- **User Function Psaj1Prc(...)**
  - **Descrição:** Processamento PSA
  - **Parâmetros:** Múltiplos parâmetros
  - **Retorno:** Nenhum

### PSARECUR.PRW
- **User Function PSARECUR()**
  - **Descrição:** Processamento de recursos PSA
  - **Retorno:** Nenhum

- **User Function PSADRECU()**
  - **Descrição:** Processamento de recursos PSA (alternativo)
  - **Retorno:** Nenhum

### PSATESTES.prw
- **User Function psaTestes()**
  - **Descrição:** Testes PSA
  - **Retorno:** Nenhum

- **User Function teste_parc()**
  - **Descrição:** Teste de parcelas
  - **Retorno:** Nenhum

- **User Function teste_entity()**
  - **Descrição:** Teste de entidades
  - **Retorno:** Nenhum

## Lei do Bem

### TSRVR392.PRW
- **User Function TSRVR392()**
  - **Descrição:** Relatório Lei do Bem - Detalhado
  - **Retorno:** Nenhum

### TSRVR393.PRW
- **User Function TSRVR393()**
  - **Descrição:** Relatório Lei do Bem - Sintético
  - **Retorno:** Nenhum

### TSRVR395.PRW
- **User Function TSRVR395()**
  - **Descrição:** Relatório Lei do Bem por Segmento
  - **Retorno:** Nenhum

### TSRVR396.PRW
- **User Function TSRVR396()**
  - **Descrição:** Relatório Lei do Bem - Incentivo
  - **Retorno:** Nenhum

### TSRVR397.PRW
- **User Function TSRVR397()**
  - **Descrição:** Relatório Lei do Bem - Participação Sintética
  - **Retorno:** Nenhum

### TSRVR398.PRW
- **User Function TSRVR398()**
  - **Descrição:** Relatório Lei do Bem - Participação Detalhada
  - **Retorno:** Nenhum

### TSRVR399.PRW
- **User Function TSRVR399(aParamWork)**
  - **Parâmetros:** aParamWork: Parâmetros de trabalho
  - **Descrição:** Processamento Lei do Bem
  - **Retorno:** Nenhum

### TSRVR399M.PRW
- **User Function TSRVR39M(aParamWork)**
  - **Parâmetros:** aParamWork: Parâmetros de trabalho
  - **Descrição:** Processamento Lei do Bem - Mensal
  - **Retorno:** Nenhum

- **User Function ProcLEIM(aParamWork)**
  - **Parâmetros:** aParamWork: Parâmetros de trabalho
  - **Descrição:** Processamento Lei do Bem - Integração
  - **Retorno:** Nenhum

---

## Índice de Busca Rápida por Funcionalidade

### Integração PSA
- TSRVA500 - TSRVA519: Funções de integração com PSA
- PSABXIDS, PSAFUNC, PSAJ001: Funções auxiliares PSA
- PSARECUR, PSATESTES: Recursos e testes PSA

### Relatórios
- TSRVR001 - TSRVR271: Relatórios diversos de serviços
- TSRVR392 - TSRVR399: Relatórios Lei do Bem

### Gestão de Projetos (PMS)
- TSRVA271 - TSRVA293: Milestones
- TSRVA300 - TSRVA302: Predecessoras e recursos
- TSRVR300, TSRVX003: Relatórios e estruturas PMS

### TPS (Tecnologia, Produtos e Serviços)
- TSRVA260 - TSRVA270: Cadastros e gestão TPS

### CRM e Vendas
- TCRMX233: Funções CRM
- ESPA050, ESPG020, ESPG050: EasySales
- TIPORV01, TIRVF000: Comissões RV

### Web Services
- TSRVS001, TSRVS004, TSRVS005: Web Services principais
- WRTotvsDigital: Integração digital
- WSGTOKEN: Geração de tokens

### Configuração e Administração
- TCFGW001: Configurador principal
- TGCTC002: Consulta de filiais
- TIITC001: Interceptor

### Pontos de Entrada
- TXGV02BP: Validação de funções
- F151BOR: Borderô cobrança
- F620QRY: Filtros de consulta
- CRMXFILPAP: Filtros CRM

---

## Convenções e Padrões

### Nomenclatura
1. **Prefixo T**: Todas as User Functions começam com "T" (TOTVS)
2. **Módulo**: 3 caracteres identificando o módulo (SRV=Serviços, CRM=CRM, etc.)
3. **Tipo**: 1 caractere para tipo (A=Cadastro, R=Relatório, X=Auxiliar, etc.)
4. **Sequencial**: 3 dígitos sequenciais

### Documentação TDoc
Muitas funções utilizam o padrão TDoc:
```advpl
/*/{Protheus.doc} NomeFuncao
Descrição da função
@type Function
<AUTHOR>
@since dd/mm/aaaa
@version versao
@param parametro, tipo, descrição
@return tipo, descrição
/*/
```

### Parâmetros Comuns
- **lJob**: Indica se a função está sendo executada via job
- **oSay**: Objeto para exibição de mensagens durante processamento
- **cEmp/cFil**: Empresa e filial para processamento
- **aEmp**: Array com dados de empresas para processamento multi-empresa

### Tipos de Retorno
- **Lógico**: .T./.F. para validações e confirmações
- **String**: Textos, códigos, filtros SQL
- **Array**: Listas de dados, estruturas complexas
- **Objeto**: Objetos JSON, classes específicas
- **Numérico**: Valores calculados, contadores
- **Nil**: Funções que não retornam valor

---

## Manutenção do Documento

### Como Atualizar
1. Execute o comando findstr para localizar novas User Functions:
   ```cmd
   findstr /s /i /n "^User Function" *.prw
   ```

2. Para cada nova função encontrada:
   - Identifique o módulo/diretório
   - Extraia a documentação TDoc se disponível
   - Identifique parâmetros e tipo de retorno
   - Adicione na seção apropriada

3. Mantenha a organização por módulos e ordem alfabética dentro de cada seção

### Controle de Versão
- **Versão 1.0**: Documentação inicial (05/08/2025)
- **Próximas versões**: Adicionar novas funções e melhorias na documentação

---

*Documento gerado automaticamente em: 05/08/2025*
*Total de User Functions catalogadas: 350+*
*Última atualização: 05/08/2025*
