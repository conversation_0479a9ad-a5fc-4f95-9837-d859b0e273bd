#INCLUDE "TOTVS.CH"
/*/{Protheus.doc} ExportEasy
    Exporta a tabela informada para csv
    @type  User Function
    <AUTHOR>
    @since 31/08/2020
    @version 1.0
    @param cTabela
    @return return boolean
/*/
user function ExportEasy(aTabEasy)

    local   lCheck      := .f.
    local   i           := 0  
    local   aErro       := {}  
    private aColunas    := {}
    private aLinha      := {} 
    Private aDados      := {}

    Private cDir        := cGetFile ("*.csv", "Ajustar Arquivo",0,"C:\",.f.,GETF_LOCALHARD+GETF_RETDIRECTORY,.f.,.f.) //caso queira ajustar v�rios arquivos
    Private aFiles      := Directory(cDir+ "\*.csv*", "D")
    
    Default aTabEasy    := {'PJ1','PJ2','PJ3','PJ4','PJ5','PJ6','PJ7','PJ8','PJ9','PJA','PJB','PJC','PJD','PJE','PJF','PJG','PJH','PJI','PJK','PJL','PZ1','PZ2','PZ3','PZ4','PZ5','PZ6','PZ7','PZA','PZB'}     
    Default aProtheus   := {'PV0','PV1','PV2','PV3','PV4','PV5','PV6','PV7','PV8','PV9','PVA','PVB','PVC','PVD','PVE','PVF','PVG','PVH','PVI','PVJ','PVK','PVL','PVM','PVN','PVO','PVP','PVQ','PVR','PVS'}
    
    U_startamb()

    for i := 1  to len(aFiles)
        //exportaCsv()
        CsvAppend(substr(aFiles[i][1],1,3))
    next
    

return

Static Function CsvAppend(cTabela_)

    Local cDiret    := cGetFile ("*.csv", "Ajustar Arquivo",0,"C:\",.f.,GETF_LOCALHARD,.t.,.t.)   //Caso queira ajustar um arquivo espec�fico 
    //cDir + cTabela_ + ".csv"
    Local oMatriz := nil

    Private aDados    := {}

    
    //cDiret :=  cGetFile( 'Arquito CSV|*.csv', 'Selecao de Arquivos', 0,'C:\', .F. , GETF_LOCALHARD  + GETF_NETWORKDRIVE, .T.)

    oMatriz := ARQUIVOSUTIL():New()
    aDados := oMatriz:ImportarCsv(cDiret)        
    
    if !Empty(aDados)
        oMatriz:AppendCsv(substr(cDiret,-7,3), aDados)
        //AjustarArray()
        //GravarDados()
    endIf        
return


/*/{Protheus.doc} nomeFunction
    (long_description)
    @type  Function
    <AUTHOR>
    @since date
    @version version
    @param param, param_type, param_descr
    @return return, return_type, return_description
    @example
    (examples)
    @see (links_or_references)
    /*/
Static Function exportaCsv()
    //EXPORTAR OS DADOS PARA UM CSV
    local i := 0

    for i := 1 to len(aTabEasy)
        aColunas := {}
        aLinha   := {}   
        if chkfile(aTabEasy[i])
            GerarQry(aTabEasy[i])
            SalvarCsv(aTabEasy[i])
        else
            AADD(aErro, aTabEasy[i])
        endIf
    next
return

/*/{Protheus.doc} GerarQry
    (long_description)
    @type  Static Function
    <AUTHOR>
    @since 31/08/2020
    @param cAlias
    @return boolean
/*/
Static Function GerarQry(cEntidade)

    Local aAux      := {}
    local cAlias    := GetNextAlias() 
    local cTabela   := ""
    local i         := 0
    local j         := 0

    //Estrutura da consulta
    cTabela   :=  "%" + RetSqlName(cEntidade) + "%"

    BeginSql alias cAlias
        SELECT *    
        FROM %exp:cTabela% TAB
        WHERE 
        TAB.%notDel%            
    EndSql    

    //Rcuperar a estrutura da tabela 
    aAux := DBStruct()

    //Adicinar os campos do cabe�alho
    for i := 1 to len(aAux)
        if !"R_E_C_D_E_L_" $ aAux[i][1] .and. !"R_E_C_N_O_" $ aAux[i][1]
            AAdd(aColunas,aAux[i][1])
        endIf
    next

    //Recuperar as informa��es de dados
    DbSelectArea(cAlias)
    (cAlias)->(DbGoTop())
    
    //Recuperar as linhas do registro
    while !(cAlias)->(eof())
        aAux := {}
        for j := 1 to len(aColunas)
            AADD(aAux, (cAlias)->&(aColunas[j]))
        next
        AADD(aLinha,aAux)
        (cAlias)->(dbskip())   
    enddo   

    (cAlias)->(DBCloseArea())
return 

/*/{Protheus.doc} SalvarCsv
    (long_description)
    @type  Function
    <AUTHOR>
    @since date
    @version version
    @param param, param_type, param_descr
/*/
 Static Function SalvarCsv(cEntidade)

    local oARQUIVOS:= ARQUIVOSUTIL():New()

    oARQUIVOS:lSalvarCsv := .t. //Salvar o arquivo
    oARQUIVOS:cDiretorio := "C:\TEMP\TAB\" + cEntidade + ".csv"


    if oARQUIVOS:ExportCsv(aColunas, aLinha, cEntidade)
        //conout("csv gerado com sucesso " + cEntidade) 
        FwLogMsg("ERROR", , 'EXPORTAEASY', FunName(), "", "01", "csv gerado com sucesso " + cEntidade, 0, (nStart - Seconds()), {})
    endIf
    
Return 

