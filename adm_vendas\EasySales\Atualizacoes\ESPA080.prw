#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'FWMVCDEF.CH'
#INCLUDE 'ESPA080.CH'

User Function ESPA080()
	
	Local aArea      := GetArea()
	
	Local oBrowse    := FWMBrowse():New()
	
	oBrowse:<PERSON><PERSON><PERSON><PERSON>("PVK")
	oBrowse:SetDescription(STR0001)
	oBrowse:Activate()
	RestArea(aArea)
	
Return

Static Function MenuDef()
	
	Local aRotina := {}
	
	ADD OPTION aRotina TITLE "Visualizar" ACTION "VIEWDEF.ESPA080" OPERATION MODEL_OPERATION_VIEW   ACCESS 0
	ADD OPTION aRotina TITLE "Incluir"    ACTION "VIEWDEF.ESPA080" OPERATION MODEL_OPERATION_INSERT ACCESS 0
	ADD OPTION aRotina TITLE "Alterar"    ACTION "VIEWDEF.ESPA080" OPERATION MODEL_OPERATION_UPDATE ACCESS 0
	ADD OPTION aRotina TITLE "Excluir"    ACTION "VIEWDEF.ESPA080" OPERATION MODEL_OPERATION_DELETE ACCESS 0
	
Return aRotina

Static Function ModelDef()
	
	Local oStrPVK := FWFormStruct(1, "PVK")
	Local oModel  := MPFormModel():New("PVKACE", , {|oModel | ValidCamp(oModel)})
	
	oModel:AddFields("PVKMASTER",, oStrPVK)
	oModel:SetPrimaryKey({"PVK_FILIAL", "PVK_PAIS", "PVK_CODACE"})
	oModel:SetDescription(STR0001)
	oModel:GetModel("PVKMASTER"):SetDescription(STR0001)
	
Return oModel

Static Function ViewDef()
	
	Local oModel  := ModelDef()
	Local oStrPVK := FWFormStruct(2, "PVK")
	Local oView   := FWFormView():New()
	
	oView:SetModel(oModel)
	oView:AddField("VIEW_PVK", oStrPVK, "PVKMASTER")
	oView:CreateHorizontalBox("PVK", 100)
	oView:EnableTitleView("VIEW_PVK",STR0002)
	oView:SetCloseOnOk({||.T.})
	oView:SetOwnerView("VIEW_PVK", "PVK")
	
Return oView

Static Function ValidCamp(oModel)
	
	Local aSavArea   := GetArea()
	
	Local lRet       := .T.
	
	Local cDir       := GetMV("FS_UPDSYS")
	
	Local oModelPVK  := oModel:GetModel("PVKMASTER")
	
	Local nOperation := oModel:GetOperation()
	
	If nOperation == 3 .OR. nOperation == 4
		If Select("SX3AC") > 0
			SX3AC->(DbCloseArea())
		EndIf
		
		//DbUseArea(.T., "CTREECDX", cDir + "sx3.dtc", "SX3AC", .T., .F.)
		USE (cDir + "sx3.dtc") ALIAS ("SX3AC") SHARED NEW VIA ("CTREECDX")
		DbSetIndex(cDir + "sx3.cdx")
		DbSetOrder(2)
		
		If !SX3AC->(DbSeek(oModelPVK:GetValue("PVK_CAMPO")))
			MsgAlert(STR0003)
			lRet := .F.
		EndIf
		
		If Select("SX3AC") > 0
			SX3AC->(DbCloseArea())
		EndIf
	EndIf
	
	RestArea(aSavArea)
	
Return lRet
