#include    "tlpp-core.th"



namespace Backoffice.Configurador.Gestao_Usuarios.Utils.GenerateJsonResponseUtils

using namespace Backoffice.Configurador.Gestao_Usuarios.Repository.CreateAreasOnDataBaseToConstructResponseRepository

/*/{Protheus.doc} GenerateJsonResponse
This class generates the JSON response required 
by the external system that invoked this routine
@type class
@version 1.0
<AUTHOR>
@since 10/31/2024
/*/
Class   GenerateJsonResponse
    Private Data    cTicket         As  Character
    Private Data    cIncomeJson     As  Character
    Private Data    cCodFor         As  Character
    Private Data    cLojFor         As  Character
    Private Data    cCodPart        As  Character
    Private Data    cGeneratedJson  As  Character
    Private Data    oJson           As  Json
    Private Data    oJsonUSR        As  Json
    Private Data    oBody           As  Json

    Public  Method  new(cParamJson As  Character, cParamCodFor  As  Character, cParamLojFor As  Character,  cParamCodPart    As  Character)
    Public  Method  getJsonToAnswer()   As  Character
    Public  Method  executeProcess()
    Private Method  processDataAndConstructJson()
    Private Method  ConstructAuxObjects()
    Private Method  setTicket()
EndClass

/*/{Protheus.doc} GenerateJsonResponse::new
This method aims to create a new instance of the object
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@param cParamJson, character, The income Json from the external system
@param cParamCodFor, character, The Code of the Supplier on SA2 Table
@param cParamLojFor, character, The Store of the Supplier on SA2 Table
@param cParamCodPart, character, The Code of the participant on RDO Table
@return Object, Return the instantiated object itself
/*/
Method  new(cParamJson As  Character, cParamCodFor  As  Character, cParamLojFor As  Character,  cParamCodPart    As  Character)   Class   GenerateJsonResponse
	::cTicket         :=    ''
	::cIncomeJson     :=    cParamJson
	::cCodFor         :=    cParamCodFor
	::cLojFor         :=    cParamLojFor
	::cCodPart        :=    cParamCodPart
	::cGeneratedJson  :=    ''
    ::oJson           := JsonObject():new()
    ::oJsonUSR        := JsonObject():new()
    ::oBody           := JsonObject():new() 
Return Self

/*/{Protheus.doc} GenerateJsonResponse::ConstructAuxObjects
Method to construct the the necessary auxiliary objects
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  ConstructAuxObjects()   Class   GenerateJsonResponse
    ::oBody     := ::oJson:FromJson(::cIncomeJson)
    ::oJsonUSR  := ::oJson:GetJsonObject("USR")
Return  Self

/*/{Protheus.doc} GenerateJsonResponse::setTicket
This method set the property cTicket of the class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the instantiated object itself
/*/
Method  setTicket()   Class   GenerateJsonResponse
    ::cTicket :=  ::oJson:GetJsonObject( "ticket" )
Return  Self

/*/{Protheus.doc} GenerateJsonResponse::processDataAndConstructJson
This method creates the Json of Response required by the external system that invoked this routine 
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the object instantiated itself
/*/
Method  processDataAndConstructJson()   Class   GenerateJsonResponse
    Local   nCpo        :=  0   As  Integer
    Local   cRetFun     :=  ''  As  Character
    Local   cValType    :=  ''  As  Character
    
    cRetFun += '{'
    cRetFun += ' "USR": {'
    cRetFun += ' "USR_CODIGO":"' +  Upper(::oJsonUSR:GetJsonObject( "USR_CODIGO"    ) ) + '",'
    cRetFun += ' "USR_NOME":"' +    Upper(::oJsonUSR:GetJsonObject( "USR_NOME"      ) ) + '",'
    cRetFun += ' "USR_EMAIL":"' +   Upper(::oJsonUSR:GetJsonObject( "USR_EMAIL"     ) ) + '",'
    cRetFun += ' "USR_GRUPO":"' +   Upper(::oJsonUSR:GetJsonObject( "USR_GRUPO"     ) ) + '",'
    cRetFun += ' "USR_CARGO":"' +   Upper(::oJsonUSR:GetJsonObject( "USR_CARGO"     ) ) + '"'
    cRetFun += '},'

    RD0->( dbSetOrder( 1 ) )
    If RD0->( dbSeek( xFilial( "RD0" ) + ::cCodPart ) )    

        cRetFun += ' "RD0": {'
        
        For nCpo := 1 To RD0->( FCount() )
            cValType    :=  ValType( RD0->( FieldGet( nCpo ) ) )
            Do  Case
                Case    cValType    $   'C|M'
                    cRetFun +=  '"'
                    cRetFun +=  AllTrim( RD0->( FieldName( nCpo ) ) )
                    cRetFun +=  '":"'
                    cRetFun += AllTrim( RD0->( FieldGet( nCpo ) ) )
                    If(nCpo != RD0->( FCount() ))
                        cRetFun += '",'
                    Else
                        cRetFun += '"'
                    EndIf
                Case    cValType    ==  'N'
                    cRetFun +=  '"'
                    cRetFun +=  AllTrim( RD0->( FieldName( nCpo ) ) )
                    cRetFun +=  '":'
                    cRetFun +=  AllTrim( Str( RD0->( FieldGet( nCpo ) ) ) )
                    If( nCpo != RD0->( FCount() ))
                        cRetFun +=  ','
                    Else
                        cRetFun +=  ''
                    EndIf
                Case    cValType    ==  'D'
                    cRetFun +=  '"' 
                    cRetFun +=  '":"'
                    cRetFun +=  AllTrim( RD0->( FieldName( nCpo ) ) )
                    cRetFun +=  DtoC( RD0->( FieldGet( nCpo ) )  )
                    If  (nCpo != RD0->( FCount() ))
                        cRetFun +=  '",'
                    Else
                        cRetFun +=  '"' 
                    EndIf
                OtherWise
            EndCase
        Next nCpo

        cRetFun += '},'
        cRetFun += ' "PSA": {'
        cRetFun += ' "PSA_statecode":"' 
        If(RD0->RD0_MSBLQL != "1")
            cRetFun +=  "0"
        Else
            cRetFun +=  "1"
        EndIf
        cRetFun +=  '",'
        cRetFun += ' "PSA_statuscode":"'
        If(RD0->RD0_MSBLQL != "1")
            cRetFun +=  "1"
        Else
            cRetFun += "2"
        EndIf
        cRetFun +=  '",'
        cRetFun += ' "PSA_viagemnacional":"'
        If  RD0->RD0_DVIAGE $ "2|3|4"
            cRetFun +=  "true"
        Else
            cRetFun +=  "false"
        EndIf
        cRetFun +=  '",'
        cRetFun += ' "PSA_viageminternacional":"'
        If(RD0->RD0_DVIAGE = "4")
            cRetFun +=  "true"
        Else
            cRetFun +=  "false"
        EndIf
        cRetFun +=  '",'
        cRetFun += ' "PSA_EMPFILIAL":"' + RD0->RD0_EMPATU + RD0->RD0_FILATU + '",'
        cRetFun += ' "PSA_FORNCLOJA":"' + ::cCodFor + ::cLojFor + '"'
        cRetFun += '},'
    Endif

    SA2->( dbSetOrder( 1 ) )
    If SA2->( dbSeek( xFilial( "SA2" ) + ::cCodFor + ::cLojFor ) )
        cRetFun += ' "SA2": {'
        For nCpo := 1 To SA2->( FCount() )
            cValType    :=   ValType( SA2->( FieldGet( nCpo ) ) )
            Do  Case
                Case    cValType    $       "C|M"
                    cRetFun +=  '"'
                    cRetFun +=  AllTrim( SA2->( FieldName( nCpo ) ) )
                    cRetFun +=  '":"'
                    cRetFun +=  AllTrim( SA2->( FieldGet( nCpo ) ) )
                    If  nCpo != SA2->( FCount() )
                        cRetFun += '",'
                    Else
                        cRetFun += '"'
                    EndIf
                Case    cValType    ==      "N"
                    cRetFun +=  '"'
                    cRetFun +=  AllTrim( SA2->( FieldName( nCpo ) ) )
                    cRetFun +=  '":'
                    cRetFun +=  AllTrim( Str( SA2->( FieldGet( nCpo ) ) ) )
                    If  nCpo != SA2->( FCount() )
                        cRetFun +=  ','
                    Else
                        cRetFun +=  ''
                    EndIf   
                Case    cValType    ==      "D"
                    cRetFun +=  '"'
                    cRetFun +=  AllTrim( SA2->( FieldName( nCpo ) ) )
                    cRetFun +=  '":"'
                    cRetFun +=  DtoC( SA2->( FieldGet( nCpo ) )  )
                    If  (nCpo != SA2->( FCount() ))
                        cRetFun     +=  '",'
                    Else
                        cRetFun     +=  '"'
                    EndIf
                OtherWise
            EndCase
        Next nCpo
        cRetFun += '}'
    Endif
    cRetFun += '}'
    
    ::cGeneratedJson    :=  cRetFun
Return  Self

/*/{Protheus.doc} GenerateJsonResponse::getJsonToAnswer
This method aims to get the Json generated in this class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return character, The generated character json object
/*/
Method  getJsonToAnswer()   As  Character   Class   GenerateJsonResponse
Return  ::cGeneratedJson

/*/{Protheus.doc} GenerateJsonResponse::executeProcess
This method aims to coordinate the process to create a json to response
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Object, Return the object instantiated itself
/*/
Method  executeProcess()    Class   GenerateJsonResponse
    Local oAreas    As  Object
    
    oAreas  :=  CreateAreasOnDataBaseToConstructResponse():new()
    ::ConstructAuxObjects()
    //::setTicket()
    ::processDataAndConstructJson()
    oAreas:destroy()
Return  Self
