#Include 'Protheus.ch'
#Include 'FWMVCDef.ch'
#Include 'TFINA785.CH'

Static cRet785F3	:= ""
Static nLenSx8		:= 0
Static __nOper		:= 0
Static lPerg		:= .F.
Static lCanceled	:= .F.
Static aSelFil		:= {}

//-------------------------------------------------------------------
/*/{Protheus.doc} FINA785
Rotina respons�vel pelo processo de Solicita��o de Aprova��o do Border� de Pagamento

<AUTHOR>
@since 16/06/2015
@version 12.1.6
/*/
//-------------------------------------------------------------------
User Function TFINA785() //FINRADU()p
Local aArea   := GetArea()
Local cPermite	:= GETNEWPAR("MV_CTAPBD","2")
Local oBrowse	:= Nil
Local oDlg		:= Nil
Private vBtn785C := .T.

If cPermite == "1"
	oBrowse := FWMBrowse():New()
	oBrowse:setAlias("PRO") // Cabe�alho do Border� de Pagamento
	oBrowse:SetDescription(STR0001) // "Solicita��o de Aprova��o de Border� de Pagamento"

	ADD LEGEND DATA {|| PRO_STATUS  == '2'} COLOR "BLUE"	TITLE	STR0002 Of oBrowse // "Em An�lise"
	ADD LEGEND DATA {|| PRO_STATUS  == '1'} COLOR "YELLOW"	TITLE	STR0003 Of oBrowse // "Solicitado"
	ADD LEGEND DATA {|| PRO_STATUS  == '3'} COLOR "GREEN"	TITLE	STR0004 Of oBrowse // "Totalmente Aprovado"
	ADD LEGEND DATA {|| PRO_STATUS  == '4'} COLOR "RED"		TITLE	STR0054 Of oBrowse // "Cancelado"
	ADD LEGEND DATA {|| PRO_STATUS  == '5'} COLOR "ORANGE"	TITLE	STR0065 Of oBrowse // "Totalmente Reprovado"
	ADD LEGEND DATA {|| PRO_STATUS  == '6'} COLOR "PINK"	TITLE	STR0066 Of oBrowse // "Finalizado com Reprova��es"

	//oBrowse:SetMenuDef("TFINA785")
	oBrowse:Activate()
Else
	Help(" ",1,"SOLAPROVBOR",, STR0006 + CRLF + STR0007,3,1) //"Processo de Solicita��o de Aprova��o de Border� n�o dispon�vel."//"Verificar o conte�d do par�metro MV_CTAPBD."
EndIf


RestArea(aArea)
Return Nil




//-------------------------------------------------------------------
/*/{Protheus.doc} MenuDef
Defini��o do menu da tela da rotina de Solicita��o de Aprova��o de Border� de Pagamento

<AUTHOR> Ara�jo Silva
@since 16/06/2015
@version 12.1.6
@return aRotMenu Array com as configura��es das op��es do menu da rotina
/*/
//-------------------------------------------------------------------

Static Function MenuDef()

Local aRotMenu := {}

ADD OPTION aRotMenu TITLE OemToAnsi(STR0015)	ACTION "VIEWDEF.TFINA785"		OPERATION MODEL_OPERATION_VIEW ACCESS 0 //"Visualizar"
ADD OPTION aRotMenu TITLE OemToAnsi(STR0016)	ACTION "VIEWDEF.TFINA785"		OPERATION MODEL_OPERATION_INSERT ACCESS 0 //"Incluir"
ADD OPTION aRotMenu TITLE OemToAnsi(STR0017)	ACTION "VIEWDEF.TFINA785"		OPERATION MODEL_OPERATION_UPDATE ACCESS 0 //"Alterar"
ADD OPTION aRotMenu TITLE OemToAnsi(STR0018)	ACTION "VIEWDEF.TFINA785"		OPERATION MODEL_OPERATION_DELETE ACCESS 0 //"Excluir"
ADD OPTION aRotMenu TITLE OemToAnsi(STR0019)	ACTION "U_F785LG()"			OPERATION 9 ACCESS 0 //"Transa��es"
ADD OPTION aRotMenu TITLE OemToAnsi(STR0020)	ACTION "VIEWDEF.TFINA785"	OPERATION 8 ACCESS 0 //"Imprimir"
	
Return aRotMenu


//-------------------------------------------------------------------
/*/{Protheus.doc} ModelDef
Func�o respons�vel pela gera��o do modelo de dados da rotina.

<AUTHOR> Ara�jo Silva
@since 16/06/2015
@version 12.1.6
@return oModel Retorna o objeto do modelo de dados da rotina de solicita��o de aprova��o de border� de pagamento

/*/
//-------------------------------------------------------------------

Static Function ModelDef()
Local oModel
Local oStruPRO	:= FWFormStruct(1, 'PRO')
Local oStruPSO	:= FWFormStruct(1, 'PSO')
Local oStruPSB	:= FWFormStruct(1, 'PSB',,.T.)
Local cFRWFil	:= FWxFilial('FRW')
Local cTamNome	:= CVALTOCHAR(TamSX3('PSO_NOMAPR')[1])
Local bCancel 	:= {|oModel| FwFormCancel(oModel), vBtn785C := .T.}

//oModel := MPFormModel():New('MFINA590', /*bPre / , /*bPost /, /*bCommit*/, bCancel )
oModel	:= MPFormModel():New('MFIN785',/*bPre*/,{|oModel| U_F785Vd(oModel) }/*bPos*/,{|oModel| U_F785GRV(oModel) }/*bCommit*/,bCancel)

/*
 * Adicional campo OK para controle da opera��o
 */


oStruPSB:AddField("","" , 'OK', 'L', 1, 0, { |oModel| U_AF785MA(oModel) } , , {}, .F., , .F., .F., .F., , )
oStruPSB:SetProperty('OK', MODEL_FIELD_INIT, FWBuildFeature( STRUCT_FEATURE_INIPAD, "Iif(!INCLUI,.T.,.F.)" ))
oStruPSB:AddField("Status","Status" , 'COR', 'BT',15,,,,,,{ |oModel| U_AF785CO(oModel:GetValue("PSB_STATUS")) },,.F.,.T. )
oStruPSB:SetProperty('COR', MODEL_FIELD_INIT, FWBuildFeature( STRUCT_FEATURE_INIPAD, "U_AF785CO(Iif(INCLUI, '1',PSB->PSB_STATUS))" ))

oStruPSB:AddField(STR0072,STR0072, 'PSB_NOMEFIL', 'C',41,,,,,,,,.F.,.T. ) //"Nome Filial"//"Nome Filial"
oStruPSB:SetProperty('PSB_NOMEFIL', MODEL_FIELD_INIT, FWBuildFeature( STRUCT_FEATURE_INIPAD, "AllTrim(FWFilialName(/*Grupo*/,PSB->PSB_FILORI,1))" ) )

oStruPSO:AddTrigger('PSO_PAPEL','PSO_DSCPAP',/*bPre*/,{ |oModel| POSICIONE('FRW', 1, cFRWFil + oModel:GetValue('PSO_PAPEL') , 'FRW_DESCR')})
oStruPSO:SetProperty('PSO_DSCPAP', MODEL_FIELD_INIT, FWBuildFeature( STRUCT_FEATURE_INIPAD, "IIF(!INCLUI,POSICIONE('FRW', 1, '" + cFRWFil + "' + PSO->PSO_PAPEL, 'FRW_DESCR'),'')" ))

oStruPSO:AddTrigger('PSO_CODAPR','PSO_NOMAPR',/*bPre*/,{ |oModel| SubStr(USRFULLNAME(oModel:GetValue('PSO_CODAPR')) ,1,VAL(cTamNome))  })
oStruPSO:SetProperty('PSO_NOMAPR', MODEL_FIELD_INIT, FWBuildFeature( STRUCT_FEATURE_INIPAD, "Iif(!INCLUI, SubStr(USRFULLNAME(PSO->PSO_CODAPR),1," + cTamNome + "),'')" ))


If oModel:GetOperation() == MODEL_OPERATION_VIEW
	oStruPSB:SetProperty('PSB_URNOME', MODEL_FIELD_INIT, FWBuildFeature( STRUCT_FEATURE_INIPAD, "SubStr(UsrFullName(PSB->PSB_USUCRI),1,TamSX3('PSB_URNOME')[1])" ))
EndIf

oModel:SetDescription(STR0008) // "Rotina de Solicita��o de Aprova��o de Border� de Pagamento"
oModel:Addfields("CABSOL",/*cOwner*/,oStruPRO/*oStruct*/,/*bPre*/,/*bPost*/,/*bLoad*/)
oModel:GetModel("CABSOL"):SetDescription(STR0009) // "Cabe�alho da Solicita��o de Aprova��o de Border� de Pagamento"

oModel:AddGrid("CABBOR","CABSOL",oStruPSB)
oModel:GetModel("CABBOR"):SetDescription(STR0010) // "Cabe�alho do Border� de Pagamento"
oModel:AddGrid("ITEMAPV","CABSOL",oStruPSO)
oModel:GetModel("ITEMAPV"):SetDescription(STR0011) // "Aprovadores do Border� de Pagamento"

oModel:SetPrimaryKey({})

oModel:SetRelation('CABBOR'	,{ {'PSB_PROAPR','PRO_CODIGO'} }, PSB->(IndexKey(1)) )
oModel:SetRelation('ITEMAPV',{ {'PSO_FILIAL','XFILIAL("PSO")'}, {'PSO_CODIGO','PRO_CODIGO'}  }, PSO->(IndexKey(1)) )

oModel:SetOnlyQuery('CABBOR',.T.)

oModel:SetVldActivate( {|oModel| U_F785VdP(oModel) } )
oModel:SetActivate( {|oModel| U_F785Loa(oModel) } )


//oModel:SetDeActivate( {|oModel| TF590End(oModel) } )

oModel:GetModel("ITEMAPV"):SetNoDeleteLine(.T.)
oModel:GetModel("CABBOR"):SetNoInsertLine(.F.)

Return oModel

//-------------------------------------------------------------------
/*/{Protheus.doc} ViewDef
Funcao repons�vel pela View

<AUTHOR> Ara�jo Silva
@since 16/06/2015
@version 12.1.6
@return oView Retorna o objeto da interface da rotina de solicita��o de aprova��o de border� de pagamento

/*/
//-------------------------------------------------------------------
Static Function ViewDef()

	Local oView		:= Nil
	Local oModel	:= FWLoadModel('TFINA785') //ModelDef()//FWLoadModel('CUICA')
	Local oStruPSB	:= FWFormStruct(2, 'PSB',,.T.)
	Local oStruPSO	:= FWFormStruct(2, 'PSO')
	Local oStruPRO	:= FWFormStruct(2, 'PRO')
	Local nCpo		:= 0
	Local lPerg
	Local aSelFil := {}
	Local nOperation	:= oModel:GetOperation()

	//IF MODEL_OPERATION_INSERT
	// If INCLUI
	// 	lPerg := Pergunte("TFINA785",.t.)

	// 	If lPerg
			
	// 		aSelFil := {}
			
	// 		If MV_PAR01 == 1
			
	// 			aSelFil := AdmGetFil()
				
	// 			If Len( aSelFil ) <= 0
	// 				lRet := .F.
	// 			EndIf

	// 		EndIf
	
	// 		cIniBor	:= MV_PAR02
	// 		cFimBor	:= MV_PAR03
	// 		lRet := .t.
	// 	Else
	// 		lRet := .F.
	// 	EndIf

	// Endif



oStruPSB:RemoveField("PSB_USNOME")
oStruPSB:RemoveField("PSB_PROAPR")
oStruPSB:RemoveField("PSB_FILPRO")
oStruPSB:RemoveField("PSB_USUSOL")
oStruPSB:RemoveField("PSB_RECPAG")
oStruPSB:RemoveField("PSB_SITBRD")
oStruPSB:RemoveField("PSB_STATUS")
oStruPSB:RemoveField("PSB_SITUAC")

oStruPSO:RemoveField("PSO_CODIGO")

oStruPRO:RemoveField("PRO_WFNCAR")

For nCpo := 1 To Len(oStruPSB:aFields)
	oStruPSB:SetProperty( oStruPSB:aFields[nCpo][1] , MVC_VIEW_CANCHANGE, .F. )
Next nCpo

/*
 * Adiciona Campos Virtuais
 */
oStruPSB:AddField( 'OK','00'," "," ",, 'Check' ,,,,,,,,,,,, )
oStruPSB:AddField( 'COR','01',STR0012,STR0012,,'BC','@BMP',,,.T. ) // "Status"// "Status"
oStruPSB:AddField( 'PSB_NOMEFIL','23',STR0072,STR0072,,"C","@!",,,.T. ) //"Nome Filial"//"Nome Filial"

oView := FWFormView():New()
oView:SetModel(oModel)
/*
 * Cabe�alho da Solicita��o de Aprova��o de Border� de Pagamento
 */
oView:AddField("VCABSOL",oStruPRO,"CABSOL")
oView:EnableTitle('VCABSOL',STR0001) // "Solicita��o de Aprova��o de Border� de Pagamento"
/*
 * Grid de Cabe�alho de Border� de Pagamento
 */      
oView:AddGrid("VCABBOR",oStruPSB,"CABBOR")
oView:EnableTitle('VCABBOR',STR0013) // "Border�s de Pagamento"
/*
 * Grid de Cabe�alho de Border� de Pagamento
 */      
oView:AddGrid("VITEMAPV",oStruPSO,"ITEMAPV")
oView:EnableTitle('VITEMAPV',STR0014) // "Gestores"
oView:AddIncrementField( 'VITEMAPV', 'PSO_ITEM')

oView:AddOtherObject('VBOTOES', {|oPanel,oView| U_F785Bot(oPanel,oView)})

oView:CreateHorizontalBox( 'SUPERIOR'	, 33)
oView:CreateHorizontalBox( 'BORDERO'	, 34)
oView:CreateHorizontalBox( 'BOTOES'		, 10)
oView:CreateHorizontalBox( 'APROVADOR'	, 23)

oView:SetOwnerView( 'VCABSOL'	, 'SUPERIOR')
oView:SetOwnerView( 'VCABBOR'	, 'BORDERO')
oView:SetOwnerView( 'VBOTOES'	, 'BOTOES')
oView:SetOwnerView( 'VITEMAPV'	, 'APROVADOR')

oView:AddUserButton(STR0055, STR0055, {|oView| U_AF785lg() } ) //'Legenda'//'Legenda'

oView:SetCloseOnOk( {|| .T. } )

oView:SetAfterViewActivate({|oView| U_F785VdAf(oView)})
//oView:SetAfterViewActivate({|oView| Iif(!lCanceled,U_F785VdAf(oView),.f.)})

If !MODEL_OPERATION_INSERT  .And. PRO->PRO_STATUS == "4"
	oView:ShowUpdateMsg(.F.)
	oView:GetModel("CABBOR"):SetOnlyView(.T.)
	
    oModel:SetRelation('CABBOR'	,{}, PSB->(IndexKey(1)) )
	oView:GetModel("ITEMAPV"):SetOnlyView(.T.)
	oStruPSB:SetProperty("OK", MVC_VIEW_CANCHANGE, .F. )
	oModel:bCommit := {|| .T.}
    
Endif 







Return oView

Static Function  FCan785()



	vBtn785C := .F.

Return .t.

Static Function  FOk785()

	vBtn785C := .T.

Return .t.



//-------------------------------------------------------------------
/*/{Protheus.doc} PROCodigo
Funcao repons�vel pela identifica��o do n�mero do processo da rotina de solicita��o de aprova��o do border� de pagamento

<AUTHOR> Ara�jo Silva
@since 16/06/2015
@version 12.1.6
@return cIdProc Retorna a identifica��o do processo

/*/
//-------------------------------------------------------------------
User Function PROCod()

Local cIdProc	:= GetSx8Num("PRO","PRO_CODIGO","PRO_CODIGO",1)
Local nSaveSx8	:= GetSX8Len()  //Vari�vel est�tica

PRO->(dbSetOrder(1)) //PRO_FILIAL + PRO_CODIGO

While PRO->(MsSeek( FWxFilial("PRO") + cIdProc ))
	If ( __lSx8 )
		ConfirmSX8()
	EndIf
	nLenSx8 := GetSX8Len()
	cIdProc := GetSx8Num("PRO","PRO_CODIGO","PRO_CODIGO",1)
EndDo

Return cIdProc

//-------------------------------------------------------------------
/*/{Protheus.doc} AF785MAK

Fun��o que retorna a carga da grid de Tipos de Ativos

<AUTHOR> Ara�jo Silva
@since 18/06/2015
@version 12.1.6
@return lRet Retorna se o campo deve ser marcado ou n�o
/*/
//-------------------------------------------------------------------

User Function AF785MA(oModel)
Local lRet 	:= .T.
Return lRet			

//-------------------------------------------------------------------
/*/{Protheus.doc} AF785CO

Fun��o que retorna a carga da grid de Border�s de Pagamento

<AUTHOR> Ara�jo Silva
@since 24/06/2015
@version 12.1.6
@param cStatus Situa��o do Border� de Pagamento
@return cCor Cor da Legenda do Status do border� de pagamento
/*/
//-------------------------------------------------------------------

User Function AF785CO(cStatus,cBordero,cVersao,cProcesso)

	Local cCor		:= ""
	Local cChave	:= ""

	DEFAULT cStatus		:= ""
	DEFAULT cBordero	:= ""
	DEFAULT cVersao		:= ""
	DEFAULT cProcesso	:= ""

	PSP->(DbSetOrder(2))

	cChave := xFilial("PSP")
	cChave += PadR(cBordero,TamSx3("PSP_BORDER")[1])
	cChave += PadR(cVersao,TamSx3("PSP_VERSAO")[1])
	cChave += PadR(cProcesso,TamSx3("PSP_PROAPR")[1])

	IF PSP->(MsSeek(cChave))
		If PSP->PSP_MOVIME == '2'
			cStatus := "5" // "Reprovado"
		Endif
	Endif
	
	If cStatus == "1" .OR. EMPTY(cStatus)//"Bloqueado"
		cCor := "BR_BRANCO"
	ElseIf cStatus == "2" //"Aguardando Aprova��o"
		cCor := "BR_AMARELO"
	ElseIf cStatus == "3" //"Aprovado Parcialmente"
		cCor := "BR_LARANJA"
	ElseIf cStatus == "4" //"Aprovado"
		cCor := "BR_VERDE"
	ElseIf cStatus == "5" // "Reprovado"
		cCor := "BR_VERMELHO"
	EndIf

Return cCor			

//-------------------------------------------------------------------
/*/{Protheus.doc} FIN785Load

Carrega os dados da tela de solicita��o de aprova��o de border� de pagamento

<AUTHOR> Ara�jo Silva
@since 17/06/2015
@version 12.1.6
@param oModel Objeto do Modelo de Dados da Rotina de Solicita��o de Aprova��o de Border� de Pagamento
/*/
//-------------------------------------------------------------------

User Function F785Loa(oModel)    //FIN785Load

	Local nOperation	:= oModel:GetOperation()
	Local oPROMdl		:= oModel:GetModel("CABSOL")
	Local oPSBMdl		:= oModel:GetModel("CABBOR")
	
	Local cFilPRO		:= FwxFilial("PRO")
	Local cIniBor		:= ""
	Local cFimBor		:= ""
	
	Local nPSB			:= 0

	Local lRet			:= .T.
	Local lShowPerg		:= IsInCallStack("U_TFINA785")
	Local aSelFil		:= {}
	

	If ( nOperation == MODEL_OPERATION_INSERT .Or. nOperation == MODEL_OPERATION_UPDATE )

		lPerg := Pergunte("TFINA785",lShowPerg)

		If lPerg
			
			aSelFil := {}
			
			If MV_PAR01 == 1
			
				aSelFil := AdmGetFil()
				
				If Len( aSelFil ) <= 0
					lRet := .F.
				EndIf

			EndIf
		
		EndIf
		
		cIniBor	:= MV_PAR02
		cFimBor	:= MV_PAR03
		
		/*
			* Carrega os dados da solicita��o de aprova��o de border� de pagamento
		*/
		
		If ( nOperation == MODEL_OPERATION_INSERT )
		
			oPROMdl:SetValue("PRO_FILIAL"	,cFilPRO)
			oPROMdl:SetValue("PRO_CODIGO"	,U_PROCod())
			oPROMdl:SetValue("PRO_DATA"		,DDATABASE)
			oPROMdl:SetValue("PRO_USUCRI"	,AllTrim(RetCodUsr()))
			oPROMdl:SetValue("PRO_USNOME"	,AllTrim(cUserName))
			oPROMdl:SetValue("PRO_HORA"		,SUBSTR(Time(),1,5))
			oPROMdl:SetValue("PRO_STATUS"	,CriaVar("PRO_STATUS"))
		
		EndIf
		/*
			* Carrega os border�s de acordo com os dados preenchidos no pergunte FINA785
		*/
		
		lRet := U_FI785LB(oModel,cIniBor,cFimBor,aSelFil)

	// Else
		
	// 	If ( nOperation == 4 )
		
	// 		If ( PRO->PRO_STATUS $ "4|5|6" )
	// 			lRet := U_FI785LB(oModel,cIniBor,cFimBor,aSelFil,.T.)
	// 		Endif

	// 	EndIf

	EndIf	

	If ( lRet )

		If __nOper == 6 .AND. nOperation == 4
			oModel:GetModel("CABBOR"):SetNoInsertLine() 
			oPROMdl:SetValue("PRO_STATUS","4")
		EndIf

	EndIf

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} FI785LB

Carrega os dados da Se��o de Border�s da tela de solicita��o de aprova��o de border� de pagamento

<AUTHOR> Ara�jo Silva
@since 17/06/2015
@version 12.1.6
@param oModel Objeto do Modelo de Dados da Rotina de Solicita��o de Aprova��o de Border� de Pagamento
/*/
//-------------------------------------------------------------------

User Function FI785LB(oModel,dIniBor,dFimBor,aSelFil)    //FI785LB - FI785LB(oModel,dIniBor,dFimBor,aSelFil,lFO5,cOpc)

	Local oPSBMdl		:= oModel:GetModel("CABBOR")

	Local lRet			:= .T.

	Local cPSBAls		:= GetNextAlias()
	Local cPSBQry		:= ""
	
	Local nOperation	:= oModel:GetOperation()

	Local aArea			:= {}

	aArea := GetArea()

	oPSBMdl:SetNoInsertLine(.F.)

	cPSBQry := " SELECT " + CRLF
	cPSBQry += "	PSB.PSB_FILIAL 	" + CRLF
	cPSBQry += "	,PSB.PSB_FILORI " + CRLF
	cPSBQry += "	,PSB.PSB_BORDER " + CRLF
	cPSBQry += "	,PSB.PSB_BANCO 	" + CRLF
	cPSBQry += "	,PSB.PSB_AGENCI	" + CRLF
	cPSBQry += "	,PSB.PSB_DVAGE 	" + CRLF
	cPSBQry += "	,PSB.PSB_CONTA 	" + CRLF
	cPSBQry += "	,PSB.PSB_DVCTA 	" + CRLF
	cPSBQry += "	,PSB.PSB_DATA 	" + CRLF
	cPSBQry += "	,PSB.PSB_TIPOPG " + CRLF
	cPSBQry += "	,PSB.PSB_TOTAL 	" + CRLF
	cPSBQry += "	,PSB.PSB_USUCRI	" + CRLF
	cPSBQry += "	,PSB.PSB_STATUS	" + CRLF
	cPSBQry += "	,PSB.PSB_MODELO	" + CRLF
	cPSBQry += "	,PSB.PSB_SITUAC	" + CRLF
	cPSBQry += "	,PSB.PSB_VERSAO	" + CRLF
	cPSBQry += "	,PSB.PSB_SITBRD	" + CRLF
	cPSBQry += "	,PSB.PSB_RECPAG	" + CRLF
	cPSBQry += "FROM " + RetSqlName("PSB") + " PSB " + CRLF
	cPSBQry += "WHERE " + CRLF

	If Len(aSelFil) > 0
		cPSBQry += "	PSB.PSB_FILIAL " + GetRngFil( aSelFil, "PSB" ) + " " + CRLF		
	Else	
		cPSBQry += "	PSB.PSB_FILIAL = '" + FWxFilial("PSB") + "' " + CRLF
	EndIf
	
	cPSBQry += "	AND PSB.PSB_RECPAG = 'P' " + CRLF
	cPSBQry += "	AND PSB.PSB_SITBRD = '1' " + CRLF
	cPSBQry += "	AND PSB.PSB_DATA BETWEEN '" + DTOS(dIniBor) + "' AND '" + DTOS(dFimBor) + "' " + CRLF
		
	If ( nOperation == MODEL_OPERATION_INSERT )
	
		If ( mv_par04 == 1 ) 
			cPSBQry += "	AND PSB.PSB_STATUS IN ('1','5') " + CRLF
		Else
			cPSBQry += "	AND PSB.PSB_STATUS = '1' " + CRLF
		Endif

	EndIf

	cPSBQry += "	AND PSB.D_E_L_E_T_ = ' ' " + CRLF
	
	cPSBQry := ChangeQuery(cPSBQry)
	
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cPSBQry),cPSBAls,.T.,.T.)
	TcSetField(cPSBAls,'PSB_DATA','D')

	While (cPSBAls)->(!Eof())
		
		If ( nOperation <> MODEL_OPERATION_INSERT )
			lRet := oPSBMdl:SeekLine({{"PSB_FILIAL",(cPSBAls)->PSB_FILIAL},{"PSB_BORDER",(cPSBAls)->PSB_BORDER},;
									{"PSB_RECPAG",(cPSBAls)->PSB_RECPAG},{"PSB_VERSAO",(cPSBAls)->PSB_VERSAO}})
		Else
			
			If ( !Empty(oPSBMdl:GetValue("PSB_BORDER")) )
				lRet := oPSBMdl:Length() < oPSBMdl:AddLine()
			EndIf
				
		Endif
		
		If ( lRet )

			If ( nOperation != MODEL_OPERATION_INSERT )
				//Visualiza��o de solicita��o de aprova��o Cancelada, Totalmente Reprovada ou Finalizada com reprova��es
				cCor := U_AF785CO((cPSBAls)->PSB_STATUS,(cPSBAls)->PSB_BORDER,(cPSBAls)->PSB_VERSAO,PRO->PRO_CODIGO) 
				oPSBMdl:LoadValue("COR",cCor)
				oPSBMdl:LoadValue("OK",.T.)
			Else
				oPSBMdl:LoadValue("COR",U_AF785CO((cPSBAls)->PSB_STATUS))
			EndIf
			
			lRet := oPSBMdl:LoadValue("PSB_FILIAL"	,(cPSBAls)->PSB_FILIAL) .And.;
					oPSBMdl:LoadValue("PSB_FILORI"	,(cPSBAls)->PSB_FILORI) .And.;
					oPSBMdl:LoadValue("PSB_NOMEFIL"	,AllTrim(FWFilialName(/*Grupo*/,(cPSBAls)->PSB_FILORI,1)) ) .And.;
					oPSBMdl:LoadValue("PSB_RECPAG"	,(cPSBAls)->PSB_RECPAG) .And.;
					oPSBMdl:LoadValue("PSB_SITBRD"	,(cPSBAls)->PSB_SITBRD) .And.;
					oPSBMdl:LoadValue("PSB_STATUS"	,(cPSBAls)->PSB_STATUS)	.And.;
					oPSBMdl:LoadValue("PSB_VERSAO"	,(cPSBAls)->PSB_VERSAO) .And.;
					oPSBMdl:LoadValue("PSB_SITUAC"	,(cPSBAls)->PSB_SITUAC) .And.;
					oPSBMdl:LoadValue("PSB_BORDER"	,(cPSBAls)->PSB_BORDER) .And.;
					oPSBMdl:LoadValue("PSB_MODELO"	,(cPSBAls)->PSB_MODELO) .And.;
					oPSBMdl:LoadValue("PSB_BANCO"	,(cPSBAls)->PSB_BANCO ) .And.;
					oPSBMdl:LoadValue("PSB_AGENCI"	,(cPSBAls)->PSB_AGENCI) .And.;
					oPSBMdl:LoadValue("PSB_DVAGE"	,(cPSBAls)->PSB_DVAGE) .And.;
					oPSBMdl:LoadValue("PSB_CONTA"	,(cPSBAls)->PSB_CONTA) .And.;
					oPSBMdl:LoadValue("PSB_DVCTA"	,(cPSBAls)->PSB_DVCTA) .And.;
					oPSBMdl:LoadValue("PSB_DATA"	,(cPSBAls)->PSB_DATA) .And.;
					oPSBMdl:LoadValue("PSB_TIPOPG"	,(cPSBAls)->PSB_TIPOPG) .And.;
					oPSBMdl:LoadValue("PSB_TOTAL"	,(cPSBAls)->PSB_TOTAL) .And.;
					oPSBMdl:LoadValue("PSB_USUCRI"	,(cPSBAls)->PSB_USUCRI) .And.;
					oPSBMdl:LoadValue("PSB_URNOME"	,SubStr(BUSCAUSR((cPSBAls)->PSB_USUCRI),1,TamSX3("PSB_URNOME")[1]))
					
		EndIf

		If ( !lRet )
			Exit
		EndIf

		(cPSBAls)->(DbSkip())

	EndDo

	If ( nOperation == MODEL_OPERATION_VIEW)
		oModel:lModify := .F.
	Endif

	oPSBMdl:GoLine(1)

	(cPSBAls)->(DbCloseArea())

	RestArea(aArea)

	Asize(aArea,0)
	aArea := Nil

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} F785flt
Constru��o de Browse de Consulta Padr�o do tipo Especif�ca 
Aprovadores x Papeis
@return lRet - Retorna se o campo onde a consulta padr�o foi chamada ser� preecnhido.

<AUTHOR> Pequim Jr
@since 26/05/2015
@version 12.1.6
/*/
//-------------------------------------------------------------------
User Function F785flt()

Local oMdl           := FWModelActive()
Local lRet           := .T.
Local oBrowse        := Nil
Local cNEAls         := GetNextAlias()
Local nSuperior      := 0
Local nEsquerda      := 0
Local nInferior      := 0
Local nDireita       := 0
Local oDlgTela       := Nil
Local cNEQry         := ""
Local cCodPapel      := oMdl:GetModel("ITEMAPV"):GetValue("PSO_PAPEL")
Local cCpoFonR       := "PSO_CODAPR"

/*
* Defini��o do �ndice da Consulta Padr�o
*/
Local aIndex	:= {"FRX_USER"}
Local aSeek		:= {{ STR0022, {{"","C",15,0,STR0022,,}} }} //"Gestor"#"Gestor"

cRet785F3 := ""

If EMPTY(cCodPapel)
     Help(" ",1,"APROVXPAPEL",,STR0021,3,1)//"Para selecionar um aprovador � necess�rio que seja informado o c�digo do papel"
     lRet := .F.
Else
     cNEQry := " SELECT " + CRLF
     cNEqRY += " FRX.FRX_FILIAL, " + CRLF
     cNEQry += " FRX.FRX_USER, " + CRLF
     cNEQry += " FRX.FRX_NOME, " + CRLF
     cNEQry += " FRX.FRX_CODGES " + CRLF
     cNEQry += " FROM "  + RetSqlName("FRX") + " FRX " + CRLF
     cNEQry += " WHERE " + RetSqlCond("FRX") + CRLF
     cNEQry += " AND FRX.FRX_CODIGO = '" + cCodPapel + "' " + CRLF
     cNEQry += " ORDER BY FRX.FRX_FILIAL, FRX.FRX_USER "
     
     nSuperior := 0
     nEsquerda := 0
     nInferior := 460
     nDireita  := 800
     
     DEFINE MSDIALOG oDlgTela TITLE STR0023 FROM nSuperior,nEsquerda TO nInferior,nDireita PIXEL // "Gestores x Papeis"
   
     oBrowse := FWFormBrowse():New()
     oBrowse:SetDescription(STR0023) // "Gestores x Papeis"
     oBrowse:SetAlias(cNEAls)
     oBrowse:SetDataQuery()
     oBrowse:SetQuery(cNEQry)
     oBrowse:SetOwner(oDlgTela)
     oBrowse:SetDoubleClick({ || cRet785F3 := (oBrowse:Alias())->FRX_USER, oDlgTela:End()})
     oBrowse:AddButton(STR0024, {|| cRet785F3 := (oBrowse:Alias())->FRX_USER, oDlgTela:End() },,2 ) // "Confirmar"
     oBrowse:AddButton(STR0025, {|| cRet785F3 := "", lRet := .F., oDlgTela:End() } ,, 2 ) // "Cancelar"
     oBrowse:DisableDetails()
     oBrowse:SetQueryIndex(aIndex)
     oBrowse:SetSeek({||.T.},aSeek)
     
     ADD COLUMN oColumn DATA { ||  FRX_FILIAL 			} TITLE STR0026 SIZE 15 OF oBrowse //"Filial"
     ADD COLUMN oColumn DATA { ||  FRX_USER				} TITLE STR0027 SIZE 15 OF oBrowse //"C�digo"
     ADD COLUMN oColumn DATA { ||  UsrFullName(FRX_USER)} TITLE STR0028 SIZE 17 OF oBrowse //"Nome"

     oBrowse:Activate()
     ACTIVATE MSDIALOG oDlgTela CENTERED
     
EndIf
Return( lRet )

//-------------------------------------------------------------------
/*/{Protheus.doc} F785RET
Retorna ao c�digo do Gestor escolhido para o papel na aprova��o

@return cRet785F3 - Retorno da consulta padr�o de Gestor x Papel
<AUTHOR> Pequim Jr
@since 26/05/2015
@version 12.1.6
/*/
//-------------------------------------------------------------------

User Function F785RET()
Return( cRet785F3 )

//-------------------------------------------------------------------
/*/{Protheus.doc} F785GRV
Fun��o para grava��o dos dados modelo de dados da solicita��o de aprova��o de border� de pagamento.

@return lRet Retorno se a grava��o do modelo de dados foi bem sucedida.
<AUTHOR> Ara�jo Silva
@since 18/06/2015
@version 12.1.6
@param oModel Objeto do modelo de dados da rotina de solicita��o de aprova��o de border� de pagamento
/*/
//-------------------------------------------------------------------

User Function F785GRV(oModel)

	Local lRet		:= .T.

	Local oPROMdl		:= oModel:GetModel("CABSOL")
	Local oPSBMdl		:= oModel:GetModel("CABBOR")
	Local oPSOMdl		:= oModel:GetModel("ITEMAPV")
	Local oMdl785		:= Nil

	Local cIdCV8		:= ""
	Local cUserFluig	:= ""
	Local cAprPro		:= ""

	Local nPSB			:= 1
	Local nPSO			:= 1
	Local nOperation	:= oModel:GetOperation()
	Local nTamProc		:= TamSX3("PSB_PROAPR")[1]

	Local aSaveLines	:= {}
	Local aUser			:= {}

	Local lAprSub		:= .F.
	Local lFluig		:= MTFluigAtv("WFFINA785","SOLAPR")

	BEGIN TRANSACTION

	ProcLogIni( {}, "SOLAPR" + CVALTOCHAR(VAL(oPROMdl:GetValue("PRO_CODIGO"))), "SOLAPR" + CVALTOCHAR(Val(oPROMdl:GetValue("PRO_CODIGO"))), @cIdCV8 )

	If ( oPROMdl:GetValue("PRO_STATUS") == "4" .AND. __nOper == 0 )
		__nOper == 6
	EndIf

	If ( nOperation == MODEL_OPERATION_INSERT )
		
		aSaveLines	:= FWSaveRows()
		
		//Registro no log a grava��o/altera��o do registro do documento h�bil
		ProcLogAtu( "MENSAGEM",STR0001, STR0029 + oPROMdl:GetValue("PRO_CODIGO"), , .T. ) // "Inclus�o do Processo de Solicita��o de Aprova��o de Border� n�: "
		
		For nPSB := 1 To oPSBMdl:Length()
			
			If ( oPSBMdl:GetValue("OK",nPSB))

				oPSBMdl:GoLine(nPSB)
				lRet := U_F785ViBo(oPSBMdl:GetValue("PSB_FILIAL",nPSB) + oPSBMdl:GetValue("PSB_BORDER",nPSB) + "P" + oPSBMdl:GetValue("PSB_VERSAO",nPSB), oPROMdl:GetValue("PRO_CODIGO"),oPROMdl:GetValue("PRO_FILIAL"))
				// If lRet
				// 	RecLock("FO5",.T.)
				// 	Replace FO5->FO5_FILIAL	With xFilial("FO5")
				// 	Replace FO5->FO5_PROAPR	With oPROMdl:GetValue("PRO_CODIGO")
				// 	Replace FO5->FO5_FILBOR	With oPSBMdl:GetValue("PSB_FILORI",nPSB)
				// 	Replace FO5->FO5_BORDER	With oPSBMdl:GetValue("PSB_BORDER",nPSB)
				// 	Replace FO5->FO5_VERBOR	With oPSBMdl:GetValue("PSB_VERSAO",nPSB)
				// 	FO5->(MsUnLock())
				// Endif
			
			EndIf

		Next nPSB
		
		FWRestRows( aSaveLines )

	ElseIf ( nOperation == MODEL_OPERATION_UPDATE .AND. __nOper == 6 )  // Cancelamento
		
		ProcLogAtu( "MENSAGEM",STR0030, STR0031 + oPROMdl:GetValue("PRO_CODIGO"), , .T. ) //"Cancelamento de Solicita��o de Aprova��o de Border� de Pagamento" //"Cancelamento do Processo de Solicita��o de Aprova��o de Border� n�: "
		
		oPROMdl:SetValue("PRO_STATUS","4")
		
		DbSelectArea("PSO") // Aprova��es de Border�
		PSO->(DbSetOrder(2)) // Filial + Border� + Vers�o + Processo de Aprova��o
		
		For nPSB := 1 To oPSBMdl:Length()

			ProcLogAtu( "MENSAGEM",STR0030, STR0032 + oPSBMdl:GetValue("PSB_BORDER",nPSB), , .T. ) //"Cancelamento de Solicita��o de Aprova��o de Border� de Pagamento" // "Libera��o do Border� de Pagamento n�: "
		
			oPSBMdl:GoLine(nPSB)
		
			cAprPro := oPSBMdl:GetValue("PSB_PROAPR",nPSB)
		
			oPSBMdl:SetValue("PSB_PROAPR"	,Space(nTamProc))
			oPSBMdl:SetValue("PSB_USUSOL"	,"")
			oPSBMdl:SetValue("PSB_USNOME"	,"")
			oPSBMdl:SetValue("PSB_STATUS"	,"2")
			oPSBMdl:SetValue("PSB_FILPRO"	,"")
			
			/*
			* Verifica��o da quantidade de aprova��es/reprova��es feitas para o border�
			*/
			If ( PSO->(DbSeek(FWxFilial("PSO",oPSBMdl:GetValue("PSB_FILORI",nPSB)) + oPSBMdl:GetValue("PSB_BORDER",nPSB) + oPSBMdl:GetValue("PSB_VERSAO",nPSB) + cAprpro)) )
				/*
				* Se um aprovador da solicita��o de aprova��o rejeitar, todas as aprova��es feitas ser�o rejeitadas.
				*/
				PSO->(RecLock("PSO",.F.))
				PSO->PSO_MOVIME := "3" // Cancelado
				PSO->(MsUnLock())
				PSO->(DbSkip())
		
			EndIf

		Next nPSB

	EndIf

	If ( __nOper <> 6 .AND. (nOperation == MODEL_OPERATION_UPDATE .OR. nOperation == MODEL_OPERATION_INSERT) )
		
		ProcLogAtu( "MENSAGEM",STR0033, STR0047, , .T. ) // "Atualiza��o de Solicita��o de Aprova��o de Border� de Pagamento"
		
		For nPSO := 1 To oPSOMdl:Length()
		
			oPSOMdl:GoLine(nPSO)
		
			If ( nOperation == MODEL_OPERATION_UPDATE )
			
				If ( oPSOMdl:IsFieldUpdated("PSO_STATUS",nPSO) )
					                                                                                                                                     
					ProcLogAtu( "MENSAGEM",STR0033, oPSOMdl:GetValue("PSO_CODAPR",nPSO) + " - " + AllTrim( BUSCAUSR( oPSOMdl:GetValue("PSO_CODAPR",nPSO) ) ) + STR0045 + U_F785DCS(oPSOMdl:GetValue("PSO_STATUS",nPSO)) + "' ", , .T. ) //"Atualiza��o de Solicita��o de Aprova��o de Border� de Pagamento" // " atualizado para '"
					lAprSub := .T. 

				ElseIf ( oPSOMdl:IsInserted(nPSO) )
					
					ProcLogAtu( "MENSAGEM",STR0033, oPSOMdl:GetValue("PSO_CODAPR",nPSO) + " - " + AllTrim(BUSCAUSR( oPSOMdl:GetValue("PSO_CODAPR",nPSO) ) ) + STR0046, , .T. ) //"Atualiza��o de Solicita��o de Aprova��o de Border� de Pagamento" // " inclu�do com aprovador."
					lAprSub := .T.

				EndIf
			
			ElseIf ( nOperation == MODEL_OPERATION_INSERT ) 
				ProcLogAtu( "MENSAGEM",STR0033, oPSOMdl:GetValue("PSO_CODAPR",nPSO) + " - " + AllTrim(BUSCAUSR( oPSOMdl:GetValue("PSO_CODAPR",nPSO) ) ) + STR0046, , .T. ) //"Atualiza��o de Solicita��o de Aprova��o de Border� de Pagamento" // " inclu�do com aprovador."
			EndIf

		Next nPSO

	EndIf

	lRet := FWFormCommit(oModel)

	If ( lRet .AND. nOperation == MODEL_OPERATION_UPDATE .AND. __nOper == 6 )// Cancelamento
		lRet := U_F785LiBo(oPROMdl:GetValue("PRO_CODIGO"))
	EndIf

	If ( lRet .AND. lFluig )

		For nPSO := 1 To oPSOMdl:Length()

			If oPSOMdl:GetValue("PSO_STATUS",nPSO) == '1'

				oPSOMdl:GoLine(nPSO)
				cUserFluig := FWWFColleagueId(oPSOMdl:GetValue("PSO_CODAPR",nPSO))

				If ( !Empty(cUserFluig) )
					aAdd(aUser,cUserFluig)
				EndIf

			EndIf

		Next nPSO
		
		If ( nOperation == MODEL_OPERATION_UPDATE .AND. __nOper <> 6 .AND. lAprSub )

			WFFINA785( oPROMdl:GetValue("PRO_USUCRI"), oPROMdl:GetValue("PRO_CODIGO"), nOperation, /*aUser*/ , .T.)
			WFFINA785( oPROMdl:GetValue("PRO_USUCRI"), oPROMdl:GetValue("PRO_CODIGO"), MODEL_OPERATION_INSERT, aUser, .F.)	
		
		Else
			WFFINA785( oPROMdl:GetValue("PRO_USUCRI"), oPROMdl:GetValue("PRO_CODIGO"), nOperation, aUser, __nOper == 6)
		EndIf
		
	EndIf

	END TRANSACTION

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} F785Can
Fun��o para efetuar o cancelamento da Solicita��o da Aprova��o de Border� de Pagamento

<AUTHOR> Ara�jo Silva
@since 22/06/2015
@version 12.1.6
/*/
//-------------------------------------------------------------------

User Function F785Can()
Local oMdlOld	:= FwModelActive()
Local oVwOld	:= FWViewActive() 

__nOper := 6

FWExecView(STR0030,; // "Cancelamento de Solicita��o de Aprova��o de Border� de Pagamento"
			'TFINA785',;
			MODEL_OPERATION_UPDATE,;
			/*oDlg*/,;
			{ || .T. },;
			/*bOk*/,;
			/*nPercReducao*/,;
			/*aEnableButtons*/,;
			/*bCancel*/,;
			/*cOperatId*/,;
			/*cToolBar*/,;
			/*oModel*/)

FwModelActive(oMdlOld)
FWViewActive(oVwOld)

__nOper := 0 //restaura a opera��o 

 	
Return Nil

//-------------------------------------------------------------------
/*/{Protheus.doc} F785VdP
Fun��o para valida��o do registro no momento de Manuten��o ou Cancelamento da Solicita��o de Aprova��o de Border� de Pagamento

@return lRet Retorno se � poss�vel efetuar a manuten��o ou cancelamento do registro.
<AUTHOR> Ara�jo Silva
@since 22/06/2015
@version 12.1.6
@param oModel Objeto do modelo de dados da rotina de solicita��o de aprova��o de border� de pagamento
/*/
//-------------------------------------------------------------------
User Function F785VdP(oModel)
Local lRet			:= .T.
Local nOperation	:= oModel:GetOperation()

If nOperation <> MODEL_OPERATION_INSERT .AND. nOperation <> MODEL_OPERATION_VIEW
	If !(PRO->PRO_STATUS $ "1,2") .AND. __nOper == 6
		Help(" ",1,"SOLAPRCAN",,STR0034,3,1) //"Solicita��o de Aprova��o n�o poder ser cancelada."
		lRet := .F.
	ElseIf nOperation <> MODEL_OPERATION_VIEW .AND. !IsInCallStack("FINA240") .AND. !IsInCallStack("FINA241") .AND. PRO->PRO_STATUS $ "3,4"
		Help(" ",1,"SOLAPRMAN",,STR0035,3,1) //"Solicita��o de Aprova��o n�o poder ser cancelada."
		lRet := .F.
	EndIf
Endif

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} F785LiBo
Fun��o para libera��o de border�s no cancelamento da Solicita��o de Aprova��o de Border� de Pagamento

@return lRet Retorno se os border�s foram liberados da solicita��o da aprova��o de border� de pagamento no cancelamento.
<AUTHOR> Ara�jo Silva
@since 22/06/2015
@version 12.1.6
@param cCodProc C�digo de identifica��o do processo de solicita��o da aprova��o de border� de pagamento.
/*/
//-------------------------------------------------------------------
User Function F785LiBo(cCodProc)
Local lRet		:= .T.
Local cPSBQry	:= ""
Local cPSBAls	:= GetNextAlias()
Local aPSBArea	:= {}
Local aArea		:= GetArea()

DbSelectArea("PSB")
aPSBArea := PSB->(GetArea())

cPSBQry := " SELECT " + CRLF
cPSBQry += " PSB.R_E_C_N_O_ RECNO " + CRLF
cPSBQry += " FROM " + RetSqlName("PSB") + " PSB " + CRLF
cPSBQry += " WHERE PSB.D_E_L_E_T_ = ' ' " + CRLF
cPSBQry += " AND PSB.PSB_PROAPR = '" + cCodProc + "' "

dbUseArea(.T.,"TOPCONN",TcGenQry(,,cPSBQry),cPSBAls,.T.,.T.)

While (cPSBAls)->(!Eof())
	If PSB->(DbGoTo((cPSBAls)->RECNO))
		PSB->(RecLock("PSB",.F.))
		PSB->PSB_FILPRO := ""
		PSB->PSB_PROAPR := ""
		PSB->PSB_USUSOL := ""
		PSB->PSB_USNOME := ""
		PSB->PSB_STATUS := "1"
		PSB->(MsUnLock())
	EndIf
	(cPSBAls)->(DbSkip())
EndDo

DbSelectArea(cPSBAls)
DbCloseArea()
RestArea(aArea)
RestArea(aPSBArea)

Return lRet


//-------------------------------------------------------------------
/*/{Protheus.doc} F785ViBo
Fun��o para vincula��o de border�s na Solicita��o de Aprova��o de Border� de Pagamento

@return lRet Retorno se os border�s foram liberados da solicita��o da aprova��o de border� de pagamento no cancelamento.
<AUTHOR> Ara�jo Silva
@since 22/06/2015
@version 12.1.6
@param cChvBor	Chave do Border� que ser� vinculado ao processo de aprova��o de border� de pagamento.
@param cCodProc	C�digo de identifica��o do processo de solicita��o da aprova��o de border� de pagamento.
@param cFilProc	Filial de gera��o da solicita��o de aprova��o de border� de pagamento.
/*/
//-------------------------------------------------------------------

User Function F785ViBo(cChvBor,cProc,cFilProc)
Local lRet		:= .T.
Local aArea		:= GetArea()
Local aPSBArea	:= PSB->(GetArea())

PSB->(DbSetOrder(1))
If PSB->(DbSeek(cChvBor))
	PSB->(RecLock("PSB",.F.))
	PSB->PSB_PROAPR := cProc
	PSB->PSB_FILPRO := cFilProc
	PSB->PSB_USUSOL := AllTrim(RetCodUsr())
	PSB->PSB_USNOME := AllTrim(cUserName)	
	PSB->PSB_STATUS := "2"
	PSB->(MsUnLock())
	ProcLogAtu( "MENSAGEM", STR0001, STR0036 + PSB->PSB_BORDER, , .T. ) // "Inclus�o do Border� n�: " 
Else
	lRet := .F.
EndIf

RestArea(aArea)
RestArea(aPSBArea)
Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} F785LG

Fun��o de consulta do log de processamento do documento h�bil.

<AUTHOR>
@since 23/06/2015
@version 12.1.6
/*/
//-------------------------------------------------------------------
User Function F785LG()

ProcLogView( cFilAnt, "SOLAPR" + CVALTOCHAR(VAL(PRO->PRO_CODIGO)), "SOLAPR" + CVALTOCHAR(VAL(PRO->PRO_CODIGO) ) )

Return

//-------------------------------------------------------------------
/*/{Protheus.doc} F785Vd
Fun��o para valida��o dos dados na grava��o dos dados da Solicita��o ou Manuten��o da Solicita��o de Aprova��o de Border� de Pagamento

@return lRet Retorno se � poss�vel efetuar confirma a grava��o dos dados.
<AUTHOR> Ara�jo Silva
@since 23/06/2015
@version 12.1.6
@param oModel Objeto do modelo de dados da rotina de solicita��o de aprova��o de border� de pagamento
/*/
//-------------------------------------------------------------------

User Function F785Vd(oModel)
Local lRet		:= .T.
Local aPapeis		:= {}
Local aPapPrenc	:= {}
Local oMdlPSB		:= oModel:GetModel("CABBOR")
Local oMdlPSO		:= oModel:GetModel("ITEMAPV")
Local nPSB		:= 0
Local nPSO		:= 0
Local aSaveLines	:= FWSaveRows()
Local nPosPapel	:= 0
Local cFRWFil		:= FWxFilial("FRW")

lRet := .F.
For nPSB := 1 To oMdlPSB:Length()
	If (lRet := oMdlPSB:GetValue("OK",nPSB))
		Exit
	EndIf
Next nPSB

If !lRet
	Help(" ",1,"VLDSOLAPR",,STR0049,3,1) //"� necess�rio selecionar pelo um border� de pagamento."
EndIf

If lRet
	For nPSO := 1 To oMdlPSO:Length()
		If (nPosPapel := aScan(aPapeis,{|cPapel| cPapel[1] == oMdlPSO:GetValue("PSO_PAPEL",nPSO) .AND. cPapel[2] } )) > 0
			Aadd(aPapeis,{oMdlPSO:GetValue("PSO_PAPEL",nPSO),.F.})
		EndIf
				
		If (nPosPapel := aScan(aPapPrenc,{|aPapel| aPapel[1] + aPapel[2] == oMdlPSO:GetValue("PSO_PAPEL",nPSO) + oMdlPSO:GetValue("PSO_STATUS",nPSO)} ) ) > 0
			aPapPrenc[nPosPapel][3] += 1
		Else
			Aadd(aPapPrenc,{oMdlPSO:GetValue("PSO_PAPEL",nPSO),oMdlPSO:GetValue("PSO_STATUS",nPSO),1})
		EndIf
	Next nPSO

	For nPSO := 1 To Len(aPapPrenc)
		If aPapPrenc[nPSO][2] == "1"
			If aPapPrenc[nPSO][3] > 1
				lRet := .F.
				Help(" ",1,"VLDSOLAPR",,STR0037 + POSICIONE('FRW', 1, cFRWFil + aPapPrenc[nPSO][1] , 'FRW_DESCR') + STR0038,3,1) //"Para o Papel '"//"' � poss�vel informar somente um aprovador 'Ativo'."
				Exit
			EndIf
		EndIf
	Next nPSO
EndIf

If lRet
	For nPSO := 1 To Len(aPapeis)
		If aScan(aPapPrenc,{|aPapel| aPapel[1] + aPapel[2] == aPapeis[nPSO][1] + "1" .AND. !aPapeis[nPSO][2]}) == 0
			lRet := .F.
			Help(" ",1,"VLDSOLAPR",,STR0037 + POSICIONE('FRW', 1, cFRWFil + aPapPrenc[nPSO][1] , 'FRW_DESCR') + STR0039,3,1) //"Para o Papel '"//"' � obrigat�rio informar um aprovador como 'Ativo'."
			Exit
		EndIf
	Next nPSO
EndIf

FWRestRows( aSaveLines )

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} F785UsVd
Fun��o para valida��o do usu�rio preenchido no grid de aprovadores.

@return lRet Retorno se � poss�vel efetuar confirma a grava��o dos dados.
<AUTHOR> Ara�jo Silva
@since 23/06/2015
@version 12.1.6s
/*/
//-------------------------------------------------------------------

User Function F785UsVd()
Local lRet		:= .T.
Local oModel		:= FWModelActive()
Local oMdlPSO		:= oModel:GetModel("ITEMAPV")
Local cFRXAls		:= GetNextAlias()
Local cFRXQry		:= ""
Local cCpoVld		:= AllTrim(SUBSTR(ReadVar(),4))
Local cCpoVlr		:= M->&(cCpoVld)
Local nAtGrid		:= oMdlPSO:GetLine()
Local cFRWFil		:= FWXFilial("FRW")
Local aSaveLines	:= FWSaveRows()
Local aArea		:= {}

If !EMPTY(cCpoVlr)
	If "PSO_PAPEL" $ cCpoVld .OR. ("PSO_CODAPR" $ cCpoVld .AND. PswSeek(cCpoVlr))
		If !EMPTY(oMdlPSO:GetValue("PSO_CODAPR",nAtGrid)) .AND. !EMPTY(oMdlPSO:GetValue("PSO_PAPEL",nAtGrid))
			aArea := GetArea()
			cFRXQry := " SELECT 1 " + CRLF
			cFRXQry += " FROM "  + RetSqlName("FRX") + " FRX " + CRLF
			cFRXQry += " WHERE " + RetSqlCond("FRX") + CRLF
			cFRXQry += " AND FRX.FRX_CODIGO = '" + oMdlPSO:GetValue("PSO_PAPEL",nAtGrid) + "' " + CRLF 
			cFRXQry += " AND FRX.FRX_USER = '" + cCpoVlr + "' " + CRLF
			
			cFRXQry := ChangeQuery(cFRXQry)
			dbUseArea(.T.,"TOPCONN",TcGenQry(,,cFRXQry),cFRXAls,.T.,.T.)
				
			lRet := (cFRXAls)->(!Eof())
			(cFRXAls)->(DbCloseArea())
			RestArea(aArea)
		EndIf
		
		If "PSO_CODAPR" $ cCpoVld .AND. !lRet
			Help(" ",1,"VLDSOLAPR",,CRLF + STR0040 + cCpoVlr + " - " + AllTrim(USRFULLNAME(cCpoVlr)) + CRLF +;
			STR0041 + CRLF + POSICIONE('FRW', 1, cFRWFil + oMdlPSO:GetValue("PSO_PAPEL",nAtGrid), 'FRW_DESCR') + "'." + CRLF +;
			STR0042,3,1) // "Usu�rio: " // " n�o desempenha o papel: '" // "Reveja o cadastro de pap�is ou informe outro usu�rio."
		EndIf
		
		If "PSO_PAPEL" $ cCpoVld .AND. !lRet .AND. !EMPTY(oMdlPSO:GetValue("PSO_CODAPR",nAtGrid))
			Help(" ",1,"VLDSOLAPR",,CRLF + STR0064 + cCpoVlr + " - " + POSICIONE('FRW', 1, cFRWFil + cCpoVlr, 'FRW_DESCR') + CRLF +; //"Papel: "
			STR0063 + CRLF + oMdlPSO:GetValue("PSO_CODAPR",nAtGrid) + " - " + AllTrim(USRFULLNAME(oMdlPSO:GetValue("PSO_CODAPR",nAtGrid))) + "." + CRLF +;
			STR0042,3,1) // "Incompat�vel com o usu�rio informado: " // " n�o desempenha o papel: '" // "Reveja o cadastro de pap�is ou informe outro usu�rio."
		EndIf
		
		If "PSO_CODAPR" $ cCpoVld .AND. oMdlPSO:SeekLine({{"PSO_CODAPR",cCpoVlr},{"PSO_STATUS","1"}},.F.) .AND. oMdlPSO:GetLine() <> nAtGrid
			Help(" ",1,"VLDSOLAPR",,CRLF + STR0040 + cCpoVlr + " - " + AllTrim(USRFULLNAME(cCpoVlr)) + CRLF +;
			STR0043 + CRLF + STR0044,3,1) //"Usu�rio: " //" j� informado como aprovador." //"Informe outro usu�rio."
			lRet := .F.
		EndIf
	Else
		If "PSO_CODAPR" $ cCpoVld
			Help(" ",1,"VLDSOLAPR",,CRLF + STR0040 + cCpoVlr + STR0053,3,1) //"Usu�rio: " //" n�o existente."
			lRet := .F.
		EndIf
	EndIf
EndIf

FWRestRows( aSaveLines )
Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} F785DCS
Retorna a descri��o da situa��o do aprovador

@return cDesc - Descri��o da Situa��o do Aprovador
<AUTHOR> Ara�jo Silva
@since 24/06/2015
@version 12.1.6
@param cSituacao Situa��o do Aprovador 
/*/
//-------------------------------------------------------------------

User Function F785DCS(cSituacao)
Local cDescr := ""

If cSituacao == "1"
	cDescr := STR0050 //"Ativo"
ElseIf cSituacao == "2"
	cDescr := STR0051 //"Substitu�do"
ElseIf cSituacao == "3"
	cDescr := STR0052 //"Desativado"
EndIf

Return( cDescr )

//-------------------------------------------------------------------
/*/{Protheus.doc} F785VdAf
Fun��o para valida��o da abertura da View da Solicita��o de Aprova��o de Border� de Pagamento

@return lRet Retorno se � poss�vel efetuar a abertura
<AUTHOR> Ara�jo Silva
@since 24/06/2015
@version 12.1.6
@param oModel Objeto da view da interface da rotina de solicita��o de aprova��o de border� de pagamento
/*/
//-------------------------------------------------------------------
User Function F785VdAf(oView)
Local lRet		:= .T.
Local oModel		:= oView:GetModel()
Local nOperation	:= oModel:GetOperation()
Local oPSBMdl		:= oModel:GetModel("CABBOR")
Local aArea		:= GetArea()
Local aSEAArea	:= {}
Local cSEAQry		:= ""
Local cSEAAls		:= GetNextAlias()
Local nPSB		:= 0

If nOperation == MODEL_OPERATION_INSERT
	If oPSBMdl:IsEmpty()
		If lPerg
			Help(" ",1,"NOBORD",, STR0048,3,1) //"Nenhum border� de pagamento encontrado para o per�odo informado."
		EndIf
		
		oModel:lModify := .F.
		oView:lModify := .F.
		oView:ButtonCancelAction()
		lRet := .F.
	EndIf
ElseIf nOperation == MODEL_OPERATION_UPDATE .AND. __nOper == 6
	DbSelectArea("SEA")
	aSEAArea := SEA->(GetArea())
	
	For nPSB := 1 To oPSBMdl:Length()
		cSEAAls	:= GetNextAlias()
		cSEAQry := " SELECT 1 " + CRLF
		cSEAQry += " FROM "  + RetSqlName("SEA") + " SEA " + CRLF
		cSEAQry += " WHERE SEA.EA_FILIAL = '" + xFilial("SEA",oPSBMdl:GetValue("PSB_FILIAL",nPSB)) + "' " + CRLF
		cSEAQry += " AND SEA.EA_NUMBOR = '" + oPSBMdl:GetValue("PSB_BORDER",nPSB) + "' " + CRLF 
		cSEAQry += " AND SEA.EA_VERSAO = '" + oPSBMdl:GetValue("PSB_VERSAO",nPSB) + "' " + CRLF
		cSEAQry += " AND SEA.EA_TRANSF != ' ' " + CRLF
		cSEAQry += " AND SEA.EA_CART = 'P' " + CRLF
		cSEAQry += " AND " + RetSqlDel("SEA") + CRLF
		
		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cSEAQry),cSEAAls,.T.,.T.)
		
		If (cSEAAls)->(!Eof())
			Help(" ",1,"NOCANCANC",, STR0005+STR0073,3,1) //"O processo de solicita��o de aprova��o de border� de pagamento n�o pode ser cancelado"###"Existem border�s com arquivos de CNAB gerados para envio a banco."
			oModel:lModify := .F.
			oView:lModify := .F.
			oView:ButtonCancelAction()
			lRet := .F.
			Exit
		EndIf
		DbSelectArea(cSEAAls)
		DbCloseArea()
	Next nPSB
	RestArea(aSEAArea)
EndIf

oModel:GetModel("CABBOR"):SetNoInsertLine(.T.)

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} AF785lg
Fun��o para exibi��o da legenda de status no grid de border�s de pagamento

<AUTHOR> Ara�jo Silva
@since 02/07/2015
@version 12.1.6

/*/
//-------------------------------------------------------------------
User Function AF785lg()

Local aCores := {;
{"BR_BRANCO"		,OemToAnsi(STR0056)},;	//"Bloqueado"
{"BR_AMARELO"		,OemToAnsi(STR0057)},;	//"Aguardando Aprova��o"
{"BR_LARANJA"		,OemToAnsi(STR0058)},;	//"Aprovado Parcialmente"
{"BR_VERDE"		,OemToAnsi(STR0059)},;	//"Aprovado"
{"BR_VERMELHO"	,OemToAnsi(STR0060)}}	//"Rejeitado"

BrwLegenda(STR0055,STR0001,aCores) //"Legenda" //"Solicita��o de Aprova��o de Border� de Pagamento"

Return()

/*/{Protheus.doc} F785TemSoL()
Fun��o para validar se um titulo est� relacionado a um processo de aprova��o de border�

@param lFiltro	Indica se a rotina deve retornar uma expressao de filtro ou valor l�gico para valida��o
@param cAlias	Alias a ser considerado para valida��o ou filtro
@param lHelp	Indica se o help ser� mostrado ou n�o
@param lTop		Indica se a express�o de filtro deve ser no padr�o codebase (.F.) ou SQL (.T.)

@return xRet 	String com a express�o que ser� considerada no filtro ou retorno l�gico de valida��o    

<AUTHOR> Ara�jo Silva
@since�07/08/2015
@version P12.1.7
/*/

User Function F785TSoL(lFiltro,cAlias, lHelp, lTop)
Local xRet		:= ""
Local lProApr		:= GETNEWPAR("MV_CTAPBD","2") == '1'
Local aSEAArea	:= {}
Local cSEAAls		:= ""
Local cSEAQry		:= ""
Local cCpo		:= ""
Local aArea		:= GetArea()
Local lLibBor 	:= SuperGetMv("MV_BXBOAPR",,"1")=="1"

Default lFiltro := .F.
Default cAlias  := "SE2"
Default lHelp   := .T.
Default lTop    := .T.

If cAlias == "SE2"
	cCpo := "E2"
ElseIf cAlias == "SE1"
	cCpo := "E1"
EndIf

If lProApr .and. lLibBor
	If lTop
		If lFiltro
			xRet := " AND SE2.E2_NUM NOT IN (" + CRLF
			xRet += " SELECT EA_NUM "
			xRet += " FROM "  + RetSqlName("SEA") + " SEA " + CRLF
			xRet += " INNER JOIN " + RetSqlName("PSB") + " PSB ON PSB.PSB_BORDER = SEA.EA_NUMBOR AND PSB.PSB_VERSAO = SEA.EA_VERSAO " 
			xRet += " WHERE " + RetSqlCond("SEA") + CRLF
			xRet += " AND " + RetSqlCond("PSB") + CRLF
			xRet += " AND SEA.EA_NUM 	= " + cAlias + "." + cCpo + "_NUM" 	+ " " + CRLF 
			xRet += " AND SEA.EA_PREFIXO = " + cAlias + "." + cCpo + "_PREFIXO"	+ " " + CRLF
			xRet += " AND SEA.EA_PARCELA = " + cAlias + "." + cCpo + "_PARCELA" + " " + CRLF
			xRet += " AND SEA.EA_TIPO 	= " + cAlias + "." + cCpo + "_TIPO" 	+ " " + CRLF
			xRet += " AND SEA.EA_FORNECE = " + cAlias + "." + cCpo + "_FORNECE" + " " + CRLF
			xRet += " AND SEA.EA_LOJA 	= " + cAlias + "." + cCpo + "_LOJA" 	+ " " + CRLF
			xRet += " AND PSB.PSB_SITBRD IN ('1') " + CRLF
			
			If cAlias == "SE2"
				xRet += " AND SEA.EA_CART = 'P' " + CRLF
			ElseIf cAlias == "SE1"
				xRet += " AND SEA.EA_CART = 'R' " + CRLF
			EndIf
			xRet += " ) " + CRLF
		Else
			xRet := .F.
		EndIf
	Else		
		DbSelectArea("SEA")
		aSEAArea := SEA->(GetArea())
		cSEAAls	:= GetNextAlias()
		
		cSEAQry := " SELECT 1 RETORNO " + CRLF
		cSEAQry += " ,PSB.PSB_SITBRD "
		cSEAQry += " ,PSB.PSB_STATUS "
		cSEAQry += " FROM "  + RetSqlName("SEA") + " SEA " + CRLF
		cSEAQry += " INNER JOIN " + RetSqlName("PSB") + " PSB ON PSB.PSB_BORDER = SEA.EA_NUMBOR AND PSB.PSB_VERSAO = SEA.EA_VERSAO " 
		cSEAQry += " WHERE " + RetSqlCond("SEA") + CRLF
		cSEAQry += " AND " + RetSqlCond("PSB") + CRLF
		cSEAQry += " AND SEA.EA_NUM 	= '" + &(cAlias + "->" + cCpo + "_NUM") 	+ "' " + CRLF 
		cSEAQry += " AND SEA.EA_PREFIXO = '" + &(cAlias + "->" + cCpo + "_PREFIXO")	+ "' " + CRLF
		cSEAQry += " AND SEA.EA_PARCELA = '" + &(cAlias + "->" + cCpo + "_PARCELA") + "' " + CRLF
		cSEAQry += " AND SEA.EA_TIPO 	= '" + &(cAlias + "->" + cCpo + "_TIPO") 	+ "' " + CRLF
		cSEAQry += " AND SEA.EA_FORNECE = '" + &(cAlias + "->" + cCpo + "_FORNECE") + "' " + CRLF
		cSEAQry += " AND SEA.EA_LOJA 	= '" + &(cAlias + "->" + cCpo + "_LOJA") 	+ "' " + CRLF
		cSEAQry += " AND PSB.PSB_SITBRD	= '1' " + CRLF
		
		If cAlias == "SE2"
			cSEAQry += " AND SEA.EA_CART = 'P' " + CRLF
		ElseIf cAlias == "SE1"
			cSEAQry += " AND SEA.EA_CART = 'R' " + CRLF
		EndIf
		
		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cSEAQry),cSEAAls,.T.,.T.)
		
		xRet := (cSEAAls)->RETORNO == 1
	
		If xRet .and. lHelp .AND. (cSEAAls)->PSB_STATUS != '4'
			HELP(" ",1,"SOLAPRINT",, STR0061 + CRLF + STR0062,2,0) //"T�tulo vinculado a um border� em processo de aprova��o." //"N�o pode ser baixado."
		ElseIf xRet .AND. (cSEAAls)->PSB_STATUS == '4'
			If lFiltro
				xRet :=	.T.
			Else
				xRet := !xRet
			EndIf
		Else
			If lFiltro
				xRet := !xRet
			Else
				xRet :=	.F.
			EndIf
		EndIf
		DbSelectArea(cSEAAls)
		DbCloseArea()
		RestArea(aSEAArea)
	Endif
Else
	//Se for um filtro
	If lFiltro
		If lTop
			xRet := ""
		Else
			xRet := .T.
		EndIf
	Else
		xRet := .F.
	Endif
Endif

RestArea(aArea)
Return xRet


//-------------------------------------------------------------------
/*/{Protheus.doc} F785Vw
Fun��o para verificar a visualiza��o das solicita��es

<AUTHOR> Gabriel
@since 07/12/2015

/*/
//-------------------------------------------------------------------

User Function F785Vw(nOpc)

    Local aEnableButtons	:= {{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.T.,Nil},{.T.,"Cancelar"},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil},{.F.,Nil}}
	Local cIniBor		:= ""
    Local oExecView		:= Nil
	Local nRet			:= 0
	Local cFimBor		:= ""
    Default nOpc        := MODEL_OPERATION_VIEW


	

	IF !lCanceled .AND. nOpc==3 

		lPerg := Pergunte("TFINA785",.t.)

		If lPerg
			
			aSelFil := {}
			
			If MV_PAR01 == 1
			
				aSelFil := AdmGetFil()
				
				If Len( aSelFil ) <= 0
					lRet := .F.
				EndIf

			EndIf
	
			cIniBor	:= MV_PAR02
			cFimBor	:= MV_PAR03
		Else
			lRet := .F.
		EndIf

		nRet := FWExecView(STR0023, "TFINA785",nOpc, /*oDLG*/, /**/,/*{|| lCanceled := .f., .t.}*/,/**/,aEnableButtons,/*{|| lCanceled := .t.}*/ )



	Endif



    If ( nOpc == 4 )
        nRet := FWExecView(STR0023, "TFINA785",nOpc, /*oDLG*/, /**/,{|| lCanceled := .f., .t.},/**/,aEnableButtons,{|| lCanceled := .t.} ) 
    Else
		nRet := FWExecView(STR0023, "TFINA785",nOpc)        
    EndIf    

	//EndIf

    Asize(aEnableButtons,0)
    aEnableButtons := Nil

	//lCanceled := nRet == 1


Return(nRet <> 1)


//-------------------------------------------------------------------
/*/{Protheus.doc} F785AtBo
Atualiza a tabela de borderos das solicitacoes.

<AUTHOR> Gabriel
@since 07/12/2015

/*/
//-------------------------------------------------------------------
User Function F785AtBo(cFilPro,cProApr,cFilOri,cBorder,cVersao,nOp)
Local aArea	:= {}

If FO5->(DbSeek(cFilPro + cProApr + cFilOri + cBorder))
	aArea := GetArea()
	If nOP == 1		//alteracao da versao
		If FO5->FO5_VERBOR <> cVersao
			RecLock("FO5",.F.)
			Replace FO5->FO5_VERBOR With cVersao
			MSUnLock()
		Endif
	ElseIf nOP == 2		//exclusao do bordero
		RecLock("FO5",.F.)
		FO5->(DbDelete())
		MsUnLock()
	Endif
	RestArea(aArea)
	Asize(aArea,0)
	aArea := Nil
Endif 
Return()

//-------------------------------------------------------------------
/*/{Protheus.doc} F785Bot
Cria bot�o para aprovar ou reprovar todos os border�s 

<AUTHOR> Ara�jo Silva
@since 10/03/2016
@version 12.1.7	
/*/
//-------------------------------------------------------------------
User Function F785Bot(oPanel,oView)
Local oButton	:= Nil
Local oRadio	:= Nil
Local nRadio	:= 1

@4,4 Radio oRadio VAR nRadio ITEMS STR0067,STR0068 3D SIZE 100,10 OF oPanel PIXEL //"Marcar todos os border�s"###"Demarcar todos os border�s"
@4,150 BUTTON oButton PROMPT STR0069 SIZE 100,10 FONT oPanel:oFont ACTION MsgRun(STR0070,STR0071,{|| U_F785Mark(nRadio)}) OF oPanel PIXEL  //"Executar"   //"Marcando / Desmarcando todos os border�s."###"Border�s"

Return

//-------------------------------------------------------------------
/*/{Protheus.doc} F785Mark
Efetua a marca��o/demarca��o de todos os border�s.

<AUTHOR> Ara�jo Silva
@since 10/03/2016
@version 12.1.7
/*/
//-------------------------------------------------------------------
User Function F785Mark(nAcao)
Local nX			:= 0
Local nLenPSB		:= 0
Local oModel		:= Nil
Local oBordero	:= Nil
Local oView		:= Nil
Local oDlg		:= Nil
Local lMarca		:= .F.

oModel	:= FWModelActive()
oBordero	:= oModel:GetModel("CABBOR")
nLenPSB	:= oBordero:Length()
If nLenPSB > 0
	oView := FwViewActive()
	For nX := 1 To nLenPSB
		oBordero:GoLine(nX)
		lMarca := oBordero:GetValue("OK")
		If nAcao == 1 // Marca
			lMarca := .T.
		ElseIf nAcao == 2 // Demarca
			lMarca := .F.
		Else
			lMarca := !lMarca
		Endif
		oBordero:LoadValue('OK',lMarca)
	Next nX

	oBordero:GoLine(1)
	oView:Refresh()
Endif

Return

User Function F785Screen(nOpc)

Local oModel    := FwLoadModel("TFINA785")

oModel:SetOperation(nOpc)

oModel:Activate()

FwExecView("Teste MVC","TFINA785",nOpc,,,,,,,,,oModel)

Return(.t.)    


Static Function TF590End(oModel)

Local lRet	:= .t.

Local oView	:= FwViewActive()

If ( ValType(oView) == "O" ) 
	oView:DeActivate()
EndIf

oModel:DeActivate()

Return(lRet)

// User Function MFIN785()
// // Local oModel    := FwLoadModel("TFINA785")

// // oModel:SetOperation(nOpc) {{.F.,Nil},{.F.,Nil},{.F.,Nil},{.T.,Nil},{.T.,Nil},{.T.,Nil},{.T.,"Salvar"},{.T.,"Cancelar"},{.T.,Nil},{.T.,Nil},{.T.,Nil},{.T.,Nil},{.T.,Nil},{.T.,Nil}}

// // oModel:Activate()

// // FwExecView("Teste MVC","TFINA785",nOpc,,,,,,,,,oModel)

// Local cId := ""
// Local aMyRot	:= {}

// If (Valtype(ParamIXB) == "A")
// 	iF (ParamIxb[2] == "MENUDEF")
// 		Alert(Varinfo("Paramixb",ParamIxb))
// 	ENDIF
// ENDIF
// Return .t.


Static Function BUSCAUSR(cCodUSR)
Local cUsrName := AllTrim(UsrFullName(cCodUSR))
Return(cUsrName)
