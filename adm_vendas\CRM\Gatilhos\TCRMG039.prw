#Include "totvs.ch"

/*/{Protheus.doc} TCRMG039
    Gatilho para o campo ADY_XPRAVP - Permite Red.Aviso Previo
    @type Function
    <AUTHOR>
    @since 25/10/2023
    @version Undefined
    /*/
User Function TCRMG039()
    Local aArea   := GetArea()
    Local cCodAgr := "000112"
    Local cAviPre := FwFldGet("ADY_XAVPRE")

    If FwFldGet("ADY_XPRAVP") == "2"
        PKG->(dbSetOrder(1))
        If PKG->(dbSeek(xFilial("PKG")+cCodAgr+FwFldGet("ADY_XMODAL")))
            cAviPre := PKG->PKG_AVPREV
        EndIf
    EndIf

    RestArea(aArea)

Return(cAviPre)
