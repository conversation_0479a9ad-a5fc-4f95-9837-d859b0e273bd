#INCLUDE "tlpp-core.th"

namespace tdi.sigagct.cancellation.approval.batch.temporary.browser



Class TempApprovalBrowser
	Data oTempTableUp   as Object
	Data oTempTableDown as Object
	Data aStructUp      as Array
	Data aStructDown    as Array
	Public Data aFieldUp       as Array
	Public Data aFieldDown     as Array
	Data aIndexUp       as Array
	Data aIndexDown     as Array
	Data cAliUp         as Character
	Data cAliDown       as Character
	Data cAliTmpUp      as Character
	Data cAliTmpDown    as Character
	Data cStatus        as Character
	Data nTamIDPH3      as Numeric
	Data nTamNumero     as Numeric
	Data nTamItem       as Numeric
	

	Public  Method New(cStatus)
	Public  Method Destroy()
	Method SetFilterField()
	Method AddFieldFilt(aField, cField, cPrefix) 
	Private Method CreateTmpUp()
	Private Method CreateTmpDown()  
	Private Method StructTableUp()
	Private Method StructTableDown()
	Private Method TableIndexUp()
	Private Method TableIndexDown()
	Public  Method GetAliasUp()
	Public  Method GetAliasDown()
	Public  Method Reload()
	Private Method LoadTempSeek() 
	Private Method PosDadosRevatu()
	Private Method GravaTempUp(cDescMotivo, cDescAvPrev, cUsrName)
	Private Method GravaTempDown(cDescMotivo, cDescAvPrev, cUsrName)
	
	Private Method GetSx5Value(cTabela, cChave)
	Private Method AmtoCMP(cAm)

EndClass

Method New(cStatus) class TempApprovalBrowser

	::cAliTmpUp      := GetNextAlias() 
	::cAliTmpDown    := GetNextAlias() 
	::aStructUp      := {}
	::aStructDown    := {}
	::aFieldUp       := {}   
	::aFieldDown     := {} 
	::aIndexUp       := {}
	::aIndexDown     := {}
	::cAliUp         := GetNextAlias()
	::cAliDown       := GetNextAlias() 
	::cStatus        := cStatus

	::nTamIDPH3      := FwGetSx3Cache("PH3_ID"    , "X3_TAMANHO")
	::nTamNumero     := FwGetSx3Cache("PH3_NUMERO", "X3_TAMANHO")
	::nTamItem       := FwGetSx3Cache("PH3_ITEM"  , "X3_TAMANHO")
	

	Self:StructTableUp()
	Self:CreateTmpUp()

	Self:StructTableDown()
	Self:CreateTmpDown()


	Self:SetFilterField()
	
	Self:LoadTempSeek()

Return Self

Method Destroy() Class TempApprovalBrowser
	::oTempTableUp:Delete()
	FreeObj(::oTempTableUp)
	aSize(::aStructUp, 0)
	aSize(::aIndexUp , 0)

	::oTempTableDown:Delete()
	FreeObj(::oTempTableDown)
	aSize(::aStructDown, 0)
	aSize(::aIndexDown , 0)

Return


Method SetFilterField() Class TempApprovalBrowser

	Self:AddFieldFilt(::aFieldUp, "PH3_CONTRA", "PH3") 
	Self:AddFieldFilt(::aFieldUp, "PH3_REVISA", "PH3") 


	Self:AddFieldFilt(::aFieldDown, "PH3_DTCHAM", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_MOTBC" , "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_PROPOS", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_CONTRA", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_REVISA", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_NUMERO", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_ITEM"  , "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_TPNPSA", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_TPAPRO", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "AOM_DESCRI", "AOM") 
	Self:AddFieldFilt(::aFieldDown, "PH3_PRODUT", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_MENSAL", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_MENCAN", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_MENPRO", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_CMPCAN", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_AMTMPM", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "PH3_AMAVPR", "PH3") 
	Self:AddFieldFilt(::aFieldDown, "CNB_XCOMBO", "CNB") 
	Self:AddFieldFilt(::aFieldDown, "CNB_SITUAC", "CNB") 
	Self:AddFieldFilt(::aFieldDown, "CNB_TIPREC", "CNB") 
	Self:AddFieldFilt(::aFieldDown, "CNB_DTSITU", "CNB") 
	Self:AddFieldFilt(::aFieldDown, "CNB_MOEDA" , "PH3") 
	Self:AddFieldFilt(::aFieldDown, "CNB_CONDIC", "CNB") 
	Self:AddFieldFilt(::aFieldDown, "CNB_UNINEG", "CNB") 
	Self:AddFieldFilt(::aFieldDown, "ZRM_DTINC" , "ZRM") 
	Self:AddFieldFilt(::aFieldDown, "ZRM_CODSOL", "ZRM") 
	Self:AddFieldFilt(::aFieldDown, "PH3_ID"    , "PH3") 
	Self:AddFieldFilt(::aFieldDown, "ZRM_STATUS", "ZRM") 
Return

Method AddFieldFilt(aField, cField, cPrefix)  Class TempApprovalBrowser
	AADD(aField, {cField, RetTitle(cField), FwGetSx3Cache(cField, "X3_TIPO"), FwGetSx3Cache(cField, "X3_TAMANHO"), FwGetSx3Cache(cField, "X3_DECIMAL"), PesqPict(cPrefix, cField)})
Return

Method CreateTmpUp() Class TempApprovalBrowser
	::oTempTableUp := FWTemporaryTable():New( ::cAliTmpUp  )
	::oTempTableUp:SetFields(::aStructUp)
	Self:TableIndexUp()
	::oTempTableUp:Create()
Return


Method CreateTmpDown() Class TempApprovalBrowser

	::oTempTableDown := FWTemporaryTable():New( ::cAliTmpDown  )
	::oTempTableDown:SetFields(::aStructDown)
	Self:TableIndexDown()
	::oTempTableDown:Create()
Return


Method StructTableUp() Class TempApprovalBrowser

	AADD(::aStructUp, {"MARKOK"       , "L", 01, 0 })	
	AADD(::aStructUp, {"MARKNO"       , "L", 01, 0 })	

	AADD(::aStructUp, {"PHBOBS"       , "C", 250, 0 })  
	//AADD(::aStructUp, FWSX3Util():GetFieldStruct( "PH3_TPAPRO" ))  
	AADD(::aStructUp, FWSX3Util():GetFieldStruct( "PH3_FILIAL" ))  
	AADD(::aStructUp, FWSX3Util():GetFieldStruct( "PH3_CONTRA" ))  
	AADD(::aStructUp, FWSX3Util():GetFieldStruct( "PH3_REVISA" )) 
	AADD(::aStructUp, {"TOTQTDCAN", "N", 16, 2})  
	AADD(::aStructUp, {"VLTOTCAN" , "N", 16, 2})  
	AADD(::aStructUp, {"VLMENCAN" , "N", 16, 2})  
	AADD(::aStructUp, {"VLMENPRO" , "N", 16, 2}) 
	AADD(::aStructUp, FWSX3Util():GetFieldStruct( "ZRM_DTINC"  ))   

Return 

Method StructTableDown() Class TempApprovalBrowser
	Local aStruPH3 := {} as Array

	AADD(::aStructDown, {"MARKOK"     , "L", 01, 0 })
	AADD(::aStructDown, {"MARKNO"     , "L", 01, 0 })	
	AADD(::aStructDown, {"LEGEND"     , "C", 01, 0 })
	AADD(::aStructDown, {"DESCMOTIVO" , "C", 55, 0 }) 
	AADD(::aStructDown, {"DESAVPREV"  , "C", 55, 0 }) 
	AADD(::aStructDown, {"USRNOME"    , "C", 55, 0 }) 

	AADD(::aStructDown, {"ZRM_OBS"    , "C", 250, 0 })  
	AADD(::aStructDown, {"PH3_OBS"    , "C", 250, 0 })    

	
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_FILIAL" ))
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_CONTRA" ))  
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_CHAMAD" ))     
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_DTCHAM" ))     
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_MOTBC"  ))     
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_PROPOS" ))     
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_NUMERO" )) 
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_TPNPSA" )) 
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_TPAPRO" )) 
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "AOM_DESCRI" ))     
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_ITEM"   ))     
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_ITSEQ"  ))     
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_PRODUT" ))     
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_DESCRI" ))
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_QUANT"  ))       
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_QUANT"  ))     
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_QTDCAN" ))   
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_VLTOT"  ))    
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_MENSAL" ))       
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_MENCAN" ))       
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_MENPRO" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_CMPCAN" )) 

	aStruPH3 := FWSX3Util():GetFieldStruct( "PH3_CMPCAN" ) 
	aStruPH3[1] := "PH3_AMAVPR"     
	AADD(::aStructDown, aClone(aStruPH3))  
	aStruPH3[1] := "PH3_AMTMPM"       
	AADD(::aStructDown, aStruPH3)     
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_XCOMBO" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_SITUAC" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_TIPREC" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_DTSITU" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_VIGINI" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_VIGFIM" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_MOEDA"  ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_CONDIC" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_UNINEG" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_XFANTS" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_XQTDCB" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "ZRM_DTINC"  ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "ZRM_CODSOL" ))          
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_ID"     ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "ZRM_STATUS" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_REVISA" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "PH3_VLMULT" ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "CNB_VLUNIT" ))       
	     
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "ZRM_CHAVE"  ))      
	AADD(::aStructDown, FWSX3Util():GetFieldStruct( "ZRM_IDPROC" )) 

Return

Method TableIndexUp() Class TempApprovalBrowser
	Local nx as numeric

	aadd(::aIndexUp, {"PH3_CONTRA"})

	For nx:= 1 to len(::aIndexUp)
		::oTempTableUp:AddIndex(StrZero(nx, 2), ::aIndexUp[nx])
	Next 

Return

Method TableIndexDown() Class TempApprovalBrowser
	Local nx as numeric

	aadd(::aIndexDown, {"PH3_CONTRA", "PH3_NUMERO", "PH3_ITEM"})
	
	For nx:= 1 to len(::aIndexDown)
		::oTempTableDown:AddIndex(StrZero(nx, 2), ::aIndexDown[nx])
	Next 

Return

Method GetAliasUP() Class TempApprovalBrowser

Return ::oTempTableUp:GetAlias()


Method GetAliasDown() Class TempApprovalBrowser

Return ::oTempTableDown:GetAlias()


Method Reload() Class TempApprovalBrowser
	Local cAliTmpUp   := ::oTempTableUp:GetAlias()
	Local cAliTmpDown := ::oTempTableDown:GetAlias()

	::oTempTableUp:Zap()
	::oTempTableDown:Zap()
	Self:LoadTempSeek()

	(cAliTmpUp)->(DbGoTop())
	(cAliTmpDown)->(DbGoTop())

Return


Method LoadTempSeek() class TempApprovalBrowser

	Local nTamCodPrc  := FwGetSx3Cache("ZRM_CODPRC", "X3_TAMANHO")
	Local cCodPrcZrm  := Padr("APVCANCEL", nTamCodPrc)
	Local cChvZRM     := FwxFilial("ZRM") + cCodPrcZrm + "PH3"
	Local cDescMotivo := ""
	Local cDescAvPrev := ""
 	Local cUsrName    := ""


	ZRM->(DbSetOrder(3)) //ZRM_FILIAL+ZRM_CODPRC+ZRM_ALIAS+ZRM_CHAVE 
	PH3->(DbSetOrder(8)) //PH3_FILIAL+PH3_ID+PH3_NUMERO+PH3_ITEM+PH3_CONTRA+PH3_REVISA 
	CN9->(dbOrderNickName("CN9P03")) // CN9_FILIAL+CN9_SITUAC+CN9_ESPCTR+CN9_TPCTO+CN9_NUMERO   
	CNB->(DbSetOrder(1)) //CNB_FILIAL+CNB_CONTRA+CNB_REVISA+CNB_NUMERO+CNB_ITEM  
	CNA->(DbSetOrder(1)) //CNA_FILIAL+CNA_CONTRA+CNA_REVISA+CNA_NUMERO 
	AOM->(DbSetOrder(1)) //AOM_FILIAL+AOM_CODAGR+AOM_CODNIV     

	ZRM->(DbSeek(cChvZRM))

	While !(ZRM->(EOF())) .And. cChvZRM == ZRM->(ZRM_FILIAL + ZRM_CODPRC + ZRM_ALIAS)
		If ZRM->ZRM_STATUS <> ::cStatus
			
			If ! (ZRM->ZRM_STATUS == "W" .And. ::cStatus == "0")
				ZRM->(DbSkip())
				Loop
			EndIf
		EndIf

		Self:PosDadosRevatu()

		cDescMotivo := Self:GetSx5Value("_C", PH3->PH3_MOTBC )
		cDescAvPrev := Self:GetSx5Value("PE", PH3->PH3_PERIOD)
		cUsrName    := UsrFullName(ZRM->ZRM_CODSOL) 
		Self:GravaTempUp(cDescMotivo, cDescAvPrev, cUsrName)
		Self:GravaTempDown(cDescMotivo, cDescAvPrev, cUsrName)

		ZRM->(DbSkip())
	End
Return


Method PosDadosRevatu() class TempApprovalBrowser
	Local cIdPH3    := Left(ZRM->ZRM_CHAVE, ::nTamIDPH3  )
	Local cNumero   := SubStr(ZRM->ZRM_CHAVE, ::nTamIDPH3 + 1                , ::nTamNumero)
	Local cItem     := SubStr(ZRM->ZRM_CHAVE, ::nTamIDPH3 + 1 + ::nTamNumero , ::nTamItem  )
	Local cChavePH3 := FwxFilial("PH3") + cIdPH3 + cNumero + cItem

	If !PH3->(DbSeek(cChavePH3))
		Return .F.
	EndIf
	If !CN9->(DbSeek(FwxFilial("CN9") + "05" + "2" + "013" + PH3->PH3_CONTRA))
		Return .F.
	EndIf

	If !PH3->(DbSeek(cChavePH3 + CN9->CN9_NUMERO + CN9->CN9_REVISA))
		Return .F.
	EndIf

	If !CNB->(DbSeek(FwxFilial("CNB") + PH3->PH3_CONTRA + PH3->PH3_REVISA + PH3->PH3_NUMERO + PH3->PH3_ITEM))
		Return .F.
	EndIf

	If !CNA->(DbSeek(FwxFilial("CNA") + PH3->PH3_CONTRA + PH3->PH3_REVISA + PH3->PH3_NUMERO))
		Return .F.
	EndIf

	If !AOM->(DbSeek(FwxFilial("AOM") + "000001" + CNA->CNA_PICPAD))
		Return .F.
	EndIf


Return .T.

Method GravaTempUp(cDescMotivo, cDescAvPrev, cUsrName) class TempApprovalBrowser

	Local cAliTmpUp   := ::oTempTableUp:GetAlias() as Character
	Local dLastMonth  := Monthsum(DDataBase, -1)   as Date



	If (cAliTmpUp)->(DbSeek(PH3->PH3_CONTRA))
		(cAliTmpUp)->(RecLock(cAliTmpUp, .F.)	)

		(cAliTmpUp)->(TOTQTDCAN)  := (cAliTmpUp)->(TOTQTDCAN) + PH3->PH3_QTDCAN
		(cAliTmpUp)->(VLTOTCAN)   := (cAliTmpUp)->(VLTOTCAN)  + (PH3->PH3_QTDCAN * CNB->CNB_VLUNIT)	
		(cAliTmpUp)->(VLMENCAN)   := (cAliTmpUp)->(VLMENCAN) + PH3->PH3_MENCAN
		(cAliTmpUp)->(VLMENPRO)   := (cAliTmpUp)->(VLMENPRO) + PH3->PH3_MENPRO
		If ZRM->ZRM_DTINC >= dLastMonth
			(cAliTmpUp)->(ZRM_DTINC ) := ZRM->ZRM_DTINC
		EndIf 
	Else
		
		(cAliTmpUp)->(RecLock(cAliTmpUp, .T.)	)
		(cAliTmpUp)->(MARKOK    ) := .F. 	 
		(cAliTmpUp)->(MARKNO    ) := .F.	 
		(cAliTmpUp)->(PH3_FILIAL) := PH3->PH3_FILIAL
		(cAliTmpUp)->(PH3_CONTRA) := PH3->PH3_CONTRA
		(cAliTmpUp)->(TOTQTDCAN)  := PH3->PH3_QTDCAN
		(cAliTmpUp)->(VLTOTCAN)   := PH3->PH3_QTDCAN * CNB->CNB_VLUNIT	
		(cAliTmpUp)->(VLMENCAN)   := PH3->PH3_MENCAN
		(cAliTmpUp)->(VLMENPRO)   := PH3->PH3_MENPRO
		If ZRM->ZRM_DTINC >= dLastMonth
			(cAliTmpUp)->(ZRM_DTINC ) := ZRM->ZRM_DTINC
		EndIf 
		(cAliTmpUp)->(PHBOBS)     := Space(250)
	EndIf		

	(cAliTmpUp)->(MsUnLock())

Return

Method GravaTempDown(cDescMotivo, cDescAvPrev, cUsrName) class TempApprovalBrowser

	Local cAliTmpDown     := ::oTempTableDown:GetAlias() as Character

	Local ni                                     as Numeric
	Local nPosAli                                as Numeric
	Local cField                                 as Character
	Local cAliTab                                as Character
	Local xData                                  as Variant


	(cAliTmpDown)->(RecLock(cAliTmpDown, .T.)	)
	(cAliTmpDown)->(MARKOK    ) := .F. 	 
	(cAliTmpDown)->(MARKNO    ) := .F.	 
	(cAliTmpDown)->(LEGEND    ) := ZRM->ZRM_STATUS 
	(cAliTmpDown)->(DESCMOTIVO) := cDescMotivo	
	(cAliTmpDown)->(DESAVPREV ) := cDescAvPrev	
	(cAliTmpDown)->(USRNOME  ) := cUsrName

	For ni:= 1 to Len(::aStructDown)
		cField  := ::aStructDown[ni, 1]
		nPosAli := At("_", cField )
		If nPosAli == 0
			Loop
		EndIf

		cAliTab := Left(cField, nPosAli - 1)

		xData := (cAliTab)->(FieldGet(FieldPos(cField)))

		If cField $ "PH3_AMAVPR*PH3_AMTMPM"
			xData := ::AmtoCMP(xData)
		EndIf

		(cAliTmpDown)->(FieldPut(FieldPos(cField), xData) )

	Next

	(cAliTmpDown)->(MsUnLock())

Return


Method GetSx5Value(cTabela, cChave) class TempApprovalBrowser
	Local aValueX5 := FwGetSx5(cTabela, cChave) as Array
	Local cRetValue as Character
	Local ni        as Numeric

	For ni:=1 to Len(aValueX5)
		cRetValue := aValueX5[ni, 4]
	Next
Return cRetValue

Method AMtoCmp(cAM) class TempApprovalBrowser
    Local cCmp := Right(cAM, 2) + "/" + Left(cAM, 4)

Return cCmp



