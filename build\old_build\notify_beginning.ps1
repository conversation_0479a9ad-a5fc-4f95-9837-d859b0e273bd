Import-Module -name GHCWebhook
$uri = "https://chat.googleapis.com/v1/spaces/AAAAXD6T1Bo/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=I4MHd57Sqm02BIemDNi0MFkqIBpzJlUpPJ9MpTxhjEU%3D"

$message = New-GHCCardMessage -Content {
    New-GHCCardHeader -Title "Protheus v25" -Subtitle "Iniciando build e deploy" -ImageURL "https://img.icons8.com/color/48/000000/brazil.png" -ImageStyle avatar
    New-GHCCardSection -Content {
        New-GHCCardWidget -Content {
            New-GHCWidgetKeyValue -TopLabel "Ambiente" -Content "$Env:BUILD_SOURCEBRANCHNAME"   
            New-GHCWidgetKeyValue -TopLabel "Build" -Content "$Env:BUILD_BUILDNUMBER"
            New-GHCWidgetKeyValue -TopLabel "Iniciado por" -Content "$Env:BUILD_QUEUEDBY"
        }
        New-GHCCardWidget -Content {
            New-GHCWidgetTextButton -text "Acompanhe no TFS" -onclickurl "$Env:BUILD_REPOSITORY_URI"
        }
    }
}

Write-host "Iniciando build e deploy"

Send-GHCWebhookMessage -URI $uri -Message $message  >$null 2>&1
