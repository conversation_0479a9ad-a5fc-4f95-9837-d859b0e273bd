#INCLUDE "protheus.ch"
#include "FWBROWSE.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "TOPCONN.CH"
#Include "TOTVS.CH"

/*/{Protheus.doc} TITCTBA073
Higieniza registros duplicados de parcelas de diferimento na tabela P62, e corrige o saldo da P61 de acordo com as parcelas contabilizadas.
@type function
<AUTHOR>
@since 21/10/2024
@version 12.1.2410
/*/

User function TITCTBA073()


    BeginSQL Alias "QRYP62"
        SELECT COUNT(*) OVER (PARTITION BY ' ') TOTREG, P62.R_E_C_N_O_ RECNOP62 ,P62.* FROM %Table:P62% P62
        WHERE P62.%NotDel%
        ORDER BY P62_FILIAL,P62_DOC,P62_SERIE,P62_TIPO,P62_FORNEC,P62_<PERSON><PERSON>J<PERSON>,P62_REVI<PERSON>,P62_ITEM,P62_ITEMPC,P62_NUM,P62_PARCEL
    EndSQL

    FwMsgRun(,{|oSay| lRet := ProcDuplP62(oSay) },"Aguarde","Verificando registros duplicados")

    QRYP62->(DbCloseArea())

Return

Static Function ProcDuplP62(oSay)
    Local cKey  := ""
    Local nReg  := 1

    P61->(DbSetOrder(1))  
    While !QRYP62->(EoF())
        cKey := QRYP62->(P62_FILIAL+P62_DOC+P62_SERIE+P62_TIPO+P62_FORNEC+P62_LOJA+P62_REVISA+P62_ITEM+P62_ITEMPC+P62_NUM+P62_PARCEL)
        oSay:SetText("Verificando registro "+cValToChar(nReg)+ "/" +cValToChar(QRYP62->TOTREG))
        ProcessMessage()
        QRYP62->(DbSkip())
        nReg++
        While QRYP62->(P62_FILIAL+P62_DOC+P62_SERIE+P62_TIPO+P62_FORNEC+P62_LOJA+P62_REVISA+P62_ITEM+P62_ITEMPC+P62_NUM+P62_PARCEL) == cKey;
        .AND. !QRYP62->(EoF())
            If !Empty(QRYP62->P62_DTLANC)                                                                      
                If P61->(MsSeek(QRYP62->(P62_FILIAL+P62_DOC+P62_SERIE+P62_FORNEC+P62_LOJA+P62_REVISA+P62_ITEM+P62_ITEMPC+P62_NUM)))
                    Reclock("P61",.F.)
                    P61->P61_SALDO += QRYP62->(P62_VALOR)
                    MsUnlock()
                EndIf
            EndIF
            P62->(DbGoTo(QRYP62->RECNOP62))
            Reclock("P62",.F.)
            DbDelete()
            MsUnlock()
            QRYP62->(DbSkip())
            nReg++
        End
    End
Return

/*
SELECT P61_FILIAL, P61_DOC, P61_REVISA, P61_FORNEC, SUM(P61_SALDO) SOMAP61SALDO  
FROM P61000  
WHERE D_E_L_E_T_ = ' ' 
GROUP BY P61_FILIAL, P61_DOC, P61_FORNEC, P61_REVISA  


SELECT P62_FILIAL, P62_DOC, P62_REVISA, P62_FORNEC, SUM(P62_VALOR) SOMAP62VALOR 
FROM P62000 
WHERE D_E_L_E_T_ = ' ' AND P62_DTLANC = ' '  
GROUP BY P62_FILIAL, P62_DOC, P62_FORNEC, P62_REVISA 


*/
