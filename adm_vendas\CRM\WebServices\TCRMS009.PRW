#INCLUDE "PROTHEUS.CH"
#INCLUDE "APWEBSRV.CH"
#INCLUDE "TOPCONN.CH"  

/*/{Protheus.doc} CLASSE TCRMS009
WebService para retorno de dados de vendedor

<AUTHOR>
@since 28/07/2016
@version 1.0
/*/


User Function LstCli(eMailID)
    Local cVend		:= ""
	Local aSttCli	:= {}
	Local nCont		:= 1
	Local cSTRSQL	:= ""
	Local cEmail     
	Local cDesc		:= ""
    
    cEmail := Iif ( Empty(eMailID) , "<EMAIL>" , eMailID )                    
	cEmail := U_ValInput(cEmail)

   	cSTRSQL := " SELECT SA3.A3_COD FROM "+ RetSqlName("SA3") +" SA3 "
	cSTRSQL += " WHERE SA3.A3_FILIAL = '"+ xFilial("SA3") +"' AND UPPER(SA3.A3_EMAIL) LIKE '%" + Upper(cEmail) + "%'"
	cSTRSQL += " AND SA3.D_E_L_E_T_ <> '*'"                                                              
	TCQuery cSTRSQL Alias VENDEDOR New   
	
	DbSelectArea("VENDEDOR")
	VENDEDOR->(DbGoTop())
	
	While !VENDEDOR->(EoF())     
    	cVend += VENDEDOR->A3_COD + ";"
    	VENDEDOR->(DbSkip())    
    EndDo
    VENDEDOR->(DbCloseArea())  
    
    cVend := SubStr( cVend , 1 , Len(cVend)-1 )
    
    If Len(cVend) > 0
    
    	cSTRSQL	:= " SELECT SA1.A1_COD, SA1.A1_LOJA, SA1.A1_NOME, SA1.A1_CGC, SA1.A1_CODSEG FROM "+ RetSqlName("SA1") +" SA1 "
	    cSTRSQL	+= " WHERE SA1.A1_FILIAL = '"+ xFilial("SA1") +"' AND (SA1.A1_VEND IN "+ FormatIn(cVend,";") + ")"
	    cSTRSQL	+= " AND SA1.D_E_L_E_T_ <> '*'" 
		TCQuery cSTRSQL Alias CLIENTES New
	
		While !CLIENTES->(Eof())
		
		cDesc := Posicione("AOV",1,XFILIAL("AOV")+CLIENTES->A1_CODSEG,"AOV_DESSEG")

	   	AAdd(aSttCli, WSClassNew("CliStruct"))
		   	aSttCli[nCont]:cCODIGO		:= AllTrim( CLIENTES->A1_COD    )
			aSttCli[nCont]:cLOJA		:= AllTrim( CLIENTES->A1_LOJA   )	   	
			aSttCli[nCont]:cDESCRICAO	:= AllTrim( CLIENTES->A1_NOME   )
			aSttCli[nCont]:cCNPJ		:= AllTrim( CLIENTES->A1_CGC    )
			aSttCli[nCont]:cCODSEG		:= AllTrim( CLIENTES->A1_CODSEG )
			aSttCli[nCont]:cDESCSEG		:= AllTrim( cDesc  				)
			CLIENTES->(dbSkip())
		
			nCont ++
		EndDo     
   		CLIENTES->(DbCloseArea())
   		
  	Else
  	
  		AAdd(aSttCli, WSClassNew("CliStruct"))
		  	aSttCli[nCont]:cCODIGO		:= "Participante nao e EAR"
			aSttCli[nCont]:cLOJA		:= ""	   	
			aSttCli[nCont]:cDESCRICAO	:= ""
			aSttCli[nCont]:cCNPJ		:= ""
			aSttCli[nCont]:cCODSEG		:= ""
			aSttCli[nCont]:cDESCSEG		:= ""
  	
  	EndIf
Return aSttCli