#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'FWMVCDEF.CH'
#INCLUDE 'ESPA050.CH'

User Function ESPA050()
	
	Local aArea      := GetArea()
	
	Local oBrowse    := FWMBrowse():New()
	
	oBrowse:Set<PERSON><PERSON><PERSON>("PVM")
	oBrowse:SetDescription(STR0001)
	oBrowse:Activate()
	RestArea(aArea)
	
Return

Static Function MenuDef()
	
	Local aRotina := {}
	
	ADD OPTION aRotina TITLE "Visualizar"     ACTION "VIEWDEF.ESPA050" OPERATION MODEL_OPERATION_VIEW   ACCESS 0
	ADD OPTION aRotina TITLE "Incluir"        ACTION "VIEWDEF.ESPA050" OPERATION MODEL_OPERATION_INSERT ACCESS 0
	ADD OPTION aRotina TITLE "Alterar"        ACTION "VIEWDEF.ESPA050" OPERATION MODEL_OPERATION_UPDATE ACCESS 0
	ADD OPTION aRotina TITLE "Excluir"        ACTION "VIEWDEF.ESPA050" OPERATION MODEL_OPERATION_DELETE ACCESS 0
	
Return aRotina

Static Function ModelDef()
	
	Local oStrPVM := FWFormStruct(1, "PVM")
	Local oModel  := MPFormModel():New("PVMACE", , )
	
	oModel:AddFields("PVMMASTER",, oStrPVM)
	oModel:SetPrimaryKey({"PVM_FILIAL", "PVM_PAIS", "PVM_CODACE"})
	oModel:SetDescription(STR0001)
	oModel:GetModel("PVMMASTER"):SetDescription(STR0001)
	
Return oModel

Static Function ViewDef()
	
	Local oModel  := ModelDef()
	Local oStrPVM := FWFormStruct(2, "PVM")
	Local oView   := FWFormView():New()
	
	oView:SetModel(oModel)
	oView:AddField("VIEW_PVM", oStrPVM, "PVMMASTER")
	oView:CreateHorizontalBox("PVM", 100)
	oView:EnableTitleView("VIEW_PVM",STR0002)
	oView:SetCloseOnOk({||.T.})
	oView:SetOwnerView("VIEW_PVM", "PVM")
	oView:AddUserButton(STR0003, "", { |oModel| CArquivo(oModel) } )
	
Return oView

Static Function CArquivo(oModel)
	
	Local lRet       := .F. 
	Local cExtensao  := ""
	
	If Pergunte("FSWUPDARQ", .T.)
	
		SplitPath (Alltrim(MV_PAR01),,,, @cExtensao)
		
		cNome := RetArq( ,Alltrim(MV_PAR01), .F.) + cExtensao
		
		lRet := __CopyFile( Alltrim(MV_PAR01), Alltrim(MV_PAR02) + cNome )
		
		If lRet == .T.
			MsgAlert(STR0004)
		Else
			MsgAlert(STR0005)
		EndIf
		
	EndIf
	
Return
