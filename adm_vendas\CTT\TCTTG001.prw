#Include "Protheus.ch"
//-----------------------------------------------------------------------------
/*/ {Protheus.doc} Function
Gatilho para preenchimento do campo PD9_QTDHRS

<AUTHOR> Mobile Costa
@version P12
@since 16/04/2015
/*/
//-----------------------------------------------------------------------------

User Function TCTTG001()

Local nPD9_QTDHRS := M->PD9_QTDE

oModel := FWModelActive()
oModelPD9 := oModel:GetModel("PD9")
nPD9_QTDHRS *= Posicione("PDM", 1, x<PERSON><PERSON>l("PDM") + oModelPD9:GetValue("PD9_CURSO"), "PDM_QTDHOR")

Return If(nPD9_QTDHRS > 999, 999, nPD9_QTDHRS)