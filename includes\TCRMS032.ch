#ifdef SPANISH
		#define STR0001 "Retona quantidade de vagas disponíveis de um município"
		#define STR0002 "Vagas disponíveis de um município"
		#define STR0003 "É obrigatório informar o município"
		#define STR0004 "Município informado não existe no cadastro de capilaridade"
		#define STR0005 "Realiza reserva/cancelamento/confirmação do municipio"
		#define STR0006 "Reserva/cancelamento/confirmação de municipio"
		#define STR0007 "ABERTO"
		#define STR0008 "FECHADO"
		#define STR0009 "Tipo de operação (RESERVA|CANCELA|CONFIRMA) não informado"
		#define STR0010 "Erro na operacao solicitada"
		#define STR0011 "NAO REALIZADO - MOTIVO: vaga indisponivel"
		#define STR0012 "NAO REALIZADO - MOTIVO: NAO EXISTEM RESERVAS PARA SEREM CANCELADAS"
		#define STR0013 "NAO REALIZADO - MOTIVO: NAO EXISTEM RESERVAS PARA SEREM CONFIRMADAS"
		#define STR0014 "É obrigatório informar o CPF"
		#define STR0015 "Não é possível prosseguir. CPF informado pertence a um profissional que saiu há menos de "
		#define STR0016 " meses da TOTVS"
		#define STR0017 "CPF informado não encontrado na tabela de Pessoas/Participantes"
		#define STR0018 "Não é possível prosseguir. CPF informado pertence a um TOTVER"
		#define STR0019 "Validacao de CPF"
		#define STR0020 "Valida CPF informado"
#else
	#ifdef ENGLISH
		#define STR0001 "Retona quantidade de vagas disponíveis de um município"
		#define STR0002 "Vagas disponíveis de um município"
		#define STR0003 "É obrigatório informar o município"
		#define STR0004 "Município informado não existe no cadastro de capilaridade"
		#define STR0005 "Realiza reserva/cancelamento/confirmação do municipio"
		#define STR0006 "Reserva/cancelamento/confirmação de municipio"
		#define STR0007 "ABERTO"
		#define STR0008 "FECHADO"
		#define STR0009 "Tipo de operação (RESERVA|CANCELA|CONFIRMA) não informado"
		#define STR0010 "Erro na operacao solicitada"
		#define STR0011 "NAO REALIZADO - MOTIVO: vaga indisponivel"
		#define STR0012 "NAO REALIZADO - MOTIVO: NAO EXISTEM RESERVAS PARA SEREM CANCELADAS"
		#define STR0013 "NAO REALIZADO - MOTIVO: NAO EXISTEM RESERVAS PARA SEREM CONFIRMADAS"
		#define STR0014 "É obrigatório informar o CPF"
		#define STR0015 "Não é possível prosseguir. CPF informado pertence a um profissional que saiu há menos de "
		#define STR0016 " meses da TOTVS"
		#define STR0017 "CPF informado não encontrado na tabela de Pessoas/Participantes"
		#define STR0018 "Não é possível prosseguir. CPF informado pertence a um TOTVER"
		#define STR0019 "Validacao de CPF"
		#define STR0020 "Valida CPF informado"
	#else
		#define STR0001 "Retona quantidade de vagas disponíveis de um município"
		#define STR0002 "Vagas disponíveis de um município"
		#define STR0003 "É obrigatório informar o município"
		#define STR0004 "Município informado não existe no cadastro de capilaridade"
		#define STR0005 "Realiza reserva/cancelamento/confirmação do municipio"
		#define STR0006 "Reserva/cancelamento/confirmação de municipio"
		#define STR0007 "ABERTO"
		#define STR0008 "FECHADO"
		#define STR0009 "Tipo de operação (RESERVA|CANCELA|CONFIRMA) não informado"
		#define STR0010 "Erro na operacao solicitada"
		#define STR0011 "NAO REALIZADO - MOTIVO: vaga indisponivel"
		#define STR0012 "NAO REALIZADO - MOTIVO: NAO EXISTEM RESERVAS PARA SEREM CANCELADAS"
		#define STR0013 "NAO REALIZADO - MOTIVO: NAO EXISTEM RESERVAS PARA SEREM CONFIRMADAS"	
		#define STR0014 "É obrigatório informar o CPF"
		#define STR0015 "Não é possível prosseguir. CPF informado pertence a um profissional que saiu há menos de "
		#define STR0016 " meses da TOTVS"
		#define STR0017 "CPF informado não encontrado na tabela de Pessoas/Participantes"
		#define STR0018 "Não é possível prosseguir. CPF informado pertence a um TOTVER"
		#define STR0019 "Validacao de CPF"
		#define STR0020 "Valida CPF informado"
	#endif
#endif


