#include "tlpp-core.th"

Class   SendEmailProcessService
    Private Data    cBodyEmail                                                          As  Character
    Private Data    lStatusEnvio                                                        As  Logical

    Public  Method  new()                                                               As  Object
    Public  Method  setJsonDataToSendEmail(cValue   As  Character)                      As  Object
    Public  Method  SendEmailProcessServiceToClient()                                   As  Object
    Public  Method  getStatus()                                                         As  Logical
    Private Method  sendDirectlyToAws(cJson As  Character)                              As  Object
    Private Method  sendIpass(cJson As  Character)                                      As  Object
    Private Method  sendGoogleChatNotify()                                              As  Object
EndClass


Method  new()   As  Object  Class   SendEmailProcessService
    ::cBodyEmail    :=    ''
    ::lStatusEnvio  :=  .F.
Return Self


Method  setJsonDataToSendEmail(cValue   As  Character)    As  Object  Class   SendEmailProcessService
    ::cBodyEmail  :=    cValue
Return Self

Method  SendEmailProcessServiceToClient() As  Object  Class   SendEmailProcessService   
    If GetMV("TI_TIPD004",,.T.) 
        ::sendIpass(::cBodyEmail)
    Else
        ::sendDirectlyToAws(::cBodyEmail)
    EndIf
Return Self

Method  sendIpass(cJson As  Character) As  Object  Class   SendEmailProcessService
    Local   cUrl    := GetMV("TI_TIPD005",,"https://api-ipaas.totvs.app/ipaas/api/v1/integrations/bedf3666-b94d-4893-b51c-e3ddb8b20439/api-key/34084cc2-4eb4-4e09-8774-722909561c37")
    Local   oIpaas  :=  Nil
	
    oIpaas := TIiPaaS():New()
	oIpaas:SetWebHook(cUrl)
	oIpaas:SetBodyWebHook(cJson)
	oIpaas:SendToiPaaS()
    ::lStatusEnvio  :=  oIpaas:getIpaasStatus()
    If  (::lStatusEnvio <> .T.) 
        ::sendDirectlyToAws(cJson)
        ::sendGoogleChatNotify()
    EndIf
    FreeObj(oIpaas)
	oIpaas := Nil 
		

Return Self


Method  sendDirectlyToAws(cJson As  Character) As  Object  Class   SendEmailProcessService

    Local   oAws        :=  Nil     As  Object
    Local   aStatus     :=  {}      As  Array
    
    Local   cUser       := GetMV("TI_TIPD002",,'6n7h68dlu2f82qbem6t9istinm')                            As  Character
    Local   cPassword   := GetMV("TI_TIPD003",,'85bn53m9so16g1dkte5c0itr6jttad69m2equb96vp672tnpdq1')   As  Character  
    Local   cTokenUrl   := GetMV("TI_TIPD006",,'https://stg-apihub.auth.us-east-1.amazoncognito.com')   As  Character 
    Local   cTokenPath  := GetMV("TI_TIPD007",,'/oauth2/token?grant_type=client_credentials')           As  Character 
    Local   cApiUrl     := GetMV("TI_TIPD008",,'https://apihub.totvs.com.br')                           As  Character 
    Local   cApiPath    := GetMV("TI_TIPD009",,'/stg/api/v1/email/sync')                                As  Character 


    oAws = consumeAwsApis():new()
    oAws:setUserCognito(cUser)
    oAws:setPasswordCognito(cPassword)
    oAws:setTokenUrl(cTokenUrl)
    oAws:setTokenPath(cTokenPath)

    oAws:setApiUrl(cApiUrl)
    oAws:setApiPath(cApiPath)
    oAws:setBodyToPost(cJson)
    oAws:execRequest()

    aStatus :=  oAws:returnStatusRequestPost() 
    ::lStatusEnvio :=   aStatus[1]

    FreeObj(oAws)
	oAws := Nil 
		

Return Self






Method  sendGoogleChatNotify()  As  Object Class   SendEmailProcessService
    Local   aHeader         :=  {}  As  Array
    Local   cMessage        :=  ''  As  Character
    Local   cUrlBot         :=  GetMV('TI_TIPD010',,'https://chat.googleapis.com/v1/spaces/AAAAoZZOI6Q/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=hWAeYDP5D0xbU3H2xzxfAx_CB5-DJ6bAMPdGWZNlUzQ') As  Character
    Local   cBody           :=  ''  As  Character
    
    cMessage := 'Falha ao enviar atraves do Ipaas, email enviado diretamente para AWS'
    cBody       :=  '{"text":"'+cMessage+'"}'

    AAdd(aHeader, "Content-Type: application/json")
	AAdd(aHeader, "Accept: application/json")
    HttpPost(cUrlBot,,cBody,,aHeader)
Return Self



Method  getStatus()     As  Logical Class   SendEmailProcessService
Return  ::lStatusEnvio
