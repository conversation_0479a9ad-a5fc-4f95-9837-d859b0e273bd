#Include "Totvs.Ch"
#Include "RESTFUL.Ch"
#Include 'Protheus.ch'

/*/
{Protheus.doc} TIGESJ01
Funcao responsavel por salvar na pasta do EDI
@type  Function
<AUTHOR>
@since  01/01/2022
@version 1.0
 */

User Function TIGESJ01()
	Local oInter
	Local cCodApi := "000049"
	If Select("SM0") == 0
		RpcSetEnv( '00', '00001000100')
	EndIf
	cCodApi:= GetMv("TI_GESPA1",,'000049')
	cValor:=  ''
	cVal2 := ''
	oInter:=TIINTERCEPTOR():New()
	cIdReq:=""
	cIdReq:=oInter:GetP37COD()
	IF(Empty(Alltrim(cIdReq)))
		cIdReq		   := GETSXENUM("P37","P37_COD")
		ConfirmSX8()
	EndIF
	FREEOBJ( oInter )
	Reclock("P37",.T.)
	P37->P37_FILIAL:= xFilial("P37")
	P37->P37_COD   := cIdReq
	P37->P37_DATA  := DDATABASE
	P37->P37_HORA  := TIME()
	P37->P37_CODAPI:= cCodApi
	If P36->(DbSeek(xFilial("P36")+P37->P37_CODAPI))
		P37->P37_URL   := P36->P36_URL
		P37->P37_PATH := P36->P36_PATH
	EndIf
	P37->P37_METHOD:= "GET"
	P37->P37_ASYNC1:= "N"
	P37->P37_STATUS:= "0"
	P37->P37_TIPORE:= "1"//Envio
	P37->P37_CALLBA:= "U_TIGES001"
	P37->(MsUnLock())
Return


 /*/
	{Protheus.doc} TIGES001
	Funcao responsavel por CALLBACK EXECUTADA p37
	@type  Function
	<AUTHOR> Robes
	@since  01/01/2022
	@version 1.0
 */


USer Function TIGES001(cCodReq,cResponse,cAsync,cStatReq)

	local  oRecep   :=TIINTERCEPTOR():New()
	Local cTypeErro := 2
	Local cCodApi 	:= GetMv("TI_GESPA2",,'000050')
	Local nU := 0
	uJsonResp :=oRecep:SearchJsonKey(cResponse,"arquivos")
	IF  Valtype(uJsonResp)=="A"
		For nU := 1 to len(uJsonResp)
			cIdReq:=""
			cIdReq:=oRecep:GetP37COD()
			IF(Empty(Alltrim(cIdReq)))
				cIdReq		   := GETSXENUM("P37","P37_COD")
				ConfirmSX8()
			EndIF
			Reclock("P37",.T.)
			P37->P37_FILIAL:= xFilial("P37")
			P37->P37_COD   := cIdReq
			P37->P37_DATA  := DDATABASE
			P37->P37_HORA  := TIME()
			P37->P37_CODAPI:= cCodApi
			If P36->(DbSeek(xFilial("P36")+P37->P37_CODAPI))
				P37->P37_URL   := P36->P36_URL
				P37->P37_PATH := alltrim(P36->P36_PATH) +uJsonResp[nU]["cfilename"]
			EndIf
			P37->P37_METHOD:= "GET"
			P37->P37_ASYNC1:= "N"
			P37->P37_STATUS:= "0"
			P37->P37_TIPORE:= "1"//Envio
			P37->P37_CALLBA:= "U_TIGES002"
			P37->P37_CODREQ := cCodReq
			P37->(MsUnLock())
		Next

	Else
		cTypeErro:="3"
	EndIF
	if(!Empty(Alltrim(cTypeErro)))
		Reclock("P37",.F.)
		P37->P37_ASYNC1 :="N"//Finalizando a Async
		P37->P37_STATUS :=cTypeErro
		P37->(MsUnlock())
	EndIF
	FREEOBJ( oRecep )
Return

 /*/
	{Protheus.doc} TIGES002
	Funcao responsavel por CALLBACK EXECUTADA p37
	@type  Function
	<AUTHOR> Robes
	@since  01/01/2022
	@version 1.0
 */

USer Function TIGES002(cCodReq,cResponse,cAsync,cStatReq)

	Local cBody := cResponse
	local  oRecep   :=TIINTERCEPTOR():New()
	Local cTypeErro := 2
	Local cCodApi := GetMv("TI_GESPA3",,"000051")


	uJsonBin :=oRecep:SearchJsonKey(cResponse,"bin")
	uJsonFil :=oRecep:SearchJsonKey(cResponse,"cFilial")
	uJsonName :=oRecep:SearchJsonKey(cResponse,"cfilename")
	If  Valtype(uJsonBin) <> 'U'
		lRet:= GRVARQ(uJsonBin,uJsonFil,uJsonName)
		If lRet
			cIdReq:=""
			cIdReq:=oRecep:GetP37COD()
			IF(Empty(Alltrim(cIdReq)))
				cIdReq		   := GETSXENUM("P37","P37_COD")
				ConfirmSX8()
			EndIF
			Reclock("P37",.T.)
			P37->P37_FILIAL:= xFilial("P37")
			P37->P37_COD   := cIdReq
			P37->P37_DATA  := DDATABASE
			P37->P37_HORA  := TIME()
			P37->P37_CODAPI:= cCodApi
			If P36->(DbSeek(xFilial("P36")+P37->P37_CODAPI))
				P37->P37_URL   := P36->P36_URL
				P37->P37_PATH := alltrim(P36->P36_PATH) +uJsonName
			EndIf

			P37->P37_METHOD:= "POST"
			P37->P37_ASYNC1:= "N"
			P37->P37_STATUS:= "0"
			P37->P37_TIPORE:= "1"//Envio
			P37->P37_CODREQ := cCodReq
			P37->P37_CALLBA:= "U_TIGES003"
			P37->(MsUnLock())
		Else
			cTypeErro := "3"
		EndiF
	Else
		cTypeErro := "3"
	EndiF
	if(!Empty(Alltrim(cTypeErro)))
		Reclock("P37",.F.)
		P37->P37_ASYNC1 :="N"//Finalizando a Async
		P37->P37_STATUS :=cTypeErro
		P37->(MsUnlock())
	EndIF
	FREEOBJ( oRecep )
Return

 /*/{Protheus.doc} GRVARQ
	Funcao responsavel GRAVAR ARQUIVO NA PASTA
	@type  Function
	<AUTHOR> Robes
	@since  01/01/2022
	@version 1.0
 */

Static Function  GRVARQ(uJsonBin,uJsonFil,uJsonName)

	local lRet := .T.
	Local cPathEdi :=  ''
	Local nI:= 0
	Default uJsonBin  := ''
	Default uJsonFil  := ''
	Default uJsonName := ''

	cPathEdi		:= SuperGetMv("MV_LOCENV", .F.,"",uJsonFil)

	aDir			:= Separa(cPathEdi,"\",.F.)
	cDir			:= ""
	For nI:= 1 To Len(aDir)
		cDir		+= "\"+aDir[nI]
		nDir 		:= MakeDir( cDir )
	Next nI
	uJsonName:= Replace(UPPER(uJsonName),".TXT",".00")
	nHandle 		:= MsfCreate(cPathEdi+uJsonName,0)
	if (nHandle > 0)
		FWrite(nHandle, decode64(uJsonBin))
		FClose(nHandle)
	Else
		lRet := .F.
	EndIF

Return lRet

/*/{Protheus.doc} TIGES002
Funcao responsavel por CALLBACK EXECUTADA p37 
@type  Function
<AUTHOR> Robes
@since  01/01/2022
@version 1.0
 */

USer Function TIGES003(cCodReq,cResponse,cAsync,cStatReq)
local  oRecep   :=TIINTERCEPTOR():New()
Local  uJsonStat :=oRecep:SearchJsonKey(cResponse,"status")
           
    If  uJsonStat != 200 
        RecLock("P37", .F.)
        P37->P37_STATUS := '3'
        P37->(MsUnlock())
        
    EndIf 		
	
Return   
