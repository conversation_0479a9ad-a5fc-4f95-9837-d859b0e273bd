#Include "Protheus.ch"
#Include "RwMake.ch"

User Function TCORPM02()

	Local _aDados := {}
	Local _aCodver := {}
	Local _aPedido := {}
	
	Local _cStatus := ""
	Local _cCodCalc := ""
	
	Local _nCount := 0
	
	_aDados := _fMakeQry()


// [01] ZB2.ZB2_CLIENT 			CLIENTE 
// [02] ZB2.ZB2_LOJA 			LOJA 
// [03] ZB2.ZB2_CONTRA 			CONTRATO 
// [04] ZB2.ZB2_CODVER 			CODATU 
// [05] ZB2.ZB2_PROPOS 			PROPOSTA 
// [06] SUBSTR(ZB2.ZB2_SITFAT,1,2)	EMPRESA 
// [07] SUBSTR(ZB2.ZB2_SITFAT,3,2)	FILIAL 
// [08] ZB2_ANOREF 
// [09] MAX(ZB2.ZB2_CONDPG) 	CONDPG 
// [10] ZB2.ZB2_CALC 			CALC 
// [11] ZB2.ZB2_TIPO 			TIPO
// [12] ZB2.ZB2_USERGI 			USERGI 
// [13] ZB2.ZB2_USERGA 			USERGA 
// [14] ZB2.ZB2_OBSCAL 			OBSCAL 	
// [15] ZB2.ZB2_DATCAL 			DATCAL 	
	
	APCFG110()
	If !Empty(_aDados)

			For _nCount := 1 To Len(_aDados)
				Begin Transaction
				
				_aCodver := {}
				_aPedido := {}
				
				_aCodver := _fContrato(_aDados[_nCount][7],_aDados[_nCount][5])
				
				If !Empty(_aCodver)
					_aPedido := _fPedido(_aDados[_nCount][7],_aDados[_nCount][5])
				EndIf
				
				_cStatus := "1"
				
				_cCodCalc := GetSxeNum("PH0","PH0_CODIGO")
				PH0->(ConfirmSX8())
				
				RecLock("PH0",.T.)
					PH0->PH0_FILIAL := xFilial("PH0")
					PH0->PH0_CODIGO := _cCodCalc
					PH0->PH0_CLIENT := _aDados[_nCount][1]
					PH0->PH0_LOJA := _aDados[_nCount][2]
					PH0->PH0_CONTRA := _aDados[_nCount][3]
					PH0->PH0_VERATU := _aDados[_nCount][4]
					PH0->PH0_PROPOS := _aDados[_nCount][5]
					PH0->PH0_GRPFAT := _aDados[_nCount][6]
					PH0->PH0_FILFAT := _aDados[_nCount][7]
					PH0->PH0_ANOREF := _aDados[_nCount][8]
					PH0->PH0_CONDPG := _aDados[_nCount][9]
					PH0->PH0_USERGI := _aDados[_nCount][12]					
					PH0->PH0_USERGA := _aDados[_nCount][13]					
					PH0->PH0_OBSCAL := _aDados[_nCount][14]					
					PH0->PH0_DATCAL := SToD(_aDados[_nCount][15])					
					PH0->PH0_TIPCAL := "N"		

					If _aDados[_nCount][11] $ '1|7' // 1-Incremento ou 7=Grat Increm
						PH0->PH0_CALC := 'I'
						PH0->PH0_MULTA := 'N'
						PH0->PH0_EXCECA := 'N'
					ElseIf _aDados[_nCount][11] $ '2|8' // 2-Multa 1a.Data ou 8-Grat 1a.Multa
						PH0->PH0_CALC := 'I'
						PH0->PH0_MULTA := 'S'
						PH0->PH0_EXCECA := 'N'
					ElseIf _aDados[_nCount][11] $ '3|A' // 3-Multa 2a.Data ou A-Grat 2a.Multa
						PH0->PH0_CALC := 'D'
						PH0->PH0_MULTA := 'S'
						PH0->PH0_EXCECA := 'N'
					ElseIf _aDados[_nCount][11] $ '4|C' // 4-Diferenca ou C-Grat Diferenca
						PH0->PH0_CALC := 'D'
						PH0->PH0_MULTA := 'N'
						PH0->PH0_EXCECA := 'N'
					ElseIf _aDados[_nCount][11] == '5' // 5-Exce Increm
						PH0->PH0_CALC := 'I'
						PH0->PH0_MULTA := 'N'
						PH0->PH0_EXCECA := 'S'
					ElseIf _aDados[_nCount][11] == '6' // 6-Exce 1a.Multa
						PH0->PH0_CALC := 'I'
						PH0->PH0_MULTA := 'S'
						PH0->PH0_EXCECA := 'S'
					ElseIf _aDados[_nCount][11] == '9' // 9-Exce 2a.Multa
						PH0->PH0_CALC := 'D'
						PH0->PH0_MULTA := 'S'
						PH0->PH0_EXCECA := 'S'
					ElseIf _aDados[_nCount][11] == 'B' // B-Exce Diferenca
						PH0->PH0_CALC := 'D'
						PH0->PH0_MULTA := 'N'
						PH0->PH0_EXCECA := 'S'
					EndIf 
					
					If !Empty(_aDados[_nCount][5])
						_cStatus := "2"
					EndIf
					
					If !Empty(_aCodver)
						PH0->PH0_VERGER := _aCodver[1][1]
						_cStatus := "3"
					EndIf 
	
					If !Empty(_aPedido)
						PH0->PH0_PEDCDU := _aPedido[1][1]
						_cStatus := _aPedido[1][2]
					EndIf
					PH0->PH0_STATUS := _cStatus
				
				PH0->(MsUnLock())
				
				ZB2->(DbSetOrder(1))
//				If ZB2->(DbSeek(xFilial("ZB2")+_aDados[_nCount][1]+_aDados[_nCount][2]+_aDados[_nCount][8]))
				If ZB2->(DbSeek("  "+_aDados[_nCount][1]+_aDados[_nCount][2]+_aDados[_nCount][8]))
					If !Empty(ZB2->ZB2_CODIGO)

						If Z39->(DbSeek("  "+ZB2->ZB2_CODIGO))
							RecLock("PH0",.F.)							
							PH0->PH0_TIPREC := Z39->Z39_TIPREC
							PH0->PH0_STAREP := Z39->Z39_STAREP
							PH0->PH0_PEDREP := Z39->Z39_PEDREP
							PH0->(MsUnLock())
						Else
							Aviso("ERRO", "NAO ENCONTROU PH0 EQUIVALENTE", {"ok"})
						EndIf
												
					EndIf
//					While xFilial("ZB2")+_aDados[_nCount][1]+_aDados[_nCount][2]+_aDados[_nCount][8] == ZB2->(ZB2_FILIAL+ZB2_CLIENT+ZB2_LOJA+ZB2_ANOREF)
					While "  "+_aDados[_nCount][1]+_aDados[_nCount][2]+_aDados[_nCount][8] == ZB2->(ZB2_FILIAL+ZB2_CLIENT+ZB2_LOJA+ZB2_ANOREF)						
					
						//If ZB2->ZB2_CALC == _aDados[_nCount][12]
						//	RecLock("ZB2",.F.)
						//		ZB2->ZB2_CODIGO = _cCodCalc
						//	ZB2->(MsUnLock())
						//EndIf
					
						RecLock("PH1", .T.)
						PH1->PH1_FILIAL	:= xFilial("PH1") 
						PH1->PH1_CODIGO	:= _cCodCalc
						PH1->PH1_ITEM 	:= ZB2->ZB2_SEQ
						PH1->PH1_CODPRD 	:= ZB2->ZB2_PRODUT
						PH1->PH1_VLRCAL 	:= ZB2->ZB2_VLCALC
						PH1->PH1_VENCTO 	:= ZB2->ZB2_VENCTO 
						PH1->PH1_TIPPRD 	:= ZB2->ZB2_TPPROD
						PH1->(MsUnLock())				
				
						ZB2->(DbSkip())
					EndDo
				EndIf
				End Transaction
			Next
	EndIf
	
Return

Static Function _fMakeQry()

	Local _cQuery := ""

	_cQuery := " SELECT " + CRLF
	_cQuery += "	 ZB2.ZB2_CLIENT CLIENTE " + CRLF
	_cQuery += "	,ZB2.ZB2_LOJA LOJA " + CRLF
	_cQuery += "	,ZB2.ZB2_CONTRA CONTRATO " + CRLF
	_cQuery += "	,ZB2.ZB2_CODVER CODATU " + CRLF
	_cQuery += "	,ZB2.ZB2_PROPOS PROPOSTA " + CRLF
	_cQuery += "	,SUBSTR(ZB2.ZB2_SITFAT,1,2)	EMPRESA " + CRLF
	_cQuery += "	,SUBSTR(ZB2.ZB2_SITFAT,3,2)	FILIAL " + CRLF
	_cQuery += "	,ZB2_ANOREF " + CRLF
	_cQuery += "	,MAX(ZB2.ZB2_CONDPG) CONDPG " + CRLF
	_cQuery += "	,ZB2.ZB2_CALC CALC " + CRLF
	_cQuery += "	,ZB2.ZB2_TIPO TIPO "	 + CRLF
 	_cQuery += "	,ZB2.ZB2_USERGI USERGI "	 + CRLF
	_cQuery += "	,ZB2.ZB2_USERGA USERGA "	 + CRLF
	_cQuery += "	,ZB2.ZB2_OBSCAL OBSCAL "	 + CRLF	
	_cQuery += "	,ZB2.ZB2_DATCAL DATCAL "	 + CRLF	
	_cQuery += " FROM " + RetSqlName("ZB2") + " ZB2 " + CRLF
	_cQuery += CRLF 
	_cQuery += " WHERE ZB2.D_E_L_E_T_ = '' " + CRLF
	_cQuery += "	AND ZB2.ZB2_FILIAL = '' " + CRLF
//	_cQuery += "	AND ZB2.ZB2_FILIAL = '" + xFilial("ZB2") + "' " + CRLF	
	_cQuery += "	AND ZB2.ZB2_STATUS <> '3' " + CRLF
	_cQuery += "	AND ZB2.ZB2_STATUS <> '4' " + CRLF
	_cQuery += "	AND ZB2.ZB2_CALC <> '' " + CRLF
	_cQuery += CRLF
	_cQuery += " GROUP BY " + CRLF
	_cQuery += "	 ZB2.ZB2_CLIENT " + CRLF
	_cQuery += "	,ZB2.ZB2_LOJA " + CRLF
	_cQuery += "	,ZB2.ZB2_CONTRA " + CRLF
	_cQuery += "	,ZB2.ZB2_CODVER " + CRLF
	_cQuery += "	,ZB2.ZB2_PROPOS " + CRLF
	_cQuery += "	,ZB2.ZB2_SITFAT " + CRLF
	_cQuery += "	,ZB2_ANOREF " + CRLF
	_cQuery += "	,ZB2.ZB2_CALC " + CRLF
	_cQuery += "	,ZB2.ZB2_TIPO " + CRLF
	_cQuery += "	,ZB2.ZB2_USERGI " + CRLF  
	_cQuery += "	,ZB2.ZB2_USERGA " + CRLF  
	_cQuery += "	,ZB2.ZB2_OBSCAL " + CRLF  
	_cQuery += "	,ZB2.ZB2_DATCAL " + CRLF  		

Return U_TDIFUN01(_cQuery)

Static Function _fContrato(_cFilial,_cProposta)

	Local _cQuery := ""

	_cQuery := " SELECT MIN(SD6.D6_CODVER) " + CRLF
	_cQuery += " FROM " + RetSqlName("SD6") + " SD6 " + CRLF
	_cQuery += " WHERE SD6.D_E_L_E_T_ = '' " + CRLF
	_cQuery += "	AND SD6.D6_FILIAL = '" + _cFilial + "' " + CRLF
	_cQuery += "	AND SD6.D6_PROPOST = '" + _cProposta + "' " + CRLF

Return U_TDIFUN01(_cQuery)

Static Function _fPedido(_cFilial,_cProposta)

	Local _cQuery := ""

	_cQuery := " SELECT 	SC5.C5_NUM " + CRLF
	_cQuery += "			,CASE WHEN SC5.C5_LIBEROK <> '' OR SC5.C5_NOTA <> '' OR SC5.C5_BLQ <> '' THEN '5' " + CRLF
	_cQuery += "	 		ELSE '4' END LIBERADO " + CRLF
	_cQuery += " FROM ( SELECT " + CRLF
	_cQuery += "		 			SC6.C6_FILIAL FILIAL " + CRLF
	_cQuery += "					,MIN(SC6.C6_NUM) NUMERO " + CRLF
	_cQuery += "	 		FROM " + RetSqlName("SC6") + " SC6 " + CRLF
	_cQuery += CRLF	 
	_cQuery += "	 		WHERE 	SC6.D_E_L_E_T_ = '' " + CRLF
	_cQuery += "					AND SC6.C6_FILIAL = '" + _cFilial + "' " + CRLF
//	_cQuery += "					AND SC6.C6_PROPOST = '" + _cProposta + "' " + CRLF
	_cQuery += "					AND SC6.C6_XPROPOS = '" + _cProposta + "' " + CRLF
	_cQuery += CRLF		
	_cQuery += "	 		GROUP BY SC6.C6_FILIAL " + CRLF
	_cQuery += " ) PED " + CRLF
	_cQuery += CRLF
	_cQuery += " INNER JOIN " + RetSqlName("SC5") + " SC5 ON " + CRLF
	_cQuery += "				SC5.D_E_L_E_T_ = '' " + CRLF
	_cQuery += "				AND SC5.C5_FILIAL = PED.FILIAL " + CRLF 
	_cQuery += "				AND SC5.C5_NUM = PED.NUMERO " + CRLF
	
Return U_TDIFUN01(_cQuery)