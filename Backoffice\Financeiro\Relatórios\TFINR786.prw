#INCLUDE "Totvs.CH"
#INCLUDE "REPORT.CH"
#INCLUDE "TFINR786.CH"

//-------------------------------------------------------------------
/*/{Protheus.doc} TFINR786
Fun��o do relat�rio de rela��o de border�s de pagamento pendentes de aprova��o.
Projeto Migra��o P12

<AUTHOR>
@since 25/11/19
@version 12.1.25
/*/
//-------------------------------------------------------------------

User Function TFINR786()

Local oReport	:= Nil
Local lTReport	:= TRepInUse()
Local lSelFil	:= .F.
Local aSM0Fils	:= AdmAbreSM0()
Local lRet		:= .T.
Local cPerg		:= "TFINR786"
Local aSelFil	:= {}

If !lTReport
	Help("  ",1,"FINR786R4",,STR0001,1,0) //"Fun��o dispon�vel apenas para TReport, por favor atualizar ambiente e verificar parametro MV_TREPORT"
	Return
EndIf

lRet	:= Pergunte( cPerg , .T. )
lSelFil	:= (MV_PAR04 == 1)

If lRet
	If lSelFil .And. Len( aSelFil ) <= 0
		aSelFil := AdmGetFil()
		If Len( aSelFil ) <= 0
			Return
		EndIf
	EndIf

	oReport:= ReportDef(aSelFil,cPerg)
	oReport:PrintDialog()
EndIf

Return

//-------------------------------------------------------------------
/*/{Protheus.doc} ReportDef
Fun��o de defini��o do layout e formato do relat�rio

@param aSelFil	Array com as informa��es da filiais selecionadas para emiss�o do relat�rio
@return oReport	Objeto criado com o formato do relat�rio
<AUTHOR> Ara�jo Silva
@since 01/07/2015
@version 12.1.6
/*/
//-------------------------------------------------------------------

Static Function ReportDef(aSelFil,cPerg)

Local oReport		:= nil
Local oBordero		:= nil
Local cDesc			:= STR0002 //"Este relat�rio tem o objetivo de relacionar os border�s de pagamento em processo de aprova��o."
Local cTitulo		:= STR0003 // "Relat�rio de Aprova��o de Border�s de Pagamento"
Local cAlsBor 		:= GetNextAlias()
Local lSelFil		:= (MV_PAR04 == 1)

/*
 * Chamada do pergunte com os par�metros para definir o comportamento e filtros
 * do relat�rio
 */
Pergunte(cPerg,.F.)

/*
 * Defini��o padr�o do relat�rio TReport
 */
DEFINE REPORT oReport NAME "TFINR786" TITLE cTitulo PARAMETER cPerg ACTION {|oReport| PrintReport(oReport,cPerg,aSelFil,cAlsBor)} DESCRIPTION cDesc

/*
 * Se��o dos dados principais do relat�rio
 */
DEFINE SECTION oBordero OF oReport TITLE STR0003 TABLES cAlsBor, "PSB","PSO","PSP"  //"Rela��o de Border�s de Pagamento Pendentes de Aprova��o"
	TRCell():New( oBordero, "PSB_FILIAL"	, "PSB", /*X3Titulo*/, /*Picture*/, /*Tamanho*/,/*lPixel*/,{|| (cAlsBor)->PSB_FILIAL }		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSB_BORDER"	, "PSB", /*X3Titulo*/, /*Picture*/, /*Tamanho*/10,/*lPixel*/,{|| (cAlsBor)->PSB_BORDER }		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSB_VERSAO"	, "PSB", /*X3Titulo*/, /*Picture*/, /*Tamanho*/5,/*lPixel*/,{|| (cAlsBor)->PSB_VERSAO }		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSB_TOTAL"	, "PSB", /*X3Titulo*/, /*Picture*/, /*Tamanho*/10,/*lPixel*/.T.,{|| (cAlsBor)->PSB_TOTAL }		,/*nALign*/ "RIGHT"	,/*lLineBreak*/,/*cHeaderAlign*/"RIGHT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSB_TIPOPG"	, "PSB", /*X3Titulo*/, /*Picture*/, /*Tamanho*/40,/*lPixel*/,{|| (cAlsBor)->PSB_TIPOPG + " - " + AllTrim((cAlsBor)->(FieldGet(FieldPos("X5_DESCRI")))) }		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSB_DATA"		, "PSB", STR0006		, /*Picture*/, /*Tamanho*/10,/*lPixel*/,{|| (cAlsBor)->PSB_DATA }		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSP_DATA"		, "PSP", STR0007		, /*Picture*/, /*Tamanho*/10,/*lPixel*/,{|| (cAlsBor)->PSP_DATA }		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSP_HORA"		, "PSP", /*X3Titulo*/, /*Picture*/, /*Tamanho*/10,/*lPixel*/,{|| (cAlsBor)->PSP_HORA }		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSO_CODAPR"	, "PSO", /*X3Titulo*/, /*Picture*/, /*Tamanho*/40,/*lPixel*/,{|| (cAlsBor)->PSO_CODAPR + " - " + AllTrim(UsrFullName((cAlsBor)->PSO_CODAPR))}		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSO_PAPEL"	, "PSO", /*X3Titulo*/, /*Picture*/, /*Tamanho*/40,/*lPixel*/,{|| (cAlsBor)->PSO_PAPEL + " - " + AllTrim((cAlsBor)->FRW_DESCR) }		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSP_MOVIME"	, "PSP", /*X3Titulo*/, /*Picture*/, /*Tamanho*/5,/*lPixel*/.T.,{|| AFR786St((cAlsBor)->PSP_MOVIME) }		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSP_HISTOR"	, "PSP", /*X3Titulo*/, /*Picture*/, /*Tamanho*/40,/*lPixel*/.T.,{|| AFR786GMM((cAlsBor)->PSP_RECNO) }		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
	TRCell():New( oBordero, "PSB_PROAPR"	, "PSB", /*X3Titulo*/, /*Picture*/, /*Tamanho*/30,/*lPixel*/,{|| (cAlsBor)->PSB_PROAPR }		,/*nALign*/ "LEFT"	,/*lLineBreak*/,/*cHeaderAlign*/"LEFT"	,/*lCellBreak*/,/*nColSpace*/,/*lAutoSize*/.F. )
oBordero:SetAutoSize()

oReport:SetLandScape()
oReport:DisableOrientation()

Return oReport

//-------------------------------------------------------------------
/*/{Protheus.doc} PrintReport
Fun��o para busca das informa��es que ser�o impressas no relat�rio

@param oReport	Objeto para manipula��o das se��es, atributos e dados do relat�rio.
@param cPerg	Identifica��o do Grupo de Perguntas do relat�rio
@param aSelFil	Array com as informa��es de todas as filiais do sistema.
@return void
<AUTHOR> Ara�jo Silva
@since 01/07/2015
@version 12.1.6
/*/
//-------------------------------------------------------------------

Static Function PrintReport(oReport,cPerg,aSelFil,cAlsBor)

Local oBordero		:= oReport:Section(1)
Local cConsTodos	:= ""
Local cBordIni		:= ""
Local cBordFim		:= ""
Local lSelFil		:= .F.
Local cTmpFil		:= ""
Local cCondFil		:= ""
Local cCondSta		:= "% 1 = 1 %"

/*
 * Chamada do pergunte com os par�metros para definir o comportamento e filtros
 * do relat�rio
 */
Pergunte( cPerg , .F. )

cBordIni	:= MV_PAR01
cBordFim	:= MV_PAR02
cConsTodos	:= MV_PAR03
lSelFil		:= (MV_PAR04 == 1)

/*
 * Tratamento do Filtro de Filiais de acordo com Pergunte "Seleciona Filial ?"
 */
If lSelFil
	cCondFil 	:= "% PSB.PSB_FILIAL " + GetRngFil( aSelfil , "PSB", .T., @cTmpFil ) + " %"
Else
	cCondFil 	:= "% PSB.PSB_FILIAL = '" + FWXFILIAL("PSB") + "' %"
EndIf

If cConsTodos == 2
	cCondSta := "% PSP.PSP_MOVIME = '1' %"
ElseIf cConsTodos == 3
	cCondSta := "% PSP.PSP_MOVIME = '2' %"
EndIf 	

/*
 * Se��o de Border�s (Border�)
 */
BEGIN REPORT QUERY oBordero

BeginSql alias cAlsBor

SELECT
	PSB.PSB_BORDER
	,PSB.PSB_PROAPR
	,PSB.PSB_TOTAL
	,PSB.PSB_STATUS
	,PSB.PSB_VERSAO
	,PSO.PSO_CODIGO
	,PSO.PSO_ITEM
	,FRW.FRW_DESCR
	,PSO.PSO_PAPEL
	,PSO.PSO_CODAPR
	,PSB.PSB_TIPOPG
	,PSB.PSB_DATA
	,PSP.PSP_DATA
	,PSP.PSP_HORA
	,PSP.PSP_MOVIME
	,PSB.PSB_FILIAL
	,SX5.X5_DESCRI
	,PSP.R_E_C_N_O_ PSP_RECNO
FROM
	%table:PSB% PSB
INNER JOIN %table:PRO% PRO ON
	PRO.PRO_FILIAL = %xfilial:PRO% AND
	PRO.PRO_CODIGO = PSB.PSB_PROAPR 
INNER JOIN %table:PSO% PSO ON
	PSO.PSO_FILIAL = %xfilial:PSO% AND
	PSO.PSO_CODIGO = PRO.PRO_CODIGO
INNER JOIN %table:FRW% FRW ON
	FRW.FRW_FILIAL = %xfilial:FRW% AND
	PSO.PSO_PAPEL  = FRW.FRW_CODIGO
INNER JOIN %table:PSP% PSP ON
	PSP.PSP_FILIAL = %xfilial:PSP% AND
	PSP.PSP_PROAPR = PSB.PSB_PROAPR AND
	PSP.PSP_BORDER = PSB.PSB_BORDER AND
	PSP.PSP_VERSAO = PSB.PSB_VERSAO
INNER JOIN %table:SX5% SX5 ON
	SX5.X5_FILIAL = %xfilial:SX5% AND
	SX5.X5_TABELA = '59' AND
	SX5.X5_CHAVE = PSB.PSB_TIPOPG
WHERE
	%exp:cCondSta%	AND
	%exp:cCondFil%	AND
	PSB.PSB_RECPAG 	= 'P' AND
	PSB.PSB_SITBRD = '1' AND
	PSB.PSB_BORDER BETWEEN %exp:cBordIni% AND %exp:cBordFim%
ORDER BY
	PSB.PSB_FILIAL, PSB.PSB_BORDER

EndSql

END REPORT QUERY oBordero

//PSB_FILIAL+PSB_BORDER+PSB_RECPAG+PSB_VERSAO
TRPosition():New(oBordero,"PSB", 1,{ || xFilial("PSB")+(cAlsBor)->(PSB_BORDER+"P"+PSB_VERSAO ) }, .T.)

//PSP_FILIAL+PSP_BORDER+PSP_VERSAO+PSP_PROAPR
TRPosition():New(oBordero,"PSP", 2,{ || xFilial("PSP")+(cAlsBor)->(PSB_BORDER+PSB_VERSAO+PSB_PROAPR) }, .T.)

//PSO_FILIAL+PSO_CODIGO+PSO_ITEM
TRPosition():New(oBordero,"PSO", 1,{ || xFilial("PSO")+(cAlsBor)->(PSO_CODIGO+PSO_ITEM) }, .T.)


CtbTmpErase(cTmpFil)
oBordero:Print()

Return

//-------------------------------------------------------------------
/*/{Protheus.doc} AFR786St

Fun��o que retorna a descri��o do Status do movimento de aprova��o grid de Border� de Pagamentos

<AUTHOR> Ara�jo Silva
@since 01/07/2015
@version 12.1.6
@param cStatus Situa��o do Border� de Pagamento
@return cDesc Descri��o Status do Movimento de Aprova��o
/*/
//-------------------------------------------------------------------

Static Function AFR786St(cStatus)
Local cDesc		:= ""

DEFAULT cStatus := ""

If cStatus == "1" 
	cDesc := STR0004 //"Aprovado" 
ElseIf cStatus == "2"
	cDesc := STR0005 //"Reprovado"
EndIf
	
Return cDesc


//-------------------------------------------------------------------
/*/{Protheus.doc} AFR786GMM

Fun��o que retorna o conte�do do campo de hist�rico do movimento de aprova��o grid de Border� de Pagamentos

<AUTHOR> Ara�jo Silva
@since 02/07/2015
@version 12.1.6
@param nRec Recno do registro do movimento de aprova��o
@return cDesc Conte�do do campo de hist�rico do movimento de aprova��o
/*/
//-------------------------------------------------------------------

Static Function AFR786GMM(nRec)
Local cDesc		:= ""
Local aPSPArea	:= PSP->(GetArea())

DEFAULT nRec := 0

PSP->(DbGoTo(nRec))
cDesc := PSP->PSP_HISTOR

RestArea(aPSPArea)	
Return cDesc		