#INCLUDE "TOTVS.CH"
#INCLUDE "FWBROWSE.CH"
#INCLUDE "FWMVCDEF.CH"

/*{Protheus.doc} TGCVA117
Cadastro da Motivos de Bonificacao e Cancelamento do Churn

<AUTHOR> Carneiro
@since 15/12/2020
@version 0.1
*/

User Function TGCVA117()

Local oBrowse
oBrowse := FWMBrowse():New()
oBrowse:SetAlias('PU4')
oBrowse:SetDescription('Cadastro de Motivos do Contrato')
oBrowse:DisableDetails()
oBrowse:Activate()

Return NIL


Static Function MenuDef()
Local aRotina := {}

ADD OPTION aRotina TITLE 'Pesquisar'  ACTION 'PesqBrw'          OPERATION 1 ACCESS 0
ADD OPTION aRotina TITLE 'Visualizar' ACTION 'VIEWDEF.TGCVA117' OPERATION 2 ACCESS 0
ADD OPTION aRotina TITLE 'Incluir'    ACTION 'VIEWDEF.TGCVA117' OPERATION 3 ACCESS 0
ADD OPTION aRotina TITLE 'Alterar'    ACTION 'VIEWDEF.TGCVA117' OPERATION 4 ACCESS 0
ADD OPTION aRotina TITLE 'Excluir'    ACTION 'VIEWDEF.TGCVA117' OPERATION 5 ACCESS 0
ADD OPTION aRotina TITLE 'Imprimir'   ACTION 'VIEWDEF.TGCVA117' OPERATION 8 ACCESS 0

Return aRotina

Static Function ModelDef()

Local oStruPU4 := FWFormStruct( 1, 'PU4' )
Local oModel 
Local aTrigger1		:= FwStruTrigger("PU4_MOTBC","PU4_DESCMT","SX5->(FieldGet(FieldPos('X5_DESCRI')))",.f.,Nil,Nil,Nil)

oStruPU4:AddTrigger(aTrigger1[1],aTrigger1[2],aTrigger1[3],aTrigger1[4])

oModel := MPFormModel():New('GVA117MVC' )
oModel:AddFields( 'PU4MASTER', /*cOwner*/, oStruPU4)
oModel:SetDescription( 'Cadastro de Motivos do Contrato' )
oModel:GetModel( 'PU4MASTER' ):SetDescription( 'Cadastro de Motivos do Contrato' )
oModel:SetPrimaryKey( {} )

Return oModel

Static Function ViewDef()
Local oModel := FWLoadModel( 'TGCVA117' )
Local oStruPU4 := FWFormStruct( 2, 'PU4' )
Local oView

oView := FWFormView():New()
oView:SetModel( oModel )
oView:AddField( 'VIEW_PU4', oStruPU4, 'PU4MASTER' )
oView:CreateHorizontalBox( 'TELA' , 100 )
oView:SetOwnerView( 'VIEW_PU4', 'TELA' )
Return oView

User Function GVA117PU4()

	Local oModel	:= FWModelActive()
	Local oObjPU4	:= oModel:GetModel('PU4MASTER')
	Local cTipo		:= oObjPU4:GetValue('PU4_TIPO')
	Local cAliasXB	:= ""
	Local lRet 		:= .F.
	Local cPrefTip	:= ""

	if cTipo == 'B' // BOnificacao
		cAliasXB	:= "_B"
	ElseIf cTipo == 'C' // Carencia
		cAliasXB	:= "_I"
	ElseIf cTipo == 'X' // Cancelamento	
		cAliasXB	:= "_C"
	EndIf 

	SXB->(DbSetOrder(1))

	If SXB->( DbSeek("TI"+cAliasXB) ) //Verifica se existe o cadastro na SXB para a consulta
		cPrefTip := "TI"
	EndIf

	If Conpad1(,,, cPrefTip+cAliasXB, "PU4_MOTBC" )
		lRet := .T.
        cChave :=  SX5->(FieldGet(FieldPos("X5_CHAVE")))
		&(Readvar()) := AllTrim(cChave)
	EndIf

Return lRet