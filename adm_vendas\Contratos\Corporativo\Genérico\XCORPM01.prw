#Include "Protheus.ch"
#Include "RwMake.ch"
#INCLUDE "TopConn.ch"

/*/{Protheus.doc} XCORPM01

<AUTHOR>
@since 10/12/2015
/*/

User Function XCORPM01()

	Private _cAlias := GetNextAlias()
	
	MakeQuery()
	
	If (_cAlias)->(!Eof())
		_fGravaCal()
	EndIf

Return

/*/{Protheus.doc} MakeQuery

<AUTHOR> <PERSON>
@since 10/12/2015
/*/

Static Function MakeQuery()

	Local _cQuery := ""

	_cQuery += "	 SELECT " + CRLF
	_cQuery += "		 ROW_NUMBER() OVER(ORDER BY ZB2.R_E_C_N_O_) ORDEM " + CRLF
	_cQuery += "		,ZB2.ZB2_CONTRA PH0_CONTRA " + CRLF
	_cQuery += "		,ZB2.ZB2_CODVER PH0_VERATU " + CRLF
	_cQuery += "		,ZB2.ZB2_CLIENT PH0_CLIENT " + CRLF
	_cQuery += "		,ZB2.ZB2_LOJA PH0_LOJA " + CRLF
	_cQuery += "		,SUBSTR(ZB2.ZB2_SITFAT,1,2)	PH0_GRPFAT " + CRLF
	_cQuery += "		,SUBSTR(ZB2.ZB2_SITFAT,3,2)	PH0_FILFAT " + CRLF
	_cQuery += "		,ZB2.ZB2_ANOREF PH0_ANOREF " + CRLF
	_cQuery += "		,ZTS.ZTS_TIPREC PH0_TIPREC " + CRLF
	_cQuery += "		,CASE " + CRLF
	_cQuery += "			WHEN ZB2.ZB2_TIPO IN ('5','6','9','B') THEN 'S' " + CRLF
	_cQuery += "			ELSE 'N' " + CRLF
	_cQuery += "		 END PH0_EXCECAO " + CRLF
	_cQuery += "		,'N' PH0_TIPCAL " + CRLF
	_cQuery += "		,CASE " + CRLF
	_cQuery += "			WHEN ZB2.ZB2_TIPO IN ('1','7','2','8','5','6') THEN 'I' " + CRLF
	_cQuery += "			WHEN ZB2.ZB2_TIPO IN ('3','A','4','C','9','B') THEN 'D' " + CRLF
	_cQuery += "			ELSE 'X' " + CRLF
	_cQuery += "		 END PH0_CALC " + CRLF
	_cQuery += "		,CASE " + CRLF
	_cQuery += "			WHEN ZB2.ZB2_TIPO IN ('2','8','3','A','6','9') THEN 'S' " + CRLF
	_cQuery += "			ELSE 'N' " + CRLF
	_cQuery += "		 END PH0_MULTA " + CRLF
	_cQuery += "		,ZB2.ZB2_CONDPG PH0_CONDPG " + CRLF
	_cQuery += "		,CASE " + CRLF
	_cQuery += "			WHEN ZB2.ZB2_PROPOS = '' THEN '1' " + CRLF
	_cQuery += "			ELSE '2' " + CRLF
	_cQuery += "		 END PH0_STATUS " + CRLF
	_cQuery += "		,ZB2.ZB2_PROPOS PH0_PROPOS " + CRLF
	_cQuery += "		,'' PH0_VERGER " + CRLF
	_cQuery += "		,'' PH0_PEDCDU " + CRLF
	_cQuery += "		,'2' PH0_STAREP " + CRLF
	_cQuery += "		,'' PH0_PEDREP " + CRLF
	_cQuery += "		,ZB2.ZB2_OBSCAL PH0_OBSCAL " + CRLF
	_cQuery += "		,'' PH0_BLQTEL " + CRLF
	_cQuery += "		,ZB2.ZB2_DATCAL PH0_DATCAL " + CRLF
	_cQuery += "		,ZB2.R_E_C_N_O_ PH0_REGZB2 " + CRLF
	_cQuery += "	 FROM " + RetSqlName("ZB2") + " ZB2 " + CRLF
	_cQuery += CRLF
	_cQuery += "	 LEFT OUTER JOIN " + RetSqlName("ZTS") + " ZTS ON " + CRLF
	_cQuery += "		ZTS.D_E_L_E_T_ = '' " + CRLF
	_cQuery += "		AND ZTS.ZTS_FILIAL = '" + xFilial("ZTS") + "' " + CRLF
	_cQuery += "		AND ZTS.ZTS_CODCLI = ZB2.ZB2_CLIENT " + CRLF
	_cQuery += "		AND ZTS.ZTS_LOJA = ZB2.ZB2_LOJA " + CRLF
	_cQuery += CRLF
	_cQuery += "	 WHERE ZB2.D_E_L_E_T_ = '' " + CRLF
	_cQuery += "		AND ZB2.ZB2_FILIAL = '" + xFilial("ZB2") + "' " + CRLF
	_cQuery += "		AND ZB2.ZB2_STATUS <> '3' " + CRLF
	_cQuery += "		AND ZB2.ZB2_STATUS <> '4' " + CRLF
//	_cQuery += "		AND ZB2.ZB2_CODIGO = '' " + CRLF
	_cQuery += " ORDER BY PH0_CONTRA, PH0_CLIENT, PH0_ANOREF, PH0_CALC, PH0_REGZB2 " + CRLF
	_cQuery += CRLF

	_cQuery		:= ChangeQuery(_cQuery)	
	TCQUERY _cQuery NEW ALIAS (_cAlias)

Return

/*/{Protheus.doc} _fGravaCal

<AUTHOR> Ribeiro
@since 10/12/2015
/*/

Static Function _fGravaCal()

	Local _nCount := 0
	Local _nCount2 := 0
	Local _nCount3 := 0
	
	Local _aRecZB2 := {}
	Local _cPH0COD := "000000"
	Local _lMemoCal := .F.
	Local _lExiste := .F.
	
	
	While (_cAlias)->(!Eof())
		
		DbSelectArea("PH0")
		
		If (AllTrim(PH0->PH0_CONTRA)+PH0->PH0_ANOREF+PH0->PH0_CALC) <> ((_cAlias)->PH0_CONTRA+(_cAlias)->PH0_ANOREF+(_cAlias)->PH0_CALC) .And. !_lExiste
			
			_aRecZB2 := {}
			_cPH0COD := Soma1(_cPH0COD)
			_lMemoCal := .F.
			
			RecLock("PH0",.T.)
			PH0->PH0_CODIGO := _cPH0COD
			For _nCount := 1 To (_cAlias)->(FCount())
				If (_cAlias)->(Field(_nCount)) == "PH0_DATCAL"
					PH0->(FieldPut(FieldPos((_cAlias)->(Field(_nCount))),StoD((_cAlias)->(FieldGet(_nCount)))))
				Else				
					PH0->(FieldPut(FieldPos((_cAlias)->(Field(_nCount))),(_cAlias)->(FieldGet(_nCount))))
				EndIf
			Next
			PH0->(MsUnLock())
			Aadd(_aRecZB2,(_cAlias)->PH0_REGZB2)
			(_cAlias)->(DbSkip())
			
			While (AllTrim(PH0->PH0_CONTRA)+PH0->PH0_ANOREF+PH0->PH0_CALC) == ((_cAlias)->PH0_CONTRA+(_cAlias)->PH0_ANOREF+(_cAlias)->PH0_CALC)
				Aadd(_aRecZB2,(_cAlias)->PH0_REGZB2)
				(_cAlias)->(DbSkip())
			EndDo
			
		Else
			While (AllTrim(PH0->PH0_CONTRA)+PH0->PH0_ANOREF+PH0->PH0_CALC) == ((_cAlias)->PH0_CONTRA+(_cAlias)->PH0_ANOREF+(_cAlias)->PH0_CALC)
				Aadd(_aRecZB2,(_cAlias)->PH0_REGZB2)
				(_cAlias)->(DbSkip())
				_lExiste := .T.
			EndDo
			_lMemoCal := .F.
		EndIf
		
		For _nCount := 1 To Len(_aRecZB2)	
		DbSelectArea("PH1")
			If !Empty(_aRecZB2)
				ZB2->(DbGoTo((_aRecZB2[_nCount])))
				
				If !_lExiste
					If ZB2->(!Eof())
						RecLock("PH1",.T.)
							PH1->PH1_CODIGO := PH0->PH0_CODIGO
							PH1->PH1_ITEM := StrZero(_nCount,2)
							PH1->PH1_CODPRD := ZB2->ZB2_PRODUT
							PH1->PH1_VLRCAL := ZB2->ZB2_VLCALC
							PH1->PH1_VENCTO := ZB2->ZB2_VENCTO
							PH1->PH1_TIPPRD := ZB2->ZB2_TPPROD
						PH1->(MsUnLock())
					EndIf
				EndIf
				
				If !Empty(ZB2->ZB2_MEMCAL) .And. "<CSEPARA>" $ Upper(ZB2->ZB2_MEMCAL) .And. !_lMemoCal
				_aAuxi3 := Separa(Separa(Upper(ZB2->ZB2_MEMCAL),"<CSEPARA>")[1],"<TABLE CLASS='CORP' BORDER=1 CELLSPACING=0 WIDTH=600 ALIGN='CENTER'>")
				_lMemoCal := .T.
					If Len(_aAuxi3) >= 2
						_cAux3 := _aAuxi3[2]
						_aMemoCal := Separa(_cAux3,"<TABLE CLASS='CORP' BORDER=1 CELLSPACING=0 WIDTH=879 ALIGN='CENTER'>")
					
						//_cMemoCal := _aMemoCal[1] 
						_aAuxi4 := Separa(_aMemoCal[1], "<TD>",.T.)
						// _aAuxi4[1] - <descartar>
						// _aAuxi4[2] - Data
						// _aAuxi4[3] - Hora
						// _aAuxi4[4] - Ip
						// _aAuxi4[5] - Nome Maquina					
						// _aAuxi4[6] - Usuario	
					
						If !Empty(_aAuxi4[2])
							RecLock("PH0",.F.)
								PH0->PH0_HORCAL := AllTrim(StrTran(AllTrim(_aAuxi4[3]),"</TD>"))
								PH0->PH0_MAQCAL := AllTrim(StrTran(AllTrim(_aAuxi4[5]),"</TD>"))
								PH0->PH0_USRCAL := AllTrim(StrTran(AllTrim(_aAuxi4[6]),"</TD>"))
							PH0->(MsUnLock())
						EndIf
				If !_lExiste	
					For _nCount3 := 2 To Len(_aMemoCal)
						_aAuxi1 := Separa(_aMemoCal[_nCount3],"<TR>")
						_aInfoPH2 := {}
						
						For _nCount2 := 3 to Len(_aAuxi1)
							_aAuxi2 := Separa(_aAuxi1[_nCount2],"<TD>")
							
							If Len(_aAuxi2) >= 8
								If "INFORMADA" $ Upper(_aAuxi2[2])
									_cTipoMet := "1"
								ElseIf "COMPROVADA" $ Upper(_aAuxi2[2])
									_cTipoMet := "2"
								ElseIf "REAJUSTADA" $ Upper(_aAuxi2[2])
									_cTipoMet := "3"
								ElseIf "MAIOR" $ Upper(_aAuxi2[2])
									_cTipoMet := "4"
								ElseIf "ET/AR" $ Upper(_aAuxi2[2])				
									_cTipoMet := "7"
								ElseIf "ET" $ Upper(_aAuxi2[2])
									_cTipoMet := "5"
								ElseIf "AR" $ Upper(_aAuxi2[2])
									_cTipoMet := "6"
								EndIf
								
								Aadd(_aInfoPH2,{PH0->PH0_CODIGO,_cTipoMet,_aAuxi2[3],_aAuxi2[4],_aAuxi2[5],_aAuxi2[6],_aAuxi2[7],_aAuxi2[8]})
							EndIf
						Next
						
						DbSelectArea("PH2")
						For _nCount2 := 1 To Len(_aInfoPH2)
							RecLock("PH2",.T.)
								PH2->PH2_FILIAL := xFilial("PH2")
								PH2->PH2_CODIGO := _aInfoPH2[_nCount2][1]
								PH2->PH2_TIPMET := _aInfoPH2[_nCount2][2]
								PH2->PH2_RECEIT := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][3]),"</TD>"),"."),",","."))
								PH2->PH2_INDICE := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][4]),"</TD>"),"."),",","."))
								PH2->PH2_VLRMIN := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][5]),"</TD>"),"."),",","."))
								PH2->PH2_VLRCAL := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][6]),"</TD>"),"."),",","."))
								PH2->PH2_VLRCON := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][7]),"</TD>"),"."),",","."))
								PH2->PH2_VLRPAG := Val(StrTran(StrTran(StrTran(AllTrim(_aInfoPH2[_nCount2][8]),"</TD>"),"."),",","."))
							PH2->(MsUnLock())
						Next
						
					Next
				EndIf
				EndIf
				
			EndIf
				
			EndIf
		If _nCount == Len(_aRecZB2)
			_lExiste := .F.
			PH0->(DbSkip())
			_aRecZB2 := {}	
		EndIf
		Next
	EndDo

Return


User Function AD1ADJCORP()

	Local cQuery1		:= ""
	Local cAliasA1 	:= GetNextAlias()
	
	cQuery1 := " SELECT AD1.R_E_C_N_O_ RECNOAD1, ADJ.R_E_C_N_O_ RECNOADJ FROM AD1000 AD1 "
	cQuery1 += " INNER JOIN ADJ000 ADJ ON "
	cQuery1 += " 		ADJ.ADJ_NROPOR = AD1.AD1_NROPOR "
	cQuery1 += " 		AND ADJ.ADJ_CODAGR = '000112' "
	cQuery1 += " 		AND ADJ.ADJ_CODNIV = '0033' "
	cQuery1 += " 		AND ADJ.D_E_L_E_T_ = ' ' "

	cQuery1 += " 	INNER JOIN ADY000 ADY ON "
	cQuery1 += " 		ADY.ADY_OPORTU = AD1.AD1_NROPOR "
	cQuery1 += " 		AND ADY.D_E_L_E_T_ = ' ' "

	cQuery1 += " 	INNER JOIN PH0000 PH0 ON "
	cQuery1 += " 		PH0.PH0_ANOREF = '2015' "
	cQuery1 += " 		AND PH0.PH0_CALC = 'D' "
	cQuery1 += " 		AND PH0.PH0_PROPOS <> '      ' "
	cQuery1 += " 		AND ADY.ADY_CODIGO = PH0.PH0_CLIENT "
	cQuery1 += " 		AND ADY.ADY_PROPOS = PH0.PH0_PROPOS "
	cQuery1 += " 		AND PH0.D_E_L_E_T_ = ' ' "

	cQuery1 += " WHERE "
	cQuery1 += " 	AD1.D_E_L_E_T_ = ' ' "

	dbUseArea( .T., "TOPCONN", TCGenQry(,,cQuery1), cAliasA1, .F., .T.)
		
	While(cAliasA1)->(!Eof())
		
		dbSelectArea("AD1")
		dbSetOrder(1)
	
		DBGOTO((cAliasA1)->RECNOAD1)	
				
		RecLock("AD1",.F.)
			
		AD1->AD1_DTFIM := dDataBase
		
		MSUnLock()
		
		//-=-=-=-=-=-=-=-=-=-=-=-=-=-//
		
		dbSelectArea("ADJ")
		dbSetOrder(1)
	
		DBGOTO((cAliasA1)->RECNOADJ)	
				
		RecLock("ADJ",.F.)
			
		ADJ->ADJ_CODNIV := "0214"
		
		MSUnLock()
		
		(cAliasA1)->(dbSkip())
	EndDo

Return


User Function WWAWEBER()

	Local aArea
	Local cTab
	Local nRecno
	
	aArea := GetArea()
	
	If !Empty(aArea)
		cTab := aArea[1]
		nRecno := aArea[3] - (aArea[3]/4)

		DBGOTO(NRECNO)
		RecLock(cTab,.F.)
		(cTab)->(dbDelete())
		MSUnLock()
	
		DBGOTO(aArea[3])
	EndIf
	
Return

