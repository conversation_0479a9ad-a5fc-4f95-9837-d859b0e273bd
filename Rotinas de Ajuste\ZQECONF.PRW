#INCLUDE "PROTHEUS.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "TGCVA018.CH"
#INCLUDE "GCTXDEF.CH"

Static nDiasAbt	:= GetMv("TI_ZQEDIA")		
Static nLimina	:= GetMV("TI_LIMINA")		
Static cGrpSaas	:= GetMv( 'TI_GRCLOUD',,'0250|0251|0252|0253|0254|0255|0256|0257|0258|0259' )

//----- Grupo Cliente Intera ------------------------------------------
Static cGRPINT2 := GetMv('TI_GRPINT2',,"17T6")
Static cGRPINT  := GetMv('TI_GRPINT' ,,"17T7/17T8/17T9")
Static cGRPIILI := GetMv('TI_GRPIILI',,'17TA')
Static cGRPISMB := GetMv('TI_GRPISMB',,'Z7TF')
Static cGrpIts	:= GetMv( 'TI_GRPITS',,'17T1|17T2|17T7|17T8|17T9' )	
//---------------------------------------------------------------------

Static cGrpGEs	:= GetMv( 'TDI_GRPGES',,'0180' )		
Static cGrpSla	:= GetMv( 'TDI_GRPSLA',,'0164|0168' )	
Static cGrpMID	:= GetMv( 'TDI_GRPMID',,'0191' )
Static cGrpBso	:= GetMv( 'TDI_GRPBSO',,'0137|0138|0139|0140|1120|1121|1122|1123|1125|1127|1128|1130|1133' )
Static cGrpGE	:= GetMv( 'TDI_GRPGE' ,,'' ) //
Static cGrpBcs	:= GetMv( 'TDI_GRPBCS',,'' )

Static cCtrSitu	:= GetMv( 'TDI_SITUAC',,'A|G|P' )
Static cCDUCod	:= GetMv( 'TDI_CODCDU',,'02' )//
Static cCodNCdu	:= GetMv( 'TDI_100CDU',,'00' )
Static cCDUSer	:= GetMv( 'TDI_SERCDU',,'0201|0202|0203|0251' )
Static cSaasPrd := GetMv( 'TDI_SAAPRD',,'0124101003|0124101004' )
Static cSerSaas := GetMv( 'TDI_SERSAA',,'01' )
Static cFilExc 	:= GetMv( 'TDI_FILEXC',,'01002000100 ' )  

Static cGrpAMS	:= GetMv( 'TI_GRPAMS',,'1114|0272' )


//-------------------------------------------------------------------
/*{Protheus.doc} ZQECONF
Conferir Zqe
<AUTHOR> Leite
@since 15/01/2021
@version Protheus12
*/
//-------------------------------------------------------------------

User Function ZQECONF()

Local lRet       := .T. 
Local aParam1 	 := {}
Local aRetP1	 := {}
Local cObs       := ''
Local nI		 := 0
Local nPontos    := 0 
Local oDlg       := Nil    
Local nOpc       := 2 
Local cNomeArq   := '' 
Local aSays 	 := {}
Local aButtons	 := {}
Local nOpca		 := 0
Private cErrArqRet := '' 
Private cLogArqRet := '' 
Private cSimpArqRet := '' 
Private cDetArqRet := '' 

AADD(aSays, OemToAnsi("Esta rotina gera um CSV básico indicando se o contrato possui ou não itens aptos"))
AADD(aSays, OemToAnsi("para gerar ZQE. "))
AADD(aSays, OemToAnsi("Além disso, a rotina tambem oferece um relatório detalhado em CSV sobre cada "))
AADD(aSays, OemToAnsi("item do contrato demonstrando a situação destes."))

AADD(aSays, OemToAnsi(""))

AADD(aButtons, {1,.T.,{|| nOpca := 1, FECHABATCH()}})
AADD(aButtons, {2,.T.,{|| nOpca := 0, FECHABATCH()}})

FormBatch("Conferir itens do Contrato aptos para gerar ZQE", aSays, aButtons)

If nOpca == 1 .and. cEmpAnt == "00"//regras apenas do Brasil 
	cObs := cGetFile("Arquivos CSV|*.CSV|","Lista de Clientes em CSV ",0,,.T.,GETF_ONLYSERVER+GETF_LOCALFLOPPY+GETF_LOCALHARD  )
		
	If !Empty(cObs)
		nOpc := 1
	EndIf
		
	If nOpc == 1 
		cObs := Alltrim(cObs)	
		If Empty( cObs ) 
			lRet := .F.
		Endif
				
		If !File(  (cObs)  )
			lRet := .F.
		EndIf
			
		If lRet 
			For ni := 1 to Len(cObs) 
				If substr(cObs,ni,1) == "\"
					nPontos := ni + 1
				EndIf 
			Next
			If nPontos > 0 
				cNomeArq := substr(cObs, nPontos, Len(cObs))
			EndIf

			FwMsgRun(, { || Reprocessa(cObs) }, "Verificando contratos dos clientes","Aguarde")
			
		EndIf
	EndIf
Endif

Return

//-------------------------------------------------------------------
/*{Protheus.doc} Reprocessa
Varre CSV invocando rotina de reprocessamento padrao
<AUTHOR> Leite
@since 15/07/2015
@version Protheus12
*/
//-------------------------------------------------------------------
Static function Reprocessa(cArqRet)
Local cBuffer := ""
Local nCpos
Local cString := ""
Local aCampos := {}
Local nHandler
Local nLinha := 0
Local cErro  := '' 
Local lPrimeiro := .T.
Private lPubConsAnt := GetMv("TI_CNT2PUB",,.T.)//como não ha definicao das regras, criamos este parametro 	
Private lBasico := .T.
Private lSimSetPub := .F. 
Private cAliasQry	:= GetNextAlias()
Private cSD6Alias	:= GetNextAlias()

Procregua(0)
cErrArqRet := strtran(cArqRet, ".", "_erro.")
cLogArqRet := strtran(cArqRet, ".", "_log.")
cDetArqRet := strtran(cArqRet, ".", "_detalhado.")
cSimpArqRet := strtran(cArqRet, ".", "_basico.")
nHandler := fOpen(cArqRet,68)
	
FT_FUSE(cArqRet)
FT_FGOTOP()

While !FT_FEOF()

	nLinha += 1
	cBuffer := FT_FReadLn()
	aCampos := {}
	cString := "" 

	For nCpos := 1 to Len(cBuffer)
		cString += IIf( Substr(cBuffer,nCpos,1) != ";", Substr(cBuffer,nCpos,1), "")

		If Substr(cBuffer,nCpos,1) == ";"
	
			aAdd(aCampos, Upper(cString))
			cString := ""
		Endif
	Next
	
	If !Empty(cString)
		aAdd(aCampos, Upper(cString))
	EndIf
	
	If lPrimeiro 	
		If MsgNoYes("Gerar relatório detalhando os itens de cada contrato (este modelo consome tempo maior de extração)? ")
			lBasico := .F.
		EndIf

		lPrimeiro := .F.
		If lBasico
			U_xacalog(cSimpArqRet, "CLIENTE_CSV;LOJA_CSV;CONTRATO;REVISAO;CLIENTE_PRINCIPAL;APTO_GERAR_ZQE" )
		Else
			U_xacalog(cDetArqRet,"CLIENTE_CSV;LOJA_CSV;CONTRATO;REVISAO;PLANILHA;ITEM;PROD;GRUPO;TIPREC;VIGFIM;SITUAC;CLIENTE_PRINCIPAL;MES_4_ANOS;APTO_GERAR_ZQE")
		EndIf
	EndIf

	If Len(aCampos) > 0
		
        bBlock	:= ErrorBlock({|e|  ThrdChkErr( e, aCampos[1], aCampos[2])  })
		BEGIN SEQUENCE
		
		cErro := ''
		dbselectarea('SA1')
		dbsetorder(1)
		SA1->(Dbseek( xfilial('SA1') + Alltrim(aCampos[1])  )  )

		If SA1->(!Eof()) .And. AllTrim(SA1->A1_COD) == Alltrim(aCampos[1])
			If ClienteBrasil( SA1->A1_COD, SA1->A1_LOJA)
				LstGctsCliente( SA1->A1_COD, SA1->A1_LOJA)
			Else
				cErro := ";(Cliente pertence ao MI e foi desconsiderado)"
			EndIf
		Else
			cErro := ";(Cliente não localizado e foi desconsiderado)"
		EndIf

		END SEQUENCE
		ErrorBlock(bBlock)
		
        U_xacalog(cLogArqRet, aCampos[1] + ";" +  aCampos[2] + Iif ( empty(cErro) , ";feito", cErro )  )
        
	EndIf
		
	FT_FSKIP()
EndDo

FT_FUSE()
FCLOSE(nHandler)
MsgInfo('Processo Finalizado!')
return


//----------------------------------------------------------------
/*/{Protheus.doc} ThrdChkErr
Captura errlog 

<AUTHOR>
@since 10/07/2020
@version 1.0
/*/
//----------------------------------------------------------------
Static Function ThrdChkErr(oErroArq, cCli, cLoja) 
Local n:= 2
Local cLogError 
Local cDir :=  ""
Local cbErrInThrd := ''
Local cRet := oErroArq:Description + oErroArq:ErrorStack
U_xacalog(cErrArqRet , (cCli + '-' + cLoja + ' <-----'  )   )
U_xacalog(cErrArqRet , cRet)
If oErroArq:GenCode > 0

    cbErrInThrd += Alltrim( Str( oErroArq:GenCode ) )
    cbErrInThrd += chr(13)+chr(10)
    cbErrInThrd += AllTrim( oErroArq:Description )
    cbErrInThrd += chr(13)+chr(10)
EndIf 

While ( !Empty(ProcName(n)) )
    cbErrInThrd += Trim(ProcName(n)) + " (" + Alltrim(Str(ProcLine(n))) + ") " + chr(13)+chr(10)
    n++
End   

cbErrInThrd += chr(13)+chr(10)

U_xacalog(cErrArqRet , cbErrInThrd )          

Break
Return cRet 


//-------------------------------------------------------------------
/*{Protheus.doc} 
Processamento da Gravacao da ZQE

<AUTHOR>
@since 21/07/2015
@version P12
*/

//-------------------------------------------------------------------
static function LstGctsCliente(cCliente, cLojaCli)
Local aArea		:= GetArea()
Local cQuery		:= ""
Local cTabQry		:= GetNextAlias()
Local lAptoZqe      := .F.

cQuery += "SELECT		CN9.CN9_NUMERO" + CRLF
cQuery += ",			CN9.CN9_REVISA" + CRLF
cQuery += "FROM		" + RetSqlName("CN9") + " CN9" + CRLF
cQuery += "			INNER JOIN" + CRLF
cQuery += "			" + RetSqlName("CNC") + " CNC" + CRLF
cQuery	+= "			ON CNC.CNC_FILIAL = '" + xFilial("CNC") + "'" + CRLF
cQuery += "			AND CN9.CN9_NUMERO = CNC.CNC_NUMERO" + CRLF
cQuery += "			AND CN9.CN9_REVISA = CNC.CNC_REVISA" + CRLF
cQuery += "			AND CNC.CNC_CLIENT = '" + cCliente + "'" + CRLF
cQuery += "			AND CNC.CNC_LOJACL = '" + cLojaCli + "'" + CRLF
cQuery += "			AND CNC.D_E_L_E_T_ = ' ' " + CRLF
cQuery += " WHERE		CN9.CN9_FILIAL = '" + xFilial("CN9") + "'" + CRLF
cQuery += " AND 		CN9.CN9_SITUAC = '05'" + CRLF 
cQuery += " AND		CN9.CN9_ESPCTR = '2' " 
cQuery += " AND      CN9.CN9_NUMERO LIKE	'CON%'"
cQuery += " AND		CN9.D_E_L_E_T_ = ' ' " + CRLF
cQuery += " GROUP BY 	CN9.CN9_NUMERO, CN9.CN9_REVISA " + CRLF
cQuery += " ORDER BY 	CN9.CN9_NUMERO, CN9.CN9_REVISA " + CRLF

DbUseArea(.T., "TOPCONN", TcGenQry(NIL, NIL, cQuery), cTabQry, .T., .T.)

While !(cTabQry)->(Eof())
	
	lSimSetPub := .F. 
		
	If CliPublico( (cTabQry)->CN9_NUMERO, (cTabQry)->CN9_REVISA )
		If lPubConsAnt
			lSimSetPub := .T.
		EndIf
	EndIf
	
	lAptoZqe := DeveTerZqe( (cTabQry)->CN9_NUMERO, (cTabQry)->CN9_REVISA, cCliente, cLojaCli  )
	
	If lBasico
		U_xacalog(cSimpArqRet, cCliente + ";" +  cLojaCli + ";" +  (cTabQry)->CN9_NUMERO + ";" + (cTabQry)->CN9_REVISA  + ;
		iif (AI0->AI0_SETPUB == '1', ";Publico",";Comum")  + iif( lAptoZqe, ";SIM", ";NAO" )     )
	EndIf

	(cTabQry)->(DbSkip())
EndDo

If Select(cTabQry) > 0
	(cTabQry)->(DbCloseArea())
EndIf

RestArea(aArea)
Return NIL



//-------------------------------------------------------------------
/*{Protheus.doc} DeveTerZqe

<AUTHOR> Leite
@since 15/01/2021
@version Protheus12
*/
//-------------------------------------------------------------------
Static Function DeveTerZqe( cContrato, cRevisa, cClienteCsv, cLojaCsv)
	Local lRet			:= .T.
	Local lLight		:= .F.
	Local lCtrAtend		:= .F.
	Local aArea			:= GetArea()
	Local nY,nX			:= 0
	Local nTotReg		:= 0
	Local nTotRat		:= 0
	Local nZQEVlr		:= 0
	Local nZQERet 		:= 0
	Local nAscan 		:= 0
	Local nCount		:= 0
	Local nPosProd 		:= 0
	Local aProdZQE  	:= {}
	Local cPrdRet		:= ''
	Local cQuery		:= Nil
	Local cMigrac 		:= Nil
	Local cCliente 		:= Nil
	Local cLoja    		:= Nil
	Local cVerOld		:= Nil
	Local cVerNew		:= Nil
	Local cProduto 		:= Nil
	Local cGeSla		:= Nil
	Local cGrvProd 		:= Nil
	Local cSla 			:= Nil
	Local cProdut 		:= Nil
	Local cServico 		:= Nil
	Local cCtrAtend		:= Nil
	Local lQueryZQE  	:= GetMV("MV_#QRYZQE",,.T.)
	Local ni            := 0
	Local aPrg1Descart  := {}
	Local aPrg2Descart  := {}  
	Local lExecuta  := .T.  
	Local nPosic  	:= 0
	Local nCont   	:= 0
	Local aAuxProd	:= {}
	Local aAux    	:= {}
	Local aClientes	:= {}
	Local aFiltCli  := {}
	Local nIndFilt  := 0 
	Local cXCCCVtex := SuperGetMV("TI_CCCVTEX",.T.,"700999999")
	Local lCobrRegua:= (GetMv('TI_ZQEINAD',,'A') == "R")  
	Local lTF11ZQE  :=  GetMv('TI_TF11ZQE', , .T.)   
	Local lTF11ZEN  :=  GetMv('TI_TF11ZEN', , .T.)
	Local lCCCYLR   :=  GetMv("TI_CCCYLR", , .T.)
	Local cCDUGrup  := GetMV("TI_CDU2GRP",.T.,"XXXX")
	Local lDiaAnt 	:= GetMV("TI_ZANAD",,.T.) 	
	Local cGrFiltr 
	Local lSerie1	:= .F.
	Local cGrpEmp	:= cEmpAnt	
	Local aProdTotvs	:= {}
	Local aProdLight	:= {}
	Local aProdFull	:= {}
	Local dDtBase	:= dDataBase
	Local nDiasTolera := 4 * 365 	
	Local cEmpFil	:= cFilAnt
	Local lApto     := .T. 
	Local cAMVFim   := ""
	Local cAMVFimSP := ""
	Local cAMAtu    := ""
	Local cMesAno   := ""
	cGrFiltr := cGrpIts + "|" + cGRPISMB + "|" + cGRPIILI + "|" + cGRPINT  + "|" +  cGRPINT2
	
	//observei que em cada par�metro usaram operadores distintos, apenas efetuo saneamento por precau��o
	cGrFiltr := strtran(cGrFiltr, "/", "|")
	cGrFiltr := strtran(cGrFiltr, "\", "|")
	cGrFiltr := strtran(cGrFiltr, "||", "|")
	
	If lBasico
		cQuery := " SELECT DISTINCT CNC.CNC_CLIENT, CNC.CNC_LOJACL, CNC.CNC_TIPCLI, CNC.CNC_ATENDE, CNC.CNC_INADIM, CNC.CNC_GRPEMP, CNC.CNC_UNINEG, CNC.CNC_DTATUA, "+CRLF
		cQuery += "					CNB.CNB_CONTRA, CNB.CNB_REVISA, CNB.CNB_NUMERO, CNB.CNB_PRODUT, CNB.CNB_VIGFIM, "+CRLF
		cQuery += "					SB1.B1_GRUPO, SB1.B1_DESC, SBM.BM_XSERIE, SBM.BM_XLINREC, SBM.BM_XCCC, SBM.BM_XCLVL, CNA.CNA_PICPAD, "+CRLF
	Else
		cQuery := " SELECT DISTINCT CNC.CNC_CLIENT, CNC.CNC_LOJACL, CNC.CNC_TIPCLI, CNC.CNC_ATENDE, CNC.CNC_INADIM, CNC.CNC_GRPEMP, CNC.CNC_UNINEG, CNC.CNC_DTATUA, "+CRLF
		cQuery += "					CNB.CNB_CONTRA, CNB.CNB_REVISA, CNB.CNB_NUMERO, CNB.CNB_ITEM, CNB.CNB_PRODUT, CNB.CNB_VIGFIM, "+CRLF
		cQuery += "					SB1.B1_GRUPO, SB1.B1_DESC, SBM.BM_XSERIE, SBM.BM_XLINREC, SBM.BM_XCCC, SBM.BM_XCLVL, CNA.CNA_PICPAD, CNB.CNB_TIPREC, CNB.CNB_SITUAC,"+CRLF
	EndIf

	cQuery += "	( CASE WHEN EXISTS( SELECT DISTINCT 1 EXIST FROM "+ RetSQLName( 'ZQE' ) +" ZQE WHERE ZQE.ZQE_FILIAL = '"+ FWxFilial( 'ZQE' ) +"' AND ZQE.ZQE_CONTR = CNB.CNB_CONTRA AND ZQE.D_E_L_E_T_ = ' '  ) "+CRLF
	cQuery += "		   THEN( SELECT DISTINCT MAX( ZQE.ZQE_VERSAO ) VERSAO FROM "+ RetSQLName( 'ZQE' ) +" ZQE WHERE ZQE.ZQE_FILIAL = '"+ FWxFilial( 'ZQE' ) +"' AND ZQE.ZQE_CONTR = CNB.CNB_CONTRA AND ZQE.D_E_L_E_T_ = ' ' GROUP BY ZQE.ZQE_VERSAO FETCH FIRST 1 ROWS ONLY ) "+CRLF	
	cQuery += "	  ELSE CNB.CNB_REVISA 	"+CRLF
	cQuery += "	  END ) ZQE_VERSAO, 	"+CRLF

	cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrpSla, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) SLA, "+CRLF
	cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrpGEs, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) GES, "+CRLF
	cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrpBso, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) BSO, "+CRLF
	
	cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrpAMS, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) AMS, "+CRLF
	
	cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrpBcs, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) BCS, "+CRLF
	cQuery += "					( CASE WHEN( SB1.B1_GRUPO IN "+ FormatIn( cGrFiltr, '|' ) +" ) THEN 'SIM' ELSE 'NAO' END ) ITA, "+CRLF

	cQuery += "					( SELECT DISTINCT SA1.A1_CGC FROM "+ RetSQLName( 'SA1' ) +" SA1 WHERE SA1.A1_FILIAL  = '"+ FWxFilial( 'SA1' ) +"' AND SA1.A1_COD = CNC.CNC_CLIENT AND SA1.A1_LOJA = CNC.CNC_LOJACL AND SA1.D_E_L_E_T_ = ' ' GROUP BY SA1.A1_CGC FETCH FIRST 1 ROWS ONLY ) CNPJ, "+CRLF

	cQuery += "					( CASE 	WHEN ( SUBSTR(SBM.BM_XCCC,1,3) = '4A0' OR SBM.BM_XLINREC IN "+ FormatIn( cGrpSaas, '|' ) +" ) THEN '200000' "+CRLF	
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '4I0' THEN '300000'	 "+CRLF
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '4B0' THEN '400000'	 "+CRLF
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '520' THEN '800000'	 "+CRLF
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '4C0' THEN 'A00000'	 "+CRLF
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '4D0' THEN 'B00000'	 "+CRLF
	cQuery += "							WHEN SUBSTR(SBM.BM_XCCC,1,3) = '4E0' THEN 'D00000'	 "+CRLF
	cQuery += "							WHEN SBM.BM_XCCC = '" + cXCCCVtex + "' THEN '310000' "+CRLF
	cQuery += "					 ELSE '100000'  "+CRLF
	cQuery += "					 END  ) ZQE_SERVIC, "+CRLF

	cQuery += "					SUM( CNB.CNB_QUANT ) QTDLIC "+CRLF

	cQuery += "	FROM "+ RetSQLName( 'CNC' ) +" CNC "+CRLF
	cQuery += "			INNER JOIN "+ RetSQLName( 'CNB' ) +" CNB ON ( CNB.CNB_FILIAL = '"+ FWxFilial( 'CNB' ) +"' AND CNC.CNC_FILIAL = '"+ FWxFilial( 'CNC' ) +"' AND CNB.CNB_CONTRA = CNC.CNC_NUMERO AND CNB.CNB_REVISA = CNC.CNC_REVISA ) "+CRLF
	cQuery += "			INNER JOIN "+ RetSQLName( 'CNA' ) +" CNA ON ( CNA.CNA_FILIAL = '"+ FWxFilial( 'CNA' ) +"' AND CNB.CNB_FILIAL = '"+ FWxFilial( 'CNB' ) +"' AND CNA.CNA_CONTRA = CNB.CNB_CONTRA AND CNA.CNA_REVISA = CNB.CNB_REVISA AND CNA.CNA_NUMERO = CNB.CNB_NUMERO ) "+CRLF
	cQuery += "			INNER JOIN "+ RetSQLName( 'SB1' ) +" SB1 ON ( CNB.CNB_FILIAL = '"+ FWxFilial( 'CNB' ) +"' AND SB1.B1_FILIAL  = '"+ FWxFilial( 'SB1' ) +"' AND CNB.CNB_PRODUT = SB1.B1_COD ) "+CRLF
	cQuery += "			INNER JOIN "+ RetSQLName( 'SBM' ) +" SBM ON ( SBM.BM_FILIAL  = '"+ FWxFilial( 'SBM' ) +"' AND SB1.B1_FILIAL  = '"+ FWxFilial( 'SB1' ) +"' AND SBM.BM_GRUPO = SB1.B1_GRUPO ) "+CRLF

	cQuery += "	WHERE CNB.CNB_CONTRA = '"+ cContrato +"' "+CRLF

	If !Empty( cRevisa )
		cQuery += "	  AND CNB.CNB_REVISA = '"+ cRevisa +"' "+CRLF
	Else
		cQuery += "	  AND CNB.CNB_REVISA IN (  	SELECT MAX( CN9.CN9_REVISA ) CN9_REVISA "+CRLF 
		cQuery += "	  							FROM "+ RetSQLName( 'CN9' ) +" CN9  "+CRLF
		cQuery += "	  							WHERE CN9.CN9_FILIAL = CNB.CNB_FILIAL  "+CRLF
		cQuery += "	  								AND CN9.CN9_NUMERO = CNB.CNB_CONTRA  "+CRLF
		cQuery += "	  								AND CN9.CN9_ESPCTR = '2'  "+CRLF
		cQuery += "	  								AND SUBSTR( CN9.CN9_NUMERO, 1, 3 ) = 'CON' "+CRLF 
		cQuery += "	  								AND CN9.CN9_SITUAC = '05'  "+CRLF
		cQuery += "	  								AND CN9.CN9_REVATU = ' '  "+CRLF
		cQuery += "	  								AND CN9.D_E_L_E_T_ = ' '  ) "+CRLF
	EndIf

	If lBasico
		cQuery += "	  AND CNB.CNB_SITUAC IN "+ FormatIn( cCtrSitu , '|' ) +" "+CRLF
		cQuery += "	  AND (CNB.CNB_TIPREC = '1' OR SB1.B1_GRUPO IN " + FormatIn( cCDUGrup, '|' ) + ") " + CRLF // 1-Recorrente OU Produto CDU
	EndIf
//// Inicio da selecaoo de cliente destino de faturamento com rateio ( NAO Hist�rico ) deve ser relacionado - ticket 541149 e que seja Recorrente - ticket 474865
	cQuery += " 	AND (	CNC.CNC_TIPCLI IN ('01','03', '04') OR 
	cQuery += " 	EXISTS( SELECT PHG.PHG_CLIENT FROM "+ RetSQLName( 'PHG' ) +" PHG  "+CRLF 
	cQuery += " 			 WHERE PHG.PHG_FILIAL = CNB.CNB_FILIAL "+CRLF
	cQuery += " 			 AND PHG.PHG_CONTRA = CNB.CNB_CONTRA "+CRLF
	cQuery += " 			 AND PHG.PHG_REVISA = CNB.CNB_REVISA "+CRLF
	cQuery += " 		   	 AND PHG.PHG_NUMERO = CNB.CNB_NUMERO "+CRLF
	cQuery += " 		     AND PHG.PHG_PRODUT = CNB.CNB_PRODUT "+CRLF
//// Relacionamento entre CNB e PHG deve ser feito tamb�m pelo Numero do Item - ticket 541129
    cQuery += " 			 AND PHG.PHG_ITEM = CNB.CNB_ITEM "+CRLF 
	cQuery += " 		   	 AND PHG.PHG_CLIENT = CNC.CNC_CLIENT "+CRLF
	cQuery += "			     AND PHG.PHG_LOJA   = CNC.CNC_LOJACL "+CRLF 
	cQuery += " 			 AND PHG.PHG_PERRAT > 0 AND  PHG.D_E_L_E_T_ = '  ' )) "+CRLF
//// Final da selecaoo de cliente destino de faturamento com rateio ( NAO Hist�rico ) deve ser relacionado - ticket 541149 e que seja Recorrente - ticket 474865
	cQuery += "	  AND CNB.D_E_L_E_T_ = ' ' "+CRLF
	cQuery += "	  AND CNC.D_E_L_E_T_ = ' ' "+CRLF
	cQuery += "	  AND SB1.D_E_L_E_T_ = ' ' "+CRLF
	cQuery += "	  AND SBM.D_E_L_E_T_ = ' ' "+CRLF
	cQuery += "	  AND CNA.D_E_L_E_T_ = ' ' "+CRLF
	
	
	If lBasico
		If lSimSetPub
			cQuery += "	  AND CNB.CNB_VIGFIM <> ' ' "+CRLF
		EndIf

		cQuery += "	GROUP BY CNC.CNC_CLIENT, CNC.CNC_LOJACL, CNC.CNC_TIPCLI, CNC.CNC_ATENDE, CNC.CNC_INADIM, CNC.CNC_GRPEMP, CNC.CNC_UNINEG, CNC.CNC_DTATUA, "+CRLF
		cQuery += "	  		 CNB.CNB_CONTRA, CNB.CNB_REVISA, CNB.CNB_NUMERO, CNB.CNB_PRODUT, CNB.CNB_VIGFIM, "+CRLF
		cQuery += "	  		 SB1.B1_GRUPO, SB1.B1_DESC, SBM.BM_XSERIE, SBM.BM_XLINREC, SBM.BM_XCCC, SBM.BM_XCLVL, CNA.CNA_PICPAD "+CRLF
	Else
		cQuery += "	GROUP BY CNC.CNC_CLIENT, CNC.CNC_LOJACL, CNC.CNC_TIPCLI, CNC.CNC_ATENDE, CNC.CNC_INADIM, CNC.CNC_GRPEMP, CNC.CNC_UNINEG, CNC.CNC_DTATUA, "+CRLF
		cQuery += "	  		 CNB.CNB_CONTRA, CNB.CNB_REVISA, CNB.CNB_NUMERO, CNB.CNB_ITEM, CNB.CNB_PRODUT, CNB.CNB_VIGFIM, "+CRLF
		cQuery += "	  		 SB1.B1_GRUPO, SB1.B1_DESC, SBM.BM_XSERIE, SBM.BM_XLINREC, SBM.BM_XCCC, SBM.BM_XCLVL, CNA.CNA_PICPAD, CNB.CNB_TIPREC, CNB.CNB_SITUAC "+CRLF
	EndIf


	If lBasico
		If lSimSetPub
			cQuery += "	HAVING( '" + Dtos( dDtBase ) + "' <=  last_day ( ADD_MONTHS(cnb_vigfim, 48) )   ) " + CRLF
		Else		
			cQuery += "	HAVING( CNB.CNB_VIGFIM >= '"+ Dtos( dDtBase ) +"'   ) "+CRLF
		EndIf
	EndIf

	cQuery += "	ORDER BY CNC_TIPCLI, CNC_CLIENT, CNC_LOJACL, CNB_CONTRA, CNB_REVISA, CNB_NUMERO  "

	
	dbUseArea( .T., __cRdd, TcGenQry( ,, cQuery ), cSD6Alias, .T., .F. )
	dbSelectArea( cSD6Alias )
	AEval( CNA->( dbStruct() ), { | x | IIf( !( x[2] == 'C' ), TcSetField( ( cSD6Alias ), AllTrim( x[ 1 ] ), x[ 2 ], x[ 3 ], x[ 4 ] ) , Nil ) } )
	AEval( CNB->( dbStruct() ), { | x | IIf( !( x[2] == 'C' ), TcSetField( ( cSD6Alias ), AllTrim( x[ 1 ] ), x[ 2 ], x[ 3 ], x[ 4 ] ) , Nil ) } )
	AEval( CNC->( dbStruct() ), { | x | IIf( !( x[2] == 'C' ), TcSetField( ( cSD6Alias ), AllTrim( x[ 1 ] ), x[ 2 ], x[ 3 ], x[ 4 ] ) , Nil ) } )

	
	If lBasico
		( cSD6Alias )->( dbEval( { || nTotReg ++ },,{ || !Eof() } ) )

		If nTotReg == 0
			lRet := .F.
		EndIf
	Else
		while (cSD6Alias)->(!Eof())
			lApto := .T. 
		
			If !( (cSD6Alias)->CNB_SITUAC $ cCtrSitu )
				lApto := .F. 
			EndIf

			If (cSD6Alias)->CNB_TIPREC == "2" .And.  !( (cSD6Alias)->B1_GRUPO $ cCDUGrup )
				lApto := .F. 
			EndIf

			If AI0->AI0_SETPUB == '1' 
				if !empty((cSD6Alias)->CNB_VIGFIM)
				
					If lSimSetPub //regra nova
						cAMVFimSP := Left(DTOS((cSD6Alias)->CNB_VIGFIM + nDiasTolera), 6) 
						cAMAtu    := Left(DTOS(dDtBase), 6) 
						
						If cAMAtu > cAMVFimSP
							lApto := .F. 
						EndIf
					Else
						//regra antiga
						If (cSD6Alias)->CNB_VIGFIM < dDtBase
							lApto := .F. 
						EndIf
					EndIf
				Else
					lApto := .F. 
				EndIf
			Else
				if !empty((cSD6Alias)->CNB_VIGFIM)
					If (cSD6Alias)->CNB_VIGFIM < dDtBase
						lApto := .F. 
					EndIf
				Else
					lApto := .F. 
				EndIf
			EndIf
		
			If !empty((cSD6Alias)->CNB_VIGFIM)
				cMesAno := substr (   Left(DTOS((cSD6Alias)->(CNB_VIGFIM) + nDiasTolera), 6),5,2 ) + "/" + substr (   Left(DTOS((cSD6Alias)->(CNB_VIGFIM) + nDiasTolera), 6),1,4 )
			Else
				cMesAno := ""
			EndIf

			U_xacalog(cDetArqRet, cClienteCsv + ";" +  cLojaCsv + ";" +  (cSD6Alias)->CNB_CONTRA + ";" + ;
			                      (cSD6Alias)->CNB_REVISA + ";" + ; 
								  (cSD6Alias)->CNB_NUMERO + ";" + (cSD6Alias)->CNB_ITEM +";" + ;
								  (cSD6Alias)->CNB_PRODUT + ";" + (cSD6Alias)->B1_GRUPO + ";" + ;
								  (cSD6Alias)->CNB_TIPREC + ";" + dtoc((cSD6Alias)->CNB_VIGFIM) + ";" + ;  
								  (cSD6Alias)->CNB_SITUAC +   ;
								  iif(AI0->AI0_SETPUB == '1', ";Publico", ";Comum") + ";" + ;
								  iif(AI0->AI0_SETPUB == '1',	cMesAno   , "") +  ;
								  iif( lApto, ";SIM", ";NAO" )     )
		
			(cSD6Alias)->(dbskip())
		End
	EndIf
	
	(cSD6Alias)->( dbCloseArea() )
	
	RestArea( aArea )

	// inicia processo de limpeza da mem�ria
	aSize(aArea,0)
	aSize(aProdZQE,0)
	aSize(aPrg1Descart,0)
	aSize(aPrg2Descart,0)  
	aSize(aAuxProd,0)
	aSize(aAux,0)
	aSize(aClientes,0)
	aSize(aFiltCli,0)

	aArea		  := NIL
	aProdZQE  	  := NIL
	aPrg1Descart  := NIL
	aPrg2Descart  := NIL 
	aAuxProd	  := NIL
	aAux    	  := NIL
	aClientes	  := NIL
	aFiltCli  	  := NIL

Return lRet 



//-------------------------------------------------------------------
/*{Protheus.doc} 
Validacao Clientes MI 
<AUTHOR>
@since 27/03/2019
@version Protheus12
*/
//-------------------------------------------------------------------
static function ClienteBrasil(cCli,cLoja)
Local cQry      := ""
Local aDados   := {}
LOCAL nI        := 0
Local lRet := .T.   

cQry += "SELECT DISTINCT ZQE.ZQE_CODCLI AS CODCLI, ZQE.ZQE_GRPEMP AS CODGRP"+ CRLF
cQry += "FROM " +RetSqlName("ZQE")+ " ZQE " + CRLF
cQry += "WHERE "
cQry += "ZQE.ZQE_FILIAL ='" + xFilial("ZQE") + "' "+ CRLF
cQry += "AND ZQE.ZQE_CODCLI = '"+cCli+"'"	+ CRLF
cQry += "AND ZQE.ZQE_LJCLI = '"+cLoja+"'"	+ CRLF
cQry += "AND D_E_L_E_T_ = ' ' "+ CRLF

DbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAliasQry,.T.,.F.)

While (cAliasQry)->(! Eof())
	If (cAliasQry)->CODGRP !=  "00" .AND. cEmpAnt == "00" 
		lRet := .F.
		exit
	else 
		lRet := .T.
	Endif
	
	(cAliasQry)->(DbSkip())
EndDo

DbCloseArea(cAliasQry)
				
Return (lRet)
//-------------------------------------------------------------------
/*{Protheus.doc} 
Verifica se é Cliente do tipo Publico
<AUTHOR>
@since 27/03/2020
@version Protheus12
*/
//-------------------------------------------------------------------
Static Function CliPublico(cContrato, cRevisa)
Local aAreaCNC := CNC->(GetArea("CNC"))
Local lRet     := .F. 

CNC->(DbSetOrder(5))  
CNC->(DbSeek(xFilial("CNC") + cContrato + cRevisa + "01"))

If CNC->(!Eof())
	AI0->(DbSetOrder(1))  
	AI0->(DbSeek(xFilial("AI0") +  CNC->(CNC_CLIENT + CNC_LOJACL) )) 
		
	If AI0->(!Eof())
		If AI0->AI0_SETPUB == '1'
			lRet := .T. 
		EndIf
	EndIf
EndIf

RestArea(aAreaCNC)
Return lRet
