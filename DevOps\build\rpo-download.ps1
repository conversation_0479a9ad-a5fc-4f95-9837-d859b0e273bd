param (
    [string]$ftppath    = "FTP://ftp.cp.totvs.com.br/TI_sistemas/TOTVS12/PRODUCAO/RPO/totvs12/",
    [string]$ftpfile    = "file_test.log",
    [string]$ftpuser    = "user", 
    [string]$ftppsw     = "psw",
    [string]$outpath    = "outpath"
)

Write-Host "Exebot param1: $ftppath"
Write-Host "Exebot param2: $ftpfile"
Write-Host "Exebot param3: $ftpuser"
Write-Host "Exebot param5: $ftppsw"
Write-Host "Exebot param3: $outpath"

$PWord = ConvertTo-SecureString -String $ftppsw -AsPlainText -Force

Write-Host "PWord: $PWord"

$Cred = New-Object System.Management.Automation.PSCredential ("$ftpuser", $PWord)

Write-Host "Cred: $Cred"

Invoke-WebRequest -Uri "$ftppath$ftpfile" -OutFile "$outpath$ftpfile" -Credential $Cred