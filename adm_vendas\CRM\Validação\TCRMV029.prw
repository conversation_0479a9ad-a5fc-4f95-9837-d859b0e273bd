#INCLUDE 'PROTHEUS.CH'

/*/{Protheus.doc} TCRMV029
Criado para atualizar
<AUTHOR>
@since 07/09/2016
@version undefined

@type function
/*/
User Function TCRMV029
Local oModel		:= FWModelActive()
Local oModelADY		:= oModel:GetModel("ADYMASTER")

If lRet .and. oModelADY:GetValue("ADY_XTPINT") $ "3/4" .and. !(oModelADY:GetValue("ADY_XMODAL") $ GetMV("TI_NVINTI",,"0352"))
	FWMsgRun( ,{|| U_TCRMX102() },"Atualizando proposta...","Aguarde...")
EndIf

Return(.t.)


 