#INCLUDE "PROTHEUS.CH"

#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} TCTBA056 
Funcao responsavel pela callback do interceptor referente a gravar dados na CT2 DA GESPLAN
@type  Function
<AUTHOR> Menabue Lima
@since 29/08/2022
@version 1.0
/*/
User Function TCTBA056()

	Local oJson         := JsonObject():new()
	Local oBody
	Local lFalhou       := .F.
	Local cFalha        := ""
	Local aLancam		:= {}
	Local cSyscode		:= ""
	Local oResponse 	:=  JsonObject():new()
	Local aHeader       := {}
	Local nHeader		:= 0

	oBody:= oJson:FromJson(PARAMIXB[2])
	oResponse["status" ] := 200
	//Desmarca o registro para evitar sujeira
	P37->(Re<PERSON><PERSON>ock("P37",.F.))
	P37->P37_STATUS := "2"
	P37->(MsUnLock())

	If VALTYPE(oBody) == 'U'
		aHeader := oJson:GetJsonObject("header")
		If ValType(aHeader) == "A"
			for nHeader :=1 to Len(aHeader)
				aLancam := aHeader[nHeader]:GetJsonObject("items")
				cSyscode:= aHeader[nHeader]:GetJsonObject("syscode")
				If ValType(aLancam) == "A"
					If Len(aLancam) > 0
						cFilAnt:= aHeader[nHeader]:GetJsonObject("filial")
						cFalha:=SaveCt2(aHeader[nHeader],aLancam)
						lFalhou:=!Empty(Alltrim(cFalha))
						P37->(RecLock("P37",.F.))
						If lFalhou
							P37->P37_STATUS := "5"
							oResponse["status" ]  := 500
							oResponse["mensagem" ]:=cFalha
							oResponse["syscode" ] :=cSyscode
							P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
						Else
							P37->P37_STATUS := "2"
							P37->P37_BODYRP := '{"status":200, "mensagem":"Registros incluidos com sucesso","syscode":"'+cSyscode+'"}'
						EndIf
						P37->(MsUnLock())
						oResponse := Nil
						FreeObj(oResponse)
					Else
						P37->(RecLock("P37",.F.))
						oResponse["status" ] := 500
						oResponse["mensagem" ] := "Invalid Body"
						oResponse["syscode" ]:=cSyscode
						P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
						P37->P37_STATUS := "3"
						P37->(MsUnLock())
					EndIF
				Else
					P37->(RecLock("P37",.F.))
					oResponse["status" ] := 500
					oResponse["mensagem" ] := "Header is not Array"
					oResponse["syscode" ]:=cSyscode
					P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
					P37->P37_STATUS := "3"
					P37->(MsUnLock())
				EndIF
			Next aHeader
		Else
			P37->(RecLock("P37",.F.))
			oResponse["status" ] := 500
			oResponse["mensagem" ] := "Body Vazio"
			oResponse["syscode" ]:=cSyscode
			P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
			P37->P37_STATUS := "3"
			P37->(MsUnLock())
		EndIF
	EndIF
Return
/*/{Protheus.doc} SaveCt2
	Salva os lancamentos Contabeis 
	@type   Function
	<AUTHOR> Menabue Lima
	@since 29/08/2022
	@version 12.1.33
 
/*/
Static Function SaveCt2(oCabec,aLcto)
	Local nI
	Local aArea         := GetArea()
//	Local nDoc          := 1
//	Local lRet          := .T.
	Local aCab          := {}
	Local aItens        := {}
	//sLocal aLinha        := {}
	Local dDataLanc     := ctod("")
	Local nLinha        := '001'
	Local cError		:= ""
	Local cDebito 		:= ""
	Local cCCD			:= ""
	Local cItemD		:= ""
	Local cClVlDb		:= ""
	Local cCredit		:= ""
	Local cCCC			:= ""
	Local cItemC		:= ""
	Local cClVlCr		:= ""
	Local cDC			:= ""
	Local cOrigem		:= ""
	Local cHist 		:= ""
	Local nValor 		:= 0
	//Local aColums       := aLcto[01]:GetNames()
	Default cEmp 		:= cEmpAnt
	Default cFil 		:= cFilAnt
	Private lMsErroAuto := .F.
	Private lMsHelpAuto := .T.
	Private CTF_LOCK    := 0
	Private lSubLote    := .T.


	SetFunName("TCTBA056")

	dDataLanc:=StoD(oCabec["data"])

	aCab := {}
	if  Empty(dDataLanc)
		dDataLanc:= ddatabase
	EndIF
	aAdd(aCab, {'DDATALANC' ,dDataLanc			,NIL} )
	aAdd(aCab, {'CLOTE' 	,oCabec["lote"] 	,NIL} )
	aAdd(aCab, {'CSUBLOTE'  ,oCabec["sublote"]  ,NIL} )
	// aAdd(aCab, {'CDOC' ,aCabec[4] ,NIL} )
	aAdd(aCab, {'CPADRAO' 	,'' 				,NIL} )
	aAdd(aCab, {'NTOTINF' 	,0 					,NIL} )
	aAdd(aCab, {'NTOTINFLOT',0 					,NIL} )

	nLinha:=0
	For nI:= 1 to Len(aLcto)
		nLinha ++
		cFilAnt			:= aLcto[nI]["CT2_FILIAL"]
		cOrigem			:= aLcto[nI]["CT2_ORIGEM"]
		cHist			:= aLcto[nI]["CT2_HIST"]
		cMoeda			:= aLcto[nI]["CT2_MOEDLC"]
		nValor			:= Val( strtran(aLcto[nI]["CT2_VALOR"],",","."))
		cDC			:= aLcto[nI]["CT2_DC"]
		If(!"EST" $ cOrigem ) //Se nao for de estorno

			cDebito 	:= aLcto[nI]["CT2_DEBITO"]
			cCCD		:= aLcto[nI]["CT2_CCD"]
			cItemD		:= aLcto[nI]["CT2_ITEMD"]
			cClVlDb		:= aLcto[nI]["CT2_CLVLDB"]
			cCredit		:= aLcto[nI]["CT2_CREDIT"]
			cCCC		:= aLcto[nI]["CT2_CCC"]
			cItemC		:= aLcto[nI]["CT2_ITEMC"]
			cClVlCr		:= aLcto[nI]["CT2_CLVLCR"]
		Else

			cDC			:= IIF(cDC=="1","2","1")
			cDebito 	:= aLcto[nI]["CT2_CREDIT"]
			cCCD		:= aLcto[nI]["CT2_CCC"]
			cItemD		:= aLcto[nI]["CT2_ITEMC"]
			cClVlDb		:= aLcto[nI]["CT2_CLVLCR"]
			cCredit		:= aLcto[nI]["CT2_DEBITO"]
			cCCC		:= aLcto[nI]["CT2_CCD"]
			cItemC		:= aLcto[nI]["CT2_ITEMD"]
			cClVlCr		:= aLcto[nI]["CT2_CLVLDB"]
		EndIF
		aAdd(aItens,  { ;
			{'CT2_FILIAL' 	,cFilAnt 					, NIL},;
			{'CT2_LINHA' 	,StrZero(nLinha,3)			, NIL},;
			{'CT2_MOEDLC'	,cMoeda					 	, NIL},;
			{'CT2_DC' 		,cDC				 		, NIL},;
			{'CT2_DEBITO' 	,cDebito				 	, NIL},;
			{'CT2_CREDIT' 	,cCredit				 	, NIL},;
			{'CT2_CCD' 		,cCCD				 		, NIL},;
			{'CT2_CCC' 		,cCCC				 		, NIL},;
			{'CT2_ITEMD' 	,cItemD						, NIL},;
			{'CT2_ITEMC' 	,cItemC					 	, NIL},;
			{'CT2_CLVLDB' 	,cClVlDb 					, NIL},;
			{'CT2_CLVLCR' 	,cClVlCr 					, NIL},;
			{'CT2_VALOR' 	,nValor						, NIL},;
			{'CT2_ORIGEM'   ,cOrigem				 	, NIL},;
			{'CT2_HIST' 	,cHist						, NIL} ,;
			{'CT2_EMPORI' 	,cEmpAnt 					, NIL} ,;
			{'CT2_FILORI' 	,cFilAnt 					, NIL} } )

	Next nI

	MSExecAuto({|x, y,z| CTBA102(x,y,z)}, aCab ,aItens, 3,.T.)

	If !lMsErroAuto
		//UPD para trocar o CT2_MANUAL PARA 2
		cQry := " UPDATE "+ RetSQLName( 'CT2' ) +" CT2 "
		cQry += " SET CT2_MANUAL = '2' , CT2_ROTINA ='TCTBA056'"
		cQry += " WHERE CT2.CT2_FILIAL = '"+cFilAnt+"' "
		cQry += " AND CT2.CT2_ORIGEM = '"+cOrigem+"' "
		cQry += " AND CT2.CT2_MANUAL = '1' "
		cQry += " AND CT2.D_E_L_E_T_ = '"+Space(1)+"' "

		If TcSQLExec( cQry ) == 0
			TcSQLExec( 'COMMIT' )
		Else
			TcSQLExec( 'ROLLBACK' )
		EndIf
	Else // EM ESTADO DE JOB
		cTime:= dTos(Date()) + strtran(IncTime(time() ,3,0,0 ),":")
		cError := MostraErro("/system/gesplan/", "TCTBA056_"+cTime+".log")
		cError := SubStr(cError,1,3000)
	EndIf

	RestArea(aArea)


Return cError
