#INCLUDE 'TOTVS.CH'
#INCLUDE 'RESTFUL.CH'
#INCLUDE 'FWMVCDEF.CH'


/*/{Protheus.doc} TGCTS007

OFERTAS_DIGITAIS - API de busca de status processos de cancelamento de contrato.
Issue: https://jiraproducao.totvs.com.br/browse/TIPDBP-272

<AUTHOR>
@since		18/05/2022
@version	1.0
/*/
            
User Function TGCTS007()
    Local lRet	      := .T.
    Local oResponse   := Nil

    Local cBody       := PARAMIXB[2]
    Local oJson       := JsonObject():new()
    Local cRetoJson   := NIL

    Local cErroBlk    := ''
    Local oException  := ErrorBlock({|e| cErroBlk := + e:Description + e:ErrorStack, lRet := .F. })

    Local cIdseq      := ''
    Local cCinteg     := GetMv("PMX_CINTG", .F. ,"000000")
   
    Local cStatusCan  := ""
    Local cMsgCan     := ""
    Local cMessage    := ''
    Local cDetailMsg  := ''      

    Begin Sequence

    /*
    //Json modelo
    {
        "idseq": "000000000004",
    }
    */

        cRetoJson := oJson:FromJson(cBody)
        If ValType(cRetoJson) == "U"
            cIdseq  := AllTrim(oJson:GetJsonObject("idseq"))
        Else
            lRet := .F.
        EndIf

        If ! Empty(cIdseq) .And. ! Empty(cCinteg)  
            PMX->(DbSetOrder(3))  // "PMX_FILIAL+PMX_IDSEQ+PMX_CINTEG+PMX_TPTRAN"
            If ! PMX->(DbSeek(xFilial("PMX") + cIdseq + cCinteg))
                lRet := .F.          
                cMessage := 'Cliente nao encontrado'
                cDetailMsg := 'iDseq/Cinteg nao existe na base de dados.'
            EndIf
        Else
            lRet := .f.
            cMessage := 'Parametro obrigatorio nao preenchido: idseq.'
            cDetailMsg := 'Envie o codigo/ticket.'
        EndIf

        If lRet 
            //Retorna o status do cancelamento.
            cStatusCan := PMX->PMX_STATUS
            cMsgCan    := Alltrim(PMX->PMX_OBS)
        EndIf

    End Sequence

    ErrorBlock(oException)

    If Type('oResponse') == 'O'
        FreeObj(oResponse)
    EndIf

    oResponse := JsonObject():New()

    // Verifica errorBlock
    If lRet
        oResponse['statusCan']  := cStatusCan
        oResponse['messageCan'] := cMsgCan
    Else
        If !Empty(cMessage)
            oResponse['code'] := 2
            oResponse['status'] := 400
            oResponse['message'] :=  DecodeUTF8(cMessage)
            oResponse['detailedMessage'] :=  DecodeUTF8(cDetailMsg)
        Else
            oResponse['code'] := 1
            oResponse['status'] := 500
            oResponse['message'] := DecodeUTF8('Aconteceu um erro inesperado no serviao!')
            oResponse['detailedMessage'] := cErroBlk
        EndIf
    EndIf

    P37->(RecLock( "P37", .F.))
    P37->P37_STATUS := "2"
    P37->P37_TIMERP := Time()
    P37->P37_BODYRP := EncodeUTF8(oResponse:toJson())
    P37->(MsUnLock())

    oJson     := Nil
    oResponse := Nil

    FreeObj(oJson)
    FreeObj(oResponse)
  
Return Nil


