#INCLUDE "PROTHEUS.CH"

#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"
#INCLUDE "TRYEXCEPTION.CH"
#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} TCTBA069 
Funcao responsavel pela callback do interceptor referente a gravar dados na CT2  de forma assincrona
@type  Function
<AUTHOR> Menabue Lima
@since 30/05/2023
@version 1.0
/*/
User Function TCTBA069(nPar)
//U_TCTBA069(14019818) DIMENSA U_TCTBA069(14019819) MPN U_TCTBA069(14019820) 00 15225568
	Local oJson         := JsonObject():new()
	Local oBody
	Local lFalhou       := .F.
	Local cFalha        := ""
	Local aLancam		:= {}
	Local oResponse 	:=  JsonObject():new()
	Local aHeader       := {}
	Local nHeader		:= 0
	Local cFil 			:=""
	Local cFilMpn		:= SuperGetMv("TI_TBA069F",.F.,"XXXXXXXXXXX")//Filiais que vao entrar no Midleware
	Local cEmpMiddle	:= SuperGetMv("TI_TBA069E",.F.,"XX")//Filiais que vao entrar no Midleware
	Local oContabil
	Default nPar        := 0
	if(nPar==0)
		oBody:= oJson:FromJson(PARAMIXB[2])
	Else
		DBSELECTAREA( "P37" )
		P37->(DBSETORDER(1))
		P37->(DBGOTO(nPar))//14189199
		oBody:= oJson:FromJson(P37->P37_BODY)
	EndIF
	oResponse["status" ] := 200
	//Desmarca o registro para evitar sujeira
	P37->(RecLock("P37",.F.))
	P37->P37_STATUS := "F"
	P37->P37_TIMERE := TIME()
	P37->P37_BODYRP := '{ "mensagem":"Preparando pra processamento"}'
	P37->(MsUnLock())
	__cUserID := "000000"
	If VALTYPE(oBody) == 'U'
		aHeader := oJson:GetJsonObject("header")
		If ValType(aHeader) == "A"
			for nHeader :=1 to Len(aHeader)
				cFil:=aHeader[nHeader]:GetJsonObject("filial")
				cEmpreFil:= U_TIRCVEMP(cFil)
				//Se no item For a Filial do Parametro ou Empresa Do parametro faz o desvio para as demais bases de acordo com o cadastro da P36
				//Neste desvio sera gerado uma nova P37 do tipo get no grupo 00 que ficara responsavel por mandar o json para o sistema legado e coletar seu processando
				//, atualizando o status P37->P37_BODYRP nesta requisiocao aqui do grupo 00
				IF(cFil $ cFilMpn .Or. cEmpMiddle $ cEmpreFil )
					oContabil:=TICONTABIL():New()
					oContabil:SetMiddlewareTCTBA069(P37->P37_COD,P37->P37_BODY,P37->P37_FILIAL,cFil,cEmpreFil)
					FreeObj(oContabil)
					Return
				EndIF
				if(!Empty(Alltrim(cFil)))
					cFilAnt:=cFil
					aLancam := aHeader[nHeader]:GetJsonObject("items")
					If ValType(aLancam) == "A"
						If Len(aLancam) > 0
							cFalha :=SaveCt2(aHeader[nHeader],aLancam)
							lFalhou:=!Empty(Alltrim(cFalha))
							P37->(RecLock("P37",.F.))
							If lFalhou
								P37->P37_STATUS := "5"
								oResponse["status" ]  := 500
								oResponse["mensagem" ]:=cFalha
								P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
								P37->P37_TIMERP:= TIME()
							Else
								P37->P37_STATUS := "2"
								P37->P37_BODYRP := '{"status":200, "mensagem":"Registros incluidos com sucesso"}'
								P37->P37_TIMERP:= TIME()
							EndIf
							P37->(MsUnLock())
							oResponse := Nil
							FreeObj(oResponse)
						Else
							P37->(RecLock("P37",.F.))
							oResponse["status" ] := 500
							oResponse["mensagem" ] := "Invalid Body"
							P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
							P37->P37_STATUS := "3"
							P37->P37_TIMERP:= TIME()
							P37->(MsUnLock())
						EndIF
					Else
						P37->(RecLock("P37",.F.))
						oResponse["status" ] := 500
						oResponse["mensagem" ] := "Company/Branch not found!"
						P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
						P37->P37_STATUS := "3"
						P37->P37_TIMERP:= TIME()
						P37->(MsUnLock())
					EndIF
				Else
					P37->(RecLock("P37",.F.))
					oResponse["status" ] := 500
					oResponse["mensagem" ] := "Filial is Empty"
					P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
					P37->P37_STATUS := "3"
					P37->P37_TIMERP:= TIME()
					P37->(MsUnLock())

				EndIF
			Next aHeader
		Else
			P37->(RecLock("P37",.F.))
			oResponse["status" ] := 500
			oResponse["mensagem" ] := "Header is not Array"
			P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
			P37->P37_STATUS := "3"
			P37->P37_TIMERP:= TIME()
			P37->(MsUnLock())
		EndIF
	Else
		P37->(RecLock("P37",.F.))
		oResponse["status" ] := 500
		oResponse["mensagem" ] := "Body is Empty"
		P37->P37_BODYRP := EncodeUtf8( oResponse:toJson() )
		P37->P37_STATUS := "3"
		P37->P37_TIMERP:= TIME()
		P37->(MsUnLock())
	EndIF
	FreeObj(oResponse)
Return
/*/{Protheus.doc} SaveCt2
	Salva os lancamentos Contabeis ""
	@type   Function
	<AUTHOR> Menabue Lima
	@since 29/08/2022
	@version 12.1.33
 
/*/
Static Function SaveCt2(oCabec,aLcto)
	Local nI
	Local aArea         := GetArea()
	Local aCab          := {}
	Local aItens        := {}
	Local dDataLanc     := ctod("")
	Local cError		:= ""
	Local cDebito 		:= ""
	Local cCCD			:= ""
	Local cItemD		:= ""
	Local cClVlDb		:= ""
	Local cCredit		:= ""
	Local cCCC			:= ""
	Local cItemC		:= ""
	Local cClVlCr		:= ""
	Local cDC			:= ""
	Local cOrigem		:= ""
	Local cHist 		:= ""
	Local nValor 		:= 0
	Local cLinha		:= "   "
	Local lNextLin      := GetMv("TI_CTB69LN" , .F., .F.) //Incrementa linhas no documento existente?
	Local oError
	Default cEmp 		:= cEmpAnt
	Default cFil 		:= cFilAnt
	Private lMsErroAuto := .F.
	Private lMsHelpAuto := .T.
	Private CTF_LOCK    := 0
	Private lSubLote    := .T.
	TRYEXCEPTION
	//TCTBA069SetFunName("CTBA102")
	SetFunName("TCTBA069")
	lMsErroAuto := .F.
	lMsHelpAuto := .T.
	CTF_LOCK    := 0
	lSubLote    := .T.
	dDataLanc:=StoD(oCabec["data"])

	aCab := {}
	if  Empty(dDataLanc)
		dDataLanc:= ddatabase
	EndIF

	aAdd(aCab, {'DDATALANC' ,dDataLanc					,NIL} )
	aAdd(aCab, {'CLOTE' 	,SubStr(oCabec["lote"],1,6) ,NIL} )
	aAdd(aCab, {'CSUBLOTE'  ,oCabec["sublote"]  		,NIL} )
	If(!Empty(Alltrim(oCabec["documento"])))
		aAdd(aCab, {'CDOC'  , oCabec["documento"]  ,NIL} )
	EndIF
	aAdd(aCab, {'CPADRAO' 	,'' 				,NIL} )
	aAdd(aCab, {'NTOTINF' 	,0 					,NIL} )
	aAdd(aCab, {'NTOTINFLOT',0 					,NIL} )
	cOrigem	:= oCabec["origem"]
	nTot:=Len(aLcto)
	For nI:= 1 to Len(aLcto)
		//cFilAnt	:= aLcto[nI]["CT2_FILIAL"]
		P37->(RecLock("P37",.F.))
		P37->P37_BODYRP := '{ "mensagem":"Lendo Linhas '+cValToChar(nI)+' de '+cValToChar(nTot)+'  "}'
		P37->(MsUnLock())
		cHist		:= aLcto[nI]["HIST"]
		cMoeda		:= aLcto[nI]["MOEDLC"]
		nValor		:= Val( strtran(aLcto[nI]["VALOR"],",","."))
		cDC			:= aLcto[nI]["DC"]
		cDebito 	:= aLcto[nI]["DEBITO"]
		cCCD		:= aLcto[nI]["CCD"]
		cItemD		:= aLcto[nI]["ITEMD"]
		cClVlDb		:= aLcto[nI]["CLVLDB"]
		cCredit		:= aLcto[nI]["CREDIT"]
		cCCC		:= aLcto[nI]["CCC"]
		cItemC		:= aLcto[nI]["ITEMC"]
		cClVlCr		:= aLcto[nI]["CLVLCR"]
		If(Empty(Alltrim(cLinha)) .And. lNextLin)
			cLinha:= fNextLin(cOrigem,'TCTBA069',aCab)
		Else
			cLinha:= Soma1(cLinha)
		EndIF
		aAdd(aItens,  { ;
			{'CT2_FILIAL' 	,cFilAnt 					, NIL},;
			{'CT2_LINHA' 	,cLinha					    , NIL},;
			{'CT2_MOEDLC'	,cMoeda					 	, NIL},;
			{'CT2_DC' 		,cDC				 		, NIL},;
			{'CT2_DEBITO' 	,cDebito				 	, NIL},;
			{'CT2_CREDIT' 	,cCredit				 	, NIL},;
			{'CT2_CCD' 		,cCCD				 		, NIL},;
			{'CT2_CCC' 		,cCCC				 		, NIL},;
			{'CT2_ITEMD' 	,cItemD						, NIL},;
			{'CT2_ITEMC' 	,cItemC					 	, NIL},;
			{'CT2_CLVLDB' 	,cClVlDb 					, NIL},;
			{'CT2_CLVLCR' 	,cClVlCr 					, NIL},;
			{'CT2_VALOR' 	,nValor						, NIL},;
			{'CT2_ORIGEM'   ,cOrigem				 	, NIL},;
			{'CT2_HIST' 	,cHist						, NIL},;
			{'CT2_EMPORI' 	,cEmpAnt 					, NIL},;
			{'CT2_FILORI' 	,cFilAnt 					, NIL}})
	Next nI
	P37->(RecLock("P37",.F.))
	P37->P37_BODYRP := '{ "mensagem":"Contabilizando  "}'
	P37->(MsUnLock())

	MSExecAuto({|x, y,z| CTBA102(x,y,z)}, aCab ,aItens, 3,.T.)

	If !lMsErroAuto
		// UPD para trocar o CT2_MANUAL PARA 2
		cQry := " UPDATE "+ RetSQLName( 'CT2' ) +" CT2 "
		cQry += " SET CT2_MANUAL = '2' , CT2_ROTINA ='TCTBA069'"
		cQry += " WHERE CT2.CT2_FILIAL = '"+cFilAnt+"' "
		cQry += " AND CT2.CT2_ORIGEM = '"+cOrigem+"' "
		cQry += " AND CT2.CT2_MANUAL = '1' "
		cQry += " AND CT2.D_E_L_E_T_ = '"+Space(1)+"' "

		If TcSQLExec( cQry ) == 0
			TcSQLExec( 'COMMIT' )
		Else
			TcSQLExec( 'ROLLBACK' )
		EndIf
	Else // EM ESTADO DE JOB
		cTime:= dTos(Date()) + strtran(IncTime(time() ,3,0,0 ),":")
		cError := MostraErro("/system/"+cOrigem+"/", "TCTBA069_"+cTime+".log")
		cError := SubStr(cError,1,3000)
	EndIf
	CATCHEXCEPTION USING oError
	cMsgFault:=""
	IF ( ValType( oError ) == "O" )
		cMsgFault := IF( !Empty( oError:Description )	, oError:Description	, "" )
		cMsgFault += IF( !Empty( oError:ErrorStack )	, oError:ErrorStack 	, "" )

	EndIF
	cError:='{ "mensagem":"Error '+cMsgFault+'"}'
	IF InTransact()
		DisarmTransaction()
	EndIF
	ENDEXCEPTION


	RestArea(aArea)

Return cError


/*/{Protheus.doc} SearchFil
	Busca a filial do RM no cadastro de DEPARA filial ZX5
	@type   Function
	<AUTHOR> Menabue Lima
	@since 22/03/2023
	@version 12.1.33
/*/
Static Function SearchFil(cChave2)
	Local cFil := " "
	LOcal cQry := ''
	Local cAliasZX5 := GetNextAlias()


	cQry := "SELECT * FROM "+RETSQLNAME("ZX5")+" where D_E_L_E_T_= ' '  "
	cQry += " AND ZX5_CHAVE2 = '"+cChave2+"'"
	cQry += " AND ZX5_TABELA = 'CTBA47' "
	cQry += " AND ZX5_CHAVE = 'CTBA47'
	cQry := ChangeQuery(cQry)
	//ZX5_CHAVE2 -->'77-1'
	//ZX5_DESCRI -->'00001000100'
	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAliasZX5,.T.,.T.)

	IF !(cAliasZX5)->(EOF())
		cFil:=(cAliasZX5)->ZX5_DESCRI
	ENDIF
	(cAliasZX5)->(DBCLOSEAREA())


Return cFil

 /*/{Protheus.doc} fNextLin
	Funcao para retornar a proxima Linha do documento
	@type   Function
	<AUTHOR> Menabue Lima
	@since 15/05/2023
	@version 12.1.33
 /*/

Static Function fNextLin(cOrigem,cRot,aCab)
	Local cLinDoc := "001"
	Local cQuery  := ""
	Local cAli    := GetnextAlias()



	cQuery  := "  Select max(CT2_LINHA) ULTLIN "
	cQuery  += "    from "+RetFullName("CT2")+" CT2 "
	cQuery  += "   Where CT2_FILIAL = '" + cFilAnt + "' "
	cQuery  += "     And CT2_DATA   = '" + dTos(aCab[1][2])+ "' "
	cQuery  += "     And CT2_LOTE   = '" + aCab[2][2] + "' "
	cQuery  += "     And CT2_SBLOTE = '" + aCab[3][2] + "' "
	If(Len(aCab)>3)
		cQuery  += " And CT2_DOC	= '" + aCab[4][2] + "' "
	EndIF
	cQuery  += "     And CT2_ORIGEM = '" + cOrigem + "'"
	cQuery  += "     And CT2_ROTINA = '" + cRot + "'"
	cQuery  += "     And CT2_MANUAL = '2' "
	cQuery  += "     And D_E_L_E_T_ <> '*' "

	If Select(cAli) > 0
		cAli->(DbCloseArea())
	EndIf

	cQuery := ChangeQuery(cQuery)
	TcQuery cQuery ALIAS cAli NEW

	If  (cAli)->(!(BOF().and.EOF()))
		cLinDoc :=  Soma1((cAli)->ULTLIN)
	EndIf

	(cAli)->(DbCloseArea())

Return(cLinDoc)

Static Function ChkErrP(oErroArq)

	If oErroArq:GenCode > 0
		__cErroPCt := '(' + Alltrim( Str( oErroArq:GenCode ) ) + ') : ' + AllTrim( oErroArq:Description ) + CRLF
	EndIf

	Break
Return
