#INCLUDE 'TOTVS.CH'
#INCLUDE 'ESPC010.CH'

/*/{Protheus.doc} ESPC010

	Description

	<AUTHOR>
	@example Example
	@param   cEntrada
	@return  cRetorno
	@table   Tables
	@since   25-08-2020
/*/

User Function ESPC010(cEntrada)
	
	Local cDirInic   := GetMV("FS_UPDSYS", , "")
	Local cMascara   := "*.*"
	Local cTitulo    := STR0001
	Local cRetorno   := ""
	
	Local nMascpad   := 1
	Local nOpcoes	 := 0
	
	Local lSalvar    := .F.
	Local lArvore    := .T.		

	Default cEntrada := ""

	nOpcoes	:= ( GETF_RETDIRECTORY, GETF_NETWORKDRIVE, GETF_LOCALHARD )
	
	cRetorno := cGetFile(cMascara, cTitulo, nMascpad, cDirInic,lSalvar, nOpcoes, lArvore)
	
Return cRetorno
