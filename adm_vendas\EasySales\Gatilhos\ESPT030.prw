
/*/{Protheus.doc} ESPT030
    Gatilho usado na rotina  ( antigo gatilho U_GATPVA())
    <AUTHOR>
    @example Example
    @param   [Parameter_Name],Parameter_type,Parameter_Description
    @return  Return
    @table   Tables
    @since   25-08-2020
/*/
User Function ESPT030()
    Local lRet      := .T.
    Local oModel    := FWModelActive()
    Local cCodPais  := oModel:GetValue('PVBMASTER','PVB_PAIS')
    Local cCodPrg   := oModel:GetValue('PVCDETAIL','PVC_CODPRG')

    Local oView     := FWViewActive()
    Local oMdlx     := oModel:GetModel('PVADETAIL')

    PVA->(dbSetOrder(1))
    if PVA->( dbSeek( FWxFilial("PVA") + cCodPais + cCodPrg ) )
        While PVA->( ! Eof() ) .And. PVA->PVA_FILIAL = FWxFilial("PVA") ;
            .AND. PVA->PVA_PAIS == cCodPais .AND. PVA->PVA_CODPRG == cCodPrg
                oMdlx:AddLine()
                oMdlx:GoLine(oMdlx:Length())
                oMdlx:LoadValue('PVA_PAIS',cCodPais)
                oMdlx:LoadValue('PVA_CODPRG',cCodPrg)
                oMdlx:LoadValue('PVA_COMBO',PVA->PVA_COMBO)
                oMdlx:LoadValue('PVA_DSCCMB',PVA->PVA_DSCCMB)
                oMdlx:LoadValue('PVA_MODULO',PVA->PVA_MODULO)
                oView:Refresh()
                PVA->( dbSkip())
        End
    EndIf

Return lRet
