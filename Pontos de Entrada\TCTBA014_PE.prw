#Include 'Protheus.ch'

User Function MCTBA014()
Local aPEctba14	:= PARAMIXB
Local lRet		:= .T.
Local nX		:= 0
Local oModPIL	:= Nil
Local aFieldPL9	:= {}
Local nOperPIL	:= aPEctba14[1]:GETOPERATION()


If aPEctba14[2] == "MODELCOMMITNTTS"
	If nOperPIL == 3 .OR. nOperPIL == 4 .OR. nOperPIL == 5
		oModPIL    := aPEctba14[1]:GetModel("PILMASTER")
	
		AADD(aFieldPL9,{"PL9_FILIAL",oModPIL:GetValue("PIL_FILIAL")})
		AADD(aFieldPL9,{"PL9_GRPPRO",oModPIL:GetValue("PIL_GRPPRO")})
		AADD(aFieldPL9,{"PL9_CCUSTO",oModPIL:GetValue("PIL_CCUSTO")})
		AADD(aFieldPL9,{"PL9_ITEMCT",oModPIL:GetValue("PIL_ITEMCT")})
		AADD(aFieldPL9,{"PL9_CLASSE",oModPIL:GetValue("PIL_CLASSE")})
		AADD(aFieldPL9,{"PL9_ADKCOD",oModPIL:GetValue("PIL_ADKCOD")})
		AADD(aFieldPL9,{"PL9_DTINIA",oModPIL:GetValue("PIL_DTINIA")})
		AADD(aFieldPL9,{"PL9_DTFIMA",oModPIL:GetValue("PIL_DTFIMA")})
		U_BI013A03(aFieldPL9,nOperPIL)
	EndIf
EndIf

Return lRet
