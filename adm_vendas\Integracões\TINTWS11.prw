#INCLUDE "TOTVS.CH"
#INCLUDE "RESTFUL.CH"

WSRESTFUL TINTWS11 DESCRIPTION "Ws de controle de envio de status para parceiro" FORMAT "application/json"
 
    WSDATA account
    WSDATA integ
    WSDATA idregister
    WSMETHOD PUT DESCRIPTION "Ws de controle de envio de status para parceiro" WSSYNTAX "/TINTWS11"
 
END WSRESTFUL

WSMETHOD PUT WSRECEIVE RECEIVE WSSERVICE TINTWS11

    Local cJSON      := Self:GetContent() // –> Pega a string do JSON
    Local cAccount   := Self:account // –> Pega o parâmetro recebido pela URl
    Local nProcess   := VAL(Self:idregister)
    Local cInteg     := Self:integ
    Local oRequest   := Nil
    Local lRet       := .T.
    Local cErro      := ""
    Local aArea      := GetArea()

    ::SetContentType("application/json")

    // Deserializa JSON
    IF !fWJsonDeserialize(ALLTRIM(cJson), @oRequest)
        SetRestFault( 400 , "TINTWS11 | ERRO | Ocorreu erro no processamento do Json")
        lRet := .F.
    ENDIF
    If lRet
        aRepar := Resppar(cJson,cAccount,nProcess,cInteg)    	 
        If aRepar[1]
            ::SetResponse(aRepar[2])
            lRet:=.T.
        Else
            SetRestFault( 400 , aRepar[2])
            lRet := .F.
        ENDIF
    ENDIF
    RestArea(aArea)
Return (lRet)

/*/{Protheus.doc} Resppar
    (long_description)
    @type  Static Function
    <AUTHOR>
    @since 28/11/2019
    @version 1.0
    @param ORequest, param_type, param_descr
    @return return_var, return_type, return_description
    @example
    (examples)
    @see (links_or_references)
    /*/
Static Function Resppar(cJson,cAccount,nProcess,cInteg)

    Local aArea   := ZZG->(GetArea())
    LOCAL aAlias  := GETMV( "TI_ZZGTAB", .f., {'SF2','SE1','SF3'} )
    LOCAL aRet    := {}
    
    DbselectArea("ZZG")
    ZZG->(DBSETORDER(3))
    IF DBSEEK(xFilial("ZZG")+PADR(cInteg,TAMSX3("ZZG_CINTEG")[1])+PADR(aAlias[nProcess],TAMSX3("ZZG_ALIAS")[1])+cAccount)
        Reclock("ZZG",.F.)
        ZZG->ZZG_RETGRV := DTOS(Date()) + ' ' + TIME() + CRLF + cJson
        ZZG->(MSUNLOCK())
        ZZG->(DBSKIP())
        cRetJson :=  '{ "RESPONSE": {"STATUS":"SUCCESSO", "MESSAGE": "Atualização efetuada com sucesso!"}}'
        lRet:= .T. 
    Else 
        cRetJson :=  'Contrato não localizado!'
        lRet := .F.
        ZZG->(DBSKIP())
    EndIf
    
    AADD( aRet, lRet )
    AADD( aRet, cRetJson )

Return aRet