#include 'totvs.ch'
#include 'FWMVCDef.ch'
#include 'ESPA103.ch'

//---------------------------------------------------------------------------------------
/*/{Protheus.doc} ESPA103
Cadastro de itens da proposta comercial.

<AUTHOR>
@since     20/09/2019
@version   P12
/*/
//---------------------------------------------------------------------------------------
User Function ESPA103()

Local oBrowse := BrowseDef()

// Ativa browser.
oBrowse:Activate()

Return

/*/{Protheus.doc} BrowseDef
Defini��es do browse.

<AUTHOR>
@since     20/09/2019
@version   P12
/*/
Static Function BrowseDef()

Local oBrowse := FWMBrowse():New()
oBrowse:SetAlias("PER")

Return oBrowse

//---------------------------------------------------------------------------------------
/*/{Protheus.doc} MenuDef
Defini��o do aRotina

<AUTHOR> Raposo
@since     20/09/2019
@version   P12
/*/
//---------------------------------------------------------------------------------------
Static Function MenuDef()
Return FWMVCMenu('ESPA103')  // Retorna as op��es padr�es de menu.

//-------------------------------------------------------------------
/*/{Protheus.doc} ModelDef
Defini��o do modelo de Dados

<AUTHOR> Raposo
@since     20/09/2019
@version   P12
/*/
//-------------------------------------------------------------------
Static Function ModelDef()

Local oModel   as object
//Local bCommit  := {|oModel| ES103Grv(oModel)}

oModel := MPFormModel():New('PERModel', /*bPreValidacao*/,  /*bPosValidacao*/, /*bCommit*/, /*bCancel*/ )
oModel:SetDescription(FwX2Nome("PER"))

oModel:AddFields('PERMASTER',, FWFormStruct(1, "PER"))
oModel:GetModel('PERMASTER'):SetDescription(FwX2Nome("PER"))
oModel:SetPrimaryKey({"PER_FILIAL", "PER_CODIGO"})

Return oModel

//-------------------------------------------------------------------
/*/{Protheus.doc} ES103Grv
Bloco de persist�ncia (grava��o de dados) do modelo.

<AUTHOR> Raposo
@since     26/09/2019
@version   P12
/*/
//-------------------------------------------------------------------
Static Function ES103Grv(oModel)

Local lRet       as logical
Local aRet       as array

Local oRegJson   as object
Local oJson      as object
Local aStruct    as array
Local cCampo     as character

Local oConnApi   as object
Local nX         as numeric

// Efetua a grava��o do contrato.
lRet := FWFormCommit(oModel)

// Se gravou com sucesso, envia o registro ao Protheus12.
If lRet
	oConnApi := ConnApiManager():New()
	If (oModel:GetOperation() == MODEL_OPERATION_DELETE)
		aRet := oConnApi:DeleteGen("modelstruct", "chvpesq=" + oModel:GetValue("PERMASTER", "PER_CODIGO") + "&struct=PER")
	Else
		oJson := JsonObject():new()
		oJson['chvpesq'] := oModel:GetValue("PERMASTER", "PER_CODIGO")
		oJson['struct']  := "PER"
		oJson['deletar'] := .T.
		oJson['PER']     :=  {}

		oRegJson := JsonObject():new()
		aStruct := PER->(dbStruct())
		For nX := 1 to len(aStruct)
			cCampo := aStruct[nX, 1]
			If aStruct[nX, 2] == "D"
				oRegJson[cCampo] := DtoS(PER->(&cCampo))
			ElseIf aStruct[nX, 2] == "C" .or. aStruct[nX, 2] == "M"
				oRegJson[cCampo] := Alltrim(PER->(&cCampo))
			Else
				oRegJson[cCampo] := PER->(&cCampo)
			Endif
		Next nX
		aAdd(oJson['PER'], oRegJson)

		aRet := oConnApi:PostGen("modelstruct", oJson:toJson())
	Endif

	If aRet[1]
		ApMsgInfo(STR0001) // 'Sincronismo conclu�do com sucesso!'
	Else
		ApMsgInfo(STR0002 + CRLF + aRet[2]) // 'Ocorreu um erro no sincronismo com #TOTVS12!'
		lRet := .F.
	Endif
Endif

Return lRet

//-------------------------------------------------------------------
/*/{Protheus.doc} ViewDef
Defini��o do interface

<AUTHOR> Raposo
@since     20/09/2019
@version   P12
/*/
//-------------------------------------------------------------------
Static Function ViewDef()

Local oView    := FWFormView():New()
Local oModel   := ModelDef()

oView:SetModel(oModel)
oView:bCloseOnOk := {|| .T.}
oView:ShowUpdateMsg(.F.)

oView:AddField('VIEW_PER', FWFormStruct(2, "PER"), 'PERMASTER')

// Cria box na tela para os objetos.
oView:CreateHorizontalBox('SUPERIOR', 100)

// Relaciona a Field com o box da tela.
oView:SetOwnerView('VIEW_PER', 'SUPERIOR')

Return oView
