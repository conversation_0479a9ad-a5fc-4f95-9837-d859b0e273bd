<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project name="Protheus Branch Release" default="sonar" basedir="." xmlns:sonar="antlib:org.sonar.ant">
    
    <!-- load environment variables-->
	<property environment="env" />
    
    <!-- Define the SonarQube global properties (the most usual way is to pass these properties via the command line) -->
    <property name="sonar.host.url" value="http://************:9000" />
    
    <!-- Define the SonarQube project properties -->
    <property name="sonar.login" value="${env.sonar_agent_user}" />
    <property name="sonar.password" value="${env.sonar_agent_password}" />
    <property name="sonar.projectKey" value="protheus_release" />
    <property name="sonar.projectName" value="Protheus_branch_release" />
    <property name="sonar.projectVersion" value="1.0" />
    <property name="sonar.projectBaseDir" value="${env.BUILD_SOURCESDIRECTORY}" />
    <!--<property name="sonar.sources" value="adm_vendas\CRM,Pontos de Entrada" />-->
    <property name="sonar.sourceEncoding" value="UTF-8" />
    <property name="sonar.advpl.file.chs_paths" value="${env.BUILD_SOURCESDIRECTORY}\includes" />
    <property name="sonar.skipPackageDesign" value="true" />
    <property name="sonar.skipDesign" value="true" />
    <property name="sonar.exclusions" value="**/RUP*,**/RUP*.prw,**/RUP*.PRW,**/*.java,**/*.js,**/*.jav*,**/*.jsx,**/*.py" />
    <property name="sonar.test.exclusions" value="**/RUP*,**/RUP*.prw,**/RUP*.PRW" />

    <!-- Define SonarScanner for Ant Target -->
    <target name="sonar">
        <taskdef uri="antlib:org.sonar.ant" resource="org/sonar/ant/antlib.xml">
            <!-- Update the following line, or put the "sonarqube-ant-task-*.jar" file in your "$HOME/.ant/lib" folder -->
            <!--<classpath path="/home/<USER>/ant/lib/sonarqube-ant-task-*.jar" />-->
        </taskdef>
    
        <!-- Execute SonarScanner for Ant Analysis -->
        <sonar:sonar />
    </target>
</project>
