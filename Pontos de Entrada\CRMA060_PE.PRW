#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'FWMVCDEF.CH'

/*/{Protheus.doc} CRMA060

Ponto de entrada da manutencao de contatos x entidade
Integracao PSA

<AUTHOR>
@since  22/12/2017
@version 12.1.5
/*/

User Function CRMA060()

Local aParam    := PARAMIXB
Local aArea     := GetArea()
Local aRetPSA	:= {}

Local xRet      := .T.
Local lHbIntPSA	:= getMv('TI_#HCAPSA',,.F.) //GetMv('TI_#HABPSA',,.F.)

Local nAC8      := 0
Local nLenAC8   := 0

Local cCdCliente:= ""

Local oObj      := NIL
Local cIdPonto  := Nil
Local cIdModel  := Nil
Local oGeneric	:= Nil
Local oModel    := Nil
Local oModelAC8M:= Nil

Local lIntCPSA	:= getMv('TI_INTCPSA',,.F.) // integra contato do protheus com o PSA


If aParam <> NIL

	oObj       	:= aParam[1]
	cIdPonto   	:= aParam[2]
	cIdModel   	:= aParam[3]

	//-----------------------------------------------------------------------
	//Para projetos PSA, integra contatos de clientes
	//-----------------------------------------------------------------------
	If oObj:IsActive() .AND. cIdPonto == 'FORMPOS' .And. cIdModel == 'AC8CONTDET'  //MODELCOMMITNTTS
		If lHbIntPSA .And. lIntCPSA
            oModel	:= FWModelActive()
            oModelAC8M := oModel:GetModel("AC8MASTER")
            If oModelAC8M:GetValue("AC8_ENTIDA") == "SA1"
                cCdCliente := Alltrim(oModelAC8M:GetValue("AC8_CODENT"))

                nLenAC8	:= oObj:Length()
                For nAC8:=1 to nLenAC8
                    oObj:GoLine(nAC8)
                    If oObj:IsInserted()
                        If FindFunction("U_PSAContact")
                            U_PSAContact(cCdCliente,oObj:GetValue('AC8_CODCON'),.F.)
                        EndIf
                    EndIf
                Next nAC8
            EndIf
		EndIf
	EndIf
	
EndIf			

RestArea(aArea)
Return xRet