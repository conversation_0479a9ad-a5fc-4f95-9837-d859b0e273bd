#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'FWMVCDEF.CH'
#INCLUDE 'ESPA070.CH'

User Function ESPA070()
	Local oBrowse := FWMBrowse():New()
	
	oBrowse:Set<PERSON><PERSON><PERSON>('PVR')
	oBrowse:SetDescription(STR0001) 
	oBrowse:Activate()
Return

Static Function MenuDef()
	Local aRotina := {}
	
	ADD OPTION aRotina TITLE OemToAnsi("Visualizar")	ACTION 'VIEWDEF.ESPA070'   OPERATION 2 ACCESS 0
	ADD OPTION aRotina TITLE OemToAnsi("Incluir")	   ACTION 'VIEWDEF.ESPA070'   OPERATION 3 ACCESS 0
	ADD OPTION aRotina TITLE OemToAnsi("Alterar")	   ACTION 'VIEWDEF.ESPA070'   OPERATION 4 ACCESS 0
	ADD OPTION aRotina TITLE OemToAnsi("Excluir")	   ACTION 'VIEWDEF.ESPA070'   OPERATION 5 ACCESS 0
Return(aRotina)

Static Function ModelDef()
	Local oStructPVR	:= FWFormStruct(1, 'PVR')
	Local oStructPVS	:= FWFormStruct(1, 'PVS')
	Local bCommit	   := { |oModel| ES070Grv(oModel) }
	Local oModel		:= MPFormModel():New('ESPA070M', /*bPreValidacao*/,  /*bPosValidacao*/,  bCommit, /*bCancel*/ )

	
	oModel:AddFields('PVRMASTER', , oStructPVR)
	oModel:AddGrid('PVSDETAIL', 'PVRMASTER', oStructPVS)
	
	oModel:SetRelation('PVSDETAIL', {{'PVS_FILIAL', 'xFilial("PVS")'}, {'PVS_PAIS', 'PVR_PAIS'}, {'PVS_PROPUE', 'PVR_PROPUE'}, {'PVS_ORCAME', 'PVR_ORCAME'}}, PVS->(IndexKey(1)))
	
	oModel:GetModel('PVRMASTER'):SetDescription(STR0002)
	oModel:GetModel('PVSDETAIL'):SetDescription(STR0003)
	
	oModel:GetModel('PVSDETAIL'):SetUniqueLine({'PVS_VERSAO', 'PVS_TAREFA', 'PVS_NIVEL', 'PVS_EDTPAI', 'PVS_CODPRG'})
Return (oModel)

Static Function ViewDef()
	Local oStructPVR	:= FWFormStruct(2, 'PVR')
	Local oStructPVS	:= FWFormStruct(2, 'PVS')  
	Local oModel		:= FWLoadModel('ESPA070')
	Local oView		 := FWFormView():New()
	
	oStructPVS:RemoveField('PVS_PAIS')
	oStructPVS:RemoveField('PVS_PROPUE')
	oStructPVS:RemoveField('PVS_ORCAME')
	oStructPVS:SetProperty('PVS_ITEM', MVC_VIEW_ORDEM, '00')
	
	oView:SetModel(oModel)
	
	oView:AddField('PVRVIEW', oStructPVR, 'PVRMASTER')
	oView:AddGrid('PVSVIEW', oStructPVS, 'PVSDETAIL')
	
	oView:CreateHorizontalBox('PVRBOX', 35)
	oView:CreateHorizontalBox('PVSBOX', 65)
	
	oView:SetOwnerView('PVRVIEW', 'PVRBOX')
	oView:SetOwnerView('PVSVIEW', 'PVSBOX')
	
	oView:EnableTitleView('PVRVIEW',STR0002) 
	oView:EnableTitleView('PVSVIEW',STR0003) 

	oView:AddIncrementField('PVSVIEW','PVS_ITEM')
Return (oView)


/*/{Protheus.doc} ES070Grv
@description Grava os dados da model'
<AUTHOR>
@since 24/02/2017
@version 1.0
@param oModel, objeto, Model
@return lRet, Logico informando se gravou com sucesso
/*/

Static Function ES070Grv(oModelAux)
Local lRet		:= .T.
Local oModelPVR	:= oModelAux:GetModel("PVRMASTER")
Local cPropue	:= oModelPVR:GetValue("PVR_PROPUE") 
Local cOrcame	:= oModelPVR:GetValue("PVR_ORCAME")
Local aRet		:= {}
Local oMldJson

//��������������������������������������������������������������������������Ŀ
//� Realiza a gravacao padrao do Modelo.									�
//����������������������������������������������������������������������������
If !FWFormCommit(oModelAux)
	DisarmTransaction()
	Return(.F.)
EndIf


//��������������������������������������������������������������������������Ŀ
//� Realiza o sincronismo com #TOTVS12.										 �
//����������������������������������������������������������������������������
If oModelAux:GetOperation() == MODEL_OPERATION_INSERT .OR. oModelAux:GetOperation() == MODEL_OPERATION_UPDATE
	
	aRet :=  AssociaAF2(cPropue, cOrcame)
	
	If aRet[1] .AND. Len(aRet[2]) > 0
		oMldJson := JsonObject():new()

		oMldJson['chvpesq']	:= cOrcame
		oMldJson['deletar']	:= .F.
		oMldJson['struct']	:= "AF2"
		oMldJson['AF2']		:=  aRet[2]

		//CHAMA ROTINA DE Exporta��o
		aRetSinc := SincroDados( oMldJson:toJSON() )

		FreeObj(oMldJson)
	EndIf
EndIf

Return lRet

/*/{Protheus.doc} AssociaAF2
@description Grava os dados da model'
<AUTHOR>
@since 24/02/2017
@version 1.0
@param oModel, objeto, Model
@return aRet, Array,  {.T. ou .F., Array com dados do AF2}
/*/

Static Function AssociaAF2(cPropue, cOrcame)
Local aAreaAF2		:= AF2->( GetArea() )
Local lRet			:= .F.
Local aRet			:= {}
Local cView			:= GetNextAlias() 
Local nRecnoAF2		:= 0
Local cChvQuiz		:= ""
Local aAnwser		:= {}
Local aQuiz			:= {}
Local aRegAF2		:= {}
Local aStrGen		:= AF2->( DbStruct() )
Local oTask
Local oQuiz
Local oAnwser
Local oAF2Json
Local nY

//��������������������������������������������������������������������������Ŀ
//� Realiza o sincronismo com #TOTVS12.		 								 �
//����������������������������������������������������������������������������
DbSelectArea("AF2") 

BeginSql Alias cView
SELECT PVS.PVS_ORCAME
		,PVS.PVS_TAREFA
		,AF2.R_E_C_N_O_ RECNOAF2
		,PVA.PVA_CODPRG
		,PVA.PVA_DESPRG
		,PVA.PVA_TIPRES
		,CAST(PVA.PVA_COMBO AS VARCHAR(500)) NUMRESP
		,PVA.PVA_DSCCMB AS RESPOSTA
		,PVA.PVA_ESTIMA
		,PVA.PVA_BLOQUE
FROM %Table:PVS% PVS
INNER JOIN AF2000 AF2
	ON AF2_FILIAL = %xFilial:PVS%
		AND AF2.AF2_ORCAME = PVS.PVS_ORCAME
		AND AF2.AF2_TAREFA = PVS.PVS_TAREFA 
		AND AF2.D_E_L_E_T_ = ' '
INNER JOIN %Table:PVA% PVA 
	ON PVA.PVA_FILIAL = %xFilial:PVA%
		AND PVA.PVA_PAIS = PVS.PVS_PAIS
		AND PVA.PVA_CODPRG = PVS.PVS_CODPRG
		AND PVA.%NotDel%
WHERE PVS.PVS_FILIAL = %xFilial:PVS%
		AND PVS.PVS_PROPUE = %Exp:cPropue%
		AND PVS.PVS_ORCAME = %Exp:cOrcame%
		AND PVS.%NotDel%
ORDER BY PVS_ORCAME
		,PVS_TAREFA
		,PVS_CODPRG
		,NUMRESP
EndSql

dbselectArea(cView)
(cView)->(dbGoTop())
While (cView)->(!Eof())
	oTask     := JsonObject():New()
	nRecnoAF2 := (cView)->RECNOAF2
	aQuiz     := {}
	
	While (cView)->(!Eof()) .AND. (cView)->RECNOAF2 == nRecnoAF2
		oQuiz := JsonObject():New()

		oQuiz['codequiz'] := AllTrim( (cView)->PVA_CODPRG )
		oQuiz['desc']     := AllTrim( (cView)->PVA_DESPRG )
		
		IF AllTrim((cView)->PVA_TIPRES) == '1'
			oQuiz['type'] := 'radio'
		ELSEIF AllTrim((cView)->PVA_TIPRES) == '2'
			oQuiz['type'] := 'combo'
		ELSEIF AllTrim((cView)->PVA_TIPRES) == '5'
			oQuiz['type'] := 'multiselect'
		ELSEIF AllTrim((cView)->PVA_TIPRES) == '3'
			oQuiz['type'] := 'input'
		ELSE	
			oQuiz['type'] := 'nothing'
		ENDIF

		cChvQuiz := (cView)->PVA_CODPRG
		aAnwser  := {}

		While (cView)->(!Eof()) .AND. (cView)->RECNOAF2 == nRecnoAF2 .AND. (cView)->PVA_CODPRG == cChvQuiz
			oAnwser := JsonObject():New()

			oAnwser['label']		:= Alltrim(RESPOSTA)
			oAnwser['value']		:= Alltrim(NUMRESP)
			oAnwser['qtdresp']		:= (cView)->PVA_ESTIMA
			oAnwser['block']        := IIF( Empty((cView)->PVA_BLOQUE), '0', AllTrim((cView)->PVA_BLOQUE ) )
			
			IF AllTrim((cView)->PVA_TIPRES) $ '1245'
				oAnwser['operation'] := 'soma'
			ELSEIF AllTrim((cView)->PVA_TIPRES) == '3'
				oAnwser['operation'] := 'multiplica'
			ELSE	
				oAnwser['operation'] := 'nothing'
			ENDIF
			
			aAdd(aAnwser, oAnwser)

			FreeObj(oAnwser)

			(cView)->(dbSkip())
		EndDo

		oQuiz['anwsers'] := aAnwser
		
		aAdd(aQuiz, oQuiz)

		FreeObj(oQuiz)
	EndDo

	oTask['quizzes'] := aQuiz

	AF2->( Dbgoto(nRecnoAF2) )
	Reclock("AF2", .F.)
	AF2->AF2_PERGES := oTask:ToJson()
	AF2->( MsUnlock() )

	FreeObj(oTask)				

	//JA MONTA JSON DE SINCRONISMO
	oAF2Json := JsonObject():new()

	For nY := 1 To len( aStrGen )
		If aStrGen[nY][2] == "D"
			oAF2Json[ aStrGen[nY][1] ] := DtoS(AF2->&(aStrGen[nY][1]))
		ElseIf aStrGen[nY][2] == "C" .OR. aStrGen[nY][2] == "M"
			oAF2Json[ aStrGen[nY][1] ] := Alltrim(AF2->&(aStrGen[nY][1]) )
		Else
			oAF2Json[ aStrGen[nY][1] ] := AF2->&(aStrGen[nY][1])
		EndIf
	Next
		
	AADD(aRegAF2, oAF2Json)
	FreeObj( oAF2Json )
	lRet := .T.
End

RestArea(aAreaAF2)

aRet := {lRet, aRegAF2}

Return aRet


/*/{Protheus.doc} SincroDados
	Sincroniza o json com ambiente externo 
	@type  Static Function
	<AUTHOR>
	@since 08/09/2019
	@version version
	@param cBodyModel, caracter, body em formato json do modelo ou parametros para dele��o
	@return lRet, Logico, .T. para exporta��o realizada com sucesso, .F. para erro
	@example
	(examples)
	@see (links_or_references)
	/*/
Static Function SincroDados(cBodyModel, lDelete)
Local aRet 			:= { .T., ""}
Local cMethod		:= "modelstruct"
Local oGenEasy

Default lDelete := .F.

oGenEasy  := ConnApiManager():New()

If lDelete
	aRet := oGenEasy:DeleteGen(cMethod, cBodyModel)
Else
	aRet := oGenEasy:PostGen(cMethod, cBodyModel)
EndIf

IIf( ValType( oGenEasy ) == 'O', ( FreeObj( oGenEasy ), oGenEasy := Nil ), Nil )

Return aRet
