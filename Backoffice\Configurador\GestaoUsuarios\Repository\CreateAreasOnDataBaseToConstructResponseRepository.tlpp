#include    "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Repository.CreateAreasOnDataBaseToConstructResponseRepository


/*/{Protheus.doc} CreateAreasOnDataBaseToConstructResponse
These Class aims to create a set of areas on Database
to possibility to create an response for income system
@type class
@version  1.0
<AUTHOR>
@since 10/29/2024
/*/
Class   CreateAreasOnDataBaseToConstructResponse
    Private Data    aAreaAtu    As  Array
    Private Data    aAreaRd0    As  Array
    Private Data    aAreaSa2    As  Array
    Public  Method  new()   Constructor
    Public  Method  destroy()
EndClass

/*/{Protheus.doc} CreateAreasOnDataBaseToConstructResponse::new
This method aims to create a new object instance of the class
@type method
@version  1.0
<AUTHOR>
@since 10/29/2024
@return object, Return the instantiated object itself
/*/
Method  new()   Class   CreateAreasOnDataBaseToConstructResponse
    ::aAreaAtu  :=  GetArea()
    ::aAreaRd0  :=  RD0->( GetArea() )
    ::aAreaSa2  :=  SA2->( GetArea() )
Return  Self

/*/{Protheus.doc} CreateAreasOnDataBaseToConstructResponse::destroy
Method that apply an RestArea
@type method
@version 1.0 
<AUTHOR>
@since 10/29/2024
@return variant, Return a Nil Value
/*/
Method  destroy()    Class   CreateAreasOnDataBaseToConstructResponse
    RestArea(::aAreaSA2)
    RestArea(::aAreaRD0)
    RestArea(::aAreaAtu)
Return  Nil
