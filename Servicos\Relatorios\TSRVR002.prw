#INCLUDE "TSRVR002.ch"
#Include 'Totvs.ch'



/*/{Protheus.doc} TSRVR002
(Relat�rio de Servi�os - Saldo de Encerrados)
Antigo EPOR050.PRW
<AUTHOR>
@since 29/09/2015
@version 1.0
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
ID SRV0231
/*/

User Function TSRVR002()

SV002A01("TSRVR002") // Cria pergunte do SX1
    

If !Pergunte("TSRVR002",.T.)
	Return
Endif

oProcess := MsNewProcess():New({|| lRet := SV002R02()},STR0001,STR0038,.T.)	//"Saldo de Encerrados"	## "Processando..." 
oProcess:Activate()


Return


/*/{Protheus.doc} SV002R02
(Impress�o do relat�rio)
<AUTHOR>
@since 29/09/2015
@version 1.0
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
Static Function SV002R02(lJob)

Local nTamGPP      := TAMSX3("PE7_CODREC")[1]
Local nTamRd0      := TAMSX3("RD0_NOME")[1]
Local cCodGPP      := GETMV("TI_SRVGPP") // Filtro PE7_CODREC = GPP
Local cCodLDS      := GETMV("TI_SRVLDES") // Filtro PE7_CODREC = LDS Lider Desenvolvimento
Local cAnoMesIni   := "" 
Local cAnoMesFim   := ""
Local aMoedasOri   := U_SRVXRetMoeda(cEmpAnt,cFilAnt,Date())
Local cAliTmp      := GetNextAlias()
Local cTrbSD1      := ""
Local oTempTable   := Nil
Local cFrtMotCFP   := GetMV( "TI_SRV0121" ,, "12345" ) // Frentes com mesmo motivo do CFP
Local lJob         := .F.
Local cMotPDD      := ""
Local EhTeste      := .F.
Local cAliasSld    := GetNextAlias()
Local cQuery       := ""
Local cTimeIni     := Time()
Local cfile        := ""
Local cTabZX5      := Alltrim( GETMV("TI_SRVTPRJ",, "SRV003") )	// C�digo da Tabela gen�rica "SRV003", para encontrar o tipo de projeto.
Local cArqDestino	:= ""

Private cAliTbTmp := GetNextAlias()

Default lJob := .F.

If !lJob
	oProcess:SetRegua1(0)
	oProcess:IncRegua1(STR0038) //"Processando... "
	oProcess:SetRegua2(0)
	oProcess:IncRegua2( STR0042 +"("+ElapTime(cTimeIni,Time())+")") //"Calculando Saldos..."
	cTimeIni := Time()
EndIf

If Val(MV_PAR01+MV_PAR02) = 0 .OR. Empty(CtoD("01/"+MV_PAR01+"/"+MV_PAR02))
	ApMsgStop(STR0035, STR0002) //"Parametro 'M�s De ou Ano De' Incorreto!" ## "Relat�rio Saldo de Encerrados"
	Return
Else
	cAnoMesIni := Left(DtoS(CtoD("01/"+MV_PAR01+"/"+MV_PAR02)),6)
EndIf

If Val(MV_PAR03+MV_PAR04) = 0 .OR. Empty(CtoD("01/"+MV_PAR03+"/"+MV_PAR04))
	ApMsgStop(STR0036, STR0002) //"Parametro 'M�s At� ou Ano At�' Incorreto!" ## "Relat�rio Saldo de Encerrados"
	Return
Else
	cAnoMesFim := Left(DtoS(CtoD("01/"+MV_PAR03+"/"+MV_PAR04)),6)
EndIf

//BUSCA OS MOTIVOS DE PDD
cQuery := " SELECT PE0.PE0_COD "
cQuery += " FROM " + RetSQLName("PE0") + " PE0 "
cQuery += " WHERE PE0_FILIAL = '" + FWxFilial("PE0") + "' "
cQuery += "       AND PE0_TPABON = '8' "
cQuery += "       AND PE0.D_E_L_E_T_ = '' "
cQuery := ChangeQuery(cQuery)

MPSysOpenQuery(cQuery,"TSRVR001PDD")


While !TSRVR001PDD->( Eof() )
	cMotPDD += TSRVR001PDD->PE0_COD+";"
	TSRVR001PDD->( DbSkip() )
End

TSRVR001PDD->( DbCloseArea() )

cMotPDD := FormatIn(cMotPDD, ";")

If MV_PAR15 == 2
	//CRIA TABELA TEMPORARIA NO BANCO COM OS CANCELAMENTOS DE TODAS AS FILIAIS
	oTemptable := FWTemporaryTable():New( cAliTbTmp)
	
	//                     lJob, Obj:TRBTEMP, AliasTRB, cAnoMesIni, cAnoMesFim, aMoedasOri, EhTeste)
	cTrbSD1 := U_FATSRVMES(lJob, @oTempTable,cAliTbTmp, cAnoMesIni, cAnoMesFim, aMoedasOri, EhTeste)
EndIf

If !lJob
	oProcess:IncRegua1(STR0042) //"Calculando Saldos..."	
EndIf

cQuery := "SELECT PE8_MSREF MESREF" + CRLF
cQuery += "       ,PE5_PROJET PROJETO" + CRLF
cQuery += "       ,PE5_DESPRO DESCR_PRJ" + CRLF
cQuery += "       ,PE5_CLIENT CLIENTE" + CRLF
cQuery += "       ,PE5_LOJA  LOJA" + CRLF
cQuery += "       ,A1_NOME   NOMECLI" + CRLF
cQuery += "       ,CASE WHEN AI0_SETPUB = '1' THEN 'SIM' ELSE 'NAO' END PUBLICO" + CRLF
cQuery += "       ,PE5_FILPRJ FILIALPRJ" + CRLF
cQuery += "       ,PE5_UNSRV  UNSRV" + CRLF
cQuery += "       ,PE1.PE1_DESCRI DESC_UNSRV" + CRLF
cQuery += "       ,CASE WHEN PE2.PE2_CCUSTO <> '' THEN PE2.PE2_CCUSTO ELSE PE1.PE1_CCUSTO END CCUST_CFP" + CRLF
cQuery += "       ,CASE WHEN PE2.PE2_CCUSTO <> '' THEN CTT2.CTT_DESC01 ELSE CTT1.CTT_DESC01 END DCCUST_CFP" + CRLF
cQuery += "       ,A1_CODSEG CODSEG" + CRLF
cQuery += "       ,AOV_DESSEG SEGMENTO" + CRLF
cQuery += "       ,AOV.AOV_XITECO ITEM_CTB" + CRLF

   cQuery += " ,(SELECT PE5.PE5_TPPROJ || ' - ' || ZX5A.ZX5_DESCRI" + CRLF
   cQuery += "  FROM " + RetSqlName("ZX5") + " ZX5A " + CRLF 
   cQuery += "  WHERE ZX5A.ZX5_FILIAL = '"+FwxFilial("ZX5")+"' " + CRLF 
   cQuery += "        AND ZX5A.ZX5_TABELA = '"+cTabZX5+"' " + CRLF 
   cQuery += "        AND ZX5A.ZX5_CHAVE = PE5.PE5_TPPROJ" + CRLF 
   cQuery += "        AND ZX5A.D_E_L_E_T_ = ' ') " + CRLF
cQuery += "TIPO_PRJ "

cQuery += "       ,PE5_EMISSA EMISSAO" + CRLF
cQuery += "       ,PE5_DTENCR DTENCERRA" + CRLF
cQuery += "       ,PE5_STATUS STATUS" + CRLF
cQuery += "       ,PE5_CODMOT CODMOTIVO" + CRLF
cQuery += "       ,PE0_DESC DESCR_MOT" + CRLF
cQuery += "       ,PE5_QTDORC QTD_ORCADA" + CRLF
cQuery += "       ,PE5_VLRORC VLR_ORCADO" + CRLF
cQuery += "       ,PE5_LIMRNP LIMRNP" + CRLF
cQuery += "       ,PE5_HRSUSP HRSUPENSA" + CRLF

   cQuery += " ,(SELECT PE7_CODREC || ' - ' || RD01.RD0_NOME " + CRLF
   cQuery += "   FROM " + RetSqlName("PE7") + " PE71" + CRLF
   cQuery += "   INNER JOIN " + RetSqlName("RD0") + " RD01" + CRLF
   cQuery += "       ON RD0_FILIAL = '" + xFilial("RD0") + "'" + CRLF
   cQuery += "          AND RD01.RD0_CODIGO = PE71.PE7_CODREC" + CRLF
   cQuery += "          AND RD01.D_E_L_E_T_ = ' '" + CRLF
   cQuery += "   WHERE PE71.PE7_FILIAL = '" + xFilial("PE7") + "'" + CRLF
   cQuery += "         AND PE71.PE7_PROJET = PE5_PROJET" + CRLF
   cQuery += "         AND PE71.PE7_PAPEL = '" + cCodGPP + "'" + CRLF
   cQuery += "         AND ROWNUM = 1" + CRLF
   cQuery += "         AND PE71.D_E_L_E_T_ = ' ')" + CRLF
cQuery += "       AS GPP" + CRLF

If MV_PAR15 == 1
	cQuery += "       ,PE8_CODIGO COD_FRENTE" + CRLF
	cQuery += "       ,PE8_DESCR DESCR_FRT" + CRLF
	cQuery += "       ,PE8_COORD || ' - ' || RD0.RD0_NOME COORD" + CRLF
	
	cQuery += "       ,PE8_UNSRES UNSRV_RESP" + CRLF
	cQuery += "       ,PE1A.PE1_DESCRI DUNSRVRESP" + CRLF
	
	cQuery += "       ,PE2A.PE2_CCUSTO CUST_RESP" + CRLF
	cQuery += "       ,CTTA.CTT_DESC01 DCUST_RESP" + CRLF

	cQuery += "       ,PE8_QTDHRS HRORCFRT" + CRLF
	cQuery += "       ,((PE5_VLRORC / PE5_QTDORC) * PE8_QTDHRS) VLRORCFRT" + CRLF
Else
	   cQuery += " ,(SELECT SUM(PE8_QTDHRS) " + CRLF
	   cQuery += "   FROM "+RetSqlName("PE8")+" PE8C " + CRLF	
	   cQuery += "   WHERE PE8C.PE8_FILIAL = '"+FwxFilial("PE8")+"' " + CRLF
	   cQuery += "         AND PE8C.PE8_TPFRT <> '5'  " + CRLF
	   cQuery += "         AND PE8C.PE8_PROJET = PE5.PE5_PROJET " + CRLF	
	   cQuery += "         AND PE8C.PE8_MOTIVO = PE5.PE5_CODMOT " + CRLF	
	   cQuery += "         AND PE8C.D_E_L_E_T_ = ' ')" + CRLF
	cQuery += "    AS HRSFRENTES"

	   cQuery += " ,((SELECT SUM(PE8_QTDHRS) " + CRLF
	   cQuery += "    FROM "+RetSqlName("PE8")+" PE8C " + CRLF	
	   cQuery += "    WHERE PE8C.PE8_FILIAL = '"+FwxFilial("PE8")+"' " + CRLF
	   cQuery += "          AND PE8C.PE8_TPFRT <> '5'  " + CRLF
	   cQuery += "          AND PE8C.PE8_PROJET = PE5.PE5_PROJET " + CRLF	
	   cQuery += "          AND PE8C.PE8_MOTIVO = PE5.PE5_CODMOT " + CRLF	
	   cQuery += "          AND PE8C.D_E_L_E_T_ = ' ') * (PE5_VLRORC / PE5_QTDORC) ) VLRORCFRT" + CRLF
EndIf

cQuery += "       ,HRSRECONHE" + CRLF

If MV_PAR15 == 2
	cQuery += "       ,HRFATURADA" + CRLF
	cQuery += "       ,HRCANCELA" + CRLF
	cQuery += "       ,(HRFATURADA - HRCANCELA) HRFATLIQUI" + CRLF
	cQuery += "       ,HRAPONTADA" + CRLF
	cQuery += "       ,HRABONADA" + CRLF
	cQuery += "       ,(HRAPONTADA - HRABONADA) HRREALIQUI" + CRLF
	cQuery += "       ,HRSPDD" + CRLF
	cQuery += "       ,DTULTFAT" + CRLF
	cQuery += "       ,ULTATEND" + CRLF
	cQuery += "       ,VLFATM1" + CRLF
	cQuery += "       ,VLFATM2" + CRLF
	cQuery += "       ,VLFATM3" + CRLF
	cQuery += "       ,VLFATM4" + CRLF
	cQuery += "       ,VLFATM5" + CRLF
EndIf

cQuery += "FROM "+RetSqlName("PE5")+" PE5 " + CRLF

cQuery += "INNER JOIN "+RetSqlName("PE0")+" PE0 " + CRLF
cQuery += "    ON PE0.PE0_FILIAL = '"+FwxFilial("PE0")+"' " + CRLF
cQuery += "       AND PE0.PE0_COD =  PE5.PE5_CODMOT " + CRLF
cQuery += "        AND PE0.D_E_L_E_T_ = ' ' " + CRLF

cQuery += "LEFT JOIN "+RetSqlName("SA1")+" SA1 " + CRLF
cQuery += "   ON SA1.A1_FILIAL = '"+FwxFilial("SA1")+"' " + CRLF
cQuery += "      AND SA1.A1_COD =  PE5.PE5_CLIENT " + CRLF
cQuery += "      AND SA1.D_E_L_E_T_ = ' ' " + CRLF

cQuery += "LEFT JOIN " + RetSqlName("AI0") + " AI0" + CRLF 
cQuery += "   ON AI0_FILIAL = '" + FwxFilial("AI0") + "'" + CRLF 
cQuery += "      AND AI0_CODCLI = A1_COD" + CRLF 
cQuery += "      AND AI0_LOJA = A1_LOJA" + CRLF 
cQuery += "      AND AI0.D_E_L_E_T_ = ' '" + CRLF 

cQuery += " LEFT JOIN " + RetSqlName("PE1") + " PE1 " + CRLF 
cQuery += "    ON PE1.PE1_CODIGO = PE5_UNSRV" + CRLF 
cQuery += "       AND PE1.D_E_L_E_T_ = ' '" + CRLF 

cQuery += " LEFT JOIN " + RetSqlName("CTT") + " CTT1" + CRLF
cQuery += "    ON CTT1.CTT_FILIAL = '" + xFilial("CTT") + "'" + CRLF
cQuery += "       AND CTT1.CTT_CUSTO = PE1.PE1_CCUSTO" + CRLF
cQuery += "       AND CTT1.D_E_L_E_T_ = ' '" + CRLF

cQuery += " LEFT JOIN " + RetSqlName("PE2") + " PE2 " + CRLF 
cQuery += "    ON PE2.PE2_UNSRV = PE5_UNSRV" + CRLF 
cQuery += "       AND PE2.PE2_EMPFIL = PE5_EMPPRJ || PE5_FILPRJ" + CRLF 
cQuery += "       AND PE2.D_E_L_E_T_ = ' '" + CRLF

cQuery += " LEFT JOIN " + RetSqlName("CTT") + " CTT2" + CRLF
cQuery += "    ON CTT2.CTT_FILIAL = '" + xFilial("CTT") + "'" + CRLF
cQuery += "       AND CTT2.CTT_CUSTO = PE2.PE2_CCUSTO" + CRLF
cQuery += "       AND CTT2.D_E_L_E_T_ = ' '" + CRLF

cQuery += " LEFT JOIN " + RetSqlName("AOV") + " AOV" + CRLF 
cQuery += "    ON AOV_FILIAL = '" + xFilial("AOV") + "'"  + CRLF
cQuery += "       AND AOV_CODSEG = A1_CODSEG" + CRLF
cQuery += "       AND AOV.D_E_L_E_T_ = ' ' " + CRLF

cQuery += " INNER JOIN (SELECT PE8_PROJET" + CRLF
cQuery += "                   ,PE8_MSREF" + CRLF
cQuery += "                   ,SUM(CASE WHEN PE8_TPFRT NOT IN ('5', '6') THEN PE8_QTDHRS ELSE 0 END) PE8_QTDHRS" + CRLF
cQuery += "                   ,SUM(CASE WHEN PE8_TPFRT = '5' THEN PE8_QTDHRS ELSE 0 END) HRSRECONHE" + CRLF

If MV_PAR15 == 1
	cQuery += "              ,PE8_CODIGO" + CRLF
	cQuery += "              ,PE8_DESCR"  + CRLF
	cQuery += "              ,PE8_COORD"  + CRLF
	cQuery += "              ,PE8_UNSRES" + CRLF
EndIf
	
cQuery += "             FROM "+RetSqlName("PE8")+" PE8 " + CRLF
cQuery += "             INNER JOIN "+RetSqlName("PE0")+" PE01 " + CRLF
cQuery += "                 ON PE01.PE0_FILIAL = '"+FwxFilial("PE0")+"' " + CRLF
cQuery += "                    AND PE01.PE0_COD =  PE8.PE8_MOTIVO " + CRLF
If MV_PAR14 < 3 
	cQuery += "                    AND PE01.PE0_TPMOT = '"+IIF(MV_PAR14 == 1, "1", "2")+"' " + CRLF
EndIf 
cQuery += "                    AND PE01.D_E_L_E_T_ = ' ' " + CRLF
cQuery += "             WHERE PE8.PE8_FILIAL = '"+FwxFilial("PE8")+"' " + CRLF

If MV_PAR16 == 1
	cQuery += "             AND PE8.PE8_MSREF BETWEEN '"+cAnoMesIni+"' AND '"+cAnoMesFim+"' " + CRLF
	cQuery += "             AND PE8.PE8_TPFRT = '5' " + CRLF
EndIf
cQuery += "             AND PE8.D_E_L_E_T_ = ' ' " + CRLF
cQuery += "       GROUP BY PE8_PROJET, PE8_MSREF" + CRLF "

If MV_PAR15 == 1
	cQuery += "   ,PE8_CODIGO,PE8_DESCR,PE8_COORD,PE8_UNSRES" + CRLF
EndIf
cQuery += "   ) PE8 " + CRLF
cQuery += "   ON PE8.PE8_PROJET = PE5.PE5_PROJET" + CRLF

If MV_PAR15 == 1
	cQuery += "       LEFT JOIN " + RetSqlName("PE1") + " PE1A " + CRLF 
	cQuery += "          ON PE1A.PE1_CODIGO = PE8_UNSRES" + CRLF 
	cQuery += "             AND PE1A.D_E_L_E_T_ = ' '" + CRLF 

	cQuery += "       LEFT JOIN "+RetSqlName("RD0")+" RD0 " + CRLF
	cQuery += "          ON RD0.RD0_FILIAL = '"+FwxFilial("RD0")+"' " + CRLF
	cQuery += "             AND RD0.RD0_CODIGO =  PE8.PE8_COORD " + CRLF
	cQuery += "             AND RD0.D_E_L_E_T_ = ' ' " + CRLF
	
	cQuery += " LEFT JOIN " + RetSqlName("PE2") + " PE2A " + CRLF 
	cQuery += "    ON PE2A.PE2_UNSRV = PE8_UNSRES" + CRLF 
	cQuery += "       AND PE2A.PE2_EMPFIL = PE5_EMPPRJ || PE5_FILPRJ" + CRLF 
	cQuery += "       AND PE2A.D_E_L_E_T_ = ' '" + CRLF
	
	cQuery += "       LEFT JOIN " + RetSqlName("CTT") + " CTTA" + CRLF
	cQuery += "          ON CTTA.CTT_FILIAL = '" + xFilial("CTT") + "'" + CRLF
	cQuery += "             AND CTTA.CTT_CUSTO = PE2A.PE2_CCUSTO" + CRLF
	cQuery += "             AND CTTA.D_E_L_E_T_ = ' '" + CRLF
EndIf

If MV_PAR15 == 2
	//APONTAMENTOS DOS PROJETOS
	cQuery += "LEFT JOIN (SELECT PF91.PF9_PROJET" + CRLF
	cQuery += "                  ,SUM(CASE WHEN PF91.PF9_STATUS = '3' THEN PF91.PF9_TOTAL ELSE 0 END) HRAPONTADA" + CRLF
	cQuery += "                  ,SUM(CASE WHEN PE01.PE0_TPMOT <> '1' AND PF91.PF9_STATUS = '3' THEN PF91.PF9_TOTAL ELSE 0 END) HRABONADA" + CRLF
	cQuery += "                  ,MAX(CASE WHEN PF91.PF9_STATUS = '3' THEN PF91.PF9_DATA ELSE '' END) ULTATEND" + CRLF
	cQuery += "                  ,SUM(CASE WHEN PF91.PF9_CODMOT IN " +cMotPDD+ " AND PF91.PF9_STATUS = '3' THEN PF91.PF9_TOTAL ELSE 0 END) HRSPDD" + CRLF
	cQuery += "           FROM " + RetSqlName("PF9") + " PF91" + CRLF
	cQuery += "           INNER JOIN " + RetSqlName("PE0") + " PE01" + CRLF
	cQuery += "              ON PE01.PE0_FILIAL = '" + FwxFilial("PE0") + "'" + CRLF 
	cQuery += "                 AND PE01.PE0_COD = PF91.PF9_CODMOT " + CRLF
	cQuery += "                 AND PE01.D_E_L_E_T_ = ' '" + CRLF
	cQuery += "           WHERE PF91.PF9_FILIAL = '" + FwxFilial("PF9") + "'" + CRLF
	If MV_PAR16 == 1
		cQuery += "                 AND EXISTS( SELECT 'OK' " + CRLF
		cQuery += "                             FROM "+RetSqlName("PE8")+" PE8B " + CRLF	
		cQuery += "                             WHERE PE8B.PE8_FILIAL = '"+FwxFilial("PE8")+"' " + CRLF
		cQuery += "                                   AND PE8B.PE8_TPFRT = '5' " + CRLF
		cQuery += "                                   AND PE8B.PE8_MSREF BETWEEN '"+cAnoMesIni+"' AND '"+cAnoMesFim+"' " + CRLF
		cQuery += "                                   AND PE8B.PE8_PROJET = PF91.PF9_PROJET " + CRLF	
		cQuery += "                                   AND PE8B.D_E_L_E_T_ = ' ') " + CRLF
	EndIf
	cQuery += "           	    AND PF91.D_E_L_E_T_ = ' '" + CRLF
	cQuery += "           GROUP BY PF9_PROJET) PF9" + CRLF
	cQuery += "   ON PF9.PF9_PROJET = PE5_PROJET" + CRLF
	
	//FATURAMENTO/CANCELAMENTOS DOS PROJETOS
	cQuery += "LEFT JOIN "+cTrbSD1+" TRBSD1 " + CRLF
	cQuery += "   ON TRBSD1.PROJETO = PE8.PE8_PROJET" + CRLF
	cQuery += "      AND TRBSD1.MESREF = PE8.PE8_MSREF" + CRLF
EndIf

cQuery += "WHERE PE5.PE5_FILIAL = '"+FwxFilial("PE5")+"'" + CRLF
cQuery += "       AND PE5.PE5_CLIENT || PE5.PE5_LOJA BETWEEN '"+MV_PAR05+MV_PAR06+"' AND '"+MV_PAR07+MV_PAR08+"' " + CRLF 
cQuery += "       AND PE5.PE5_PROJET BETWEEN '"+MV_PAR09+"' AND '"+MV_PAR10+"' "  + CRLF

If !Empty(MV_PAR13)
	cQuery += "       AND PE5.PE5_UNSRV IN "+FormatIn(MV_PAR13, ";")  + CRLF
EndIf

If MV_PAR16 == 2
	cQuery += "       AND SUBSTR(PE5.PE5_DTENCR, 1, 6) BETWEEN '"+cAnoMesIni+"' AND '"+cAnoMesFim+"' " + CRLF
EndIf

cQuery += "       AND EXISTS(SELECT 'OK' " + CRLF
cQuery += "                  FROM "+RetSqlName("PE7")+" PE7 " + CRLF
cQuery += "                  WHERE PE7.PE7_FILIAL = '"+FwxFilial("PE7")+"' " + CRLF
cQuery += "                        AND PE7.PE7_PROJET =  PE5.PE5_PROJET " + CRLF
cQuery += "                        AND PE7.PE7_CODREC BETWEEN '"+MV_PAR11+"' AND '"+MV_PAR12+"' " + CRLF 
cQuery += "                        AND PE7.PE7_PAPEL = '"+cCodGPP+"' " + CRLF
cQuery += "                        AND PE7.D_E_L_E_T_ = ' ') " + CRLF
cQuery += "       AND PE5.D_E_L_E_T_ = ' ' " + CRLF
cQuery += "ORDER BY MESREF, PROJETO"

cQuery := ChangeQuery(cQuery)

If !lJob
	oProcess:IncRegua2(STR0041 + "("+ElapTime(cTimeIni,Time())+")") //"Selecionando Reg.: "
	cTimeIni := Time()
EndIf

dbUseArea(.T.,"TOPCONN",TCGENQRY(,,cQuery),cAliasSld,.F.,.T.)

If !lJob
	oProcess:IncRegua2("Compatibilizando campos.: " + "("+ElapTime(cTimeIni,Time())+")")
	cTimeIni := Time()
EndIf

dbSelectArea(cAliasSld)

//COMPATIBILIZA OS CAMPOS DO RESULTSET
TcSetField( cAliasSld, "EMISSAO"    , "D", 8, 0)
TcSetField( cAliasSld, "DTENCERRA"  , "D", 8, 0)
TcSetField( cAliasSld, "QTD_ORCADA" , "N",18, 2)
TcSetField( cAliasSld, "VLR_ORCADO" , "N",18, 2)
TcSetField( cAliasSld, "LIMRNP"     , "N",18, 2)
TcSetField( cAliasSld, "HRSUPENSA"  , "N",18, 2)
TcSetField( cAliasSld, "VLFATM1"    , "N",18, 2)
TcSetField( cAliasSld, "VLFATM2"    , "N",18, 2)
TcSetField( cAliasSld, "VLFATM3"    , "N",18, 2)
TcSetField( cAliasSld, "VLFATM4"    , "N",18, 2)
TcSetField( cAliasSld, "VLFATM5"    , "N",18, 2)
TcSetField( cAliasSld, "VLCANCM1"   , "N",18, 2)
TcSetField( cAliasSld, "VLCANCM2"   , "N",18, 2)
TcSetField( cAliasSld, "VLCANCM3"   , "N",18, 2)
TcSetField( cAliasSld, "VLCANCM4"   , "N",18, 2)
TcSetField( cAliasSld, "VLCANCM5"   , "N",18, 2)
TcSetField( cAliasSld, "HRORCFRT"   , "N",18, 2)
TcSetField( cAliasSld, "HRSRECONHE" , "N",18, 2)
TcSetField( cAliasSld, "VLRORCFRT"  , "N",18, 2)
TcSetField( cAliasSld, "HRSFRENTES" , "N",18, 2)
TcSetField( cAliasSld, "HRFATURADA" , "N",18, 2)
TcSetField( cAliasSld, "HRCANCELA"  , "N",18, 2)
TcSetField( cAliasSld, "HRFATLIQUI" , "N",18, 2)
TcSetField( cAliasSld, "HRAPONTADA" , "N",18, 2)
TcSetField( cAliasSld, "HRABONADA"  , "N",18, 2)
TcSetField( cAliasSld, "HRREALIQUI" , "N",18, 2)

DbSelectArea(cAliasSld)

(cAliasSld)->(DbGoTop())
cfile := "SLD_"+DtoS(Date())+STRTRAN(Time(), ":", "")+".XLS"



If !ExistDIR("C:\APEXCEL")
	If	MAKEDIR("C:\APEXCEL")!= 0
		Aviso("Erro" ,"Nao foi possivel criar diretorio: C:\APEXCEL\",{"OK"}) //"Inconsistencia"###"Nao foi possivel criar diretorio "###".Finalizando ..."
		return nil
	EndIf
EndIf

cArqDestino := "C:\APEXCEL\"+cfile



//ERRO invalid handle used in file D:\bamboo-agent- DENTRO DESTA FUNÇÃO
If U_TRGXLX01(cAliasSld, @cArqDestino, , /*cTitle*/, .f./*lAskOpen*/)
	MsgAlert('Arquivo de Conferencia salvo em '+cArqDestino,'AVISO')
EndIf
/*
//COPIA DO TOP PARA DBF
(cAliasSld)->  (__dbCopy( (RetFileName(cfile)) , { },,,,,.F., "DBFCDXADS" ))

//COPIA O ARQUIVO DBF PARA A MAQUINA DO USUARIO
If  __CopyFile(cfile+".dbf", U_GetLocEnd( 0 ) + cfile+".dbf")
	Aviso("*** AVISO ***","Foi gerado o arquivo: "+cfile+".dbf"+", no diretorio "+ U_GetLocEnd( 0 ),{"Ok"},2)
Else
	Aviso("*** AVISO ***","N�o foi poss�vel a gera��o do arquivo.",{"Ok"})	
EndIf

//APAGA O ARQUIVO DBF DO SERVER 
If File( cfile+".dbf" )
	FErase(cfile+".dbf")
EndIf
*/
If MV_PAR15 == 2
	oTemptable:Delete()
EndIf

Return




/*/{Protheus.doc} SV002A01
(CriaSx1 - Fun��o de cria��o de dicion�rio de Perguntes do SX1)
<AUTHOR>
@since 17/09/2015
@version 1.0
@param cPerg, character, (Nome do Pergunte a ser criado)
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/

Static Function SV002A01(cPerg)

Local aArea        := GetArea()
Local cAliAtu    := Alias()
Local aRegs :={}
Local i := 0, j := 0
Local cGrupo     := ''
Local cOrdem	 := ''
Local cPergunt	 := ''
Local cPerSpa	 := ''
Local cPerEng	 := ''
Local cVar		 := ''
Local cTipo		 := ''
Local nTamanho   := 0
Local nDecimal   := 0 
Local nPresel	 := 0
Local cGSC		 := ''
Local cValid	 := ''
Local cF3		 := ''
Local cGrpSxg	 := ''
Local cPyme	     := ''
Local cVar01	 := ''
Local cDef01	 := ''
Local cDefSpa1	 := ''
Local cDefEng1	 := ''
Local cCnt01     := ''
Local cDef02     := ''
Local cDefSpa2	 := ''
Local cDefEng2	 := ''
Local cDef03	 := ''
Local cDefSpa3	 := ''
Local cDefEng3	 := ''
Local cDef04	 := ''
Local cDefSpa4	 := ''
Local cDefEng4	 := ''
Local cDef05	 := ''
Local cDefSpa5	 := ''
Local cDefEng5	 := ''
Local aHelpPor	 := {}
Local aHelpEng	 := {}
Local aHelpSpa	 := {}
Local cHelp		 := ''

//ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Ä¿
//ï¿½Campos a serem grav. no SX1ï¿½
//ï¿½aRegs[nx][01] - x1_pergunteï¿½
//ï¿½aRegs[nx][02] - x1_perspa  ï¿½
//ï¿½aRegs[nx][03] - x1_pereng  ï¿½
//ï¿½aRegs[nx][04] - x1_variavl ï¿½
//ï¿½aRegs[nx][05] - x1_tipo    ï¿½
//ï¿½aRegs[nx][06] - x1_tamanho ï¿½
//ï¿½aRegs[nx][07] - x1_decimal ï¿½
//ï¿½aRegs[nx][08] - x1_presel  ï¿½
//ï¿½aRegs[nx][09] - x1_gsc     ï¿½
//ï¿½aRegs[nx][10] - x1_valid   ï¿½
//ï¿½aRegs[nx][11] - x1_var01   ï¿½
//ï¿½aRegs[nx][38] - x1_f3      ï¿½
//ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
//U_FTTVSX001(cGrupo,cOrdem,cPergunt,cPerSpa,cPerEng,cVar,cTipo,nTamanho,nDecimal,nPresel,cGSC,cValid,cF3, cGrpSxg,cPyme,cVar01,cDef01,cDefSpa1,cDefEng1,;
				//ok  ok      ok        ok     ok      ok   ok    ok         ok 
AAdd(aRegs,{cPerg,"01","Mï¿½s De"           ,"Mï¿½s De"        ,"Mï¿½s De"          ,"mv_ch1","C",2                      ,0,0,"G",""                      ,"MV_PAR01",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""      })
AAdd(aRegs,{cPerg,"02","Ano De"             ,"Ano de"            ,"Ano de"            ,"mv_ch2","C",4                      ,0,0,"G",""                      ,"MV_PAR02",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""      })
AAdd(aRegs,{cPerg,"03","M�s At�"            ,"M�s At�"           ,"M�s At�"           ,"mv_ch3","C",2                      ,0,0,"G",""                      ,"MV_PAR03",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""      })
AAdd(aRegs,{cPerg,"04","Ano At�"            ,"Ano At�"           ,"Ano At�"           ,"mv_ch4","C",4                      ,0,0,"G",""                      ,"MV_PAR04",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""      })
AAdd(aRegs,{cPerg,"05","Cliente De"         ,"Cliente De"        ,"Cliente De"        ,"mv_ch5","C",TAMSX3("A1_COD")[1]    ,0,0,"G",""                      ,"MV_PAR05",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","","SA1"   })
AAdd(aRegs,{cPerg,"06","Loja De"            ,"Loja De"           ,"Loja De"           ,"mv_ch6","C",TAMSX3("A1_LOJA")[1]   ,0,0,"G",""                      ,"MV_PAR06",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""      })
AAdd(aRegs,{cPerg,"07","Cliente At�"        ,"Cliente At�"       ,"Cliente At�"       ,"mv_ch7","C",TAMSX3("A1_COD")[1]    ,0,0,"G",""                      ,"MV_PAR07",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","","SA1"   })
AAdd(aRegs,{cPerg,"08","Loja At�"           ,"Loja At�"          ,"Loja At�"          ,"mv_ch8","C",TAMSX3("A1_LOJA")[1]   ,0,0,"G",""                      ,"MV_PAR08",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""      })
AAdd(aRegs,{cPerg,"09","Projeto de ?"       ,"Projeto de ?"      ,"Projeto de ?"      ,"mv_ch9","C",TAMSX3("PE5_PROJET")[1],0,0,"G",""                      ,"MV_PAR09",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","","PE5"   })
AAdd(aRegs,{cPerg,"10","Projeto Ate ?"      ,"Projeto Ate ?"     ,"Projeto Ate ?"     ,"mv_cha","C",TAMSX3("PE5_PROJET")[1],0,0,"G",""                      ,"MV_PAR10",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","","PE5"   })
AAdd(aRegs,{cPerg,"11","GPP de ?"           ,"GPP de ?"          ,"GPP de ?"          ,"mv_chb","C",TAMSX3("RD0_CODIGO")[1],0,0,"G",""                      ,"MV_PAR11",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","","RD0PE7"})
AAdd(aRegs,{cPerg,"12","GPP Ate ?"          ,"GPP Ate ?"         ,"GPP Ate?"          ,"mv_chc","C",TAMSX3("RD0_CODIGO")[1],0,0,"G",""                      ,"MV_PAR12",""                ,""                 ,""                 ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","","RD0PE7"})
AAdd(aRegs,{cPerg,"13","Unidades de Servi�o","Unidade de Servi�o","Unidade de Servi�o","mv_chd","C",99                     ,0,0,"G","U_SV011C01('MV_PAR13')","MV_PAR13",""                ,""               ,""                   ,"","",""               ,""                ,""                ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""      })
AAdd(aRegs,{cPerg,"14","Motivos"            ,"Motivos"           ,"Motivos"           ,"mv_che","N",1                      ,0,0,"C",""                      ,"MV_PAR14","Fatur�veis"      ,"Fatur�veis"       ,"Fatur�veis"       ,"","","N�o Faturaveis" ,"N�o Faturaveis"  ,"N�o Faturaveis"  ,"","","3=Ambos","3=Ambos","3=Ambos","","","","","","","","","","","",""      })
AAdd(aRegs,{cPerg,"15","Agrupar"            ,"Agrupar"           ,"Agrupar"           ,"mv_chf","N",1                      ,0,0,"C",""                      ,"MV_PAR15","Por Frente Ent." ,"Por Frente Ent."  ,"Por Frente Ent."  ,"","","Por Projeto  "  ,"Por Projeto  "   ,"Por Projeto  "   ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""      })
AAdd(aRegs,{cPerg,"16","Processar"          ,"Processar"         ,"Processar"         ,"mv_chg","N",1                      ,0,0,"C",""                      ,"MV_PAR16","Com Reconhecim." ,"Com Reconhecim."  ,"Com Reconhecim."  ,"","","Todos Encerrad.","Todos Encerrad." ,"Todos Encerrad." ,"","",""       ,""       ,""       ,"","","","","","","","","","","",""      })
			//grpok  ordok   perpok              perINGok             perESPok			okvar  oktip oktam		           OKOK  OK  VALID OK					VAR       DEF                 DEFENG             DEFSPA
				//01  02        03                 04                   05               06      07   08                  09 10  11  12						   13           14                15                 16
dbSelectArea("SX1")
dbSetOrder(1)

aHelpPor	:= {}
aHelpEng	:= {}
aHelpSpa	:= {}

Aadd( aHelpPor,	"Help Port	"	)
Aadd( aHelpPor,	"Help Port								"	)

Aadd( aHelpSpa,	"Help Spa		"	)
Aadd( aHelpSpa,	"Help Spa		"	)

Aadd( aHelpEng,	"Help Eng		"	)
Aadd( aHelpEng,	"Help Eng"	)

   For i:= 1 to Len(aRegs)
			cGrupo       := aRegs[i][1]
			cOrdem		 := aRegs[i][2]
			cPergunt	 := aRegs[i][3]
			cPerSpa		 := aRegs[i][4]
			cPerEng		 := aRegs[i][5]
			cVar		 := aRegs[i][6]
			cTipo		 := aRegs[i][7]
			nTamanho     := aRegs[i][8]
			nDecimal	 := aRegs[i][9]
            nPresel		 := aRegs[i][10]
			cGSC		 := aRegs[i][11]
			cValid		 := aRegs[i][12]
			cF3			 := aRegs[i][38]
			cGrpSxg      := ''
		
			If Len(aRegs[i]) > 38 
				cPyme		 := aRegs[i][39]
			else
				cPyme		 := ""
			Endif 
				
			cHelp        := ''
			cVar01		 := aRegs[i][13]
			cDef01		 := aRegs[i][14]
			cDefSpa1	 := aRegs[i][15]
			cDefEng1	 := aRegs[i][16]
	        cCnt01       := aRegs[i][17]
			cDef02		 := aRegs[i][19]
			cDefSpa2	 := aRegs[i][20]
			cDefEng2	 := aRegs[i][21]
			cDef03		 := aRegs[i][24]
			cDefSpa3	 := aRegs[i][25]
			cDefEng3	 := aRegs[i][26]
			cDef04		 := aRegs[i][29]
			cDefSpa4	 := aRegs[i][30]
	        cDefEng4	 := aRegs[i][31]
			cDef05		 := aRegs[i][34]
			cDefSpa5	 := aRegs[i][35]
			cDefEng5	 := aRegs[i][36]
			

		U_FTTVSX001(cGrupo,cOrdem,cPergunt,cPerSpa,cPerEng,cVar,cTipo,nTamanho,nDecimal,;
                        nPresel,cGSC,cValid,cF3, cGrpSxg,cPyme,cVar01,cDef01,cDefSpa1,cDefEng1,;
	                    cCnt01,cDef02,cDefSpa2,cDefEng2,cDef03,cDefSpa3,cDefEng3,cDef04,cDefSpa4,;
	                    cDefEng4,cDef05,cDefSpa5,cDefEng5,aHelpPor,aHelpEng,aHelpSpa,cHelp)		
	Next


DbSelectArea(cAliAtu)
RestArea(aArea)

Return 

/*/{Protheus.doc} FATSRVMES
(FATSRVMES - Cria temporario com faturamento e cancelamentos mes a mes)
<AUTHOR>
@since 01/04/2016
@version 1.0
@param lJob      , Logico   , (Se est� sendo executado via job (.T.) ou remote (.F.))
@param oTempTable, objeto   , (Tabela Temporaria gerada pela FWtemporaryTable)
@param cAliTbTmp , character, (Alias da tabela tempor�ria)
@param cAnoMesIni, character, (AnoMes inicial para filtro)
@param cAnoMesFim, character, (AnoMes final para filtro)
@param aMoedasOri, array    , (Array com a moedas da empresa de origem)
@return cTbTemp, Nome real da tabela tempor�ria criada no banco
@example
(examples)
@see (links_or_references)
/*/

User Function FATSRVMES(lJob, oTempTable,cAliTbTmp, cAnoMesIni, cAnoMesFim, aMoedasOri, EhTeste)
Local aAreaSX3   := SX3->(GetArea())
Local cQuery     := ""
Local aFilProc   := U_RetFil("1", "")
Local cBkpEmpAnt := cEmpAnt
Local cBkpFilAnt := cFilAnt
Local aCposField := {}
Local aTamCpos   := {}
Local cTbTemp    := ""
Local aMoedas    := {}
Local cTimeIni   := Time()
Local cEmpProc   := ""
Local cFilProc   := ""
Local cMoedaOri  := ""
Local cCpoVLCan  := ""
Local nPosMoeda  := 0
Local cMoedaExt  := ""
Local cCpoMoeda  := ""
Local lGrava     := .T.
Local cMesRef    := ""
Local nY
Local nX
Local nZ

If !lJob
	oProcess:SetRegua2(0)
	oProcess:IncRegua2( STR0037 +"("+ElapTime(cTimeIni,Time())+")") //"Processando Faturamentos"
	cTimeIni := Time()
EndIf

//CRIA ESTRUTURA DA TABELA TEMPORARIA
SX3->(DbSetOrder(2))
If SX3->(DbSeek("PEA_PROJET"))
	aTamCpos := TAMSX3("PEA_PROJET")
	aadd(aCposField,{"PROJETO",aTamCpos[3],aTamCpos[1],aTamCpos[2]})
   
	aadd(aCposField,{"MESREF"    ,"C",  6,0})
	aadd(aCposField,{"HRFATURADA","N", 18,2})
	aadd(aCposField,{"HRCANCELA" ,"N", 18,2})
	aadd(aCposField,{"VLFATM1"   ,"N", 18,2})
	aadd(aCposField,{"VLFATM2"   ,"N", 18,2})
	aadd(aCposField,{"VLFATM3"   ,"N", 18,2})
	aadd(aCposField,{"VLFATM4"   ,"N", 18,2})
	aadd(aCposField,{"VLFATM5"   ,"N", 18,2})
	aadd(aCposField,{"DTULTFAT"  ,"D",  8,0})
	For nX = 1 To Len(aMoedasOri)
		aadd(aCposField,{"VLCANCM"+cValToChar(aMoedasOri[nX][1]) ,"N", 18,2})
	Next
	aadd(aCposField,{"HRAFATURAR"   ,"N", 18,2})

	oTemptable:SetFields( aCposField )
	oTempTable:AddIndex("index1", {"PROJETO","MESREF"} )   
	
	oTempTable:Create()
	cTbTemp := oTempTable:GetRealName()
	cAliTbTmp := oTempTable:GetAlias()

	
	DbSelectArea(cAliTbTmp)
	
	//SELECIONA E GRAVA OS REGISTROS DE FATURAMENTO POR MES DE REFERENCIA
	cQuery := "SELECT PEA1.PEA_PROJET PROJETO" + CRLF
	cQuery += "       ,CASE WHEN SUBSTR(PEA1.PEA_DTFAT, 1, 6) <= '"+cAnoMesIni+"' 
	cQuery += "             THEN '"+cAnoMesIni+"' 
	cQuery += "             ELSE SUBSTR(PEA1.PEA_DTFAT, 1, 6) END MESREF" + CRLF
	cQuery += "       ,SUM(PEA1.PEA_QTDPAR) HRFATURADA" + CRLF
	cQuery += "       ,SUM(PEA1.PEA_VPMOE1) VLFATM1" + CRLF
	cQuery += "       ,SUM(PEA1.PEA_VPMOE2) VLFATM2" + CRLF
	cQuery += "       ,SUM(PEA1.PEA_VPMOE3) VLFATM3" + CRLF
	cQuery += "       ,SUM(PEA1.PEA_VPMOE4) VLFATM4" + CRLF
	cQuery += "       ,SUM(PEA1.PEA_VPMOE5) VLFATM5" + CRLF
	cQuery += "       ,MAX(PEA1.PEA_DTFAT) DTULTFAT" + CRLF
	cQuery += "       ,SUM(CASE WHEN PEA_NUMFAT = '' THEN PEA1.PEA_QTDPAR ELSE 0 END) HRAFATURAR" + CRLF
	cQuery += "FROM " + RetSqlName("PEA") + " PEA1" + CRLF
	cQuery += "WHERE PEA1.PEA_FILIAL = '" + xFilial("PEA") + "' " + CRLF
	cQuery += "      AND SUBSTR(PEA1.PEA_DTFAT, 1, 6) <= '" + cAnoMesFim + "'" + CRLF
	cQuery += "      AND PEA_NUMFAT <> '' " + CRLF
	cQuery += "      AND PEA1.D_E_L_E_T_ = ' '" + CRLF
	
	cQuery += "      AND EXISTS( SELECT 'OK' " + CRLF
	cQuery += "                  FROM "+RetSqlName("PE8")+" PE8A " + CRLF	
	cQuery += "                  WHERE PE8A.PE8_FILIAL = '"+FwxFilial("PE8")+"' " + CRLF
	cQuery += "                        AND PE8A.PE8_TPFRT = '5' " + CRLF
	cQuery += "                        AND PE8A.PE8_MSREF BETWEEN '"+cAnoMesIni+"' AND '"+cAnoMesFim+"' " + CRLF
	cQuery += "                        AND PE8A.PE8_PROJET = PEA1.PEA_PROJET " + CRLF	
	cQuery += "                        AND PE8A.D_E_L_E_T_ = ' ') " + CRLF
	
	cQuery += "GROUP BY PEA_PROJET" + CRLF
	cQuery += "       ,CASE WHEN SUBSTR(PEA1.PEA_DTFAT, 1, 6) <= '"+cAnoMesIni+"' 
	cQuery += "             THEN '"+cAnoMesIni+"' 
	cQuery += "             ELSE SUBSTR(PEA1.PEA_DTFAT, 1, 6) END" + CRLF
	cQuery += "ORDER BY PEA_PROJET" + CRLF
	cQuery += "	,MESREF" + CRLF
	
	cQuery := ChangeQuery(cQuery)
	
	GrvResult(lJob, cAliTbTmp, cQuery, aCposField, cTimeIni, .T.)
	
	If !lJob
		oProcess:SetRegua1(0)
		oProcess:IncRegua1(STR0039) //"Processando Cancelamento"
		oProcess:SetRegua2(0)
		oProcess:IncRegua2( STR0038 +"("+ElapTime(cTimeIni,Time())+")") //"Processando... "
		cTimeIni := Time()
	EndIf
	
	//SELECIONA E GRAVA OS REGISTROS DE CANCELAMENTO POR MES DE REFERENCIA EM CADA PROJETO
	For nY := 1 To Len(aFilProc)
		If cEmpProc <> SubStr(aFilProc[nY], 1, 2)
			cEmpProc := SubStr(aFilProc[nY], 1, 2)
			cFilProc := SubStr(aFilProc[nY], 3)
			
			oProcess:IncRegua1(STR0039 +"  - "+ cEmpProc)	//"Processando Cancelamento"
			
			//TROCA A FILIAL SE VAI GERAR EM OUTRA FILIAL DIFERENTE DA LOGADA
			If cEmpAnt <> cEmpProc 
				loop //SOMENTE PARA O FECHAMENTO de 201608
				U_SrvXEmpChange(cEmpProc,cFilProc)
				
				If !( Select( cAliTbTmp ) > 0 )
					MPSysOpenQuery(cTbTemp, cAliTbTmp)
				EndIf
				
				aMoedas := U_SRVXRetMoeda(cEmpProc,cFilProc,Date())
			Else
				aMoedas := aClone(aMoedasOri)
			EndIf
				
			cQuery := "SELECT PEA_PROJET PROJETO" + CRLF 
			cQuery += "       ,CASE WHEN SUBSTR(D1_EMISSAO, 1, 6) <= '"+cAnoMesIni+"' 
			cQuery += "             THEN '"+cAnoMesIni+"' 
			cQuery += "             ELSE SUBSTR(D1_EMISSAO, 1, 6) END MESREF" + CRLF
			cQuery += "       ,SUM(D1_QUANT) HRCANCELA" + CRLF 
			
			For nX = 1 To Len(aMoedasOri)
				//NUMERO DAS MOEDAS DA EMPRESA DE ORIGEM
				cMoedaOri := cValToChar(aMoedasOri[nX][1])
				cCpoVLCan := "VLCANCM" + cMoedaOri
				
				//NUMERO DAS MOEDAS DA EMPRESA DE EXTRACAO
				nPosMoeda := aScan(aMoedas,{|x| Alltrim(x[3]) == Alltrim(aMoedasOri[nX][3])})
				nPosMoeda := IIF(nPosMoeda > 0, aMoedas[nPosMoeda][1], 0)
				
				If nPosMoeda = 0
					cQuery += ", 0 " + cCpoVLCan
				Else
					cMoedaExt := cValToChar(aMoedas[nPosMoeda][1])
					cCpoMoeda := "SM2.M2_MOEDA" + cMoedaExt
					
					cQuery += "       ,SUM("
					If cValToChar(aMoedas[1][1]) <> cMoedaExt
						cQuery += "CASE WHEN "+cCpoMoeda+" <> 0 THEN (" + CRLF
					EndIf
					
					cQuery += "CASE WHEN F1_MOEDA <> 1 " + CRLF
					cQuery += "     THEN D1_TOTAL * (CASE "
					
					For nZ := 2 To Len(aMoedas)
						cQuery += " WHEN F1_MOEDA = "+cValToChar(aMoedas[nZ][1])+" THEN SM2.M2_MOEDA"+cValToChar(aMoedas[nZ][1])+" " + CRLF 
					Next
					cQuery += "                           ELSE 1 END) " + CRLF
					cQuery += "     ELSE D1_TOTAL END " + CRLF
					
					If cValToChar(aMoedas[1][1]) <> cMoedaExt
						cQuery += ") / "+cCpoMoeda+ CRLF
						cQuery += "ELSE 0 END"+ CRLF
					EndIf
					
					cQuery += ") " + cCpoVLCan+ CRLF
				EndIf
			Next
			
			cQuery += "FROM "+RetSqlName("SD1")+" SD1" + CRLF 
			cQuery += "INNER JOIN "+RetSqlName("PEA")+" PEA" + CRLF 
			cQuery += "    ON PEA.PEA_FILFAT = '"+cEmpProc+"' || D1_FILIAL" + CRLF 
			cQuery += "	   AND PEA.PEA_NUMFAT = SD1.D1_NFORI" + CRLF 
			cQuery += "	   AND PEA.PEA_SERFAT = SD1.D1_SERIORI" + CRLF 
			cQuery += "	   AND PEA.PEA_CLIENT = SD1.D1_FORNECE" + CRLF 
			cQuery += "	   AND PEA.PEA_LOJA = SD1.D1_LOJA" + CRLF 
			cQuery += "	   AND PEA.PEA_PRODUT = SD1.D1_COD" + CRLF 
			cQuery += "	   AND PEA.PEA_ITEMOR = SD1.D1_ITEMORI" + CRLF 
			cQuery += "	   AND PEA.D_E_L_E_T_ = ' '" + CRLF
			cQuery += "INNER JOIN "+RetSqlName("SF1")+" SF1 " + CRLF 
			cQuery += "    ON SF1.F1_FILIAL = SD1.D1_FILIAL" + CRLF 
			cQuery += "       AND SF1.F1_DOC = SD1.D1_DOC" + CRLF 
			cQuery += "	      AND SF1.D_E_L_E_T_ = ' '" + CRLF 
			cQuery += "LEFT JOIN "+RetSqlName("SM2")+" SM2" + CRLF 
			cQuery += "   ON M2_DATA = SF1.F1_EMISSAO" + CRLF 
			cQuery += "      AND SM2.D_E_L_E_T_ = ' '" + CRLF 
			cQuery += "WHERE SUBSTR(D1_EMISSAO, 1, 6) BETWEEN '"+cAnoMesIni+"' AND '"+cAnoMesFim+"' " + CRLF 
			cQuery += "      AND SD1.D_E_L_E_T_ = ' '" + CRLF 
			cQuery += "GROUP BY PEA_PROJET, SUBSTR(D1_EMISSAO, 1, 6)" + CRLF
			
			cQuery := ChangeQuery(cQuery)
			
			GrvResult(lJob, cAliTbTmp, cQuery, aCposField, cTimeIni)
		EndIf	
	Next
	
	//VOLTA PARA A FILIAL DE ORIGEM SE TROCOU
	If cEmpAnt <> cBkpEmpAnt .OR. cFilAnt <> cBkpFilAnt 
		U_SrvXEmpChange(cBkpEmpAnt,cBkpFilAnt)
		
		If !( Select( cAliTbTmp ) > 0 )
			MPSysOpenQuery( cTbTemp, cAliTbTmp)
		EndIf
	EndIf
EndIf

RestArea(aAreaSX3)
Return cTbTemp


/*/{Protheus.doc} GrvResult
(Grava temporario com os dados enviados de faturamento e cancelamentos mes a mes)
<AUTHOR>
@since 01/04/2016
@version 1.0
@param lJob      , Logico   , (Se est� sendo executado via job (.T.) ou remote (.F.))
@param cAliTbTmp , character, (Alias da tabela tempor�ria)
@param aCposField, array    , (Array com os campos criados na tabela temporaria)
@param cTimeIni  , character, (Hora de inicio do processo para Timer)
@example
(examples)
@see (links_or_references)
/*/

Static Function GrvResult(lJob, cAliTbTmp, cQuery, aCposField, cTimeIni, lRetFilFat)
Local cAliRep := GetNextAlias()
Local nTotReg := 0
Local nQtdVez := 0
Local nPosCpo := 0
Local nPosPrj := 0
Local aQtdPrj := {}
Local nX
Local nY

Default cTimeIni := Time()


For nY := 1 To Len(aCposField)
	AADD(aQtdPrj, {aCposField[nY][1], aCposField[nY][2], Nil})
Next

nPosPrj := aScan(aQtdPrj,{|x| Alltrim(x[1]) == "PROJETO"})


If Select(cAliRep) > 0
   (cAliRep)->(dbCloseArea())
EndIf

If !lJob
	oProcess:IncRegua2( STR0040 +"("+ElapTime(cTimeIni,Time())+")") //"Contando Registros: "
	cTimeIni := Time()
EndIf

nTotReg := U_CountQry(cQuery)

If !lJob
	oProcess:IncRegua2( STR0041 + "("+ElapTime(cTimeIni,Time())+")") //"Selecionando Reg.: "
	cTimeIni := Time()
EndIf

MPSysOpenQuery(cQuery,cAliRep)

dbSelectArea(cAliRep)


//COMPATIBILIZA OS CAMPOS DO RESULTSET
AEval( aCposField, {|x| IIf( x[2] != 'C', TcSetField( (cAliRep), AllTrim(x[1]), x[2], x[3], x[4] ), Nil ) } )

If !lJob
	oProcess:SetRegua2(0)
	oProcess:IncRegua2( " ("+ElapTime(cTimeIni,Time())+")" )
	cTimeIni := Time()
EndIf

(cAliRep)->(DbGoTop())

While (cAliRep)->(!EOF())
	If !lJob
		nQtdVez++
		oProcess:IncRegua2( STR0038 + StrZero(nQtdVez, 6) + " / " + StrZero(nTotReg, 6) + " ("+ElapTime(cTimeIni,Time())+")") //"Processando..."
	EndIf
	
	(cAliTbTmp)->( DBSetOrder(1) )
	If (cAliTbTmp)->( DbSeek((cAliRep)->(PROJETO+MESREF)) )
		lGrava := .F.
	Else
		lGrava := .T.
	EndIf
	
	//SE O PROJETO FOR DIFERENTE DO ANTERIOR ZERA O ARRAY DO ANTERIOR INTEIRO
	If aQtdPrj[nPosPrj][3] <> (cAliRep)->PROJETO
		For nY := 1 To Len(aCposField)
			If aQtdPrj[nY][2] <> "N"
				aQtdPrj[nY][3] := ""
			Else
				aQtdPrj[nY][3] := 0
			EndIf
		Next
		
		aQtdPrj[nPosPrj][3] := (cAliRep)->PROJETO
	EndIf
	
	RecLock(cAliTbTmp, lGrava)
	
	
	For nX := 1 To (cAliRep)->(FCOUNT())
		nPosCpo := aScan(aQtdPrj,{|x| Alltrim(x[1]) == Alltrim((cAliRep)->(FieldName(nX)))})
		
		If aQtdPrj[nPosCpo][2] <> "N"
			(cAliTbTmp)->( &((cAliRep)->(FieldName(nX))) ) := (cAliRep)->( &(Field(nX)) )
		Else
			//SEMPRE SOMA A QTD DO MES ANTERIOR
			aQtdPrj[nPosCpo][3] += (cAliRep)->( &(Field(nX)) )
			(cAliTbTmp)->( &((cAliRep)->(FieldName(nX))) ) := aQtdPrj[nPosCpo][3] 
		EndIf
	Next
	
	(cAliTbTmp)->( MsUnlock() )
	
	(cAliRep)->(DbSkip())
End	

(cAliRep)->(dbCloseArea())

Return

