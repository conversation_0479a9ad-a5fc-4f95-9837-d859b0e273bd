#include "Protheus.ch"

User Function TCPRO090()

Local lRet		:= .T.
Local cPodut 	:= FwFldGet("PLD_PRODUT")
Local cDescri	:= ""

If ! Empty(cPodut)
    
	cDescri	:=	Posicione("SB1",1,xFilial("SB1")+cPodut,"B1_DESC")                                                                     
	FWFldPut('PLD_DESCRI',alltrim(cDescri))
                                                                           
EndIf

Return (lRet)