#Include 'Protheus.ch
#INCLUDE 'FWMVCDEF.CH'


/*
Programa  	: TIRVAM02()
Objetivo    : Ponto de entrada da rotina de TIRVA002 - Indicadores RV
Retorno     : lRetVal
Data/Hora   : 09/09/2022 	
*/ 

User Function TIRVAM02()
	Local aParam    := PARAMIXB
	Local lRet      := .T.
	Local oActio	:= TIRVACTIO():New()

	If aParam <> NIL
		oMod := aParam[1]
		cIdPonto := aParam[2]
		cIdModel := aParam[3]

		If cIdPonto == 'MODELCOMMITNTTS'
			nOperation := oMod:GetOperation()
			// Verifica se nao existe mais linas no Grid da P85 e ZX5 RVCGC
			oModelMas:=oMod:getModel("P63MASTER")
			oModelP85:=oMod:getModel("P85DETAIL")
			oModelCGC:=oMod:getModel("CGCDETAIL")
			cIndica	:= oModelMas:GetValue("P63_CODIND")

			//Verifica se nao existe mais linhas nesses grids
			IF oModelP85:Length() ==1
				// Troca o Indicador .SUB NA P66 e envia o indicador sem o .SUB pois o valor agora muda
				IF Empty(Alltrim(oModelP85:GetValue("P85_CODCAT"))) .Or. oModelP85:isDeleted()
					oActio:cleanIndicadorSub(cIndica)
				eNDif
			EndIF
			//Envia uma PWK com valor zerado para os indicador .CNPJ e deleta da tabela o dado
			//ja deletado desse indicador para nao entrar em loop toda vez que alguem anterar o cadastro
			// Troca o Indicador .SUB NA P66 e envia o indicador sem o .SUB pois o valor agora muda
			IF oModelCGC:Length() == 1
				IF Empty( Alltrim(oModelCGC:GetValue("ZX5_CHAVE"))	 ) .Or. oModelCGC:isDeleted()
					oActio:cleanIndicadorApur(cIndica)
				EndIF
			EndIF
			If nOperation == MODEL_OPERATION_DELETE
				// Envia pra ACTIO esse indicador com valor 0
				oActio:RecAgrupaVlProd("", "", .F., .F., "", "", cIndica,cIndica,.T.) // cria pwk
				//troca  o Indicador que foi excluido da tabela P66 para o conteudo do parametro TI_RVIND
				oActio:cleanIndicadorP66(cIndica)
			EndIF

		EndIF

	ENDIF
	FreeObj( oActio )
	oActio:= Nil
Return lRet
