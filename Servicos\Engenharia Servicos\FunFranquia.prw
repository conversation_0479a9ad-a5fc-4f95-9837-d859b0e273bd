#Include "TOTVS.CH"

//---------------------------------------------------------------------------------------
/*/{Protheus.doc} fMaxRecno
@protected
<AUTHOR>         
@version       P10 R1.3
/*/
//---------------------------------------------------------------------------------------

user function fMaxRecno(Tabela,FiltroSql,cCodEmp,cCodFil)

Local lOk		:= .T.
Local nI		:= 0
Local cRetJSON	:= ""
Local cCpoQry	:= ""
Local aCpoQry	:= {}
Local cCpoJSon	:= ""
Local cCpoAux	:= ""
Local uConteudo	:= ""
Local cQuery	:= ""
Local cData		:= ""
Local cMsg		:= ""
Local cAliasTRB	:= getNextAlias()
Local lTabSM0	:= .F.
Local cAliasTab	:= SubStr(Tabela,1,3)
Local cTipoDB	:= AllTrim(Upper(TCGETDB()))
Local nQtdReg	:= 0
Local nUltRecno	:= 0
Local lConcluido:= .T.
Local aAreaSM0	:= {}
Local cQryAgrp	:= ""
Local cErrChv	:= ""
Local cUnSrv	:= ""
Local cTabPrj 	:= getMV("TI_PTABFRQ",,"PE6/PE7/PE8/PE9/PEA/PEB/PEC/PG8/PG9")		// POR PROJETO (a tabela PE5 nao pode conter)
Local cTabOS  	:= getMV("TI_OTABFRQ",,"PF8/PF9/PFF")		
Local cRelJoin := ''						
Local nMaxRec	:= 0
Local oObj 	:= nil
Local cJson 	:= nil
Local cTabNFil	:= getMV("TI_NTABFIL",,"PE5/PF9")
Local cTabCond  := getMV("TI_FRQCTAB",,"PE5")
Local nPosX2Arq := 0
Local nPosX2Mod := 0
Local nPosX2Chv := 0
Local cSX2TMP   := GetNextAlias()

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ PEGA UNIDADE DE SERVICO PELO EMPRESA/FILIAL FRANQUIA   ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
PE2->(dbSetOrder(2))
if PE2->(dbSeek( xfilial('PE2') + cCodEmp + cCodFil  ))
    while !PE2->(eof()) .and. PE2->PE2_EMPFIL == cCodEmp + cCodFil
        cUnSrv += PE2->PE2_UNSRV + ';'	
        PE2->(dbSkip())
    end
    if right(cUnSrv,1) == ';'
        cUnSrv := left(cUnSrv,len(cUnSrv)-1)
    endif
endif

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ REALIZA SELECAO DE DADOS    ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ

OPENSXS(NIL, NIL, NIL, NIL, cEmpAnt, cSX2TMP, "SX2", NIL, .F.)
(cSX2TMP)->(DBSetOrder(1))	
(cSX2TMP)->(DBSeek(left(Tabela,3)))
nPosX2Arq := (cSX2TMP)->(FieldPos("X2_ARQUIVO"))
nPosX2Mod := (cSX2TMP)->(FieldPos("X2_MODO"))
nPosX2Chv := (cSX2TMP)->(FieldPos("X2_CHAVE")) 

cQuery := "SELECT MAX(" + left(Tabela,3) + ".R_E_C_N_O_) AS MAXREC "
cQuery += "	FROM 	" + AllTrim((cSX2TMP)->( FieldGet(nPosX2Arq))) + ' ' + left(Tabela,3)

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ TRATAMENTO PARA TABELA PE5/PF9  BUSCAR SOMENTE DA UNIDADE DE SERVICO RELACIONADO A FRANQUIA	 ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
if left(Tabela,3) $ cTabPrj
    cRelJoin += " PE5_PROJET = " + left(Tabela,3) + '_PROJET' + " "
    if ! empty(cRelJoin)
        cQuery += " JOIN "+RetSqlName("PE5")+" PE5 " 
        cQuery += "   ON "  
        cQuery += cRelJoin  
        cQuery += "  AND PE5_UNSRV IN " + FormatIn(cUnSrv,';') 
        cQuery += "  AND PE5.D_E_L_E_T_ = '' " + CRLF
    endif
/*elseif left(Tabela,3) $ cTabOS
    cRelJoin += " PF9_IDOS = " + left(Tabela,3) + '_IDOS' + " "
    if ! empty(cRelJoin)
        if left(Tabela,3) <> 'PF9'
            cQuery += " JOIN "+RetSqlName("PF9")+" PF9 ON " + cRelJoin + " AND PF9.D_E_L_E_T_ = '' " + CRLF
        endif
        cQuery += " JOIN "+RetSqlName("RD0")+" RD0 ON  RD0_CODIGO = PF9_CODTEC AND RD0_XUNSRV IN "+FormatIn(cUnSrv,';')+" AND RD0.D_E_L_E_T_ = '' " + CRLF
    endif*/
endif

cQuery += "	WHERE "

if left(Tabela,3) $ cTabOS .and. left(Tabela,3) == 'PF9'
    cQuery += "  ( " + CRLF
    cQuery += " 		( PF9_CODTEC IN ( "
    cQuery += "                           SELECT RD0_CODIGO "
    cQuery += "                             FROM "+RetSqlName("RD0")+ " RD0 "
    cQuery += "                            WHERE RD0_FILIAL = '"+xfilial(left(Tabela,3))+"' "
    cQuery += "                              AND RD0_XUNSRV IN "+FormatIn(cUnSrv,';')  
    cQuery += "                              AND RD0.D_E_L_E_T_ = '' ) ) " + CRLF
    cQuery += " 	OR " + CRLF
    cQuery += " 		( PF9_GRDEST = '"+cCodEmp+"' AND PF9_FILDES = '"+cCodFil+"' ) " + CRLF
    cQuery += " 	) " + CRLF
endif

if (cSX2TMP)->(dbSeek(left(Tabela,3))) .and. !( left(Tabela,3) $ cTabNFil )
    if (cSX2TMP)->(FieldGet(nPosX2Mod)) == 'C'
        cQuery += "	 " + iif( left((cSX2TMP)->(FieldGet(nPosX2Chv)),1) == 'S' , substr((cSX2TMP)->(FieldGet(nPosX2Chv)),2,2) , (cSX2TMP)->(FieldGet(nPosX2Chv))) + "_FILIAL = '"+xfilial(left(Tabela,3))+"' "
    else
        cQuery += "	 " + iif( left((cSX2TMP)->(FieldGet(nPosX2Chv)),1) == 'S' , substr((cSX2TMP)->(FieldGet(nPosX2Chv)),2,2) , (cSX2TMP)->(FieldGet(nPosX2Chv))) + "_FILIAL = '"+cCodFil+"' "  
    endif
endif


//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ TRATAMENTO FILTRO ADICIONAL  ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
If	!Empty(FiltroSql)
    //cQuery += iif( left(Tabela,3) $ cTabPrj .or. left(Tabela,3) $ alltrim(cTabCond) , " " + FiltroSql , " AND " + FiltroSql ) 
    cQuery += iif( left(Tabela,3) $ alltrim(cTabCond) , " " + FiltroSql , " AND " + FiltroSql ) 
EndIf

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ TRATAMENTO PARA TABELA PE5 PROJETOS BUSCAR SOMENTE DA UNIDADE DE SERVICO RELACIONADO A FRANQUIA ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
if alltrim(upper(Tabela)) == 'PE5' .and. !empty(cUnSrv)
    cQuery += " AND PE5_UNSRV IN "+FormatIn(cUnSrv,';')+" " + CRLF
endif 

memoWrite('\temp\FRQREST_MAX_' + cCodEmp + cCodFil + '.txt',cQuery)

cQuery := ChangeQuery(cQuery)
MPSysOpenQuery( cQuery, cAliasTrb )
    
nMaxRec := (cAliasTrb)->MAXREC
(cAliasTrb)->(DBCloseArea())
(cSX2TMP)->(DBCloseArea())

cMsg	:= 'ok' 
oObj 	:= Franquia():maxRecno(cMsg,nMaxRec)
cJson 	:= FWJsonSerialize(oObj,.F.,.F.)

return cJson

//---------------------------------------------------------------------------------------
/*/{Protheus.doc} fTotalRange
@protected
<AUTHOR>         
@version       P10 R1.3
/*/
//---------------------------------------------------------------------------------------

user function fTotalRange(Tabela,FiltroSql,cCodEmp,cCodFil)

Local lOk		:= .T.
Local nI		:= 0
Local cRetJSON	:= ""
Local cCpoQry	:= ""
Local aCpoQry	:= {}
Local cCpoJSon	:= ""
Local cCpoAux	:= ""
Local uConteudo	:= ""
Local cQuery	:= ""
Local cData		:= ""
Local cMsg		:= ""
Local cAliasTRB	:= getNextAlias()
Local lTabSM0	:= .F.
Local cAliasTab	:= SubStr(Tabela,1,3)
Local cTipoDB	:= AllTrim(Upper(TCGETDB()))
Local nQtdReg	:= 0
Local nUltRecno	:= 0
Local lConcluido:= .T.
Local aAreaSM0	:= {}
Local cQryAgrp	:= ""
Local cErrChv	:= ""
Local cUnSrv	:= ""
Local cTabPrj 	:= getMV("TI_PTABFRQ",,"PE6/PE7/PE8/PE9/PEA/PEB/PEC/PG8/PG9")		// POR PROJETO (a tabela PE5 nao pode conter)
Local cTabOS  	:= getMV("TI_OTABFRQ",,"PF8/PF9/PFF")		
Local cRelJoin := ''						
Local nMaxRec	:= 0
Local nTotal	:= 0
Local oObj 	:= nil
Local cJson 	:= nil
Local cTabNFil	:= getMV("TI_NTABFIL",,"PE5/PF9")
Local cTabCond  := getMV("TI_FRQCTAB",,"PE5")
Local nPosX2Arq := 0
Local nPosX2Mod := 0
Local nPosX2Chv := 0
Local cSX2TMP   := GetNextAlias()

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ PEGA UNIDADE DE SERVICO PELO EMPRESA/FILIAL FRANQUIA   ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
PE2->(dbSetOrder(2))
if PE2->(dbSeek( xfilial('PE2') + cCodEmp + cCodFil  ))
    while !PE2->(eof()) .and. PE2->PE2_EMPFIL == cCodEmp + cCodFil
        cUnSrv += PE2->PE2_UNSRV + ';'	
        PE2->(dbSkip())
    end
    if right(cUnSrv,1) == ';'
        cUnSrv := left(cUnSrv,len(cUnSrv)-1)
    endif
endif

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ REALIZA SELECAO DE DADOS    ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
OPENSXS(NIL, NIL, NIL, NIL, cEmpAnt, cSX2TMP, "SX2", NIL, .F.)
(cSX2TMP)->(DBSetOrder(1))	
(cSX2TMP)->(DBSeek(left(Tabela,3)))
nPosX2Arq := (cSX2TMP)->(FieldPos("X2_ARQUIVO"))
nPosX2Mod := (cSX2TMP)->(FieldPos("X2_MODO"))
nPosX2Chv := (cSX2TMP)->(FieldPos("X2_CHAVE")) 

cQuery := "SELECT COUNT(" + left(Tabela,3) + ".R_E_C_N_O_) AS TOTAL "
cQuery += "	FROM 	" + AllTrim((cSX2TMP)->( FieldGet(nPosX2Arq))) + ' ' + left(Tabela,3)

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ TRATAMENTO PARA TABELA PE5/PF9  BUSCAR SOMENTE DA UNIDADE DE SERVICO RELACIONADO A FRANQUIA	 ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
if left(Tabela,3) $ cTabPrj
    cRelJoin += " PE5_PROJET = " + left(Tabela,3) + '_PROJET' + " "
    if ! empty(cRelJoin)
        cQuery += " JOIN "+RetSqlName("PE5")+" PE5 " 
        cQuery += "   ON " 
        cQuery += cRelJoin  
        cQuery += "  AND PE5_UNSRV IN "+FormatIn(cUnSrv,';')
        cQuery += "  AND PE5.D_E_L_E_T_ = '' " + CRLF
    endif
/*elseif left(Tabela,3) $ cTabOS
    cRelJoin += " PF9_IDOS = " + left(Tabela,3) + '_IDOS' + " "
    if ! empty(cRelJoin)
        if left(Tabela,3) <> 'PF9'
            cQuery += " JOIN "+RetSqlName("PF9")+" PF9 ON " + cRelJoin + " AND PF9.D_E_L_E_T_ = '' " + CRLF
        endif
        cQuery += " JOIN "+RetSqlName("RD0")+" RD0 ON  RD0_CODIGO = PF9_CODTEC AND RD0_XUNSRV IN "+FormatIn(cUnSrv,';')+" AND RD0.D_E_L_E_T_ = '' " + CRLF
    endif*/
endif

cQuery += "	WHERE "

if left(Tabela,3) $ cTabOS .and. left(Tabela,3) == 'PF9'
    cQuery += " ( " + CRLF
    cQuery += " 		( PF9_CODTEC IN ( 
    cQuery += "                          SELECT RD0_CODIGO 
    cQuery += "                            FROM "+RetSqlName("RD0")+" RD0 " 
    cQuery += "                           WHERE RD0_FILIAL = '"+xfilial(left(Tabela,3))+"' "
    cQuery += "                             AND RD0_XUNSRV IN "+FormatIn(cUnSrv,';')
    cQuery += "                             AND RD0.D_E_L_E_T_ = '' ) ) " + CRLF
    cQuery += " 	OR " + CRLF
    cQuery += " 		( PF9_GRDEST = '"+cCodEmp+"' AND PF9_FILDES = '"+cCodFil+"' ) " + CRLF
    cQuery += " 	) " + CRLF
endif

if (cSX2TMP)->(dbSeek(left(Tabela,3))) .and. !( left(Tabela,3) $ cTabNFil )
    if (cSX2TMP)->(FieldGet(nPosX2Mod)) == 'C'
        cQuery += "	 " + iif( left((cSX2TMP)->(FieldGet(nPosX2Chv)),1) == 'S' , substr((cSX2TMP)->(FieldGet(nPosX2Chv)),2,2) , (cSX2TMP)->(FieldGet(nPosX2Chv))) + "_FILIAL = '"+xfilial(left(Tabela,3))+"' "
    else
        cQuery += "	 " + iif( left((cSX2TMP)->(FieldGet(nPosX2Chv)),1) == 'S' , substr((cSX2TMP)->(FieldGet(nPosX2Chv)),2,2) , (cSX2TMP)->(FieldGet(nPosX2Chv))) + "_FILIAL = '"+cCodFil+"' "  
    endif
endif

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ TRATAMENTO FILTRO ADICIONAL  ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
If	!Empty(FiltroSql)
    //cQuery += iif( left(Tabela,3) $ cTabPrj .or. left(Tabela,3) $ alltrim(cTabCond) , " " + FiltroSql , " AND " + FiltroSql ) 
    cQuery += iif( left(Tabela,3) $ alltrim(cTabCond) , " " + FiltroSql , " AND " + FiltroSql )
EndIf

//ÚÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ¿
//³ TRATAMENTO PARA TABELA PE5 PROJETOS BUSCAR SOMENTE DA UNIDADE DE SERVICO RELACIONADO A FRANQUIA ³
//ÀÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ
if alltrim(upper(Tabela)) == 'PE5' .and. !empty(cUnSrv)
    cQuery += " AND PE5_UNSRV IN "+FormatIn(cUnSrv,';')+" " + CRLF
endif 

memoWrite('\temp\FRQREST_TOTAL_' + cCodEmp + cCodFil + '.txt',cQuery)

cQuery := ChangeQuery(cQuery)
MPSysOpenQuery( cQuery, cAliasTrb )
    
nTotal := (cAliasTrb)->TOTAL
(cAliasTrb)->(DBCloseArea())
(cSX2TMP)->(DBCloseArea())

cMsg	:= 'ok'
oObj 	:= Franquia():totalRange(cMsg,nTotal)
cJson 	:= FWJsonSerialize(oObj,.F.,.F.)


return cJson

//---------------------------------------------------------------------------------------
/*/{Protheus.doc} fBuscaDados
@protected
<AUTHOR>         
@version       P10 R1.3
/*/
//---------------------------------------------------------------------------------------

user function fBuscaDados(Tabela,Campos,FiltroSql,Limite,cCodEmp,cCodFil)

Local lOk		:= .T.
Local nI		:= 0
Local cRetJSON	:= ""
Local cCpoQry	:= ""
Local aCpoQry	:= {}
Local cCpoJSon	:= ""
Local cCpoAux	:= ""
Local uConteudo	:= ""
Local cQuery	:= ""
Local cData		:= ""
Local cMsg		:= ""
Local cAliasTRB	:= getNextAlias()//"BUSCADADOS"
Local lTabSM0	:= .F.
Local cAliasTab	:= SubStr(Tabela,1,3)
Local cTipoDB	:= AllTrim(Upper(TCGETDB()))
Local nQtdReg	:= 0
Local nUltRecno	:= 0
Local lConcluido:= .T.
Local aAreaSM0	:= {}
Local cQryAgrp	:= ""
Local cErrChv	:= ""
Local cUnSrv	:= ""
Local cTabPrj 	:= getMV("TI_PTABFRQ",,"PE6/PE7/PE8/PE9/PEA/PEB/PEC/PG8/PG9")		// POR PROJETO (a tabela PE5 nao pode conter)
Local cTabOS  	:= getMV("TI_OTABFRQ",,"PF8/PF9/PFF")
Local cTabNFil	:= getMV("TI_NTABFIL",,"PE5/PF9")
Local cRelJoin	:= ''
Local cCpoRec	:= ''
Local cTabCond  := getMV("TI_FRQCTAB",,"PE5")
Local _aRetSx3  := {}
Local nPosX2Arq := 0
Local nPosX2Mod := 0
Local nPosX2Chv := 0
Local cSX2TMP   := GetNextAlias()

//
// PEGA UNIDADE DE SERVICO PELO EMPRESA/FILIAL FRANQUIA   
//
PE2->(dbSetOrder(2))
if PE2->(dbSeek( xfilial('PE2') + cCodEmp + cCodFil  ))
    while !PE2->(eof()) .and. PE2->PE2_EMPFIL == cCodEmp + cCodFil
        cUnSrv += PE2->PE2_UNSRV + ';'	
        PE2->(dbSkip())
    end
    if right(cUnSrv,1) == ';'
        cUnSrv := left(cUnSrv,len(cUnSrv)-1)
    endif
endif

Campos	:= Upper(AllTrim(Campos))

cCpoRec := Campos

If	SubStr(Campos,Len(Campos),1) != ","
    Campos += ","
EndIf

//
// Verifica os campos a serem retornados.                                   
//
For nI := 1 To Len(Campos)
    
    If	SubStr(Campos,nI,1) != ","
        cCpoAux += SubStr(Campos,nI,1)
    Else
        //
        // Verifica se o campo existe.                                              
        //
        DBSelectArea("SX3")
        SX3->(DBSetOrder(2))
        If	SX3->(DBSeek(cCpoAux)) 
             //Se campo for virtual nao retorna          
            If GetSx3Cache(cCpoAux,"X3_CONTEXT") == 'V'
                Loop
            EndIf
            
            aAdd(aCpoQry,{cCpoAux,GetSx3Cache(cCpoAux,"X3_TIPO")})
            
            cCpoJSon += '"'+cCpoAux+'",'
            
            cCpoQry += cCpoAux+","
            
        EndIf
        cCpoAux := ""
    EndIf
Next nI

If	SubStr(cCpoQry,Len(cCpoQry),1) == ","
    cCpoQry := SubStr(cCpoQry,1,Len(cCpoQry)-1)
EndIf

If	SubStr(cCpoJSon,Len(cCpoJSon),1) == ","
    cCpoJSon := SubStr(cCpoJSon,1,Len(cCpoJSon)-1)
EndIf

cCpoJSon += ',"R_E_C_N_O_"'

//
// REALIZA SELECAO DE DADOS    
//
cQuery := "SELECT "

if	"SQL" $ cTipoDB
    if Limite <> '*'
        cQuery += "TOP " + Limite
    endif
endif

//
// SO TRAZ A COLUNA RECNO    
//
if cCpoRec == 'R_E_C_N_O_'
    cQuery += " "+ left(Tabela,3) +".R_E_C_N_O_ "
else
    cQuery += cCpoQry +", " + " "+ left(Tabela,3) +".R_E_C_N_O_ " + ", " + left(Tabela,3) +".R_E_C_D_E_L_ "
endif

OPENSXS(NIL, NIL, NIL, NIL, cEmpAnt, cSX2TMP, "SX2", NIL, .F.)
(cSX2TMP)->(DBSetOrder(1))	
(cSX2TMP)->(DBSeek(left(Tabela,3)))
nPosX2Arq := (cSX2TMP)->(FieldPos("X2_ARQUIVO"))
nPosX2Mod := (cSX2TMP)->(FieldPos("X2_MODO"))
nPosX2Chv := (cSX2TMP)->(FieldPos("X2_CHAVE")) 

cQuery += "	FROM 	" + AllTrim((cSX2TMP)->(FieldGet(nPosX2Arq))) + ' ' + left(Tabela,3)

//
// TRATAMENTO PARA TABELA PE5/PF9  BUSCAR SOMENTE DA UNIDADE DE SERVICO RELACIONADO A FRANQUIA	 
//
if left(Tabela,3) $ cTabPrj
    cRelJoin += " PE5_PROJET = " + left(Tabela,3) + '_PROJET' + " "
    if ! empty(cRelJoin)
        cQuery += " JOIN "+RetSqlName("PE5")+" PE5 " 
        cQuery += "   ON " 
        cQuery += cRelJoin 
        cQuery +=  " AND PE5_UNSRV IN "+FormatIn(cUnSrv,';')
        cQuery += "  AND PE5.D_E_L_E_T_ = '' " + CRLF
    endif
/*elseif left(Tabela,3) $ cTabOS
    cRelJoin += " PF9_IDOS = " + left(Tabela,3) + '_IDOS' + " "
    if ! empty(cRelJoin)
        if left(Tabela,3) <> 'PF9'
            cQuery += " JOIN "+RetSqlName("PF9")+" PF9 ON " + cRelJoin + " AND PF9.D_E_L_E_T_ = '' " + CRLF
        endif
        cQuery += " JOIN "+RetSqlName("RD0")+" RD0 ON  RD0_CODIGO = PF9_CODTEC AND RD0_XUNSRV IN "+FormatIn(cUnSrv,';')+" AND RD0.D_E_L_E_T_ = '' " + CRLF
    endif*/
endif

cQuery += "	WHERE "

if left(Tabela,3) $ cTabOS .and. left(Tabela,3) == 'PF9'
    cQuery += " ( " + CRLF
    cQuery += " 		( PF9_CODTEC IN ( 
    cQuery += "                          SELECT RD0_CODIGO "
    cQuery += "                            FROM "+RetSqlName("RD0")+" RD0 " 
    cQuery += "                           WHERE RD0_FILIAL = '"+xfilial(left(Tabela,3))+"' " 
    cQuery += "                             AND RD0_XUNSRV IN "+FormatIn(cUnSrv,';')
    cQuery += "                             AND RD0.D_E_L_E_T_ = '' ) ) " + CRLF
    cQuery += " 	OR " + CRLF
    cQuery += " 		( PF9_GRDEST = '"+cCodEmp+"' AND PF9_FILDES = '"+cCodFil+"' ) " + CRLF
    cQuery += " 	) " + CRLF
endif

if (cSX2TMP)->(dbSeek(left(Tabela,3))) .and. !( left(Tabela,3) $ cTabNFil )
    if (cSX2TMP)->(FieldGet(nPosX2Mod)) == 'C'
        cQuery += "	 " + iif( left((cSX2TMP)->(FieldGet(nPosX2Chv)),1) == 'S' , substr((cSX2TMP)->(FieldGet(nPosX2Chv)),2,2) , (cSX2TMP)->(FieldGet(nPosX2Chv))) + "_FILIAL = '"+xfilial(left(Tabela,3))+"' "
    else
        cQuery += "	 " + iif( left((cSX2TMP)->(FieldGet(nPosX2Chv)),1) == 'S' , substr((cSX2TMP)->(FieldGet(nPosX2Chv)),2,2) , (cSX2TMP)->(FieldGet(nPosX2Chv))) + "_FILIAL = '"+cCodFil+"' "  
    endif
endif

//
// TRATAMENTO FILTRO ADICIONAL  
//
if	!Empty(FiltroSql)
    //cQuery += iif( left(Tabela,3) $ cTabPrj .or. left(Tabela,3) $ alltrim(cTabCond) , " " + FiltroSql , " AND " + FiltroSql ) 
     cQuery += iif( left(Tabela,3) $ alltrim(cTabCond) , " " + FiltroSql , " AND " + FiltroSql )
endif

//
// TRATAMENTO PARA TABELA PE5 PROJETOS BUSCAR SOMENTE DA UNIDADE DE SERVICO RELACIONADO A FRANQUIA 
//
if left(Tabela,3) == 'PE5' .and. !empty(cUnSrv)
    cQuery += " AND PE5_UNSRV IN "+FormatIn(cUnSrv,';')+" " + CRLF
endif 

if	"ORACLE" $ cTipoDB
    if Limite <> '*'
        cQuery += "	AND ROWNUM <= "+Limite
    endif
endif

cQuery += "	ORDER BY "+ left(Tabela,3) +".R_E_C_N_O_ "

memoWrite('\temp\FRQREST_BUSCADADOS_' + cCodEmp + cCodFil + '.txt',cQuery)

cQuery := ChangeQuery(cQuery)
MPSysOpenQuery( cQuery, cAliasTrb )

dbSelectArea(cAliasTrb)
(cAliasTrb)->(dbGoTop()) 

cRetJSON := '{'
cRetJSON += '"table": [' /* "Header": ['+cCpoJSon+']},' */

If	!(cAliasTrb)->(Eof())

    While	!(cAliasTrb)->(Eof())
            
            cData := '{ '
            
            For nI := 1 To Len(aCpoQry)
                
                If	nI > 1
                    cData += ','
                EndIf
                
                cCpoAux := aCpoQry[nI][01]//"("+cAliasTrb+")->"+
                uConteudo := &(cCpoAux)
                
                cData += '"' + aCpoQry[nI][01]+'": '
                
                Do Case
                    
                    Case aCpoQry[nI][02] == "N" 
                        cData +=  AllTrim(Str(uConteudo))
                    OtherWise
                        cData += '"'+RTrim(FmtJson(uConteudo))+'"'
                
                EndCase
                
            Next nI
            
            //
            // SO TRAZ A COLUNA RECNO    
            //
            if cCpoRec == 'R_E_C_N_O_'
                cData += '"R_E_C_N_O_":'+AllTrim(Str((cAliasTrb)->R_E_C_N_O_)) + ' },'
            else 
                cData += ',"R_E_C_N_O_":'	+AllTrim(Str((cAliasTrb)->R_E_C_N_O_))
                cData += ',"R_E_C_D_E_L_":'	+AllTrim(Str((cAliasTrb)->R_E_C_D_E_L_))+ ' },'
            endif
            
            //cRetJSON += '{"Data":['+cData+']},'
            cRetJSON += cData
            //ConOut(cData)
            
            nQtdReg++
            
            //
            // Verifica se o registro e o ultimo do limite da paginacao.                
            //
            
            if Limite <> '*'
            
                if	nQtdReg == val(Limite)
                    
                    nUltRecno := (cAliasTrb)->R_E_C_N_O_
                    
                    (cAliasTrb)->(DBSkip())
                    
                    if	!(cAliasTrb)->(eof())
                        lConcluido := .F.
                    endif
                    
                    Exit
                    
                endif
            endif
            
        (cAliasTrb)->(DBSkip())
    
    EndDo
    
    
    If	SubStr(cRetJSON,Len(cRetJSON),1) == ","
        cRetJSON := SubStr(cRetJSON,1,Len(cRetJSON)-1)
    EndIf

EndIf

(cAliasTrb)->(dbCloseArea())
(cSX2TMP)->(DBCloseArea())

cRetJSON += '],'
cRetJSON += '"concluido": "'+IIf(lConcluido,'true','false')+'"	,'
cRetJSON += '"ultimorecno": '+AllTrim(Str(nUltRecno))
cRetJSON += '}'

return cRetJSON

//---------------------------------------------------------------------------------------
/*/{Protheus.doc} FmtJson
@protected
<AUTHOR>         
@version       P10 R1.3
/*/
//---------------------------------------------------------------------------------------

Static Function FmtJson(cString)

Local cRet	:= cString

cRet := strtran(cRet,'\','\\')
cRet := strtran(cRet,'"','\"')
cRet := strtran(cRet,chr(9),'\t')

Return cRet
