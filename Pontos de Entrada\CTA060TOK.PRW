#INCLUDE "PROTHEUS.CH"
#INCLUDE "TOTVS.CH"

/*/{Protheus.doc} CTA060TOK
Tudo Ok Classe de Valor
<AUTHOR>            
@since 26/09/2017
/*/
user function CTA060TOK()
Local lRet      := .T.
Local nOpc      := PARAMIXB
Local lHbIntPSA	:= getMv('TI_#HCAPSA',,.F.) //getMv('TI_#HABPSA',,.F.)
Local cIdClVl	:= ''
Local cMethod	:= ''
Local oGeneric

// INTEGRACAO PSA SERVICOS
if lHbIntPSA .and. findFunction("u_inJobPSA")
	
    aDados  := {}
	aClVl   := {}
	aClVl   := {  M->CTH_CLVL , alltrim(M->CTH_DESC01) }
	oGeneric:= GenericoPSA():New()

	// SET OS VALORES PARA BUSCAR O ID
	oGeneric:setEntityID('ctm_classedevalors')		// entidade/metodo
	oGeneric:setParamID('ctm_codigodoprotheus')		// campo para busca
	oGeneric:setPropID('ctm_classedevalorId')		// campo de retorno ID
	oGeneric:setValueID( M->CTH_CLVL )				// valor para busca
	cIdClVl := oGeneric:GetID()						// metodo que retorna os ID, conforme parametros setados acima

	if !( upper( left(cIdClVl,4) ) == 'ERRO' ) .and. !empty(cIdClVl)
        aadd(aClVl,cIdClVl)
        if nOpc == 3 .or. nOpc == 4
            cMethod := 'PutClVl'
        else
            cMethod := 'DeleteClVl'
        endif
	else
		cMethod := 'PostClVl'
	endif

	aadd(aDados,aClVl)
	aadd(aDados,CTH->(recno()))
	aadd(aDados,__cUserID)

	//u_inJobPSA(SM0->M0_CODIGO,SM0->M0_CODFIL,{'CTT'},aDados, cMethod, .F. )
	StartJob( "u_inJobPSA", GetEnvServer(), .F., SM0->M0_CODIGO,SM0->M0_CODFIL,{'CTH'},aDados, cMethod  )
endif

return lRet