#INCLUDE 'TOTVS.CH'


/*/{Protheus.doc} ESPG060
	Description

	<AUTHOR>
	@example Example
	@param   [Parameter_Name],Parameter_type,Parameter_Description
	@return  cRetorno
	@table   Tables
	@since   25-08-2020
/*/
User Function ESPG060()	
	Local cTipo    := ''
	Local cChave   := ''
	Local cRetorno := ''
	Local cCodAC   := ''
	Local cCodPS   := ''
	Local oModel   := FWModelActive()
	
	cCodPS := oModel:GetValue('PVAMASTER','PVA_PAIS')
	cTipo  := oModel:GetValue('PVQ','PVQ_TIPO')
	cCodAC := oModel:GetValue('PVQ','PVQ_CODACE')
	cChave := FWxFilial('PVQ') + cCodPS + cCodAC
	
	IF cTipo $ '123'
		cAlias   := 'PZ' + cTipo
		cRetorno := Posicione(cAlias, 1, cChave, cAlias + '_DESACE')
	ELSE
		IF cTipo == '4'
			cTipo := 'G'
		ELSEIF cTipo == '5'
			cTipo := '4'
		ELSEIF cTipo == '6'
			cTipo 	:= 'I'
		ELSEIF cTipo == '7'
			cTipo	:= 'K'
		EndIF				
		cAlias   := 'PJ' + cTipo
		cRetorno := Posicione(cAlias, 2, cChave, cAlias + '_DESACE')		
	EndIF	
Return(cRetorno)
