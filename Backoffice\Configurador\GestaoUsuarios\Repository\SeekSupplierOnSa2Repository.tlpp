#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Repository.SeekSupplierOnSa2Repository

/*/{Protheus.doc} SeekSupplierOnSa2
A Static Method with the purpose of execute the function dbSeek() 
on the Table SA2, the table of Supplier
@type class
@version  1.0
<AUTHOR>
@since 10/25/2024
/*/
Class   SeekSupplierOnSa2
  Static    Method  executeSeek(cCodeSupplier   As  Character, cStoreSupplier   As  Character)
EndClass

/*/{Protheus.doc} SeekSupplierOnSa2::executeSeek
This method use the two parameters and execute the function
dbSeek on Table SA2 using the order "1", Filial+Code+Store
@type method
@version 1.0 
<AUTHOR>
@since 10/25/2024
@param cCodeSupplier, character,  The Code of Supplier on Table SA2, A2_LOJA
@param cStoreSupplier, character, The Store of the Supplier on Table SA2, A2_LOJA
@return Logical, A Logical Result, .T. if the DbSeek finds the register using the income parameters otherwise .F.
/*/
Method  executeSeek(cCodeSupplier   As  Character, cStoreSupplier   As  Character)   Class   SeekSupplierOnSa2
  Local lSeekOnSa2  :=  .F.   As  Logical
  DbSelectArea("SA2")
  DbSetOrder(1)
  If(DbSeek(xFilial("SA2")+cCodeSupplier+cStoreSupplier))
    lSeekOnSa2  :=  .T.
  Else
    lSeekOnSa2  :=  .F.
  EndIf
Return  lSeekOnSa2
