﻿#Include 'Protheus.ch'
#INCLUDE "TOPCONN.CH"
#Include "FILEIO.ch"

Static cRetConXb

/*/{Protheus.doc} TFCRR002
Relatorio de Titulos Vencidos
@type function
<AUTHOR>
@since 30/04/2020
@version P12.25
@return ${return}, ${return_description}
@example(examples)
@see (links_or_references)
@history 23/07/2015, <PERSON>, (Relatorio de Titulos vencidos - P12) - <PERSON><PERSON><PERSON> original
/*/

User Function TFCRR002()
Local cCRR002X :=SuperGetMV("TI_CRR002X",,"NNNSNN") //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
Local lJob     := .F. //(SuperGetMV("TI_CRR002V",,"1")=="1")  //Versao (1=StartJob,2=Tela)
Local cPerg    :="TFCRR002C"
Local cExcelArq:="\system\tfcrr002_csv\"
Local cExcelTmp:="c:\temp\"
Local cExcelNom:=__cUserId+DtoS(Date())+StrTran(Time(),":","")+".dbf"

Private TMV_PAR21  := "" //Tipo Titulo

If Subs(cCRR002X,1,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
	If !MsgYesNo("MODO_DEBUG: Confirma?")
		Return
	EndIf
EndIf

While fGerados(cExcelArq,cExcelTmp)  //Verifica se tem arquivos gerados para o usuário
End

AjustaSX1(cPerg)
If Pergunte(cPerg,.T.)
	fGeraXLS(cPerg,cExcelArq,cExcelTmp,cExcelNom,lJob,cCRR002X)  //lJob: .t.=StartJob,.f.=Tela
EndIf

Return
//-----------------------------------------------------------------------------

Static Function fGeraXLS(cPerg,cExcelArq,cExcelTmp,cExcelNom,lJob,cCRR002X)  //Executa StartJob: .t.=Sim,.f.=Não
Local __aSelFil  := {}
Local nI         := 0
Local aEmpSm0    := {}
Local TMV_PAR01  := "" //Filial
Local TMV_PAR16  := "" //Portador
Local TMV_PAR19  := "" //Situacao

Local oObjp		:= FWSX1Util():New()
Local aPergunte := {} 
Local aPerg    :={}
Local nPerg    := 0

If !ExistDir(cExcelArq)
	MakeDir(cExcelArq)
EndIf

oObjp:addGroup(cPerg)
oObjp:SearchGroup()
aPergunte := oObjp:GetGroup(cPerg)


For nPerg := 1 to len(aPergunte[2])
	AAdd(aPerg,{"MV_PAR"+StrZero(nPerg,2),&("MV_PAR"+StrZero(nPerg,2))})
Next nPerg

oObjp:Destroy()
FreeObj(oObjp)

//-----------------------------------------------------------------------------
// Prepara as variáveis com Selecao de registros - Início
//-----------------------------------------------------------------------------
If MV_PAR01 == 2 //Filtra Filiais ?: 1=Todas; 2=Selecao; 3=Filial Atual
	U_FR002P02(1)  //Tela para Selecao (1=Empresas,2=Tipo de cliente,3=Bancos ou portadores,4=SituaCões)
	TMV_PAR01 := StrTran(cRetConXb, " ", "")  //cRetConXb: Variável Static !!
	__aSelFil := Strtokarr( TMV_PAR01, ";")
ElseIf MV_PAR01 == 1 //Filtra Filiais ?: 1=Todas; 2=Selecao; 3=Filial Atual
	aEmpSm0 := FR002P05()  //Carrega array com as filias da empresa posicionada
	For nI := 1 To Len(aEmpSm0)
		aAdd(__aSelFil, aEmpSm0[nI][1])  //{SM0->M0_CODFIL,SM0->M0_FILIAL}
	Next
Else
	__aSelFil := {cFilAnt}  //Filial corrente
EndIf

If MV_PAR16 == 2 //Tipo filtro Bancos: 1=Todos; 2=Selecao
	U_FR002P02(3)  //Tela para Selecao (1=Empresas,2=Tipo de cliente,3=Bancos ou portadores,4=SituaCões)
	TMV_PAR16 := cRetConXb  //cRetConXb: Variável Static !!
EndIf

If MV_PAR19 == 2 //Situacao: 1=Todos; 2=Selecao
	U_FR002P02(4)  //Tela para Selecao (1=Empresas,2=Tipo de cliente,3=Bancos ou portadores,4=SituaCões)
	TMV_PAR19 := cRetConXb  //cRetConXb: Variável Static !!
EndIf

If MV_PAR21 == 2 //Situacao: 1=Todos; 2=Selecao
	U_FR002P02(5) //U_TFINTPTIT("05", 2)    //Tela para Selecao (Tipo de Titulos Bancarios - SX5 - 05 )
	TMV_PAR21 := cRetConXb  //cRetConXb: Variável Static !!
EndIf

If Len( __aSelFil ) > 0
	TMV_PAR01:=fRetFilSE1(__aSelFil)  //Retorna as filiais do SE1
Else
	MsgStop('Nenhuma filial selecionada.')
	Return
EndIf
//-----------------------------------------------------------------------------
// Prepara as variáveis com Selecao de registros - Fim
//-----------------------------------------------------------------------------

Processa( {|| fGeraXLS1(cExcelArq,cExcelNom,aPerg,TMV_PAR01,TMV_PAR16,TMV_PAR19,lJob,cCRR002X) }, "Aguarde...", "Preparando os dados para o processamento...")  //lJob: .t.=StartJob,.f.=Tela

Return
//-----------------------------------------------------------------------------

Static Function fGeraXLS1(cExcelArq,cExcelNom,aPerg,TMV_PAR01,TMV_PAR16,TMV_PAR19,lJob,cCRR002X)  //Executa StartJob: .t.=Sim,.f.=Não

If lJob
	ProcRegua(3)
	IncProc();Sleep(1000)
	IncProc();Sleep(1000)

	fGravaLog(cExcelArq+StrTran(cExcelNom,".dbf",".log"),"[TFCRR002] U_FR002X01: Inicio",.t.)

	fEnviaMail("Arquivo: "+cExcelNom,"Inicio de processamento.",.f.,"",cCRR002X)  //Dispara e-mail com o status do processamento

	StartJob("U_FR002X01",GetEnvServer(),.F.,cEmpAnt,cFilAnt,__cUserId,cExcelArq,cExcelNom,aPerg,TMV_PAR01,TMV_PAR16,TMV_PAR19,lJob,Time(),cCRR002X)

	MsgAlert("O arquivo sera gerado em segundo plano."+CRLF+"Voce devera receber um e-mail quando finalizar."+CRLF+"Favor aguardar e-mail com o fim de processamento.")
Else
	ProcRegua(3)
	IncProc();Sleep(1000)
	IncProc();Sleep(1000)
	U_FR002X01(cEmpAnt,cFilAnt,__cUserId,cExcelArq,cExcelNom,aPerg,TMV_PAR01,TMV_PAR16,TMV_PAR19,lJob,Time(),cCRR002X)
EndIf

Return
//-----------------------------------------------------------------------------

User Function FR002X01(pEmpAnt,pFilAnt,p__cUserId,cExcelArq,cExcelNom,aPerg,TMV_PAR01,TMV_PAR16,TMV_PAR19,lJob,cHoraJob,cCRR002X)
Local bErroA   :=ErrorBlock({|x| cErroA:=fVerErroA(x,@cErroA)})
Local cErroA   :=""
Local cAnexos  :=""
Local nPos     :=0
Local nGravados:=0
Local cHoraIni :=""  //Guarda a hora inicial

If lJob
	RpcSetType(3)
	RpcSetEnv(pEmpAnt,pFilAnt, , , "FIN", , {"SE1"})  //Prepara o ambiente

	__cUserId:=p__cUserId

	For nPos:=1 to Len(aPerg)
		&("MV_PAR"+StrZero(nPos,2)):=aPerg[nPos,2]  //Atualiza as variáveis MV_PAR
	Next
EndIf

cHoraIni:=Time()  //Guarda a hora inicial
fGravaLog(cExcelArq+StrTran(cExcelNom,".dbf",".log"),"[TFCRR002] fGeraDbf..: Inicio",.t.)
Begin Sequence
	nGravados:=fGeraDbf(cExcelArq,cExcelNom,TMV_PAR01,TMV_PAR16,TMV_PAR19,cCRR002X)
End Sequence
fGravaLog(cExcelArq+StrTran(cExcelNom,".dbf",".log"),"[TFCRR002] fGeraDbf..: Fim"+" => Tempo: "+ElapTime(cHoraIni,Time()),.t.)

ErrorBlock(bErroA)

If !Empty(cErroA)
	MemoWrite(cExcelArq+StrTran(cExcelNom,".dbf",".err"),cErroA)
	fEnviaMail("Arquivo: "+cExcelNom,"Problemas na geracao do arquivo.",.f.,"",cCRR002X)  //Dispara e-mail com o status do processamento

	cAnexos:=cExcelArq+StrTran(cExcelNom,".dbf",".log")
	cAnexos+=";"+cExcelArq+StrTran(cExcelNom,".dbf",".sql")
	cAnexos+=";"+cExcelArq+StrTran(cExcelNom,".dbf",".err")
	fEnviaMail("Arquivo: "+cExcelNom,"ERRO no processamento.",.t.,cAnexos,cCRR002X)  //Dispara e-mail com o status do processamento
Else
	fGravaLog(cExcelArq+StrTran(cExcelNom,".dbf",".log"),"[TFCRR002] U_FR002X01: Fim"+" => Tempo: "+ElapTime(cHoraJob,Time()),.t.)
	MemoWrite(cExcelArq+StrTran(cExcelNom,".dbf",".fim"),"Status: Arquivo gerado com sucesso.")

	If Subs(cCRR002X,2,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
		cAnexos:=fVerAnexo(cExcelArq,cExcelNom)  //Verifica tamanho máximo do arquivo para enviar por e-mail
	Else
		cAnexos:=""
	EndIf

	Do Case
	Case nGravados==0
		fEnviaMail("Arquivo: "+cExcelNom,"Arquivo gerado sem registros.",.f.,cAnexos,cCRR002X)  //Dispara e-mail com o status do processamento
	Case nGravados==1
		fEnviaMail("Arquivo: "+cExcelNom,"Arquivo gerado com sucesso com um registro.",.f.,cAnexos,cCRR002X)  //Dispara e-mail com o status do processamento
	Otherwise
		fEnviaMail("Arquivo: "+cExcelNom,"Arquivo gerado com sucesso com "+AllTrim(Str(nGravados))+" registros.",.f.,cAnexos,cCRR002X)  //Dispara e-mail com o status do processamento
	EndCase

	cAnexos:=cExcelArq+StrTran(cExcelNom,".dbf",".log")
	cAnexos+=";"+cExcelArq+StrTran(cExcelNom,".dbf",".sql")
	fEnviaMail("Arquivo: "+cExcelNom,"LOG do processamento.",.t.,cAnexos,cCRR002X)  //Dispara e-mail com o status do processamento
EndIf

If lJob
	RpcClearEnv()
EndIf

Return
//-----------------------------------------------------------------------------

Static Function fGeraDbf(cExcelArq,cExcelNom,TMV_PAR01,TMV_PAR16,TMV_PAR19,cCRR002X)
Local cAliasQry  :=""
Local cQuery     :=""
Local nPos       :=0
Local nGravados  :=0
Local cHoraIni   :=""  //Guarda a hora inicial

Local aTabCol    :=fTabCol(cCRR002X)  //Cria o array com as colunas do select

Local lVerEmpres :=Lj950Acres(SM0->M0_CGC)
Local lR5        :=GetRpoRelease("R5")
Local lMV_LJICMJR:=SuperGetMV("MV_LJICMJR",,.F.)
Local lTpcalcJr  :=GETMV("MV_LJRCABT")
Local cxMvJurTipo:=SuperGetMv("MV_JURTIPO",,"")
Local lxMulLoj   :=SuperGetMv("MV_LJINTFS",,.F.)

Local cTabMonit  :=GETMV("TI_FCR0020")
Local cTabVIP    :=GETMV("TI_FCR0021")

Local cNomeTMP   :=SuperGetMV("TI_CRR002T",,"")  //Nome da tabela temporária = TTMPCR
Local cSelect    :=SuperGetMV("TI_CRR002S",,"0")  //SELECT: 0=Normal,1=Hint,2=Distinct,3=Hint+Distinct

If !Empty(cNomeTMP)  //Nome da tabela temporária
	cAliasQry := cNomeTMP  //Nome da tabela temporária

	If !TCCanOpen(cAliasQry)
		fEnviaMail("Arquivo: "+cExcelNom,"ATENCAO: Tabela "+cAliasQry+" nao encontrada. (Parametro: TI_CRR002T)",.t.,"",cCRR002X)  //Dispara e-mail com o status do processamento
		Return(nGravados)
	EndIf

	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAliasQry,.T.,.T.)  //Abre a tabela temporária
Else
	cAliasQry := GetNextAlias() + "_GERADBF"

	cQuery:=fMontaQry(cTabMonit,cTabVIP,TMV_PAR01,TMV_PAR16,TMV_PAR19,cSelect,cCRR002X)  //Monta a query

	MemoWrite(cExcelArq+StrTran(cExcelNom,".dbf",".sql"),cQuery)  //Guarda a query utilizada

	cHoraIni:=Time()  //Guarda a hora inicial
	fGravaLog(cExcelArq+StrTran(cExcelNom,".dbf",".log"),"[TFCRR002] dbUseArea.: Inicio",.t.)
	fEnviaMail("Arquivo: "+cExcelNom,"Inicio da execucao da query.",.t.,"",cCRR002X)  //Dispara e-mail com o status do processamento

	dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQuery),cAliasQry,.T.,.T.)  //Abre a query

	fGravaLog(cExcelArq+StrTran(cExcelNom,".dbf",".log"),"[TFCRR002] dbUseArea.: Fim"+" => Tempo: "+ElapTime(cHoraIni,Time()),.t.)
	fEnviaMail("Arquivo: "+cExcelNom,"Fim da execucao da query.",.t.,"",cCRR002X)  //Dispara e-mail com o status do processamento

	For nPos:=1 to Len(aTabCol)  //Array com as colunas do select
		Do Case
		Case aTabCol[nPos,2]=="D"
			TCSetField(cAliasQry,(cAliasQry)->(FieldName(nPos)),"D",8,0)
		Case aTabCol[nPos,2]=="N"
			TcSetField(cAliasQry,(cAliasQry)->(FieldName(nPos)),"N",TamSX3("E1_SALDO")[1],TamSX3("E1_SALDO")[2])
		EndCase
	Next
EndIf

cHoraIni:=Time()  //Guarda a hora inicial
fGravaLog(cExcelArq+StrTran(cExcelNom,".dbf",".log"),"[TFCRR002] fGravaDbf.: Inicio",.t.)
nGravados:=fGravaDbf(cExcelArq,cExcelNom,cAliasQry,lVerEmpres,lR5,lMV_LJICMJR,lTpcalcJr,cxMvJurTipo,lxMulLoj,cCRR002X)
fGravaLog(cExcelArq+StrTran(cExcelNom,".dbf",".log"),"[TFCRR002] fGravaDbf.: Fim"+" => Tempo: "+ElapTime(cHoraIni,Time()),.t.)

(cAliasQry)->(dbCloseArea())
Return(nGravados)
//-----------------------------------------------------------------------------

Static Function fVerErroA(oErroArq,cErroA)
Local nPos:=0

If oErroArq:GenCode>0
	cErroA:='('+AllTrim(Str(oErroArq:GenCode))+'):'+AllTrim(oErroArq:Description)+CRLF
EndIf

++nPos  //Para começãr a partir do segundo ProcName
While !Empty(ProcName(++nPos))
	cErroA+=Trim(ProcName(nPos))+"("+AllTrim(Str(ProcLine(nPos)))+")"+CRLF
End

If Intransact()
	cErroA+="Transacao aberta desarmada"
	DisarmTransaction()
EndIf

Break

Return
//-----------------------------------------------------------------------------

Static Function fGravaLog(cArqLog,cMens,lLog)
Local nPosFim,nHandle,cBuffer

DEFAULT lLog:=.f.

If lLog
	cMens:=fDtoC(Date())+" "+Time()+" => " + cMens  //Inclui a data e a hora do processamento
EndIf

If !File(cArqLog)
	nHandle:=fCreate(cArqLog,0)
	If nHandle==-1
		Alert("LOG ERROR: "+nHandle+" ==> Nao foi possivel criar "+cArqLog)
		Return(.f.)
	EndIf
	fClose(nHandle)
EndIf

nHandle:=fOpen(cArqLog,2)

If fError()<>0
	Alert("LOG ERROR: "+fError()+" ==> Nao foi possivel abrir "+cArqLog)
	Return(.f.)
EndIf

cBuffer:=cMens+Chr(13)+Chr(10)

nPosFim:=fSeek(nHandle,0,2)  //Posiciona no Fim do Arquivo

fWrite(nHandle,cBuffer,Len(cBuffer))

If fError()<>0
	Alert("LOG ERROR: "+fError()+" ==> Nao foi possivel gravar "+cArqLog)
	fClose(nHandle)
	Return(.f.)
EndIf

nPosFim:=fSeek(nHandle,0,2)  //Posiciona no Fim do Arquivo

fClose(nHandle)

Return(.t.)
//-----------------------------------------------------------------------------

Static Function fEnviaMail(pSubject,pBody,lDebug,pAnexos,cCRR002X)
Local cTo       :=""
Local cSubject  :=pSubject
Local cBody     :=pBody
Local cAnexos   :=If(pAnexos==Nil,"",pAnexos)
Local lJob      :=.t.
Local cCopia    :=Nil
Local cEmailDe  :=Nil
Local lAutentica:=GetMv("TI_RELAUTH")
Local lTexto    :=.t.
Local cCO       :=""

Local cEmailsCO :=GetMv("TI_CRR002E",,"")  //E-mails Copia Oculta
Local cEmailsDe :=GetMv("TI_CRR002D",,"")  //E-mails Debug

Default lDebug :=.f.  //Envia e-mail com os processos para debug

cBody := fDtoC(Date())+" "+Time()+" => " + cBody  //Inclui a data e a hora do processamento

If Empty(cEmailsCO)  //E-mails Cópia Oculta
	cEmailsCO:="<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>"
EndIf

If Empty(cEmailsDe)  //E-mails Debug
	cEmailsDe:="<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>"
EndIf

If lDebug
	cTo:=AllTrim(cEmailsDe)  //E-mails Debug
	cCO:=""
Else
	cTo:=AllTrim(UsrRetMail(__cUserID))  //E-mail do usuário

	If Empty(cTo)
		cTo:=AllTrim(cEmailsCO)  //E-mails Cópia Oculta
	Else
		cCO:=AllTrim(cEmailsCO)  //E-mails Cópia Oculta
	EndIf

	cCO:=fAjustacCO(cTo,cCO)  //Ajusta os E-mails Cópia Oculta
EndIf

If Subs(cCRR002X,1,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
	cTo:="<EMAIL>"
	cCO:=""
EndIf

//turn(U_xSendMail(cMailDestino,cAssunto,cTexto,cAnexos,lJob,cCopia,cEmailDe,lAutentica,lFormatoTexto,cCopOcult))
Return(U_xSendMail(cTo         ,cSubject,cBody ,cAnexos,lJob,cCopia,cEmailDe,lAutentica,lTexto       ,cCO      ))
//-----------------------------------------------------------------------------

Static Function fAjustacCO(cTo,cCO)  //Ajusta os E-mails Cópia Oculta

If At(cTo,cCO)
	cCO:=StrTran(cCO,cTo,"")  //Retira o cTo de cCO
EndIf

While At(";;",cCO)>0
	cCO:=StrTran(cCO,";;",";")  //Transforma os ";;" em ";"
End

If Subs(cCO,1,1)==";"
	cCO:=Subs(cCO,2,Len(cCO)-1)  //Retira o ";" do início
EndIf

If Subs(cCO,Len(cCO),1)==";"
	cCO:=Subs(cCO,1,Len(cCO)-1)  //Retira o ";" do Fim
EndIf

If cTo==cCO
	cCO:=""
EndIf

Return(cCO)
//-----------------------------------------------------------------------------

Static Function fGravaDbf(cExcelArq,cExcelNom,cAliasQry,lVerEmpres,lR5,lMV_LJICMJR,lTpcalcJr,cxMvJurTipo,lxMulLoj,cCRR002X)
Local aAreaAnt  :=GetArea()

Local uCampo    :=Nil
Local cCampo    :=""
Local nMaximo   :=0
Local cLinha    :=""  //Linha a ser gravada
Local nPos      :=0
Local nGravados :=0
Local nFator    :=SuperGetMV("TI_CRR002Q",,50000)  //Quantidade de registros para enviar e-mails Debug
//Local nBuffer   :=SuperGetMV("TI_CRR002B",,100000)  //Buffer em bytes para gravacao dos arquivos

Local aTabCol   :=fTabCol(cCRR002X)  //Cria o array com as colunas do select
Local cFilGuard :=""  //Guarda a filial corrente

Local aTabFil   :=fTabFil()  //Cria o array com as filiais
Local cDescFil  :=""  //Traz o nome da filial

Local nValorE1  :=0

Local nSaldoE1  :=0
Local nValAbat  :=0
Local nImpostos :=0

Local nValCorr  :=0
Local nJurosE1  :=0
Local nMultaE1  :=0
Local nDiasAtr  :=0

Local cTpCliente:=""
Local aTpCliDesc:={}
Local aTpCli    :={}
Local nI        :=0

Local aTabPJ9   :={}

Local nSaldoTit :=0
Local nSomaAbat :=0

Local oDBF      :=Nil
Local aStruct   :=aClone(aTabCol)
Local cFile     :=cExcelArq+cExcelNom
Local cArquivo  :=StrTran(Lower(cFile),".dbf","")

Local cComboSE1 :=GetSX3Cache("E1_XPROPEN","X3_CBOX") //A=Alta;M=Media;B=Baixa
Local cComboAI0 :=GetSX3Cache("AI0_XPROPE","X3_CBOX") // = ;A=Alta;M=Media;B=Baixa
Local cComboFWB :=GetSX3Cache("FWB_OCORR" ,"X3_CBOX") //0=Retirado;1=Selecionado;2=Enviado;3=Incluido;4=Sel. Retirada;5=Retirada Sol.;6=Erro;7=Negociado;8=Recebido;9=Erro Retirada

Local aComboSE1 :=StrTokArr2(cComboSE1,";",.t.)  //Traz o array do campo informado
Local aComboAI0 :=StrTokArr2(cComboAI0,";",.t.)  //Traz o array do campo informado
Local aComboFWB :=StrTokArr2(cComboFWB,";",.t.)  //Traz o array do campo informado

AAdd(aComboFWB," =Sem Restricoes") //Inclui a descrição "Sem Restricoes"

For nPos:=1 to Len(aStruct)
	If aStruct[nPos,2]=="D"
		aStruct[nPos,2]:="C" //Ajustar campo data para dd/mm/yyyy
		aStruct[nPos,3]:=10  //Ajustar campo data para dd/mm/yyyy
	EndIf
Next

oDBF := ZXBFXILE():New(cArquivo+".dbf")
oDBF:Create(aStruct)

If !oDBF:Open(.T.,.T.)
	UserException( oDBF:GetErrorStr() )
	Return(nGravados)
EndIf

SX3->(DbSetOrder(2))
If SX3->(msseek("A1_XCLIVIP"))
	cTpCliente:= AllTrim(X3CBox())
Else
	cTpCliente:= "1=VIP;2=Padrao;3=Setor Publico;4=Large;5=Gens PM;6=TFS;7=VBP;8=PBP;9=Franquia;P=Parceiro;G=Gare;U=Unidade Propria"
EndIf
aTpCli := SEPARA(cTpCliente,";")
For ni:=1 to Len(aTpCli)
	AADD(aTpCliDesc,SEPARA(aTpCli[ni],"="))
Next

cLinha:=""
For nPos:=1 to Len(aTabCol)  //Array com as colunas do select
	cCampo:=aTabCol[nPos,1]  //1=Nome do campo

	If Subs(cCRR002X,6,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
		Do Case
		Case cCampo=="DIF1"
			cCampo:="SALDOTIT-E1_SALDO"
		Case cCampo=="DIF2"
			cCampo:="SOMAABAT-IMPOSTOS"
		EndCase
	EndIf

	cLinha+=If(nPos==1,"",";") + cCampo
Next

///////////////////////////////////////////////////////////////////////////////
If Select("__SE1") == 0
	ChkFile("SE1",.F.,"__SE1")
Else
	DbSelectArea("__SE1")
EndIf
///////////////////////////////////////////////////////////////////////////////

While !(cAliasQry)->(Eof())

	If Mod(++nGravados,nFator)==0  //Fator = 50000 registros
		fEnviaMail("Arquivo: "+cExcelNom,"Em processamento: "+AllTrim(Str(nGravados))+" registros.",.t.,"",cCRR002X)  //Dispara e-mail com o status do processamento
	EndIf

	SE1->(DbGoto((cAliasQry)->(RECNOSE1)))  //Posiciona no SE1
	__SE1->(DbGoTo(SE1->(recno())  ))  //Posiciona no __SE1

	cFilGuard:= cFilAnt  //Guarda a filial corrente
	cFilAnt  := SE1->E1_FILIAL

	nValorE1 := SE1->E1_VALOR

	nImpostos:= (cAliasQry)->( IRRF + ISS + INSS + PIS + COFINS + CSLL )

///////////////////////////////////////////////////////////////////////////////
	If Subs(cCRR002X,3,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
		nSaldoE1 := SE1->E1_SALDO + nImpostos
		nValAbat := nImpostos
	Else
		nSaldoE1 := SaldoTit(SE1->E1_PREFIXO,SE1->E1_NUM,SE1->E1_PARCELA,SE1->E1_TIPO,SE1->E1_NATUREZ,"R",SE1->E1_CLIENTE,SE1->E1_MOEDA,dDataBase,dDataBase,SE1->E1_LOJA,,Iif(!Empty(SE1->E1_TXMOEDA),SE1->E1_TXMOEDA,RecMoeda(SE1->E1_EMISSAO,SE1->E1_MOEDA)),1)
		nValAbat := SomaAbat(SE1->E1_PREFIXO, SE1->E1_NUM, SE1->E1_PARCELA, "R", SE1->E1_MOEDA, dDataBase, SE1->E1_CLIENTE, SE1->E1_LOJA, SE1->E1_FILIAL )
	EndIf
	If Subs(cCRR002X,6,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
		nSaldoTit := SaldoTit(SE1->E1_PREFIXO,SE1->E1_NUM,SE1->E1_PARCELA,SE1->E1_TIPO,SE1->E1_NATUREZ,"R",SE1->E1_CLIENTE,SE1->E1_MOEDA,dDataBase,dDataBase,SE1->E1_LOJA,,Iif(!Empty(SE1->E1_TXMOEDA),SE1->E1_TXMOEDA,RecMoeda(SE1->E1_EMISSAO,SE1->E1_MOEDA)),1)
		nSomaAbat := SomaAbat(SE1->E1_PREFIXO, SE1->E1_NUM, SE1->E1_PARCELA, "R", SE1->E1_MOEDA, dDataBase, SE1->E1_CLIENTE, SE1->E1_LOJA, SE1->E1_FILIAL )
	EndIf
///////////////////////////////////////////////////////////////////////////////

	nValCorr := 0
	nJurosE1 := 0
	nMultaE1 := 0
	nDiasAtr := 0

	If nSaldoE1 > 0
		lBxParcial  := .F.
		lAcresVlTit := lVerEmpres .OR. (lR5 .AND. lMV_LJICMJR .AND. cPaisLoc == "BRA")
		If SE1->E1_TIPO $ MVABATIM //MVRECANT+"/"+MV_CRNEG
			nValCorr := nSaldoE1 - nValAbat  //MVRECANT+"/"+MV_CRNEG+"/"
		Else
			If dDataBase > SE1->E1_VENCREA .And. !(SE1->E1_TIPO $ MVRECANT)
				nJurosE1 := fa070Juros(SE1->E1_MOEDA,nSaldoE1 - nValAbat ,"SE1",SE1->E1_BAIXA)
				If nJurosE1 > 0
					__SE1->(DbGoTo(SE1->(recno())  ))
					If (cxMvJurTipo == "L" .Or. lxMulLoj) .And. !(SE1->E1_TIPO $ MVRECANT+"/"+MV_CRNEG) .and. SE1->E1_SALDO == SE1->E1_VALOR
						nMultaE1 := LojxRMul(,,,SE1->E1_VALOR ,,SE1->E1_VENCREA,dDataBase,,SE1->E1_MULTA,,SE1->E1_PREFIXO,SE1->E1_NUM,SE1->E1_PARCELA,SE1->E1_TIPO,SE1->E1_CLIENTE,SE1->E1_LOJA,"SE1",.T.)
					EndIf
				EndIf
				nDiasAtr := IIF(MV_PAR20 == 1 , dDataBase - SE1->E1_VENCREA, dDataBase - SE1->E1_VENCTO) //Considera data?: 1=Vencimento Real; 2=Vencimento
			Else
				nDiasAtr := 0
			EndIf
			If lTpcalcJr
				nValCorr := IIF(SE1->E1_ACRESC > 0 .And. dDataBase <= SE1->E1_VENCREA, ((nSaldoE1 - nValAbat)+nJurosE1+ nMultaE1+SE1->E1_ACRESC) * If(SE1->E1_TIPO $ MVABATIM, -1,1), ((nSaldoE1 - nValAbat)+nJurosE1+ nMultaE1) * If(SE1->E1_TIPO $ MVABATIM, -1,1))
			Else
				nValCorr := IIF(SE1->E1_ACRESC > 0 .And. dDataBase <= SE1->E1_VENCREA, ((nSaldoE1 - nValAbat)+nValAbat+nJurosE1+ nMultaE1+SE1->E1_ACRESC) * If(SE1->E1_TIPO $ MVABATIM, -1,1), ((nSaldoE1 - nValAbat)+nValAbat+nJurosE1+ nMultaE1) * If(SE1->E1_TIPO $ MVABATIM, -1,1))
			EndIf
		EndIf
	EndIf

	cDescFil:=fTrazFil(aTabFil,cEmpAnt,cFilAnt)  //Traz o nome da filial

	cFilAnt := cFilGuard  //Volta a filial corrente

	nImpostos:= (cAliasQry)->( IRRF + ISS + INSS + PIS + COFINS + CSLL )

	If Subs(cCRR002X,5,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
		aTabPJ9:=SE1->( fTabPJ9(E1_FILIAL,E1_PREFIXO,E1_NUM,E1_PARCELA,E1_TIPO,E1_CLIENTE,E1_LOJA) )  //Retorna PJ9_LOTE,PJ2_DESCRI,PJ4_DESCRI
	EndIf

	If oDBF:Insert()
		cLinha:=""
		For nPos:=1 to Len(aTabCol)  //Array com as colunas do select
			cCampo :=aTabCol[nPos,1]  //1=Nome do campo
			nMaximo:=aTabCol[nPos,3]  //3=Tamanho do campo
			Do Case
			Case cCampo=="TP_CLIENTE"
				uCampo:=(cAliasQry)->TP_CLIENTE + "-" + aTpCliDesc[Ascan(aTpCliDesc,{|x| Alltrim(x[1])== (cAliasQry)->TP_CLIENTE}),2]
			Case cCampo=="MONITORADO"
				uCampo:=If((cAliasQry)->MONITORADO == "1","Sim","Nao" )
			Case cCampo=="LIN_REC"
				uCampo:=(cAliasQry)->LIN_REC + " - " + (cAliasQry)->AOM_DESCRI
			Case cCampo=="SITUACAO"
				uCampo:=AllTrim((cAliasQry)->SITUACAO) + '-' + (cAliasQry)->FRV_DESCRI
			Case cCampo=="MOTIVO_VIP"
				uCampo:=(cAliasQry)->MOTIVO_VIP + "-" + (cAliasQry)->MOTVIP
			Case cCampo=="EMP_PUBLIC"
				uCampo:=If((cAliasQry)->EMP_PUBLIC == "1","Sim","Nao" )
			Case cCampo=="ATEND_CLI"
				uCampo:=If(Alltrim((cAliasQry)->ATEND_CLI) == "S","Sim","Nao" )
			Case cCampo=="DESC_FIL"
				uCampo:=cDescFil
			Case cCampo=="SALDO_BRU"
				uCampo:=nSaldoE1 - nValAbat + nImpostos
			Case cCampo=="VALOR_LIQ"
				uCampo:=nValorE1 - nValAbat
			Case cCampo=="SALDO"
				uCampo:=nSaldoE1 - nValAbat
			Case cCampo=="VAL_CORRIG"
				uCampo:=nValCorr
			Case cCampo=="JUROS"
				uCampo:=nJurosE1
			Case cCampo=="MULTA"
				uCampo:=nMultaE1
			Case cCampo=="DIAS_ATR"
				uCampo:=nDiasAtr
			Case cCampo=="PROPTITULO"
				uCampo:=fTrazCombo(aComboSE1,(cAliasQry)->PROPTITULO) //A=Alta;M=Media;B=Baixa
			Case cCampo=="PROP_CLI"
				uCampo:=fTrazCombo(aComboAI0,(cAliasQry)->PROP_CLI  ) // = ;A=Alta;M=Media;B=Baixa
			Case cCampo=="STAT_SERA"
				uCampo:=fTrazCombo(aComboFWB,(cAliasQry)->STAT_SERA ) //0=Retirado;1=Selecionado;2=Enviado;3=Incluido;4=Sel. Retirada;5=Retirada Sol.;6=Erro;7=Negociado;8=Recebido;9=Erro Retirada
			Case cCampo=="CNPJ"
				uCampo:=Transform((cAliasQry)->CNPJ,"@R 99.999.999/9999-99")
			Otherwise
				uCampo:=(cAliasQry)->(FieldGet(nPos))
			EndCase

///////////////////////////////////////////////////////////////////////////////
			If Subs(cCRR002X,5,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
				Do Case
				Case cCampo=="ULT_ACAO"
					uCampo:=aTabPJ9[2]  //PJ9_LOTE,PJ2_DESCRI,PJ4_DESCRI
				Case cCampo=="ULT_REGUA"
					uCampo:=aTabPJ9[3]  //PJ9_LOTE,PJ2_DESCRI,PJ4_DESCRI
				Case cCampo=="ULT_LOTE"
					uCampo:=aTabPJ9[1]  //PJ9_LOTE,PJ2_DESCRI,PJ4_DESCRI
				EndCase
			EndIf
			If Subs(cCRR002X,6,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
				Do Case
				Case cCampo=="E1_VALOR"
					uCampo:=(cAliasQry)->E1_VALOR
				Case cCampo=="E1_SALDO"
					uCampo:=(cAliasQry)->E1_SALDO
				Case cCampo=="SALDOTIT"
					uCampo:=nSaldoTit
				Case cCampo=="DIF1"//SALDOTIT-E1_SALDO
					uCampo:=nSaldoTit-(cAliasQry)->E1_SALDO
				Case cCampo=="IMPOSTOS"
					uCampo:=nImpostos
				Case cCampo=="SOMAABAT"
					uCampo:=nSomaAbat
				Case cCampo=="DIF2"//SOMAABAT-IMPOSTOS
					uCampo:=nSomaAbat-nImpostos
				EndCase
			EndIf
///////////////////////////////////////////////////////////////////////////////

			Do Case
			Case ValType(uCampo)=="D"
				uCampo:=fDtoC(uCampo) //Retorna DD/MM/YYYY
			Case ValType(uCampo)=="C"
				uCampo:=fTrataChar( AllTrim(uCampo) ) //Trata os caracteres especiais do campo informado
			EndCase
			oDBF:FieldPut(nPos, uCampo)
		Next

		oDBF:Update()
	EndIf

	(cAliasQry)->(dbSkip())
End

oDBF:Close()
FreeObj(oDBF)

RestArea(aAreaAnt)
Return(nGravados)
//-----------------------------------------------------------------------------

Static Function fTabFil()  //Cria o array com as filiais
Local aAreaSM0:=SM0->(GetArea())
Local aTabAux :={}

SM0->(dbGoTop())
While SM0->(!Eof())
	AAdd(aTabAux,{SM0->M0_CODIGO,SM0->M0_CODFIL,SM0->M0_FILIAL})
	SM0->(dbSkip())
End

SM0->(RestArea(aAreaSM0))
Return(aTabAux)
//-----------------------------------------------------------------------------

Static Function fTrazFil(aTabFil,pEmpAnt,pFilAnt)  //Traz o nome da filial
Local nPos:=0

nPos:=Ascan(aTabFil,{|x| AllTrim(x[1]+x[2])==AllTrim(pEmpAnt+pFilAnt)})

If nPos>0
	cRet:=aTabFil[nPos,3]
Else
	cRet:=pFilAnt
EndIf

Return(cRet)
//-----------------------------------------------------------------------------

Static Function fMontaQry(cTabMonit,cTabVIP,TMV_PAR01,TMV_PAR16,TMV_PAR19,cSelect,cCRR002X)  //Monta a query
Local cQuery   :=""
Local cBarra   :="/"
Local cAster   :="*"
Local cMais    :="+"
Local cHint    :=""
Local cDistinct:=""

Local cCampoVen := If(MV_PAR20 == 1, "E1_VENCREA", "E1_VENCTO") //Considera data?: 1=Vencimento Real; 2=Vencimento
Local cWhereFil := If(MV_PAR01 == 1 .Or. MV_PAR01 == 2, " E1_FILIAL IN (" + TMV_PAR01 + ")", " E1_FILIAL = '" + xFilial("SE1") + "'") //Filtra Filiais ?: 1=Todas; 2=Selecao; 3=Filial Atual
Local cWhereZDP := If(MV_PAR01 == 1 .Or. MV_PAR01 == 2, " ZDP_FILIAL IN (" + TMV_PAR01 + ")", " ZDP_FILIAL = '" + xFilial("ZDP") + "'") //Filtra Filiais ?: 1=Todas; 2=Selecao; 3=Filial Atual

If cSelect$"13"  //0=Normal,1=Hint,2=Distinct,3=Hint+Distinct
	cHint    :=" "+cBarra+cAster+cMais+" PARALLEL(8) "+cAster+cBarra  //"/*+ PARALLEL(8) */"
EndIf

If cSelect$"23"  //0=Normal,1=Hint,2=Distinct,3=Hint+Distinct
	cDistinct:=" DISTINCT"
EndIf

cQuery := " SELECT" + cHint + cDistinct
cQuery += " E1_FILIAL FILIAL"
cQuery += ", ' ' DESC_FIL"
cQuery += ", A1_XCLIVIP TP_CLIENTE"
cQuery += ", E1_CLIENTE COD_CLIENT"
cQuery += ", A1_NOME RAZAO_SOC"
cQuery += ", A1_CGC CNPJ"
cQuery += ", E1_NFELETR NF_ELET"
cQuery += ", E1_PREFIXO PREFIXO"
cQuery += ", E1_NUM NUMERO"
cQuery += ", E1_PARCELA PARCELA"
cQuery += ", E1_TIPO TIPO"
cQuery += ", E1_EMISSAO DT_EMISSAO"
cQuery += ", E1_VENCTO VENCIMENTO"
cQuery += ", E1_VENCREA VENC_REAL"
cQuery += ", E1_VALOR VALOR"
cQuery += ", 0 SALDO_BRU"
cQuery += ", (SELECT E1_SALDO FROM " + RetSqlName("SE1") + " SE1IR WHERE SE1IR.E1_FILIAL = SE1.E1_FILIAL AND SE1IR.E1_NUM = SE1.E1_NUM AND SE1IR.E1_PREFIXO = SE1.E1_PREFIXO AND SE1IR.E1_PARCELA = SE1.E1_PARCELA AND SE1IR.E1_TIPO = 'IR-' AND SE1IR.E1_CLIENTE = SE1.E1_CLIENTE AND SE1IR.E1_LOJA = SE1.E1_LOJA AND SE1IR.D_E_L_E_T_ = ' ') AS IRRF"
cQuery += ", (SELECT E1_SALDO FROM " + RetSqlName("SE1") + " SE1IR WHERE SE1IR.E1_FILIAL = SE1.E1_FILIAL AND SE1IR.E1_NUM = SE1.E1_NUM AND SE1IR.E1_PREFIXO = SE1.E1_PREFIXO AND SE1IR.E1_PARCELA = SE1.E1_PARCELA AND SE1IR.E1_TIPO = 'IS-' AND SE1IR.E1_CLIENTE = SE1.E1_CLIENTE AND SE1IR.E1_LOJA = SE1.E1_LOJA AND SE1IR.D_E_L_E_T_ = ' ') AS ISS"
cQuery += ", (SELECT E1_SALDO FROM " + RetSqlName("SE1") + " SE1IR WHERE SE1IR.E1_FILIAL = SE1.E1_FILIAL AND SE1IR.E1_NUM = SE1.E1_NUM AND SE1IR.E1_PREFIXO = SE1.E1_PREFIXO AND SE1IR.E1_PARCELA = SE1.E1_PARCELA AND SE1IR.E1_TIPO = 'IN-' AND SE1IR.E1_CLIENTE = SE1.E1_CLIENTE AND SE1IR.E1_LOJA = SE1.E1_LOJA AND SE1IR.D_E_L_E_T_ = ' ') AS INSS"
cQuery += ", (SELECT E1_SALDO FROM " + RetSqlName("SE1") + " SE1IR WHERE SE1IR.E1_FILIAL = SE1.E1_FILIAL AND SE1IR.E1_NUM = SE1.E1_NUM AND SE1IR.E1_PREFIXO = SE1.E1_PREFIXO AND SE1IR.E1_PARCELA = SE1.E1_PARCELA AND SE1IR.E1_TIPO = 'PI-' AND SE1IR.E1_CLIENTE = SE1.E1_CLIENTE AND SE1IR.E1_LOJA = SE1.E1_LOJA AND SE1IR.D_E_L_E_T_ = ' ') AS PIS"
cQuery += ", (SELECT E1_SALDO FROM " + RetSqlName("SE1") + " SE1IR WHERE SE1IR.E1_FILIAL = SE1.E1_FILIAL AND SE1IR.E1_NUM = SE1.E1_NUM AND SE1IR.E1_PREFIXO = SE1.E1_PREFIXO AND SE1IR.E1_PARCELA = SE1.E1_PARCELA AND SE1IR.E1_TIPO = 'CF-' AND SE1IR.E1_CLIENTE = SE1.E1_CLIENTE AND SE1IR.E1_LOJA = SE1.E1_LOJA AND SE1IR.D_E_L_E_T_ = ' ') AS COFINS"
cQuery += ", (SELECT E1_SALDO FROM " + RetSqlName("SE1") + " SE1IR WHERE SE1IR.E1_FILIAL = SE1.E1_FILIAL AND SE1IR.E1_NUM = SE1.E1_NUM AND SE1IR.E1_PREFIXO = SE1.E1_PREFIXO AND SE1IR.E1_PARCELA = SE1.E1_PARCELA AND SE1IR.E1_TIPO = 'CS-' AND SE1IR.E1_CLIENTE = SE1.E1_CLIENTE AND SE1IR.E1_LOJA = SE1.E1_LOJA AND SE1IR.D_E_L_E_T_ = ' ') AS CSLL"
cQuery += ", 0 VALOR_LIQ"
cQuery += ", 0 SALDO"
cQuery += ", 0 VAL_CORRIG"
cQuery += ", 0 JUROS"
cQuery += ", 0 MULTA"
cQuery += ", E1_PORTADO PORTADOR"
cQuery += ", E1_SITUACA SITUACAO"
cQuery += ", 0 DIAS_ATR"
cQuery += ", E1_BAIXA DT_BAIXA"
cQuery += ", SBM.BM_XLINREC LIN_REC"
cQuery += ", SBM.BM_GRUPO GRUPO_PROD"
cQuery += ", SBM.BM_DESC DESC_GRUPO"
cQuery += ", (SELECT DISTINCT LISTAGG(C6_XPROPOS,'/' ON OVERFLOW TRUNCATE) WITHIN GROUP (ORDER BY C6_XPROPOS) OVER (PARTITION BY E1_NUM) FROM SC6000 SC6 WHERE C6_FILIAL = E1_FILIAL AND SC6.C6_CLI = SE1.E1_CLIENTE AND SC6.C6_NOTA = SE1.E1_NUM AND SC6.C6_SERIE = SE1.E1_PREFIXO GROUP BY C6_XPROPOS) AS COD_PROP"
cQuery += ", A1_VEND COD_VEND"
cQuery += ", A3_NOME NOME_VEND"
cQuery += ", A1_SUPER COD_FRANQ"
cQuery += ", ADKSU.ADK_NOME NOME_FRANQ"
cQuery += ", A1_XMOTVIP MOTIVO_VIP"
cQuery += ", A1_XESTRAT MONITORADO"
cQuery += ", (SELECT X5_DESCRI FROM " + RetSqlName("SX5") + " SX5M WHERE SX5M.X5_FILIAL = '" + xFilial("SX5") + "' AND SX5M.X5_TABELA = '" + cTabMonit + "' AND SX5M.X5_CHAVE = A1_XMOTEST AND ROWNUM = 1 AND SX5M.D_E_L_E_T_ = ' ' ) AS MOT_MONIT"
cQuery += ", (SELECT MAX(ZA8_DATA) FROM " + RetSqlName("ZA8") + " ZA8  WHERE ZA8.ZA8_FILIAL = '" + xFilial("ZA8") + "' AND ZA8.ZA8_CLIENT = SE1.E1_CLIENTE AND ZA8.ZA8_LOJACL = SE1.E1_LOJA AND ZA8.D_E_L_E_T_ = ' ' ) AS VCT_MONIT"
cQuery += ", (SELECT COUNT(ZA8_DATA) FROM " + RetSqlName("ZA8") + " ZA8  WHERE ZA8.ZA8_FILIAL = '" + xFilial("ZA8") + "' AND ZA8.ZA8_CLIENT = SE1.E1_CLIENTE AND ZA8.ZA8_LOJACL = SE1.E1_LOJA AND ZA8.D_E_L_E_T_ = ' ' ) AS PAS_MONIT"
cQuery += ", AI0_SETPUB EMP_PUBLIC"
cQuery += ", (SELECT FWB_OCORR FROM " + RetSqlName("FWB") + " FWB WHERE FWB_IDDOC = FK7_IDDOC AND FWB.R_E_C_N_O_ = (SELECT MAX(FWB1.R_E_C_N_O_) FROM " + RetSqlName("FWB") + " FWB1 WHERE  FWB1.FWB_IDDOC = FK7_IDDOC AND FWB1.FWB_DTOCOR <= '" + Dtos(dDataBase) + "' AND FWB1.D_E_L_E_T_ = ' ') AND FWB.D_E_L_E_T_ = ' ' ) AS STAT_SERA"
cQuery += ", (SELECT ZQE_ATENDE FROM " + RetSqlName("ZQE") + " ZQE WHERE ZQE_FILIAL = '" + xFilial("ZQE") + "' AND ZQE_CODCLI = A1_COD AND ZQE_LJCLI = A1_LOJA AND ROWNUM = 1 AND ZQE.D_E_L_E_T_ = ' ') AS ATEND_CLI"
cQuery += ", (SELECT MIN(ZDP_VENCTO) FROM " + RetSqlName("ZDP") + " ZDP WHERE " + cWhereZDP + " AND ZDP_CLIENT = A1_COD AND ZDP.D_E_L_E_T_ = ' ' ) AS VENC_SENHA"
cQuery += ", A1_UNIDVEN UNID_ATEND"
cQuery += ", ADKUV.ADK_NOME NOME_UNID"
cQuery += ", E1_HIST HISTORICO"
cQuery += ", SC5.C5_MENNOTA MENSAGEM"
cQuery += ", E1_XPROPEN PROPTITULO"
cQuery += ", AI0_XPROPE PROP_CLI"
cQuery += ", AI0_XDTPRR DT_PROP"

If Subs(cCRR002X,5,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
	cQuery += ", ' ' ULT_ACAO"
	cQuery += ", ' ' ULT_REGUA"
	cQuery += ", ' ' ULT_LOTE"
Else
	cQuery += ", (SELECT PJ2_DESCRI FROM " + RetSQLName("PJ9") + " PJ9 LEFT JOIN "+RetSQLName("PJ2")+" PJ2 ON PJ2_FILIAL = '"+xFilial("PJ2")+"' AND PJ2_CODIGO = PJ9_CODACA AND PJ2.D_E_L_E_T_ = ' ' WHERE PJ9.D_E_L_E_T_ = ' ' AND PJ9_FILTIT = SE1.E1_FILIAL AND PJ9_PREFIX = SE1.E1_PREFIXO AND PJ9_NUMTIT = SE1.E1_NUM AND PJ9_PARCEL = SE1.E1_PARCELA AND PJ9_TIPTIT = SE1.E1_TIPO ORDER BY PJ9_DATA DESC, PJ9_HORA DESC FETCH FIRST 1 ROWS ONLY) AS ULT_ACAO"
	cQuery += ", (SELECT PJ4_DESCRI FROM " + RetSQLName("PJ9") + " PJ9 LEFT JOIN "+RetSQLName("PJ4")+" PJ4 ON PJ4_FILIAL = PJ9_FILIAL AND PJ4_CODREG = PJ9_CODREG AND PJ4.D_E_L_E_T_ = ' ' WHERE PJ9.D_E_L_E_T_ = ' ' AND PJ9_FILTIT = SE1.E1_FILIAL AND PJ9_PREFIX = SE1.E1_PREFIXO AND PJ9_NUMTIT = SE1.E1_NUM AND PJ9_PARCEL = SE1.E1_PARCELA AND PJ9_TIPTIT = SE1.E1_TIPO ORDER BY PJ9_DATA DESC, PJ9_HORA DESC FETCH FIRST 1 ROWS ONLY) AS ULT_REGUA"
	cQuery += ", (SELECT PJ9_LOTE   FROM " + RetSQLName("PJ9") + " PJ9 WHERE PJ9.D_E_L_E_T_ = ' ' AND PJ9_FILTIT = SE1.E1_FILIAL AND PJ9_PREFIX = SE1.E1_PREFIXO AND PJ9_NUMTIT = SE1.E1_NUM AND PJ9_PARCEL = SE1.E1_PARCELA AND PJ9_TIPTIT = SE1.E1_TIPO ORDER BY PJ9_DATA DESC, PJ9_HORA DESC FETCH FIRST 1 ROWS ONLY) AS ULT_LOTE"
EndIf

If Subs(cCRR002X,4,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
	cQuery += ", CASE WHEN D2_CCUSTO IS NULL OR D2_CCUSTO=' ' THEN E1_CCUSTO ELSE D2_CCUSTO END CCUSTO"
	cQuery += ", CTT_DESC01 DESC_CCUST"
	cQuery += ", CASE WHEN D2_CLVL IS NULL OR D2_CLVL=' ' THEN E1_CLVL ELSE D2_CLVL END CLASSE_VLR"
	cQuery += ", CTH_DESC01 DESC_CLVLR"
	cQuery += ", CASE WHEN D2_ITEMCC IS NULL OR D2_ITEMCC=' ' THEN E1_ITEMCTA ELSE D2_ITEMCC END ITEM_CTB"
	cQuery += ", CTD_DESC01 DESC_ITCTB"
EndIf

If Subs(cCRR002X,6,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
	cQuery += ", E1_VALOR"
	cQuery += ", E1_SALDO"
	cQuery += ", 0 SALDOTIT"
	cQuery += ", 0 DIF1"
	cQuery += ", 0 IMPOSTOS"
	cQuery += ", 0 SOMAABAT"
	cQuery += ", 0 DIF2"
EndIf

cQuery += ", FRV_DESCRI"
cQuery += ", AOM_DESCRI"
cQuery += ", (SELECT X5_DESCRI FROM " + RetSqlName("SX5") + " SX5V WHERE SX5V.X5_FILIAL = '" + xFilial("SX5") + "' AND SX5V.X5_TABELA = '" + cTabVIP   + "' AND SX5V.X5_CHAVE = A1_XMOTVIP AND ROWNUM = 1 AND SX5V.D_E_L_E_T_ = ' ' ) AS MOTVIP"
cQuery += ", SE1.R_E_C_N_O_ AS RECNOSE1"

cQuery += " FROM " + RetSqlName("SE1") + " SE1"

cQuery += " INNER JOIN " + RetSqlName("SA1") + " SA1"
cQuery += " ON A1_FILIAL = '" + xFilial("SA1") + "'"
cQuery += " AND A1_COD = E1_CLIENTE"
cQuery += " AND A1_LOJA = E1_LOJA"
cQuery += " AND SA1.D_E_L_E_T_ = ' '"

cQuery += " LEFT JOIN " + RetSqlName("AI0") + " AI0"
cQuery += " ON AI0_FILIAL = '" + xFilial("AI0") + "'"
cQuery += " AND AI0_CODCLI = A1_COD"
cQuery += " AND AI0_LOJA = A1_LOJA"
cQuery += " AND AI0.D_E_L_E_T_ = ' '"

cQuery += " LEFT JOIN " + RetSqlName("SA3") + " SA3"
cQuery += " ON A3_FILIAL = '" + xFilial("SA3") + "'"
cQuery += " AND A3_COD = A1_VEND"
cQuery += " AND SA3.D_E_L_E_T_ = ' '"

cQuery += " INNER JOIN " + RetSqlName("FRV") + " FRV"
cQuery += " ON FRV_FILIAL = '" + xFilial("FRV") + "'"
cQuery += " AND FRV_CODIGO = E1_SITUACA"
cQuery += " AND FRV.D_E_L_E_T_ = ' '"

cQuery += " LEFT JOIN " + RetSqlName("ADK") + " ADKSU"
cQuery += " ON ADKSU.ADK_FILIAL = '" + xFilial("ADK") + "'"
cQuery += " AND ADKSU.ADK_COD = A1_SUPER"
cQuery += " AND ADKSU.D_E_L_E_T_ = ' '"

cQuery += " LEFT JOIN " + RetSqlName("ADK") + " ADKUV"
cQuery += " ON ADKUV.ADK_FILIAL = '" + xFilial("ADK") + "'"
cQuery += " AND ADKUV.ADK_COD = A1_UNIDVEN"
cQuery += " AND ADKUV.D_E_L_E_T_ = ' '"

cQuery += " LEFT JOIN " + RetSqlName("FK7") + " FK7"
cQuery += " ON FK7_FILIAL = E1_FILORIG"
cQuery += " AND FK7_ALIAS = 'SE1'"
cQuery += " AND FK7_CHAVE = E1_FILIAL || '|' || E1_PREFIXO || '|' || E1_NUM || '|' || E1_PARCELA || '|' || E1_TIPO || '|' || E1_CLIENTE || '|' || E1_LOJA"
cQuery += " AND FK7.D_E_L_E_T_ = ' '"

cQuery += " LEFT JOIN " + RetSqlName("SC5") + " SC5"
cQuery += " ON SC5.C5_FILIAL = E1_FILIAL"
cQuery += " AND SC5.C5_NUM = E1_PEDIDO"
cQuery += " AND SC5.D_E_L_E_T_ = ' '"

cQuery += " LEFT JOIN " + RetSqlName("SF2") + " SF2"
cQuery += " ON SF2.F2_FILIAL = E1_FILORIG"
cQuery += " AND SF2.F2_CLIENTE = E1_CLIENTE"
cQuery += " AND SF2.F2_LOJA = E1_LOJA"
cQuery += " AND SF2.F2_DOC = E1_NUM"
cQuery += " AND SF2.F2_SERIE = E1_PREFIXO"
cQuery += " AND SF2.D_E_L_E_T_ = ' '"

cQuery += " LEFT JOIN " + RetSqlName("SD2") + " SD2"
cQuery += " ON SD2.D2_FILIAL = F2_FILIAL"
cQuery += " AND SD2.D2_CLIENTE = F2_CLIENTE"
cQuery += " AND SD2.D2_LOJA = F2_LOJA"
cQuery += " AND SD2.D2_DOC = F2_DOC"
cQuery += " AND SD2.D2_SERIE = F2_SERIE"
cQuery += " AND SD2.D_E_L_E_T_ = ' '"
cQuery += " AND SD2.D2_ITEM = (SELECT MIN(D2_ITEM) FROM SD2000 WHERE D2_DOC = SF2.F2_DOC AND D2_SERIE = SF2.F2_SERIE AND D2_CLIENTE = SF2.F2_CLIENTE AND D2_LOJA = SF2.F2_LOJA AND D_E_L_E_T_ = ' ')"

cQuery += " LEFT JOIN " + RetSqlName("SB1") + " SB1"
cQuery += " ON SB1.B1_COD = D2_COD"
cQuery += " AND SB1.B1_FILIAL = '"+xFilial("SB1")+"'
cQuery += " AND SB1.D_E_L_E_T_ = ' '"

cQuery += " LEFT JOIN " + RetSqlName("SBM") + " SBM"
cQuery += " ON SBM.BM_GRUPO = B1_GRUPO"
cQuery += " AND SBM.BM_FILIAL = '"+xFilial("SBM")+"'
cQuery += " AND SBM.D_E_L_E_T_ = ' '"

cQuery += " LEFT JOIN " + RetSqlName("AOM") + " AOM"
cQuery += " ON AOM.AOM_CODNIV = BM_XLINREC"
cQuery += " AND AOM.D_E_L_E_T_ = ' ' AND AOM.AOM_CODAGR = '000001'"

If Subs(cCRR002X,4,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
	cQuery += " LEFT JOIN " + RetSqlName("CTT") + " CTT"
	cQuery += " ON CTT_FILIAL = '" + xFilial("CTT") + "'"
	cQuery += " AND CTT_CUSTO = CASE WHEN D2_CCUSTO IS NULL OR D2_CCUSTO=' ' THEN E1_CCUSTO ELSE D2_CCUSTO END"
	cQuery += " AND CTT.D_E_L_E_T_ = ' '"

	cQuery += " LEFT JOIN " + RetSqlName("CTH") + " CTH"
	cQuery += " ON CTH_FILIAL = '" + xFilial("CTH") + "'"
	cQuery += " AND CTH_CLVL =  CASE WHEN D2_CLVL IS NULL OR D2_CLVL=' ' THEN E1_CLVL ELSE D2_CLVL END"
	cQuery += " AND CTH.D_E_L_E_T_ = ' '"

	cQuery += " LEFT JOIN " + RetSqlName("CTD") + " CTD"
	cQuery += " ON CTD_FILIAL = '" + xFilial("CTD") + "'"
	cQuery += " AND CTD_ITEM =  CASE WHEN D2_ITEMCC IS NULL OR D2_ITEMCC=' ' THEN E1_ITEMCTA ELSE D2_ITEMCC END"
	cQuery += " AND CTD.D_E_L_E_T_ = ' '"
EndIf

cQuery += " WHERE" + cWhereFil
cQuery += " AND E1_SALDO <> 0"
cQuery += " AND E1_PREFIXO BETWEEN '" + MV_PAR03 + "' AND '" + MV_PAR04 + "'"
cQuery += " AND E1_NUM BETWEEN '" + MV_PAR05 + "' AND '" + MV_PAR06 + "'"
cQuery += " AND E1_CLIENTE BETWEEN '" + MV_PAR07 + "' AND '" + MV_PAR09 + "'"
cQuery += " AND E1_LOJA BETWEEN '" + MV_PAR08 + "' AND '" + MV_PAR10 + "'"
cQuery += " AND E1_EMISSAO BETWEEN '" + DtoS(MV_PAR11) + "' AND '" + DtoS(MV_PAR12) + "'"
cQuery += " AND E1_TIPO NOT IN " + Formatin(MVABATIM,"|")

cQuery += " AND " + cCampoVen + " BETWEEN '" + DtoS(MV_PAR13) + "' AND '" + DtoS(MV_PAR14) + "'"

If !Empty(TMV_PAR21) //Imprimir Tipos ?
	TMV_PAR21 := StrTran(TMV_PAR21, ' ', '')
	cQuery += " AND E1_TIPO IN " + FormatIn(AllTrim(TMV_PAR21),";")
EndIf
If !Empty(MV_PAR15) //Tipo de Cliente
	cQuery += " AND A1_XCLIVIP IN " + Formatin(AllTrim(MV_PAR15),";")
EndIf
If MV_PAR16 == 2 //Tipo filtro Bancos: 1=Todos; 2=Selecao
	cQuery += " AND E1_PORTADO IN " + Formatin(AllTrim(TMV_PAR16),";")
EndIf
If MV_PAR17 <> 3 //Empresa publica?: 1=Sim; 2=Nao; 3=Ambas
	cQuery += " AND AI0_SETPUB = '" + cValToChar(MV_PAR17) + "'"
EndIf
If MV_PAR18 <> 3 //Cliente Monitorado?: 1=Sim; 2=Nao; 3=Ambas
	cQuery += " AND A1_XESTRAT = '" + AllTrim(Str(MV_PAR18)) + "'"
EndIf
If MV_PAR19 == 2 //Situacao: 1=Todos; 2=Selecao
	cQuery += " AND E1_SITUACA IN " + Formatin(AllTrim(TMV_PAR19),";")
EndIf

cQuery += " AND SE1.D_E_L_E_T_ = ' '"

cQuery += " ORDER BY E1_CLIENTE, E1_LOJA, E1_FILORIG, " + cCampoVen

Return(cQuery)
//-----------------------------------------------------------------------------

Static Function fTabCol(cCRR002X)  //Cria o array com as colunas do select
Local aTabAux:={}

AAdd(aTabAux,{"FILIAL"    ,"C",12                     ,0                      })
AAdd(aTabAux,{"DESC_FIL"  ,"C",50                     ,0                      })
AAdd(aTabAux,{"TP_CLIENTE","C",50                     ,0                      })
AAdd(aTabAux,{"COD_CLIENT","C",08                     ,0                      })
AAdd(aTabAux,{"RAZAO_SOC" ,"C",50                     ,0                      })
AAdd(aTabAux,{"CNPJ"      ,"C",18                     ,0                      })
AAdd(aTabAux,{"NF_ELET"   ,"C",TamSX3('E1_NFELETR')[1],0                      })
AAdd(aTabAux,{"PREFIXO"   ,"C",05                     ,0                      })
AAdd(aTabAux,{"NUMERO"    ,"C",12                     ,0                      })
AAdd(aTabAux,{"PARCELA"   ,"C",05                     ,0                      })
AAdd(aTabAux,{"TIPO"      ,"C",05                     ,0                      })
AAdd(aTabAux,{"DT_EMISSAO","D",08                     ,0                      })
AAdd(aTabAux,{"VENCIMENTO","D",08                     ,0                      })
AAdd(aTabAux,{"VENC_REAL" ,"D",08                     ,0                      })
AAdd(aTabAux,{"VALOR"     ,"N",TamSX3('E1_VALOR'  )[1],TamSX3('E1_VALOR'  )[2]})
AAdd(aTabAux,{"SALDO_BRU" ,"N",TamSX3('E1_SALDO'  )[1],TamSX3('E1_SALDO'  )[2]})
AAdd(aTabAux,{"IRRF"      ,"N",TamSX3('E1_IRRF'   )[1],TamSX3('E1_IRRF'   )[2]})
AAdd(aTabAux,{"ISS"       ,"N",TamSX3('E1_ISS'    )[1],TamSX3('E1_ISS'    )[2]})
AAdd(aTabAux,{"INSS"      ,"N",TamSX3('E1_INSS'   )[1],TamSX3('E1_INSS'   )[2]})
AAdd(aTabAux,{"PIS"       ,"N",TamSX3('E1_PIS'    )[1],TamSX3('E1_PIS'    )[2]})
AAdd(aTabAux,{"COFINS"    ,"N",TamSX3('E1_COFINS' )[1],TamSX3('E1_COFINS' )[2]})
AAdd(aTabAux,{"CSLL"      ,"N",TamSX3('E1_CSLL'   )[1],TamSX3('E1_CSLL'   )[2]})
AAdd(aTabAux,{"VALOR_LIQ" ,"N",TamSX3('E1_SALDO'  )[1],TamSX3('E1_SALDO'  )[2]})
AAdd(aTabAux,{"SALDO"     ,"N",TamSX3('E1_SALDO'  )[1],TamSX3('E1_SALDO'  )[2]})
AAdd(aTabAux,{"VAL_CORRIG","N",TamSX3('E1_SALDO'  )[1],TamSX3('E1_SALDO'  )[2]})
AAdd(aTabAux,{"JUROS"     ,"N",TamSX3('E1_JUROS'  )[1],TamSX3('E1_JUROS'  )[2]})
AAdd(aTabAux,{"MULTA"     ,"N",TamSX3('E1_MULTA'  )[1],TamSX3('E1_MULTA'  )[2]})
AAdd(aTabAux,{"PORTADOR"  ,"C",15                     ,0                      })
AAdd(aTabAux,{"SITUACAO"  ,"C",15                     ,0                      })
AAdd(aTabAux,{"DIAS_ATR"  ,"N",10                     ,0                      })
AAdd(aTabAux,{"DT_BAIXA"  ,"D",08                     ,0                      })
AAdd(aTabAux,{"LIN_REC"   ,"C",30                     ,0                      })
AAdd(aTabAux,{"GRUPO_PROD","C",04                     ,0                      })
AAdd(aTabAux,{"DESC_GRUPO","C",30                     ,0                      })
AAdd(aTabAux,{"COD_PROP"  ,"C",254                    ,0                      })
AAdd(aTabAux,{"COD_VEND"  ,"C",10                     ,0                      })
AAdd(aTabAux,{"NOME_VEND" ,"C",50                     ,0                      })
AAdd(aTabAux,{"COD_FRANQ" ,"C",08                     ,0                      })
AAdd(aTabAux,{"NOME_FRANQ","C",30                     ,0                      })
AAdd(aTabAux,{"MOTIVO_VIP","C",50                     ,0                      })
AAdd(aTabAux,{"MONITORADO","C",50                     ,0                      })
AAdd(aTabAux,{"MOT_MONIT" ,"C",50                     ,0                      })
AAdd(aTabAux,{"VCT_MONIT" ,"D",08                     ,0                      })
AAdd(aTabAux,{"PAS_MONIT" ,"N",10                     ,0                      })
AAdd(aTabAux,{"EMP_PUBLIC","C",50                     ,0                      })
AAdd(aTabAux,{"STAT_SERA" ,"C",30                     ,0                      })
AAdd(aTabAux,{"ATEND_CLI" ,"C",30                     ,0                      })
AAdd(aTabAux,{"VENC_SENHA","D",08                     ,0                      })
AAdd(aTabAux,{"UNID_ATEND","C",50                     ,0                      })
AAdd(aTabAux,{"NOME_UNID" ,"C",50                     ,0                      })
AAdd(aTabAux,{"HISTORICO" ,"C",100                    ,0                      })
AAdd(aTabAux,{"MENSAGEM"  ,"C",254                    ,0                      })
AAdd(aTabAux,{"PROPTITULO","C",08                     ,0                      })
AAdd(aTabAux,{"PROP_CLI"  ,"C",08                     ,0                      })
AAdd(aTabAux,{"DT_PROP"   ,"D",08                     ,0                      })
AAdd(aTabAux,{"ULT_ACAO"  ,"C",100                    ,0                      })
AAdd(aTabAux,{"ULT_REGUA" ,"C",100                    ,0                      })
AAdd(aTabAux,{"ULT_LOTE"  ,"C",12                     ,0                      })

If Subs(cCRR002X,4,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
	AAdd(aTabAux,{"CCUSTO"    ,"C",09                     ,0                      })
	AAdd(aTabAux,{"DESC_CCUST","C",75                     ,0                      })
	AAdd(aTabAux,{"CLASSE_VLR","C",09                     ,0                      })
	AAdd(aTabAux,{"DESC_CLVLR","C",40                     ,0                      })
	AAdd(aTabAux,{"ITEM_CTB"  ,"C",09                     ,0                      })
	AAdd(aTabAux,{"DESC_ITCTB","C",40                     ,0                      })
EndIf

If Subs(cCRR002X,6,1)=="S" //1=Email_Debug,2=Envia_Anexo,3=Desativa_SaldoTit,4=Campos_CCusto,5=dbSeek_PJ9,6=Colunas_SaldoTit
	AAdd(aTabAux,{"E1_VALOR"  ,"N",TamSX3('E1_VALOR'  )[1],TamSX3('E1_VALOR'  )[2]})
	AAdd(aTabAux,{"E1_SALDO"  ,"N",TamSX3('E1_SALDO'  )[1],TamSX3('E1_SALDO'  )[2]})
	AAdd(aTabAux,{"SALDOTIT"  ,"N",TamSX3('E1_SALDO'  )[1],TamSX3('E1_SALDO'  )[2]})
	AAdd(aTabAux,{"DIF1"      ,"N",TamSX3('E1_SALDO'  )[1],TamSX3('E1_SALDO'  )[2]})
	AAdd(aTabAux,{"IMPOSTOS"  ,"N",TamSX3('E1_SALDO'  )[1],TamSX3('E1_SALDO'  )[2]})
	AAdd(aTabAux,{"SOMAABAT"  ,"N",TamSX3('E1_SALDO'  )[1],TamSX3('E1_SALDO'  )[2]})
	AAdd(aTabAux,{"DIF2"      ,"N",TamSX3('E1_SALDO'  )[1],TamSX3('E1_SALDO'  )[2]})
EndIf

Return(aTabAux)
//-----------------------------------------------------------------------------

Static Function fDtoC(dData) //Retorna DD/MM/YYYY
Local cData:=DtoS(dData)
Return(Subs(cData,7,2)+"/"+Subs(cData,5,2)+"/"+Subs(cData,1,4))
//-----------------------------------------------------------------------------

Static Function fRetFilSE1(__aSelFil)  //Retorna as filiais do SE1
Local aAreaAnt :=GetArea()
Local cAlsFil  :=GetNextAlias()
Local nX       :=0
Local cFilSE1  :=""
Local cQryAux  :=""

For nX := 1 to len(__aSelFil)
	cFilSE1 += "'" + __aSelFil[nX] + "',"
Next nX

cFilSE1 := SubStr(AllTrim(cFilSE1), 1, Rat(',', AllTrim(cFilSE1)) - 1)

cQryAux := " SELECT DISTINCT E1_FILIAL FROM " + RetSqlName("SE1") + " SE1 "
cQryAux += " WHERE E1_FILIAL IN (" + cFilSE1 + ") AND SE1.D_E_L_E_T_ = ' ' "

Sleep(1000)
IncProc('Selecionando Filial(is)...')

If Select(cAlsFil) > 0; (cAlsFil)->(dbCloseArea()); Endif
dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQryAux),cAlsFil,.T.,.T.)

__aSelFil := {}

cFilSE1 := ''

While !(cAlsFil)->(Eof()) 
	AADD(__aSelFil, (cAlsFil)->E1_FILIAL)
	cFilSE1 += "'" + (cAlsFil)->E1_FILIAL + "',"
	(cAlsFil)->(DbSkip())
End

(cAlsFil)->(dbCloseArea())  //Fecha cAlsFil

cFilSE1 := SubStr(AllTrim(cFilSE1), 1, Rat(',', AllTrim(cFilSE1)) - 1)

RestArea(aAreaAnt)
Return(cFilSE1)
//-----------------------------------------------------------------------------

Static Function fVerAnexo(cExcelArq,cExcelNom)  //Verifica tamanho máximo do arquivo para enviar por e-mail
Local nMaximo:=SuperGetMV("TI_CRR002M",,1000000)  //Tamanho máximo do arquivo para enviar por e-mail
Local aDir   :={}
Local cRet   :=""

aDir:=Directory(cExcelArq+cExcelNom)
If Len(aDir)>0
	If aDir[1,2]<=nMaximo  //Tamanho do arquivo
		cRet:=cExcelArq+cExcelNom
	EndIf
EndIf

Return(cRet)
//-----------------------------------------------------------------------------

//-----------------------------------------------------------------------------
// Funções para cópia dos arquivos gerados - Início
//-----------------------------------------------------------------------------
Static Function fGerados(cExcelArq,cExcelTmp)  //Verifica se tem arquivos gerados para o usuário
Local aArquivos:={}
Local aDir     :={}
Local aDirFim  :={}
Local nPos     :=0
Local nPosFim  :=0
Local lTodos   :=.f.  //Todos os arquivos
Local cUsersAdm:=SuperGetMV("TI_CRR002A",,"286603,286334")  //Usuários Admin

Local lRet     :=.f.
Local cRet     :=""

If __cUserId $ cUsersAdm  //Usuários Admin
	lTodos:=.t.  //Todos os arquivos
Else
	lTodos:=.f.  //Todos os arquivos
EndIf

If lTodos  //Todos os arquivos
	aDir:=Directory(cExcelArq+"*.*")
Else
	aDir:=Directory(cExcelArq+"*.dbf")
EndIf

If Len(aDir)>0
	aDirFim:=Directory(cExcelArq+"*.fim")
	For nPos:=1 to Len(aDir)
		If Subs(aDir[nPos,1],1,Len(__cUserId))==__cUserId .or. lTodos  //Todos os arquivos
			If Lower(Right(aDir[nPos,1],4))==".dbf"  //Se foi gerado pela rotina
				nPosFim:=Ascan(aDirFim,{|x| StrTran(Lower(AllTrim(x[1])),".fim","")==StrTran(Lower(AllTrim(aDir[nPos,1])),".dbf","")})  //Verifica se já terminou
				AAdd(aArquivos,{aDir[nPos,1],aDir[nPos,2],aDir[nPos,3],aDir[nPos,4],aDir[nPos,5],If(nPosFim==0,"0","1")})  //6=Status: 0=Gerando,1=Gerado,2=Outros
			Else
				AAdd(aArquivos,{aDir[nPos,1],aDir[nPos,2],aDir[nPos,3],aDir[nPos,4],aDir[nPos,5],"2"})  //6=Status: 0=Gerando,1=Gerado,2=Outros
			EndIf
		EndIf
	Next
EndIf

If Len(aArquivos)>0
	cRet:=fTelaCopia(aArquivos,cExcelArq,cExcelTmp,cUsersAdm)  //Browse com os arquivos gerados
	lRet:=(Upper(cRet)$Upper("Copiar,Excluir,Refresh"))
EndIf

Return(lRet)
//-----------------------------------------------------------------------------

Static Function fTelaCopia(aArquivos,cExcelArq,cExcelTmp,cUsersAdm)  //Browse com os arquivos gerados
Local aObjects,aInfo,aPosGet,aPosObj,aSizeAut
Local cTitJan:="Arquivos gerados no servidor"
Local aButtons:={}
Local oDlg,oBrowse,aCols,aHeader,bHeader
Local nPos,cOk:=""

Local bMark     :={ || If(U_FR002XMk(oBrowse),'LBOK','LBNO') } //xxxMark
Local bLDblClick:={ |oBrowse| U_FR002XMa(oBrowse,cMarca,cUsersAdm) } //xxxMarca
Local bHeaderCli:={ |oBrowse| U_FR002XMl(oBrowse,cMarca,cUsersAdm) } //xxxMkAll

Local cMarca:=GetMark()

Local bOk     :={|| (cOk:="Ok"     ,If(fCopiaArqs(oBrowse,cMarca,cExcelArq,cExcelTmp,"Mover"  ),oDlg:End(),"") )}
Local bCancel :={|| (cOk:="Cancel" ,oDlg:End())}
Local bExcluir:={|| (cOk:="Excluir",If(fCopiaArqs(oBrowse,cMarca,cExcelArq,cExcelTmp,"Excluir"),oDlg:End(),"") )}
Local bCopiar :={|| (cOk:="Copiar" ,If(fCopiaArqs(oBrowse,cMarca,cExcelArq,cExcelTmp,"Copiar" ),oDlg:End(),"") )}
Local bRefresh:={|| (cOk:="Refresh",If(fCopiaArqs(oBrowse,cMarca,cExcelArq,cExcelTmp,"Refresh"),oDlg:End(),"") )}

AAdd( aButtons, {"HISTORIC", bExcluir, "Excluindo...", "Excluir" , {|| .T.}} )
AAdd( aButtons, {"HISTORIC", bCopiar , "Copiando..." , "Copiar"  , {|| .T.}} )
AAdd( aButtons, {"HISTORIC", bRefresh, "Aguarde..."  , "Refresh" , {|| .T.}} )

aCols:={}
For nPos:=1 to Len(aArquivos)
	aCols0:={}
	AAdd(aCols0,Space(Len(cMarca))                 )  //Coluna da marca
	AAdd(aCols0,aArquivos[nPos,1]                  )  //Nome do arquivo
	AAdd(aCols0,AllTrim(Str(aArquivos[nPos,2]))    )  //Tamanho
	AAdd(aCols0,fTrazArq(aArquivos[nPos],"DataIni"))  //Data Ini
	AAdd(aCols0,fTrazArq(aArquivos[nPos],"HoraIni"))  //Hora Ini
	AAdd(aCols0,fTrazArq(aArquivos[nPos],"DataFim"))  //Data Fim
	AAdd(aCols0,fTrazArq(aArquivos[nPos],"HoraFim"))  //Hora Fim
	AAdd(aCols0,If(aArquivos[nPos,6]=="0","Em processamento",If(aArquivos[nPos,6]=="1","Gerado com sucesso","Outros")) )  //6=Status: 0=Gerando,1=Gerado,2=Outros
	AAdd(aCols,aCols0)
Next

If Len(aCols)==0
	If MsgYesNo("Não foram encontrados arquivos para este usuario.")
		Return(cOk)
	EndIf
EndIf

nPos:=0
aHeader:={}
++nPos  //Primeira Coluna = Marca
bHeader:="{||aCols[oBrowse:At(),"+StrZero(++nPos,2)+"]}" ; AAdd(aHeader,{"Arquivo"    ,&bHeader,"C","@X",1,40,00})
bHeader:="{||aCols[oBrowse:At(),"+StrZero(++nPos,2)+"]}" ; AAdd(aHeader,{"Tamanho"    ,&bHeader,"C","@X",1,10,00})
bHeader:="{||aCols[oBrowse:At(),"+StrZero(++nPos,2)+"]}" ; AAdd(aHeader,{"Data Inicio",&bHeader,"C","@X",1,10,00})
bHeader:="{||aCols[oBrowse:At(),"+StrZero(++nPos,2)+"]}" ; AAdd(aHeader,{"Hora Inicio",&bHeader,"C","@X",1,08,00})
bHeader:="{||aCols[oBrowse:At(),"+StrZero(++nPos,2)+"]}" ; AAdd(aHeader,{"Data Fim"   ,&bHeader,"C","@X",1,10,00})
bHeader:="{||aCols[oBrowse:At(),"+StrZero(++nPos,2)+"]}" ; AAdd(aHeader,{"Hora Fim"   ,&bHeader,"C","@X",1,08,00})
bHeader:="{||aCols[oBrowse:At(),"+StrZero(++nPos,2)+"]}" ; AAdd(aHeader,{"Status"     ,&bHeader,"C","@X",1,20,00})

///////////////////////////////////////////////////////////////////////////////
If oMainWnd:nClientWidth > 800
	aObjects:={}
	AAdd(aObjects,{100,001,.T., .T. } )  //Enchoice
	AAdd(aObjects,{100,099,.T., .T. } )  //MsGetDados
Else
	aObjects:={}
	AAdd(aObjects,{100,002,.T., .T. } )  //Enchoice
	AAdd(aObjects,{100,098,.T., .T. } )  //MsGetDados
EndIf
aSizeAut:=MsAdvSize()
aInfo   :={aSizeAut[1],aSizeAut[2],aSizeAut[3],aSizeAut[4],3,3}
aPosObj :=MsObjSize( aInfo, aObjects, .T. , .F. )
aPosGet :=MsObjGetPos((aSizeAut[3]-aSizeAut[1]),315,{{004,024,240,270}} )
///////////////////////////////////////////////////////////////////////////////

SetKey(VK_F5, bRefresh)  //F5=Refresh

DEFINE MSDIALOG oDlg FROM aSizeAut[7],0 TO aSizeAut[6],aSizeAut[5] TITLE OemToAnsi(cTitJan) Of oMainWnd PIXEL

//
@ aPosObj[1,1],aPosObj[1,2] MSPANEL oPanel1 PROMPT "" SIZE 10,01 OF oDlg
@ aPosObj[2,1],aPosObj[2,2] MSPANEL oPanel2 PROMPT "" SIZE 10,10 OF oDlg
oPanel1:Align:=CONTROL_ALIGN_TOP
oPanel2:Align:=CONTROL_ALIGN_ALLCLIENT
//

oBrowse:=FWBrowse():New()
oBrowse:SetDataArray()
oBrowse:SetArray(aCols)
oBrowse:AddMarkColumns(bMark,bLDblClick,bHeaderCli)
oBrowse:SetColumns(aHeader)
oBrowse:SetOwner(oDlg)
oBrowse:DisableReport()
oBrowse:DisableConfig()
oBrowse:Activate()
Activate MsDialog oDlg CENTERED On Init EnchoiceBar(oDlg,bOk,bCancel,,aButtons)

SetKey(VK_F5, {||})  //F5=Refresh

Return(cOk)
//-----------------------------------------------------------------------------

Static Function fCopiaArqs(oBrowse,cMarca,cExcelArq,cExcelTmp,cAcao)  //Copia os arquivos selecionados
Local aTabAux  :=Aclone(oBrowse:Data():aArray)
Local aArquivos:={}
Local lRet     :=.t.
Local nPos     :=0
Local nPosMar  :=1  //Posição da coluna com a marca

If Upper(cAcao)==Upper("Refresh")
	lRet:=.t.
	Return(lRet)
EndIf

For nPos:=1 to Len(aTabAux)
	If aTabAux[nPos,nPosMar]==cMarca
		AAdd(aArquivos,{aTabAux[nPos,2],aTabAux[nPos,3],aTabAux[nPos,4],aTabAux[nPos,5]})
	EndIf
Next

If Len(aArquivos)>0
	Processa({|| fCpyS2T(aArquivos,cExcelArq,cExcelTmp,cAcao) },"Processando...")  //Copia do servidor para a máquina local
	lRet:=.t.
Else
	MsgAlert("Nenhum arquivo selecionado.")
	lRet:=.f.
EndIf

Return(lRet)
//-----------------------------------------------------------------------------

Static Function fCpyS2T(aArquivos,cExcelArq,cExcelTmp,cAcao)  //Copia do servidor para a máquina local
Local nPos      :=0
Local nRegistros:=0
Local cExcelNom :=""

nRegistros:=Len(aArquivos)
If Upper(cAcao)$Upper("Mover,Copiar")
	nRegistros+=Len(aArquivos)
EndIf
If Upper(cAcao)$Upper("Mover,Excluir")
	nRegistros+=Len(aArquivos)
EndIf
If Upper(cAcao)$Upper("Mover")
	nRegistros+=Len(aArquivos)
EndIf

ProcRegua(nRegistros)  //Numero de registros a processar

For nPos:=1 to Len(aArquivos)

	IncProc()

	cExcelNom:=Lower(AllTrim(aArquivos[nPos,1]))  //Nome do arquivo

	If File(cExcelArq+cExcelNom) //Achou arquivo no servidor?
		If Upper(cAcao)$Upper("Mover,Copiar")

			IncProc("Copiando arquivo ["+cExcelNom+"] para maquina local...")

			CpyS2T(cExcelArq+cExcelNom,cExcelTmp)  //Copia o arquivo do servidor para a máquina local

			If Upper(cAcao)$Upper("Mover")
				If File(cExcelTmp+cExcelNom) //Achou arquivo na máquina local?
				Else
					MsgAlert("ERRO: Problemas na copia do arquivo ["+cExcelNom+"] para maquina local.")
				EndIf
			EndIf
		EndIf

		If Upper(cAcao)$Upper("Mover,Excluir")

			IncProc("Excluindo arquivo ["+cExcelNom+"] do servidor...")

			If Upper(cAcao)$Upper("Mover")
				If File(cExcelTmp+cExcelNom) //Achou arquivo na máquina local?
					If File(cExcelArq+cExcelNom) //Achou arquivo no servidor?
						FErase(cExcelArq+cExcelNom)
					EndIf
					If File(cExcelArq+cExcelNom) //Achou arquivo no servidor?
						MsgAlert("ATENCAO: Problemas na exclusão do arquivo ["+cExcelNom+"] no servidor.")
					EndIf

					cExcelNom:=StrTran(cExcelNom,".dbf",".fim")  //Apaga o arquivo ".fim"

					IncProc("Excluindo arquivo ["+cExcelNom+"] do servidor...")

					If File(cExcelArq+cExcelNom) //Achou arquivo no servidor?
						FErase(cExcelArq+cExcelNom)
					EndIf
				Else
					MsgAlert("ERRO: Problemas na copia do arquivo ["+cExcelNom+"] para maquina local.")
				EndIf
			Else
				If File(cExcelArq+cExcelNom) //Achou arquivo no servidor?
					FErase(cExcelArq+cExcelNom)
				EndIf
				If File(cExcelArq+cExcelNom) //Achou arquivo no servidor?
					MsgAlert("ATENCAO: Problemas na exclusão do arquivo ["+cExcelNom+"] no servidor.")
				EndIf
			EndIf
		EndIf
	Else
		//MsgAlert("ERRO: Problemas na gravacao do arquivo ["+cExcelNom+"] no servidor.")
	EndIf
Next

Return
//-----------------------------------------------------------------------------

Static Function fTrazArq(aArquivos,cQual)  //Retorna dados do nome do arquivo
Local cRet:=aArquivos[1]
Local cArq:=".dbf,.fim,.log,.sql,.err"  //Se foi gerado pela rotina

If Lower(Right(cRet,4))$Lower(cArq)  //Se foi gerado pela rotina
	Do Case
	Case Upper(cQual)==Upper("DataIni")
		cRet:=DtoC(StoD( Subs(cRet,07,08)               )) //xxxxxx20200415080102.dbf
	Case Upper(cQual)==Upper("HoraIni")
		cRet:=Transform( Subs(cRet,15,06) , "@R XX:XX:XX") //xxxxxx20200415080102.dbf
	Case Upper(cQual)==Upper("DataFim")
		If Lower(Right(cRet,4))$Lower(".dbf")
			If aArquivos[6]=="0"  //6=Status: 0=Gerando,1=Gerado,2=Outros
				cRet:=CtoD("")                 //Data
			Else
				cRet:=DtoC(aArquivos[3])       //Data
			EndIf
		Else
			cRet:=DtoC(aArquivos[3]) //Data
		EndIf
	Case Upper(cQual)==Upper("HoraFim")
		If Lower(Right(cRet,4))$Lower(".dbf")
			If aArquivos[6]=="0"  //6=Status: 0=Gerando,1=Gerado,2=Outros
				cRet:=Space(Len(aArquivos[4])) //Hora
			Else
				cRet:=aArquivos[4]             //Hora
			EndIf
		Else
			cRet:=aArquivos[4]       //Hora
		EndIf
	EndCase
Else
	Do Case
	Case Upper(cQual)==Upper("DataIni") .or. Upper(cQual)==Upper("DataFim")
		cRet:=DtoC(aArquivos[3]) //Data
	Case Upper(cQual)==Upper("HoraIni") .or. Upper(cQual)==Upper("HoraFim")
		cRet:=aArquivos[4]       //Hora
	EndCase
EndIf

Return(cRet)
//-----------------------------------------------------------------------------

User Function FR002XMa(oBrowse,cMarca,cUsersAdm)  //xxxMarca
Local lRet   :=.t.
Local nPos   :=0  //Posição do registro no array
Local nPosMar:=1  //Posição da coluna com a marca
Local nPosFim:=7  //Posição da coluna Hora Fim
Local lEmProc:=.f.  //Em Processamento

nPos:=oBrowse:At()  //Posição do registro no array

If __cUserId $ cUsersAdm  //Usuários Admin
	lEmProc:=.f.                                         //Em processamento
Else
	lEmProc:=Empty(oBrowse:Data():aArray[nPos,nPosFim])  //Em processamento
EndIf

If lEmProc  //Se estiver em processamento
	MsgAlert("Arquivo em processamento, nao pode ser selecionado.")
EndIf

If Empty(oBrowse:Data():aArray[nPos,nPosMar]) .and. !lEmProc  //Se não estiver em processamento
	oBrowse:Data():aArray[nPos,nPosMar]:=cMarca
Else
	oBrowse:Data():aArray[nPos,nPosMar]:=Space(01)
EndIf

lRet:=U_FR002XMk(oBrowse)  //xxxMark

Return(lRet)
//-----------------------------------------------------------------------------

User Function FR002XMk(oBrowse)  //xxxMark
Local lRet   :=.t.
Local nPosMar:=1  //Posição da coluna com a marca

lRet:=(!Empty(oBrowse:Data():aArray[oBrowse:At(),nPosMar]))

Return(lRet)
//-----------------------------------------------------------------------------

User Function FR002XMl(oBrowse,cMarca,cUsersAdm)  //xxxMkAll
Local nPos   :=0  //Posição do registro no array
Local nPosMar:=1  //Posição da coluna com a marca
Local nPosFim:=7  //Posição da coluna Hora Fim
Local lEmProc:=.f.  //Em Processamento

nPos:=oBrowse:At()

If __cUserId $ cUsersAdm  //Usuários Admin
	lEmProc:=.f.                                         //Em processamento
Else
	lEmProc:=Empty(oBrowse:Data():aArray[nPos,nPosFim])  //Em processamento
EndIf

For nPos:=1 to Len(oBrowse:Data():aArray)
	If Empty(oBrowse:Data():aArray[nPos,nPosMar]) .and. !lEmProc  //Se não estiver em processamento
		oBrowse:Data():aArray[nPos,nPosMar]:=cMarca
	Else
		oBrowse:Data():aArray[nPos,nPosMar]:=Space(01)
	EndIf
Next

oBrowse:Refresh()

Return
//-----------------------------------------------------------------------------
//-----------------------------------------------------------------------------
// Funções para cópia dos arquivos gerados - Fim
//-----------------------------------------------------------------------------

//-----------------------------------------------------------------------------
// Funções para Selecao de registros - Início
//-----------------------------------------------------------------------------
/*/{Protheus.doc} FR002P02
(Consulta Padrao Customizada, monta uma tela de selecao selecionando os itens que deseja;
filtrar no relatorio.)
<AUTHOR>
@since 22/07/2015
@version 1.0
@param nOpcPerg, numerico, (1=Empresas;2=Tipo de cliente;3=Bancos ou portadores)
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
User Function FR002P02( nOpcPerg )
	//Local oModel 		:= FWModelActive()
	Local oDlg
	Local cTitulo := "Escolha as Unidades de ServiCo"
	Local bClick
	Local oChkQual
	Local bSetGet
	Local oGroup
	Local bDBLClick
	Local oQual
	Local oBtnOK
	Local oBtnCan
	//Local oBtnFil
	Local aHeader 		:= {"", "Codigo","Unidade de ServiCo"}
	Local lQual
//Carrega bitmaps
	Local oOk       	:= LoadBitmap( GetResources(), "LBOK")
	Local oNo       	:= LoadBitmap( GetResources(), "LBNO")
	Local lRet := .T.
	Local nX
	Local cCPAlias		:= GetNextAlias()
	Local aAreaSm0		:= SM0->(GetArea())
	Local aAreaSX3		:= SX3->(GetArea())
	Local cCodempAtu	:= SM0->M0_CODIGO
	Local aArray		:= {}
	Local cTpCliente	:= "" //"1=VIP;2=Padrao;3=Governo;4=Private;5=Gens PM;6=TFS;7=VBP;8=PBP;9=Franquia;P=Parceiro;G=Gare;U=Unidade Propria"  // Tipos de cliente
	Local aTpCli		:= "" //SEPARA(cTpCliente,";") //1=VIP;2=Padrao;3=Governo;4=Private;5=Gens PM;6=TFS;7=VBP;8=PBP
	Local ni			:= 0
	
	If nOpcPerg == 2
		cRetConXb := &(ReadVar())
	Else
		cRetConXb := ""
	EndIF

//Retorna os motivos que podem ser utilizados neste motivo
//cRetConXb := If(oModel:GetOperation() =  MODEL_OPERATION_INSERT,"", cRetConXb)

	If nOpcPerg == 1 // -- Empresas
		cTitulo	:= "Selecione as empresas/filiais desejadas"

		dbSelectArea("SM0")
		SM0->(dbGoTop())
		While SM0->(!EoF())
			If cCodempAtu == SM0->M0_CODIGO
				Aadd(aArray, {(SM0->( M0_CODFIL) $ cRetConXb), SM0->( M0_CODFIL), SM0->( M0_FILIAL )})
			EndIf
		
			SM0->( dbSkip() )
		EndDo
	ElseIf nOpcPerg == 2 // -- Tipo de cliente
		SX3->(DbSetOrder(2))
		If SX3->(msseek("A1_XCLIVIP"))
			cTpCliente	:= AllTrim(X3CBox())
		Else
			cTpCliente	:= "1=VIP;2=Padrao;3=Setor Publico;4=Large;5=Gens PM;6=TFS;7=VBP;8=PBP;9=Franquia;P=Parceiro;G=Gare;U=Unidade Propria"
		EndIf
		aTpCli := SEPARA(cTpCliente,";")
		cTitulo	:= "Selecione os tipos de clientes desejados"
	
		For ni := 1 to Len(aTpCli)
			Aadd(aArray, {Left(aTpCli[ni],AT("=",aTpCli[ni])-1) $ cRetConXb, Left(aTpCli[ni],AT("=",aTpCli[ni])-1), SubStr(aTpCli[ni],AT("=",aTpCli[ni])+1,Len(aTpCli[ni]))})
		Next
	
	ElseIf nOpcPerg == 3 // -- Bancos ou portadores

		cTitulo	:= "Selecione os bancos desejados"
		If Select(cCPAlias) > 0
			dbSelectArea(cCPAlias)
			(cCPAlias)->(DbCloseArea())
		EndIf

		BeginSql Alias cCPAlias
			SELECT DISTINCT A6_COD, MAX(A6_NOME) A6_NOME
			  FROM %table:SA6% SA6
			 WHERE A6_FILIAL = %xFilial:SA6%
			  	   AND SA6.%notDel%
			GROUP BY A6_COD
			ORDER BY A6_COD
		EndSql
	
	//-----------------------------------------------------------------------------
	// Atualizo o array e a variavel para gravar os dados que serao selecionados //
	//-----------------------------------------------------------------------------	
		While (cCPAlias)->(!EoF())
			Aadd(aArray, {((cCPAlias)->( A6_COD) $ cRetConXb), (cCPAlias)->( A6_COD), (cCPAlias)->(A6_NOME )})
			(cCPAlias)->( dbSkip() )
		EndDo

		(cCPAlias)->(DbCloseArea())
	
	ElseIf nOpcPerg == 4 // -- SituaCões

		cTitulo	:= "Selecione as situaCões desejadas"
	
		If Select(cCPAlias) > 0
			dbSelectArea(cCPAlias)
			(cCPAlias)->(DbCloseArea())
		EndIf
		
		BeginSql Alias cCPAlias
			SELECT	FRV_CODIGO, FRV_DESCRI
			  FROM	%table:FRV%	 FRV
			 WHERE	FRV_FILIAL = %xFilial:FRV%
				   AND FRV.%notDel%
			ORDER BY FRV_FILIAL, FRV_CODIGO
		EndSql
	
	//-----------------------------------------------------------------------------
	// Atualizo o array e a variavel para gravar os dados que serao selecionados //
	//-----------------------------------------------------------------------------	
	
		While (cCPAlias)->(!EoF())
			Aadd(aArray, {((cCPAlias)->( FRV_CODIGO) $ cRetConXb), (cCPAlias)->( FRV_CODIGO), (cCPAlias)->(FRV_DESCRI )})
			(cCPAlias)->( dbSkip() )
		EndDo
		(cCPAlias)->(DbCloseArea())

	ElseIf nOpcPerg == 5 // -- SituaCões

		cTitulo	:= "Selecione os Tipos de Titulo"
		cTab    := '05'
		
		If Select(cCPAlias) > 0
			dbSelectArea(cCPAlias)
			(cCPAlias)->(DbCloseArea())
		EndIf
		
		BeginSql Alias cCPAlias
			SELECT X5_CHAVE, X5_DESCRI 
			  FROM %table:SX5% SX5 
			 WHERE SX5.X5_FILIAL = %xFilial:SX5%
			   AND SX5.X5_TABELA = %exp:cTab%
			   AND SX5.%notDel%
			GROUP BY X5_CHAVE, X5_DESCRI
			ORDER BY X5_CHAVE, X5_DESCRI
		EndSql
	
		//-----------------------------------------------------------------------------
		// Atualizo o array e a variavel para gravar os dados que serao selecionados //
		//-----------------------------------------------------------------------------	
		While (cCPAlias)->(!EoF())
			Aadd(aArray, {((cCPAlias)->(X5_CHAVE) $ cRetConXb), (cCPAlias)->(X5_CHAVE), (cCPAlias)->(X5_DESCRI)})
			(cCPAlias)->( dbSkip() )
		EndDo
		(cCPAlias)->(DbCloseArea())
	EndIf


	If Len(aArray) > 0
		oDlg:= MSDIALOG():New(000,000,300,625, cTitulo,,,,,,,,,.T.)
		oDlg:lEscClose := .F.
		oGroup := TGROUP():New(005,015,125,300,cTitulo,oDlg,,,.T.)
		
		bClick := {||(AEval(aArray, {|z| z[1] := If(z[1]==.T.,.F.,.T.)}), oQual:Refresh(.F.))}
		bSetGet:= {|l| IIF(PCount()>0,lQual:=l,lQual)}
		oChkQual:= tCheckBox():New(134,20,"Inverte Selecao",bSetGet,oDlg,50,10,,bClick,,,,,,.T.)
	
		bDBLClick := {|| (aArray:=FR002C01(oQual:nAt,aArray),oQual:Refresh())}
		oQual := TwBrowse():New(20,20,273,100,, aHeader ,,oDlg,,,,,bDBLClick,,,,,,,.F.,,.T.,,.F.,,,)
		oQual:SetArray(aArray)
		oQual:bLine := { || {If(aArray[oQual:nAt,1],oOk,oNo),aArray[oQual:nAt,2],aArray[oQual:nAt,3]}}
		@ 134, 180 	BUTTON oLimpa 	Prompt "Limpar" 	SIZE 25 ,11  ACTION {|| (AEval(aArray, {|z| z[1] := .F.})) } OF oDlg PIXEL
		oBtnOK  := SBUTTON():New(134, 210, 01, {|| (lRet := .T.,oDlg:End())}, oDlg, .T., , {|| .T.})
		oBtnCan := SBUTTON():New(134, 240, 02, {|| (lRet := .F.,oDlg:End())}, oDlg, .T., , {|| .T.})
	
		oDlg:lCentered := .T.
		oDlg:Activate()
	ELSE
		If nOpcPerg == 1
			HELP(" ",1,"NORECS",,"Nao existem Empresas Cadastradas!",1,0)
		ElseIf nOpcPerg == 3
			HELP(" ",1,"NORECS",,"Nao existem Bancos/Portadores Cadastrados!",1,0)
		ElseIf nOpcPerg == 4
			HELP(" ",1,"NORECS",,"Nao existem SituaCões Cadastradas!",1,0)
		EndIf
	ENDIF
// Se a rotina confirmou a selecao de motivos cocatenar
	if lRet

		cRetConXb := ""
	
		for nX := 1 to Len(aArray)

			if aArray[nX, 1 ]
	
				if Len(cRetConXb) > 0
					cRetConXb += ";"
				endif
				cRetConXb += aArray[nX, 2]
			endif
	 
		next
	else
		cRetConXb := ""
	endif

	SX3->(RestArea(aAreaSx3))
	SM0->(RestArea(aAreaSM0))
Return lRet

/*/{Protheus.doc} FR002C01
(Funcao de marcar tudo ou desrmacar da tela de consulta padrao por selecao.)
<AUTHOR>
@since 05/11/2015
@version 1.0
@param nIt, numerico, (Descricao do parametro)
@param aArray, array, (Descricao do parametro)
@return ${return}, ${return_description}
@example
(examples)
@see (links_or_references)
/*/
Static Function FR002C01(nIt,aArray)
	aArray[nIt,1] := !aArray[nIt,1]
Return aArray

/*/{Protheus.doc} FR002P05
(Carrega array com as Empresas e Filias do sistema)
<AUTHOR>
@since 29/07/2015
@version 1.0
@param aSintetic, array, (Descricao do parametro)
@return ${aSm0},;
${Array de duas posiCões contendo na primeira posicao Codigo da empresa + Cod. Filial;
 e na segunda posicao Descricao da filial}
@example
(examples)
@see (links_or_references)
/*/
Static Function FR002P05()
	Local aSM0 := {}
	Local aSm0Ali := SM0->(GetArea())
	Local cCodEmpAtu	:= SM0->M0_CODIGO
	DbSelectArea("SM0")
	DbGoTop()

	While !EOF()
		If SM0->M0_CODIGO == cCodEmpAtu
			AADD(aSm0,{SM0->M0_CODFIL,SM0->M0_FILIAL})
		EndIf
		SM0->(DbSkip())
	End

	SM0->(RestArea(aSm0Ali))

Return aSm0
//-----------------------------------------------------------------------------

Static Function fTrazCombo(aCombo,cCampo) //Traz a descrição do ComboBox
Local nPos:=0
Local cRet:=""

nPos:=Ascan(aCombo,{|x| Subs(x,1,Len(cCampo))==cCampo})

If nPos>0
	cRet:=Subs(aCombo[nPos],Len(cCampo)+2)
EndIf

Return(cRet)
//-----------------------------------------------------------------------------
// Funções para Selecao de registros - Fim
//-----------------------------------------------------------------------------

Static Function fTabPJ9(pFilial,cPrefixo,cNum,cParcela,cTipo,cCliente,cLoja) //Retorna PJ9_LOTE,PJ2_DESCRI,PJ4_DESCRI
Local aAreaPJ9:=PJ9->(GetArea())
Local aAreaPJ2:=PJ2->(GetArea())
Local aAreaPJ4:=PJ4->(GetArea())
Local aRet:={"","",""}

PJ9->(dbSetOrder(10))  //PJ9_FILTIT+PJ9_PREFIX+PJ9_NUMTIT+PJ9_PARCEL+PJ9_TIPTIT+PJ9_DATA+PJ9_HORA
PJ9->(dbSeek(pFilial+cPrefixo+cNum+cParcela+cTipo))  //Posiciona no PJ9

If PJ9->(!Eof())
	PJ2->(dbSetOrder(1))  //PJ2_FILIAL+PJ2_CODIGO
	PJ2->(dbSeek(xFilial("PJ2") +PJ9->PJ9_CODACA))  //Posiciona no PJ2
	PJ4->(dbSetOrder(1))  //PJ4_FILIAL+PJ4_CODREG
	PJ4->(dbSeek(PJ9->PJ9_FILIAL+PJ9->PJ9_CODREG))  //Posiciona no PJ4
	aRet:={PJ9->PJ9_LOTE,PJ2->PJ2_DESCRI,PJ4->PJ4_DESCRI}
EndIf

PJ9->(RestArea(aAreaPJ9))
PJ2->(RestArea(aAreaPJ2))
PJ4->(RestArea(aAreaPJ4))
Return(aRet)
//-----------------------------------------------------------------------------

User Function FR002P06()

Return	cRetConXb
//-----------------------------------------------------------------------------

Static Function fTrataChar(cVar) //Trata os caracteres especiais do campo informado
Local nPos:=0
Local cPos:=""
Local nAsc:=0
Local nRet:=0
Local cRet:=""
Local aRet:={}

Local nSimD  :=032 //Considera Chr() de
Local nSimA  :=125 //Considera Chr() at�
Local nTrataD:=192 //Trata Chr() de
Local nTrataA:=255 //Trata Chr() at�

//Tratar Chr(192) a Chr(255)//
AAdd(aRet,{192,"A"})
AAdd(aRet,{193,"A"})
AAdd(aRet,{194,"A"})
AAdd(aRet,{195,"A"})
AAdd(aRet,{196,"A"})
AAdd(aRet,{197,"A"})
AAdd(aRet,{198,"_"})
AAdd(aRet,{199,"C"})
AAdd(aRet,{200,"E"})
AAdd(aRet,{201,"E"})
AAdd(aRet,{202,"E"})
AAdd(aRet,{203,"E"})
AAdd(aRet,{204,"I"})
AAdd(aRet,{205,"I"})
AAdd(aRet,{206,"I"})
AAdd(aRet,{207,"I"})
AAdd(aRet,{208,"_"})
AAdd(aRet,{209,"N"})
AAdd(aRet,{210,"O"})
AAdd(aRet,{211,"O"})
AAdd(aRet,{212,"O"})
AAdd(aRet,{213,"O"})
AAdd(aRet,{214,"O"})
AAdd(aRet,{215,"_"})
AAdd(aRet,{216,"_"})
AAdd(aRet,{217,"U"})
AAdd(aRet,{218,"U"})
AAdd(aRet,{219,"U"})
AAdd(aRet,{220,"U"})
AAdd(aRet,{221,"Y"})
AAdd(aRet,{222,"_"})
AAdd(aRet,{223,"_"})
AAdd(aRet,{224,"a"})
AAdd(aRet,{225,"a"})
AAdd(aRet,{226,"a"})
AAdd(aRet,{227,"a"})
AAdd(aRet,{228,"a"})
AAdd(aRet,{229,"a"})
AAdd(aRet,{230,"_"})
AAdd(aRet,{231,"c"})
AAdd(aRet,{232,"e"})
AAdd(aRet,{233,"e"})
AAdd(aRet,{234,"e"})
AAdd(aRet,{235,"e"})
AAdd(aRet,{236,"i"})
AAdd(aRet,{237,"i"})
AAdd(aRet,{238,"i"})
AAdd(aRet,{239,"i"})
AAdd(aRet,{240,"o"})
AAdd(aRet,{241,"n"})
AAdd(aRet,{242,"o"})
AAdd(aRet,{243,"o"})
AAdd(aRet,{244,"o"})
AAdd(aRet,{245,"o"})
AAdd(aRet,{246,"o"})
AAdd(aRet,{247,"_"})
AAdd(aRet,{248,"_"})
AAdd(aRet,{249,"u"})
AAdd(aRet,{250,"u"})
AAdd(aRet,{251,"u"})
AAdd(aRet,{252,"u"})
AAdd(aRet,{253,"y"})
AAdd(aRet,{254,"_"})
AAdd(aRet,{255,"y"})

For nPos:=1 to Len(cVar)
	cPos:=Subs(cVar,nPos,1)
	nAsc:=Asc(cPos)
	Do Case
	Case nAsc>=nSimD .and. nAsc<=nSimA
		cRet+=cPos
	Case nAsc>=nTrataD .and. nAsc<=nTrataA
		nRet:=Ascan(aRet,{|x| x[1]==nAsc})
		If nRet>0
			cRet+=aRet[nRet,2]
		EndIf
	Otherwise
		cRet+="_"
	EndCase
Next

Return(cRet)
//-----------------------------------------------------------------------------

//-----------------------------------------------------------------------------
// dbffile - Início
//-----------------------------------------------------------------------------
//xx//#include "zLibDec2Hex.ch"
//xx//#include "zLibDateTime.ch"

/* ===========================================================================

Classe		ZXBFXILE
Autor		Julio Wittwer
Data		04/01/2019

Descrição   Classe de acesso a arquivos DBF - SOMENTE LEITURA 
            Suporte DBF DBASE III / Clipper / ADS 
			Suporte a leitura de campos MEMO dos formatos DBT e FPT

Observações Embora todas as propriedades sejam publicas, o uso da classe 
            deve ser feito totalmente pelos MÉTODOS implementados
            Os métodos de uso interno e/ou restritos da classe são 
            iniciados com "_"

			O objeto gerado exige apenas o nome do arquivo no construtor. 
			O arquivo é aberto em modo de leitura compartilhado. 
			Pode ser usado inclusive para ler arquivos no SmartClient 
			( Porém o desempenho pode ser prejudicado devido ao tráfego 
			de rede entre APPServer e SmartClient a cada leitura de registro


Release 20190105
- Implementação de filtro -- DbSetFilter e DBCleanFilter()

Release 20190106 
- Implementação de indice em memória 
- Implementação de filtro de registros deletados

Debitos Tecnicos

1) Melhorar a inserção. Atualmente é inserido um registro em branco 
direto no final da tabela, e então ele é alterado. Verificar forma 
de postergar a inserção, para escrever os novos dados de uma vez 

Referências do Formato de Arquivos DBF / DBT / FPT 

http://web.tiscali.it/SilvioPitti/
https://www.dbase.com/Knowledgebase/INT/db7_file_fmt.htm
http://dbfviewer.com/dbf-file-structure/
https://www.loc.gov/preservation/digital/formats/fdd/fdd000325.shtml
https://en.wikipedia.org/wiki/.dbf
http://www.dbfree.org/webdocs/1-documentation/b-dbf_header_specifications.htm
http://www.independent-software.com/dbase-dbf-dbt-file-format.html
http://www.idea2ic.com/File_Formats/DBF%20FILE%20STRUCTURE.pdf
http://www.oocities.org/geoff_wass/dBASE/GaryWhite/dBASE/FAQ/qformt.htm
http://www.dbfree.org/webdocs/1-documentation/a-about_indexes.htm


=========================================================================== */

// Pseudo-comando para trabalhar com OFFSET de Base 0 em string AdvPL 
// Recebe STring, Offset Base 0 e Tamanho do bloco 
#xtranslate DBF_OFFSET(<cBuffer>,<nOffset>,<nSize>) => Substr(<cBuffer>,<nOffset>+1,<nSize>)

CLASS ZXBFXILE FROM ZXSAMXILE

  DATA cDataFile			// Nome do arquivo de dados
  DATA cMemoFile			// Nome do arquivo memo (DBT/FPT) 

  DATA cDBFType				// Identificador hexadecimal do tipo do DBF 
  DATA dLastUpd				// Data registrada dentro do arquivo como ultimo UPDATE 
  DATA nRecLength			// Tamanho de cada registro 
  DATA nDataPos 			// Offset de inicio dos dados 
  DATA lHasMemo				// Tabela possui campo MEMO ?
  DATA nMemoType            // Tipo de campo MEMO da RDD ( 1 = DBT, 2 = FPT ) 
  DATA cMemoExt             // Identificador (extensao) do tipo do campo MEMO
  DATA lExclusive           // Arquivo aberto em modo exclusivo ?
  DATA lUpdPend             // Flag indicando update pendente 
	DATA lNewRecord           // Flag indicando a inserção de um registro
  DATA lDeleted				// Indicador de registro corrente deletado (marcado para deleção ) 
  DATA lSetDeleted          // Filtro de registros deletados ativo 

  DATA nHData				// Handler do arquivo de dados
  DATA oMemoFile			// Objeto para lidar com campo Memo 
  //xx//DATA oLogger              // Objeto de log 
    	
  // ========================= Metodos de uso publico da classe

  METHOD NEW()    			// Construtor 
  METHOD OPEN()				// Abertura da tabela 
  METHOD CLOSE()			// Fecha a tabela 
  METHOD EXISTS()           // Verifica se a tabela existe 
  METHOD CREATE()           // Cria a tabela no disco 
  METHOD DROP()             // Apaga a tabela do disco 

  METHOD GetFileType()      // Tipo do arquivo ("DBF")

  METHOD GetDBType()		// REtorna identificador hexadecimal do tipo da tabela 
  METHOD GetDBTypeStr() 	// Retorna string identificando o tipo da tabela 
  METHOD GetMemoType()      // Tipo do MEMO usado, 1 = DBT , 2 = FPT

  METHOD FieldGet( nPos )   // Recupera o conteudo da coluna informada do registro atual 
  METHOD FieldPut( nPos )   // Faz update em uma coluna do registro atual 
  METHOD FileName()         // Retorna nome do arquivo aberto 
  METHOD Recno()			// Retorna o numero do registro (RECNO) posicionado 
  METHOD Deleted()			// REtorna .T. caso o registro atual esteja DELETADO ( Marcado para deleção ) 
  METHOD SetDeleted()       // Liga ou desliga filtro de registros deletados
  
  METHOD Insert()           // Insere um registro em branco no final da tabela
  METHOD Update()           // Atualiza o registro atual na tabela 

  METHOD Header() 			// Retorna tamanho em Bytes do Header da Tabela
  METHOD FileSize()         // Retorna o tamanho ocupado pelo arquivo em bytes 
  METHOD RecSize()			// Retorna o tamanho de um registro da tabela 
  METHOD LUpdate()			// Retorna a data interna do arquivo que registra o ultimo update 
 
  // ========================= Metodos de uso interno da classe

  METHOD _InitVars() 		// Inicializa propriedades do Objeto, no construtor e no CLOSE
  METHOD _ReadHeader()		// Lê o Header do arquivo  de dados
  METHOD _ReadStruct()		// Lê a estrutura do arquivo de dados 
  METHOD _SetLUpdate()      // Atualiza data do Last Update no Header do Arquivo 
  METHOD _ReadRecord()		// Le um registro do arquivo de dados
  METHOD _ClearRecord()		// Limpa o registro da memoria (EOF por exemplo) 
  METHOD _ReadMemo()        // Recupera um conteudo de campo memo por OFFSET

ENDCLASS

// ----------------------------------------------------------
// Retorna o tipo do arquivo 

METHOD GetFileType() CLASS ZXBFXILE 
Return "DBF"

// ----------------------------------------------------------
// Construtor do objeto DBF 
// Apenas recebe o nome do arquivo e inicializa as propriedades
// Inicializa o ZXSAMXILE passando a instancia atual 

METHOD NEW(cFile,oFileDef) CLASS ZXBFXILE 
_Super:New(self)

//xx//::oLogger := ZLOGGER():New("ZXBFXILE")
//xx//::oLogger:Write("NEW","File: "+cFile)

::_InitVars() 
::cDataFile   := lower(cFile)

If oFileDef != NIL 
	// Passa a definição pro IsamFile 
	::SetFileDef(oFileDef)
Endif

Return self


// ----------------------------------------------------------
// Abertura da tabela -- READ ONLE 
// Caso retorne .F. , consulte o ultimo erro usando GetErrorStr() / GetErrorCode()
// Por hora apenas a abertura possui tratamento de erro 

METHOD OPEN(lExclusive,lCanWrite) CLASS ZXBFXILE 
Local nFMode := 0

::_ResetError()

If ::lOpened
	::_SetError(-1,"File Already Open")
	Return .F.
Endif

IF !::Exists()
	::_SetError(-6,"Unable to OPEN - File ["+::cDataFile+"] DOES NOT EXIST")
	Return .F.
Endif

If lExclusive = NIL ; 	lExclusive := .F. ; Endif
If lCanWrite = NIL ; 	lCanWrite := .F.  ; Endif

If lExclusive
	nFMode += FO_EXCLUSIVE
Else
	nFMode += FO_SHARED
Endif

If lCanWrite
	nFMode += FO_READWRITE
Else
	nFMode += FO_READ
Endif

// Por enquanto faz escrita apenas em modo exclusivo
If lCanWrite .AND. !lExclusive
	::_SetError(-6,"Unable to OPEN for WRITE in SHARED MODE -- Use Exclusive mode or OPEN FOR READ")
	Return .F.
Endif

// Atualiza propriedades de controle da classe
::lExclusive   := lExclusive
::lCanWrite    := lCanWrite

// Abre o arquivo de dados
::nHData := Fopen(::cDataFile,nFMode)

If ::nHData == -1
	::_SetError(-2,"Open Error - File ["+::cDataFile+"] Mode ["+cValToChar(nFMode)+"] - FERROR "+cValToChar(Ferror()))
	Return .F.
Endif

// Lê o Header do arquivo 
If !::_ReadHEader()
	FClose(::nHData)
	::nHData := -1
	Return .F. 
Endif

If ::lHasMemo

	// Se o header informa que a tabela possui campo MEMO 
	// Determina o nome do arquivo MEMO 

	::cMemoFile := substr(::cDataFile,1,rat(".",::cDataFile)-1)
	::cMemoFile += ::cMemoExt
	
	If !file(::cMemoFile)
		::_SetError(-3,"Memo file ["+::cMemoFile+"] NOT FOUND.")
		::Close()
		Return .F. 
	Endif

	If ::nMemoType == 1
		::oMemoFile  := ZDBTFILE():New(self,::cMemoFile)
	ElseIF ::nMemoType == 2
		::oMemoFile  := ZFPTFILE():New(self,::cMemoFile)
	Endif

	If !::oMemoFile:Open(::lExclusive,::lCanWrite)
		::_SetError(-4,"Open Error - File ["+::cMemoFile+"] - FERROR "+cValToChar(Ferror()))
		::Close()
		Return .F. 
	Endif
	
Endif

If !::_ReadStruct()

	// Em caso de falha na leitura da estrutura 

	FClose(::nHData)
	::nHData := -1
    
	IF ::oMemoFile != NIL 
		::oMemoFile:Close()
		FreeObj(::oMemoFile)
	Endif

	Return .F.
	
Endif

// Cria o array de campos do registro atual 
::aGetRecord := Array(::nFldCount)
::aPutRecord := Array(::nFldCount)

// Seta que o arquivo está aberto 
::lOpened := .T. 

// Vai para o topo do arquivo 
// e Lê o primeiro registro físico 
::GoTop()

Return .T. 


// ----------------------------------------------------------
// Fecha a tabela aberta 
// Limpa as variaveis de controle. 
// A tabela pode ser aberta novamente pela mesma instancia 

METHOD CLOSE() CLASS ZXBFXILE 

// Fecha o arquivo aberto 
If ::nHData <> -1
	fClose(::nHData)
Endif

// Se tem memo, fecha 
IF ::oMemoFile != NIL 
	::oMemoFile:Close()
	FreeObj(::oMemoFile)
Endif

// Fecha todos os indices abertos 
::ClearIndex()

// Limpa as propriedades
::_InitVars()

Return 


// ----------------------------------------------------------\
// Verifica se a tabela existe no disco 
METHOD EXISTS() CLASS ZXBFXILE 
IF File(::cDataFile)
	Return .T. 
Endif
Return .F. 

// ----------------------------------------------------------\
// Cria a tabela no disco 
// O nome já foi recebido no construtor 
// Recebe a estrutura e a partir dela cria a tabela 
// Se o objeto já está atrelado a uma definição, usa a estrutura da definição 

METHOD CREATE( aStru ) CLASS ZXBFXILE 
Local lHasMemo := .F.
Local nFields := 0
Local nRecSize := 1 
Local nI, nH
Local cNewHeader := ''
Local lOk, cMemoFile, oMemoFile
Local cFldName

If ::EXISTS()
	::_SetError(-7,"CREATE ERROR - File Already Exists")
Endif

If ::lOpened
	::_SetError(-8,"CREATE ERROR - File Already Opened")
Endif

If aStru = NIL .AND. ::oFileDef != NIL 
	// Se a erstrutura nao foi informada 
	// Mas a tabela tem a definição , 
	// pega a estrutura da definicao 
	aStru := ::oFileDef:GetStruct()
Endif

nFields := len(aStru)

For nI := 1 to nFields
	If aStru[nI][2] == 'M'
		lHasMemo := .T. 
	Endif
	If !aStru[nI][2]$"CNDLM"
		UserException("CREATE ERROR - INVALID FIELD TYPE "+aStru[nI][2]+ " ("+aStru[nI][1]+")" )
	Endif
	// Ajusta nome do campo 
	aStru[nI][1] := Upper(padr(aStru[nI][1],10))
	nRecSize += aStru[nI][3]
Next

// Inicio do Header
// 1o Byte - Formato do aRquivo 
// Campo memo será criado como FPT 
If lHasMemo
	::nMemoType := 2
	cNewHeader += Chr(245) // FoxPro 2.x (or earlier) with memo ( FPT ) 
Else
	cNewHeader += chr(003) // FoxBASE+/Dbase III plus, no memo
Endif

// 3 Byte(2) = Last Update Date = TODAY
cNewHeader +=  chr( Year(date())-2000 ) + ;
	           chr( Month(date()) ) + ;
	           Chr( Day(date()) ) 

// 4 byte(S) - Last Record
cNewHeader +=  L2BIN(0) 

// 2 byte(s) -- Begin Data Offset
cNewHeader +=  I2Bin( ( (nFields+1) * 32) + 2 ) 

// 2 byte(s) -- Record Size 
cNewHeader +=  I2Bin(nRecSize) 

// Filler ( 32 Bytes  )
cNewHeader +=  replicate( chr(0) , 4 )
cNewHeader +=  replicate( chr(0) , 16 )

// Acrescenta no Header a estrutura
For nI := 1 to nFields

	cFldName := alltrim(aStru[nI][1])
	while len(cFldName) < 10
		cFldName += chr(0)
	Enddo

	cNewHeader +=  cFldName + chr(0) // Nome
	cNewHeader +=  aStru[nI][2]  // Tipo 
	cNewHeader +=  replicate( chr(0) , 4 ) // Filler - Reserved
	cNewHeader +=  chr(aStru[nI][3]) // Size
	cNewHeader +=  chr(aStru[nI][4]) // Decimal
	cNewHeader +=  replicate( chr(0) , 14 ) // Filler - Reserved

Next

// Final do Header apos estrutura 

cNewHeader +=  chr(13)  // 0x0D = Fim da estrutura 
cNewHeader +=  chr(0)   // 0c00 = Filler
cNewHeader +=  chr(26)  // 0x1A = End Of File

// Cria a tabela no disco 
nH := fCreate(::cDataFile)

If nH == -1
	::_SetError(-9,"CREATE ERROR - Data File ["+::cDataFile+"] - FERROR ("+cValToChar(Ferror())+")")
	Return .F. 
Endif

fWrite(nH,cNewHeader)
fCLose(nH)

If lHasMemo
	cMemoFile := substr(::cDataFile,1,rat(".",::cDataFile)-1)
	cMemoFile += '.fpt'
	oMemoFile := ZFPTFILE():New(self,cMemoFile)
	lOk := oMemoFile:Create()
	FreeObj(oMemoFile)
	If !lOk
		::_SetError(-9,"CREATE ERROR - Data File ["+::cDataFile+"] - FERROR ("+cValToChar(Ferror())+")")
		Return .F. 
	Endif
Endif

Return .T. 


// ----------------------------------------------------------\
// Apaga a tabela do disco 

METHOD DROP() CLASS ZXBFXILE 
nErr := 0

If ::lOpened
	::_SetError(-8,"DROP ERROR - File Already Opened")
	Return .F.
Endif

If !empty(cDataFile)
	Ferase(cDataFile)
Endif

If !empty(cMemoFile)
	Ferase(cMemoFile)
Endif

Return .T. 

// ----------------------------------------------------------
// Permite ligar filtro de navegação de registros deletados
// Defaul = desligado

METHOD SetDeleted( lSet ) CLASS ZXBFXILE 
Local lOldSet := ::lSetDeleted
If pCount() > 0 
	::lSetDeleted := lSet
Endif
Return lOldSet


// ----------------------------------------------------------
// *** METODO DE USO INTERNO ***
// Inicializa / Limpa as propriedades padrao do Objeto 

METHOD _InitVars() CLASS ZXBFXILE 

// Inicialização das propriedades da classe pai
_Super:_InitVars()

::nHData      := -1
::lOpened     := .F. 
::nDataPos    := 0 
::lHasMemo    := .F. 
::lExclusive  := .F. 
::lCanWrite   := .T. 
::dLastUpd    := ctod("")
::aPutRecord  := {}
::lUpdPend    := .F. 
::lNewRecord  := .F.
::lDeleted    := .F. 
::lSetDeleted := .F. 
::nRecno      := 0
::cMemoExt    := ''
::nMemoType   := 0 


Return

// ----------------------------------------------------------
// Retorna o identificador hexadecimal do tipo do DBF

METHOD GetDBType() CLASS ZXBFXILE 
Return ::cDBFType

// ----------------------------------------------------------
// Tipo do MEMO usado, 1 = DBT , 2 = FPT

METHOD GetMemoType()  CLASS ZXBFXILE 
Return ::nMemoType

// ======================================================================================================
// Array com os tipos de DBF reconhecidos 
// O 3o elemento quando .T. indoca se o formato é suportado 

STATIC _aDbTypes := { { '0x02','FoxBASE'                                              , .F. } , ;
                      { '0x03','FoxBASE+/Dbase III plus, no memo'                     , .T. } , ;  // ####  (No Memo)
                      { '0x04','dBASE IV or IV w/o memo file'                         , .F. } , ;
                      { '0x05','dBASE V w/o memo file'                                , .F. } , ;
                      { '0x30','Visual FoxPro'                                        , .F. } , ;
                      { '0x31','Visual FoxPro, autoincrement enabled'                 , .F. } , ;
                      { '0x32','Visual FoxPro, Varchar, Varbinary, or Blob-enabled'   , .F. } , ;
                      { '0x43','dBASE IV SQL table files, no memo'                    , .F. } , ;
                      { '0x63','dBASE IV SQL system files, no memo'                   , .F. } , ;
                      { '0x7B','dBASE IV with memo'                                   , .F. } , ;
                      { '0x83','FoxBASE+/dBASE III PLUS, with memo'                   , .T. } , ;  // ####  DBT
                      { '0x8B','dBASE IV with memo'                                   , .F. } , ;
                      { '0x8E','dBASE IV w. SQL table'                                , .F. } , ;
                      { '0xCB','dBASE IV SQL table files, with memo'                  , .F. } , ;
                      { '0xF5','FoxPro 2.x (or earlier) with memo'                    , .T. } , ;  // ####  FPT
                      { '0xE5','HiPer-Six format with SMT memo file'                  , .F. } , ;
                      { '0xFB','FoxBASE'                                              , .F. } } 

// ======================================================================================================


// ----------------------------------------------------------
// Retorna a descrição do tipo de arquivo DBF 

METHOD GetDBTypeStr() CLASS ZXBFXILE
Local cRet := '(Unknow DBF Type)'
Local nPos := ascan(_aDbTypes,{|x| x[1] == ::cDBFType })
If nPos > 0
	cRet := _aDbTypes[nPos][2]
Endif
Return cREt

// ----------------------------------------------------------
// Retorna a data do ultimo update feito no arquivo 

METHOD LUPDATE() CLASS ZXBFXILE 
Return ::dLastUpd

// ----------------------------------------------------------
// *** METODO DE USO INTERNO ***
// Realiza a leitura do Header do arquivo DBF 

METHOD _ReadHeader() CLASS ZXBFXILE 
Local cBuffer := space(32)
Local nYear, nMonth, nDay
Local cTemp := ''
Local nTemp := 0

If ::nHData == -1 
	UserException("_ReadHeader() ERROR - DBF File Not Opened")
Endif

// Reposicionao o arquivo no Offset 0
// Le os primeiros 32 bytes do Header
FSeek(::nHData,0)
FRead(::nHData,@cBuffer,32)

// ----------------------------------------
// Database File Type

cTemp := DBF_OFFSET(cBuffer,0,1)       
nTemp := ASC(cTemp)

::cDBFType := '0x'+padl( upper(DEC2HEX(nTemp)) , 2 , '0')
                                 
If ::cDBFType == '0x83'   
	// FoxBASE+/dBASE III PLUS, with memo
	::lHasMemo := .T. 
	::cMemoExt := ".dbt"
	::nMemoType := 1
ElseIf ::cDBFType == '0xF5'
	// FoxPro 2.x (or earlier) with memo
	::lHasMemo := .T. 
	::cMemoExt := ".fpt"
	::nMemoType := 2
Endif

If Ascan(_aDbTypes,{|x| x[1] == ::cDBFType }) == 0 
	::_SetError(-5,"DBF FORMAT ("+::cDBFType+") NOT RECOGNIZED")
	Return .F. 
Endif

// ----------------------------------------
// Last Update ( YMD => 3 Bytes, binary )

cTemp := DBF_OFFSET(cBuffer,1,3) 

nYear  := ASC( substr(cTemp,1,1))
nMonth := ASC( substr(cTemp,2,1))
nDay   := ASC( substr(cTemp,3,1))

If nYear < 50 
	nYear += 2000
Else
	nYear += 1900
Endif

::dLastUpd := ctod(strzero(nDay,2)+"/"+strzero(nMonth,2)+"/"+strzero(nYear,4))

// ----------------------------------------
// 4 bytes (32 bits), Record Count (  LastRec ) 

cTemp := DBF_OFFSET(cBuffer,4,4) 
::nLastRec := Bin2L(cTemp)

// ----------------------------------------
// First Data Record Position  ( Offset ) 

cTemp := DBF_OFFSET(cBuffer,8,2) 
::nDataPos := Bin2I(cTemp)

// ----------------------------------------
// Length of one data record, including delete flag

cTemp := DBF_OFFSET(cBuffer,10,2) 
::nRecLength := Bin2I(cTemp)

// Limpeza de variáveis 
cTemp := NIL
cBuffer := NIL

Return .T. 


/*
FIELD DESCRIPTOR ARRAY TABLE
BYTES DESCRIPTION
0-10 Field Name ASCII padded with 0x00
11 Field Type Identifier (see table)
12-15 Displacement of field in record
16 Field length in bytes
17 Field decimal places
18-19 Reserved
20 dBaseIV work area ID
21-30 Reserved
31 Field is part of production index - 0x01 else 0x00
*/

// ----------------------------------------------------------
// *** METODO DE USO INTERNO ***
// Lê a estrutura de campos da tabela 

METHOD _ReadStruct() CLASS ZXBFXILE 
Local cFldBuff := space(32)
Local cFldName, cFldType  , nFldLen , nFldDec 

If ::nHData == -1 
	UserException("_ReadStruct() ERROR - DBF File Not Opened")
Endif

// Reposicionao o arquivo no Offset 32
FSeek(::nHData,32)

While .T.

	FRead(::nHData,@cFldBuff,32)
	
	If substr(cFldBuff,1,1) == chr(13) 
		// 0x0D => Indica final da estrutura
		EXIT
	Endif
	
	cFldName := DBF_OFFSET(cFldBuff,0,11)
	cFldName := left( cFldName,AT(chr(0),cFldName )-1 )
	cFldName := padr(cFldName,10)
	
	cFldType := DBF_OFFSET(cFldBuff,11,1)

	nFldLen  := ASC(DBF_OFFSET(cFldBuff,16,1))
	nFldDec  := ASC(DBF_OFFSET(cFldBuff,17,1))
	
	aadd(::aStruct , { cFldName , cFldType , nFldLen , nFldDec } )

Enddo

::nFldCount := len(::aStruct)

Return .T. 

// ----------------------------------------------------------
// Recupera o conteudo de um campo da tabela 
// a partir da posição do campo na estrutura

METHOD FieldGet(nPos) CLASS ZXBFXILE 
     
If valtype(nPos) = 'C'
	nPos := ::FieldPos(nPos)
Endif

If nPos > 0 .and. nPos <= ::nFldCount 

	IF ::aStruct[nPos][2] == 'M'
		// Campo MEMO, faz a leitura baseado 
		// no Bloco gravado na tabela 
		Return ::_ReadMemo( ::aGetRecord[nPos] )
	Else
		Return ::aGetRecord[nPos]
	Endif
	
Endif

Return NIL


// ----------------------------------------------------------
// Atualiza um valor na coluna informada do registro atual 
// Por hora nao critica nada, apenas coloca o valor no array 

METHOD FieldPut(nPos,xValue) CLASS ZXBFXILE 

If valtype(nPos) = 'C'
	nPos := ::FieldPos(nPos)
Endif

If ( !::lCanWrite )
	UserException("Invalid FieldPut() -- File NOT OPEN for WRITING")
Endif

If ( ::lEOF )
	UserException("Invalid FieldPut() -- File is in EOF")
Endif

If nPos > 0 .and. nPos <= ::nFldCount 
	If ::aStruct[nPos][2] == 'C'
		// Ajusta tamanho de string com espaços a direita
		xValue := PadR(xValue,::aStruct[nPos][3])
	Endif
	::aPutRecord[nPos] := xValue
	::lUpdPend := .T. 
Endif

Return NIL

// ----------------------------------------------------------
// Recupera o nome do arquivo no disco 
METHOD FileName() CLASS ZXBFXILE 
Return ::cDataFile

// ----------------------------------------
// Retorna .T. caso o registro atual esteja deletado 
METHOD DELETED() CLASS ZXBFXILE 
Return ::lDeleted

// ----------------------------------------
// Retorna o tamanho do HEader
// -- O tamanho do Header é justamente a posicao do offser de dados 
// da tabela, após o final do Header. 

METHOD HEADER() CLASS ZXBFXILE 
Return ::nDataPos

// ----------------------------------------
// Retorna o tamanho ocupado pelo arquivo em bytes 
METHOD FileSize() CLASS ZXBFXILE 
Local nFileSize := 0
If ::lOpened
	nFileSize := fSeek(::nHData,0,2)
Endif
Return nFileSize

// ----------------------------------------
// Retorna o tamanho de um registro da tabela no arquivo 
// Cada campo MEMO ocupa 10 bytes 

METHOD RECSIZE() CLASS ZXBFXILE 
Return ::nRecLength

// ----------------------------------------
// Retorna o numero do registro atualmente posicionado

METHOD RECNO() CLASS ZXBFXILE 
If ::lEOF
	Return ::nLastRec+1
Endif
Return ::nRecno 

// ----------------------------------------
// *** METODO DE USO INTERNO ***
// Lê o registro posicionado no offset de dados atual 

METHOD _ReadRecord() CLASS ZXBFXILE 
Local cTipo , nTam , cValue
Local nBuffPos := 2 , nI
Local cRecord := '' , nOffset

// ----------------------------------------
// Calcula o offset do registro atual baseado no RECNO

nOffset := ::nDataPos 
nOffset += (::nRecno * ::nRecLength)
nOffset -= ::nRecLength

// Posiciona o arquivo de dados no offset do registro 
FSeek(::nHData , nOffset )

// Lê o registro do offset atual 
FRead(::nHData , @cRecord , ::nRecLength )

// Primeiro byte = Flag de deletato
// Pode ser " " (espaço)    registro ativo 
//          "*" (asterisco) registro deletado 
   
::lDeleted := ( left(cRecord,1) = '*' )

// Agora lê os demais campos e coloca no ::aGetRecord

For nI := 1 to ::nFldCount

	cTipo := ::aStruct[nI][2]
	nTam  := ::aStruct[nI][3]
	cValue := substr(cRecord,nBuffPos,nTam)

	If cTipo == 'C'
		::aGetRecord[nI] := cValue
		nBuffPos += nTam
	ElseIf cTipo == 'N'
		::aGetRecord[nI] := val(cValue)
		nBuffPos += nTam
	ElseIf cTipo == 'D'
		::aGetRecord[nI] := STOD(cValue)
		nBuffPos += nTam
	ElseIf cTipo == 'L'
		::aGetRecord[nI] := ( cValue=='T' )
		nBuffPos += nTam
	ElseIf cTipo == 'M'
		// Recupera o Offset do campo no DBT/FPT
		// aGetRecord sempre vai conter o OFFSET
		::aGetRecord[nI] := val(cValue)
		nBuffPos += nTam
	Endif
  
Next

// Reseta flags de BOF e EOF 
::lBOF := .F. 
::lEOF := .F. 

Return .T. 


// ----------------------------------------
// Insere um registro em branco no final da tabela
// Apos a inserção, voce pode fazer fieldput 
// e confirmar tudo com UPDATE 
METHOD Insert() CLASS ZXBFXILE

If ::lUpdPend
	// Antes de mais nada, se tem um update pendente
	// Faz primeiro o update 
	::Update()
Endif

// Limpa o conteudo do registro 
::_ClearRecord()

// Nao estou em BOF ou EOF, 
// Estou em modo de inserção de registro
::lBOF := .F. 
::lEOF := .F. 
            
// Incrementa uma unidade no contador de registros
::nLastRec++

// Recno atual = registro novo 
::nRecno := ::nLastRec

// Cria uma pendencia de update 
// O update vai fazer a inserção no final do arquivo 
::lNewRecord := .T.

// Faz o update inserir o registro em branco 
IF ::Update()
               
	// Escreve o novo final de arquivo 
	FSeek(::nHData,0,2)
	fWrite(::nHData , chr(26) ) // !a = End Of File 

	// Atualiza o numero do ultimo registro no Header
	FSeek(::nHData,4)
	fWrite(::nHData , L2Bin(::nLastRec) )
	
	Return .T. 

Endif

Return .F. 

// ----------------------------------------
// Grava as alterações do registro atual na tabela 

METHOD Update() CLASS ZXBFXILE
Local cTipo , nTam , xValue
Local nI
Local cSaveRec := '' , nOffset
Local nMemoBlock, nNewBlock

If ( ::lEOF )
	UserException("ZXBFXILE::Update() ERROR -- File is in EOF")
	Return
Endif

If !::lUpdPend .and. !::lNewRecord
	// Nao tem insert e nao tem update pendente, nao faz nada
	Return
Endif

// ----------------------------------------
// Calcula o offset do registro atual baseado no RECNO

nOffset := ::nDataPos 
nOffset += (::nRecno * ::nRecLength)
nOffset -= ::nRecLength

// Primeiro byte do registro
// Flag de deletado 
cSaveRec := IIF(::lDeleted ,'*',' ') 

// Agora concatena os demais campos 
// Se nao houve alteração, monta o buffer com o valor lido

For nI := 1 to ::nFldCount

	cTipo := ::aStruct[nI][2]
	nTam  := ::aStruct[nI][3]
	nDec  := ::aStruct[nI][4]

	If cTipo == 'C'

		If ::aPutRecord[nI] != NIL 
			xValue := PADR( ::aPutRecord[nI] ,nTam)
			cSaveRec += xValue
			::aPutRecord[nI] := NIL
			::aGetRecord[nI] := xValue
		Else
			cSaveRec += ::aGetRecord[nI]
		Endif	

	ElseIf cTipo == 'N'

		If ::aPutRecord[nI] != NIL 
			xValue := ::aPutRecord[nI]
			cSaveRec += STR( xValue , nTam, nDec)
			::aPutRecord[nI] := NIL
			::aGetRecord[nI] := xValue
		Else
			cSaveRec += STR( ::aGetRecord[nI], nTam, nDec)
		Endif

	ElseIf cTipo == 'D'

		If ::aPutRecord[nI] != NIL 
			xValue := ::aPutRecord[nI]
			cSaveRec += DTOS( xValue )
			::aPutRecord[nI] := NIL
			::aGetRecord[nI] := xValue
		Else
			cSaveRec += DTOS( ::aGetRecord[nI] )
		Endif

	ElseIf cTipo == 'L'

		If ::aPutRecord[nI] != NIL 
			xValue := ::aPutRecord[nI]
			cSaveRec += IIF( xValue , 'T' , 'F')
			::aPutRecord[nI] := NIL
			::aGetRecord[nI] := xValue
		Else
			cSaveRec += IIF( ::aGetRecord[nI] , 'T' , 'F')
		Endif


	ElseIf cTipo == 'M'

		// Update de campo memo
		// Se realmente foi feito uma troca de valor, vamos ver o que fazer 
		// O bloco usado ( caso tivesse um ) está no ::aGetRecord[nI]

		If ::aPutRecord[nI] != NIL 

			// Pega o valor a atualizar no campo memo 
			xValue := ::aPutRecord[nI]
			
			// Verifica o numero do bloco usado 
			// 0 = sem bloco , sem conteudo 
			nMemoBlock := ::aGetRecord[nI]
			
			// Faz update deste memo. Se nao usava bloco, pode passar
			// a usar. Se já usava, se o memo nao for maior do que o já existente
			// ou nao atingir o limite do block, pode usar o mesmo espaço
			nNewBlock := ::oMemoFile:WRITEMEMO( nMemoBlock , xValue ) 
			
			If nNewBlock <> nMemoBlock
				// Trocou de bloco 
				cSaveRec += str( nNewBlock , 10 )
				// Atualiza a variavel de memoria 
				::aGetRecord[nI] := nNewBlock
			Else
				// Manteve o bloco 
				cSaveRec += str( nMemoBlock , 10 )
			Endif
		
		Else

			// Memo nao foi atualizado. 
			// Mantem valor atual 
			cSaveRec += STR( ::aGetRecord[nI] , 10 )

		Endif
		
	Endif

Next

IF len(cSaveRec) > ::nRecLength
	// Jamais, nunca. 
	// Se meu buffer em memoria passou o tamanho do registro 
	// do arquivo, algo deu muito errado ... 
	UserException("ZXBFXILE::Update() ERROR - FIELD BUFFER OVERFLOW")
Endif

// Posiciona o arquivo de dados no offset do registro 
FSeek(::nHData , nOffset )

// Agora grava o buffer do registro inteiro 
fWrite(::nHData , cSaveRec , ::nRecLength )

// Desliga flag de update pendente 
::lUpdPend := .F. 

// Atualiza o header do DBF com a data do ultimo update 
// caso necessario \

If Date() > ::dLastUpd 
	// Atualiza a data em memoria 
	::dLastUpd  := Date()
	// Regrava a nova data no header 
	::_SetLUpdate()
Endif

// Agora que o registro está atualizado, atualiza os indices 
if (::lNewRecord)
	// Inserção de registro, desliga o flag de inserção 
	::lNewRecord := .F. 
	// Insere a nova chave em todos os indices abertos
	aEval(::aIndexes , {|oIndex| oIndex:InsertKey() })
Else
	// Atualiza a chave de todos os indices abertos
	aEval(::aIndexes , {|oIndex| oIndex:UpdateKey() })
Endif

Return .T. 

// ----------------------------------------------------------
// *** METODO DE USO INTERNO ***
// Atualiza o header da tabela com a data atualizada
// do ultimo update realizado na tabela 
// Metodo chamado apenas quando a data do header 
// da tabela estiver desatualizada 

METHOD _SetLUpdate() CLASS ZXBFXILE
Local cBuffer

// Vai para o offset 1 -- 3 bytes com a data do ultimo update 
FSeek(::nHData,1)

// Monta a nova data em 3 bytes 
cBuffer := chr( Year(::dLastUpd)-2000 ) + ;
           chr( Month(::dLastUpd) ) + ;
           Chr( Day(::dLastUpd) ) 

// Grava a nova data no header 
fWrite(::nHData , cBuffer)

Return

// ----------------------------------------------------------
// *** METODO DE USO INTERNO ***
// Limpa os campos do registro atual 
// ( Inicializa todos com os valores DEFAULT ) 

METHOD _ClearRecord()  CLASS ZXBFXILE

// Inicializa com o valor default os campos da estrutura 
_Super:_ClearRecord()

// Limpa flag de registro deletado 
::lDeleted := .F. 

Return

// ----------------------------------------
// *** METODO DE USO INTERNO ***
// Lë um campo MEMO de um arquivo DBT 
// baseado no numero do bloco rececido como parametro 

METHOD _ReadMemo(nBlock) CLASS ZXBFXILE
Local cMemo := '' 

If nBlock > 0

	// Le o conteudo do campo MEMO 
	cMemo := ::oMemoFile:ReadMemo(nBlock)

Endif

Return cMemo
//-----------------------------------------------------------------------------
// dbffile - Fim
//-----------------------------------------------------------------------------

//-----------------------------------------------------------------------------
// isamfile - Início
//-----------------------------------------------------------------------------
//#include "zLibDateTime.ch"
//#ifndef zLibDateTime_CH
//
  //#define zLibDateTime_CH
//
  //#xtranslate STOD( <cAnsiDate> ) => STATICCALL( ZLIBDATETIME , STOD , <cAnsiDate> ) 
  //#xtranslate DATE2DJ( <dDate> ) => STATICCALL( ZLIBDATETIME , DATE2DJ , <dDate> ) 
  //#xtranslate DJ2DATE( <nDJ> ) => STATICCALL( ZLIBDATETIME , DJ2DATE , <nDJ> ) 
//
//#endif

//#include "zLibZCompare.ch"
//#ifndef zLibZCompare_CH
//
  //#define zLibZCompare_CH
//
  //#xtranslate zCompare( <xValue1> , <xValue2> )   =>  STATICCALL( ZLIBZCOMPARE , ZCOMPARE , <xValue1> , <xValue2> ) 
//
//#endif

/* ======================================================================================
Classe       ZXSAMXILE
Autor        Julio Wittwer
Data         01/2019
Descrição    A Classe ZXSAMXILE serve de base para implementação de tabeas ISAM 
             através de herançã. Atualmente é herdada pelas classes ZDBFFILE e ZMEMFILE\
             
Ela serve para unificar os métodos comuns de processamento e lógica de acesso a 
registros em tabela ISAM 
             
====================================================================================== */

CLASS ZXSAMXILE FROM LONGNAMECLASS

  DATA cError			    // Descrição do ultimo erro 
  DATA lVerbose             // Modo Verbose (echo em console ligado)
  DATA bFilter              // Codeblock de filtro 
  DATA nIndexOrd            // Ordem de indice atual 
  DATA aIndexes             // Array com objetos de indice 
  DATA oCurrentIndex        // Objeto do indice atual 
  DATA nRecno				// Numero do registro (RECNO) atualmnete posicionado 
  DATA nLastRec				// Ultimo registro do arquivo - Total de registros
  DATA aStruct		   		// Array com a estrutura do DBF 
  DATA nFldCount			// Quantidade de campos do arquivo 
  DATA lBOF					// Flag de inicio de arquivo 
  DATA lEOF					// Flag de final de arquivo 
  DATA lOpened              // Indica se o arquivo está aberto 
  DATA lCanWrite            // Arquivo aberto para gravacao 
  DATA aGetRecord			// Array com todas as colunas do registro atual 
  DATA aPutRecord           // Array com campos para update 
  //xx//DATA oISAMLogger              // Objeto de log 
  
  DATA oFileDef             // Definição extendida do arquivo 

  METHOD New()              // *** O Construtor nao pode ser chamado diretamente ***
  METHOD GoTo(nRec)		    // Posiciona em um registro informado. 
  METHOD GoTop()			// Posiciona no RECNO 1 da tabela 
  METHOD GoBottom()   	    // Posiciona em LASTREC da tabela 
  METHOD Skip()             // Navegação de registros ISAM 
  METHOD SetFilter()        // Permite setar um filtro para os dados 
  METHOD ClearFilter()      // Limpa o filtro 
  METHOD BOF()				// Retorna .T. caso tenha se tentado navegar antes do primeiro registro 
  METHOD EOF()				// Retorna .T, caso o final de arquivo tenha sido atingido 
  METHOD Lastrec()			// Retorna o total de registros / numero do ultimo registro da tabela 
  METHOD RecCount()			// Retorna o total de registros / numero do ultimo registro da tabela 
  METHOD GetStruct()		// Retorna CLONE da estrutura de dados da tabela 
  METHOD FCount()           // Retorna o numero de campo / colunas da tabela
  METHOD FieldName( nPos )	// Recupera o nome da coluna informada 
  METHOD FieldPos( cField ) // Retorna a posicao de um campo na estrutura da tabela ( ID da Coluna )
  METHOD FieldType( nPos )	// Recupera o tipo da coluna informada 

  METHOD SetOrder()         // Seta um indice / ordem ativa 
  METHOD IndexOrd()         // Retorna a ordem ativa
  METHOD IndexKey()         // Retorna a expressao de indice ativa 
  METHOD IndexValue()       // Retorna o valor da chave de indice do registro atual 
  METHOD Seek(cKeyExpr)     // Realiza uma busca usando o indice ativo 
  METHOD CreateIndex()      // Cria um Indice ( em memoria ) para a tabela 
  METHOD ClearIndex()       // Fecha todos os indices
  METHOD Search()           // Busca um registro que atenda os criterios informados

  METHOD CreateFrom()       // Cria tabela a partir da estrutura do objeto ou alias informado
  METHOD AppendFrom()       // Apenda dados do objeto ou alias informado na tabela atual 
  METHOD Export()           // Exporta o arquivo para um outro formato
  METHOD Import()           // Importa dados de arquivo externo em outro formato ( SDF,CSV,JSON )

  METHOD GetErrorStr()		// Retorna apenas a descrição do ultimo erro ocorrido

  METHOD SetVerbose()       // Liga ou desliga o modo "verbose" da classe
  METHOD IsVerbose()        // Consulta ao modo verbose
  
  METHOD SetFileDef()       // Guarda o objeto da definição do arquivo 


  // ========================= Metodos de uso interno da classe

  METHOD _ResetError()		// Limpa a ultima ocorrencia de erro 
  METHOD _SetError()        // Seta uma nova ocorrencia de erro 
  METHOD _InitVars() 		// Inicializa propriedades  

  METHOD _CheckFilter()     // Verifica se o registro atual está contemplado no filtro 
  METHOD _SkipNext()		// Le o proximo registro da tabela 
  METHOD _SkipPrev()        // Le o registro anterior da tabela 
  METHOD _ClearRecord()     // Limpa o conteudo do registro em memoria 
  METHOD _BuildFieldBlock(cFieldExpr) // Cria codeblock com expressao de campos 

  METHOD _ExportSDF()       // Exporta dados para arquivo SDF
  METHOD _ExportCSV()       // Exporta dados para arquivo CSV
  METHOD _ExportJSON()      // Exporta dados para arquivo JSON
  METHOD _ExportXML()       // Exporta dados para arquivo XML 
 
  METHOD _ImportSDF()       // Importa dados de arquivo SDF
  METHOD _ImportCSV()       // Importa dados de arquivo CSV
  METHOD _ImportJSON()      // Importa dados de arquivo JSON
 
ENDCLASS


// ----------------------------------------
METHOD New(oParent) CLASS ZXSAMXILE
//xx//::oISAMLogger := ZLOGGER():New("ZXSAMXILE")
//xx//::oISAMLogger:Write("NEW","IsamFile based on "+GetClassName(oParent))
Return

// ----------------------------------------
// Retorna .T. caso a ultima movimentação de registro 
// tentou ir antes do primeiro registro 
METHOD BOF() CLASS ZXSAMXILE 
Return ::lBOF

// ----------------------------------------\
// Retorna .T. caso a tabela esteja em EOF
METHOD EOF() CLASS ZXSAMXILE
Return ::lEOF

// ----------------------------------------------------------
// Posiciona diretamente em um regsitro 

METHOD GoTo(nRec)  CLASS ZXSAMXILE

// Verifica se o registro é válido 
// Se não for, vai para EOF
          
//xx//::oISAMLogger:Write("GoTo","Record "+cValToChar(nRec))

If nRec > ::nLastRec .or. nRec < 1
	::lEOF := .T.
	::_ClearRecord()
	Return
Endif

// ----------------------------------------
// Atualiza o numero do registro atual 
::nRecno := nRec

// Traz o registro atual para a memória
::_ReadRecord()

Return

// ----------------------------------------------------------
// Movimenta a tabela para o primeiro registro 
// Release 20190105 : Contempla uso de indice

METHOD GoTop() CLASS ZXSAMXILE 

//xx//::oISAMLogger:Write("GoToP")

IF ::nLastRec == 0 
	// Nao há registros 
	::lBOF := .T. 
	::lEOF := .T. 
	::nRecno   := 0
	::_ClearRecord()
	Return
Endif

If ::nIndexOrd > 0 
	// Se tem indice ativo, pergunta pro indice
	// quanl é o primeiro registro da ordem 
	::nRecno := ::oCurrentIndex:GetFirstRec()
Else
	// Ordem fisica 
	// Atualiza para o primeiro registtro 
	::nRecno     := 1
Endif

// Traz o registro atual para a memória
::_ReadRecord()

If ( !::_CheckFilter() )
	// Nao passou na verificacao do filtro
	// busca o proximo registro que atenda
	::_SkipNext()
Endif

Return

// ----------------------------------------------------------
// Movimenta a tabela para o ultimo registro

METHOD GoBottom() CLASS ZXSAMXILE 

//xx//::oISAMLogger:Write("GoBottom")

IF ::nLastRec == 0 
	// Nao há registros 
	::lBOF := .T. 
	::lEOF := .T. 
	::nRecno   := 0
	::_ClearRecord()
	Return
Endif

If ::nIndexOrd > 0 
	// Se tem indice ativo, pergunta pro indice
	// quanl é o primeiro registro da ordem 
	::nRecno := ::oCurrentIndex:GetLastRec()
Else
	// Ordem fisica 
	// Atualiza o RECNO para o ultimo registro 
	::nRecno     := ::nLastRec
Endif

// Traz o registro atual para a memória
::_ReadRecord()

If ( !::_CheckFilter() )
	// Nao passou na verificacao do filtro
	// busca nos registros anteriores o primeiro que atende
	::_SkipPrev()
Endif

Return

// ----------------------------------------------------------
// Avançã ou retrocede o ponteiro de registro 
// No caso de DBSkip(0), apenas faz refresh do registro atual   
// Default = 1 ( Próximo Registro ) 

METHOD Skip( nQtd ) CLASS ZXSAMXILE
Local lForward := .T. 

//xx//::oISAMLogger:Write("Skip")

If nQtd  == NIL
	nQtd := 1
ElseIF nQtd < 0 
	lForward := .F. 
Endif

// Quantidade de registros para mover o ponteiro
// Se for negativa, remove o sinal 
nQtd := abs(nQtd)

While nQtd > 0 
	If lForward
		IF ::_SkipNext()
			nQtd--
		Else
			// Bateu EOF()
			::_ClearRecord()
			Return
		Endif
	Else
		IF ::_SkipPrev()
			nQtd--
		Else
			// Bateu BOF()
			Return
		Endif
	Endif
Enddo

// Traz o registro atual para a memória
::_ReadRecord()

Return


// ----------------------------------------------------------
// Permite setar um filtro para a navegação de dados 
// Todos os campos devem estar em letras maiusculas 

METHOD SetFilter( cFilter ) CLASS ZXSAMXILE
Local cFilterBlk

//xx//::oISAMLogger:Write("SetFilter",cFilter)

// retorna string com codebloc para expressao de campos 
cFilterBlk := ::_BuildFieldBlock(cFilter)

// Monta efetivamente o codeblock 
::bFilter := &(cFilterBlk)

Return .T. 

// ----------------------------------------------------------
// Limpa a expressao de filtro atual 

METHOD ClearFilter() CLASS ZXSAMXILE
//xx//::oISAMLogger:Write("ClearFilter")
::bFilter := NIL
Return


// ----------------------------------------------------------
// Retorna o numero do ultimo registro da tabela 

METHOD Lastrec() CLASS ZXSAMXILE
Return ::nLastRec

// ----------------------------------------------------------
// Colocado apenas por compatibilidade 
// 

METHOD Reccount() CLASS ZXSAMXILE
Return ::nLastRec

// ----------------------------------------------------------
// Retorna um clone do Array da estrutura da tabela 

METHOD GetStruct() CLASS ZXSAMXILE
Return aClone( ::aStruct )

// ----------------------------------------------------------
// Retorna o numero de campo / colunas da tabela
METHOD FCount()  CLASS ZXSAMXILE
Return ::nFldCount

// ----------------------------------------------------------
// Recupera o nome de um campo da tabela 
// a partir da posicao do campo na estrutura

METHOD FieldName(nPos) CLASS ZXSAMXILE
If nPos > 0 .and. nPos <= ::nFldCount 
	Return ::aStruct[nPos][1]
Endif
Return NIL

// ----------------------------------------------------------
// Recupera o numero do campo na estrutura da tabela 
// a partir do nome do campo 

METHOD FieldPos( cField ) CLASS ZXSAMXILE
Return ASCAN( ::aStruct , {|x| x[1] = cField })

// ----------------------------------------------------------
// Recupera o tipo do campo na estrutura da tabela 
// a partir da posicao do campo na estrutura

METHOD FieldType(nPos) CLASS ZXSAMXILE
If nPos > 0 .and. nPos <= ::nFldCount 
	Return ::aStruct[nPos][2]
Endif
Return NIL

// ----------------------------------------
// Permite trocar a ordedm atual usando 
// um indice aberto 

METHOD SetOrder(nOrd) CLASS ZXSAMXILE

//xx//::oISAMLogger:Write("SetOrder","Order: "+cValToChar(nOrd))

If nOrd < 0 .OR.  nOrd > len( ::aIndexes )
	UserException("DbSetOrder - Invalid Order "+cValToChar(nOrd))
Endif
::nIndexOrd := nOrd
If ::nIndexOrd > 0 
	::oCurrentIndex := ::aIndexes[::nIndexOrd]
Else
	::oCurrentIndex := NIL
Endif
Return

// ----------------------------------------
// Retorna o numero da ordem do indce ativo 

METHOD IndexOrd() CLASS ZXSAMXILE
Return ::nIndexOrd

// ----------------------------------------
// Retorna a expressao da chave de indice atual 
// Caso nao haja indice ativo, retorna ""

METHOD IndexKey() CLASS ZXSAMXILE
IF ::nIndexOrd > 0 
	Return ::oCurrentIndex:GetIndexExpr()
Endif
Return ""

// ----------------------------------------
// Retorna o numero da ordem do indce ativo 
METHOD IndexValue() CLASS ZXSAMXILE
IF ::nIndexOrd > 0 
	Return ::oCurrentIndex:GetIndexValue()
Endif
Return NIL


// ----------------------------------------
// Retorna o numero da ordem do indce ativo 
METHOD Seek(cKeyExpr) CLASS ZXSAMXILE
Local nRecFound := 0

//xx//::oISAMLogger:Write("Seek","Key: "+cKeyExpr)

IF ::nIndexOrd <= 0
	UserException("DBSeek Failed - No active Index")
Endif

nRecFound := ::oCurrentIndex:IndexSeek(cKeyExpr)

If nRecFound > 0
	// NAo precisa resincronizar o indice
	// Eu já fiz a busca pelo indice
	::nRecno := nRecFound
	::_ReadRecord()
	Return .T.
Endif

// Nao achou nada, vai para EOF 
::lEOF := .T.
::_ClearRecord()

Return .F.
	
  
// ----------------------------------------
// *** METODO DE USO INTERNO ***
// Cria uma instancia de um indice em memoria 
// Acrescenta na lista de indices 
// Torna o indice ativo e posiciona no primeiro 
// registro da nova ordem 

METHOD CreateIndex(cIndexExpr) CLASS ZXSAMXILE
Local oMemIndex
Local nLastIndex

//xx//::oISAMLogger:Write("CreateIndex","Expression: "+cIndexExpr)

// Cria o objeto do indice passando a instancia
// do arquivo DBF atual 
oMemIndex := ZMEMINDEX():New(self)

// Cria o indice com a expressao informada
oMemIndex:CreateIndex(cIndexExpr) 

// Acrescenta o indice criado na tabela 
aadd(::aIndexes,oMemIndex)

// E torna este indice atual 
nLastIndex := len(::aIndexes)
::SetOrder( nLastIndex ) 

// Posiciona no primeiro registro da nova ordem 
::GoTop()

Return

// ----------------------------------------
// Fecha todos os indices

METHOD ClearIndex()  CLASS ZXSAMXILE
Local nI

//xx//::oISAMLogger:Write("ClearIndex")

For nI := 1 to len(::aIndexes)
	::oCurrentIndex := ::aIndexes[nI]
	::oCurrentIndex:Close()
	FreeObj(::oCurrentIndex)
Next

Return

// ----------------------------------------------------------
// Cria um arquivo de dados na instancia atual usando a estrutura 
// do objeto de arquivo de dados informado como parametro 
// Pode ser infomado um Alias / WorkArea
// Caso lAppend seja .T., a tabela é aberta em modo exclusivo e para gravação 
// e os dados são importados

METHOD CreateFrom( _oDBF , lAppend  ) CLASS ZXSAMXILE
Local lFromAlias := .F. 
Local cAlias := ""
Local aStruct := {}

//xx//::oISAMLogger:Write("CreateFrom")

If lAppend = NIL ; lAppend := .F. ; Endif

If valtype(_oDBF) == 'C'

	// Se a origem é caractere, só pode ser um ALIAS 
	lFromAlias := .T. 
	cAlias := alltrim(upper(_oDBF))
	If Select(cAlias) < 1 
		UserException("Alias does not exist - "+cAlias)
	Endif

	aStruct := (cAlias)->(DbStruct())
	
Else

	aStruct := _oDBF:GetStruct()

Endif

If !::Create(aStruct)
	Return .F.
Endif

IF lAppend

	// Dados serão apendados na criação 
	// Abre para escrita exclusiva 
	
	If !::Open(.T.,.T.)
		Return .F.
	Endif

	// Apenda os dados	
	IF !::AppendFrom(_oDBF)
		Return .F.
	Endif

	// E posiciona no primeiro registro 	
	::GoTop()
	
Endif

Return .T.


// ----------------------------------------------------------
// Apena os dados da tabela informada na atual 
// Origem = _oDBF
// Destino = self

METHOD AppendFrom( _oDBF , lAll, lRest , cFor , cWhile ) CLASS ZXSAMXILE
Local aFromTo := {}
Local aFrom := {}
Local nI, nPos, cField
Local lFromAlias := .F. 
Local cAlias := ""

//xx//::oISAMLogger:Write("AppendFrom")

DEFAULT lAll  := .T. 
DEFAULT lRest := .F.
DEFAULT cFor := ''
DEFAULT cWhile := ''
              
// Primeiro, a tabela tem qye estar aberta
IF !::lOpened
	UserException("AppendFrom Failed - Table not opened")
	Return .F.
Endif

IF !::lCanWrite
	UserException("AppendFrom Failed - Table opened for READ ONLY")
	Return .F.
Endif

If valtype(_oDBF) == 'C'

	// Se a origem é caractere, só pode ser um ALIAS 
	lFromAlias := .T. 
	cAlias := alltrim(upper(_oDBF))
	If Select(cAlias) < 1 
		UserException("Alias does not exist - "+cAlias)
	Endif

	aFrom := (cAlias)->(DbStruct())
	
Else

	aFrom := _oDBF:GetStruct()

Endif

// Determina match de campos da origem no destino 
For nI := 1 to len(aFrom)
	cField :=  aFrom[nI][1]
	nPos := ::FieldPos(cField)
	If nPos > 0 
		aadd( aFromTo , { nI , nPos })
	Endif
Next

IF lFromAlias
	
	// Dados de origem a partir de uma WorkArea
	
	If lAll 
		// Se é para importar tudo, pega desde o primeiro registro 
		(cAlias)->(DbGoTop())
	Endif
	
	While !(cAlias)->(EOF())

		// Insere um novo registro na tabela atual
		::Insert()

		// Preenche os campos com os valores da origem
		For nI := 1 to len(aFromTo)
			::FieldPut(  aFromTo[nI][2] , (cAlias)->(FieldGet(aFromTo[nI][1]))  )
		Next

		// Atualiza os valores
		::Update()

		// Vai para o procimo registro
		(cAlias)->(DbSkip())
	Enddo
	
Else
	
	If lAll 
		// Se é para importar tudo, pega desde o primeiro registro 
		_oDBF:GoTop()
	Endif
	
	While !_oDBF:EOF()

		// Insere um novo registro na tabela atual
		::Insert()

		// Preenche os campos com os valores da origem
		For nI := 1 to len(aFromTo)
			::FieldPut(  aFromTo[nI][2] , _oDBF:FieldGet(aFromTo[nI][1])  )
		Next

		// Atualiza os valores
		::Update()

		// Vai para o procimo registro
		_oDBF:Skip()

	Enddo
	
Endif

Return .T. 

// ----------------------------------------------------------
// Exporta o arquivo para um outro formato
// cFormat = Formato a exportar 
//    SDF
//    CSV 
//    JSON
//    XML
// cFileOut = Arquivo de saída 

METHOD Export( cFormat, cFileOut , bBlock ) CLASS ZXSAMXILE

//xx//::oISAMLogger:Write("Export")

// Primeiro, a tabela tem qye estar aberta
IF !::lOpened
	UserException("ZXSAMXILE:EXPORT() Failed - Table not opened")
	Return .F.
Endif

cFormat := alltrim(Upper(cFormat))

If cFormat == "SDF" 
	lOk := ::_ExportSDF(cFileOut)	
ElseIf cFormat == "CSV" 
	lOk := ::_ExportCSV(cFileOut)	
ElseIf cFormat == "JSON" 
	lOk := ::_ExportJSON(cFileOut)
ElseIf cFormat == "XML"
	lOk := ::_ExportXML(cFileOut)
Else
	UserException("Export() ERROR - Formato ["+cFormat+"] não suportado. ")
Endif

Return lOk


// ----------------------------------------------------------
// Recebe a definicao extendida da tabela 
// Com isso eu já tenho a estrutura 

METHOD SetFileDef(oDef)  CLASS ZXSAMXILE

//xx//::oISAMLogger:Write("SetFileDef")

IF ::lOpened
	UserException("SetFileDef Failed - Table already opened")
	Return .F.
Endif

// Recebe a definição do arquivo 
::oFileDef := oDef

Return .T. 

// ----------------------------------------------------------
// Formato SDF
// Texto sem delimitador , Campos colocados na ordem da estrutura
// CRLF como separador de linhas
// Campo MEMO não é exportado

METHOD _ExportSDF( cFileOut ) CLASS ZXSAMXILE
Local nHOut
Local nPos
Local cBuffer := ''
Local cRow
Local cTipo,nTam,nDec

//xx//::oISAMLogger:Write("_ExportSDF")

nHOut := fCreate(cFileOut)
If nHOut == -1
	::_SetError("Output SDF File Create Error - FERROR "+cValToChar(Ferror()))
	Return .F.
Endif

::GoTop()

While !::Eof()
	
	// Monta uma linha de dados
	cRow := ""
	
	For nPos := 1 TO ::nFldCount
		cTipo := ::aStruct[nPos][2]
		nTam  := ::aStruct[nPos][3]
		nDec  := ::aStruct[nPos][4]

		IF cTipo = 'M'
			Loop
		Endif

		If cTipo = 'C'
			cRow += ::FieldGet(nPos)
		ElseIf cTipo = 'N'
			cRow += Str(::FieldGet(nPos),nTam,nDec)
		ElseIf cTipo = 'D'
			cRow += DTOS(::FieldGet(nPos))
		ElseIf cTipo = 'L'
			cRow += IIF(::FieldGet(nPos),'T','F')
		Endif
	Next
	
	cRow += CRLF
	cBuffer += cRow
	
	If len(cBuffer) > 32000
		// A cada 32 mil bytes grava em disco
		fWrite(nHOut,cBuffer)
		cBuffer := ''
	Endif
	
	::Skip()
	
Enddo

// Grava flag de EOF
cBuffer += Chr(26)

// Grava resto do buffer que falta
fWrite(nHOut,cBuffer)
cBuffer := ''

fClose(nHOut)

Return

// ----------------------------------------------------------
// Formato CSV
// Strings entre aspas duplas, campos colocados na ordem da estrutura
// Virgula como separador de campos, CRLF separador de linhas 
// Gera o CSV com Header
// Campo MEMO não é exportado

METHOD _ExportCSV( cFileOut ) CLASS ZXSAMXILE
Local nHOut
Local nPos
Local cBuffer := ''
Local cRow
Local cTipo,nTam,nDec
	
//xx//::oISAMLogger:Write("_ExportCSV")

nHOut := fCreate(cFileOut)
If nHOut == -1
	::_SetError("Output CSV File Create Error - FERROR "+cValToChar(Ferror()))
	Return .F.
Endif

// Primeira linha é o "header" com o nome dos campos 
For nPos := 1 TO ::nFldCount
	If ::aStruct[nPos][2] == 'M'
		Loop
	Endif	
	If nPos > 1 
		cBuffer += ','
	Endif
	cBuffer += '"'+Alltrim(::aStruct[nPos][1])+'"'
Next
cBuffer += CRLF

::GoTop()

While !::Eof()
	
	// Monta uma linha de dados
	cRow := ""
	
	For nPos := 1 TO ::nFldCount
		cTipo := ::aStruct[nPos][2]
		nTam  := ::aStruct[nPos][3]
		nDec  := ::aStruct[nPos][4]

		IF cTipo = 'M'
			Loop
		Endif

		If nPos > 1
			cRow += ","
		Endif
		
		If cTipo = 'C'
			// Dobra aspas duplas caso exista dentro do conteudo 
			cRow += '"' + StrTran(rTrim(::FieldGet(nPos)),'"','""') + '"'
		ElseIf cTipo = 'N'
			// Numero trimado 
			cRow += cValToChar(::FieldGet(nPos))
		ElseIf cTipo = 'D'
			// Data em formato AAAAMMDD entre aspas 
			cRow += '"'+Alltrim(DTOS(::FieldGet(nPos)))+'"'
		ElseIf cTipo = 'L'
			// Boooleano true ou false
			cRow += IIF(::FieldGet(nPos),'true','false')
		Endif
	Next
	
	cRow += CRLF
	cBuffer += cRow
	
	If len(cBuffer) > 32000
		// A cada 32 mil bytes grava em disco
		fWrite(nHOut,cBuffer)
		cBuffer := ''
	Endif
	
	::Skip()
	
Enddo

// Grava resto do buffer que falta 
If len(cBuffer) > 0 
	fWrite(nHOut,cBuffer)
	cBuffer := ''
Endif

fClose(nHOut)

Return .T. 


// ----------------------------------------------------------
// Formato JSON - Exporta estrutura e dados   
// Objeto com 2 propriedades 
// header : Array de Arrays, 4 colunas, estrutura da tabela
// data : Array de Arrays, cada linha é um registro da tabela, 
// campos na ordem da estrutura
// -- Campo Memo não é exportado 

/* 	{ 	
"header": [
	["cCampo", "cTipo", nTam, nDec], ...
],
"data": [
    ["José", 14, true], ...
] 	}
*/

METHOD _ExportJSON( cFileOut ) CLASS ZXSAMXILE
Local nHOut
Local nPos
Local cBuffer := ''
Local lFirst := .T.
Local cRow
Local cTipo,nTam,nDec

//xx//::oISAMLogger:Write("_ExportJSON")

nHOut := fCreate(cFileOut)
If nHOut == -1
	::_SetError("Output JSON File Create Error - FERROR "+cValToChar(Ferror()))
	Return .F.
Endif


cBuffer += '{' + CRLF
cBuffer += '"header": [' + CRLF

For nPos := 1 to len(::aStruct)
	If ::aStruct[nPos][2] == 'M'
		LOOP
	Endif
	If nPos = 1
		cBuffer += "["
	Else
		cBuffer += '],'+CRLF+'['
	Endif
	cBuffer += '"'+Alltrim(::aStruct[nPos][1])+'","'+;
	::aStruct[nPos][2]+'",'+;
	cValToChar(::aStruct[nPos][3])+','+;
	cValToChar(::aStruct[nPos][4])
Next

cBuffer += ']'+CRLF
cBuffer += ']' + CRLF
cBuffer += ',' + CRLF
cBuffer += '"data": [' + CRLF

::GoTop()

While !::Eof()
	
	// Monta uma linha de dados
	if lFirst
		cRow := "["
		lFirst := .F.
	Else
		cRow := "],"+CRLF+"["
	Endif
	
	For nPos := 1 TO ::nFldCount

		cTipo := ::aStruct[nPos][2]
		nTam  := ::aStruct[nPos][3]
		nDec  := ::aStruct[nPos][4]

		IF cTipo = 'M'
			Loop
		Endif

		If nPos > 1
			cRow += ","
		Endif
		If cTipo = 'C'
			// Usa Escape sequence de conteudo
			// para astas duplas. --
			cRow += '"' + StrTran(rTrim(::FieldGet(nPos)),'"','\"') + '"'
		ElseIf cTipo = 'N'
			// Numero trimado
			cRow += cValToChar(::FieldGet(nPos))
		ElseIf cTipo = 'D'
			// Data em formato AAAAMMDD como string
			cRow += '"'+Alltrim(DTOS(::FieldGet(nPos)))+'"'
		ElseIf cTipo = 'L'
			// Boooleano = true ou false
			cRow += IIF(::FieldGet(nPos),'true','false')
		Endif
	Next
	
	cBuffer += cRow
	
	If len(cBuffer) > 32000
		// A cada 32 mil bytes grava em disco
		fWrite(nHOut,cBuffer)
		cBuffer := ''
	Endif
	
	::Skip()
	
Enddo

// Termina o JSON
cBuffer += ']' + CRLF
cBuffer += ']' + CRLF
cBuffer += '}' + CRLF

// Grava o final do buffer
fWrite(nHOut,cBuffer)
cBuffer := ''

// Fecha o Arquivo
fClose(nHOut)

Return .T.


// ----------------------------------------------------------
// Formato XML - Exporta estrutura e dados   
// Objeto com 2 array de propriedades : header e data
// Para economizar espaço, as colunas de dados são nomeadas com as tags col1, col2 ... n

METHOD _ExportXML( cFileOut ) CLASS ZXSAMXILE
Local nHOut
Local nPos
Local cBuffer := ''
Local cRow
Local cCampo,cTipo,nTam,nDec

//xx//::oISAMLogger:Write("_ExportXML")

nHOut := fCreate(cFileOut)
If nHOut == -1
	::_SetError("Output XML File Create Error - FERROR "+cValToChar(Ferror()))
	Return .F.
Endif

cBuffer += '<?xml version="1.0" encoding="windows-1252" ?>' + CRLF
cBuffer += '<table>' + CRLF

cBuffer += '<header>' + CRLF

For nPos := 1 to len(::aStruct)

	If ::aStruct[nPos][2] == 'M'
		LOOP
	Endif
                
	cBuffer += '<field>'
    cBuffer += '<name>' +lower(Alltrim(::aStruct[nPos][1]))+ '</name>'
    cBuffer += '<type>' +::aStruct[nPos][2]+ '</type>'
    cBuffer += '<size>' +cValToChar(::aStruct[nPos][3])+ '</size>'
    cBuffer += '<decimal>' +cValToChar(::aStruct[nPos][4])+ '</decimal>'
    cBuffer += '</field>' + CRLF 
    
Next

cBuffer += '</header>' + CRLF
cBuffer += '<data>' + CRLF

::GoTop()

While !::Eof()
	
	// Monta uma linha de dados
	cRow := '<record id="'+cValToChar(::Recno())+'">'
	
	For nPos := 1 TO ::nFldCount
	
		cCampo := ::aStruct[nPos][1]
		cTipo  := ::aStruct[nPos][2]
		nTam   := ::aStruct[nPos][3]
		nDec   := ::aStruct[nPos][4]

		IF cTipo = 'M'
			Loop
		Endif

	    cRow += '<'+lower(alltrim(cCampo))+'>'

		If cTipo = 'C'
			// Usa Escape sequence de conteudo
			// para aspas duplas. --
			cRow += StrTran(rTrim(::FieldGet(nPos)),'"','&quot;')
		ElseIf cTipo = 'N'
			// Numero trimado, com "." ponto deecimal 
			cRow += cValToChar(::FieldGet(nPos))
		ElseIf cTipo = 'D'
			// Data em formato AAAAMMDD 
			cRow += Alltrim(DTOS(::FieldGet(nPos)))
		ElseIf cTipo = 'L'
			// Boooleano = true ou false
			cRow += IIF(::FieldGet(nPos),'true','false')
		Endif

	    cRow += '</'+lower(alltrim(cCampo))+'>'

	Next
	
	cRow += '</record>' + CRLF 

	cBuffer += cRow
	
	If len(cBuffer) > 32000
		// A cada 32 mil bytes grava em disco
		fWrite(nHOut,cBuffer)
		cBuffer := ''
	Endif
	
	::Skip()
	
Enddo

// Termina o XML
cBuffer += '</data>' + CRLF
cBuffer += '</table>' + CRLF

// Grava o final do buffer
fWrite(nHOut,cBuffer)
cBuffer := ''

// Fecha o Arquivo
fClose(nHOut)

Return .T.

// --------------------------------------------------------------------
// Importacao de dados de arquivo externo -- Formatos SDF,CDV e JSON   

METHOD Import(cFileIn,cFormat) CLASS ZXSAMXILE
Local lOk

//xx//::oISAMLogger:Write("Import")

// Primeiro, a tabela tem qye estar aberta
IF !::lOpened
	UserException("Import Failed - Table not opened")
	Return .F.
Endif

IF !::lCanWrite
	UserException("Import Failed - Table opened for READ ONLY")
	Return .F.
Endif

// Ajusta formato 
cFormat := alltrim(Upper(cFormat))

If cFormat == "SDF"
	lOk := ::_ImportSDF(cFileIn)
ElseIf 	cFormat == "CSV"
	lOk := ::_ImportCSV(cFileIn)
ElseIf 	cFormat == "JSON"
	lOk := ::_ImportJSON(cFileIn)
Else
	UserException("Export() ERROR - Formato ["+cFormat+"] não suportado. ")
Endif

Return lOk 


// --------------------------------------------------------------------
// Importacao de arquivo SDF 
// A estrutura tem que ser a mesma que o arquivo foi gerado 
// Nao tengo como validar os campos, mas tenho como fazer uma consistencia 
// Baseado no tamanho de cada linha com a estrutura atual da tabela. 

METHOD _ImportSDF(cFileIn) CLASS ZXSAMXILE
Local nH ,nFSize
Local cOneRow := ''
Local nRowSize := 0
Local nRows := 0 
Local nCheck 
Local nOffset 
Local cTipo, nTam, nPos
Local cValue, xValue

//xx//::oISAMLogger:Write("_ImportSDF")

// Abre o arquivo SDF para leitura 
nH := FOpen(cFileIn)

If nH == -1
	::_SetError( "_ImportSDF() ERROR - File Open Failed - FERROR "+cValToChar(ferror()) )
	Return .F. 
Endif

// Pega tamanho do arquivo no disco 
nFSize := fSeek(nH,0,2)
FSeek(nH,0)
          
// Calcula o tamanho de cada linha baseado na estrutura
For nPos := 1 TO ::nFldCount
	cTipo := ::aStruct[nPos][2]
	If cTipo = 'M' 
		// Ignora campos MEMO 
		LOOP
	Endif
	nTam  := ::aStruct[nPos][3]
	nRowSize += nTam
Next

// Cada linha do SDF deve ter o numero de bytes 
// de acordo com a estrutura da tabela, mais CRLF 

nRowSize += 2

// O resto da divisao ( Modulo ) do tamanho do arquivo 
// pelo tamanho da linha deve ser 1 -- devido 
// ao ultimo byte (0x1A / Chr)26)) indicando EOF

nCheck := nFsize % nRowSize

If nCheck <> 1

	::_SetError( "_ImportSDF() ERROR - SDF File Size FERROR MISMATCH" )
	FClose(nH)
	Return .F. 

Endif

// Calcula quantas linhas tem no arquivo 
nRows :=  (nFsize-1) / nRowSize

While nRows > 0 

	// Le uma linha do arquivo 
    fRead(nH,@cOneRow,nRowSize)
	
	// Insere nova linha em branco 
	::Insert()

	// Le os valores de cOneRow
	nOffset := 1
	For nPos := 1 TO ::nFldCount

		cTipo := ::aStruct[nPos][2]
		
		If cTipo = 'M' 
			// Ignora campos MEMO 
			LOOP
		Endif
		
		nTam  := ::aStruct[nPos][3]

		cValue	:= substr(cOneRow,nOffset,nTam)
		nOffset += nTam
		
		If cTipo == "C"
			::Fieldput(nPos,cValue)
		ElseIf cTipo == "N"
			xValue := Val(cValue)
			::Fieldput(nPos,xValue)
		ElseIf cTipo == "D"
			xValue := STOD(cValue)
			::Fieldput(nPos,xValue)
		ElseIf cTipo == "L"
			xValue := ( cValue = 'T' )
			::Fieldput(nPos,xValue)
		Endif		

	Next
	
	::Update()

	nRows--

Enddo

FClose(nH)

Return


// ----------------------------------------
// Importacao de arquivo CSV
// Calculo o tamanho maximo da linha baseado na estrutura da tabela 
// e passo a ler o arquivo em blocos, parseando o conteudo lido em memória
// Comparo o Header com os campos da estrutura

METHOD _ImportCSV(cFileIn) CLASS ZXSAMXILE
Local nH , nFSize
Local cBuffer := '' , cTemp := ''
Local nMaxSize := 0
Local cValue , xValue
Local cTipo, nTam, nPos
Local nToRead 
Local nLidos , nI
Local aHeadCpos := {}
Local aFileCpos := {}

//xx//::oISAMLogger:Write("_ImportCSV")

// Abre o arquivo CSV para leitura 
nH := FOpen(cFileIn)

If nH == -1
	::_SetError( "_ImportCSV() ERROR - File Open Failed - FERROR "+cValToChar(ferror()) )
	Return .F. 
Endif

// Pega tamanho do arquivo no disco 
nFSize := fSeek(nH,0,2)
FSeek(nH,0)
          
// Calcula o tamanho máximo de uma linha baseado na estrutura da tabela 
For nPos := 1 TO ::nFldCount
	cCampo  := ::aStruct[nPos][1]
	cTipo := ::aStruct[nPos][2]
	
	If cTipo = 'M' 
		// Ignora campos MEMO 
		LOOP
	Endif
	nTam  := ::aStruct[nPos][3] 
	// Soma 3 ao tamanho de cada coluna
	// DElimitadores + separador 
	nMaxSize += ( nTam + 3 )

	// Monta a lista de campos baseado na estrutura atual 
	aadd(aFileCpos , alltrim(upper(cCampo)) )
Next

// Acrescenta um final de linha
nMaxSize += 2

// Le a primeira linha - HEader com os campos 
// Logo de cara lê 512 bytes 

nLidos := fRead(nH , @cBuffer , 512 )
nFSize -= nLidos

// Acha a quebra de linha e remove ela do buffer
nPos := AT( CRLF , cBuffer )
cOneRow := left(cBuffer , nPos-1)
cBuffer := substr(cBuffer,nPos+2)

// Cria array com os campos considerando a virgula como separador
aHeader := StrTokArr(cOneRow,",")

For nI := 1 to len(aHeader)
	cField := aHeader[nI]
	NoQuotes(@cField)

	// Monta a lista de campos baseado no header
	aadd(aHeadCpos, Alltrim(upper(cField)) )
Next

// Comparação de Arrays usando zCompare()
// 0 = Conteudos idênticos
// < 0 = Diferentes ( -1 tipo , -2 conteudo ou -3 tamanho de array ) 

If zCompare( aFileCpos , aHeadCpos ) < 0 
	fClose(nH)	
	::_SetError( "_ImportCSV() ERROR - Header Fields Mismatch." )
	Return .F. 
Endif

// Uma linha deste arquivo NUNCA deve chegar em nMaxSize
// Ele é calculado assumindo que todas as colunas tem delimitador 
// e um separador, ele soma isso inclusive na ultima coluna 

While nFSize > 0 .or. !empty(cBuffer)
    
	IF len(cBuffer) < nMaxSize .and. nFSize > 0 
		// SE o buffer em memoria 
		nToRead := MIN ( nMaxSize * 5 , nFSize ) 
		nLidos := fRead(nH , @cTemp , nToRead )
		cTemp := left(cTemp,nLidos)
		nFSize -= nLidos
		cBuffer += cTemp
	Endif	

	// Agora identifica uma linha e faz parser de conteudo 

	nPos := AT( CRLF , cBuffer )
	cOneRow := left(cBuffer , nPos-1)
	cBuffer := substr(cBuffer,nPos+2)
	
	// Insere nova linha em branco 
	::Insert()

	For nPos := 1 to ::nFldCount
	
		cTipo := ::aStruct[nPos][2]
		nTam  := ::aStruct[nPos][3]

		If cTipo = 'M' 
			// Ignora campos MEMO 
			LOOP
		Endif

		// Pega procimo valor de campo e remove da linha 
		cValue := GetNextVal(@cOneRow)
		
		If cTipo == "C"
			// Tipo caractere, coloca valor direto 
			::Fieldput(nPos,cValue)
		ElseIf cTipo == "N"
			// Numérico, converte para numero 
			xValue := Val(cValue)
			::Fieldput(nPos,xValue)
		ElseIf cTipo == "D"
			// Data , string em formato AAAMMDD , converte para Data 
			xValue := STOD(cValue)
			::Fieldput(nPos,xValue)
		ElseIf cTipo == "L"
			// Booleano , pode ser Y, T , 1 ou TRUE
			xValue := Upper(cValue)
			If xValue = 'Y' .or. xValue = 'T' .or. xValue = '1' .or. xValue = 'TRUE'
				::Fieldput(nPos,.T.)
			Endif
		Endif		

	Next
	
	::Update()

Enddo	

FClose(nH)

Return

// ----------------------------------------

METHOD _ImportJSON(cFileIn) CLASS ZXSAMXILE
UserException("ZXSAMXILE:_ImportJSON() NOT IMPLEMENTED.")
Return .F. 


// ----------------------------------------
// *** METODO DE USO INTERNO ***
// Verifica se o registro atual está contemplado pelo filtro 
// Release 20190106 -- Contempla filtro de registros deletados

METHOD _CheckFilter() CLASS ZXSAMXILE

If ::lSetDeleted .AND. ::lDeleted
	// Filtro de deletados está ligado 
	// e este registro está deletado .. ignora
	Return .F. 
Endif

If ::bFilter != NIL 
	// Existe uma expressao de filtro 
	// Roda a expressão para saber se este registro 
	// deve estar  "Visivel" 
	Return Eval(::bFilter , self )	
Endif

Return .T. 

// ----------------------------------------
// *** METODO DE USO INTERNO ***
// Le e posiciona no proximo registro, considerando filtro 

METHOD _SkipNext() CLASS ZXSAMXILE
Local nNextRecno

While (!::lEOF)

	If ::nIndexOrd > 0 
		// Se tem indice ativo, pergunta pro indice
		// qual é o próximo registro
		nNextRecno := ::oCurrentIndex:GetNextRec()
	Else
		// Estou na ordem fisica
		// Parte do registro atual , soma 1 
		nNextRecno := ::Recno() + 1 
	Endif
	
	// Retornou ZERO ou 
	// Passou do final de arquivo, esquece
	If nNextRecno == 0 .OR. nNextRecno > ::nLastRec
		::lEOF := .T.
		::_ClearRecord()
		Return .F. 
	Endif

	// ----------------------------------------
	// Atualiza o numero do registro atual 
	::nRecno := nNextRecno

	// Traz o registro atual para a memória
	::_ReadRecord()

	// Passou na checagem de filtro ? Tudo certo 
	// Senao , continua lendo ate achar um registro valido 
	If ::_CheckFilter()
		Return .T. 
	Endif

Enddo

Return .F. 

// ----------------------------------------
// *** METODO DE USO INTERNO ***
// Le e posiciona no registro anmterior, considerando filtro 

METHOD _SkipPrev() CLASS ZXSAMXILE
Local nPrevRecno

While (!::lBOF)

	If ::nIndexOrd > 0 
		// Se tem indice ativo, pergunta pro indice
		// qual é o registro anterior
		nPrevRecno := ::oCurrentIndex:GetPrevRec()
	Else
		// Estou na ordem fisica
		// Parte do registro atual , subtrai 1
		nPrevRecno := ::Recno() - 1 
    Endif
    
	// Tentou ler antes do primeiro registro 
	// Bateu em BOF()
	If nPrevRecno < 1 
		::lBOF := .T.
		Return .F. 
	Endif

	// ----------------------------------------
	// Atualiza o numero do registro atual 
	::nRecno := nPrevRecno

	// Traz o registro atual para a memória
	::_ReadRecord()

	// Passou na checagem de filtro ? Tudo certo 
	// Senao , continua lendo ate achar um registro valido 
	If ::_CheckFilter()
		Return .T. 
	Endif

Enddo

// Chegou no topo. 
// Se tem filtro, e o registro nao entra no filtro, localiza 
// o primeir registro válido 
If ( !::_CheckFilter() )
	::GoTop()
	::lBOF := .T. 
Endif

Return .F. 

// ----------------------------------------------------------
// Retorna apenas a descrição do ultimo erro 

METHOD GetErrorStr() CLASS ZXSAMXILE 
Return ::cError

// ----------------------------------------------------------
// *** METODO DE USO INTERNO ***
// Limpa o registro do ultimo erro 

METHOD _ResetError() CLASS ZXSAMXILE 
::cError := ''
Return

// ----------------------------------------------------------
// *** METODO DE USO INTERNO ***
// Seta uma nova ocorrencia de erro

METHOD _SetError(cErrorMsg,cErrorDesc) CLASS ZXSAMXILE 
//xx//::oISAMLogger:Write("_SetError",cErrorMsg)
::cError := AllTrim(Str(cErrorMsg))+"-"+cErrorDesc
Return


// ----------------------------------------------------------
// Permite setar o modo "verbose" da classe

METHOD SetVerbose( lSet ) CLASS ZXSAMXILE 
::lVerbose := lSet
Return

// ----------------------------------------------------------
// Retorna  .T. se o modo verbose está ligado 

METHOD IsVerbose() CLASS ZXSAMXILE 
Return ::lVerbose

// ----------------------------------------------------------
// Inicializa as propriedades da classe base

METHOD _InitVars() CLASS ZXSAMXILE 

::lOpened       := .F. 
::lCanWrite     := .F. 
::cError        := ''
::lVerbose      := .T. 
::bFilter       := NIL
::lBof          := .F. 
::lEof          := .F. 
::nIndexOrd     := 0
::aIndexes      := {}
::oCurrentIndex := NIL
::nLastRec      := 0
::aStruct       := {}
::nFldCount     := 0
::aGetRecord    := {}
::aPutRecord    := {}

Return


// ----------------------------------------------------------
// *** METODO DE USO INTERNO ***
// Limpa os campos do registro atual de leitura
// ( Inicializa todos com os valores DEFAULT ) 
// Limpa campos de gravação / update 
// ( seta NIL nos elementos ) 

METHOD _ClearRecord() CLASS ZXSAMXILE
Local nI , cTipo , nTam

//xx//::oISAMLogger:Write("_ClearRecord")

// Inicializa com o valor default os campos da estrutura 
For nI := 1 to ::nFldCount
	cTipo := ::aStruct[nI][2]
	nTam  := ::aStruct[nI][3]
	If cTipo == 'C'
		::aGetRecord[nI] := space(nTam)
	ElseIf cTipo == 'N'
		::aGetRecord[nI] := 0
	ElseIf cTipo == 'D'
		::aGetRecord[nI] := ctod('')
	ElseIf cTipo == 'L'
		::aGetRecord[nI] := .F.
	ElseIf cTipo == 'M'
		::aGetRecord[nI] := 0
	Endif
Next

// Zera também registro de granação
::aPutRecord := Array(::nFldCount)

Return

// ----------------------------------------------------------
// Cria uma string para criar codeblock dinamico 
// baseado em expressao usando camposa da tabela
// Os campos devem estar em letras maiusculas. Cada campo será 
// trocado por o:FieldGet(nPos), o codeblock deve ser usado 
// com Eval() passando como argumento o objeto da tabela 

METHOD _BuildFieldBlock(cFieldExpr) CLASS ZXSAMXILE
Local aCampos := {}
Local cBlockStr
Local nI, nPos

//xx//::oISAMLogger:Write("_BuildFieldBlock",cFieldExpr)

// Cria lista de campos
aEval( ::aStruct , {|x| aadd(aCampos , x[1]) } )

// Ordena pelos maiores campos primeiro
aSort( aCampos ,,, {|x,y| alltrim(len(x)) > alltrim(len(y)) } )

// Copia a expressao 
cBlockStr := cFieldExpr

// Troca os campos por o:Fieldget(nCpo)
// Exemplo : CAMPO1 + CAMPO2 será trocado para o:FieldGet(1) + o:FieldGet(2)

For nI := 1 to len(aCampos)
	cCampo := alltrim(aCampos[nI])
	nPos   := ::Fieldpos(cCampo)
	cBlockStr  := StrTran( cBlockStr , cCampo,"o:FieldGet(" +cValToChar(nPos)+ ")")
Next

// Monta a string com o codeblock para indice
cBlockStr := "{|o| "+cBlockStr+"}"

Return cBlockStr

// Remove aspas duplas delimitadoras por referencia
// Retorna por referencia se a string estava 
// delimitada por aspas duplas 
STATIC Function NoQuotes(cQuotStr,lQuoted)
lQuoted := left(cQuotStr,1) = '"' .and. right(cQuotStr,1) = '"'
If lQuoted
	cQuotStr := Substr(cQuotStr,2,len(cQuotStr)-2)	
	cQuotStr := StrTran(cQuotStr,'""','"')
Endif
Return 

// ----------------------------------------------------------

STATIC Function GetNextVal(cCSVLine)
Local lQuoted := .F.
Local lInAspas := .F.
Local nI , nT := len(cCSVLine)
Local cRet := ''

If left(cCSVLine,1) == '"'
	lQuoted := .T.
Endif

For nI := 1 to nT
	cChar := substr(cCSVLine,nI,1)
	If cChar == ','
		IF lInAspas
			cRet += cChar
		Else
			cCSVLine := substr(cCSVLine,nI+1)
			EXIT
		Endif
	ElseIF cChar == '"'
		lInAspas := !lInAspas
		cRet += cChar
	Else
		cRet += cChar
	Endif
Next

IF  nI >  nT
	// Saou do loop sem achar o separador  ","
	// Logo, a linha acabou
	cCSVLine := ""
Endif

If lQuoted
	// Remove aspas antes e depois
	// Troca escape sequence de aspas [""] por ["]
	NoQuotes(@cRet)
Endif

Return cRet

// ----------------------------------------------------------
// Busca um registro que atenda os criterios informados
// aRecord recebe os dados a procurar no formato [1] Campo [2][ Conteudo 
// aFound retorna o registro encontrado por referencia ( todos os campos ) 
// no mesmo formato do aRecord, acrescido do RECNO 
// Por padrao a busca é feita por substring 
// Caso seja especificada busca EXATA, os conteudos dos campos 
// informados devem ter correspondencia exata com a base de dados

METHOD Search(aRecord,aFound,lExact)  CLASS ZXSAMXILE 
Local nCnt := len(aRecord)
Local nI
Local aFldPos := {}
Local nFound := 0

//xx//::oISAMLogger:Write("Search")

IF lExact = NIL
	lExact := .F.
Endif

aSize(aFound,0)

// Sempre posiciona no topo 

If nCnt <= 0 
	
	// Sem condições especificadas, pega 
	// o primeiro registro 
	::GoTop()

Else

	// Mapeia campos informados com a posição 
	// do campo no arquivo 	
	For nI := 1 to nCnt
		aadd( aFldPos , ::fieldpos(aRecord[nI][1]) )
	Next
    
	// Começã a busca sempre no inicio do arquivo 
	::GoTop()

	// FAz busca sequencial	
	While !::Eof()
		nFound := 0 
		For nI := 1 to nCnt 
			IF lExact
				// Busca exata
				IF ::FieldGet(aFldPos[nI]) == aRecord[nI][2]
					nFound++
				Endif
			Else
			    // Busca por substring ( ou "like %content%" ) 
				If alltrim(aRecord[nI][2]) $ ::FieldGet(aFldPos[nI])  
					nFound++
				Endif
			Endif
		Next
		If nFound == nCnt
			EXIT
		Endif
		::Skip()
	Enddo
	
Endif

If !::Eof()  
	// Nao estou em EOF = achei um registro 
	For nI := 1 to ::nFldCount
		aadd(aFound , {  ::FieldName(nI) , ::FieldGet(nI)  })
	Next
	// Acrescenta o RECNO no campo
	aadd(aFound,{"RECNO",::Recno()})
	Return .T.
Endif

::_SetError( "Nenhum registro foi encontrado baseado nos dados informados" )

Return .F. 
//-----------------------------------------------------------------------------
// isamfile - Fim
//-----------------------------------------------------------------------------

//-----------------------------------------------------------------------------
// libdec2hex - Início
//-----------------------------------------------------------------------------
STATIC __aHEX := {'0','1','2','3','4','5','6','7','8','9','A','B','C','D','E','F'}

STATIC Function DEC2HEX(nByte)
Local nL := ( nByte % 16 )
Local nH := ( nByte-nL) / 16
Return __aHEX[nH+1]+__aHEX[nL+1]

// ----------------------------------------
// Converte um valor hexadecimal de 00 a FF para decimal

//xx//STATIC Function HEX2DEC(cHex)
//xx//Local nH := asc(Upper(substr(cHex,1,1)))
//xx//Local nL := asc(Upper(substr(cHex,2,1)))
//xx//If nH <= 57 ;    nH -= 48 ; Else ;    nH -= 55 ; Endif
//xx//If nL <= 57 ;    nL -= 48 ; Else ;    nL -= 55 ; Endif
//xx//Return (nH*16)+nL
//-----------------------------------------------------------------------------
// libdec2hex - Fim
//-----------------------------------------------------------------------------

/*
ÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜÜ
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
±±ÚÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÂÄÄÄÄÄÄÂÄÄÄÄÄÄÄÄÄÄ¿±±
±±³Fun‡…o    ³ AjustaSX1³ Autor ³ Renan Fragoso         ³ Data ³ 29/05/09 ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÝÄÄÄÄÄÄÄÝÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÝÄÄÄÄÄÄÝÄÄÄÄÄÄÄÄÄÄ´±±
±±³Descri‡…o ³ Cria grupo de perguntas no SX1 quado rel. para vendedores  ³±±
±±ÃÄÄÄÄÄÄÄÄÄÄÅÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄ´±±
±±³ Uso      ³ CRM/Totvs			                                      ³±±
±±ÀÄÄÄÄÄÄÄÄÄÄÝÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÄÙ±±
±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±±
ßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßßß
*/
Static Function AjustaSX1(cPerg)

	Local aArea    := GetArea()
	Local _x1   := 0
	Local _x2   := 0
	Local aSX1  := {'X1_GRUPO', 'X1_ORDEM', 'X1_PERGUNT', 'X1_PERSPA', 'X1_PERENG', 'X1_VARIAVL', 'X1_TIPO', 'X1_TAMANHO', 'X1_DECIMAL', 'X1_PRESEL', 'X1_GSC', 'X1_VALID', 'X1_F3', 'X1_GRPSXG', 'X1_PYME', 'X1_VAR01', 'X1_DEF01', 'X1_DEFSPA1', 'X1_DEFENG1', 'X1_DEF02', 'X1_DEFSPA2', 'X1_DEFENG2', 'X1_DEF03', 'X1_DEFSPA3', 'X1_DEFENG3', 'X1_DEF04', 'X1_DEFSPA4', 'X1_DEFENG4', 'X1_DEF05', 'X1_DEFSPA5', 'X1_DEFENG5'}
	Local aPerg := {}
 
	aadd( aPerg, { cPerg, '01', 'Filtra Filiais ?    ', 'Filtra Filiais ?    ', 'Filtra Filiais ?    ', 'mv_ch1', 'N',  1, 0, 2, 'C', '', 'TSM0  ', '   ', '', 'mv_par01', '1=Todas        ', '1=Todos        ', '1=All          ', '2=Selecao ', '2=Selecao ', '2=Selection', '3=Filial Atual', '3=Filial corrie', '3=Current Branc', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '02', 'Tipo de relatorio ? ', 'Tipo de relatorio ? ', 'Type of Report ?    ', 'mv_ch2', 'N',  1, 0, 1, 'C', '', '      ', '   ', '', 'mv_par02', 'Analitico      ', 'Analitico      ', 'Analytical     ', 'Sintetico ', 'Sintetico ', 'Synthetic  ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '03', 'Do Prefixo ?        ', 'Do Prefixo ?        ', 'From Prefixo ?      ', 'mv_ch3', 'C',  3, 0, 0, 'G', '', '      ', '   ', '', 'mv_par03', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '04', 'Ate Prefixo ?       ', 'Ate Prefixo ?       ', 'To Prefixo ?        ', 'mv_ch4', 'C',  3, 0, 0, 'G', '', '      ', '   ', '', 'mv_par04', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '05', 'Do Titulo ?         ', 'Do Titulo ?         ', 'Do Titulo ?         ', 'mv_ch5', 'C',  9, 0, 0, 'G', '', '      ', '018', '', 'mv_par05', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '06', 'Ate Titulo ?        ', 'Ate Titulo ?        ', 'Ate Titulo ?        ', 'mv_ch6', 'C',  9, 0, 0, 'G', '', '      ', '018', '', 'mv_par06', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '07', 'Do Cliente ?        ', 'Do Cliente ?        ', 'Do Cliente ?        ', 'mv_ch7', 'C',  6, 0, 0, 'G', '', 'SA1   ', '001', '', 'mv_par07', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '08', 'Da Loja ?           ', 'Da Loja ?           ', 'Da Loja ?           ', 'mv_ch8', 'C',  2, 0, 0, 'G', '', '      ', '002', '', 'mv_par08', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '09', 'Ate Cliente ?       ', 'Ate Cliente ?       ', 'Ate Cliente ?       ', 'mv_ch9', 'C',  6, 0, 0, 'G', '', 'SA1   ', '001', '', 'mv_par09', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '10', 'Ate Loja ?          ', 'Ate Loja ?          ', 'Ate Loja ?          ', 'mv_cha', 'C',  2, 0, 0, 'G', '', '      ', '002', '', 'mv_par10', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '11', 'Da Emissao ?        ', 'Da Emissao ?        ', 'Da Emissao ?        ', 'mv_chb', 'D',  8, 0, 0, 'G', '', '      ', '   ', '', 'mv_par11', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '12', 'Ate Emissao ?       ', 'Ate Emissao ?       ', 'Ate Emissao ?       ', 'mv_chc', 'D',  8, 0, 0, 'G', '', '      ', '   ', '', 'mv_par12', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '13', 'Do Vencimento ?     ', 'Do Vencimento ?     ', 'Do Vencimento ?     ', 'mv_chd', 'D',  8, 0, 0, 'G', '', '      ', '   ', '', 'mv_par13', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '14', 'Ate Vencimento ?    ', 'Ate Vencimento ?    ', 'Ate Vencimento ?    ', 'mv_che', 'D',  8, 0, 0, 'G', '', '      ', '   ', '', 'mv_par14', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '15', 'Tipo de Cliente ?   ', 'Tipo de Cliente ?   ', 'Tipo de Cliente ?   ', 'mv_chf', 'C', 99, 0, 0, 'G', '', 'TPCLIS', '   ', '', 'mv_par15', '               ', '               ', '               ', '          ', '          ', '           ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '16', 'Tipo filtro Bancos ?', 'Tipo filtro Bancos ?', 'Tipo Filtro Bancos ?', 'mv_chg', 'N',  1, 0, 2, 'C', '', '      ', '   ', '', 'mv_par16', 'Todos          ', 'Todos          ', 'Todos          ', 'Selecao   ', 'Selecao   ', 'Selecao    ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '17', 'Empresa publica?    ', 'Empresa publica?    ', 'Empresa publica?    ', 'mv_chh', 'N',  1, 0, 3, 'C', '', '      ', '   ', '', 'mv_par17', 'Sim            ', 'Si             ', 'Yes            ', 'Nao       ', 'No        ', 'No         ', 'Ambas         ', 'Ambas          ', 'Ambas          ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '18', 'Cliente Monitorado? ', 'Cliente Monitorado? ', 'Cliente Monitorado ?', 'mv_chi', 'N',  1, 0, 3, 'C', '', '      ', '   ', '', 'mv_par18', 'Sim            ', 'Si             ', 'Yes            ', 'Nao       ', 'No        ', 'No         ', 'Ambas         ', 'Ambas          ', 'Ambas          ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '19', 'Situacao ?          ', 'Situacao ?          ', 'Situacao ?          ', 'mv_chj', 'C',  1, 0, 1, 'C', '', '      ', '   ', '', 'mv_par19', 'Todos          ', 'Todos          ', 'All            ', 'Selecao   ', 'Selecion  ', 'Selection  ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '20', 'Considera data?     ', 'Considera data?     ', 'Considera data?     ', 'mv_chk', 'N',  1, 0, 1, 'C', '', '      ', '   ', '', 'mv_par20', 'Vencimento Real', 'Vencimento Real', 'Vencimento Real', 'Vencimento', 'Vencimento', 'Vencimento ', '              ', '               ', '               ', '', '', '', '', '', '','' })
	aadd( aPerg, { cPerg, '21', 'Imprimir Tipos ?    ', 'Imprime Tipos ?     ', 'Print types ?       ', 'mv_chl', 'N',  1, 0, 0, 'C', '', '      ', '   ', '', 'mv_par21', 'Todos          ', 'Todos          ', 'Todos          ', 'Selecao   ', 'Selecao   ', 'Selecao    ', '              ', '               ', '               ', '', '', '', '', '', '','' })

	DbSelectAre('SX1')
	SX1->(DbSetOrder(1))
	For _x1 := 1 To Len(aPerg)
		If !DbSeek(Padr(cPerg, Len( SX1->(FieldGet(FieldPos("X1_GRUPO"))) ) )+ aPerg[_x1,2])
			RecLock("SX1",.T.)
		Else
			RecLock("SX1",.F.)
		EndIf

		For _x2 := 1 To Len(aSX1)
			SX1->(FieldPut(FieldPos(aSX1[_x2]), aPerg[_x1,_x2] ))
		Next _n2

		MsUnlock()
	Next _x1 

	RestArea(aArea)
Return
