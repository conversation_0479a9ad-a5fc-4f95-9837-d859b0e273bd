#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Repository.UpdateTableOfParticipantsRd0Repository

/*/{Protheus.doc} UpdateTableOfParticipantsRd0
This class has the purpose of realize an update on Table Rd0
@type class
@version 1.0 
<AUTHOR>
@since 10/25/2024
/*/
Class   UpdateTableOfParticipantsRd0
	Static  Method  execWithArrayDataRd0(cCodeParticipant   As  Character   ,   aDados  As  Array)
EndClass

/*/{Protheus.doc} UpdateTableOfParticipantsRd0::execWithArrayDataRd0
This Method use the Array with all data from RD0 and update the table using
the parameter cCodeParticipant to seek on this table.
@type method
@version 1.0  
<AUTHOR>
@since 10/25/2024
@param cCodeParticipant, character, The Code of the Participant
@param aDados, array, A data array with income data from the Horarios to create a register on Table Rd0
@return variant, Return a Nil Value
/*/
Method   execWithArrayDataRd0(cCodeParticipant   As  Character ,   aDados  As  Array)    Class   UpdateTableOfParticipantsRd0
	Local    nCntFor :=  1   As  Numeric
	RD0->( dbSetOrder( 1 ) )
	RD0->( dbSeek( xFilial( "RD0" ) + cCodeParticipant ) )

	RecLock( "RD0", .F. )
	For nCntFor := 1 To Len( aDados )
		&( "RD0->" + aDados[nCntFor][1] ) := aDados[nCntFor][2]
	Next nCntFor
	RD0->( MsUnLock() )
Return  Nil
