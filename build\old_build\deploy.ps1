#In this first version the script will download only the specified build number. In a near future it must compare current build and last build.
#<PERSON><PERSON>t to download Protheus App from Nexus repo
#Je<PERSON>on Lemos - #10/03/2020


#Melhorias: 
# - criar funções pra notificar os passos
# - log
# - remover rpo's antigos

############
#VARIABLES #
############

$build_number = $args[0]

$url_package = "http://*************:8081/nexus/service/local/repositories/protheus-ti-v25-pre/content/br/totvs/ti/protheus_v25/bundle/win.top.bra.ti.apo-protheus_v25-pre/$build_number/win.top.bra.ti.apo-protheus_v25-pre-$build_number.zip"
$deploy_path= "D:\TOTVS\deploy\BR"
$rpo_path = "D:\TOTVS\apo_25"
$current= "D:\TOTVS\apo_25\current"
$current_target= "D:\TOTVS\apo_25\releases\BR"


############
#FUNCTIONS #
############

Function Write-Log {
    [CmdletBinding()]
    Param(
    [Parameter(Mandatory=$False)]
    [ValidateSet("INFO","WARN","ERROR","FATAL","DEBUG")]
    [String]
    $Level = "DEBUG",

    [Parameter(Mandatory=$True)]
    [string]
    $Message,

    [Parameter(Mandatory=$False)]
    [string]
    $logfile
    )

    $Stamp = (Get-Date).toString("yyyy/MM/dd HH:mm:ss")
    $Line = "$Stamp $Level $Message"
    If($logfile) {
        Add-Content $logfile -Value $Line
    }
    Else {
        Write-Output $Line
    }
}

function download {

#Baixa o pacote

$url=$args[0]
$output = $deploy_path
$start_time = Get-Date

Invoke-WebRequest -Uri $url -OutFile $output\$build_number.zip
Write-Output "Time taken: $((Get-Date).Subtract($start_time).Seconds) second(s)" 2>&1 >> $deploy_path\$build_number.log

}

function get_path {
    $get = Get-Item $current | Select-Object -ExpandProperty Target 
    $target = $get.Substring($get.get_Length()-4)
    Write-Host $target
    return $target

}

function update_shortcut {
    #criar if pra ve se existe
    Write-Host "Deletando current"
    Remove-Item $current -Force -Recurse
    Write-Host "Criando novo current. Target: $build_number"
    New-Item -ItemType Junction -Path $current -value $current_target\$build_number

}

############
#    RUN   #
############

#Baixa pacote
Write-output "Baixando pacote em  $deploy_path" >> $deploy_path\$build_number.log
Write-Output $url_package >> $deploy_path\$build_number.log
download $url_package 

#Cria diretório da release
if ((get_path) -eq "$build_number") {
     Write-output "Build igual ao atual. Parando script." >> $deploy_path\$build_number.log
     exit 1
 } else {
     Write-output "Cria nova pasta de release $build_number" >> $deploy_path\$build_number.log
     New-Item -Path $rpo_path\releases\BR -ItemType Directory -Name $build_number
}


#Descomprime pacote
Expand-Archive -Path $deploy_path\$build_number.zip -DestinationPath $current_target\$build_number

#Atualiza atalho
update_shortcut