#include 'protheus.ch'
#include 'parmtype.ch'

user function CMPRC001(cMsgErr)
Local cQuery 	:= " "
Local lRet		:= .F.
Local cNamProc	:= GetMv("CMS_PRCARC",,"CMS_CMP_ARCO1")
Local cTabZXO	:= RetSqlName("ZXO")
Local cTabZPX	:= RetSqlName("ZPX")
Local cTabZXA	:= RetSqlName("ZXA")
Local cTabZXC	:= RetSqlName("ZXC")
Local cTabZXE	:= RetSqlName("ZXE")
Local cTabSB1	:= RetSqlName("SB1")
Local cTabSBM	:= RetSqlName("SBM")


cQuery := " CREATE OR REPLACE PROCEDURE " + cNamProc + CRLF
cQuery += " ( " + CRLF
cQuery += " v_cRefer	IN varchar2," + CRLF 
cQuery += " v_cVend		IN varchar2," + CRLF
cQuery += " v_cCamp     IN " + cTabZXA + ".ZXA_CODCMP%TYPE," + CRLF
cQuery += " v_dDtGer	IN Date " + CRLF
cQuery += " )" + CRLF
cQuery += CRLF
cQuery += " IS" + CRLF
cQuery += CRLF
cQuery += "	v_nZXORECNO "+cTabZXO +".R_E_C_N_O_%TYPE;" + CRLF
cQuery += " v_nZPXRECNO "+cTabZPX+".R_E_C_N_O_%TYPE;" + CRLF
cQuery += "	v_cXCLIENT	"+cTabZXO+".ZXO_CLIENT%TYPE;" + CRLF
cQuery += "	v_cXLOJA	"+cTabZXO+".ZXO_LOJA%TYPE;" + CRLF
cQuery += "	v_cXDOC     "+cTabZXO+".ZXO_DOC%TYPE;" + CRLF
cQuery += "	v_cItem     "+cTabZXO+".ZXO_DOC%TYPE;" + CRLF
cQuery += " v_cXPROD    "+cTabZXO+".ZXO_PRODUT%TYPE;" + CRLF
cQuery += " v_cXCTA     "+cTabSBM+".BM_XPREVCO%TYPE;" + CRLF
cQuery += " v_cXOBS     "+cTabZPX+".ZPX_OBS%TYPE;" + CRLF
cQuery += CRLF

cQuery += "--CURSOR GERA REGISTROS E ATUALIZA VINCULO"+ CRLF
cQuery += CRLF 
cQuery += " CURSOR cr_ZPXREGS Is" + CRLF 
cQuery += CRLF
cQuery += " 	SELECT ZPX.ZPX_EMPORI, ZPX.ZPX_TIPAGN, SBM.BM_XPREVCO, ZPX.ZPX_VEND, ZPX.ZPX_CCUSTO, ZPX.ZPX_ITEMCT, ZPX.ZPX_CLVL, ZPX.ZPX_PCOM ,ZXE.ZXE_PCATU, SUM(ZPX.ZPX_COMISS) TOTALCMS FROM " + cTabZPX + " ZPX " + CRLF
cQuery += " 	INNER JOIN "+cTabZXE+" ZXE ON (ZXE.ZXE_FILIAL = '" + xFilial("ZXE") + "' AND ZXE.ZXE_CODCMP = v_cCamp AND ZXE.ZXE_GC = ZPX.ZPX_GC AND ZXE.ZXE_PCANT = ZPX.ZPX_PCOM AND ZXE.D_E_L_E_T_=' ')" + CRLF
cQuery += " 	INNER JOIN "+cTabZXC+" ZXC ON (ZXC.ZXC_FILIAL = '" + xFilial("ZXC") + "' AND ZXC.ZXC_CODCMP = v_cCamp AND ZXC.ZXC_AGN = ZPX.ZPX_TIPAGN AND ZXC.D_E_L_E_T_=' ')" + CRLF
cQuery += " 	INNER JOIN "+cTabSB1+" SB1 ON (SB1.B1_FILIAL = '" + xFilial("SB1") + "' AND SB1.B1_COD = ZPX.ZPX_CODPRO AND SB1.D_E_L_E_T_=' ')" + CRLF
cQuery += " 	INNER JOIN "+cTabSBM+" SBM ON (SBM.BM_FILIAL = '" + xFilial("SBM") + "' AND SBM.BM_GRUPO = SB1.B1_GRUPO AND SBM.D_E_L_E_T_=' ')" + CRLF
cQuery += " 	WHERE ZPX.ZPX_FILIAL = '" + xFilial("ZPX") + "'" + CRLF  
cQuery += " 		AND ZPX.ZPX_REFER = v_cRefer" + CRLF
cQuery += " 		AND ZPX.ZPX_VEND = v_cVend" + CRLF
cQuery += " 		AND ZPX.ZPX_STATUS='1'" + CRLF
cQuery += " 	  	AND ZPX.ZPX_VINCMP = 0" + CRLF
cQuery += "			AND ZPX.D_E_L_E_T_=' '" + CRLF
cQuery += "		GROUP BY ZPX.ZPX_EMPORI, ZPX.ZPX_TIPAGN, SBM.BM_XPREVCO,ZPX.ZPX_VEND,ZPX.ZPX_CCUSTO, ZPX.ZPX_ITEMCT, ZPX.ZPX_CLVL,ZPX.ZPX_PCOM,ZXE.ZXE_PCATU" + CRLF
cQuery += "      ORDER BY  SBM.BM_XPREVCO;" + CRLF
cQuery += CRLF	
cQuery += " v_crZPXREGS cr_ZPXREGS%ROWTYPE;" + CRLF
cQuery += CRLF
cQuery += CRLF  
cQuery += "--CURSOR PARA BUSCAR OS PRODUTOS DA CONTA CONTABIL DA LINHA QUE ESTA SENDO GERADA"+ CRLF  
cQuery += CRLF
cQuery += " CURSOR cr_ZPXPRDS (v_cCTABM IN "+cTabSBM+".BM_XPREVCO%TYPE )Is" + CRLF
cQuery += CRLF  
cQuery += "     SELECT SB1.B1_COD FROM "+cTabSB1+" SB1" + CRLF
cQuery += "           INNER JOIN "+cTabSBM+" SBM ON (SBM.BM_FILIAL = '" + xFilial("SBM") + "' AND SBM.BM_GRUPO= SB1.B1_GRUPO AND SBM.BM_XPREVCO= v_cCTABM AND SBM.D_E_L_E_T_=' ')" + CRLF
cQuery += "           WHERE SB1.B1_FILIAL = '" + xFilial("SB1") + "' " + CRLF 
cQuery += "           AND SB1.D_E_L_E_T_=' '" + CRLF
cQuery += "           AND ROWNUM = 1;" + CRLF
cQuery += CRLF       
cQuery += " v_crZPXPRDS cr_ZPXPRDS%ROWTYPE;" + CRLF
cQuery += CRLF
cQuery += CRLF	
cQuery += " BEGIN " + CRLF
cQuery += CRLF	
cQuery += "		SELECT  '99061' 		INTO v_cXCLIENT	FROM DUAL; " + CRLF 
cQuery += "		SELECT  '00'		  	INTO v_cXLOJA	FROM DUAL; " + CRLF
cQuery += CRLF 
cQuery += "		SELECT  ZXA.ZXA_NOMCMP  INTO v_cXOBS FROM "+cTabZXA+" ZXA " + CRLF 
cQuery += "			WHERE ZXA.ZXA_FILIAL = '" + xFilial("ZXA") + "' " + CRLF
cQuery += "		      AND ZXA.ZXA_CODCMP = v_cCamp; " + CRLF
cQuery += CRLF 
cQuery += "		v_cXCTA := '0';	" + CRLF
cQuery += "		v_cXPROD:= '99999999'; " + CRLF
cQuery += CRLF			
cQuery += "	OPEN cr_ZPXREGS;" + CRLF
cQuery += "		LOOP " + CRLF
cQuery += "		FETCH cr_ZPXREGS INTO v_crZPXREGS;" + CRLF
cQuery += "		EXIT WHEN cr_ZPXREGS%notfound;" + CRLF
cQuery += CRLF
cQuery += CRLF
cQuery += "--INCLUSAO DOS REGISTROS DA ZXO" + CRLF
cQuery += CRLF      
cQuery += "--SEQUENCIA" + CRLF
cQuery += " SELECT  'CM'||TO_CHAR(seq_cmp_zxodoc.nextval) INTO v_cXDOC		FROM DUAL;" + CRLF 
cQuery += CRLF      
cQuery += "--RECNO" + CRLF
cQuery += " SELECT MAX(R_E_C_N_O_)+1 INTO v_nZXORECNO FROM "+cTabZXO+";" + CRLF
cQuery += CRLF      
cQuery += "--PRODUTO DA CONTA" + CRLF
cQuery += CRLF
cQuery += "     IF v_cXCTA != v_crZPXREGS.BM_XPREVCO THEN " + CRLF      
cQuery += CRLF      
cQuery += "        OPEN cr_ZPXPRDS(v_crZPXREGS.BM_XPREVCO); " + CRLF
cQuery += "          FETCH cr_ZPXPRDS INTO v_cXPROD; " + CRLF
cQuery += "            IF cr_ZPXPRDS%notfound THEN " + CRLF
cQuery += "                v_cXPROD:= '99999999'; " + CRLF
cQuery += "            END IF; " + CRLF
cQuery += "        CLOSE cr_ZPXPRDS; " + CRLF
cQuery += CRLF     
cQuery += "        v_cXCTA := v_crZPXREGS.BM_XPREVCO; " + CRLF
cQuery += CRLF          
cQuery += "		END IF; " + CRLF
cQuery += CRLF
cQuery += CRLF      
cQuery += "		INSERT INTO "+cTabZXO+" COLUMNS (R_E_C_N_O_,ZXO_IDSEQ,ZXO_CLIENT,ZXO_LOJA,ZXO_DOC,ZXO_SERIE,ZXO_PRODUT,ZXO_VEND,ZXO_COMISS,ZXO_PCOM,ZXO_BASE,ZXO_REFER " + CRLF
cQuery += "										,ZXO_EMPFAT,ZXO_EMPORI,ZXO_GERADO,ZXO_CLVL,ZXO_ITEMCC,ZXO_CCUSTO, ZXO_STATUS, ZXO_DTGER,ZXO_ORIGEM,ZXO_OBS,ZXO_VENCTO,ZXO_EMISS, ZXO_CODCMP, ZXO_TIPAGN) " + CRLF
cQuery += "					VALUES (v_nZXORECNO,v_nZXORECNO,v_cXCLIENT,v_cXLOJA,v_cXDOC,'ARC',v_cXPROD,v_cVend,Round((v_crZPXREGS.TOTALCMS/(v_crZPXREGS.ZPX_PCOM/100))*(v_crZPXREGS.ZXE_PCATU/100),2),v_crZPXREGS.ZXE_PCATU,v_crZPXREGS.TOTALCMS/(v_crZPXREGS.ZPX_PCOM/100),v_cRefer " + CRLF
cQuery += "												,v_crZPXREGS.ZPX_EMPORI,v_crZPXREGS.ZPX_EMPORI,'X',v_crZPXREGS.ZPX_CLVL,v_crZPXREGS.ZPX_ITEMCT,v_crZPXREGS.ZPX_CCUSTO,'L',TO_CHAR(v_dDtGer,'YYYYMMDD'),'CAMPANHA',v_cXOBS,v_cRefer,v_cRefer,v_cCamp, v_crZPXREGS.ZPX_TIPAGN); " + CRLF
cQuery += CRLF
cQuery += CRLF
cQuery += "-- ATUALIZANDO CAMPO IDSEQ " + CRLF										
cQuery += "			UPDATE "+cTabZXO+" SET ZXO_IDSEQ = v_nZXORECNO " + CRLF
cQuery += "        		WHERE R_E_C_N_O_ = v_nZXORECNO ; " + CRLF
cQuery += CRLF
cQuery += CRLF
cQuery += "--INCLUSAO DOS REGISTROS DA ZPX " + CRLF
cQuery += CRLF
cQuery += "--RECNO " + CRLF
cQuery += "	SELECT MAX(R_E_C_N_O_)+1 INTO v_nZPXRECNO FROM "+cTabZPX+"; " + CRLF
cQuery += CRLF										
cQuery += "				INSERT INTO "+cTabZPX+" COLUMNS (R_E_C_N_O_,ZPX_IDSEQ,ZPX_CLIENT,ZPX_LOJA,ZPX_DOC,ZPX_SERIE,ZPX_CODPRO,ZPX_VEND,ZPX_COMISS,ZPX_PCOM,ZPX_BASE,ZPX_REFER " + CRLF
cQuery += "											,ZPX_EMPFAT,ZPX_EMPORI,ZPX_GERADO,ZPX_CLVL,ZPX_ITEMCT,ZPX_CCUSTO, ZPX_STATUS, ZPX_DTGER, ZPX_ORIGEM,ZPX_FILBX, ZPX_OBS, ZPX_VNCTIT, ZPX_EMISS, ZPX_BAIXA, ZPX_CODCMP) " + CRLF
cQuery += "				VALUES (v_nZPXRECNO,v_nZPXRECNO,v_cXCLIENT,v_cXLOJA,v_cXDOC,'ARC',v_cXPROD,v_cVend,Round((v_crZPXREGS.TOTALCMS/(v_crZPXREGS.ZPX_PCOM/100))*(v_crZPXREGS.ZXE_PCATU/100),2),v_crZPXREGS.ZXE_PCATU,v_crZPXREGS.TOTALCMS/(v_crZPXREGS.ZPX_PCOM/100),v_cRefer " + CRLF
cQuery += "											,v_crZPXREGS.ZPX_EMPORI,v_crZPXREGS.ZPX_EMPORI,'X',v_crZPXREGS.ZPX_CLVL,v_crZPXREGS.ZPX_ITEMCT,v_crZPXREGS.ZPX_CCUSTO,'1',TO_CHAR(v_dDtGer,'YYYYMMDD'),'CAMP','00001000100',v_cXOBS,v_cRefer,v_cRefer,v_cRefer,v_cCamp); " + CRLF
cQuery += CRLF
cQuery += CRLF
cQuery += "-- ATUALIZANDO CAMPO IDSEQ " + CRLF										
cQuery += "	UPDATE "+cTabZPX+" SET ZPX_IDSEQ = v_nZPXRECNO " + CRLF
cQuery += "     WHERE R_E_C_N_O_ = v_nZPXRECNO; " + CRLF
cQuery += CRLF
cQuery += CRLF     
cQuery += "--ATUALIZACAO DO VINCULO DA ZPX CRIADA COM OS REGISTROS DA ZPX ORIGEM " + CRLF                    
cQuery += "      UPDATE "+cTabZPX+" ZPX " + CRLF
cQuery += "        SET ZPX.ZPX_VINCMP =  v_nZPXRECNO, ZPX.ZPX_CODCMP = v_cCamp" + CRLF 
cQuery += "        WHERE ZPX.ZPX_FILIAL = '" + xFilial("ZPX") + "' " + CRLF
cQuery += "          AND ZPX.ZPX_REFER = v_cRefer " + CRLF
cQuery += "          AND ZPX.ZPX_VEND = v_cVend " + CRLF
cQuery += "          AND ZPX.ZPX_STATUS='1' " + CRLF
cQuery += "          AND ZPX.ZPX_VINCMP = 0 " + CRLF
cQuery += "          AND ZPX.ZPX_PCOM   = v_crZPXREGS.ZPX_PCOM " + CRLF
cQuery += "          AND ZPX.ZPX_CCUSTO = v_crZPXREGS.ZPX_CCUSTO " + CRLF
cQuery += "          AND ZPX.ZPX_ITEMCT = v_crZPXREGS.ZPX_ITEMCT " + CRLF
cQuery += "          AND ZPX.ZPX_CLVL   = v_crZPXREGS.ZPX_CLVL " + CRLF
cQuery += "          AND ZPX.ZPX_EMPORI = v_crZPXREGS.ZPX_EMPORI " + CRLF
cQuery += "          AND ZPX.ZPX_TIPAGN = v_crZPXREGS.ZPX_TIPAGN " + CRLF
cQuery += "          AND EXISTS( SELECT ZXE.ZXE_GC " + CRLF
cQuery += "                        FROM "+cTabZXE+" ZXE " + CRLF
cQuery += "                        WHERE ZXE.ZXE_FILIAL = '" + xFilial("ZXE") + "' " + CRLF
cQuery += "                          AND ZXE.ZXE_CODCMP = v_cCamp " + CRLF
cQuery += "                          AND ZXE.ZXE_GC = ZPX.ZPX_GC " + CRLF
cQuery += "                          AND ZXE.ZXE_PCANT = ZPX.ZPX_PCOM " + CRLF
cQuery += "                          AND ZXE.D_E_L_E_T_=' ') " + CRLF
cQuery += "		  AND EXISTS ( SELECT SBM.BM_GRUPO " + CRLF
cQuery += "						FROM "+cTabSBM+" SBM " + CRLF
cQuery += "						INNER JOIN "+cTabSB1+" SB1 ON (SB1.B1_FILIAL = '" + xFilial("SB1") + "' AND SB1.B1_COD = ZPX.ZPX_CODPRO AND SB1.D_E_L_E_T_=' ') " + CRLF
cQuery += "						WHERE SBM.BM_FILIAL = '" + xFilial("SBM") + "' " + CRLF
cQuery += "						AND SBM.BM_GRUPO = SB1.B1_GRUPO " + CRLF
cQuery += "						AND SBM.BM_XPREVCO = v_crZPXREGS.BM_XPREVCO " + CRLF
cQuery += "						AND SBM.D_E_L_E_T_=' ') " + CRLF
cQuery += "          AND ZPX.D_E_L_E_T_=' '; " + CRLF
cQuery += CRLF		
cQuery += CRLF			
cQuery += " 		COMMIT; " + CRLF	
cQuery += " 		END LOOP; " + CRLF
cQuery += " 	CLOSE cr_ZPXREGS; " + CRLF
cQuery += CRLF
cQuery += CRLF
cQuery += " EXCEPTION " + CRLF
cQuery += "   WHEN OTHERS THEN " + CRLF
cQuery += CRLF          
cQuery += "     dbms_output.put_line(SQLCODE); " + CRLF
cQuery += "     dbms_output.put_line(sqlerrm); " + CRLF
cQuery += CRLF     
cQuery += " END; "

If !TcSQLExec( cQuery ) == 0

	//If ValType( cMsgErr ) == "C"
		cMsgErr := TcSQLError() 
	//EndIf

	lRet := .F.

Else

	lRet := .T.

EndIf

Return lRet
