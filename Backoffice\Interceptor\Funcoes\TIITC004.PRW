#INCLUDE 'TOTVS.CH'
#INCLUDE "FWBROWSE.CH"
#INCLUDE "FWMVCDEF.CH"
#INCLUDE "TOPCONN.CH"

#DEFINE CRLF		Chr(13)+Chr(10)

#DEFINE THREAD_IDLE		'TIINTEXEC - ##IDLE'
#DEFINE POS_USER 		01
#DEFINE POS_PC	 		02
#DEFINE POS_IDTHREAD	03
#DEFINE POS_INSTRUC		09
#DEFINE POS_OBS 		11

/*/{Protheus.doc} TIITC004
    Funcoes Responsavel por retornar processar uma fila  distribuida no interceptor de acordo os  parametros recebidos
    @type  Function
    <AUTHOR> Menabue Lima
    @since 31/03/2023
    @version 1.0
    /*/
User Function TIITC004()
	Local aRequest  := PARAMIXB //Parametros recebidos do Interceptor  [1]= Codigo da Requisicao P37_COD [2]= Body Recebido da requisicao  P37_BODY
	Local oJson     := JsonObject():new()
	Local oBody     := oJson:FromJson( aRequest[2] )
	Local oIntercep := TIINTERCEPTOR():New(.T.)

	If ValType( oBody ) == "U"
		cFila   :=oJson:GetJsonObject( "fila" )
		cTipoRe :=oJson:GetJsonObject( "tipo" ) //1=Envio;2=Recebimento;3=Jobs
		oIntercep:PrepareDistributedQueues(cFila,cTipoRe)
	EndIF

Return '{"message":"FILA RECEBIDA E SERA EXECUTADA EM INSTANTES '+cFila+'" }'

