#INCLUDE "TOTVS.CH"


/*/{Protheus.doc} User Function TIRVJ000
    Job responsavel por buscar os dados do PSA e Calcular na P66
    @type  Function
    <AUTHOR> Menabue Lima
    @since 11/12/2023
    @version 12.23.10
    @parameters  type: number, description 0- Busca dados do PSA; 1= Grava dados na tabela P66
     /*/
User Function TIRVJ000(aParam)
Local lJob := .F.
Local nPar

	Default aParam := { 0, "00", "00001000100"}
	nPar := aParam[1]
		
	If Select("SM0") == 0
		lJob := .T.
		RpcSetEnv(aParam[2], aParam[3])
		RpcSetType(3)
	EndIF	
	if nPar <> 5
		U_TIRVPSA(nPar,1,,lJob)//Margem Esperada
		U_TIRVPSA(nPar,2,,lJob)//Produtividade
		U_TIRVPSA(nPar,3,,lJob)//Escopo
		U_TIRVPSA(nPar,4,,lJob)//Cubo
	Else
		U_TIRVARQ()// Geracao de Arquivos para actio
	EndIF
Return


/*/{Protheus.doc} User Function TIRVARQ
   Envio de arquivos Agrupados para a Actio
    @type  Function
    <AUTHOR> Menabue Lima
    @since 13/12/2023
    @version 12.1.2310
 
    /*/
User Function TIRVARQ()
	Local oActio:=TIRVACTIO():New()
	oActio:AgrupFiles()
	FREEOBJ( oActio )
Return

