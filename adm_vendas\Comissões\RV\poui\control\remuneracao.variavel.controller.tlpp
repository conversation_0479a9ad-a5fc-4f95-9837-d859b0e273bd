#include 'tlpp-core.th'
#include 'tlpp-rest.th'

namespace tdi.protheus.backoffice.remuneracao.variavel.controller
Using namespace tdi.protheus.backoffice.remuneracao.variavel.service

Class RemuneracaoVariavelController

	Data jResposta	as json
	Data Page       as Numeric
	Data PageSize   as Numeric
	Data Recno 		as Numeric
	Data Form 		as logical
	Data match      as Character
	Data table      as Character
	Data sort       as Character
	Data oService   as Object
	Data aFieldsApi as Array
	Data aUrlFilter as Array
	Private Data _service

	Public Method New() Constructor


	@Post("/v1/remuneracaovariavel/referenceaccount")
	Public Method Post()

	@Put("/v1/remuneracaovariavel/referenceaccount")
	Public Method Put()

	@Get("/v1/remuneracaovariavel/area")
	Public Method GetArea()

	@Get("/v1/remuneracaovariavel/area/metadata")
	Public Method GetMetadataArea()

	@Get("/v1/remuneracaovariavel/elegiveis")
	Public Method GetElegiveis()

	@Get("/v1/remuneracaovariavel/elegiveis/metadata")
	Public Method GetMetadataElegiveis()

	@Get("/v1/remuneracaovariavel/macroarea")
	Public Method GetMacroArea()

	@Get("/v1/remuneracaovariavel/macroarea/metadata")
	Public Method GetMetadataMacroAre()

	@Get("/v1/remuneracaovariavel/indicadoresmatricula")
	Public Method GetIndMatric()

	@Get("/v1/remuneracaovariavel/indicadoresmatricula/metadata")
	Public Method GetMetadataIndMatric()

	@Get("/v1/remuneracaovariavel/indicadores")
	Public Method GetIndicadores()

	@Get("/v1/remuneracaovariavel/indicadores/metadata")
	Public Method GetMetadataIndicadores()

	@Get("/v1/remuneracaovariavel/periodos")
	Public Method GetPeriodos()

	@Get("/v1/remuneracaovariavel/periodos/metadata")
	Public Method GetMetadataPeriodos()

	@Get("/v1/remuneracaovariavel/regras")
	Public Method GetRegras()
	@Get("/v1/remuneracaovariavel/regras/metadata")
	Public Method GetMetadataRegras()

	@Get("/v1/remuneracaovariavel/eventos")
	Public Method GetEventos()
	@Get("/v1/remuneracaovariavel/eventos/metadata")
	Public Method GetMetadataeventos()
	@Get("/v1/remuneracaovariavel/eventosbase")
	Public Method GetEventosBase()

	@Get("/v1/remuneracaovariavel/eventosbase/metadata")
	Public Method GetMetadataEvtBase()
	//Genericos
	@Get("/v1/tdi/generic/data")
	Public Method GetData()

	Method getPageAndPageSize(nPage, nPageSize,nRecno)
	Method getQueryParam(cParamName)
	Method GetFields(cTab)
EndClass

Method New() Class RemuneracaoVariavelController
	::page       := 1
	::pagesize   := 10
	::Recno		 := 0
	::Form		 := .F.
	::match      := ""
	::table      := ""
	::sort       := ""
	Self:_service:= RemuneracaoVariavelService():New()
	::jResposta  := JsonObject():New()
	::aFieldsApi := {}
	::aUrlFilter := {}
	U_RCVENV( "00",'00001000100')
return self


/*/{Protheus.doc} getPageAndPageSize
    Retorna o valor dos Parametros Page e PageSize.
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
*/
Method getPageAndPageSize(nPage, nPageSize,nRecno) Class RemuneracaoVariavelController
	If (oRest:getQueryRequest():GetJsonText('pageSize') != 'null')
		nPageSize := Val(oRest:getQueryRequest():GetJsonText('pageSize'))
	Else
		nPageSize := 10
	EndIf

	If (oRest:getQueryRequest():GetJsonText('page') != 'null')
		nPage := Val(oRest:getQueryRequest():GetJsonText('page'))
	Else
		nPage := 1
	Endif
	If (oRest:getQueryRequest():GetJsonText('id') != 'null')
		nRecno := Val(oRest:getQueryRequest():GetJsonText('id'))
	Else
		nRecno := 0
	Endif

	
Return Nil


/*/{Protheus.doc} getQueryParam
    Retorna o valor do Query Param.
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
/*/

Method getQueryParam(cParamName) Class RemuneracaoVariavelController
	Local cParamText As Character

	If (oRest:getQueryRequest():GetJsonText(cParamName) != 'null')
		cParamText := oRest:getQueryRequest():GetJsonText(cParamName)
	Else
		cParamText := ""
	EndIf
Return cParamText





Method Post() Class RemuneracaoVariavelController
	Local jBody := JsonObject():New()
	Local cBody := oRest:GetBodyRequest()

	cBodyContent := jBody:FromJson( cBody )

	If ( cBodyContent <> nil )
		oRest:setStatusCode ( 403 )
		oRest:setResponse( "NULL" + cBodyContent )
	Else
		jBody := Self:_service:Post( jBody )
		oRest:setStatusCode ( 200 )
	EndIf
	oRest:setResponse(jBody:ToJson())
return .T.

Method Put() Class RemuneracaoVariavelController
	Local jBody := JsonObject():New()
	Local cBody := oRest:GetBodyRequest()

	cBodyContent := jBody:FromJson( cBody )

	If ( cBodyContent <> nil )
		oRest:setStatusCode ( 403 )
		oRest:setResponse( "NULL" + cBodyContent )
	Else
		jBody := Self:_service:Put( jBody )
		oRest:setStatusCode ( 200 )
	EndIf
	oRest:setResponse(jBody:ToJson())

return .T.
/*/{Protheus.doc} User Function GetArea
    Funcao controller responsavel por retornar a Area
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
/*/
Method GetArea() Class RemuneracaoVariavelController
	Local jData as json
	::getPageAndPageSize(@::page, @::pagesize,@::Recno)
	jData := Self:_service:GetArea(::page,::pagesize,::Recno)

	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetElegiveis
    Funcao controller responsavel por retornar os elegiveis
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
/*/
Method GetElegiveis() Class RemuneracaoVariavelController
	Local jData as json
	Self:getPageAndPageSize(@::page, @::pagesize,@::Recno)
	jData := Self:_service:GetElegiveis(::page,::pagesize,::Recno)

	oRest:SetResponse(jData['payload'])
return .t.

/*/{Protheus.doc} User Function GetMetadataArea
    Funcao controller responsavel por retornar os  campos que serao utilizados no frontEnd
    @type  Method
    <AUTHOR> Menabue Lima
    @since 03/11/2023
    @version 12.1.2310
/*/
Method GetMetadataArea() Class RemuneracaoVariavelController

	jData := Self:GetFields("P79")
	jData['payload']['version'] :=1
	jData['payload']['title']	:='Cadastro de Area'
	//jData['payload']:toJson()
	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetMetadataArea
    Funcao controller responsavel por retornar os  campos que serao utilizados no frontEnd
    @type  Method
    <AUTHOR> Menabue Lima
    @since 03/11/2023
    @version 12.1.2310
/*/
Method GetMetadataElegiveis() Class RemuneracaoVariavelController

	jData := Self:GetFields("P64")
	jData['payload']['version']:=1
	jData['payload']['title']:='Cadastro de Elegiveis'
	//jData['payload']:toJson()
	oRest:SetResponse(jData['payload'])
return .t.

/*/{Protheus.doc} GetFields
Funcao generica 
	@type   Function
	<AUTHOR> Menabue lima
	@since 03/11/2023
	@version 12.1.2310
/*/
Method GetFields(cTab) Class RemuneracaoVariavelController
	If (oRest:getQueryRequest():GetJsonText('form') != 'null')
		Self:Form := Val(oRest:getQueryRequest():GetJsonText('form'))
	Else
		Self:Form := .F.
	Endif

Return   Self:_service:getFields(cTab,Self:Form)

/*/{Protheus.doc} User Function GetMacroArea
    Funcao controller responsavel por retornar os Macro Area
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
/*/
Method GetMacroArea() Class RemuneracaoVariavelController
	Local jData as json
	Self:getPageAndPageSize(@::page, @::pagesize,@::Recno)
	jData := Self:_service:GetMacroArea(::page,::pagesize,::Recno)

	oRest:SetResponse(jData['payload'])
return .t.

/*/{Protheus.doc} User Function GetMetadataMacroAre
    Funcao controller responsavel por retornar os  campos que serao utilizados no frontEnd
    @type  Method
    <AUTHOR> Menabue Lima
    @since 03/11/2023
    @version 12.1.2310
/*/
Method GetMetadataMacroAre() Class RemuneracaoVariavelController

	jData := Self:GetFields("P65")
	jData['payload']['version']:=1
	jData['payload']['title']:='Cadastro de Macro Area'
	//jData['payload']:toJson()
	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetMacroArea
    Funcao controller responsavel por retornar os Macro Area
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
/*/
Method GetIndMatric() Class RemuneracaoVariavelController
	Local jData as json
	Self:getPageAndPageSize(@::page, @::pagesize,@::Recno)
	jData := Self:_service:GetIndMatric(::page,::pagesize,::Recno)

	oRest:SetResponse(jData['payload'])
return .t.

/*/{Protheus.doc} User Function GetMetadataMacroAre
    Funcao controller responsavel por retornar os  campos que serao utilizados no frontEnd
    @type  Method
    <AUTHOR> Menabue Lima
    @since 03/11/2023
    @version 12.1.2310
/*/
Method GetMetadataIndMatric() Class RemuneracaoVariavelController

	jData := Self:GetFields("P81")
	jData['payload']['version']:=1
	jData['payload']['title']:='Indicadores por Matricula'
	//jData['payload']:toJson()
	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetIndicadores
    Funcao controller responsavel por retornar os Indicadores
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
/*/
Method GetIndicadores() Class RemuneracaoVariavelController
	Local jData as json
	Self:getPageAndPageSize(@::page, @::pagesize,@::Recno)
	jData := Self:_service:GetIndicadores(::page,::pagesize,::Recno)

	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetMetadataIndcadores
    Funcao controller responsavel por retornar os  campos que serao utilizados no frontEnd
    @type  Method
    <AUTHOR> Menabue Lima
    @since 03/11/2023
    @version 12.1.2310
/*/
Method GetMetadataIndicadores() Class RemuneracaoVariavelController

	jData := Self:GetFields("P63")
	jData['payload']['version']:=1
	jData['payload']['title']:='Cadastro de Indicadores'
	//jData['payload']:toJson()
	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetIndicadores
    Funcao controller responsavel por retornar os Indicadores
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
/*/
Method GetPeriodos() Class RemuneracaoVariavelController
	Local jData as json
	Self:getPageAndPageSize(@::page, @::pagesize,@::Recno)
	jData := Self:_service:GetPeriodos(::page,::pagesize,::Recno)

	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetMetadataIndcadores
    Funcao controller responsavel por retornar os  campos que serao utilizados no frontEnd
    @type  Method
    <AUTHOR> Menabue Lima
    @since 03/11/2023
    @version 12.1.2310
/*/
Method GetMetadataPeriodos() Class RemuneracaoVariavelController

	jData := Self:GetFields("P77")
	jData['payload']['version']:=1
	jData['payload']['title']:='Cadastro de Periodos'
	//jData['payload']:toJson()
	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetRegras
    Funcao controller responsavel por retornar os Regras
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
/*/
Method GetRegras() Class RemuneracaoVariavelController
	Local jData as json
	Self:getPageAndPageSize(@::page, @::pagesize,@::Recno)
	jData := Self:_service:GetRegras(::page,::pagesize,::Recno)

	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetMetadataIndcadores
    Funcao controller responsavel por retornar os  campos que serao utilizados no frontEnd
    @type  Method
    <AUTHOR> Menabue Lima
    @since 03/11/2023
    @version 12.1.2310
/*/
Method GetMetadataRegras() Class RemuneracaoVariavelController

	jData := Self:GetFields("P80")
	jData['payload']['version']:=1
	jData['payload']['title']:='Cadastro de Regras'
	//jData['payload']:toJson()
	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetEventos
    Funcao controller responsavel por retornar os eventos de provisao
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
/*/
Method GetEventos() Class RemuneracaoVariavelController
	Local jData as json
	Self:getPageAndPageSize(@::page, @::pagesize,@::Recno)

	jData := Self:_service:GetEventos(::page,::pagesize,::recno)

	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetMetadataEventos
    Funcao controller responsavel por retornar os  campos que serao utilizados no frontEnd
    @type  Method
    <AUTHOR> Menabue Lima
    @since 03/11/2023
    @version 12.1.2310
/*/
Method GetMetadataEventos() Class RemuneracaoVariavelController
	Local jAux := JsonObject():New()
	Local aAux := {}
	jData := Self:GetFields("P84")
	jData['payload']['version']:=1
	jData['payload']['title']:='Cadastro de Eventos Provisao'
	jData['payload']['autoRouter'] := .T.
	jAux['label']:="Home"
	jAux['link']:="/"
	//BreadCumbs
	aAdd(aAux,jAux)
	jAux['label']:=jData['payload']['title']
	aAdd(aAux,jAux)
	//jData['payload']['breadcrumb']:= JsonObject():New()
	//jData['payload']['breadcrumb']['items'] := aClone(aAux)
	//Actions
	//jAux := JsonObject():New()
	//jAux['detail']		:="detail/:id"
	//jAux['duplicate']	:="new"
	//jAux['edit']		:="edit/:id"
	//jAux['remove']		:=.T.
	//jAux['removeAll']	:= .T.
	//jData['payload']['actions']:= jAux


	//jData['payload']:toJson()
	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetEventosBase
    Funcao controller responsavel por retornar os eventos base de calcuo provisao
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
/*/
Method GetEventosBase() Class RemuneracaoVariavelController
	Local jData as json
	Self:getPageAndPageSize(@::page, @::pagesize,@::Recno)
	jData := Self:_service:GetEventosBase(::page,::pagesize,::Recno)

	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetMetadataEvtBase
    Funcao controller responsavel por retornar os  campos que serao utilizados no frontEnd
    @type  Method
    <AUTHOR> Menabue Lima
    @since 03/11/2023
    @version 12.1.2310
/*/
Method GetMetadataEvtBase() Class RemuneracaoVariavelController
	Local jAux := JsonObject():New()
	Local aAux := {}
	jData := Self:GetFields("P86")
	jData['payload']['version']:=1
	jData['payload']['title']:='Cadastro de Eventos Provisao Base Calculo'
	jData['payload']['autoRouter'] := .T.
	jAux['label']:="Home"
	jAux['link']:="/"
	//BreadCumbs
	aAdd(aAux,jAux)
	jAux['label']:=jData['payload']['title']
	aAdd(aAux,jAux)
	//jData['payload']['breadcrumb']:= JsonObject():New()
	//jData['payload']['breadcrumb']['items'] := aClone(aAux)
	//Actions
	//jAux := JsonObject():New()
	//jAux['detail']		:="detail/:id"
	//jAux['duplicate']	:="new"
	//jAux['edit']		:="edit/:id"
	//jAux['remove']		:=.T.
	//jAux['removeAll']	:= .T.
	//jData['payload']['actions']:= jAux

	//jData['payload']:toJson()
	oRest:SetResponse(jData['payload'])
return .t.
/*/{Protheus.doc} User Function GetData
    Funcao controller responsavel por retornar os dados de uma determinada tabela
    @type  Method
    <AUTHOR> Menabue Lima
    @since 01/11/2023
    @version 12.1.2310
/*/
Method GetData() Class RemuneracaoVariavelController
	Local jData as json
	Local cTable as Character
	Self:getPageAndPageSize(@::page, @::pagesize,@::Recno)
	If (oRest:getQueryRequest():GetJsonText('alias') != 'null')
		cTable := oRest:getQueryRequest():GetJsonText('alias')
	Else
		cTable := ""
	Endif
	
	jData := Self:_service:GetData(cTable,::page,::pagesize,::recno)

	oRest:SetResponse(jData['payload'])
return .t.
