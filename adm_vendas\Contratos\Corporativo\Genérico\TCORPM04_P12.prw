#Include "Protheus.ch"
#Include "RwMake.ch"

User Function TCORPM04()

	Local _cQuery := ""
	
	Local _aDados := {}
	
	Local _nCount := 0
	Local lAjustRot := GetMv("TI_RV2EMP",,.T.) 

	_cQuery := " SELECT " + CRLF
	_cQuery += "	 ZTS.ZTS_CODCLI " + CRLF
	_cQuery += "	,ZTS.ZTS_LOJA " + CRLF
	_cQuery += "	,CN9.CN9_NUMERO " + CRLF
	_cQuery += "	,CN9.CN9_REVISA " + CRLF
	_cQuery += " FROM " + RetSqlName("CN9") + " CN9 " + CRLF
	_cQuery += CRLF
	_cQuery += " INNER JOIN " + RetSqlName("CNC") + " CNC ON " + CRLF
	_cQuery += "	CNC.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND CNC.CNC_FILIAL = '" + xFilial("CNC") + "' " + CRLF
	_cQuery += "	AND CNC.CNC_NUMERO = CN9.CN9_NUMERO " + CRLF
	_cQuery += "	AND CNC.CNC_REVISA = CN9.CN9_REVISA " + CRLF
	_cQuery += "	AND CNC.CNC_TIPCLI = '01' " + CRLF
	_cQuery += CRLF
	_cQuery += " INNER JOIN " + RetSqlName("ZTS") + " ZTS ON " + CRLF
	_cQuery += "	ZTS.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZTS.ZTS_FILIAL = '" + xFilial("ZTS") + "' " + CRLF
	_cQuery += "	AND ZTS.ZTS_CODCLI = CNC.CNC_CLIENT " + CRLF
	_cQuery += "	AND ZTS.ZTS_LOJA = CNC.CNC_LOJACL " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	CN9.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND CN9.CN9_FILIAL = '" + xFilial("CN9") + "' " + CRLF
	_cQuery += "	AND CN9.CN9_SITUAC = '05' " + CRLF
	
	_aDados := U_GV001X23(_cQuery)
	
	If !Empty(_aDados)
		For _nCount := 1 To Len(_aDados)
	
			If lAjustRot .And. empty(_aDados[_nCount][4])
				_aDados[_nCount][4] := U_TiGetContr(_aDados[_nCount][3])
			EndIf

			MsgRun("Alterando ZAX - "+_aDados[_nCount][1]+"/"+_aDados[_nCount][2],,{|| _fGravaZAX(_aDados[_nCount][1],_aDados[_nCount][2],_aDados[_nCount][3],_aDados[_nCount][4]) })
			MsgRun("Alterando ZAL - "+_aDados[_nCount][1]+"/"+_aDados[_nCount][2],,{|| _fGravaZAL(_aDados[_nCount][1],_aDados[_nCount][2],_aDados[_nCount][3],_aDados[_nCount][4]) })
			MsgRun("Alterando ZAW - "+_aDados[_nCount][1]+"/"+_aDados[_nCount][2],,{|| _fGravaZAW(_aDados[_nCount][1],_aDados[_nCount][2],_aDados[_nCount][3],_aDados[_nCount][4]) })
		Next
	EndIf
	
Return

Static Function _fGravaZAX(_cCliente,_cLoja,_cContrato,_cRevisa)

	Local _cQuery := ""

	Local _aDados2 := {}
	
	Local _nCount2 := 0

	_cQuery := " SELECT " + CRLF
	_cQuery += "	ZAX.R_E_C_N_O_ " + CRLF
	_cQuery += " FROM " + RetSqlName("ZAX") + " ZAX " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	ZAX.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAX.ZAX_FILIAL = '" + xFilial("ZAX") + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND ZAX.ZAX_LOJA = '" + _cLoja + "' " + CRLF
	
	_aDados2 := U_GV001X23(_cQuery)

	If !Empty(_aDados2)
		For _nCount2 := 1 To Len(_aDados2)
			ZAX->(DbGoTo(_aDados2[_nCount2][1]))
			
			If ZAX->(!Eof())
				RecLock("ZAX",.F.)
					ZAX->ZAX_CONTRA := _cContrato
					ZAX->ZAX_REVISA := _cRevisa
				ZAX->(MsUnLock())
			EndIf
		Next
	EndIf
	
Return

Static Function _fGravaZAL(_cCliente,_cLoja,_cContrato,_cRevisa)

	Local _cQuery := ""

	Local _aDados3 := {}
	
	Local _nCount3 := 0
	
	_cQuery := " SELECT " + CRLF
	_cQuery += "	ZAL.R_E_C_N_O_ " + CRLF
	_cQuery += " FROM " + RetSqlName("ZAL") + " ZAL " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	ZAL.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAL.ZAL_FILIAL = '" + xFilial("ZAL") + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND ZAL.ZAL_LOJA = '" + _cLoja + "' " + CRLF
	
	_aDados3 := U_GV001X23(_cQuery)

	If !Empty(_aDados3)
		For _nCount3 := 1 To Len(_aDados3)
			ZAL->(DbGoTo(_aDados3[_nCount3][1]))
			
			If ZAL->(!Eof())
				RecLock("ZAL",.F.)
					ZAL->ZAL_CONTRA := _cContrato
					ZAL->ZAL_REVISA := _cRevisa
				ZAW->(MsUnLock())
			EndIf
		Next
	EndIf

Return

Static Function _fGravaZAW(_cCliente,_cLoja,_cContrato,_cRevisa)

	Local _cQuery := ""

	Local _aDados4 := {}
	
	Local _nCount4 := 0

	_cQuery := " SELECT " + CRLF
	_cQuery += "	ZAW.R_E_C_N_O_ " + CRLF
	_cQuery += " FROM " + RetSqlName("ZAW") + " ZAW " + CRLF
	_cQuery += CRLF
	_cQuery += " WHERE " + CRLF
	_cQuery += "	ZAW.D_E_L_E_T_ = ' ' " + CRLF
	_cQuery += "	AND ZAW.ZAW_FILIAL = '" + xFilial("ZAW") + "' " + CRLF
	_cQuery += "	AND ZAW.ZAW_CLIENT = '" + _cCliente + "' " + CRLF
	_cQuery += "	AND ZAW.ZAW_LOJA = '" + _cLoja + "' " + CRLF
	
	_aDados4 := U_GV001X23(_cQuery)

	If !Empty(_aDados4)
		For _nCount4 := 1 To Len(_aDados4)
			ZAW->(DbGoTo(_aDados4[_nCount4][1]))
			
			If ZAW->(!Eof())
				RecLock("ZAW",.F.)
					ZAW->ZAW_CONTRA := _cContrato
					ZAW->ZAW_REVISA := _cRevisa
				ZAW->(MsUnLock())
			EndIf
		Next
	EndIf

Return
