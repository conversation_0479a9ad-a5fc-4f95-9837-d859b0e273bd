|![Logo](https://i.ibb.co/JFfd8bV/Ico.jpg) | Aplicação de patchs |
| --- | ---: |

## Resumo
O diretório ./path/applyoldprogram, contém os patchs que serão aplicados, respeitando as seguintes regras:
 - A ordem da aplicação ocorre em ordem alfabética crescente;
 - A compilação aplica apenas os fontes com data mais recente aos fontes já existentes no RPO.
 
## Técnico
A aplicação dos patchs é executada um arquivo de cada vez, através do exemplo de cmdsabaixo:
```bash
appserver.exe -compile -applypatch -files=patch.ptm -env=environment -applyoldprogram
```
## Observações
**Não pode ser excluído o arquivo ./path/applyoldprogram/README.MD**, pois o diretório ficará vazio e o mesmo não existirá no repositório após o push
