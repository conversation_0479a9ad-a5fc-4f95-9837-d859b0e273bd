#include "tlpp-core.th"
#Include "Totvs.Ch"
#Include "TOPCONN.CH"

Class   UpdateRegisterToControlCheckout
    Private     Data    oUpdateStatusRegisterTableDTO                       As  Object
    Private     Data    aResult                                             As  Array
    
    Public      Method  new()                                               As  Object
    Public      Method  setObject(oValue As  Object)                        As  Object  
    Public      Method  execute()                                           As  Object
    Public      Method  getStatus()                                         As  Array
    Private     Method  seekRegisterOnTable()                               As  Object
    

EndClass


Method  new()   As  Object  Class   UpdateRegisterToControlCheckout
    ::oUpdateStatusRegisterTableDTO     :=  Nil
    ::aResult                           :=      {.F., ''}
Return  Self

Method  getStatus()  As  Array  Class   UpdateRegisterToControlCheckout
Return  ::aResult


Method  setObject(oValue    As  Object) As  Object  Class   UpdateRegisterToControlCheckout
    ::oUpdateStatusRegisterTableDTO   :=  oValue
Return  Self



Method  execute()   As  Object  Class   UpdateRegisterToControlCheckout
    ::seekRegisterOnTable()
Return  Self



Method  seekRegisterOnTable()   As  Object  Class   UpdateRegisterToControlCheckout
    Local   dDate    :=  Date()      As  Date
    Local   cTime    :=  Time()      As  Character
    Local   cCheck   := ::oUpdateStatusRegisterTableDTO:checkout
    Local   cQuery   :=""
    Local   _cAlias := GetNextAlias()

cQuery := "SELECT R_E_C_N_O_ NREG  FROM " + RetSQLName("PXG") + " WHERE PXG_CKCODE='"+cCheck+"' AND D_E_L_E_T_ = ' ' "

cQuery := ChangeQuery(cQuery)
TCQUERY cQuery NEW ALIAS (_cAlias)

If !(_cAlias)->(EOF())
	PXG->(DbGoto((_cAlias)->NREG))
    PXG->(RecLock("PXG",.F.))
            //PXG->PXG_CODIGO     :=  GetSxeNum("PXG","PXG_CODIGO")
            PXG->PXG_DATALT     :=  dDate 
            PXG->PXG_HORALT     :=  cTime
            PXG->PXG_STOKEN     :=  '5'//cValToChar(::oUpdateStatusRegisterTableDTO:status)
            //ConfirmSX8()
     PXG->(MsUnLock())
    ::aResult   :=  {'Sucesso na atualizacao do Registro na tabela PXG' , ''}

    (_cAlias)->(DbCloseArea())

Else

     ::aResult   :=  {'' , 'Falha na atualizacao do Registro na Tabela PXG, registro nao encontrado'} 

EndIf

/*
    dbSelectArea("PXG")
    dbSetOrder(2)
    If !PXG->(dbSeek(xFilial("PXG")+cCheck))
        ::aResult   :=  {'' , 'Falha na atualizacao do Registro na Tabela PXG, registro nao encontrado'} 
    Else
        PXG->(RecLock("PXG",.F.))
            PXG->PXG_CODIGO     :=  GetSxeNum("PXG","PXG_CODIGO")
            PXG->PXG_DATALT     :=  dDate 
            PXG->PXG_HORALT     :=  cTime
            PXG->PXG_STOKEN     :=  '5'//cValToChar(::oUpdateStatusRegisterTableDTO:status)
        PXG->(MsUnLock())
        ::aResult   :=  {'Sucesso na atualizacao do Registro na tabela PXG' , ''}
    EndIf
*/
Return  Self
