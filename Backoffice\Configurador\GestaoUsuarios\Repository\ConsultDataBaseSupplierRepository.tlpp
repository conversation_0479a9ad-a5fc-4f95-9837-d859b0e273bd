#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Repository.ConsultDataBaseSupplierRepository


/*/{Protheus.doc} ConsultDataBaseSupplier
Class that aims to consult the database of suppliers
@type class
@version  1.0
<AUTHOR>
@since 10/30/2024
/*/
Class   ConsultDataBaseSupplier
    private data    aAreaSA2            As  Array
    private data    oJsonSa2            As  Json
    private data    cCodeSupplier       As  Character
    private data    cStoreSupplier      As  Character
    private data    nOperacao           As  Numeric

    public method new(oJson     As  Json)
    public method finish()
    public method searchSupplier() 
    public method getOperation()        As  Numeric
    public method getCodeSupplier()     As  Character
    public method getStoreSupplier()    As  Character 
EndClass

/*/{Protheus.doc} ConsultDataBaseSupplier::new
Method that aims to create a new instance of the class
@type method
@version  1.0
<AUTHOR>
@since 10/30/2024
@param oJson, json, These parameter is a Json Object of the Table SA2 formed using the JSON String
@return Object, Return the instantiated object itself
/*/
Method  new(oJson   As  Json)     Class   ConsultDataBaseSupplier
    ::aAreaSA2  :=  SA2->( GetArea() )
    ::oJsonSa2  :=  oJson   
Return Self

/*/{Protheus.doc} ConsultDataBaseSupplier::finish
This method apply an command RestArea on DataBase
@type method
@version  1.0
<AUTHOR>
@since 10/30/2024
@return Object, Return the instantiated object itself
/*/
Method  finish()    Class   ConsultDataBaseSupplier
    RestArea( ::aAreaSA2 )
Return Self

Method  searchSupplier()   Class   ConsultDataBaseSupplier
    Local cDoc  :=  ::oJsonSa2:GetJsonObject( "A2_CGC" ) As Character
    SA2->( dbSetOrder( 3 ) )
    If ! SA2->( dbSeek( xFilial( "SA2" ) + cDoc ) )
        ::nOperacao         := 3
        ::cCodeSupplier     := ''
        ::cStoreSupplier    := ''
    Else 
        ::nOperacao         := 4
        ::cCodeSupplier     := SA2->A2_COD
        ::cStoreSupplier    := SA2->A2_LOJA
    Endif
Return Self

/*/{Protheus.doc} ConsultDataBaseSupplier::getOperation
This method return the operation inclusion or alteration
@type method
@version  1.0
<AUTHOR>
@since 10/30/2024
@return numeric, Return 3 for an insertion into the SA2 table, or 4 for an update
/*/
Method  getOperation()      As  Numeric     Class   ConsultDataBaseSupplier
Return ::nOperacao

/*/{Protheus.doc} ConsultDataBaseSupplier::getCodeSupplier
When exist a Supplier on table SA2 this method can be used to recovery the value of the A2_COD(Code Supplier)
@type method
@version  1.0
<AUTHOR>
@since 10/30/2024
@return character, Return the Code of Supplier on SA2 Table
/*/
Method  getCodeSupplier()   As  Character   Class   ConsultDataBaseSupplier
Return ::cCodeSupplier

/*/{Protheus.doc} ConsultDataBaseSupplier::getStoreSupplier
When exist a Supplier on table SA2 this method can be used to recovery the value of the A2_LOJA(Store Supplier)
@type method
@version  1.0
<AUTHOR>
@since 10/30/2024
@return character, return the Store of Supplier on SA2 Table
/*/
Method  getStoreSupplier()  As  Character   Class   ConsultDataBaseSupplier
Return ::cStoreSupplier


