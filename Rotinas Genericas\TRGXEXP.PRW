#include 'protheus.ch'

/*/{Protheus.doc} ADZ_DT1V
Rotina para copia do campo ADZ_DT1VEN na copia de propostas 
<AUTHOR>
@since 05/05/2016
@version undefined
@return cRet, Competencia
@type function
/*/
User Function ADZ_DT1V

Local aArea			:= GetArea()
Local dVenc			:= MonthSum(dDataBase, 1)
Local cProduto		:= ADZ->ADZ_PRODUT
Local cCond			:= ADZ->ADZ_CONDPG
Local cTabela		:= ADY->ADY_TABELA
Local cDDD			:= ""
Local dParam		:= CtoD("")
Local cRet			:= ""
Local cAgrMigInt	:= GetMV("TI_MIGINT",,"0169")
Local cProposta		:= ADY->ADY_PROPOS
Local llCloud		:= .F.
Local aPrdSaaSBT 	:= U_TC008BT()
Local cCondBT		:= GetMv('TI_CONDBT',,'FB3')
local cGrpSaasVt	:= GetMv('TI_GRPSAVT',,"")


DbSelectArea("PIC")
PIC->(dbSetOrder(3))
If PIC->(dbSeek( xFilial("PIC") + cProposta ))
	llCloud := .T.
EndIF
If Empty(dVenc)
	dVenc := dDataBase
EndIf
	
dbSelectArea("SE4")
dbSetOrder(1)
If SE4->(DbSeek(xFilial("SE4")+cCond))
	cDDD :=  SE4->E4_DDD
EndIf

//Validações para produtos do tipo SAAS
dbselectArea("SB1")
dbSetOrder(1)
If Dbseek(XFilial("SB1")+cProduto)

	dbSelectArea("SBM")
	dbSetOrder(1)

	If Dbseek(xFilial("SBM")+SB1->B1_GRUPO)
		If SBM->BM_XGRPREC == '1'
			If !Empty(cDDD) .and. cDDD == 'F' .and. !llCloud 
				dVenc	:= MonthSub( dVenc , 1 ) //subtrai 1 mês			
			EndIf
			
			//Tratativa para produtos Voucher VTex
			If SBM->BM_GRUPO $ cGrpSaasVt
				dVenc := dDataBase
			//TICRM-508: Permitir condição de pagamento especial para produtos Boticário - luiz.santana, 28/05/2018
			ElseIf cCond $ cCondBT .AND. aScan(aPrdSaaSBT,cTabela+AllTrim(cProduto)) > 0
				dVenc := dDataBase
			EndIf
		EndIf	
	EndIf
		
EndIf

cRet 	:= U_TGetCmpAtu( 1, dVenc )
	
RestArea(aArea)
	
Return(cRet)

/*/{Protheus.doc} TCADYCPY
Chamada da consulta personalizada ADYCPY pelo menu
@since 02/02/2011
@version undefined
@type function
/*/

User Function TCADYCPY

	U_TRGC020("ADYCPY")

Return