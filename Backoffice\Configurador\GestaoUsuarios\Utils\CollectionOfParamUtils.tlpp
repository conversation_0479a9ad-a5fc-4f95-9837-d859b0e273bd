#include "tlpp-core.th"

namespace Backoffice.Configurador.Gestao_Usuarios.Utils.CollectionOfParamUtils

/*/{Protheus.doc} CollectionOfParam
This class aims to gather all of the necessary parameters for use in this process
@type class
@version  1.0
<AUTHOR>
@since 10/31/2024
/*/
Class   CollectionOfParam
    Private Data    cConta                      As  Character
    Private Data    cArqHTML                    As  Character
    Private Data    cArqPath                    As  Character
    Private Data    cPathHTML                   As  Character
    Private Data    lSSOObr                     As  Logical
    Public  Method  new()                       Constructor
    Public  Method  getBankAccountDefault()     As  Character
    Public  Method  getPathHtmlFileOnServer()   As  Character
    Public  Method  getAddressFromTheWeb()      As  Character
    Public  Method  getPathHtml()               As  Character
    Public  Method  getUseSso()                 As  Logical
EndClass

/*/{Protheus.doc} CollectionOfParam::new
This method aims to initialize all necessary parameters
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return object, Return the instantiated object itself
/*/
Method  new()   Class   CollectionOfParam
    ::cConta    := SuperGetMv( "TI_CNTA2ID", Nil, "************" )
    ::cArqHTML  := "\workflow\comunicado_areas_sucesso_do_cliente_PT.html"
    ::cArqPath  := Supergetmv("TI_PTIMGI",.F.,"https://f.hubspotusercontent40.net/hubfs/2287241/EMKT%20-%20Templates%20-%20Exact%20target/banner-totvs.jpg")
    ::cPathHTML := GetMV("MV_WFDIR")
    ::lSSOObr   := SuperGetMv('TI_SSOOBR', , .F.)
Return Self

/*/{Protheus.doc} CollectionOfParam::getBankAccountDefault
This method aims to get the value of the property cConta
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return character, Return the property cConta
/*/
Method  getBankAccountDefault()     Class   CollectionOfParam
Return ::cConta


/*/{Protheus.doc} CollectionOfParam::getPathHtmlFileOnServer
This method aims to get the property cArqHTML of the Class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Character, Return the property cArqHTML
/*/
Method  getPathHtmlFileOnServer()   Class   CollectionOfParam
Return  ::cArqHTML

/*/{Protheus.doc} CollectionOfParam::getAddressFromTheWeb
This method aims to get the property cArqPath from the Class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Character, Return the property cArqPath
/*/
Method  getAddressFromTheWeb()  Class   CollectionOfParam
Return  ::cArqPath

/*/{Protheus.doc} CollectionOfParam::getPathHtml
This method aims to return the property cPathHTMl of the Class
@type method
@version  1.0
<AUTHOR>
@since 10/31/2024
@return Character, The property cPathHTML
/*/
Method  getPathHtml()   Class   CollectionOfParam
Return  ::cPathHTML


/*/{Protheus.doc} CollectionOfParam::getUseSso
This method return the param that indicates if
the SSO on Protheus is active.
@type method
@version  1.0
<AUTHOR>
@since 11/27/2024
@return logical, The value of the param TI_SSOOBR on SX6
/*/
Method  getUseSso() As  Logical Class   CollectionOfParam
Return  ::lSSOObr
