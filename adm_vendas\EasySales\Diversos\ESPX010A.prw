#INCLUDE 'PROTHEUS.CH'
#INCLUDE 'APWEBSRV.CH'
#INCLUDE 'FILEIO.CH'  
#INCLUDE 'ESPX010A.CH'

/* 
Valida token
*/

User Function ESPX010A(cTokenExt)

    Local lValido := .F.

    cAlias := GetNextAlias()

    If !Empty(cTokenExt)
        BeginSQL Alias cAlias
            SELECT PV7.PV7_TOKEN,
                PV7.PV7_PROPOS
            FROM %Table:PV7 % PV7
            WHERE PV7.PV7_FILIAL = %XFilial:PV7%
            AND PV7.PV7_TOKEN  = %Exp:cTokenExt%
            AND PV7.%NotDel%
        EndSQL

        If (cAlias)->(!EoF())
            lValido := .T.
        End

        (cAlias)->(DbCloseArea())
    EndIf


Return lValido

