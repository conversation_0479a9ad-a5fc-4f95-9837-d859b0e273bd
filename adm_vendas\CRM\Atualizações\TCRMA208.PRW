#INCLUDE 'TOTVS.CH'
#INCLUDE 'TCRMA208.ch'

/*/{Protheus.doc} TCRMA208
	Simulador de Acrescimo CRM - HTML
	@type  Function
	<AUTHOR>
	@since 02/05/2022
	@version 1.0
/*/
User Function TCRMA208(cProduto, nValor, cCondPag)

	Local aParcs 		:= {0, 0, 0, 0, {}} //Por default vem com uma parcela e nenhum Acrescimo

	Default nValor		:= 0
	Default cCondPag	:= ''

	Private cNomeProd	:= cProduto
	Private nValorOri	:= nValor

	If Empty(nValor)

		Help(" ",1,STR0011,,STR0012,3,1)
		Return

	ElseIf !Empty(cCondPag)

		aParcs := aClone(U_TC207Par(nValor, 0, cCondPag))

		If aParcs[1] == 0
			Help(" ",1,STR0013,,STR0014,3,1)
			Return
		EndIf

	Else

		aParcs 	:= {1, 0, 0, nValor, {nValor}}

	EndIf

	Simulador(aParcs)

Return

/*/{Protheus.doc} Simulador
	Di?logo do Simulador de Acrescimo CRM - HTML
	@type  Function
	<AUTHOR>
	@since 02/05/2022
	@version 1.0
	/*/
Static Function Simulador(aParcs)

	Local oDlg

	Default nQtdParc	:= 0

	Private oSimAcrescimo, oDialog
	Private aParcelas := aClone(aParcs)

	oSimAcrescimo := TWebFormBase():New()

	oDialog := FWDialogModal():New('TCRMA208')
	oDialog:SetTitle( STR0008 + " - " + STR0009 )
	oDialog:SetSubTitle(STR0010)
	oDialog:SetBackGround( .T. )
	oDialog:SetEscClose( .T. )
	oDialog:SetSize( 300, 500 )
	oDialog:EnableFormBar( .F. )
	oDialog:SetCloseButton(.T.)
	oDialog:CreateDialog()

	oDlg := oDialog:GetPanelMain()

	oSimAcrescimo:cBtnConfirm := "Ok"
	oSimAcrescimo:SetCancelButton(.F.)
	oSimAcrescimo:SetFields(MakeForm())
	oSimAcrescimo:SetAdvplToJs(MakeAdvpl())
	oSimAcrescimo:SetScript(MakeScript())
	oSimAcrescimo:SetJsToAdvpl({|oWebChannel, key, value| U_TCR208JS(oWebChannel, key, value)})
	oSimAcrescimo:Connect(oDlg)

	oDialog:Activate()

Return NIL

/*{Protheus.doc} MakeAdvpl
Monta o script para o simulador
<AUTHOR>
@since 02/05/2022
@version undefined
@type function
@return cField ,character, Formulario HTML
*/
Static Function MakeAdvpl()

	Local cScript := ''

	BeginContent var cScript
		var elemento = '#' + key
		if(elemento == '#linhasParcelas') {
			$(elemento).html(value);
		} else {
			$(elemento).val(value);
		}

	EndContent

Return cScript

/*{Protheus.doc} MakeScript
Monta o script para o simulador
<AUTHOR>
@since 02/05/2022
@version undefined
@type function
@return cField ,character, Formulario HTML
*/
Static Function MakeScript()

	BeginContent var cScript
		<script>
			changeQtd = function(qtdParcelas){
				var fields = {};
				fields['qtdParcelas'] = +$('#qtdParcelas').val()
				var jsonStr = JSON.stringify(fields);
				twebchannel.jsToAdvpl("<calculate>", jsonStr);
				return;
			}
		</script>
	EndContent

Return cScript

/*{Protheus.doc} MakeForm
Monta o form para o simulador
<AUTHOR>
@since 02/05/2022
@version undefined
@type function
@return cField ,character, Formulario HTML
*/
Static Function MakeForm()

	Local cDefField := ""
	Local cField    := ""

	// Insere campos no formulario HTML
	BeginContent var cDefField
		<div class="container-fluid">
			<div class="row">
				<div class="form-group col-sm-6">
					<label for="nomeProduto" class="control-label">%Exp:STR0001%</label>
					<input type="text" class="form-control" name="nomeProduto" id="nomeProduto" readonly value="{{nomeProduto}}">
				</div>
				<div class="form-group col-sm-6">
					<label for="qtdParcelas" class="control-label">%Exp:STR0002%</label>
					<input type="number" class="form-control" name="qtdParcelas" id="qtdParcelas" placeholder="Qtd. Parcelas" required min="1" {{qtdParcelas}}
						onchange="changeQtd(this.value)" >
					<div class="help-block with-errors"></div>
				</div>
			</div>
			<div class="row">
				<div class="form-group col-sm-4">
					<label for="percentualAcrescimo" class="control-label">%Exp:STR0003%</label>
					<input type="text" class="form-control" name="percentualAcrescimo" id="percentualAcrescimo" readonly value="{{percentualAcrescimo}}">
				</div>
				<div class="form-group col-sm-4">
					<label for="valorAcrescimo" class="control-label">%Exp:STR0004%</label>
					<input type="text" class="form-control" name="valorAcrescimo" id="valorAcrescimo" readonly value="{{valorAcrescimo}}"}>
				</div>
				<div class="form-group col-sm-4">
					<label for="totalComAcrescimo" class="control-label">%Exp:STR0005%</label>
					<input type="text" class="form-control" name="totalComAcrescimo" id="totalComAcrescimo" readonly value="{{totalComAcrescimo}}"}>
				</div>
			</div>
			<table class="table table-secondary table-striped table-hover">
				<thead>
					<tr class="table-primary">
						<th scope='col'>%Exp:STR0006%</th>
						<th scope='col'>%Exp:STR0007%</th>
					</tr>
				</thead>
				<tbody name="linhasParcelas" id="linhasParcelas">
					{{tableBody}}
				</tbody>
			</table>
		</div>
	EndContent

	cField := MakeBodyTbl()
	cField := StrTran(cDefField, '{{tableBody}}', cField)
	cField := StrTran(cField,'{{qtdParcelas}}',"value='" + AllTrim(Str(aParcelas[1])) + "'")
	cField := StrTran(cField,'{{nomeProduto}}',Alltrim(cNomeProd))
	cField := StrTran(cField,'{{percentualAcrescimo}}',Transform(aParcelas[2], "@E 9,999.99"))
	cField := StrTran(cField,'{{valorAcrescimo}}',Transform(aParcelas[3], "@E 999,999,999.99"))
	cField := StrTran(cField,'{{totalComAcrescimo}}',Transform(aParcelas[4], "@E 999,999,999.99"))

Return cField

/*{Protheus.doc} MakeBodyTbl
Monta a tabela de parcelas
<AUTHOR>
@since 02/05/2022
@version undefined
@type function
@return cField ,character, Formulario HTML
*/
Static Function MakeBodyTbl()

	Local cBody 	:= ''
	Local cLine 	:= ''
	Local nLoop		:= 0

	BeginContent var cBody
		<tr>
			<td>
				{{numeroParcela}}
			</td>
			<td>
				{{valorParcela}}
			</td>
		</tr>
	EndContent

	For nLoop := 1 to aParcelas[1]

		cLine += cBody

		cLine := StrTran( cLine, '{{numeroParcela}}', StrZero(nLoop,3))
		cLine := StrTran( cLine, '{{valorParcela}}', Transform(aParcelas[5][nLoop], "@E 999,999,999.99"))

	Next

Return cLine
/*{Protheus.doc} TCR208JS
Blocos de codigo recebidos via JavaScript atraves do webengine
<AUTHOR>
@since 02/05/2022
@version undefined
@type function
*/
User Function TCR208JS(oWebChannel, cKey, cValue)

	Local oJson
	Local nQtdParc	:= 0
	// ---------------------------------------------------------------
	// Tratamento para as mensagens vindas do JavaScript
	// ---------------------------------------------------------------
	Do Case

	case cKey  == "<submit>"

		oDialog:Deactivate()

	case cKey == "<cancel>"

		oDialog:Deactivate()

	case cKey == "<calculate>"

		oJson := JsonObject():New()
		oJson:FromJson(cValue)

		nQtdParc := oJson['qtdParcelas']

		aParcelas := aClone(U_TC207Par(nValorOri, nQtdParc))

		oSimAcrescimo:RunAdvplToJS('linhasParcelas',MakeBodyTbl())
		oSimAcrescimo:RunAdvplToJS('percentualAcrescimo',Transform(aParcelas[2], "@E 999,999,999.99"))
		oSimAcrescimo:RunAdvplToJS('valorAcrescimo',Transform(aParcelas[3], "@E 999,999,999.99"))
		oSimAcrescimo:RunAdvplToJS('totalComAcrescimo',Transform(aParcelas[4], "@E 999,999,999.99"))

	EndCase

Return
