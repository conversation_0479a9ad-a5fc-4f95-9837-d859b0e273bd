#INCLUDE 'TOTVS.CH'
#INCLUDE 'FWMVCDEF.CH'
#INCLUDE 'ESPA090.CH'

User Function ESPA090()
	
	Local aArea      := GetArea()
	
	Local oBrowse    := FWMBrowse():New()
	
	oBrowse:<PERSON><PERSON><PERSON><PERSON>("PVL")
	oBrowse:SetDescription(STR0001)
	oBrowse:Activate()
	RestArea(aArea)
	
Return

Static Function MenuDef()
	
	Local aRotina := {}
	
	ADD OPTION aRotina TITLE "Visualizar" ACTION "VIEWDEF.ESPA090" OPERATION MODEL_OPERATION_VIEW   ACCESS 0
	ADD OPTION aRotina TITLE "Incluir"    ACTION "VIEWDEF.ESPA090" OPERATION MODEL_OPERATION_INSERT ACCESS 0
	ADD OPTION aRotina TITLE "Alterar"    ACTION "VIEWDEF.ESPA090" OPERATION MODEL_OPERATION_UPDATE ACCESS 0
	ADD OPTION aRotina TITLE "Excluir"    ACTION "VIEWDEF.ESPA090" OPERATION MODEL_OPERATION_DELETE ACCESS 0
	
Return aRotina

Static Function ModelDef()
	
	Local oStrPVL := FWFormStruct(1, "PVL")
	Local oModel  := MPFormModel():New("PVLACE", , {|oModel | ValidPar(oModel)})
	
	oModel:AddFields("PVLMASTER",, oStrPVL)
	oModel:SetPrimaryKey({"PVL_FILIAL", "PVL_PAIS", "PVL_CODACE"})
	oModel:SetDescription(STR0001)
	oModel:GetModel("PVLMASTER"):SetDescription(STR0001)
	
Return oModel

Static Function ViewDef()
	
	Local oModel  := ModelDef()
	Local oStrPVL := FWFormStruct(2, "PVL")
	Local oView   := FWFormView():New()
	
	oView:SetModel(oModel)
	oView:AddField("VIEW_PVL", oStrPVL, "PVLMASTER")
	oView:CreateHorizontalBox("PVL", 100)
	oView:EnableTitleView("VIEW_PVL",STR0002)
	oView:SetCloseOnOk({||.T.})
	oView:SetOwnerView("VIEW_PVL", "PVL")
	
Return oView

Static Function ValidPar(oModel)
	
	Local oModelPVL  := oModel:GetModel("PVLMASTER")
	
	Local nOperation := oModel:GetOperation()
	
	Local cDir       := GetMV("FS_UPDSYS")
	Local cTipo 	 := ValType(oModelPVL:GetValue("PVL_CONTEU"))
	
	Local aSavArea   := GetArea()
	
	If nOperation == 3 .OR. nOperation == 4
		If Select("SX6AP") > 0
			SX6AP->(DbCloseArea())
		EndIf
		
		//DbUseArea(.T., "CTREECDX", cDir + "sx6.dtc", "SX6AP", .T., .F.)
		USE (cDir + "sx6.dtc") ALIAS ("SX6AP") SHARED NEW VIA ("CTREECDX") 
		DbSetIndex(cDir + "sx6.cdx")
		DbSetOrder(1)
		
		If !SX6AP->(DbSeek( xFilial("SX6AP") + oModelPVL:GetValue("PVL_VAR")))
			If MsgYesNo(STR0003,STR0004)
				RecLock("SX6AP", .T.)
				SX6AP->&("X6_VAR")      := oModelPVL:GetValue("PVL_VAR")
				SX6AP->&("X6_TIPO") 	   := cTipo
				SX6AP->&("X6_DESCRIC")  := oModelPVL:GetValue("PVL_DESVAR")
				SX6AP->&("X6_CONTEUD")  := oModelPVL:GetValue("PVL_CONTEU")
				SX6AP->(MsUnlock())
				MsgInfo(STR0005)
			EndIf
		EndIf
		
		If Select("SX6AP") > 0
			SX6AP->(DbCloseArea())
		EndIf
	EndIf
	
	RestArea(aSavArea)
	
Return .T.
