#include "Protheus.ch"
/*/{Protheus.doc} 
<AUTHOR>
@version 1.0
/*/
CLASS Map

	DATA aENTRY

	METHOD New() CONSTRUCTOR
	METHOD Size()
	METHOD HasKey()
	METHOD HasValue()
	METHOD GetIndex()
	METHOD GetEntry()
	METHOD SetEntry()
	METHOD PutEntry() 
	//METHOD DelEntry()
	METHOD ToJson()
	METHOD ToJsonArray()
ENDCLASS

METHOD New(_Map) CLASS Map
	::aENTRY := {}	
RETURN SELF

METHOD Size() CLASS Map
RETURN LEN(aEntry)

/*/{Protheus.doc} HasKey
Busca no array de entrys deste objeto
se o mesmo possue alguma Entry com a KEY informada
<AUTHOR>
@param cKEY, character, (Key que deve ser procurada)
@version 1.0
/*/
METHOD HasKey(cKEY) CLASS Map
	Local var := 0
	
	for var:= 1 to Len(::aENTRY)
		if ::aENTRY[var]:GetKey() == cKEY
			RETURN .T.		
		endif
	next
RETURN .F.

/*/{Protheus.doc} HasValue
Busca no array de entrys deste objeto
se o mesmo possue alguma Entry com o VALUE informado
<AUTHOR>
@version 1.0
@param uVALUE, ${Undefined}, (VALUE que deve ser procurado)
/*/
METHOD HasValue(uVALUE) CLASS Map
	Local var := 0
	
	for var:= 1 to Len(::aENTRY)
		if VALTYPE(::aENTRY[var]:GetValue()) == VALTYPE(var)
			if ::aENTRY[var]:GetValue() == VALTYPE(var)
				RETURN .T.		
			endif
		endif
	next
RETURN .F.


/*/{Protheus.doc} GetIndex
Retorna a posicao de uma ENTRY pela sua KEY
<AUTHOR>
@version 1.0
@param cKEY, character, (Key que deve ser procurada)
/*/
METHOD GetIndex(cKEY) CLASS Map
	Local var := 0
	
	for var:= 1 to Len(::aENTRY)
		if ::aENTRY[var]:GetKey() == cKEY
			RETURN var		
		endif
	next	
RETURN 0


/*/{Protheus.doc} GetEntry
Retorna o Objeto Entry que possui esta KEY
<AUTHOR>
@version 1.0
@param cKEY, character, (Key que deve ser procurada)
/*/
METHOD GetEntry(cKEY) CLASS Map
	Local var := 0
	local oEntry:= nil
	
	var := ::GetIndex(cKEY)
	
	if(var > 0)
		oEntry := ::aENTRY[var]
	endIf
RETURN oEntry

METHOD SetEntry(oEntry) CLASS Map
	if ::HasKey(oEntry:GetKEY())
		::aENTRY[::GetIndex(oEntry:GetKEY())] := oEntry
	Endif
RETURN 

METHOD PutEntry(oEntry) CLASS Map
	If ::HasKey(oEntry:GetKey())
		::SetEntry(oEntry)
	Else
		aadd(::aENTRY,oEntry)
	Endif
RETURN


/*/{Protheus.doc} ToJson
Retorna o valor de Todas as Entrys deste 
objeto, de acordo com a formatacao Json
<AUTHOR>
@version 1.0
/*/
METHOD ToJson() CLASS Map
	Local cJson := "{"
	Local var := 0
	Local oEntry
	
	for var:= 1 to Len(::aEntry)
		oEntry := ::aEntry[var]
		cJson += oEntry:ToJson()
		
		if(var < Len(::aEntry))
			cJson += ', ' 
		endIf
	next
	
	cJson += "}"
	
	if Len(::aEntry) == 0
		cJson := "null"	
	endIF
	
RETURN cJson	
/*/{Protheus.doc} ToJson
Retorna o valor de Todas as Entrys deste 
objeto, de acordo com a formata��o Json
<AUTHOR>
/*/
METHOD ToJsonArray() CLASS Map
	Local cJson := "["
	Local var := 0
	Local oEntry
	
	for var:= 1 to Len(::aEntry[1]:KEY)
		oEntry := ::aEntry[1]:KEY[var]
		cJson += oEntry:ToJson()
		
		if(var < Len(::aEntry[1]:KEY))
			cJson += ', ' 
		endIf
	next
	
	cJson += "]"
	
	// if Len(::aEntry[1]:KEY) == 0
	// 	cJson := "null"	
	// endIF
	
	
RETURN cJson 
