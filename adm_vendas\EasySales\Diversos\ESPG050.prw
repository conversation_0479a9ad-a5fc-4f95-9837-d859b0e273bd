#INCLUDE 'TOTVS.CH'


/*/{Protheus.doc} ESPG050
	Description
	<AUTHOR>
	@example Example
	@param   [Parameter_Name],Parameter_type,Parameter_Description
	@return  cRetorno
	@table   Tables
	@since   25-08-2020
/*/

User Function ESPG050()	
	Local cTipo    := ''
	Local cChave   := ''
	Local cRetorno := ''
	
	IF INCLUI
		Return ''
	EndIF
	
	IF IsInCallStack('U_ESPA060')
		cTipo  := PVO->PVO_TIPO
		cChave := XFilial('PVO') + PVO->PVO_PAIS + PVO->PVO_CODACE
	ELSE
		cTipo  := PVQ->PVQ_TIPO
		cChave := XFilial('PVQ') + PVQ->PVQ_PAIS + PVQ->PVQ_CODACE
	EndIF
	
	IF cTipo $ '1234567'
	   cAlias   := 'PZ' + cTipo
	   cAlias	:= u_AjustarTab(cAlias)
	   cRetorno := Posicione(cAlias, 1, cChave, cAlias + '_DESACE')
	EndIF
	
Return(cRetorno)
