#INCLUDE "PROTHEUS.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOTVS.CH"
#INCLUDE "FILEIO.CH"
#INCLUDE "TBICONN.CH"
#INCLUDE "TOPCONN.CH"

#define CRLF Chr(13) + Chr(10)

 /*/{Protheus.doc} TIRCJ001
Funcao responsavel por salvar na pasta do EDI os Cnabs Baixados pelo fonte TIRCV009.prw
@type  Function
<AUTHOR> Menabue Lima
@since 28/08/2021
@version 1.0
 */
User Function TIRCJ001()

	Local cAliasQry := ""
	Local cQry		:= ""
	Local cPathEdi	:= ""
	Local nI		:= 0


	If U_RCVENV(cEmpAnt,cFilAnt)
		cAliasQry :=  GetNextAlias()

		cQry:= " SELECT R_E_C_N_O_ REGISTRO
		cQry+= " FROM "+ RetSqlName("P40") +" WHERE   D_E_L_E_T_ = ' ' AND  P40_ENVIO ='F'      "+ CRLF
		cQry+= " ORDER BY P40_DATA "+ CRLF
		dbUseArea(.T.,"TOPCONN",TcGenQry(,,cQry),cAliasQry,.T.,.T.)

		While (cAliasQry)->(!Eof())
			DbSelectArea("P40")
			DbSetOrder(1)
			P40->(dbGoto((cAliasQry)->REGISTRO) )
			If Empty(P40->P40_CONTEN)
				(cAliasQry)->(DbSkip())
				Loop
			EndIf
			cFilAnt			:= P40->P40_FILIAL
			cPathEdi		:= SuperGetMv("MV_COBENV", .F.,"")
			aDir			:= Separa(cPathEdi,"\",.F.)
			cDir			:= ""
			For nI:= 1 To Len(aDir)
				cDir		+= "\"+aDir[nI]
				nDir 		:= MakeDir( cDir )
			Next nI
			Reclock("P40",.F.)
			if (nDir >=0)
				cPathEdi		:= SubStr(cPathEdi,2,Len(cPathEdi))
				nHandle 		:= MsfCreate(cPathEdi+"\"+Alltrim(P40->P40_FILE),0)
				if (nHandle > 0)
					FWrite(nHandle, decode64(P40->P40_CONTEN))
					FClose(nHandle)
					P40->P40_ENVIO 	:= .T.
					P40->P40_MSG	:= "Arquivo("+P40->P40_FILE+") Salvo no local "+cPathEdi
				else
					P40->P40_ERRO   := .T.
					P40->P40_MSG	:= "Erro ao Salvar o Arquivo("+P40->P40_FILE+") no local "+cPathEdi
				EndIF
			Else
				P40->P40_ERRO   := .T.
				P40->P40_MSG	:= "Local Invalido ("+cPathEdi+")"
			EndIF
			P40->(MsUnlock())
			(cAliasQry)->(DbSkip())
		EndDo
		(cAliasQry)->(DbCloseArea())
	Endif
Return




