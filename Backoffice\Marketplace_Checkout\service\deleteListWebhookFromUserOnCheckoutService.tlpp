#include "tlpp-core.th"


Class   DeleteListWebhookFromUserOnCheckoutService
    Private Data    oWebhookList                                As  Object
    Private Data    cResponseIpaas                              As  Character

    Public  Method  new(oVar    As  Object)                     As  Object
    Public  Method  execute()                                   As  Object  
    Public  Method  sendToIpaas(cJson   As  Character)          As  Object
EndClass



Method  new(oVar    As  Object)   As  Object  Class   DeleteListWebhookFromUserOnCheckoutService
    ::oWebhookList              :=  oVar
    ::cResponseIpaas            :=  ''
Return  Self


Method  execute()   As  Object  Class   DeleteListWebhookFromUserOnCheckoutService
    ::sendToIpaas(::oWebhookList:ToString())
Return  Self

Method  sendToIpaas(cJson   As  Character)       As  Object  Class   DeleteListWebhookFromUserOnCheckoutService

    //Local   cUrl          := GetMV("TI_TIPDXX5",,"https://api-ipaas.totvs.app/ipaas/api/v1/integrations/bedf3666-b94d-4893-b51c-e3ddb8b20439/api-key/34084cc2-4eb4-4e09-8774-722909561c37")
    Local   cUrl    :=  'https://api-ipaas.totvs.app/sync-hook/api/v1/integrations/dcbdac30-782d-4b78-84a5-33ad313eab7a/api-key/993e0db7-a131-424e-82f2-93c9adf05ea9'
    Local   oIpaas  :=  Nil

    oIpaas := TIiPaaS():New()
	oIpaas:SetWebHook(cUrl)
	oIpaas:SetBodyWebHook(cJson)
	oIpaas:SendToiPaaS()

    ::cResponseIpaas  :=  oIpaas:getIpaasCharacterResponse()

    FreeObj(oIpaas)

Return  Self
