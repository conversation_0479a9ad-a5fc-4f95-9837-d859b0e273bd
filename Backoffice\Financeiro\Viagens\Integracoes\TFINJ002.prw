#INCLUDE "PROTHEUS.CH"

User Function JOBRESLOG( _aParam )

Local aProcessa	:= {}
Local nX        	:= 0
Local _cRotina	:= ""
Local _cEmpresa 	:= ""
Local _cFilial	:= ""

Default _aParam	:= {"00","00001000100"} 

Conout("Data: " +DtoC(DATE()) + " - " + SubStr(Time(),1,5) + " Iniciou JOB Reserve [JobResLog]: Sincronizacao de Pedidos com gercao de LOG")

RpcSetType(3)
RPCSetEnv(_aParam[1],_aParam[2],,,"FIN")

DbSelectArea("FL2")
FL2->(DbSetOrder(1))
While FL2->(!EoF())
	If !Empty(FL2->FL2_USER) .And. !Empty(FL2->FL2_PSWRES)
		AADD(aProcessa, {FL2->FL2_BKOEMP,FL2->FL2_USER,FL2->FL2_PSWRES, FL2->FL2_LICENC})
	Endif
	FL2->(DbSkip())	     
EndDo

For nX := 1 to Len(aProcessa)
	
	DbSelectArea("SM0")
	If SM0->(DbSeek(aProcessa[nX,1]))
		_cEmpresa := SM0->M0_CODIGO
       _cFilial  := SM0->M0_CODFIL

		RPCClearEnv()
		RpcSetType(3)
		RPCSetEnv(_cEmpresa,_cFilial,,,"FIN")
      	
		//JOBRESIMP (QUE EXECUTA A CHAMADA DO FINA661)
		FINA661(aProcessa[nX,4])
	EndIf
	      
Next nX

Conout("Data: " +DtoC(DATE()) + " - " + SubStr(Time(),1,5) + " Concluiu JOB Reserve [JobResLog]: Sincronizacao de Pedidos com gercao de LOG")

RPCClearEnv()

Return()