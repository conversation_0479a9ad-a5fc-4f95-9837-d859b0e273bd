#INCLUDE "PROTHEUS.CH"
#INCLUDE "TOPCONN.CH"

//----------------------------------------------------------------
/*/{Protheus.doc} AJCROG
Ajusta PH5_VLTOT e gera arquivo CSV com valor antigo que continha no campo a titulo de bkp
obs: desconsiderar registros com PH5_VLNOVA diferente de zero
<AUTHOR>
@since 10/07/2020
@version 1.0
/*/
//----------------------------------------------------------------
User Function AJCROG()
Local aSays 	 := {}
Local aButtons	 := {}
Local nOpca		 := 0
Local lRet       := .T. 
Local aParam1 	 := {}
Local aRetP1	 := {}
Local nI		 := 0
Local nPontos    := 0 
Local oDlg       := Nil    
Local nOpc       := 2 
Local cNomeArq   := '' 
Local cLocArq 
Private cErrArqRet := '' 
Private cLogArqRet := '' 

AADD(aSays, OemToAnsi("Este programa ajusta PH5_VLTOT e gera LOG com o valor antigo do campo!"))
AADD(aSays, OemToAnsi(""))
AADD(aButtons, {1,.T.,{|| nOpca := 1, FECHABATCH()}})
AADD(aButtons, {2,.T.,{|| nOpca := 0, FECHABATCH()}})
FormBatch("Ajusta PH5_VLTOT do cronograma", aSays, aButtons)

If nOpca == 1
	cLocArq    := cGetFile("Arquivos CSV|*.CSV|","Gerar LOG no diretorio abaixo",0,,.T.,GETF_ONLYSERVER+GETF_LOCALFLOPPY+GETF_LOCALHARD +GETF_RETDIRECTORY  )
	cLocArq    += "Log_" + dtos(dDataBase) + "_" + strtran(Time(),":","") + ".csv"
	cLogArqRet := cLocArq 
	cErrArqRet := strtran(cLocArq, ".", "_erro.")                            
		
	If !Empty(cLocArq)
		FwMsgRun(, { || Reprocessa() }, "Processando e ajustando PH5","Aguarde")
	EndIf
EndIf

Return

//-------------------------------------------------------------------
/*{Protheus.doc} Reprocessa
Varre invocando rotina de ajuste da PH5

<AUTHOR> Leite
@since 15/07/2015
@version Protheus12
*/
//-------------------------------------------------------------------
Static function Reprocessa(cArqRet)
Local cBuffer := ""
Local nCpos
Local cString := ""
Local cAlias := GetNextAlias()

Procregua(0)
U_XACALOG(cLogArqRet, "CONTRATO;REVISAO;COMPET;CLIENTE;NUMERO;ITEM;RECNO;DE;PARA")

BeginSql Alias cAlias
	SELECT PH5.R_E_C_N_O_ AS RECPH5
	FROM
	%Table:CN9% CN9 INNER JOIN %Table:PH5% PH5 
	ON
	CN9_NUMERO = PH5_CONTRA AND
	CN9_REVISA = PH5_REVISA AND
	CN9_FILIAL = PH5_FILIAL
	WHERE
	CN9.%notDel% AND
	PH5.%notDel% AND
	CN9.CN9_SITUAC = '05' AND
	(PH5_VLTOT - (PH5_VLUNIT * PH5_QUANT) ) >= 0.2 AND PH5_VLNOVA = 0 
	ORDER BY PH5.R_E_C_N_O_ 
EndSql


While (cAlias)->(!Eof())
	PH5->(DBGOTO( (cAlias)->(RECPH5) ))
	cBuffer := PH5->PH5_CONTRA + ";" + PH5->PH5_REVISA + ";" + PH5->PH5_COMPET + ";" + PH5->PH5_CLIENT + ";" +  PH5->PH5_NUMERO + ";" + PH5->PH5_ITEM
	bBlock	:= ErrorBlock({|e|  ThrdChkErr( e, cBuffer)  })

	BEGIN SEQUENCE
		//Me alertaram pra desconsiderar registros que tenham campo PH5_VLNOVA informados pq seria outra situacao 
        //situação de cronograma e estaria certo mesmo diferentes.
		If Empty(PH5->PH5_VLNOVA) .And. (PH5->PH5_VLTOT - (PH5->PH5_VLUNIT * PH5->PH5_QUANT) ) >= 0.2 
			U_XACALOG(cLogArqRet, PH5->PH5_CONTRA + ";" + PH5->PH5_REVISA + ";" + PH5->PH5_COMPET + ";" + PH5->PH5_CLIENT + ";" +  ;
								PH5->PH5_NUMERO + ";" + PH5->PH5_ITEM + ";" + str((cAlias)->(RECPH5)) + ";" + str(PH5->PH5_VLTOT)+ ";" + str((PH5->PH5_VLUNIT * PH5->PH5_QUANT)) )
			reclock('PH5',.F.)
			PH5->PH5_VLTOT := PH5->PH5_VLUNIT * PH5->PH5_QUANT
			msunlock()
		EndIf
	END SEQUENCE

	ErrorBlock(bBlock)
	(cAlias)->(DbSkip())	
EndDo

(cAlias)->(DbCloseArea())

MsgInfo('Processo Finalizado!')
return

//----------------------------------------------------------------
/*/{Protheus.doc} ThrdChkErr
Captura errlog 

<AUTHOR>
@since 10/07/2020
@version 1.0
/*/
//----------------------------------------------------------------
Static Function ThrdChkErr(oErroArq, cBuffer) 
Local n:= 2
Local cLogError 
Local cDir :=  ""
Local cbErrInThrd := ''
Local cRet := oErroArq:Description + oErroArq:ErrorStack

U_xacalog(cErrArqRet , (cBuffer + ' <----- Linha problematica'  )   )
U_xacalog(cErrArqRet , cRet)

If oErroArq:GenCode > 0
    cbErrInThrd += Alltrim( Str( oErroArq:GenCode ) )
    cbErrInThrd += chr(13)+chr(10)
    cbErrInThrd += AllTrim( oErroArq:Description )
    cbErrInThrd += chr(13)+chr(10)
EndIf 

While ( !Empty(ProcName(n)) )
    cbErrInThrd += Trim(ProcName(n)) + " (" + Alltrim(Str(ProcLine(n))) + ") " + chr(13)+chr(10)
    n++
End   

cbErrInThrd += chr(13)+chr(10)

U_xacalog(cErrArqRet , cbErrInThrd )          

Break
Return cRet 

