#INCLUDE "PROTHEUS.CH"

//----------------------------------------------------------------
/*/{Protheus.doc} TXGV02BP 
<AUTHOR>
@since 02/07/2019
@version 1.0
/*/
//----------------------------------------------------------------
User Function TXGV02BP()

Local _nX		:= 0
Local _lRet		:= .F.
Local _lUsa		:= GetMv("TI_TXGV2US",,.T.)
Local _cFuncs	:= GetMv("TI_TXGV2BP",,"U_TGCVA108|")
Local _aFuncs	:= Separa(_cFuncs, "|", .T.)
Local _aArea	:= GetArea()

If _lUsa
	For _nX := 1 To Len( _aFuncs )
		If IsInCallStack( _aFuncs[_nX] )
			_lRet := .T.
			Exit
		EndIf
	Next _nX
EndIf

RestArea( _aArea )

Return( _lRet )