#INCLUDE "PROTHEUS.CH"
//-----------------------------------------------------------------------------
/*/ {Protheus.doc} Function
Analise de Credito e Risco
<AUTHOR>
@version P12
@since 16/09/2015
/*/
//-----------------------------------------------------------------------------
User Function TCRMX048()

Local nResult := 1
Local nLimite := GetMv("TI_INTLINA")
Local aArea	:= GetArea()

BeginSQL Alias "SE1QRY"
	SELECT 'S' AS INADIM, 'N' AS PRORRO, SUM(E1_SALDO) AS SALDO 
	  FROM %table:SE1%
	 WHERE E1_CLIENTE = %Exp:SA1->A1_COD% AND E1_LOJA = %Exp:SA1->A1_LOJA%
	   AND RTRIM(E1_TIPO) = 'NF' AND E1_VENCREA < %Exp:dDataBase%	   
	   AND %notDel% 
	 GROUP BY E1_CLIENTE, E1_LOJA
	HAVING SUM(E1_SALDO) > %Exp:nLimite%    
	UNION
	SELECT 'N' AS INADIM, 'S' AS PRORRO, SUM(E1_SALDO) AS SALDO 
	  FROM %table:SE1%
	 WHERE E1_CLIENTE = %Exp:SA1->A1_COD% AND E1_LOJA = %Exp:SA1->A1_LOJA%
	   AND RTRIM(E1_TIPO) = 'NF'  AND E1_VENCREA >= %Exp:dDataBase%
	   AND E1_VENCREA > E1_VENCTO AND %notDel% 
	 GROUP BY E1_CLIENTE, E1_LOJA
	HAVING SUM(E1_SALDO) > %Exp:nLimite%    	
EndSQL	

Do While !SE1QRY->(EoF())				
	If SE1QRY->INADIM == 'S'
		nResult := 3
	Endif
	If nResult == 1 .And. SE1QRY->PRORRO == 'S'
		nResult := 2
	Endif
	SE1QRY->(dbSkip())		
Enddo	
SE1QRY->(dbCloseArea())				

RestArea(aArea)

Return nResult
