#include "tlpp-core.th"

Class   MessageBodyDTO
    Public  Data    bcc             As  Array
    Public  Data    text            As  Character
    Public  Data    subject         As  Character
    Public  Data    from1            As  Object
    Public  Data    to1              As  Object
    Public  Data    dynamicData     As  Object
    Public  Data    template        As  Character
    Public  Data    bucket          As  Character

    
    Public  Method  new()   As  Object

EndClass


Method  new()   As  Object Class MessageBodyDTO
    ::bcc             :=  {}
    ::text            :=  ''
    ::subject         :=  ''
    ::from1            :=  ConstructionEmailDTO():new()
    ::to1              :=  ConstructionEmailDTO():new()
    ::dynamicData     :=  Nil
    ::template        :=  ''
    ::bucket          :=  ''



Return Self


