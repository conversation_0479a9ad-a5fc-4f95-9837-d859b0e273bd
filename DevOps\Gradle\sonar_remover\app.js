require("dotenv").config();
const axios = require("axios");
const fs = require("fs");
const moment = require("moment");
const fontsWithBugs = `data/fonts_with_bugs.json`;
let branchSonarProject = process.env.BUILD_SOURCEBRANCHNAME;

if (branchSonarProject == "merge") {
  branchSonarProject = process.env.SYSTEM_PULLREQUEST_SOURCEBRANCH.substring(
    process.env.SYSTEM_PULLREQUEST_SOURCEBRANCH.lastIndexOf("/") + 1
  );
}

const getIssues = async (project, page, status) => {
  try {
    return axios(
      `http://10.171.59.17:9000/api/issues/search?componentKeys=${project}&ps=500&p=${page}&statuses=${status}`,
      {
        auth: {
          username: process.env.SONAR_USER,
          password: process.env.SONAR_PASS,
        },
      }
    )
      .then((response) => {
        return response;
      })
      .catch((err) => err);
  } catch (error) {
    console.log(error.response);
  }
};

const treatIssues = async () => {
  let issues = [];
  let issuesQuickFiltered = [];
  let issuesNew = [];
  let issuesNewPath = [];
  let filePath = [];
  let page = 0;
  let req = true;
  let lastPath = "";
  let msg = "";
  let body = { text: msg };

  while (req) {
    page++;
    const issuesProd = await getIssues(`${process.env.SONAR_PROD}`, page, "");
    const issuesQuick = await getIssues(`${branchSonarProject}`, page, "");

    issuesProd?.data.issues.length !== 500 ? (req = false) : (req = true);

    issuesProd?.data.issues.forEach((issue) => {
      issues.push(issue);
    });

    issuesQuick?.data.issues.forEach((issue) => {
      issuesQuickFiltered.push(issue);
    });
  }

  issuesQuickFiltered.forEach((issue) => {
    const hashQuick = issue.hash;
    const found = issues.find((issue) => issue.hash === hashQuick);

    if (!found && issue.type === "BUG") {
      issuesNew.push(issue);
    }
  });

  const copia = [...issuesNew];
  let a = [];
  let b = [];
  let errors = [];
  let counter = 0;

  issuesNew.forEach((issue, index) => {
    if (lastPath !== issue.component && issue.status === "OPEN") {
      lastPath = issue.component;
      counter = counter + 1;
      issuesNewPath.push(issue.component);

      errors.push({
        id: counter,
        filePath: issue.component.split(":")[1],
        sonar: `https://sonar.ti.totvs.com/project/issues?id=${branchSonarProject}&issues=${issue.key}`,
        error: issue.rule.split(":")[1],
        rule: issue.rule.split(":")[0],
        description: issue.message,
      });

      b.push(issue);
    } else if (lastPath !== issue.component && issue.status === "CLOSED") {
      a = copia.splice(index, 1);
    }
  });

  body = getBodyGCWH(
    `${branchSonarProject}`,
    "_br",
    "https://cdn-icons-png.flaticon.com/512/5219/5219070.png",
    errors
  );
  console.log(3, body);
  download("data/issuesGoogleChat.log", errors);

  axios.post(`${process.env.SONAR_BOT}`, body);

  issues.forEach((issue) => {
    this.msg = issue.component;
    if (lastPath !== issue.component) {
      lastPath = issue.component;
      filePath.push(issue.component);
    }
  });

  if (issues.length > 0) {
    download(fontsWithBugs, issuesNewPath);
    download("data/filePath_prod.json", filePath);
    return true;
  }
};

function download(name, data) {
  let dir = "./data";
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir);
  }

  fs.writeFile(name, JSON.stringify(data), (err) => {});
}

async function createData() {
  if (await treatIssues()) {
    console.log("ARQUIVOS GERADOS COM SUCESSO!");
  }
}

function getBodyGCWH(branch, repo, avatarUrl, errors) {
  let widget = [];
  if (repo.includes("_br")) {
    repo = "protheus_br";
  } else if (repo.includes("_mi")) {
    repo = "protheus_mi";
  }

  console.log(4, errors);

  if (errors.length > 0) {
    errors.forEach((err) => {
      widget.push({
        keyValue: {
          topLabel: err.filePath,
          content: `${err.error}: ${err.description}`,
          iconUrl: "https://cdn-icons-png.flaticon.com/512/78/78946.png",
          button: {
            textButton: {
              text: "Abrir Sonar",
              onClick: {
                openLink: {
                  url: err.sonar,
                },
              },
            },
          },
        },
      });
    });
  } else {
    widget.push({
      keyValue: {
        content: `Nenhum bug novo encontrado!`,
        iconUrl: "https://img.icons8.com/ios-filled/512/checked-checkbox.png",
       
      },
    });
  }


  let body = {
    cards: [
      {
        header: {
          title: branch,
          subtitle: repo,
          imageUrl: avatarUrl,
        },
      },
      {
        sections: [
          {
            widgets: widget,
          },
        ],
      },
    ],
  };
  return body;
}

createData();
