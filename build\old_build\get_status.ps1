Import-Module -name GHCWebhook

$job_name=$args[0]
$job_status=$args[1]

$uri = "https://chat.googleapis.com/v1/spaces/AAAAXD6T1Bo/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=I4MHd57Sqm02BIemDNi0MFkqIBpzJlUpPJ9MpTxhjEU%3D"

$message = New-GHCCardMessage -Content {
    New-GHCCardSection -Content {
        New-GHCCardWidget -Content {
            New-GHCWidgetKeyValue -TopLabel "$job_name" -Content "$job_status"
        }

    }
}

Write-host $job_name - $job_status

Send-GHCWebhookMessage -URI $uri -Message $message >$null 2>&1