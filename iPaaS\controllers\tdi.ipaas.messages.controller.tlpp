#include "tlpp-core.th"
#include "tlpp-rest.th"
#Include "TOTVS.CH"
#Include "Protheus.ch"
#INCLUDE "TRYEXCEPTION.CH"
#INCLUDE "PARMTYPE.CH"
#INCLUDE "TOPCONN.CH"
namespace tdi.ipaas.messages.controller
using namespace tdi.ipaas.messages.service

/*/{Protheus.doc} TdiIpaasMessageControllers()
    Classe responsavel por conter ops metodos disponiveis para a logica de controle de messagens enviadas ao IPAAS
    @type Class
    <AUTHOR> Menabue Lima
    @since 13/03/2025
    @version 1.0
/*/
Class TdiIpaasMessageControllers from LongNameClass

	public data oServices

	public data  jParams             as Json
	public data  jPathParams         as Json
	public data  jBodyRequest		 as Json
	public data  jBodyResponse       as Json

	public data  cPage               as Character
	public data  cPageSize           as Character
	public data  cFilter             as Character

	public method New()
	public method Destroy()
	private method setResponse(oServices)

	//Apis Relacionadas  diagramas tabela PXB
	@Get("/api/tdi/ipaas/v1/diagrams")
	public method GetDiagrams()
	@Post("/api/tdi/ipaas/v1/diagrams")
	public method PostDiagram()
	@Put("/api/tdi/ipaas/v1/diagrams/:id")
	public method PutDiagram()
	@Delete("/api/tdi/ipaas/v1/diagrams/:id")
	public method DeleteDiagram()

	//Apis Relacionadas  a messagens tabela PXC
	@Get("/api/tdi/ipaas/v1/messages")
	public method GetMessages()
	@Post("/api/tdi/ipaas/v1/messages")
	public method PostMessage()
	@Put("/api/tdi/ipaas/v1/messages/:id")
	public method PutMessage()
	@Delete("/api/tdi/ipaas/v1/messages/:id")
	public method DeleteMessage()
EndClass

/*/{Protheus.doc} New()
    Metodo responsavel por instanciar a classe e iniciar variaveis
    @type Method
    <AUTHOR> Menabue Lima
    @since 13/03/2025
    @version 1.0
/*/
Method New() class TdiIpaasMessageControllers
	::oServices :=  tdi.ipaas.messages.service.TdiIpaasMessageServices():new()
	::jParams   := JsonObject():New()
	::jPathParams:= JsonObject():New()
	::jBodyRequest:= JsonObject():New()
	::jBodyResponse:= JsonObject():New()
Return Self
/*/{Protheus.doc} Destroy
	Metodo responsavel por limpar os objetos da classe
	 @type Method
	<AUTHOR> Menabue Lima
	@since 13/03/2025
	@version 1.0

	/*/
Method Destroy() Class TdiIpaasMessageControllers
	FwFreeObj(::oServices)
	FwFreeObj(::jParams)
	FwFreeObj(::jPathParams)
	FwFreeObj(::jBodyRequest)
	FwFreeObj(::jBodyResponse)
Return
/*/{Protheus.doc} getDiagrams()
    Consulta um(s) cadastro(s) de diagrama na tabela PXB
    @type  Method
    <AUTHOR> Menabue Lima
    @since 13/03/2025
    @version 1.0
   
    
/*/
Method getDiagrams() Class TdiIpaasMessageControllers
	oRest:setKeyHeaderResponse("Content-Type", "application/json")
	::jParams        := oRest:getQueryRequest()
	::oServices:getDiagrams(::jParams )
	oRest:setResponse( ::oServices:jResult)
Return
/*/{Protheus.doc} postDiagram()
    Inclui um cadastro de diagrama na tabela PXB
    @type  Method
    <AUTHOR> Menabue Lima
    @since 13/03/2025
    @version 1.0
       
/*/
Method postDiagram() Class TdiIpaasMessageControllers
	oRest:setKeyHeaderResponse("Content-Type", "application/json")
	::jBodyRequest:fromJson(oRest:getBodyRequest())
	::oServices:postDiagram(::jBodyRequest )
	::setResponse(oRest)
Return
/*/{Protheus.doc} PutDiagram()
    Altera um cadastro de diagrama na tabela PXB
    @type  Method
    <AUTHOR> Menabue Lima
    @since 13/03/2025
    @version 1.0
   
    
/*/
Method PutDiagram() Class TdiIpaasMessageControllers
	oRest:setKeyHeaderResponse("Content-Type", "application/json")
	::jPathParams := oRest:getPathParamsRequest()
	::jBodyRequest:fromJson(oRest:getBodyRequest())
	::oServices:PutDiagram(::jPathParams['id'],::jBodyRequest )
	::setResponse(oRest)
Return
/*/{Protheus.doc} DeleteDiagram()
    Deleta um cadastro de diagrama na tabela PXB
    @type  Method
    <AUTHOR> Menabue Lima
    @since 13/03/2025
    @version 1.0
 
/*/
Method DeleteDiagram() Class TdiIpaasMessageControllers
	oRest:setKeyHeaderResponse("Content-Type", "application/json")
	::jPathParams := oRest:getPathParamsRequest()
	::oServices:DeleteDiagram(::jPathParams )
	::setResponse(oRest)
Return

/*/{Protheus.doc} getMessages()
    Consulta uma(s) msg(s) na tabela PXC
    @type  Method
    <AUTHOR> Menabue Lima
    @since 13/03/2025
    @version 1.0
   
    
/*/
Method getMessages() Class TdiIpaasMessageControllers
	oRest:setKeyHeaderResponse("Content-Type", "application/json")
	::jParams        := oRest:getQueryRequest()
	::oServices:GetMessages(::jParams)
	oRest:setResponse( ::oServices:jResult)

Return
/*/{Protheus.doc} postMessage()
   Inclui uma msg na tabela PXC
    @type  Method
    <AUTHOR> Menabue Lima
    @since 13/03/2025
    @version 1.0
   
    
/*/
Method postMessage() Class TdiIpaasMessageControllers
	oRest:setKeyHeaderResponse("Content-Type", "application/json")
	::jBodyRequest:fromJson(oRest:getBodyRequest())
	::oServices:PostMessage(::jBodyRequest )
	::setResponse(oRest)
Return
/*/{Protheus.doc} putMessage()
   Altera uma msg na tabela PXC
    @type  Method
    <AUTHOR> Menabue Lima
    @since 13/03/2025
    @version 1.0
   
    
/*/
Method putMessage() Class TdiIpaasMessageControllers
	oRest:setKeyHeaderResponse("Content-Type", "application/json")
	::jPathParams := oRest:getPathParamsRequest()
	::jBodyRequest:fromJson(oRest:getBodyRequest())
	::oServices:PutMessage(::jPathParams['id'],::jBodyRequest )
	::setResponse(oRest)
Return
/*/{Protheus.doc} deleteMessage()
   Deleta uma msg na tabela PXC
    @type  Method
    <AUTHOR> Menabue Lima
    @since 13/03/2025
    @version 1.0
 
/*/
Method deleteMessage() Class TdiIpaasMessageControllers
	oRest:setKeyHeaderResponse("Content-Type", "application/json")
	::jPathParams := oRest:getPathParamsRequest()
	::oServices:DeleteMessage(::jPathParams )
	::setResponse(oRest)
Return


/*/{Protheus.doc} setResponse
	Metodo responsavel por retornar o Json de response no formato padrao desta API
	<AUTHOR> Menabue Lima
	@since 13/03/2025
	@version 1.0
	@type Method
	
	/*/
Method SetResponse(oRest) Class TdiIpaasMessageControllers
	if Empty(::oServices:cError)
		::jBodyResponse["id"] := ::oServices:Id
		::jBodyResponse["message"] := ::oServices:jResult['message']
		::jBodyResponse["result"]  := ::oServices:jResult
		oRest:setStatusCode(::oServices:httpcode)
		oRest:setResponse(::jBodyResponse:toJson())
	Else
		::jBodyResponse["id"] 		:= ::oServices:Id
		::jBodyResponse["message"] := ::oServices:cError
		::jBodyResponse["result"] :=""
		oRest:setStatusCode(::oServices:httpcode)
		oRest:setResponse(::jBodyResponse:toJson())
	Endif
Return




