#include "tlpp-core.th"

namespace   Backoffice.Configurador.Gestao_Usuarios.Utils.ValidateTheFormatOfJsonUtils

/*/{Protheus.doc} ValidateTheFormatOfJson
This method aims to validade if the format of Character Json Object is valid
@type class
@version  1.0
<AUTHOR>
@since 11/13/2024
/*/
Class   ValidateTheFormatOfJson
    Static  Method  executeProcess(cJson  As  Character) As  Array    
EndClass

/*/{Protheus.doc} ValidateTheFormatOfJson::executeProcess
This method aims to execute the process do validate de format of json.
@type method
@version  1.0
<AUTHOR>
@since 12/4/2024
@param cJson, character, The json that will be validated
@return array, Return a array with the structure {Logical Value, Object Body, Object Json}
/*/
Method  executeProcess(cJson  As  Character) As  Array  Class   ValidateTheFormatOfJson
    Local   aValidation :=  {,,}                         As  Array
    Local   oJson   :=  JsonObject():New()               As  Json
    Local   oBody   As  Json
        
    
    oBody     := oJson:FromJson(cJson)
    If  (ValType(oBody) == "U")
        aValidation  := {.T., oBody, oJson}
    Else
        aValidation  := {.F., oBody, oJson}
    EndIf
Return  aValidation



