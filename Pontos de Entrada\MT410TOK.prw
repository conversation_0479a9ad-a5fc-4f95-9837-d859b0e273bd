﻿#Include 'Protheus.ch'

/*
ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Í»ï¿½ï¿½
ï¿½ï¿½ï¿½Programa  ï¿½MT410TOK  ï¿½Autor  ï¿½ Erich Buttner      ï¿½ Data ï¿½  Ago/2015   ï¿½ï¿½ï¿½
ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Í¹ï¿½ï¿½
ï¿½ï¿½ï¿½Desc.     ï¿½Pto. Entrada para Verificar se o PV Foi Gerado a Partir de  ï¿½ï¿½ï¿½
ï¿½ï¿½ï¿½          ï¿½                                                            ï¿½ï¿½ï¿½
ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Í¹ï¿½ï¿½
ï¿½ï¿½ï¿½Uso       ï¿½ AP                                                         ï¿½ï¿½ï¿½
ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½Í¼ï¿½ï¿½
ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½ï¿½
*/
User Function MT410TOK()
	Local aArea			:= GetArea()
	Local aSC5Area		:= SC5->( GetArea() )
	Local aSC6Area		:= SC6->( GetArea() )
	Local aSB1Area		:= SB1->( GetArea() )
	Local aSBMArea		:= SBM->( GetArea() )
	Local aSA1Area		:= SA1->( GetArea() )
	Local aCNDArea		:= CND->( GetArea() )
	Local aCNCArea		:= CNC->( GetArea() )
	Local aSM4Area		:= CNC->( GetArea() )
	Local nPosProd		:= 0
	Local cNumTes			:= GetMv( 'TI_PEDTES' ,, '703/753' )
	Local nPosTes			:= 0 
	Local nInd				:= 0
	Local lRet 			:= .T.
	Local cCdSM4MNF		:= ''
	Local cMsgNFGCV		:= ''
	Local cAuxMsg			:= ''
	Local cNaturez		:= Nil
	Local cImposto		:= Nil
	Local nVlrFAT			:= 0
	Local nVlrFtOld		:= 0
	Local nVlrTrib		:= 0
	Local nPrcUnit		:= 0
	Local nPercTrib		:= 0
	Local cEmpPadT		:= GETMV("TI_EMPPADT")  //Codigos das Empresas que utilizam o padrao e precisam de tratamento diferenciado na inclusao de contratos de venda.
	Local lVld			:= .F.
	Local lPcSist		:= AllTrim(SM0->M0_CODFIL) $ GetMv("TI_PCEMPRS",,"00302000200*00302000300*00302000500") //PC SISTEMAS 
	Local cRecIss		:= ""
	Local cLib			:= ""
	local lSmartCli		:= GetRemoteType(@cLib) < 0
	Local cMsgSupPadrao	:= GetMv( 'TI_410MSUP' , , '.' )

	// Inclus�o
	If PARAMIXB[1] == 3
		if lSmartCli
			If empty(M->C5_XUSUINC)
				M->C5_XUSUINC := "(" + ALLTRIM(cUsername) + ") - " + Alltrim(USRFULLNAME(__CUSERID))
			EndIf		
		Else
				M->C5_XUSUINC := "(" + ALLTRIM(cUsername) + ") - " + Alltrim(USRFULLNAME(__CUSERID))
		EndIf
		//============================================================
		// Tratamento para Mensagem de NF, quando gerado por Contratos
		//============================================================
		cNumMed	:= GetMemVar( 'C5_MDNUMED' )
		
		If !Empty(cNumMed)
		
			dbSelectArea('CND')
			CND->(dbSetOrder(4))
			If CND->(dbSeek(FWxFilial('CND') + cNumMed ))
			
				dbSelectArea('CNC')
				CNC->(dbSetOrder(3))
				If CNC->(dbSeek(FWxFilial('CNC') + CND->(CND_CONTRA + CND_REVISA + CND_XCLIEN + CND_XLJCLI) ))
				
					If CNC->CNC_MSGNF == '1'
						
						cCdSM4MNF := GetMv('TI_CVMSGNF',,'')// GCV - Codigo da Mensagem/Formula para a mensagem da NF para pedidos gerados a partir do contrato.
						
						If !Empty(cCdSM4MNF)
							cMsgNFGCV := AllTrim(FORMULA(cCdSM4MNF))
						EndIf
						
						If Empty(cMsgNFGCV)
							cMsgNFGCV := "Ref. Compet: " + CND->CND_COMPET
						EndIf
						
						cAuxMsg := IIF( Empty(Alltrim(M->C5_OUTRINF)), "", Alltrim(M->C5_OUTRINF)  ) 
						
						SetMemVar( 'C5_OUTRINF', cAuxMsg + " "+ cMsgNFGCV )
						
					EndIf
					
				EndIf
			EndIf
			
		EndIf
		
		//----------------------------------------------------------------------------
		// Notas substituta - preenchimento dos campos e validacao do valor liquido //
		// que nao pode ser menor do que o valor que foi lancado na nota anterior   //
		//----------------------------------------------------------------------------		
		If lPcSist
			If ( !Empty(M->C5_NFSUBST) .And. !Empty(M->C5_SERSUBS) )
				For nInd := 1 To Len( aCols )
					nVlrFAT += GdFieldGet( "C6_VALOR"  , nInd , .F. , aHeader , aCols )	
				Next nInd
				
				dbSelectArea("SD2")
				SD2->(dbSetOrder(3))
				If SD2->(dbSeek(xFilial("SD2") + M->(C5_NFSUBST + C5_SERSUBS))) //Filtro somente DOC e SERIE pois pode ser NF subst. para outro cliente
					While SD2->(!EoF())	.And. ( xFilial("SD2") == SD2->D2_FILIAL ;
											.And. SD2->D2_DOC == M->C5_NFSUBST ;
											.And. SD2->D2_SERIE == M->C5_SERSUBS )
						
						nVlrFtOld += SD2->D2_TOTAL					
											
						SD2->(dbSkip())
					EndDo						
				EndIf
				
				If nVlrFtOld > nVlrFat
					lRet := .F.
					ApMsgInfo(	"O valor total informado neste pedido � menor do que o que est� na nota a ser substituida:" + CRLF + CRLF + ;
								"Nota Subs..............: " + M->C5_SERSUBS + " / " + M->C5_NFSUBST + CRLF + ;
								"Valor Nota Subs....: " + Transform(nVlrFtOld,"@E 9,999,999,999,999.99"))
				EndIf
			EndIf
		EndIf
		
	EndIf

	dbSelectArea("SA1")
	SA1->(dbSetOrder(1))
	//Faz o ajuste da TES na inclusao do PV quando o estado do cliente for igual EX e o pedido estiver sendo gerado no Brasil.
	If cPaisLoc == 'BRA'
	/*	If SA1->( dbSeek( xFilial( 'SA1' )+ Padr( Upper( M->C5_CLIENTE ),TamSx3("A1_COD")[1] )  + Padr( Upper( M->C5_LOJACLI ),TamSx3("A1_LOJA")[1] )   ) )
			If AllTrim( Upper( SA1->A1_EST ) ) == 'EX'
				nPosProd := aScan( aHeader, { | x | AllTrim( Upper( x[ 2 ] ) ) == 'C6_PRODUTO' } )
				nPosTes  := aScan( aHeader, { | x | AllTrim( Upper( x[ 2 ] ) ) == 'C6_TES' } )

				If aScan( aCols, { | x | AllTrim( Upper( x[ nPosTes ] ) ) != cNumTes } ) > 0
					For nInd := 1 To Len( aCols )
						If !Empty( aCols[ nInd ][ nPosProd ] )
							If !( aCols[ nInd ][ nPosTes ] == cNumTes )
								aCols[ nInd ][ nPosTes ] := cNumTes
							EndIf
						EndIf
					Next nInd
				EndIf

			EndIf
		EndIf */

		If cFilAnt == "00001001000"
			nPosTes  := aScan( aHeader, { | x | AllTrim( Upper( x[ 2 ] ) ) == 'C6_TES' } )
	
			For nInd := 1 To Len( aCols )
				If !( aCols[ nInd ][ nPosTes ] $ cNumTes )
					lRet := .F.
				EndIf
			Next nInd
	
			If !lRet
				ApMsgInfo("TES Invalida. TES permitida: "+cNumTes)
			EndIf
		EndIf
		
	// // TRATAMENTO PARA PERMITIR A ALTERA��O -- 12/04/2017 - FABIO BATISTA 		
		If INCLUI
			lVld := !IsInCallStak("U_TSRVA013") .AND. !IsInCallStak("U_TFINA032")
		Else
			lVld := ! AllTrim(SC5->C5_ORIGEM) $ "TSRV013,TFINA032"
		EndIf
		
		If lVld // CASO A FUN��O DE CFP ESTIVER NA PILHA SER� DESVIADO DA VALIDA��O DE FILIAL
			//Tratamento para os Itens de BPO

			If AllTrim( cFilAnt ) $ cEmpPadT 
				cNumMed	:= GetMemVar( 'C5_MDNUMED' )
				//Aplicar Calculo de Regra de Tributacao
				For nInd := 1 To Len( aCols )
					nVlrTrib	:= 0
					nPercTrib	:= 0
					nQuant  := GdFieldGet( 'C6_QTDVEN' , nInd , .F. , aHeader , aCols )
					nPrcUnit:= GdFieldGet( 'C6_PRCVEN' , nInd , .F. , aHeader , aCols )
					nVlrFAT	:= GdFieldGet( 'C6_VALOR'  , nInd , .F. , aHeader , aCols )
					cProdut	:= GdFieldGet( 'C6_PRODUTO', nInd , .F. , aHeader , aCols )
					cItemMed:= GdFieldGet( 'C6_ITEMED' , nInd , .F. , aHeader , aCols )
					
					If !Empty( cProdut )
						lRet := U_GV002IMP( @cImposto, @nVlrFAT, @nVlrTrib, @nPrcUnit, @nPercTrib, @nQuant, 2, cNumMed, cItemMed, cProdut  )
	
						If lRet
							GdFieldPut( 'C6_XIMPOST', cImposto 	, nInd , AHeader , ACols  )
							GdFieldPut( 'C6_XFATOR' , nPercTrib	, nInd , AHeader , ACols  )
							GdFieldPut( 'C6_XVLTRIB', nVlrTrib 	, nInd , AHeader , ACols  )
							GdFieldPut( 'C6_PRCVEN' , nPrcUnit 	, nInd , AHeader , ACols  )
							GdFieldPut( 'C6_VALOR' 	, nVlrFAT 	, nInd , AHeader , ACols  )
							
						EndIf
						
					EndIf
				Next nInd
			EndIf
		
		EndIf 
		If Empty( GetMemVar( 'C5_NATUREZ' ) )
			cNaturez := U_TFATA018( GetMemVar( 'C5_CLIENTE' ), GetMemVar( 'C5_LOJACLI' ), If( Len( aCols ) > 0, GdFieldGet( 'C6_PRODUTO'  , 1 , .F. , aHeader , aCols ), "" ) )
			SetMemVar( 'C5_NATUREZ',  cNaturez ) 
			
			//-----------------------------------------------------------------
			// Gatilho do campo C5_RECISS caso a natureza n�o seja informada //
			//-----------------------------------------------------------------
			DbSelectArea("SED")
			SED->(DbSetOrder(1))
			If SED->(DbSeek(xFilial("SED") + cNaturez))
				cRecIss := Iif(SED->ED_CALCISS=="N","2","1")
				SetMemVar( "C5_RECISS", cRecIss )       
			EndIf
		EndIf                  

	EndIf

	DbSelectArea("SE4")
	SE4->(DbSetOrder(1))
	If SE4->(DbSeek(xFilial("SE4")+M->C5_CONDPAG))
		If SE4->E4_TPAY//Mais Negócios.
			If Empty(M->C5_MENNOTA)
				//Tratamento para não imprimir msg padrão da Supplier
				//enquanto o time de produto não disponibiliza a opção para não
				//gravá-la, pois enviamos msg personalizada na RPS e transmissão da nota.			
				If !Empty(cMsgSupPadrao)
					M->C5_MENNOTA := '.'
				EndIf
			EndIf
		EndIf
	EndIf

	If lRet
		lRet := VldContra()
	EndIf

	RestArea( aSBMArea )
	RestArea( aSB1Area )
	RestArea( aSC6Area )
	RestArea( aSC5Area )
	RestArea( aSA1Area )
	RestArea( aCNDArea )
	RestArea( aCNCArea )
	RestArea( aSM4Area )
	RestArea( aArea )
Return lRet


/*/{Protheus.doc} VldContra
    (Ponto de entrada TudoOk do pedido de vendas.)
    @type Statis 
    <AUTHOR>
    @since 10/06/2022
    @version version
    @param param_name, param_type, return_description
    @example
    (examples)
    @see (links_or_refences)
/*/

Static Function VldContra()
    Local lRet := .T.
    Local lGCT := !Empty(M->C5_MDCONTR) .Or. Alltrim(M->C5_ORIGEM) $ 'TECA935|TECA934'
	
	If lGCT .And. ALTERA 
		lRet := ValMsgNota()

		If lRet
			lRet := VldParcela()
		EndIf
							
		If lRet
			lRet := VldDtVenct()
		EndIf	

		
	EndIf 
    
Return lRet

/*/{Protheus.doc} ValMsgNota
    (Valida tamanho da nota fiscal.)
    @type Static 
    <AUTHOR>
    @since 10/06/2022
    @version version
    @param param_name, param_type, return_description
    @example
    (examples)
    @see (links_or_refences)
/*/
Static function ValMsgNota()

    Local cMenNota := U_GC143LMSG(M->C5_MENNOTA)
    Local cOutrInf := U_GC143LMSG(M->C5_OUTRINF)
    Local cMsgPed  := cMenNota + cOutrInf
    
    Local cUnineg  := M->C5_FILIAL
    Local nLimite  := 0
    Local lRet     := .T. 

    M->C5_MENNOTA  := cMenNota
    M->C5_OUTRINF  := cOutrInf
       
    U_GC143MSG(cMsgPed, cUnineg, @nLimite) 

    If nLimite > 0
        lRet := .F.
        Help(" ", 1, 'Help', 'MT410TOK', "Para essa unidade " + cUnineg + ", a mensagem e limitada em " + Alltrim(Str(nLimite)) + " caracteres!", 3, 0) 
    EndIf

	If Type("oGetPV") == "O"
        oGetPV:Refresh()
    EndIf
Return lRet

Static Function VldParcela()
	Local nTotParcel := 0
	Local nTotPedido := 0
	Local cPictur    := ""
	Local cTotParcel := ""
	Local cTotPedido := ""
	Local lRet       := .T.

	
	If SE4->E4_TIPO == "9" 
		cPictur   := PesqPict("SC6","C6_VALOR")

		nTotParcel := TotalParce()
		nTotPedido := TotalPedid()

		If nTotParcel <> nTotPedido
			cTotParcel := Alltrim(transform(nTotParcel, cPictur))
			cTotPedido := Alltrim(transform(nTotPedido, cPictur))

			lRet := .F.
			Help(" ", 1, 'Help', 'MT410TOK', "Valor das parcelas diferente do total do pedido! Total do Pedido: " + cTotPedido + " Total das Parcelas: " + cTotParcel + ".", 3, 0) 
		EndIf
	EndIf

Return lRet 

Static Function TotalParce()
	Local cParcNum  := GetNewPar("TI_PARCNUM", "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ0")
	Local nx        := 0
	Local cDtVencto := ""
	Local dDtVencto := Ctod("")
	Local cVlParc   := ""
	Local nVlParc   := 0
	Local nTotParc  := 0

	For nx:= 1 to len(cParcNum)
		cParcela  := SubStr(cParcNum, nx, 1)
		cVlParc   := "M->C5_PARC" + cParcela
		nVlParc := &cVlParc

		nTotParc += nVlParc
	Next

Return nTotParc

Static Function TotalPedid()

	Local nPosDel    := Len(aHeader) + 1
	Local nPValor    := ASCAN(AHEADER,{|X| Alltrim(X[2]) == "C6_VALOR"	})
	Local nTotPedido := 0
	Local nx         := 0

	For nx:= 1 to Len(aCols)
		If aCols[nx, nPosDel]
			Loop
		EndIf
		nTotPedido += aCols[nx, nPValor]
	Next
Return nTotPedido



Static Function VldDtVenct()
    Local aArea      := GetArea()
    Local aAreaCNB   := CNB->(GetArea())
    Local aAreaAI0   := AI0->(GetArea())
	Local aAreaSE4   := SE4->(GetArea())
    Local nx         := 0
    Local lRet       := .T.
    Local nDiasVct   := GetMv("TI_VCTOFAT",,7)
    Local aParcelas  := {}
    Local dVencto    
    Local nDia 	     := Day(dDataBase)
	Local cCliente   := M->C5_CLIENTE
	Local cLoja      := M->C5_LOJACLI
	Local nDtLim     := Posicione("AI0", 1, xFilial("AI0") + cCliente + cLoja, "AI0_DTLIMT" )
  
    Local nVlrFat    := 0
  
	Local cCondPg    := M->C5_CONDPAG
    Local cContra    := M->C5_MDCONTR
    Local cRevisa    := M->C5_XMDREV
    Local cNumero    := M->C5_MDPLANI
    Local cItemPl    := ""
    Local cCmpFat    := ""
	Local nPosDel    := Len(aHeader) + 1
	Local lAutoEx    := .F.
	Local cHelpMsg   := ""
	Local nPCmpFat   := ASCAN(AHEADER,{|X| Alltrim(X[2]) == "C6_XCOMPET"  }) 
	Local nPItmPla   := ASCAN(AHEADER,{|X| Alltrim(X[2]) == "C6_XITMPLA"	})
	Local nPValor    := ASCAN(AHEADER,{|X| Alltrim(X[2]) == "C6_VALOR"	})
	Local dMinVencto := CtoD("")

	If Type("lMsHelpAuto") == "L"
		lAutoEx := lMsHelpAuto
	EndIf
	
	Begin Sequence
		SE4->(DbSetOrder(1))
		SE4->(DbSeek(xFilial("SE4") + cCondPg))
		If SE4->E4_TIPO == "9" .and. (Empty(M->C5_PARC1) .Or. Empty(M->C5_DATA1) )
			cHelpMsg := "A compentecia #1 com condicao de pagamento personalizada com parcelas nao informada!"
			Help(,, "MT410TOK", , cHelpMsg, 1, 0) 
			lRet := .F.
			Break
		EndIf 

		If nDtLim <> 0 .And. nDtLim < nDia
			If lAutoEx
				cHelpMsg := I18N("O Cliente: #1 possui data limite para geracao de nota fiscal para o dia #2!", {cCliente, Alltrim(Str(nDtLim))})
				Help(,, "MT410TOK", , cHelpMsg, 1, 0) 
				lRet := .F.
				Break
			Else
				cHelpMsg := I18N("O Cliente: #1 possui data limite para geracao de nota fiscal para o dia #2. Deseja Continuar? ", {cCliente, Alltrim(Str(nDtLim))})
				If !MsgNoYes(cHelpMsg, "Pedido com vencimento proximo" )
					lRet := .F.
					Break
				EndIf 
			EndIf 
		EndIf

		If SE4->E4_TIPO == "9" 
			aParcelas := DoParcTp9()
			dVencto   := M->C5_DATA1
		EndIf

		
		For nx:= 1 to Len(aCols)
			If aCols[nx, nPosDel]
				Loop
			EndIf

			cItemPl   := aCols[nx, nPItmPla ]
			cCmpFat   := GetCmpFat(nx, nPCmpFat)

			If SE4->E4_TIPO <> "9" 

				nVlrFat  := aCols[nx, nPValor]
				
				CNB->(DBSetOrder(1))
				CNB->(DbSeek(FwxFilial("CNB") + cContra + cRevisa + cNumero + cItemPl))

				dVencto := U_GCVA142D(aParcelas, cCondPg, nVlrFat, nVlrFat, 0)
			EndIf

			If Empty(aParcelas)
				cHelpMsg := I18N("A compentecia #1 nao gerou vencimento algum, verificar condicao de pagto!! ", {cCmpFat})
				Help(,, "MT410TOK", , cHelpMsg, 1, 0) 
				lRet := .F.
				Break
			EndIf 

			If Empty(dMinVencto)
				dMinVencto := dVencto
			EndIf 

			If dVencto < dMinVencto
				dMinVencto := dVencto
			EndIf		
		Next
		

		If dMinVencto <= dDatabase + nDiasVct		
			If lAutoEx
				cHelpMsg := I18N("A compentecia #1 vai gerar vencimento para o dia #2 excedendo o liminte de #3 dias de antecendencia!", {cCmpFat ,DTOC(dMinVencto), Alltrim(Str(nDiasVct))})
				Help(,, "MT410TOK", , cHelpMsg, 1, 0) 
				lRet := .F.
			Else
				cHelpMsg := I18N("A compentecia #1 vai gerar vencimento para o dia #2. Deseja continuar o processo?", {cCmpFat ,DTOC(dMinVencto)})
				If ! MsgNoYes(cHelpMsg)
					lRet := .F.
				EndIf
			EndIf 
		EndIf
	End Sequence

	RestArea(aAreaSE4)
    RestArea(aAreaAI0)
    RestArea(aAreaCNB)
    RestArea(aArea)

Return lRet

Static Function DoParcTp9()
	Local cParcNum  := GetNewPar("TI_PARCNUM", "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ0")
	Local nx        := 0
	Local cDtVencto := ""
	Local dDtVencto := Ctod("")
	Local cVlParc   := ""
	Local nVlParc   := 0
	Local aParcelas := {}

	For nx:= 1 to len(cParcNum)
		cParcela  := SubStr(cParcNum, nx, 1)
		cDtVencto := "M->C5_DATA" + cParcela
		dDtVencto := &cDtVencto

		cVlParc   := "M->C5_PARC" + cParcela
		nVlParc := &cVlParc

		If nVlParc == 0 
			Exit
		EndIf

		If Empty(dDtVencto)
			Exit
		EndIf 
		
		AADD(aParcelas, {dDtVencto, nVlParc})
	Next
Return aParcelas


Static Function GetCmpFat(nLinha, nPCmpFat)
   
    Local cCmpFat  := aCols[nLinha, nPCmpFat]

    If Empty(cCmpFat)
        cCmpFat := GetMedCmp()
    EndIf

Return cCmpFat

Static Function GetMedCmp()
    Local aAreaCND := CND->(GetArea())
    Local cFilSC5  := M->C5_FILIAL
    Local cContra  := M->C5_MDCONTR
    Local cRevisa  := M->C5_XMDREV
    Local cNumero  := M->C5_MDPLANI
    Local cNumMed  := M->C5_MDNUMED
    Local cCmpFat  := ""

    CND->(DbSetOrder(1 )) //CND_FILIAL+CND_CONTRA+CND_REVISA+CND_NUMERO+CND_NUMMED 

    If CND->(DbSeek(cFilSC5 + cContra + cRevisa + cNumero + cNumMed))
        cCmpFat := CND->CND_COMPET
    EndIf

    CND->(RestArea(aAreaCND))
Return cCmpFat
