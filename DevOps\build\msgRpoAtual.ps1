###################################################################
#  Envia o texto e a imagem gerados pelo script TIR               #
###################################################################
param (
    [string]$pathfilelog   = "D:\tfs\agents\protheus-agent-01\work\1\s\build\Log\SP-DEVOPS-APP03",
    [string]$environment = "Ambiente_nao_informado",
    [string]$tipocompilacao = "Compilacao", 
    [string]$uriTime = "sala_do_time",
    [string]$FileErro = $null
)
Write-Host "Exebot param1: $pathfilelog"
Write-Host "Exebot param2: $environment"
Write-Host "Exebot param3: $tipocompilacao"
Write-Host "Exebot param4: $uriTime"
if (![string]::IsNullOrEmpty($FileErro)){
    Write-Host "Exebot param4: $FileErro"
}
#$uri = $uriTime
$uri = "$ENV:PROTHEUS_URITIME"
Write-Host "Exebot param4: $uri"
try {
    
    #$msgImage = Import-Csv $FileCSV -Delimiter ";" | Select-Object $Collumn -ExpandProperty $Collumn
    $msgImage = Get-Content -Path $pathfilelog -Encoding "UTF8" -Force -Raw
    Write-Host "Exebot mensagem de erro: $msgImage"
 
    # Enviado o dados para o bot
    Get-Module -name GHCWebhook
    $message = New-GHCCardMessage -Content {

        if (![string]::IsNullOrEmpty($FileErro)){
            New-GHCCardHeader  -Title "$tipocompilacao" -Subtitle "$FileErro" -ImageURL "https://lh3.googleusercontent.com/proxy/G8MXdU4TOaNFfyVXxDzN7-Zrhh738IrZVTs04vNQmA7QgCBumZ4eoc_vmczNH_WZPI9wAw" -ImageStyle avatar
        }
        else{
            New-GHCCardHeader  -Title "$tipocompilacao" -ImageURL "https://lh3.googleusercontent.com/proxy/G8MXdU4TOaNFfyVXxDzN7-Zrhh738IrZVTs04vNQmA7QgCBumZ4eoc_vmczNH_WZPI9wAw" -ImageStyle avatar
        }
        
        New-GHCCardSection -Content {
            New-GHCCardWidget -Header "Ambiente: $environment" -Content { New-GHCWidgetTextParagraph -Text $msgImage }
        }
    }
    Send-GHCWebhookMessage -URI $uri -Message $message
}
catch {
    Write-Host "Exebot: Erro na execução"
    $_ | Out-String
}