#INCLUDE "PROTHEUS.CH"

//-------------------------------------------------------------------
/*/{Protheus.doc} VldForX
Funcao de validacao de CGC de fornecedores tipo X para evitar duplicadade
<AUTHOR>
@since 17/06/2021
@version 1.0
@param
@return ( Nil )
@Cliente:Totvs
@obs
/*/
//-------------------------------------------------------------------

User Function TCAD_SA2()
Local lRet      := .T.
Local aArea     := GetArea()
Local cAliasSA2 := GetNextAlias()
Local cQuery    := ''
If M->A2_TIPO <> 'X'
   Return(lRet)
EndIf
cQuery := "SELECT A2_COD, A2_LOJA, A2_NOME FROM "+RETSQLNAME("SA2") 
cQuery += " WHERE A2_CGC = '"+M->A2_CGC+"' AND A2_COD <> '"+M->A2_COD+"' AND D_E_L_E_T_ <> '*'"
dbUseArea(.T. , "TOPCONN" , TcGenQry(,,cQuery) , cAliasSA2 , .T. , .T.)
dbSelectArea(cAliasSA2)
If !Eof()
   lRet := .F.
   MsgStop("Fornecedor ja cadastrado, com codigo/loja:"+(cAliasSA2)->A2_COD+"/"+(cAliasSA2)->A2_LOJA+" - Nome:"+Alltrim((cAliasSA2)->A2_NOME))
EndIf
dbCloseArea()
RestArea(aArea)
Return(lRet)   
  